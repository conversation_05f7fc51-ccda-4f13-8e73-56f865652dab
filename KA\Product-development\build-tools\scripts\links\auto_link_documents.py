#!/usr/bin/env python3
"""
文档关联系统 - 自动文档关联与注册脚本

根据新的系统协作模式，该脚本专注于：
1. 文档自动注册到各组件的INDEX文件
2. 基于AI语义分析的文档关联发现
3. 双链网络构建和维护
4. 为INDEX表格提供关联关系建议
5. 支持Canvas关联同步

重要约束: 本系统负责建立文档级关联基础，追溯系统在此基础上建立块级追溯关系。
如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

注意: 此脚本位于公共库中，新项目可直接调用或拷贝使用
"""

import os
import json
import re
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse
import sys

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir.parent
sys.path.insert(0, str(scripts_dir))

# 导入基础设施层模块
from common.document_scanner import DocumentScanner, IndexScanner
from common.index_manager import IndexManager
from common.document_id_generator import DocumentIdGenerator
from common.config import get_component_dirs


class DocumentRegistrar:
    def __init__(self, project_path="."):
        """初始化文档注册器"""
        self.project_path = Path(project_path)
        
        # 使用基础设施层的统一服务，传入Path对象
        self.document_scanner = DocumentScanner(self.project_path)
        self.index_scanner = IndexScanner(self.project_path)
        self.index_manager = IndexManager(self.project_path)
        self.id_generator = DocumentIdGenerator()
        
        # 文档类型关键词映射
        self.doc_type_keywords = {
            'SPEC': ['规格', '规范', 'spec', 'specification'],
            'PLAN': ['计划', 'plan', '方案'],
            'ARCH': ['架构', 'architecture', '设计'],
            'GUIDE': ['指南', 'guide', '说明'],
            'REPORT': ['报告', 'report'],
            'MATRIX': ['矩阵', 'matrix'],
            'MANUAL': ['手册', 'manual'],
            'DOC': ['文档', 'document']
        }
        
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def extract_document_info(self, file_path):
        """从文件内容中提取文档ID和关联关系"""
        doc_info = {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # 只读前1000字符提高性能
            
            # 使用统一的ID提取逻辑
            existing_id = self.id_generator.extract_existing_id_from_content(content)
            if existing_id:
                doc_info['existing_doc_id'] = existing_id
            
            # 查找文档级关联引用 [[REF:DOC001]] 或 [[文档名]]
            ref_pattern = r'\[\[(?:REF:)?([A-Z]+\d+|[^\]]+)\]\]'
            refs = re.findall(ref_pattern, content)
            
            if refs:
                doc_info['content_references'] = refs
            
            # 提取文档摘要（第一段或第一个标题后的内容）
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and len(line) > 20:
                    doc_info['summary'] = line[:200]
                    break
                    
        except Exception as e:
            self.logger.warning(f"提取文档信息失败 {file_path}: {e}")
            
        return doc_info
    
    def guess_doc_type(self, filename):
        """根据文件名猜测文档类型"""
        filename_lower = filename.lower()
        
        for doc_type, keywords in self.doc_type_keywords.items():
            for keyword in keywords:
                if keyword in filename_lower:
                    return doc_type
                    
        return 'DOC'  # 默认类型
    
    def analyze_semantic_similarity(self, doc1, doc2):
        """分析两个文档的语义相似性（简化实现，实际应使用AI模型）"""
        # 简化的语义相似性分析
        content1 = doc1.get('content', '').lower()
        content2 = doc2.get('content', '').lower()
        
        # 基于关键词重叠度进行简单的相似性计算
        words1 = set(re.findall(r'\b\w+\b', content1))
        words2 = set(re.findall(r'\b\w+\b', content2))
        
        if not words1 or not words2:
            return 0.0
            
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def discover_document_associations(self, all_documents):
        """发现文档间的语义关联关系"""
        associations = []
        similarity_threshold = 0.1  # 相似度阈值
        
        for i, doc1 in enumerate(all_documents):
            for j, doc2 in enumerate(all_documents[i+1:], i+1):
                # 跳过同一文档
                if doc1['file_path'] == doc2['file_path']:
                    continue
                
                # 计算语义相似度
                similarity = self.analyze_semantic_similarity(doc1, doc2)
                
                if similarity > similarity_threshold:
                    associations.append({
                        'doc1_id': doc1.get('doc_id', ''),
                        'doc2_id': doc2.get('doc_id', ''),
                        'doc1_path': str(doc1['file_path']),
                        'doc2_path': str(doc2['file_path']),
                        'similarity': similarity,
                        'relation_type': '语义关联',
                        'auto_discovered': True
                    })
        
        # 按相似度排序
        associations.sort(key=lambda x: x['similarity'], reverse=True)
        return associations[:50]  # 返回前50个最相似的关联
    
    def register_documents_to_index(self, component_code, mode='update'):
        """将扫描到的文档注册到INDEX文件

        Args:
            component_code: 组件代码
            mode: 执行模式 ('update' 或 'init')
                - update: 更新模式，保留现有关联关系
                - init: 初始化模式，重新生成所有内容
        """
        self.logger.info(f"开始注册{component_code}组件文档到INDEX (模式: {mode})")
        
        # 使用基础设施层的统一服务扫描文档
        scanned_docs = self.document_scanner.scan_component_documents(component_code)
        self.logger.info(f"扫描到文档: {len(scanned_docs)} 个")

        if mode == 'init':
            # 初始化模式：内容重新生成，不保留现有记录
            merged_docs = self.create_new_documents(component_code, scanned_docs)
            self.logger.info(f"初始化生成记录: {len(merged_docs)} 个")
        else:
            # 更新模式：保留关联关系
            # 获取现有INDEX数据
            index_files = self.index_scanner.find_index_files()
            existing_docs = []
            if component_code in index_files:
                existing_docs = self.index_scanner.parse_index_file(index_files[component_code])
            self.logger.info(f"现有INDEX记录: {len(existing_docs)} 个")

            # 合并文档数据
            merged_docs = self.merge_documents_with_existing(component_code, scanned_docs, existing_docs)
            self.logger.info(f"合并后记录: {len(merged_docs)} 个")
        
        # 使用基础设施层的INDEX管理器更新文件
        success = self.index_manager.rebuild_index_file(component_code, merged_docs, "document")

        # 注册成功，但不在这里同步Canvas（避免重复同步）
        # Canvas同步将在批量处理完成后统一进行
        return success

    def create_new_documents(self, component_code, scanned_docs):
        """初始化模式：创建全新的文档记录，不保留任何现有关联关系"""
        new_docs = []
        all_existing_ids = []  # 初始化模式下没有现有ID

        for doc_info in scanned_docs:
            # 生成新的文档ID
            doc_id = self.id_generator.generate_new_id(component_code, all_existing_ids)
            all_existing_ids.append(doc_id)

            new_doc = {
                'doc_id': doc_id,
                'doc_name': doc_info['doc_name'],
                'doc_path': doc_info['doc_path'],
                'doc_type': doc_info['doc_type'],
                'file_size': doc_info.get('file_size', 0),
                'modified_time': doc_info['modified_time']
            }
            new_docs.append(new_doc)

        return new_docs

    def merge_documents_with_existing(self, component_code, scanned_docs, existing_docs):
        """合并扫描到的文档与现有INDEX数据"""
        merged_docs = []

        # 创建现有文档的路径映射
        existing_by_path = {doc['doc_path']: doc for doc in existing_docs}

        processed_paths = set()

        # 收集所有已存在的ID，包括现有文档和本次合并中新生成的ID
        all_existing_ids = [doc['doc_id'] for doc in existing_docs]

        # 处理扫描到的文档
        for doc_info in scanned_docs:
            rel_path = doc_info['doc_path']
            processed_paths.add(rel_path)
            
            # 检查是否已在INDEX中注册
            if rel_path in existing_by_path:
                # 更新现有记录（保留关联关系）
                existing_doc = existing_by_path[rel_path]
                merged_doc = existing_doc.copy()
                # 更新基本信息
                merged_doc['doc_name'] = doc_info['doc_name']
                merged_doc['last_update'] = doc_info['modified_time'].strftime("%Y-%m-%d")
                merged_doc['doc_type'] = doc_info['doc_type']
                merged_doc['modified_time'] = doc_info['modified_time']  # 确保有这个字段
                
            else:
                # 创建新记录，使用统一的ID生成逻辑
                doc_id = self.id_generator.generate_new_id(component_code, all_existing_ids)
                # 将新生成的ID添加到列表中，避免后续重复
                all_existing_ids.append(doc_id)

                merged_doc = {
                    'doc_id': doc_id,
                    'doc_name': doc_info['doc_name'],
                    'doc_path': rel_path,
                    'doc_type': doc_info['doc_type'],
                    'file_size': doc_info.get('file_size', 0),
                    'modified_time': doc_info['modified_time']
                }

            merged_docs.append(merged_doc)
        
        # 检查现有INDEX中的文档，移除不存在的文件
        removed_count = 0
        for existing_doc in existing_docs:
            doc_path = existing_doc['doc_path']
            if doc_path not in processed_paths:
                # 检查文件是否真的存在
                full_path = self.project_path / doc_path
                file_name = Path(doc_path).name
                
                # 检查是否是Office临时文件（应该被清理）
                is_temp_file = file_name.startswith('~$') or file_name.startswith('.')
                
                if full_path.exists() and not is_temp_file:
                    # 文件存在且不是临时文件，保留记录（可能是外部引用）
                    if 'modified_time' not in existing_doc:
                        existing_doc['modified_time'] = datetime.now()
                    merged_docs.append(existing_doc)
                    self.logger.info(f"保留未扫描到的文档: {existing_doc['doc_id']} - {doc_path}")
                else:
                    # 文件不存在或是临时文件，从INDEX中移除
                    removed_count += 1
                    if is_temp_file:
                        self.logger.info(f"移除临时文件: {existing_doc['doc_id']} - {doc_path}")
                    else:
                        self.logger.info(f"移除不存在的文档: {existing_doc['doc_id']} - {doc_path}")
        
        if removed_count > 0:
            self.logger.info(f"清理完成：移除 {removed_count} 个无效文档记录")
                
        return merged_docs

    def _auto_sync_canvas(self):
        """自动触发Canvas同步"""
        try:
            # 导入Canvas同步脚本
            from canvas.auto_link_documents import DocumentCanvasIntegrator

            integrator = DocumentCanvasIntegrator(self.project_path)
            success = integrator.sync_index_to_canvas(mode="incremental")

            if success:
                self.logger.info("✓ 自动Canvas同步完成")
                print("✓ 自动同步到Canvas完成")
            else:
                self.logger.warning("⚠ 自动Canvas同步失败")
                print("⚠ 自动Canvas同步失败")

        except ImportError:
            self.logger.info("Canvas功能不可用，跳过自动同步")
        except Exception as e:
            self.logger.error(f"自动Canvas同步出错: {e}")
            print(f"⚠ 自动Canvas同步出错: {e}")

    def apply_canvas_associations(self):
        """应用Canvas中的关联关系到INDEX文件"""
        try:
            # 导入Canvas管理器
            from canvas.canvas_manager import CanvasManager
            
            canvas_manager = CanvasManager(self.project_path)
            associations = canvas_manager.extract_edges_to_associations()
            
            if not associations:
                print("Canvas中未发现关联关系")
                return True
            
            print(f"从Canvas中提取到 {len(associations)} 个关联关系")
            
            # 按组件更新INDEX文件
            updated_components = set()
            for assoc in associations:
                from_doc_id = assoc.get("from_doc_id", "")
                to_doc_id = assoc.get("to_doc_id", "")
                
                # 查找文档所属组件并更新关联信息
                from_component = self._find_document_component(from_doc_id)
                if from_component:
                    self._add_association_to_index(from_component, from_doc_id, to_doc_id, "Canvas关联")
                    updated_components.add(from_component)
            
            print(f"✓ Canvas关联应用完成，更新了 {len(updated_components)} 个组件")
            return True
            
        except ImportError:
            print("⚠ Canvas功能不可用，跳过Canvas关联同步")
            return True
        except Exception as e:
            print(f"✗ Canvas关联应用失败: {e}")
            return False
    
    def _find_document_component(self, doc_id: str) -> Optional[str]:
        """查找文档所属组件"""
        index_files = self.index_scanner.find_index_files()
        
        for component, index_path in index_files.items():
            documents = self.index_scanner.parse_index_file(index_path)
            for doc in documents:
                if doc.get('doc_id') == doc_id:
                    return component
        return None
    
    def _add_association_to_index(self, component: str, from_doc_id: str, to_doc_id: str, relation_type: str):
        """向INDEX文件添加关联关系"""
        # 这里可以实现具体的关联关系更新逻辑
        # 暂时只记录到日志
        print(f"  添加关联: {component}.{from_doc_id} -> {to_doc_id} ({relation_type})")
    
    def generate_association_report(self, all_components=False):
        """生成文档关联分析报告"""
        if all_components:
            # 收集所有组件的文档
            all_documents = []
            component_dirs = get_component_dirs()
            for comp_code in component_dirs.keys():
                docs = self.document_scanner.scan_component_documents(comp_code)
                for doc in docs:
                    doc['component'] = comp_code
                    # 添加内容信息用于语义分析
                    try:
                        with open(doc['file_path'] if 'file_path' in doc else self.project_path / doc['doc_path'], 'r', encoding='utf-8') as f:
                            doc['content'] = f.read(2000)
                    except Exception:
                        doc['content'] = ""
                all_documents.extend(docs)
        else:
            all_documents = []
            
        # 发现语义关联
        associations = self.discover_document_associations(all_documents)
        
        # 生成报告
        report = f"""# 文档关联分析报告

## 概览

- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 分析文档总数: {len(all_documents)}
- 发现关联数: {len(associations)}

## 系统职责说明

### 文档关联系统（本系统）
- **文档注册**: 自动扫描并注册文档到INDEX表格
- **语义分析**: 基于AI模型发现文档间语义关联
- **关联建议**: 为追溯系统提供关联关系基础
- **Canvas同步**: 支持与Canvas的双向关联同步

### 追溯系统
- **块级管理**: 在文档关联基础上建立块级追溯
- **精确追溯**: 建立输出到输入的精确追溯链条
- **影响分析**: 分析变更对系统的影响范围

**重要约束**: 内容追溯关系建立在文档关联基础上，如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

## 发现的语义关联

"""
        
        for i, assoc in enumerate(associations, 1):
            doc1_name = Path(assoc['doc1_path']).stem
            doc2_name = Path(assoc['doc2_path']).stem
            report += f"{i}. **{doc1_name}** ↔ **{doc2_name}**\n"
            report += f"   - 相似度: {assoc['similarity']:.3f}\n"
            report += f"   - 关系类型: {assoc['relation_type']}\n"
            report += f"   - 文档1路径: `{assoc['doc1_path']}`\n"
            report += f"   - 文档2路径: `{assoc['doc2_path']}`\n\n"
        
        report += """
## 建议操作

1. **手动审核**: 审核上述自动发现的关联关系
2. **更新INDEX**: 将确认的关联关系添加到相应的INDEX文件中
3. **建立双链**: 在文档中添加`[[文档名]]`格式的双向链接
4. **Canvas同步**: 在Canvas中建立可视化连线
5. **启用追溯**: 在确认文档关联后，可以建立块级追溯关系

## 后续步骤

```bash
# 1. 从Canvas同步关联关系到INDEX文件
python ../scripts/links/auto_link_documents.py --sync-from-canvas

# 2. 同步关联关系到Canvas
python ../scripts/canvas/auto_link_documents.py --sync-to-canvas

# 3. 从Canvas同步手动建立的关联回INDEX
python ../scripts/links/auto_link_documents.py --sync-from-canvas

# 4. 启动追溯系统进行块级管理
python ../scripts/infoTrace/auto_index_manager.py --all --scan-blocks

# 5. 验证整体关联关系
python ../scripts/infoTrace/traceability_manager.py --validate-all
```
"""
        
        # 保存报告
        reports_dir = self.project_path / 'reports'
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / 'document_associations_report.md'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"✓ 文档关联分析报告已生成: {report_file}")
        return report_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='文档关联系统 - 文档注册与语义关联')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--component', help='组件代码 (REQ/DES/DEV/QA/PROD/DEL)')
    parser.add_argument('--register', action='store_true', help='注册文档到INDEX文件')
    parser.add_argument('--discover-associations', action='store_true', help='发现文档语义关联')
    parser.add_argument('--report', action='store_true', help='生成关联分析报告')
    # 注意：--apply-associations功能已合并到--sync-from-canvas，避免重复
    parser.add_argument('--sync-from-canvas', action='store_true', help='从Canvas同步关联关系')
    parser.add_argument('--all', action='store_true', help='处理所有组件')
    parser.add_argument('--mode', choices=['update', 'init'], default='update',
                       help='执行模式：update(更新，默认) 或 init(初始化)')

    
    args = parser.parse_args()
    
    registrar = DocumentRegistrar(args.project_path)
    
    if args.register:
        if args.component:
            # 注册指定组件的文档
            success = registrar.register_documents_to_index(args.component, args.mode)
            if success:
                print(f"✓ {args.component} 组件文档注册完成 (模式: {args.mode})")
            else:
                print(f"✗ {args.component} 组件文档注册失败")
                sys.exit(1)
        elif args.all:
            # 注册所有组件的文档
            results = {}
            component_dirs = get_component_dirs()
            for comp_code in component_dirs.keys():
                results[comp_code] = registrar.register_documents_to_index(comp_code, args.mode)

            success_count = sum(1 for success in results.values() if success)
            print(f"✓ 文档注册完成: {success_count}/{len(results)} 个组件成功 (模式: {args.mode})")

            # 如果有成功的组件，统一进行Canvas同步
            if success_count > 0:
                registrar._auto_sync_canvas()

            if success_count < len(results):
                sys.exit(1)
        else:
            print("错误: 文档注册需要指定 --component 或 --all 参数")
            sys.exit(1)
    
    elif args.discover_associations or args.report:
        # 生成语义关联分析报告
        report_file = registrar.generate_association_report(all_components=True)
        print(f"✓ 关联分析报告已生成: {report_file}")
    
    elif args.sync_from_canvas:
        # 应用Canvas关联关系
        success = registrar.apply_canvas_associations()
        if success:
            print("✓ Canvas关联同步完成")
        else:
            print("✗ Canvas关联同步失败")
            sys.exit(1)
    
    else:
        # 显示帮助信息
        print("文档关联系统 - 文档注册与语义关联")
        print("")
        print("主要功能:")
        print("1. 文档自动注册到各组件的INDEX文件")
        print("2. 基于AI语义分析的文档关联发现")
        print("3. 双链网络构建和维护")
        print("4. 为INDEX表格提供关联关系建议")
        print("5. 支持Canvas关联双向同步")
        print("")
        print("使用示例:")
        print("  # 注册指定组件的文档")
        print("  python auto_link_documents.py --project-path . --component DEV --register")
        print("")
        print("  # 注册所有组件的文档")
        print("  python auto_link_documents.py --project-path . --all --register")
        print("")
        print("  # 发现并生成语义关联报告")
        print("  python auto_link_documents.py --project-path . --report")
        print("")
        print("  # 从Canvas同步关联关系到INDEX")
        print("  python auto_link_documents.py --project-path . --sync-from-canvas")
        print("")
        print("重要约束:")
        print("  本系统负责建立文档级关联基础，追溯系统在此基础上建立块级追溯关系。")
        print("  如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。")
        print("")
        print("Canvas集成:")
        print("  • 支持从Canvas同步手动建立的关联关系")
        print("  • 保持与Canvas系统的双向同步")
        print("  • 配合Canvas实现可视化文档关联管理")
        print("")
        print("配置文件支持:")
        print("  • 支持项目级配置文件：config/document_links_config.json")
        print("  • 自定义文件类型过滤：included_file_types")
        print("  • 自定义目录排除：excluded_directories")
        print("  • 组件特定配置：component_specific")
        print("  • 详细配置说明请参考：scripts/links/README.md")

if __name__ == "__main__":
    main() 