# Hello World MCP Server

## 概述

Hello World MCP服务器是一个基础示例服务器，演示了MCP协议的基本功能和标准结构。它提供了简单的工具、资源和提示示例，是学习和开发MCP服务器的起点。

## 功能特性

- **基础工具示例**：演示MCP工具的实现方式
- **资源管理示例**：展示如何提供和管理资源
- **提示模板示例**：提供AI助手提示的模板
- **标准结构演示**：展示标准MCP服务器目录结构
- **配置示例**：提供完整的配置文件示例

## MCP工具列表

| 工具名称 | 描述 | 参数 |
|---------|------|------|
| `say_hello` | 简单的问候工具 | `name` |
| `add_numbers` | 数字加法计算 | `a`, `b` |
| `get_current_time` | 获取当前时间 | `format` |
| `echo_message` | 回显消息 | `message`, `repeat` |

## 使用示例

### 基本问候

```python
# 通过AI助手调用
"请向张三问好"

# 直接MCP调用
result = await mcp_client.call_tool("say_hello", {
    "name": "张三"
})
# 返回: "Hello, 张三! Welcome to MCP Server!"
```

### 数字计算

```python
# 通过AI助手调用
"请计算 5 + 3"

# 直接MCP调用
result = await mcp_client.call_tool("add_numbers", {
    "a": 5,
    "b": 3
})
# 返回: {"result": 8, "operation": "5 + 3 = 8"}
```

### 获取时间

```python
# 通过AI助手调用
"请告诉我现在的时间"

# 直接MCP调用
result = await mcp_client.call_tool("get_current_time", {
    "format": "YYYY-MM-DD HH:mm:ss"
})
# 返回: {"current_time": "2024-01-15 14:30:25", "timezone": "UTC"}
```

### 消息回显

```python
# 通过AI助手调用
"请重复说3遍'Hello MCP'"

# 直接MCP调用
result = await mcp_client.call_tool("echo_message", {
    "message": "Hello MCP",
    "repeat": 3
})
# 返回: {"echoed": ["Hello MCP", "Hello MCP", "Hello MCP"]}
```

## 资源示例

### 静态资源

```python
# 获取示例文档
doc = await mcp_client.get_resource("file://docs/example.md")

# 获取配置模板
config = await mcp_client.get_resource("file://templates/config.json")
```

### 动态资源

```python
# 获取服务器状态
status = await mcp_client.get_resource("status://server")

# 获取工具列表
tools = await mcp_client.get_resource("tools://list")
```

## 提示模板

### 基础提示

```
你是一个友好的AI助手，正在使用Hello World MCP服务器。
你可以：
1. 向用户问好
2. 进行简单的数学计算
3. 获取当前时间
4. 回显用户的消息

请根据用户的请求选择合适的工具来帮助他们。
```

### 计算助手提示

```
你是一个数学计算助手。当用户需要进行加法计算时，
请使用 add_numbers 工具来帮助他们。

示例：
用户："请计算 10 + 20"
你应该调用 add_numbers 工具，参数为 a=10, b=20
```

## 安装和配置

### 依赖安装

```bash
cd build-tools/mcp-server_local_integrations/examples/hello-world-mcp
pip install -r requirements.txt
```

### MCP配置

在Cursor的 `.cursor/mcp.json` 中添加：

```json
{
  "mcpServers": {
    "hello-world": {
      "command": "python",
      "args": ["./build-tools/mcp-server_local_integrations/examples/hello-world-mcp/server.py"],
      "description": "Hello World示例MCP服务器"
    }
  }
}
```

### 启动服务器

```bash
python server.py
```

## 开发指南

### 添加新工具

1. 在 `tools/` 目录下创建新的工具模块
2. 实现工具函数
3. 在 `server.py` 中注册工具
4. 更新文档

示例：

```python
# tools/new_tool.py
async def my_new_tool(name: str, value: int) -> dict:
    """新工具示例"""
    return {
        "message": f"Hello {name}",
        "value": value * 2
    }

# server.py
from tools.new_tool import my_new_tool

# 注册工具
@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    return [
        types.Tool(
            name="my_new_tool",
            description="新工具示例",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "value": {"type": "number"}
                },
                "required": ["name", "value"]
            }
        )
    ]
```

### 添加新资源

```python
# 在server.py中添加资源处理
@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    return [
        types.Resource(
            uri="file://example.txt",
            name="示例文件",
            description="一个示例文本文件",
            mimeType="text/plain"
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    if str(uri) == "file://example.txt":
        return "这是示例文件的内容"
    else:
        raise ValueError(f"未知资源: {uri}")
```

### 添加新提示

```python
# 在server.py中添加提示处理
@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    return [
        types.Prompt(
            name="greeting",
            description="问候提示",
            arguments=[
                types.PromptArgument(
                    name="name",
                    description="要问候的人的名字",
                    required=True
                )
            ]
        )
    ]

@server.get_prompt()
async def handle_get_prompt(name: str, arguments: dict) -> types.GetPromptResult:
    if name == "greeting":
        user_name = arguments.get("name", "朋友")
        return types.GetPromptResult(
            description=f"向{user_name}问好的提示",
            messages=[
                types.PromptMessage(
                    role="user",
                    content=types.TextContent(
                        type="text",
                        text=f"请向{user_name}问好"
                    )
                )
            ]
        )
```

## 测试

### 单元测试

```bash
# 运行测试
pytest tests/

# 运行特定测试
pytest tests/test_tools.py::test_say_hello
```

### 集成测试

```bash
# 测试MCP服务器
python tests/test_server.py
```

## 扩展示例

### 数据库连接示例

```python
# 添加数据库工具
async def query_database(query: str) -> dict:
    # 数据库查询逻辑
    return {"result": "查询结果"}
```

### 文件操作示例

```python
# 添加文件操作工具
async def read_file(file_path: str) -> dict:
    # 文件读取逻辑
    return {"content": "文件内容"}
```

### API调用示例

```python
# 添加API调用工具
async def call_api(url: str, method: str = "GET") -> dict:
    # API调用逻辑
    return {"response": "API响应"}
```

## 最佳实践

1. **错误处理**：为所有工具添加适当的错误处理
2. **参数验证**：验证输入参数的有效性
3. **日志记录**：记录重要操作和错误
4. **文档更新**：保持文档与代码同步
5. **测试覆盖**：为所有功能编写测试

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查Python版本和依赖
   - 查看错误日志

2. **工具调用失败**
   - 验证参数格式
   - 检查工具注册

3. **资源访问失败**
   - 确认资源路径
   - 检查权限设置

## 更新日志

- v1.0.0 - 初始版本，基础工具和示例
- v1.1.0 - 添加资源管理示例
- v1.2.0 - 添加提示模板示例
- v1.3.0 - 完善文档和测试

## 许可证

MIT License
