# Canvas可视化系统

## 概述

Canvas可视化系统基于Obsidian Canvas实现产品开发流程的可视化管理，提供文档关联、布局管理和同步功能。

## 核心功能

### 1. 文档可视化

- 将INDEX文件中的文档自动同步到Canvas
- 按组件区域进行布局排列
- 支持项目相对路径格式

### 2. 布局管理

- 自动计算节点位置
- 组件区域划分（700像素间距）
- 颜色编码区分不同组件

### 3. 双向同步

- INDEX文件 ↔ Canvas文件
- 实时验证同步状态
- 自动修复布局问题

## 脚本说明

### auto_link_documents.py

主要的Canvas管理脚本，提供以下功能：

**同步功能：**

```bash
# 将INDEX文件同步到Canvas
python auto_link_documents.py --sync-to-canvas

# 从Canvas同步回INDEX文件
python auto_link_documents.py --sync-from-canvas
```

**验证功能：**

```bash
# 验证同步状态
python auto_link_documents.py --validate-sync

# 查看统计信息
python auto_link_documents.py --stats
```

**文档管理：**

```bash
# 添加新文档到系统
python auto_link_documents.py --add-document --component REQ --type "需求文档" --name "新需求" --path "requirements/new_req.md"
```

### canvas_manager.py

Canvas文件的底层操作管理器：

- 读取和写入Canvas JSON文件
- 节点和边的CRUD操作
- 文件格式验证

### canvas_layout.py

布局计算和管理：

- 组件区域定义
- 节点位置计算
- 布局优化算法

## 组件布局配置

| 组件 | X坐标范围 | 颜色 | 说明 |
|------|-----------|------|------|
| PROD_INFO | 0-400 | #FF5722 | 产品信息 |
| REQ | 700-1100 | #4CAF50 | 需求管理 |
| DES | 1400-1800 | #2196F3 | 设计文档 |
| DEV | 2100-2500 | #FF9800 | 开发实施 |
| QA | 2800-3200 | #9C27B0 | 质量保证 |
| PROD | 3500-3900 | #795548 | 生产制造 |
| DEL | 4200-4600 | #E91E63 | 交付物 |
| PM | 4900-5300 | #607D8B | 项目管理 |

## 使用流程

### 1. 初始化Canvas

```bash
# 首次同步，创建Canvas文件
python auto_link_documents.py --sync-to-canvas
```

### 2. 日常维护

```bash
# 更新INDEX文件后同步到Canvas
python auto_link_documents.py --sync-to-canvas

# 验证同步状态
python auto_link_documents.py --validate-sync
```

### 3. 问题排查

```bash
# 查看详细统计信息
python auto_link_documents.py --stats

# 重新生成Canvas（慎用）
python auto_link_documents.py --sync-to-canvas --force
```

## 路径格式说明

Canvas中的文件路径使用项目相对格式：

```
项目名/相对路径
例如：19_Yapha-Spectrometer-MicroSensor/requirements/req_001.md
```

这种格式确保：

- 跨平台兼容性
- Obsidian正确识别
- 便于项目迁移

## 技术架构

```
Canvas系统架构：
├── auto_link_documents.py (主控制器)
├── canvas_manager.py (文件操作)
├── canvas_layout.py (布局管理)
└── common/ (公共库)
    ├── config.py (统一配置)
    ├── canvas_utils.py (工具函数)
    └── unified_index_manager.py (INDEX管理)
```

## 配置文件

### shared_config.py

- 组件定义和颜色配置
- 目录映射关系
- 项目类型配置

### Canvas布局配置

- 组件区域坐标
- 节点尺寸设置
- 间距和边距配置

## 常见问题

### Q: Canvas中出现负坐标？

A: 运行 `--validate-sync` 检查，通常是布局配置问题

### Q: 文档路径显示错误？

A: 确保使用项目相对路径格式，重新同步

### Q: 节点重叠？

A: 检查组件区域配置，调整间距设置

### Q: 同步失败？

A: 检查INDEX文件格式，确保表格结构正确

## 扩展开发

### 添加新组件

1. 在 `shared_config.py` 中定义组件
2. 更新布局配置
3. 重新同步Canvas

### 自定义布局

1. 修改 `canvas_layout.py` 中的区域配置
2. 调整间距和尺寸参数
3. 验证布局效果

### 集成新功能

1. 继承现有管理器类
2. 实现新的同步逻辑
3. 添加命令行参数

## 性能优化

- 大项目（>100文档）建议分批同步
- 定期清理重复节点
- 使用增量同步减少处理时间
- 监控Canvas文件大小

## 版本兼容性

- Obsidian 1.0+
- Python 3.8+
- 支持Windows/macOS/Linux
- Canvas JSON格式兼容
