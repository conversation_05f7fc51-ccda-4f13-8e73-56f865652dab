#ifndef MOTORMONITORSERIAL_H
#define MOTORMONITORSERIAL_H

#include <QString>
#include <QMessageBox>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QThread>
#include <QDebug>
#include <QTime>
#include <QTimer>
#include "dt2.h"
#include "YJProtocol.h"

class motorMonitorSerial:public QObject
{
    Q_OBJECT
public:
    explicit motorMonitorSerial(QObject *parent = nullptr);
    ~motorMonitorSerial();


  enum protocol_type{
    motor           = 1,
    bottom          = 2,
    sensor_cloud    = 3,
    transBoard      = 4,
  };

    int8_t      m_task_id;
    bool        m_single_receive;
    QSerialPort *m_serial_port = nullptr;
    QSerialPort *m_transBoard_port = nullptr;
    dt2Protocol *m_dt2Protocol = nullptr;
    YJProtocol  *m_yjProtocol  = nullptr;
    QStringList &detectPort();

    void task_id_change(const enum protocol_type &id);
    void main_serial_write(const enum protocol_type &type, const char *data, const uint8_t &len);
    void transB_serial_write(const enum protocol_type &type, const char *data, const uint8_t &len);
public slots:
    void loop(); //接收串口数据API
private:
    QStringList* m_comboBOXPortLists;
};

#endif
