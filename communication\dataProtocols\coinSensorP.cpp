#include "coinSensorP.h"
#include <qdebug.h>
#include "qLog.h"

CCoinSensorP::CCoinSensorP():
    mst_frame_(new StFrame)
{
    mst_frame_->header = eHEADER;
}

CCoinSensorP::~CCoinSensorP(){
    delete mst_frame_;
}

QByteArray CCoinSensorP::getControlCmd(const char &id)
{
    QByteArray cmd;

    mst_frame_->cmd = kH2D;
    mst_frame_->id = id;
    mst_frame_->num = 0;
//    mst_frame_->data

    mst_frame_->check_xor = calXOR((uint8_t*)mst_frame_, (uint8_t)EFrame::eHEADER_LEN + (mst_frame_->num << 1), (uint8_t)EFrame::eXOR_INDEX);
    cmd.append((char*)mst_frame_, (uint8_t)EFrame::eHEADER_LEN + (mst_frame_->num << 1));

    return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CCoinSensorP::getWriteCmd(const char &id, const QByteArray &w_data)
{
    QByteArray w_cmd;

    mst_frame_->cmd = kH2D | kHWS;
    mst_frame_->id = id;
    mst_frame_->num = w_data.length()>>1;

    if(mst_frame_->num > DATA_CACHE) {
        qDebug() << "-e coinSensorP/ write cmd data out off buff length";
        return w_cmd;
    }

    for(uint8_t for_i = 0; for_i < w_data.size(); for_i++) {
        mst_frame_->data[for_i] = (w_data.at(for_i));
    }

    mst_frame_->check_xor = calXOR((uint8_t*)mst_frame_, (uint8_t)eHEADER_LEN + (mst_frame_->num << 1), (uint8_t)EFrame::eXOR_INDEX);
    w_cmd.append((char*)mst_frame_, (EFrame::eHEADER_LEN + (mst_frame_->num << 1))); //+ w_data.length())); //直接拷贝整个 qbytearray数据错乱

    return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CCoinSensorP::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{
    QByteArray r_cmd;

    mst_frame_->cmd = kH2D | kHRS;
    mst_frame_->id = id;
    mst_frame_->num = 0;

    //w_cmd.append((char*)mst_frame_, (EFrame::eHEADER_LEN)); //+ w_data.length())); //直接拷贝整个 qbytearray数据错乱
    mst_frame_->check_xor = calXOR((uint8_t*)mst_frame_, (uint8_t)EFrame::eHEADER_LEN + (mst_frame_->num << 1), (uint8_t)EFrame::eXOR_INDEX);
    r_cmd.append((char*)mst_frame_, (uint8_t)EFrame::eHEADER_LEN + (mst_frame_->num << 1));

    return r_cmd;

}


