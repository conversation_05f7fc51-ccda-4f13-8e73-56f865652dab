# Canvas监控器配置文件
# 文件位置: ${PROJECT_DIR}/config/canvas_watcher.yml

watcher:
  # 监控基本配置
  watch_delay: 0.5 # 文件变化后等待时间（秒）
  sync_interval: 5 # 最小同步间隔（秒）
  max_retries: 3 # 最大重试次数
  retry_delay: 1.0 # 重试延迟（秒）
  batch_timeout: 30 # 批处理超时时间（秒）

  # 日志配置
  log_level: INFO # 日志级别: DEBUG, INFO, WARNING, ERROR
  max_log_size: 10MB # 最大日志文件大小
  backup_count: 5 # 保留的日志备份文件数量

  # 健康检查和统计
  health_check_interval: 60 # 健康检查间隔（秒）
  stats_save_interval: 300 # 统计保存间隔（秒）

  # 文件过滤配置
  file_patterns:
    - "product.canvas" # 主要产品Canvas文件
    - "*_product.canvas" # 其他产品Canvas文件
    - "*.canvas" # 所有Canvas文件（可选）

  # 同步策略配置
  sync_strategy: "batched" # 同步策略: immediate, batched, scheduled
  immediate_threshold: 1 # 立即同步的变更阈值
  batch_size_limit: 100 # 批处理最大变更数量

  # 性能优化
  enable_compression: false # 是否启用日志压缩
  memory_limit_mb: 500 # 内存使用限制（MB）
  cpu_limit_percent: 50 # CPU使用限制（百分比）

  # 通知配置（可选）
  notifications:
    enable_email: false # 启用邮件通知
    email_on_error: true # 错误时发送邮件
    email_recipients:
      - "<EMAIL>"

    enable_webhook: false # 启用Webhook通知
    webhook_url: "https://hooks.slack.com/services/..."
    webhook_on_error: true # 错误时发送Webhook

  # 备份配置
  auto_backup: true # 启用自动备份
  backup_interval_hours: 24 # 备份间隔（小时）
  backup_retention_days: 30 # 备份保留天数
  backup_location: "backups/canvas" # 备份目录

  # 高级配置
  advanced:
    use_file_locks: true # 使用文件锁防止并发冲突
    parallel_processing: false # 启用并行处理（实验性）
    debug_mode: false # 调试模式
    profiling_enabled: false # 性能分析模式

    # 自定义钩子脚本
    hooks:
      before_sync: "" # 同步前执行的脚本
      after_sync: "" # 同步后执行的脚本
      on_error: "" # 错误时执行的脚本
