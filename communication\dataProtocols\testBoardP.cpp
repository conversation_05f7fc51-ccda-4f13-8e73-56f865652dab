#include "testBoardP.h"
#include <qdebug.h>
#include "qLog.h"



CTestBoardP::CTestBoardP():
  mst_frame_(new StFrame)
{

  //* varible init
  mst_frame_->header = EFrame::eHEADER;
  mst_frame_->check_xor = 0x00;
  mst_frame_->data.clear();
}

CTestBoardP::~CTestBoardP(){
  delete mst_frame_;
}

/**
 * @brief 获取控制指令
 * @return
 */
QByteArray CTestBoardP::getControlCmd(const char &id)
{
#if 0
  QByteArray cmd;
  cmd.append(0xa5);
  cmd.append(ECMD::kH2D);
  cmd.append(id);
  cmd.append(0x01); //
  calXOR(&cmd);
#else
  QByteArray cmd;
  mst_frame_->cmd = ECMD::kH2D;
  mst_frame_->id = id;
  cmd.append((char*)mst_frame_, 4);
//  memcpy(mst_frame_, (StFrame*)cmd.data(), 4); //bytearray to struct
  calXOR(3, &cmd);
#endif
  return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CTestBoardP::getWriteCmd(const char &id, const QByteArray &w_data)
{
  QByteArray w_cmd;
#if 0
  w_cmd.append(0xa5);
  w_cmd.append(ECMD::kH2D);
  w_cmd.append(id);
  w_cmd.append(0x01); //xor

  uint16_t num = w_data.length();
  w_cmd.append(num & 0xff);
  w_cmd.append((num>>8) & 0xff);

  w_cmd.append(w_data);
#else
  mst_frame_->cmd = ECMD::kH2D;
  mst_frame_->id = id;
  mst_frame_->num = w_data.length();
  mst_frame_->data = w_data;
  w_cmd.append((char*)mst_frame_, (EFrame::eHEADER_LEN )); //+ w_data.length())); //直接拷贝整个 qbytearray数据错乱
  w_cmd.append(mst_frame_->data);
#endif
  calXOR(3, &w_cmd);
  return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CTestBoardP::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{


}
