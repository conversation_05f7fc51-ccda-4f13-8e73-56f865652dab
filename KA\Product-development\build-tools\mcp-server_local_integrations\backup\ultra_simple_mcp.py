#!/usr/bin/env python3
"""
Ultra Simple MCP Server
不依赖复杂模块的最简实现
"""

import asyncio
import json
import sys

async def main():
    """主函数"""
    
    # 从 stdin 读取，向 stdout 写入
    while True:
        try:
            # 读取头部
            headers = {}
            while True:
                line = sys.stdin.readline()
                if not line:
                    return
                line = line.strip()
                if not line:
                    break
                if ': ' in line:
                    key, value = line.split(': ', 1)
                    headers[key] = value
            
            # 读取内容
            content_length = int(headers.get('Content-Length', 0))
            if content_length == 0:
                continue
                
            content = sys.stdin.read(content_length)
            message = json.loads(content)
            
            # 处理消息
            method = message.get("method")
            msg_id = message.get("id")
            
            response = None
            
            if method == "initialize":
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "protocolVersion": "2025-03-26",
                        "capabilities": {
                            "tools": {"listChanged": False}
                        },
                        "serverInfo": {
                            "name": "ultra-simple-mcp",
                            "version": "1.0.0"
                        }
                    }
                }
            elif method == "initialized":
                # 不需要响应
                continue
            elif method == "tools/list":
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "tools": [
                            {
                                "name": "hello",
                                "description": "Say hello",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string", "description": "Name to greet"}
                                    }
                                }
                            }
                        ]
                    }
                }
            elif method == "tools/call":
                params = message.get("params", {})
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                
                if tool_name == "hello":
                    name = arguments.get("name", "World")
                    text = f"Hello, {name}! Ultra Simple MCP is working!"
                    response = {
                        "jsonrpc": "2.0",
                        "id": msg_id,
                        "result": {
                            "content": [{"type": "text", "text": text}],
                            "isError": False
                        }
                    }
            
            # 发送响应
            if response:
                response_str = json.dumps(response, ensure_ascii=False)
                response_bytes = response_str.encode('utf-8')
                
                header = f"Content-Length: {len(response_bytes)}\r\n\r\n"
                sys.stdout.write(header)
                sys.stdout.write(response_str)
                sys.stdout.flush()
                
        except Exception as e:
            print(f"Error: {e}", file=sys.stderr)
            break

if __name__ == "__main__":
    # 同步版本，不使用 asyncio
    try:
        # 从 stdin 读取，向 stdout 写入
        while True:
            # 读取头部
            headers = {}
            while True:
                line = sys.stdin.readline()
                if not line:
                    sys.exit(0)
                line = line.strip()
                if not line:
                    break
                if ': ' in line:
                    key, value = line.split(': ', 1)
                    headers[key] = value
            
            # 读取内容
            content_length = int(headers.get('Content-Length', 0))
            if content_length == 0:
                continue
                
            content = sys.stdin.read(content_length)
            message = json.loads(content)
            
            # 处理消息
            method = message.get("method")
            msg_id = message.get("id")
            
            response = None
            
            if method == "initialize":
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "protocolVersion": "2025-03-26",
                        "capabilities": {
                            "tools": {"listChanged": False}
                        },
                        "serverInfo": {
                            "name": "ultra-simple-mcp",
                            "version": "1.0.0"
                        }
                    }
                }
            elif method == "initialized":
                # 不需要响应
                continue
            elif method == "tools/list":
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "tools": [
                            {
                                "name": "hello",
                                "description": "Say hello",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string", "description": "Name to greet"}
                                    }
                                }
                            }
                        ]
                    }
                }
            elif method == "tools/call":
                params = message.get("params", {})
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                
                if tool_name == "hello":
                    name = arguments.get("name", "World")
                    text = f"Hello, {name}! Ultra Simple MCP is working!"
                    response = {
                        "jsonrpc": "2.0",
                        "id": msg_id,
                        "result": {
                            "content": [{"type": "text", "text": text}],
                            "isError": False
                        }
                    }
            
            # 发送响应
            if response:
                response_str = json.dumps(response, ensure_ascii=False)
                response_bytes = response_str.encode('utf-8')
                
                header = f"Content-Length: {len(response_bytes)}\r\n\r\n"
                sys.stdout.write(header)
                sys.stdout.write(response_str)
                sys.stdout.flush()
                
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr) 