import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 5x5测试数据矩阵
test_data = np.array([
    [271, 882, 826, 748, 58],
    [1011, 908, 792, 756, 738],
    [1074, 924, 807, 800, 859],
    [1021, 877, 777, 776, 855],
    [145, 887, 788, 740, 33]
])

# 3x3锐化核
sharpen_kernel = np.array([
    [0, -1, 0],
    [-1, 5, -1],
    [0, -1, 0]
])

print("原始数据:")
print(test_data)
print("\n锐化核:")
print(sharpen_kernel)

# 计算核的总和
kernel_sum = np.sum(sharpen_kernel)
kernel_abs_sum = np.sum(np.abs(sharpen_kernel))

print(f"\n核的实际总和: {kernel_sum}")
print(f"核的绝对值总和: {kernel_abs_sum}")

# 1. 普通归一化（实际总和）
normal_normalized_kernel = sharpen_kernel / kernel_sum if kernel_sum != 0 else sharpen_kernel

# 2. 绝对值归一化（C++代码使用的方法）
abs_normalized_kernel = sharpen_kernel / kernel_abs_sum

print("原始锐化核:")
print(sharpen_kernel)
print(f"实际总和: {kernel_sum}")
print(f"绝对值总和: {kernel_abs_sum}")

print("\n普通归一化后的核 (除以实际总和):")
print(normal_normalized_kernel)
print(f"归一化后总和: {np.sum(normal_normalized_kernel):.6f}")

print("\n绝对值归一化后的核 (除以绝对值总和):")
print(abs_normalized_kernel)
print(f"归一化后总和: {np.sum(abs_normalized_kernel):.6f}")
print(f"归一化后绝对值总和: {np.sum(np.abs(abs_normalized_kernel)):.6f}")

def mirror_pad(image, pad_width):
    """镜像扩展边界"""
    return np.pad(image, pad_width, mode='reflect')

def convolution_2d(image, kernel, strength=1.0, normalize_method='none'):
    """2D卷积操作，支持不同的归一化方法"""
    # 归一化处理
    if normalize_method == 'normal':
        kernel_sum = np.sum(kernel)
        if kernel_sum != 0:
            kernel = kernel / kernel_sum
    elif normalize_method == 'abs':
        kernel_abs_sum = np.sum(np.abs(kernel))
        if kernel_abs_sum != 0:
            kernel = kernel / kernel_abs_sum
    
    kernel_h, kernel_w = kernel.shape
    pad_h, pad_w = kernel_h // 2, kernel_w // 2
    
    # 镜像扩展
    padded_image = mirror_pad(image, ((pad_h, pad_h), (pad_w, pad_w)))
    
    # 输出图像
    output = np.zeros_like(image, dtype=np.float64)
    
    # 卷积操作
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            # 提取邻域
            region = padded_image[i:i+kernel_h, j:j+kernel_w]
            # 卷积计算
            conv_result = np.sum(region * kernel)
            # 滤波强度处理
            original_value = float(image[i, j])
            filtered_value = original_value + strength * (conv_result - original_value)
            output[i, j] = filtered_value
    
    return output, kernel

# 比较三种方法的结果
result_none, kernel_none = convolution_2d(test_data, sharpen_kernel, strength=1.0, normalize_method='none')
result_normal, kernel_normal = convolution_2d(test_data, sharpen_kernel, strength=1.0, normalize_method='normal')
result_abs, kernel_abs = convolution_2d(test_data, sharpen_kernel, strength=1.0, normalize_method='abs')

print("=== 三种方法的中心点(2,2)结果比较 ===")
print(f"原始值: {test_data[2, 2]}")
print(f"无归一化: {result_none[2, 2]:.2f}")
print(f"普通归一化: {result_normal[2, 2]:.2f}")
print(f"绝对值归一化: {result_abs[2, 2]:.2f} (C++实际使用)")
print(f"C++实际输出: 82")

# 提取中心点周围的3x3邻域
center_x, center_y = 2, 2
padded = mirror_pad(test_data, ((1, 1), (1, 1)))
neighborhood = padded[center_y:center_y+3, center_x:center_x+3]

print(f"中心点({center_x},{center_y})的3x3邻域:")
print(neighborhood)
print(f"\n原始锐化核:")
print(sharpen_kernel)
print(f"\n绝对值归一化后的核:")
print(abs_normalized_kernel)

# 逐元素计算
print("\n=== 逐元素卷积计算 ===")
conv_elements = neighborhood * abs_normalized_kernel
print("邻域 × 归一化核:")
print(conv_elements)

conv_result = np.sum(conv_elements)
print(f"\n卷积结果总和: {conv_result:.6f}")

# 滤波强度处理
original_value = float(test_data[center_y, center_x])
filtered_value = original_value + 1.0 * (conv_result - original_value)

print(f"\n=== 滤波强度处理 ===")
print(f"原始值: {original_value}")
print(f"卷积结果: {conv_result:.6f}")
print(f"滤波强度: 1.0")
print(f"最终结果: {original_value} + 1.0 × ({conv_result:.6f} - {original_value}) = {filtered_value:.6f}")
print(f"四舍五入: {int(round(filtered_value))}")

# 创建可视化
fig, axes = plt.subplots(2, 3, figsize=(15, 10))

# 第一行：原始数据和核
im1 = axes[0, 0].imshow(test_data, cmap='viridis', aspect='equal')
axes[0, 0].set_title('原始数据')
for i in range(5):
    for j in range(5):
        axes[0, 0].text(j, i, str(test_data[i, j]), ha='center', va='center', color='white', fontsize=8)
plt.colorbar(im1, ax=axes[0, 0])

im2 = axes[0, 1].imshow(sharpen_kernel, cmap='RdBu', aspect='equal')
axes[0, 1].set_title('原始锐化核')
for i in range(3):
    for j in range(3):
        axes[0, 1].text(j, i, str(sharpen_kernel[i, j]), ha='center', va='center', fontsize=10)
plt.colorbar(im2, ax=axes[0, 1])

im3 = axes[0, 2].imshow(abs_normalized_kernel, cmap='RdBu', aspect='equal')
axes[0, 2].set_title('绝对值归一化核')
for i in range(3):
    for j in range(3):
        axes[0, 2].text(j, i, f'{abs_normalized_kernel[i, j]:.3f}', ha='center', va='center', fontsize=8)
plt.colorbar(im3, ax=axes[0, 2])

# 第二行：不同归一化方法的结果
im4 = axes[1, 0].imshow(result_none, cmap='viridis', aspect='equal')
axes[1, 0].set_title('无归一化结果')
for i in range(5):
    for j in range(5):
        axes[1, 0].text(j, i, f'{int(result_none[i, j])}', ha='center', va='center', color='white', fontsize=8)
plt.colorbar(im4, ax=axes[1, 0])

im5 = axes[1, 1].imshow(result_normal, cmap='viridis', aspect='equal')
axes[1, 1].set_title('普通归一化结果')
for i in range(5):
    for j in range(5):
        axes[1, 1].text(j, i, f'{int(result_normal[i, j])}', ha='center', va='center', color='white', fontsize=8)
plt.colorbar(im5, ax=axes[1, 1])

im6 = axes[1, 2].imshow(result_abs, cmap='viridis', aspect='equal')
axes[1, 2].set_title('绝对值归一化结果\n(C++实际使用)')
for i in range(5):
    for j in range(5):
        axes[1, 2].text(j, i, f'{int(result_abs[i, j])}', ha='center', va='center', color='white', fontsize=8)
plt.colorbar(im6, ax=axes[1, 2])

# 标记中心点
for ax in [axes[0, 0], axes[1, 0], axes[1, 1], axes[1, 2]]:
    ax.add_patch(plt.Rectangle((1.5, 1.5), 1, 1, fill=False, edgecolor='red', linewidth=2))

plt.tight_layout()
plt.show()

# 数值比较表
print("\n=== 中心点(2,2)数值比较 ===")
print(f"{'方法':<15} {'结果':<10} {'与C++差异':<10}")
print("-" * 35)
cpp_result = 82
print(f"{'无归一化':<15} {result_none[2,2]:<10.2f} {abs(result_none[2,2] - cpp_result):<10.2f}")
print(f"{'普通归一化':<15} {result_normal[2,2]:<10.2f} {abs(result_normal[2,2] - cpp_result):<10.2f}")
print(f"{'绝对值归一化':<15} {result_abs[2,2]:<10.2f} {abs(result_abs[2,2] - cpp_result):<10.2f}")
print(f"{'C++实际输出':<15} {cpp_result:<10} {'0.00':<10}")