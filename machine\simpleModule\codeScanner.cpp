#include "codeScanner.h"

#include <QDebug>

#include "typeConvert.h"
#include "yoKoCodeScanEP.h"


CCodeScanner::CCodeScanner(IComm *port_) : im_port_(port_) {
    m_protocol_ = new CYoKoCodeScannerEP;

    //* varible init
    m_cmd["stop"] = m_protocol_->getControlCmd(EProtocolId::eSTOP);

    //* 此处应该可以选择数据接收方式
    im_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

    // #ifdef INIT_OUTPUT
    //     qDebug() << "mtb/ init: --------------------";
    //     qDebug() << "mtb/ start cmd: " << m_cmd["start"];
    //     qDebug() << "mtb/ stop cmd: " << m_cmd["stop"];
    // #endif
}

CCodeScanner::~CCodeScanner() {
    if (m_protocol_ != nullptr)
        delete m_protocol_;
}


/**
 * @brief CCodeScanner::getUartBaud
 * @return
 */
uint CCodeScanner::getUartBaud() {
    return 115200;
}

/**
 * @brief start
 * @return
 */
bool CCodeScanner::start(const uint8_t &id, const QByteArray &data) {
    EProtocolId protocol_id;
    QByteArray  data_s;
    uint8_t     zero = 0x00;

    data_s.append(data[0]);
    switch (id) {
    case 0:
        protocol_id = EProtocolId::eCONTINUAL;
        data_s.append(zero);
        data_s.append(zero);
        break;
    case 1:
        protocol_id = EProtocolId::eTIME;
        data_s.append(data[1]);
        data_s.append(data[2]);
        break;
    case 2:
        protocol_id = EProtocolId::eCYCLE;
        data_s.append(data[3]);
        data_s.append(data[4]);
        break;
    default:
        break;
    }

    m_cmd["start"] = m_protocol_->getWriteCmd(protocol_id, data_s);  //

    m_strPre.clear();
    return im_port_->write(m_cmd["start"]);
}

/**
 * @brief stop
 * @return
 */
bool CCodeScanner::stop(const QByteArray &data) {
    Q_UNUSED(data);

    return im_port_->write(m_cmd["stop"]);
}

QByteArray CCodeScanner::portDataRead() {
    return im_port_->read(10);
}

bool CCodeScanner::readInfo(const uint8_t &id, const uint16_t &data) {
    m_strPre.clear();
    QByteArray cmd = m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data));
#ifdef COMM_OUTPUT
    qDebug() << "-i codeScanner/ read: " << cmd;
#endif
    return im_port_->write(m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data)));  //
}

void CCodeScanner::changeIcomInterface(IComm *port_) {
    //* 此处应该可以选择数据接收方式
    im_port_ = port_;
    im_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析
}

bool CCodeScanner::interactionParsing(QByteArray str, int length) {
}

bool CCodeScanner::dataParsing(QByteArray str, const int &length) {
    //  Q_UNUSED(length);

    //  int i = 0;
    //  uint16_t num;
    //  uint8_t xor_tmp;

    //  QByteArray strSum = m_strPre + str;
    //  if(strSum.length() < 6) //
    //  {
    //      m_strPre = strSum;
    //      return false;
    //  }

    //  m_str_send.clear();

    //  /*1.1 接收数据显示*/
    ////  qDebug() << QString().sprintf("%02x",str);
    //  qDebug() << "-i mtb/ cmd ack:" << str;

    //  /*1.2 parse*/
    //  for(i = 0; i < (strSum.length() - 5); ++i) //留6个数
    //  {
    //      if(((uchar)strSum.at(i) == 0xA5) && ((uchar)strSum.at(i + 1) == 0x02)) //帧头
    //      {
    //          switch ((uchar)strSum.at(i + 2)) {
    //          case 0xAA: //start 指令成功
    //              if((uchar)strSum.at(i + 3) == 0x0D && (uchar)strSum.at(i + 4) == 0x00 && (uchar)strSum.at(i + 5) == 0x00)
    //              {
    //                  for(uint8_t n = 0; n < 6; ++n)
    //                  {
    //                      m_str_send.push_back(strSum.at(i + n)); //
    //                  }
    //                  emit dataOutput(ECommStep::eSTART_STEP, ECommStatus::eCOMM_COMP, m_str_send);
    //                  m_str_send.clear();
    //#if 0
    //                  strSum.remove(0, i + 6); //删除原有数据
    //                  i = 0;
    //#else
    //                  i += 5;
    //#endif
    //              }
    //              break;
    //          case 0x0B: //指令错误
    //              if((uchar)strSum.at(i + 3) == 0xAC && (uchar)strSum.at(i + 4) == 0x00 && (uchar)strSum.at(i + 5) == 0x00)
    //              {
    //                  for(uint8_t n = 0; n < 6; ++n)
    //                  {
    //                      m_str_send.push_back(strSum.at(i + n)); //
    //                  }
    //                  emit dataOutput(ECommStep::eSTART_STEP, ECommStatus::eCOMM_ERROR, m_str_send);
    //                  m_str_send.clear();
    //                  i += 5;
    //              }
    //              break;
    //          case 0x0A: //堵转
    //              if((uchar)strSum.at(i + 3) == 0xAD && (uchar)strSum.at(i + 4) == 0x00 && (uchar)strSum.at(i + 5) == 0x00)
    //              {
    //                  for(uint8_t n = 0; n < 6; ++n)
    //                  {
    //                      m_str_send.push_back(strSum.at(i + n)); //
    //                  }
    //                  emit dataOutput(ECommStep::eDATA_STEP, ECommStatus::eCOMM_FATAL, m_str_send);
    //                  m_str_send.clear();
    //                  i += 5;
    //              }
    //              break;
    //          case 0x0C: //捕获超时
    //              if((uchar)strSum.at(i + 3) == 0xAB && (uchar)strSum.at(i + 4) == 0x00 && (uchar)strSum.at(i + 5) == 0x00)
    //              {
    //                  for(uint8_t n = 0; n < 6; ++n)
    //                  {
    //                      m_str_send.push_back(strSum.at(i + n)); //
    //                  }
    //                  emit dataOutput(ECommStep::eDATA_STEP, ECommStatus::eCOMM_TIMEOUT, m_str_send);
    //                  m_str_send.clear();
    //                  i += 5;
    //              }
    //              break;
    //          case 0x0D: //数据接收完毕
    //              if((uchar)strSum.at(i + 3) == 0xAA && (uchar)strSum.at(i + 4) == 0x00 && (uchar)strSum.at(i + 5) == 0x00)
    //              {
    //                  for(uint8_t n = 0; n < 6; ++n)
    //                  {
    //                      m_str_send.push_back((uchar)strSum.at(i + n)); //
    //                  }
    //                  emit dataOutput(ECommStep::eCOMPLETE_STEP, ECommStatus::eCOMM_COMP, m_str_send);
    //                  m_str_send.clear();
    //                  i += 5;
    //              }
    //              break;
    //          case 0xB2: //数据反馈
    //              num = (uchar)strSum.at(i + 4) + uint16_t((uchar)strSum.at(i + 5)<<8);
    //              xor_tmp = 0;
    //              if((strSum.length() - i) >= (2*num + 6)) //数量符合
    //              {
    //                  for(uint16_t n = 0; n < 2*num + 6; ++n) {
    //                      if(n != 3)
    //                      {
    //                          xor_tmp ^= (uchar)strSum.at(i + n);
    //                      }
    //                      if(n > 5)
    //                          m_str_send.push_back(strSum.at(i + n)); //
    //                  }

    //                  if(xor_tmp == (uchar)strSum.at(i + 3)) {
    //                      emit dataOutput(ECommStep::eDATA_STEP, ECommStatus::eCOMM_COMP, m_str_send);
    //                      i += 2*num + 5;
    ////                      strSum.remove(0, (i + 2*num + 6));
    ////                      m_strPre.clear();
    ////                      m_strPre.push_back(strSum); //存储剩余数据
    //                  }
    //                  else
    //                  {
    //                     emit dataOutput(ECommStep::eDATA_STEP, ECommStatus::eCOMM_ERROR, m_str_send);
    //                     m_str_send.clear();
    //                  }
    //              }
    //              else
    //              {
    //                  strSum.remove(0, i); //i-1
    //                  m_strPre.clear();
    //                  m_strPre.push_back(strSum); //存储剩余数据
    //                  return false;
    //              }
    //              break;
    //          default:   //无效指令
    //              break;
    //          } //
    //      }
    //  }
    //  strSum.remove(0, i);
    //  m_strPre.clear();
    //  m_strPre.push_back(strSum); //存储剩余数据
    //  return false; //未接收speed数据
}
