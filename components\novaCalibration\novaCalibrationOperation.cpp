#include "novaCalibrationOperation.h"
#include <QTimer>
#include <QDebug>
#include <QMessageBox>
#include <QStandardPaths>

#include "pmsA1.h"
//#include "pmsB1.h"
//#include "pmsA1B.h"

#include "currentModule.h"
#include "stepperMotor.h"
#include "codeScanner.h"

#include "uart.h"
#include "loadXml.h"
#include "saveExcel.h"
#include "typeConvert.h"
#include "qLog.h"

#define TIMER_MS            5
#define MAIN_UART_BAUD      9600
#define CURR_UART_BAUD      9600
#define BOARD_UART_BAUD     115200
#define CODE_SCAN_UART_BAUD 115200


CNovaCalibOpt::CNovaCalibOpt(const NNovaCalibOpt::StUiConfig &st_config):
    mi_load_(new CLoadXml)
  , mst_config_(&st_config) //
  , m_timerId(0)
  , m_port_update_cnt(0)
  , mc_processList_(new TNova_process)
  , mst_task_status_(new TNova_process::StStepRecord)
  , mi_icomm_(new CUART("", MAIN_UART_BAUD)) //默认配置
  , mi_icomm_curr_(new CUART("", CURR_UART_BAUD)) //电流端口
  , mi_icomm_motor_(new CUART("", BOARD_UART_BAUD))
  , mi_icomm_code_scan_(new CUART("", CODE_SCAN_UART_BAUD))
  , mi_icomm_reserve_(new CUART("", CODE_SCAN_UART_BAUD))
  , mi_spms_(new CPmsA1(mi_icomm_, mi_icomm_motor_))
  , mi_current_module_(new CCurrentModule(mi_icomm_curr_))
  , mi_stepper_motor_(new CStepperMotor(mi_icomm_motor_))
  , mi_code_scanner_(new CCodeScanner(mi_icomm_code_scan_))
  , mst_comm_status_(new StCommunicateStatus)
  , mst_result_(new StResult)
  , m_message_box_(new QWidget)
  , mi_save_file_(new CSaveExcel)
{
    //********************************** varibles init ******************************
    m_cur_project_name = mst_config_->project;

    m_xml_param["board_material"] = 1; //1-白卡
    m_xml_param["motor_direction"] = 1;

    QString filename = QApplication::applicationDirPath() + "/config/novaCalib_config.xml";
    mi_load_->readParam(filename, &m_xml_param);

    //**********************************************子线程***********************
    //* 1.初始化就创建子线程
    qDebug() << "-i novaCalib/ main thread id: " << QThread::currentThread();
    m_sub_thread_ = new QThread;// 创建线程对象

    /* 创建工作的类对象，千万不要指定给创建的对象指定父对象
   * 如果指定了: QObject::moveToThread: Cannot move objects with a parent
   */
    m_spms_thread_ = new CSpmsSerial(nullptr, mi_spms_);
    m_spms_thread_->moveToThread(m_sub_thread_); //将工作的类对象移动到创建的子线程对象中

    m_simple_module_thread_ = new CSimpleModuleSerial(nullptr); //
    m_simple_module_thread_->addDevice(mi_current_module_);
    m_simple_module_thread_->addDevice(mi_stepper_motor_);
    m_simple_module_thread_->addDevice(mi_code_scanner_);
    m_simple_module_thread_->moveToThread(m_sub_thread_);

    //* 1.3 启动线程
    m_sub_thread_->start();
    m_spms_thread_->taskIdChange(0);
    m_simple_module_thread_->taskIdChange(0);

    //******************************************* connect ***************************
    qRegisterMetaType<CNovaCalibOpt::EProcessStep>("CNovaCalibOpt::EProcessStep");

    connect(this, &CNovaCalibOpt::subThreadSignal,
            m_spms_thread_, &CSpmsSerial::loop);
    connect(m_sub_thread_, &QThread::finished,
            m_spms_thread_, &QObject::deleteLater);
    connect(m_sub_thread_, &QThread::finished,
            m_simple_module_thread_, &QThread::deleteLater);

    connect(this, &CNovaCalibOpt::subModuleThreadSignal,
            m_simple_module_thread_, &CSimpleModuleSerial::loop);
    connect(m_sub_thread_, &QThread::finished,
            m_spms_thread_, &QObject::deleteLater);
    connect(m_sub_thread_, &QThread::finished,
            m_simple_module_thread_, &QThread::deleteLater);

    //* 数据处理
    connect(dynamic_cast<CPmsA1*>(mi_spms_), &CPmsA1::dataOutput,
            this, &CNovaCalibOpt::dataReceive); //无法用多态实现

    connect(dynamic_cast<CCodeScanner*>(mi_code_scanner_), &CCodeScanner::dataOutput,
            this, &CNovaCalibOpt::moduleDataReceive); //
    connect(dynamic_cast<CStepperMotor*>(mi_stepper_motor_), &CStepperMotor::dataOutput,
            this, &CNovaCalibOpt::moduleDataReceive); //
    connect(dynamic_cast<CCurrentModule*>(mi_current_module_), &CCurrentModule::dataOutput,
            this, &CNovaCalibOpt::moduleDataReceive); //

    connect(dynamic_cast<CPmsA1*>(mi_spms_), &CPmsA1::calibValueAckSignal,
            this, &CNovaCalibOpt::calibView_slot);
    connect(dynamic_cast<CPmsA1*>(mi_spms_), &CPmsA1::triggerCntSignal,
            this, &CNovaCalibOpt::functionTestView_slot);

    //************************************* task list init *************************
    //* MAIN TASKS
    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::readyStep, &CNovaCalibOpt::readyStepAck, 400, 0)); //readyAck

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::chipId, &CNovaCalibOpt::chipIdAck, 0, 0)); //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::version, &CNovaCalibOpt::versionAck, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::codeScanner, &CNovaCalibOpt::codeScannerAck, 500, 0)); //

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::calib, nullptr, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::infoMode, &CNovaCalibOpt::infoModeAck, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::accuracyVerify, &CNovaCalibOpt::accuracyVerifyAck, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::workMode, &CNovaCalibOpt::workModeAck, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::functionTest, nullptr, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::current, &CNovaCalibOpt::currentAck, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CNovaCalibOpt::compStep, &CNovaCalibOpt::compAck, 0, 0));

    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    //* calibration tasks
//    calibTasksUpdate();

    //* accuracyVerify tasks
//    accuracyVerifyTasksUpdate();

    m_timerId = startTimer(TIMER_MS);

    //******************************** 3.数据库 ******************************
    //* local
    //* 3.数据库
    mm_result_data.clear();
    mm_result_data.insert("时间", ""); //无法排序
    mm_result_data.insert("芯片ID", "");
    mm_result_data.insert("bar code", "");
    mm_result_data.insert("xtalk peak", "");
    mm_result_data.insert("xtalk tof", "");
    mm_result_data.insert("offset", "");
    mm_result_data.insert("ref tof", "");

    mm_result_data.insert("point1", "");
    mm_result_data.insert("voltage", "");
    mm_result_data.insert("current", "");
    mm_result_data.insert("trigger", "");
    mm_result_data.insert("version", "");

    mm_result_data.insert("time", "");
    mm_result_data.insert("result", "");
    QString desk_path = QStandardPaths::writableLocation(QStandardPaths::DesktopLocation);

    mi_save_file_->createOneFile(desk_path, "/novaCalib", mm_result_data);

}

CNovaCalibOpt::~CNovaCalibOpt(){
    delete mi_load_;

    //* tasks
    delete mc_processList_;
    delete mst_task_status_;

    //* thread
    m_spms_thread_->m_task_id = 0;
    m_sub_thread_->quit();
    m_sub_thread_->wait();
    delete m_sub_thread_; //会析构 m_serial_thread_

    killTimer(m_timerId);
    QThread::msleep(50);

    qDebug() << "-i nova calib opt/ port delete";
    //* port
    if(mi_icomm_ != nullptr) delete mi_icomm_;
    if(mi_icomm_curr_ != nullptr) delete mi_icomm_curr_;
    if(mi_icomm_motor_ != nullptr) delete mi_icomm_motor_;
    if(mi_icomm_code_scan_ != nullptr) delete mi_icomm_code_scan_;
    if(mi_icomm_reserve_ != nullptr) delete mi_icomm_reserve_;

    //*
    delete mi_spms_;
    delete mi_code_scanner_;
    delete mi_stepper_motor_;
    delete mi_current_module_;

    delete mst_comm_status_;

    delete mst_result_;
    delete mi_save_file_;
    delete m_message_box_;
}

void CNovaCalibOpt::timerEvent(QTimerEvent *event)
{
    //  Q_UNUSED(event);

    if(m_timerId == event->timerId()) {
        //* 非任务task
        m_port_update_cnt++;
        if(m_port_update_cnt == 400) {
            m_port_update_cnt = 0;
            portlistUpdate();
        }

        //**************************** 业务 tasks
        //      if(mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_) == eFATAL) ;
        mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_);

        //* 状态界面显示更新
        if(mst_task_status_->cur_step != mst_task_status_->last_step) {
            mst_task_status_->last_step = mst_task_status_->cur_step;

            if(mst_task_status_->cur_status != mst_task_status_->last_status) {
                emit stepStatusSignal((int16_t)mst_task_status_->cur_step, (int16_t)mst_task_status_->cur_status);
            }
        }
    }
}

void CNovaCalibOpt::varibleInit()
{

    //* 运行状态
    m_spms_thread_->taskIdChange(0);
    m_simple_module_thread_->taskIdChange(0);

    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

//    mst_task_status_->auto_normal_mode = true;

    mst_task_status_->cur_step = eCOMPLETE;
    mst_task_status_->cur_status = eWAIT;
    mst_task_status_->last_step = eCOMPLETE;
    mst_task_status_->last_status = eWAIT;

    //* 原光斑数据
    m_origin_bytes.clear();

    //*

    //* 设备移动数据

    //* result data
    mst_result_->chip_id.clear();
}



void CNovaCalibOpt::resultInit(StResult *result_) {
    //    result_->result = true; //
    //    result_->mp_origin_data.clear();
    //    result_->map_matrix.clear();
}

/**
 * @brief CLenAdjustOpt::portlistUpdate
 * @return
 */
bool CNovaCalibOpt::portlistUpdate(void)
{
    QStringList *port_list_ = new QStringList;

    bool port_flag = mi_icomm_->scanPort(port_list_, mst_config_->cur_port_name); //列表名称变化
    //    emit portUpdateSignal(port_list_, port_flag);
    emit portUpdateSignal(port_list_);

    return true;
}

bool CNovaCalibOpt::openSerial_slot() {
    //* 端口信息变更，（不包括波特率）
    if(mi_icomm_->getPortName() != mst_config_->port_name) {
        if(mi_icomm_ != nullptr) delete mi_icomm_;
        mi_icomm_ = new CUART(mst_config_->port_name, MAIN_UART_BAUD);
        mi_spms_->icom_change_interface(mi_icomm_);
//        m_spms_thread_->device_change_interface(mi_spms_);
    }

//    //* code scanner
//    if(mi_icomm_code_scan_->getPortName() != mst_config_->codeScan_port_name) {
//        if(mi_icomm_code_scan_ != nullptr) delete mi_icomm_code_scan_;
//        mi_icomm_code_scan_ = new CUART(mst_config_->codeScan_port_name, CODE_SCAN_UART_BAUD);

//        mi_code_scanner_->changeIcomInterface(mi_icomm_code_scan_);
//        m_simple_module_thread_->deviceChangeInterface(1, mi_code_scanner_);
//    }

//    //* steep motor
//    if(mi_icomm_motor_->getPortName() != mst_config_->stepMotor_port_name) {
//        if(mi_icomm_motor_ != nullptr) delete mi_icomm_motor_;
//        mi_icomm_motor_ = new CUART(mst_config_->stepMotor_port_name, BOARD_UART_BAUD);

//        mi_stepper_motor_->changeIcomInterface(mi_icomm_motor_);
//        m_simple_module_thread_->deviceChangeInterface(2, mi_stepper_motor_);
//    }

//    //* current module
//    if(mi_icomm_curr_->getPortName() != mst_config_->current_port_name) {
//        if(mi_icomm_curr_ != nullptr) delete mi_icomm_curr_;
//        mi_icomm_curr_ = new CUART(mst_config_->current_port_name, CURR_UART_BAUD);

//        mi_current_module_->changeIcomInterface(mi_icomm_curr_);
//        m_simple_module_thread_->deviceChangeInterface(3, mi_current_module_);
//    }

    //* 端口检测
    if(!mi_icomm_->openPort()) return false;
//    if(!mi_icomm_code_scan_->openPort()) return false;
//    if(!mi_icomm_motor_->openPort()) return false;
//    if(!mi_icomm_curr_->openPort()) return false;
    mv_task_list[eREADY].flag.exec = true; //开始任务

    //* 接收线程
    emit serialShowSignal(true);

    return true;
}



bool CNovaCalibOpt::closeSerial_slot() {
    //* close port
    m_spms_thread_->taskIdChange(-1);
    m_simple_module_thread_->taskIdChange(-1);

    mi_icomm_->closePort(); //port close
//    mi_icomm_code_scan_->closePort(); //
//    mi_icomm_motor_->closePort();
//    mi_icomm_curr_->closePort();
    qDebug() << "-i nova calib opt/ port close";

    emit subThreadSignal(false); //子线程退出
    emit serialShowSignal(false);

    return true;
}

void CNovaCalibOpt::calib1SingleTmp_slot() {
    mv_task_list[eCALIB].flag.exec = true;
    mi_spms_->calibSingleTaskRun(ISpmsSoc::ECalibProcess::eCALIB1);
}

void CNovaCalibOpt::calib2SingleTmp_slot() {
    mi_spms_->calibSingleTaskRun(ISpmsSoc::ECalibProcess::eCALIB2);
}

void CNovaCalibOpt::emptyTriggerTmp_slot() {
    //* function test
    mv_task_list[eFUNCTION_TEST].flag.exec = true;

}

void CNovaCalibOpt::calibView_slot(const uint8_t &calib_index, const uint16_t &calib_value1, const uint16_t &calib_value2) {

    emit calibTasksViewSignal(calib_index, calib_value1, calib_value2);
}

void CNovaCalibOpt::functionTestView_slot(const uint8_t &cnt, const int16_t &dist, const int16_t &ref_dist) {
    emit functionTestTmpSignal(cnt, dist, ref_dist);
}

/**
 * @brief main module data receive
 * @param step
 * @param status
 * @param bytes
 */
void CNovaCalibOpt::dataReceive(IPms::ECommStep step, ECommStatus status, QByteArray bytes) {
    mst_comm_status_->comm_status = status;

    switch(step) {
    case IPms::eCHIP_ID:

        break;
    case IPms::eVERSION:
        break;
    case IPms::eMODE_CHANGE:
        switch (mst_task_status_->cur_step) {
        case EProcessStep::eINFO_MODE: mv_task_list[eINFO_MODE].flag.stop = true;  break;
        case EProcessStep::eWORK_MODE: mv_task_list[eWORK_MODE].flag.stop = true;  break;
        default: break;
        }
        break;
    case IPms::eALL_INFO: //all distance info, include peak confidence etc.

        break;
    case IPms::eDIST: //normal distance
        break;
    case IPms::eDDS:
        break;
    default: break;
    }
}

void CNovaCalibOpt::moduleDataReceive(ISimpleModule::ECommStep step, ECommStatus status, QByteArray bytes) {
    uint16_t num = bytes.length()>>2;

    mst_comm_status_->comm_status = status;
    if(num != 0) {
#if 0
        if(m_origin_bytes.length() == 0) m_origin_bytes = bytes;
        else qDebug() << "-clen: data receive busy";
#else
        m_origin_bytes = bytes; //直接覆盖
#endif
    }

    switch(step)
    {
    case ISimpleModule::eSTART_ACK:
//        switch (mst_task_status_->cur_step) {
//        case EProcessStep::eGREY_MODE: mv_task_list[eGREY_MODE].flag.stop = true;  break;
//        case EProcessStep::eCALIB_MODE: mv_task_list[eCALIB_MODE].flag.stop = true;  break;
//        default: break;
//        }
        break;
    case ISimpleModule::eMAIN_INFO_ACK:
        switch (mst_task_status_->cur_step) {
        case EProcessStep::eCODE_SCANNER: mv_task_list[eCODE_SCANNER].flag.stop = true; break;
//        case EProcessStep::eTEST: mv_task_list[eTEST].flag.stop = true; break;
        case EProcessStep::eCURRENT: mv_task_list[eCURRENT].flag.stop = true; break;
//        case EProcessStep::eRETEST2: mv_task_list[eRETEST2].flag.stop = true; break;
        default: break;
        }
        break;
    case ISimpleModule::eSTOP_ACK:
//        switch (mst_task_status_->cur_step) {
//        case EProcessStep::eMAP_DATA: mv_task_list[eMAP_DATA].flag.stop = true; break;
//        case EProcessStep::eTEST: mv_task_list[eTEST].flag.stop = true; break;
//        case EProcessStep::eRETEST: mv_task_list[eRETEST].flag.stop = true; break;
//        case EProcessStep::eRETEST2: mv_task_list[eRETEST2].flag.stop = true; break;
//        default: break;
//        }
        break;
    default:
        break;
    }
}

EExecStatus CNovaCalibOpt::readyStep() {
    if(mst_config_->project != m_cur_project_name) {

        m_cur_project_name = mst_config_->project;

        //* update project object
        delete mi_spms_;
        //* 用反射
        switch (m_cur_project_name) {
        case IPms::EProjectName::eNOVA_A1:
            mi_spms_ = new CPmsA1(mi_icomm_, mi_icomm_motor_);
            m_spms_thread_->device_change_interface(mi_spms_);

            connect(dynamic_cast<CPmsA1*>(mi_spms_), &CPmsA1::dataOutput,
                    this, &CNovaCalibOpt::dataReceive); //无法用多态实现
            break;
//        case IPms::EProjectName::eNOVA_A1B:
//            mi_spms_ = new CPmsA1B(mi_icomm_);
//            break;
//        case IPms::EProjectName::eNOVA_B1:
//            mi_spms_ = new CPmsB1(mi_icomm_);
//            break;
        default:
            mi_spms_ = new CPmsA1(mi_icomm_, mi_icomm_motor_);

            connect(dynamic_cast<CPmsA1*>(mi_spms_), &CPmsA1::dataOutput,
                    this, &CNovaCalibOpt::dataReceive); //无法用多态实现
            break;
        }

        //* calibration tasks
//        calibTasksUpdate();                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        m

        //* accuracyVerify tasks
//        accuracyVerifyTasksUpdate();
    }

    //* varibles init
    varibleInit();

    emit subThreadSignal(true);

    return eCOMP;
}

EExecStatus CNovaCalibOpt::readyStepAck() {
    return eWAIT;
}


EExecStatus CNovaCalibOpt::codeScanner() {
    //* bar code
    if(!mi_code_scanner_->readInfo(0, 0)) return eFATAL; //
    else {
        m_simple_module_thread_->taskIdChange(3);
        return eOK;
    }
}

EExecStatus CNovaCalibOpt::codeScannerAck() {
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        //      m_serial_thread_->task_id_change(4);

        mst_result_->bar_code = m_origin_bytes;
        m_origin_bytes.clear();

        emit chipIdAckSignal(mst_result_->chip_id);

        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;
}

EExecStatus CNovaCalibOpt::version() {
    //* 校正模式
    if(!mi_spms_->readInfo(0, 0)) return eFATAL;
    else {
        m_spms_thread_->taskIdChange(1);
        return eOK;
    }
}

EExecStatus CNovaCalibOpt::versionAck() {
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        mst_result_->firm_version = m_origin_bytes;
        m_origin_bytes.clear();

        emit chipIdAckSignal(mst_result_->chip_id);

        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;
}

EExecStatus CNovaCalibOpt::chipId() {
    //* 校正模式
    if(!mi_spms_->readInfo(0, 0)) return eFATAL;
    else {
//        m_spms_thread_->taskIdChange(1);
        return eOK;
    }
    return eOK;
}

EExecStatus CNovaCalibOpt::chipIdAck() {
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        //      m_serial_thread_->task_id_change(4);

        mst_result_->chip_id = m_origin_bytes;
        m_origin_bytes.clear();

        emit chipIdAckSignal(mst_result_->chip_id);

        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;
}

EExecStatus CNovaCalibOpt::calib() {
//    qDebug() << "" <<;

    m_spms_thread_->taskIdChange(1);
    m_simple_module_thread_->taskIdChange(2);

    return mi_spms_->calibTasksRun();
}

EExecStatus CNovaCalibOpt::calibAck() {
    return eOK;
}


EExecStatus CNovaCalibOpt::infoMode() { //默认数据输出 -> 应答模式 -> 切上送模式
    //* 光斑模式
    if(mi_spms_->modeChange(0)) {
        //m_spms_thread_->taskIdChange(1);
        return eOK;
    }
    else return eFATAL;
}

EExecStatus CNovaCalibOpt::infoModeAck() {
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_spms_thread_->taskIdChange(0);
        qDebug() << "info mode ok";
        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;
}



EExecStatus CNovaCalibOpt::accuracyVerify() {
    m_spms_thread_->taskIdChange(2); //auto receive data
    m_simple_module_thread_->taskIdChange(2);

    mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_);
    return eWAIT;
}

EExecStatus CNovaCalibOpt::accuracyVerifyAck() {
    m_spms_thread_->taskIdChange(0);

    return eCOMP;
}

EExecStatus CNovaCalibOpt::workMode() {
    //* 光斑模式
    if(mi_spms_->modeChange(1)) {
        m_spms_thread_->taskIdChange(1);
        return eOK;
    }
    else return eFATAL;
}

EExecStatus CNovaCalibOpt::workModeAck() {
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_spms_thread_->taskIdChange(0);
        qDebug() << "-i novacalib/ work mode ok";
        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;
}

EExecStatus CNovaCalibOpt::functionTest() {
    m_spms_thread_->taskIdChange(2);
    m_simple_module_thread_->taskIdChange(2);

    return mi_spms_->functionTestRun();
}

EExecStatus CNovaCalibOpt::functionTestAck() {
    return eOK;
}

EExecStatus CNovaCalibOpt::current() {
    if(!mi_current_module_->readInfo(0, 0)) return eFATAL; //
    else {
        m_simple_module_thread_->taskIdChange(2);
        return eOK;
    }
}

EExecStatus CNovaCalibOpt::currentAck() {
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        //      m_serial_thread_->task_id_change(4);

        mst_result_->current = m_origin_float;
        m_origin_bytes.clear();

        emit currentAckSignal(mst_result_->current);

        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;
}

EExecStatus CNovaCalibOpt::compStep() {
    QByteArray array;
    m_spms_thread_->taskIdChange(0);
    m_simple_module_thread_->taskIdChange(0);


    return eOK;
}

EExecStatus CNovaCalibOpt::compAck() {
    //* save data
    mm_result_data["时间"] = QString(QDateTime::currentDateTime().toString("yyyy-MM-dd-hh:mm:ss")); //时间戳，转速均值，最大值，最小值，最大值偏差，最小值偏差，结果

    //    QString chip_id_str(mst_result_->chip_id.toHex());
    //    mm_result_data["芯片ID"] = chip_id_str;
    //    mm_result_data["bar code"] = QString::number(mst_result_->origin_loc.at(0), 'f', 3);
    //    mm_result_data["xtalk peak"] = QString::number(mst_result_->origin_loc.at(2), 'f', 3);
    //    mm_result_data["xtalk tof"] = QString::number(mst_result_->origin_loc.at(1), 'f', 3);
    //    mm_result_data["offset"] = facula;
    //    mm_result_data["ref tof"] = QString::number(mst_result_->final_loc.at(0), 'f', 3);

    //    mm_result_data["point1"] = QString::number(mst_result_->final_loc.at(2), 'f', 3);
    //    mm_result_data["voltage"] = QString::number(mst_result_->final_loc.at(1), 'f', 3);
    //    mm_result_data["current"] = QString::number(mst_result_->final_loc.at(2), 'f', 3);
    //    mm_result_data["trigger"] = QString::number(mst_result_->final_loc.at(2), 'f', 3);
    //    mm_result_data["version"] = QString::number(mst_result_->final_loc.at(1), 'f', 3);

    //    mm_result_data["time"] = QString(mst_result_->finalResult.result == 1?"PASS":"NG");
    //    mm_result_data["result"] = mst_task_status_->exec_time;

    mi_save_file_->writeFile(mm_result_data);

    return eCOMP;
}

