{"phases": [{"id": "product_info", "name": "产品信息", "weight": 0.1, "order": 1, "required_docs": ["product_brief.md", "market_analysis.md"]}, {"id": "requirements", "name": "需求分析", "weight": 0.2, "order": 2, "required_docs": ["market_requirements.md", "technical_requirements.md", "requirement_traceability.md"]}, {"id": "design", "name": "设计", "weight": 0.3, "order": 3, "required_docs": ["system_architecture.md", "interface_specifications.md", "design_decisions.md"]}, {"id": "development", "name": "开发", "weight": 0.3, "order": 4, "required_docs": ["hardware_design.md", "firmware_development.md", "software_development.md"]}, {"id": "quality", "name": "质量验证", "weight": 0.1, "order": 5, "required_docs": ["test_plans.md", "test_reports.md", "issue_tracking.md"]}], "project_name": "16. line lidar", "project_type": "hardware+firmware+software"}