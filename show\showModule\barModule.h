#ifndef _BAR_MODULE_H
#define _BAR_MODULE_H

#include <QWidget>
#include <QMessageBox>
#include <QDebug>
#include <qcustomplot.h>

typedef struct{
    double key;
    double value;
}KEY_VALUE;

typedef struct{
    QSharedPointer<QCPBarsDataContainer> fgHandleData;
    QSharedPointer<QCPBarsDataContainer> cycleHandleData;

    double centroidPos[10]; //不同光斑处理方式质心数据
    uint16_t dist[10];
    uint8_t percentValue;
    uint16_t dist_max[10];
    uint16_t dist_min[10];
}CentroidDistData;

class CBarModule : public QWidget
{
    Q_OBJECT
public:
    CBarModule(QCustomPlot &customPlot);
    ~CBarModule();
//    float StandardDeviation(double* array, uint8_t n);
//    float StandardDeviation(uint16_t* array, uint8_t n);
    float StandardDeviation(QVector<double> array, uint8_t n);
    float StandardDeviation(QVector<uint16_t> array, uint8_t n);
//    bool dataHandle(const QByteArray &fg_data_original, const QByteArray &cycle_data_original, CentroidDistData* centroid_dist);
    QSharedPointer<QCPBarsDataContainer> dataHandle(const QVector<uint16_t> &data);
    QSharedPointer<QCPBarsDataContainer> dataHandle1(uint16_t start_index, const QVector<uint16_t> &data);
    void customPlotShow(uint8_t layout_select, const QSharedPointer<QCPBarsDataContainer> &data_show);
    void afterGlowGraphNumChange(QVector<QCPGraph*> &mainGraphCosPoint, QPen *pen, uint8_t graph_num, uint8_t &graph_num_cur);
    void wavePointTextShow(const uint8_t &index, QCPItemText** wavePointText, QPen *pen, QVector<double> centroid_arr, QVector<uint16_t> dist_arr, float position_offset, CentroidDistData* centroid_dist);
    void subCentroidShow(QCPGraph** subCentroidGraph, QPen *pen, QVector<QCPGraphData> dataCentroidSca);

    void plotSampleCurve(QCPGraph* sub_graph, QVector<KEY_VALUE> vec);
    void plotFitCurve(QVector<KEY_VALUE> vec);

    uint m_length;

private:
    QCustomPlot *customPlots = nullptr;

    QCPBars *mainBar = nullptr;
    QCPBars *subBar = nullptr;

//    QCPGraph *m_mainGraphCos = nullptr; //绘图元素 折线图(QCPGraph) 实时

//    QCPGraph *m_mainGraphCosPoint = nullptr;
//    QCPGraph *subGraphRandoPoint = nullptr;
    QCPItemText *wavePacketText = nullptr;
    QCPAxisRect *mainRectRight = nullptr;
    QCPAxisRect *subRectLeft = nullptr;
    QCPAxisRect *subRectRight = nullptr;

signals:

private slots:
    void mouseDouClick(QMouseEvent* event);
};

#endif // CUSTOMPLOTWAVE_H
