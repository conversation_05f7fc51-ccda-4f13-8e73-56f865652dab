# ImageProcessing模块 CMakeLists.txt
cmake_minimum_required(VERSION 3.5)

project(imageProcessing LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 启用Qt相关设置
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

# 查找Qt5组件
find_package(Qt5 COMPONENTS Core Gui REQUIRED)

# 设置头文件目录
include_directories(${PROJECT_SOURCE_DIR})
include_directories(${PROJECT_SOURCE_DIR}/common)
include_directories(${PROJECT_SOURCE_DIR}/interfaces)
include_directories(${PROJECT_SOURCE_DIR}/interpolation)
include_directories(${PROJECT_SOURCE_DIR}/filters)
include_directories(${PROJECT_SOURCE_DIR}/factories)
include_directories(${PROJECT_SOURCE_DIR}/adapters)

# 收集源文件
set(COMMON_SOURCES
    common/ImageData.h
    common/ProcessingException.h
    common/ValidationUtils.h
)

set(INTERFACE_SOURCES
    interfaces/IInterpolation.h
    interfaces/IInterpolation.cpp
    interfaces/IImageFilter.h
    interfaces/IImageFilter.cpp
)

set(INTERPOLATION_SOURCES
    interpolation/BilinearInterpolation.h
    interpolation/BilinearInterpolation.cpp
    interpolation/NoInterpolation.h
    interpolation/NoInterpolation.cpp
)

set(FILTER_SOURCES
    filters/KalmanFilter.h
    filters/KalmanFilter.cpp
    filters/ConvolutionFilter.h
    filters/ConvolutionFilter.cpp
    filters/WeightedAverageFilter.h
    filters/WeightedAverageFilter.cpp
)

set(FACTORY_SOURCES
    factories/InterpolationFactory.h
    factories/InterpolationFactory.cpp
    factories/FilterFactory.h
    factories/FilterFactory.cpp
)

set(ADAPTER_SOURCES
    adapters/LegacyInterpolationAdapter.h
    adapters/LegacyInterpolationAdapter.cpp
)

# 合并所有源文件
set(ALL_SOURCES
    ${COMMON_SOURCES}
    ${INTERFACE_SOURCES}
    ${INTERPOLATION_SOURCES}
    ${FILTER_SOURCES}
    ${FACTORY_SOURCES}
    ${ADAPTER_SOURCES}
)

# 创建静态库
add_library(${PROJECT_NAME} STATIC ${ALL_SOURCES})

# 链接Qt库
target_link_libraries(${PROJECT_NAME} 
    Qt5::Core 
    Qt5::Gui
)

# 设置编译选项
target_compile_options(${PROJECT_NAME} PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:/W4>
    $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# 设置预处理器定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    QT_DEPRECATED_WARNINGS
    QT_DISABLE_DEPRECATED_BEFORE=0x060000
)

# 导出头文件路径
target_include_directories(${PROJECT_NAME} PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 安装规则（可选）
install(TARGETS ${PROJECT_NAME}
    EXPORT imageProcessingTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 安装头文件
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/
    DESTINATION include/imageProcessing
    FILES_MATCHING PATTERN "*.h"
)

# 生成配置文件（可选）
install(EXPORT imageProcessingTargets
    FILE imageProcessingTargets.cmake
    NAMESPACE ImageProcessing::
    DESTINATION lib/cmake/imageProcessing
)

# 显示构建信息
message(STATUS "ImageProcessing module configuration:")
message(STATUS "  - C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  - Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  - Qt5 Core: ${Qt5Core_VERSION}")
message(STATUS "  - Qt5 Gui: ${Qt5Gui_VERSION}")
message(STATUS "  - Source files: ${ALL_SOURCES}")

# 添加自定义目标用于代码格式化（可选）
find_program(CLANG_FORMAT_EXECUTABLE clang-format)
if(CLANG_FORMAT_EXECUTABLE)
    add_custom_target(format-imageprocessing
        COMMAND ${CLANG_FORMAT_EXECUTABLE} -i ${ALL_SOURCES}
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Formatting ImageProcessing source code"
    )
endif()

# 添加自定义目标用于静态分析（可选）
find_program(CPPCHECK_EXECUTABLE cppcheck)
if(CPPCHECK_EXECUTABLE)
    add_custom_target(cppcheck-imageprocessing
        COMMAND ${CPPCHECK_EXECUTABLE} 
            --enable=all 
            --std=c++17 
            --verbose 
            --quiet
            ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Running cppcheck on ImageProcessing module"
    )
endif()
