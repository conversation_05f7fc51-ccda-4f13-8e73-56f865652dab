#include "transferB.h"

#include <QDebug>
#include "testBoardP.h"
#include <QDebug>

const uint8_t CTransferB::start_cmd[5] = {0xAA, 0x20, 0x00, 0x00, 0x00};
const uint8_t CTransferB::stop_cmd[5] = {0xAA, 0x21, 0x00, 0x00, 0x00};

CTransferB::CTransferB(IComm *ctrl_port_):
    im_ctrl_port_(ctrl_port_)
{
  m_protocol_ = new CTestBoardP;

    //* cmd init
    //  m_cmd["start"] = m_protocol_->getWriteCmd(protocol_id, data); //
    //  m_cmd["stop"] = m_protocol_->getControlCmd(EPROTOCOL_ID::eSTOP);
    QByteArray cmd_array;
    memcpy(cmd_array.data(), (char*)start_cmd, sizeof(start_cmd));//copy数据
    m_cmd["start"] = cmd_array;
    memcpy(cmd_array.data(), (char*)stop_cmd, sizeof(stop_cmd));
    m_cmd["stop"] = cmd_array;

    //* 此处应该可以选择数据接收方式
    im_ctrl_port_->setBufferSize(2000); //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

#ifdef INIT_OUTPUT
    qDebug() << "tfb/ init: --------------------";
    qDebug() << "tfb/ start cmd: " << m_cmd["start"];
    qDebug() << "tfb/ stop cmd: " << m_cmd["stop"];
#endif

}

CTransferB::~CTransferB(){
    if(im_ctrl_port_ != nullptr) delete im_ctrl_port_;
    delete m_protocol_;
}

/**
 * @brief start
 * @return
 */
bool CTransferB::start(const uint8_t& id, const QByteArray &data)
{
    Q_UNUSED(id);
    Q_UNUSED(data);

#ifdef COMM_OUTPUT
    qDebug() << "mtb/ start cmd: " << m_cmd["start"];
#endif

    return im_ctrl_port_->write(m_cmd["start"]);
}

/**
 * @brief stop
 * @return
 */
bool CTransferB::stop(const QByteArray &data)
{
    Q_UNUSED(data);

    return im_ctrl_port_->write(m_cmd["stop"]);
}

#if 1
/**
 * @brief 数据解析
 * @param str
 * @param length
 * @return
 */
bool CTransferB::dataParsing(QByteArray str, const int &length)
{
    Q_UNUSED(length);

    qDebug() << "-i: tfb" << str << "length:" << str.length();

    QByteArray strSum = m_strPre + str;

    if(strSum.length() < 13) //固定部分长度6
    {
        m_strPre = strSum;
        return false;
    }

    QByteArray strTmp; //指令数据
    QVector<uint32_t> data_tmp_v;
    int i = 0;

    /*1.1 parse*/
    for(i = 0; i < (strSum.length() - 12); ++i) //留12个数
    {
        if(((uchar)strSum.at(i) == 0xAA)) //帧头
        {
            switch ((uchar)strSum.at(i + 1)) {    //
            case 0x20: //上电ack
                if(((uchar)strSum.at(i + 2) == 0x0D) && ((uchar)strSum.at(i + 3) == 0x0A)) //数量符合
                {
                    strSum.remove(0, 13);
                    strTmp.clear();
                    m_strPre.clear();
                    m_strPre.push_back(strSum); //存储剩余数据
                    //emit dataOutput(ThreeStep::EProcessStep::eSTART_STEP, ThreeStep::EStepStatus::eOK, m_str_send);
                    return true;
                }
                else
                {
                    strSum.remove(0, i); //i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum); //存储剩余数据
                    return false;
                }
                break;
            case 0x21: //断电
                if(((uchar)strSum.at(i + 2) == 0x0D) && ((uchar)strSum.at(i + 3) == 0x0A)) //数量符合
                {
                    strSum.remove(0, 14);
                    strTmp.clear();
                    m_strPre.clear();
                    m_strPre.push_back(strSum); //存储剩余数据
                    //emit dataOutput(ThreeStep::EProcessStep::eSTART_STEP, ThreeStep::EStepStatus::eOK, m_str_send);
                    return true;
                }
                else
                {
                    strSum.remove(0, i); //i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum); //存储剩余数据
                    return false;
                }
                break;
            default:   //无效指令
                ++i; //
                break;
            } //
        }
    }
    strSum.remove(0, i - 1);
    m_strPre.clear();
    m_strPre.push_back(strSum); //存储剩余数据
    return true;
}
#else
bool CTransferB::dataParsing(QByteArray str, const int &length)
{
    Q_UNUSED(length);

    int i = 0;

    QByteArray strSum = m_strPre + str;
    if(strSum.length() < 13) //
    {
        m_strPre = strSum;
        return false;
    }

    m_str_send.clear();

    /*1.1 接收数据显示*/
    //    qDebug() << QString().sprintf("%02x",str);
    qDebug() << str;

    /*1.2 parse*/
    for(i = 0; i < (strSum.length() - 12); ++i) //留12个数
    {
        if((uchar)strSum.at(i) == 0xAA) //帧头
        {
            switch ((uchar)strSum.at(i + 1)) {
            case 0x20: //power on ack
                if((uchar)strSum.at(i + 2) == 0x0D && (uchar)strSum.at(i + 3) == 0x0A)
                {
                    //emit dataOutput(ThreeStep::EProcessStep::eSTART_STEP, ThreeStep::EStepStatus::eOK, m_str_send);
                    m_str_send.clear();
#if 0
                    strSum.remove(0, i + 6); //删除原有数据
                    i = 0;
#else
                    i += 13;
#endif
                }
                break;
            case 0x21: //power off ack
                if((uchar)strSum.at(i + 2) == 0x0D && (uchar)strSum.at(i + 3) == 0x0A)
                {
                    //         emit dataOutput(ThreeStep::EProcessStep::eSTART_STEP, ThreeStep::EStepStatus::eERROR, m_str_send);
                    i += 14;
                }
                break;
            default:   //无效指令
                break;
            } //
        }
    }
    strSum.remove(0, i - 1);
    m_strPre.clear();
    m_strPre.push_back(strSum); //存储剩余数据
    return false; //未接收speed数据
}
#endif
