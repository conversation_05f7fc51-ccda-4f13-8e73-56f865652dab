#include "simpleInteractionP.h"
#include <qdebug.h>
#include "qLog.h"

CSimpleInteraction::CSimpleInteraction():
    mst_interaction_frame_(new StInteractionFrame)
{
    mst_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
}

CSimpleInteraction::~CSimpleInteraction(){
    delete mst_interaction_frame_;
}

QByteArray CSimpleInteraction::getControlCmd(const char &id)
{
    QByteArray cmd;
    StInteractionFrame* st_interaction_frame_ = new StInteractionFrame;

    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    st_interaction_frame_->cmd = (uint8_t)ECmd::eW | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
    st_interaction_frame_->id = id;
    st_interaction_frame_->num = 0;

    st_interaction_frame_->checkXor = calXOR((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN, (uint8_t)EInteractionFrame::eXOR_INDEX);

    cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CSimpleInteraction::getWriteCmd(const char &id, const QByteArray &w_data)
{
    QByteArray w_cmd;
    StInteractionFrame* st_interaction_frame_ = new StInteractionFrame;

    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    st_interaction_frame_->cmd = (uint8_t)ECmd::eW | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
    st_interaction_frame_->id = id;
    st_interaction_frame_->num = w_data.length();

    //mst_interaction_frame_->data.resize(mst_interaction_frame_->num);
    if(w_data.length() > DATA_CACHE) {
        qDebug() << "-e simpleInteractionP/ write cmd data out off buff length";
        return w_cmd;
    }

    for(uint8_t for_i = 0; for_i < w_data.size(); for_i++) {
        st_interaction_frame_->data[for_i] = (w_data.at(for_i));
    }
    //  w_cmd.append((char*)mst_receive_frame_, (EFrame::eHEADER_LEN )); //+ w_data.length())); //直接拷贝整个 qbytearray数据错乱
    st_interaction_frame_->checkXor = calXOR((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN + st_interaction_frame_->num, (uint8_t)EInteractionFrame::eXOR_INDEX);

    w_cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN + st_interaction_frame_->num);

    return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CSimpleInteraction::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{
    QByteArray r_cmd;
    StInteractionFrame* st_interaction_frame_ = new StInteractionFrame;

    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    st_interaction_frame_->cmd = (uint8_t)ECmd::eR | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
    st_interaction_frame_->id = id;
    st_interaction_frame_->num = 0;

    st_interaction_frame_->checkXor = calXOR((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN, (uint8_t)EInteractionFrame::eXOR_INDEX);

    r_cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return r_cmd;
}


