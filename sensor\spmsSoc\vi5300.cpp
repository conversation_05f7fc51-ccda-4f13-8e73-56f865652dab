#include "vi5300.h"

CSpmsVi5300::CSpmsVi5300() {
//    StCalibItems calib_items_tmp;
//    calib_items_tmp.calib_item_name = "xtalk_calib";
//    calib_items_tmp.peak = xml_param["xtalk_peak"];
//    calib_items_tmp.tof = xml_param["xtalk_tof"];
//    calib_items_tmp.cal = 0;
//    m_calib_items.append(calib_items_tmp);

//    calib_items_tmp.calib_item_name = "ref_calib";
//    calib_items_tmp.peak = 0;
//    calib_items_tmp.tof = xml_param["ref_tof"];
//    calib_items_tmp.cal = xml_param["ref_offset"];
//    m_calib_items.append(calib_items_tmp);

//    StTaskInfo task;
//    task.cmd = 0;
//    task.ptr_ = (typeFptr_)&CSpmsVi5300::calibTask1;
//    task.ack_ptr_ = (typeFptr_)&CSpmsVi5300::calibTask1Ack;
//    mm_calib_flow.insert("xtalk", task);

//    task.cmd = 0;
//    task.ptr_ = (typeFptr_)&CSpmsVi5300::calibTask2;
//    task.ack_ptr_ = (typeFptr_)&CSpmsVi5300::calibTask2Ack;
//    mm_calib_flow.insert("ref_offset", task);

//    mm_calib_flow.insert("xtalk", 0);
//    mm_calib_flow.insert("ref_tof", 0);
    mm_calib_flow.insert(ECalibProcess::eCALIB1, "xtalk");
    mm_calib_flow.insert(ECalibProcess::eCALIB2, "ref_tof");
}

QString CSpmsVi5300::get_class_name() {

}


EExecStatus CSpmsVi5300::calibTask1() {

}

EExecStatus CSpmsVi5300::calibTask1Ack() {

}

EExecStatus CSpmsVi5300::calibTask2() {

}

EExecStatus CSpmsVi5300::calibTask2Ack() {

}

EExecStatus CSpmsVi5300::calibTask3() {

}

EExecStatus CSpmsVi5300::calibTask3Ack() {

}

EExecStatus CSpmsVi5300::calibTask4() {

}

EExecStatus CSpmsVi5300::calibTask4Ack() {

}
