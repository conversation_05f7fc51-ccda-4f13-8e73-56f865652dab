#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web可视化服务器

提供HTTP服务器功能，支持API和静态内容服务
"""

import socket
import threading
import time
import webbrowser
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from pathlib import Path
from enum import Enum

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import VisualizationMode
from core.data_adapters import MultiModeDataAdapter

class EnumEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理枚举类型"""
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)

class WebVisualizationServer:
    """Web可视化服务器 - 单一职责：HTTP服务和API处理"""
    
    def __init__(self, port=8080):
        self.port = self._find_available_port(port)
        self.html_content = ""
        self.project_path = ""
        self.data_adapter = None
    
    def _find_available_port(self, preferred_port=8080):
        """查找可用端口"""
        for port in range(preferred_port, preferred_port + 50):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except socket.error:
                continue
        
        # 如果前50个端口都不可用，使用系统分配
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', 0))
            return s.getsockname()[1]
    
    def setup(self, html_content: str, project_path: str):
        """设置服务器内容"""
        self.html_content = html_content
        self.project_path = project_path
        self.data_adapter = MultiModeDataAdapter(Path(project_path))
    
    def start(self, auto_open: bool = True, background: bool = False):
        """启动服务器"""
        if auto_open and not background:
            # 延迟打开浏览器
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{self.port}')
            
            threading.Thread(target=open_browser, daemon=True).start()
        
        print(f"可视化服务器已启动: http://localhost:{self.port}")
        print(f"项目路径: {self.project_path}")
        if self.port != 8080:
            print(f"注意: 端口8080不可用，已使用端口 {self.port}")
        
        if not background:
            print("按 Ctrl+C 停止服务器")
        
        try:
            with HTTPServer(('localhost', self.port), self._create_request_handler()) as httpd:
                if background:
                    # 后台运行
                    server_thread = threading.Thread(target=httpd.serve_forever, daemon=True)
                    server_thread.start()
                    return server_thread
                else:
                    # 前台运行
                    httpd.serve_forever()
        except KeyboardInterrupt:
            if not background:
                print("\n服务器已停止")
    
    def _create_request_handler(self):
        """创建请求处理器类"""
        server = self
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/':
                    # 主页面
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html; charset=utf-8')
                    self.end_headers()
                    self.wfile.write(server.html_content.encode('utf-8'))
                
                elif self.path.startswith('/api/visualization'):
                    # API请求
                    self._handle_api_request()
                
                elif self.path.startswith('/api/modes'):
                    # 模式列表API
                    self._handle_modes_request()
                
                else:
                    # 404
                    self.send_response(404)
                    self.end_headers()
            
            def _handle_api_request(self):
                """处理可视化API请求"""
                try:
                    parsed_url = urlparse(self.path)
                    params = parse_qs(parsed_url.query)
                    mode = params.get('mode', ['all'])[0]
                    
                    # 获取数据
                    data = server.data_adapter.extract_data(
                        Path(server.project_path), 
                        VisualizationMode(mode)
                    )
                    
                    # 转换为JSON兼容格式
                    response_data = {
                        "title": data.title,
                        "mode": data.mode.value,
                        "nodes": [self._node_to_dict(node) for node in data.nodes],
                        "edges": [self._edge_to_dict(edge) for edge in data.edges],
                        "metadata": data.metadata
                    }
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    self.wfile.write(json.dumps(response_data, ensure_ascii=False, cls=EnumEncoder).encode('utf-8'))
                    
                except Exception as e:
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    error_response = {"error": str(e), "type": "server_error"}
                    self.wfile.write(json.dumps(error_response).encode('utf-8'))
            
            def _handle_modes_request(self):
                """处理模式列表请求"""
                try:
                    modes = [
                        {"id": "all", "name": "综合视图", "icon": "🌐"},
                        {"id": "workflow", "name": "工作流", "icon": "🔄"},
                        {"id": "documents", "name": "文档关联", "icon": "📋"},
                        {"id": "traceability", "name": "追溯链", "icon": "🔗"},
                        {"id": "progress", "name": "进度", "icon": "📊"},
                        {"id": "structure", "name": "产品结构", "icon": "🏗️"}
                    ]
                    
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json; charset=utf-8')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    self.wfile.write(json.dumps({"modes": modes}, ensure_ascii=False, cls=EnumEncoder).encode('utf-8'))
                    
                except Exception as e:
                    self.send_response(500)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    error_response = {"error": str(e)}
                    self.wfile.write(json.dumps(error_response).encode('utf-8'))
            
            def _node_to_dict(self, node):
                """将Node对象转换为字典"""
                return {
                    "id": node.id,
                    "name": node.name,
                    "type": node.type,
                    "component": node.component,
                    "x": node.x,
                    "y": node.y,
                    **node.properties
                }
            
            def _edge_to_dict(self, edge):
                """将Edge对象转换为字典"""
                return {
                    "source": edge.source,
                    "target": edge.target,
                    "type": edge.type,
                    **edge.properties
                }
            
            def log_message(self, format, *args):
                # 抑制默认日志输出
                pass
        
        return RequestHandler
    
    def stop(self):
        """停止服务器"""
        # 这个方法用于优雅地停止服务器
        pass 