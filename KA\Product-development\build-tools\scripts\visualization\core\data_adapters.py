#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据适配器 - 重构为模块化架构

单一职责：协调各个专门的数据提取器，不再直接处理具体的数据提取逻辑
"""

import sys
from pathlib import Path

# 添加contents模块到路径
sys.path.append(str(Path(__file__).parent.parent / "contents"))

from .interfaces import DataAdapter, VisualizationData, VisualizationMode
from contents.workflow_module import WorkflowDataExtractor
from contents.documents_module import DocumentsDataExtractor
from contents.traceability_module import TraceabilityDataExtractor
from contents.progress_module import ProgressDataExtractor
from contents.product_structure_module import ProductStructureDataExtractor

class MultiModeDataAdapter(DataAdapter):
    """多模式数据适配器 - 单一职责：协调各个专门的数据提取器"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
        
        # 初始化各个专门的数据提取器
        self.extractors = {
            VisualizationMode.WORKFLOW: WorkflowDataExtractor(project_path),
            VisualizationMode.DOCUMENTS: DocumentsDataExtractor(project_path),
            VisualizationMode.TRACEABILITY: TraceabilityDataExtractor(project_path),
            VisualizationMode.PROGRESS: ProgressDataExtractor(project_path),
            VisualizationMode.STRUCTURE: ProductStructureDataExtractor(project_path)
        }
    
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """从项目中提取指定模式的可视化数据"""
        self.project_path = Path(project_path)
        
        # 更新所有提取器的项目路径
        for extractor in self.extractors.values():
            extractor.project_path = self.project_path
        
        if mode == VisualizationMode.ALL:
            return self._extract_combined_data()
        elif mode in self.extractors:
            return self.extractors[mode].extract_data(project_path, mode)
        else:
            raise ValueError(f"不支持的可视化模式: {mode}")
    
    def _extract_combined_data(self) -> VisualizationData:
        """提取综合数据 - 合并所有模式的数据"""
        all_nodes = []
        all_edges = []
        combined_metadata = {}
        
        # 从各个专门的提取器获取数据并合并
        for mode, extractor in self.extractors.items():
            try:
                data = extractor.extract_data(self.project_path, mode)
                all_nodes.extend(data.nodes)
                all_edges.extend(data.edges)
                
                # 合并元数据
                combined_metadata[mode.value] = data.metadata
                
            except Exception as e:
                print(f"提取{mode.value}模式数据失败: {e}")
                combined_metadata[mode.value] = {"error": str(e)}
        
        return VisualizationData(
            title="产品体系综合视图",
            mode=VisualizationMode.ALL,
            nodes=all_nodes,
            edges=all_edges,
            metadata={
                "combined_from": list(self.extractors.keys()),
                "total_nodes": len(all_nodes),
                "total_edges": len(all_edges),
                "module_data": combined_metadata
            }
        )
    
    def get_extractor(self, mode: VisualizationMode) -> DataAdapter:
        """获取指定模式的数据提取器"""
        if mode in self.extractors:
            return self.extractors[mode]
        else:
            raise ValueError(f"不支持的可视化模式: {mode}")
    
    def add_extractor(self, mode: VisualizationMode, extractor: DataAdapter):
        """添加新的数据提取器 - 支持扩展"""
        self.extractors[mode] = extractor
    
    def list_available_modes(self) -> list:
        """列出可用的可视化模式"""
        return list(self.extractors.keys()) + [VisualizationMode.ALL] 