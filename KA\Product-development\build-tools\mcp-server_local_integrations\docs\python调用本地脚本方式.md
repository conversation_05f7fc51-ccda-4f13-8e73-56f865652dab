# Python调用本地脚本的几种方式

## 方法对比表

| 方法 | 适用场景 | 优点 | 缺点 | 阻塞性 | Windows兼容性 |
|------|----------|------|------|--------|----------------|
| `subprocess.run()` | 简单脚本，短时间执行 | 简单易用，同步执行 | 容易阻塞，管道死锁风险 | 阻塞 | ⚠️ 有风险 |
| `subprocess.Popen()` + `communicate()` | 短脚本，需要输出 | 避免死锁，有超时控制 | 仍然阻塞主线程 | 阻塞 | ✅ 良好 |
| `subprocess.Popen()` + 独立线程 | 长时间运行脚本 | 非阻塞，实时输出 | 复杂度高 | 非阻塞 | ✅ 良好 |
| `runpy.run_path()` | 简单脚本，同进程 | 快速，无进程开销 | 共享环境，可能冲突 | 阻塞 | ✅ 良好 |

## 1. runpy.run_path() - 同进程执行方式

```python
import runpy
import sys
import os
import io
import contextlib

def run_script_with_runpy(script_path, args=None):
    """使用runpy在当前进程中执行脚本"""
    if not os.path.exists(script_path):
        return {"success": False, "error": f"脚本文件不存在: {script_path}"}
    
    try:
        # 保存原始环境
        original_argv = sys.argv.copy()
        original_cwd = os.getcwd()
        
        # 设置脚本参数
        sys.argv = [str(script_path)]
        if args:
            sys.argv.extend(args)
        
        # 切换到脚本目录
        script_dir = os.path.dirname(script_path)
        if script_dir:
            os.chdir(script_dir)
        
        # 捕获输出
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        try:
            with contextlib.redirect_stdout(stdout_capture), \
                 contextlib.redirect_stderr(stderr_capture):
                # 在当前进程中执行脚本
                runpy.run_path(str(script_path), run_name="__main__")
            
            return {
                "success": True,
                "stdout": stdout_capture.getvalue(),
                "stderr": stderr_capture.getvalue(),
                "returncode": 0
            }
            
        except SystemExit as e:
            return {
                "success": e.code == 0,
                "stdout": stdout_capture.getvalue(),
                "stderr": stderr_capture.getvalue(),
                "returncode": e.code or 0
            }
        except Exception as e:
            return {
                "success": False,
                "stdout": stdout_capture.getvalue(),
                "stderr": stderr_capture.getvalue() + f"\n执行异常: {str(e)}",
                "returncode": 1
            }
        finally:
            # 恢复原始环境
            sys.argv = original_argv
            os.chdir(original_cwd)
            
    except Exception as e:
        return {"success": False, "error": f"脚本执行失败: {str(e)}"}
```

**优点**: 
- 执行速度快，无进程创建开销
- 简单脚本测试时不会阻塞
- Windows兼容性好

**缺点**: 
- 共享Python环境，可能有变量冲突
- 脚本中的全局变量会影响主进程
- 不适合有复杂依赖的脚本

**适用场景**: 
- 简单的配置脚本
- 数据处理脚本
- 测试和调试阶段

## 2. subprocess.Popen() + communicate() - 推荐方式

```python
import subprocess
import sys
from subprocess import Popen, PIPE, TimeoutExpired

def run_script_with_communicate(script_path, args=None):
    """使用Popen + communicate()，避免死锁"""
    cmd = [sys.executable, str(script_path)]
    if args:
        cmd.extend(args)
    
    try:
        proc = Popen(
            cmd,
            stdout=PIPE,
            stderr=PIPE,
            text=True,
            cwd=script_path.parent if hasattr(script_path, 'parent') else None
        )
        
        # communicate()会等待进程完成并返回所有输出
        stdout, stderr = proc.communicate(timeout=60)
        
        return {
            "success": proc.returncode == 0,
            "stdout": stdout,
            "stderr": stderr,
            "returncode": proc.returncode
        }
        
    except TimeoutExpired:
        proc.kill()
        stdout, stderr = proc.communicate()
        return {
            "success": False,
            "stdout": stdout,
            "stderr": stderr + "\n脚本执行超时",
            "returncode": -1
        }
    except Exception as e:
        return {"success": False, "error": f"执行异常: {e}"}
```

**优点**: 避免死锁，有超时控制，Windows兼容性好
**缺点**: 仍然阻塞主线程

## 3. subprocess.Popen() + 独立线程 - 高级方式

```python
import subprocess
import threading
import queue
import sys
import os

def run_script_with_threads(script_path, args=None):
    """使用Popen + 独立线程，完全非阻塞"""
    cmd = [sys.executable, str(script_path)]
    if args:
        cmd.extend(args)
    
    try:
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            stdin=subprocess.PIPE,
            text=True,
            cwd=script_path.parent if hasattr(script_path, 'parent') else None,
            # Windows特定设置
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        )
        
        # 使用队列收集输出
        stdout_queue = queue.Queue()
        stderr_queue = queue.Queue()
        
        def reader(pipe, q):
            """读取管道输出到队列"""
            try:
                for line in iter(pipe.readline, ''):
                    q.put(line)
                pipe.close()
            except Exception as e:
                q.put(f"读取错误: {e}")
        
        # 启动独立线程读取输出
        stdout_thread = threading.Thread(target=reader, args=(proc.stdout, stdout_queue), daemon=True)
        stderr_thread = threading.Thread(target=reader, args=(proc.stderr, stderr_queue), daemon=True)
        
        stdout_thread.start()
        stderr_thread.start()
        
        # 等待进程完成
        try:
            returncode = proc.wait(timeout=60)
        except subprocess.TimeoutExpired:
            proc.kill()
            proc.wait()
            return {"success": False, "error": "脚本执行超时"}
        
        # 等待线程完成
        stdout_thread.join(timeout=5)
        stderr_thread.join(timeout=5)
        
        # 收集输出
        stdout_lines = []
        stderr_lines = []
        
        while not stdout_queue.empty():
            stdout_lines.append(stdout_queue.get())
        
        while not stderr_queue.empty():
            stderr_lines.append(stderr_queue.get())
        
        return {
            "success": returncode == 0,
            "stdout": ''.join(stdout_lines),
            "stderr": ''.join(stderr_lines),
            "returncode": returncode
        }
        
    except Exception as e:
        return {"success": False, "error": f"执行异常: {e}"}
```

**优点**: 完全非阻塞，实时输出，Windows兼容性最好
**缺点**: 实现复杂度高

## 使用建议

### 根据场景选择方式

1. **MCP服务器中的短脚本**: 使用 `subprocess.Popen() + communicate()`
2. **长时间运行的脚本**: 使用 `subprocess.Popen() + 独立线程`
3. **简单测试脚本**: 使用 `runpy.run_path()`
4. **生产环境**: 避免使用 `subprocess.run()`

### Windows特定注意事项

1. 使用 `creationflags=subprocess.CREATE_NEW_PROCESS_GROUP`
2. 避免长时间的 `capture_output=True`
3. 设置合理的超时时间
4. 使用独立线程读取输出避免死锁

### 总结

- **开发阶段**: 使用 `runpy.run_path()` 快速测试
- **生产环境**: 使用 `subprocess.Popen() + communicate()` 确保稳定性
- **Windows环境**: 优先选择 `subprocess.Popen()` 相关方法
