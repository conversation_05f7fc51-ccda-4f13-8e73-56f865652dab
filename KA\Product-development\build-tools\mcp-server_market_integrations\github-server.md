# GitHub MCP服务器使用指南

## 📋 服务器信息

- **名称**：@modelcontextprotocol/server-github
- **功能**：GitHub集成和仓库管理
- **官方仓库**：[GitHub](https://github.com/modelcontextprotocol/servers/tree/main/src/github)
- **状态**：✅ 推荐使用

## 🚀 功能特性

### 核心功能

1. **仓库管理**：创建、查看、管理GitHub仓库
2. **Issue管理**：创建、查看、更新Issues
3. **Pull Request**：创建、审查、合并PR
4. **文件操作**：读取、创建、更新仓库文件
5. **分支管理**：创建、切换、合并分支
6. **搜索功能**：搜索仓库、代码、用户

### 支持的操作

- `create_repository` - 创建新仓库
- `get_repository` - 获取仓库信息
- `list_repositories` - 列出用户仓库
- `create_issue` - 创建Issue
- `get_issue` - 获取Issue详情
- `create_pull_request` - 创建PR
- `get_file_contents` - 获取文件内容
- `create_or_update_file` - 创建或更新文件
- `search_repositories` - 搜索仓库
- `fork_repository` - Fork仓库

## 🔧 配置方法

### 基础配置

```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"
      }
    }
  }
}
```

### 企业版配置

```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_token",
        "GITHUB_API_URL": "https://api.github.enterprise.com"
      }
    }
  }
}
```

## 🔑 认证设置

### 1. 创建Personal Access Token

1. 访问 GitHub Settings → Developer settings → Personal access tokens
2. 点击 "Generate new token (classic)"
3. 选择必要的权限范围：
   - `repo` - 完整仓库访问权限
   - `user` - 用户信息访问
   - `admin:org` - 组织管理（如需要）

### 2. 配置环境变量

```bash
# 方式1：系统环境变量
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxxxxxx"

# 方式2：.env文件
echo "GITHUB_PERSONAL_ACCESS_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx" >> .env
```

### 3. 权限范围说明

| 权限 | 功能 | 必需性 |
|------|------|--------|
| `repo` | 仓库完整访问 | 必需 |
| `user` | 用户信息读取 | 推荐 |
| `admin:org` | 组织管理 | 可选 |
| `workflow` | GitHub Actions | 可选 |

## 💡 使用示例

### 仓库操作

```
# 查看仓库信息
"请查看我的GitHub仓库列表"

# 创建新仓库
"请帮我创建一个名为'my-new-project'的公开仓库"

# Fork仓库
"请Fork modelcontextprotocol/servers仓库到我的账户"
```

### Issue管理

```
# 创建Issue
"请在my-project仓库中创建一个Issue，标题是'添加用户认证功能'"

# 查看Issue
"请查看my-project仓库中所有开放的Issues"

# 搜索Issue
"请搜索包含'bug'标签的所有Issues"
```

### 文件操作

```
# 读取文件
"请读取my-project仓库中的README.md文件内容"

# 创建文件
"请在my-project仓库的docs目录下创建一个API文档文件"

# 更新文件
"请更新my-project仓库的package.json文件，添加新的依赖"
```

### Pull Request

```
# 创建PR
"请创建一个PR，将feature-branch分支合并到main分支"

# 查看PR
"请查看my-project仓库中所有待审查的PR"

# 合并PR
"请合并PR #123"
```

## 🔍 高级功能

### 搜索功能

```
# 搜索仓库
"请搜索与'machine learning'相关的Python仓库"

# 搜索代码
"请在我的仓库中搜索包含'TODO'的代码"

# 搜索用户
"请搜索GitHub用户'octocat'"
```

### 分支管理

```
# 创建分支
"请在my-project仓库中创建一个名为'feature-auth'的新分支"

# 查看分支
"请列出my-project仓库的所有分支"

# 比较分支
"请比较main分支和develop分支的差异"
```

### 协作功能

```
# 添加协作者
"请将用户'collaborator'添加为my-project仓库的协作者"

# 管理标签
"请为Issue #123添加'bug'和'high-priority'标签"

# 里程碑管理
"请创建一个名为'v1.0.0'的里程碑"
```

## ⚠️ 安全注意事项

### Token安全

1. **不要在代码中硬编码Token**
2. **定期轮换Token**
3. **使用最小权限原则**
4. **监控Token使用情况**

### 权限管理

1. **仅授予必要权限**
2. **定期审查权限范围**
3. **使用组织级别的权限控制**
4. **启用双因素认证**

## 🔍 故障排除

### 常见问题

1. **认证失败**
   ```
   Error: Bad credentials
   ```
   - 检查Token是否正确
   - 验证Token权限范围
   - 确认Token未过期

2. **权限不足**
   ```
   Error: Not Found
   ```
   - 检查仓库访问权限
   - 验证Token权限范围
   - 确认仓库名称正确

3. **API限制**
   ```
   Error: API rate limit exceeded
   ```
   - 等待限制重置
   - 使用认证Token提高限制
   - 优化API调用频率

### 调试方法

```bash
# 测试Token有效性
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user

# 检查权限范围
curl -H "Authorization: token YOUR_TOKEN" -I https://api.github.com/user

# 查看API限制
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/rate_limit
```

## 📊 最佳实践

### 性能优化

1. **批量操作**：合并多个API调用
2. **缓存结果**：避免重复请求
3. **分页处理**：处理大量数据时使用分页
4. **条件请求**：使用ETag减少不必要的请求

### 工作流集成

1. **自动化PR创建**：基于分支命名规则
2. **Issue模板**：标准化Issue创建
3. **标签管理**：自动化标签分配
4. **里程碑跟踪**：自动更新进度

## 🔗 相关资源

- [GitHub API文档](https://docs.github.com/en/rest)
- [Personal Access Token指南](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token)
- [MCP GitHub服务器文档](https://github.com/modelcontextprotocol/servers/tree/main/src/github)
- [GitHub CLI](https://cli.github.com/)

## 📝 更新日志

- **v1.0.0**：基础仓库和Issue管理功能
- **v1.1.0**：添加PR管理和文件操作
- **v1.2.0**：增强搜索和分支管理功能
- **v1.3.0**：添加协作和权限管理功能
