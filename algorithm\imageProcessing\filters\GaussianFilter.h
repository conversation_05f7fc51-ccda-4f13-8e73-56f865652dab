#ifndef IMAGEPROCESSING_GAUSSIANFILTER_H
#define IMAGEPROCESSING_GAUSSIANFILTER_H

#include "../interfaces/IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QVector>
#include <QDebug>
#include <QtMath>

namespace ImageProcessing {

/**
 * @brief 高斯滤波器实现
 * 
 * 实现高斯滤波算法，提供自然外观的平滑模糊效果
 * 适用于噪声抑制和图像平滑处理
 */
class GaussianFilter : public IImageFilter {
public:
    /**
     * @brief 构造函数
     */
    GaussianFilter();

    /**
     * @brief 析构函数
     */
    ~GaussianFilter() override = default;

    // IImageFilter接口实现
    bool apply(ImageDataU32& data) override;
    bool apply(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const FilterParams& params) override;
    std::unique_ptr<FilterParams> getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t width, uint32_t height) const override;
    uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;
    bool supportsInPlace() const override;

    /**
     * @brief 设置预定义的滤波模式
     * @param preset 预设模式
     */
    void setPreset(const QString& preset);

    /**
     * @brief 获取支持的预设模式列表
     * @return 预设模式列表
     */
    static QStringList getSupportedPresets();

private:
    GaussianParams params_;    ///< 滤波参数

    /**
     * @brief 应用高斯滤波到单个像素
     * @param src 源图像
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 高斯滤波结果
     */
    float applyGaussianAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const;

    /**
     * @brief 获取安全的像素值（处理边界）
     * @param src 源图像
     * @param x X坐标
     * @param y Y坐标
     * @return 像素值
     */
    uint32_t getSafePixelValue(const ImageDataU32& src, int x, int y) const;

    /**
     * @brief 生成高斯核
     */
    void generateGaussianKernel();

    /**
     * @brief 计算高斯权重
     * @param x X偏移
     * @param y Y偏移
     * @param sigma 标准差
     * @return 高斯权重
     */
    float calculateGaussianWeight(int x, int y, float sigma) const;

    /**
     * @brief 验证高斯滤波参数
     * @param params 参数对象
     * @throws InvalidParameterException 如果参数无效
     */
    void validateGaussianParams(const GaussianParams& params) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;

    QVector<QVector<float>> m_kernel;    ///< 高斯核
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_GAUSSIANFILTER_H
