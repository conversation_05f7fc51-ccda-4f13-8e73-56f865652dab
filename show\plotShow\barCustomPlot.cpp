#include "barCustomPlot.h"
#include "statisticalFormula.h"

CBarCustomPlot::CBarCustomPlot(QCustomPlot &customPlot, StFigureInfo* st_figure_info_) :
    m_length(250)
{
    createFigure(customPlot, st_figure_info_);
    QObject::connect(&customPlot, SIGNAL(mouseDoubleClick(QMouseEvent*)), this, SLOT(mouseDouClick(QMouseEvent *)));
}

CBarCustomPlot::~CBarCustomPlot()
{
    delete wavePacketText;
    delete mainRectRight;
    delete subRectLeft;
    delete subRectRight;
    delete m_bar_;
}

void CBarCustomPlot::createFigure() {

}

void CBarCustomPlot::createFigure(QCustomPlot &customPlot, StFigureInfo* st_figure_info_) {
//    customPlot.plotLayout()->clear();   // 清空默认的轴矩形 可以清空轴的所有内容
//    customPlot.setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    //@1主视图的轴
    QCPLayoutGrid *mainGrid = new QCPLayoutGrid;

    QCPAxis *main_key_Axis = customPlot.xAxis; //(mainRectRight, QCPAxis::atLeft);
    QCPAxis *main_value_Axis = customPlot.yAxis;

    mainRectRight = new QCPAxisRect(&customPlot, true); //可缩放

    mainGrid->addElement(0, 0, mainRectRight); //layoutGrid 中添加 AxisRect

    //@3分配视图
    customPlot.plotLayout()->addElement(st_figure_info_->local.start, st_figure_info_->local.end, mainGrid);     // 在第一行添加轴矩形

    /*2.1 主图*/
    main_key_Axis = mainRectRight->axis(QCPAxis::atBottom);
    main_value_Axis = mainRectRight->axis(QCPAxis::atLeft);

    main_key_Axis->setTickLabelRotation(60);     // 轴刻度文字旋转60度
    main_key_Axis->setSubTicks(false);           // 不显示子刻度
    main_key_Axis->setTickLength(0, 4);          // 轴内外刻度的长度分别是0,4,也就是轴内的刻度线不显示
    main_key_Axis->setRange(st_figure_info_->key_range.start, st_figure_info_->key_range.end);               // 设置范围
    main_key_Axis->setLabel(st_figure_info_->key_label);
    main_key_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    main_value_Axis->setRange(st_figure_info_->value_range.start, st_figure_info_->value_range.end);
    main_value_Axis->setPadding(35);             // 轴的内边距，可以到QCustomPlot之开始（一）看图解
    main_value_Axis->setLabel(st_figure_info_->value_label);
    main_value_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    m_bar_ = new QCPBars(main_key_Axis, main_value_Axis); //

    m_bar_->setAntialiased(false); // 为了更好的边框效果，关闭抗齿锯
    m_bar_->setName(st_figure_info_->title); // 设置柱状图的名字，可在图例中显示
    m_bar_->setPen(QPen(QColor(0, 168, 140).lighter(130))); // 设置柱状图的边框颜色
    m_bar_->setBrush(QColor(0, 168, 140));  // 设置柱状图的画刷颜色

    customPlot.show();

    customPlots = &customPlot;
}

void CBarCustomPlot::mouseDouClick(QMouseEvent *event)
{
    Qt::MouseButton buttonType =  event->button();
    if(buttonType == Qt::MouseButton::LeftButton) {
        mainRectRight->axis(QCPAxis::atBottom)->setRange(0,m_length);
        mainRectRight->axis(QCPAxis::atLeft)->setRange(0,500);
    }
    else if(buttonType == Qt::MouseButton::RightButton) {
        subRectLeft->axis(QCPAxis::atBottom)->setRange(0,m_length);
        subRectLeft->axis(QCPAxis::atLeft)->setRange(0,100);
    }
    customPlots->replot(QCustomPlot::rpQueuedReplot);
}


#if 0
bool CBarCustomPlot::dataHandle(const QByteArray &fg_data_original, const QByteArray &cycle_data_original, CentroidDistData* centroid_dist)
{
#else
QSharedPointer<QCPBarsDataContainer> CBarCustomPlot::dataHandle(const QVector<uint16_t> &data)
{
#endif
    QString str;
    int maxIndex=0, length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;

    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

    length = data.length();
    //1.0
    barData.resize(length);
    for(int i=0; i<length; i++)
    {
//        centroid_dist->fgHandled[i].key = i;
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

    return handled_data;
}

QSharedPointer<QCPBarsDataContainer> CBarCustomPlot::dataHandle1(uint16_t start_index, const QVector<uint16_t> &data)
{
    QString str;
    int  length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;

    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

    length = data.length();

    barData.resize(length);
    for(int i=start_index; i<length; i++)
    {
//        uint key_tmp = round((float)1000/i*100);
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

    return handled_data;
}

/* @brief: 光斑显示；文本框；质心显示
 * @param_in:
 * @out:
 */
void CBarCustomPlot::customPlotShow(const QSharedPointer<QCPBarsDataContainer> &data_show)
{
    m_bar_->setData(data_show);//绘制fg曲线图

    customPlots->replot(QCustomPlot::rpQueuedRefresh); //更新图表
}

/**
 * @brief: 余晖模式下文本内容显示
 * @param: wavePointText 文本框
 * @param: centroid_arr 位置index 质心数据
 * @param: dist_arr 位置index 距离数据
 */
void CBarCustomPlot::wavePointTextShow(const uint8_t &index, QCPItemText **wavePointText, QPen *pen, QVector<double> centroid_arr, QVector<uint16_t> dist_arr, float position_offset, CentroidDistData* centroid_dist)
{
//    uint8_t centroid_arr_num = sizeof (centroid_arr)/sizeof(double);
//    uint8_t dist_arr_num = sizeof (dist_arr)/sizeof(uint16_t);
    uint8_t centroid_arr_num =centroid_arr.length();
    uint8_t dist_arr_num = dist_arr.length();
    if(centroid_arr_num != dist_arr_num)
    {
        //报错
        QMessageBox::warning(this,tr("质心数量:" + centroid_arr_num),tr("距离数量：" + dist_arr_num));
        return;
    }
    float centroid_sigma = CStatisticalFormula::StandardDeviation(centroid_arr, centroid_arr_num);
    float dist_sigma = CStatisticalFormula::StandardDeviation(dist_arr, dist_arr_num); //QString::number(centroid_sigma,'g', -1)
    float centr_sum = 0, centr_mean, dist_sum = 0, dist_mean;
    for(uint8_t i =0; i< centroid_arr_num; ++i)
    {
        centr_sum += centroid_arr[i];
        dist_sum += dist_arr[i];
    }
    centr_mean = centr_sum/centroid_arr_num;
    dist_mean = dist_sum/dist_arr_num;

    if(*wavePointText == NULL) {
        *wavePointText = new QCPItemText(customPlots);
        (*wavePointText)->setPositionAlignment(Qt::AlignTop|Qt::AlignLeft); //文字布局：顶、左对齐
        (*wavePointText)->position->setType(QCPItemPosition::ptAxisRectRatio); //位置类型（当前轴范围的比例为单位/实际坐标为单位）
        (*wavePointText)->position->setCoords(0.85, 0.02 + position_offset); //把文字框放在X轴的中间，Y轴的最顶部
        (*wavePointText)->setFont(QFont("仿宋",6,QFont::Bold)); //字体大小
        (*wavePointText)->setColor(pen->color()); //
        (*wavePointText)->setPen(*pen); //
        (*wavePointText)->setPadding(QMargins(2,2,2,2)); //文字距离边框几个像素
    }
//    (*wavePointText)->setText("centr_mean:" + QString::number(centr_mean,'f',2) + " centr_σ:" + QString::number(centroid_sigma,'f',2) +
//            "\ndist_max:" + QString::number(centroid_dist->dist_max[1],10) + " dist_min:" + QString::number(centroid_dist->dist_min[1],10) +
//            "\ndist_mean:" + QString::number(dist_mean,'f', 2) + " dist_σ:" + QString::number(dist_sigma,'f', 2));
}
