#include "../common/ImageData.h"
#include "../filters/WeightedAverageFilter.h"
#include <QCoreApplication>
#include <QDebug>

using namespace ImageProcessing;

// 函数声明
void manualCalculationVerification(const ImageDataU32 &testData);

/**
 * @brief 创建5x5测试数据
 */
ImageDataU32 create5x5TestData() {
    ImageDataU32 data(5, 5);

    QVector<QVector<uint32_t>> testMatrix = {
        {271, 882, 826, 748, 58}, {1011, 908, 792, 756, 738}, {1074, 924, 807, 800, 859}, {1021, 877, 777, 776, 855}, {145, 887, 788, 740, 33}};

    for (uint32_t y = 0; y < 5; ++y) {
        for (uint32_t x = 0; x < 5; ++x) {
            data.matrix()[y][x] = testMatrix[y][x];
        }
    }

    return data;
}

/**
 * @brief 打印图像数据
 */
void printImageData(const ImageDataU32 &data, const QString &title) {
    qDebug() << title << ":";
    for (uint32_t y = 0; y < data.height(); ++y) {
        QString row;
        for (uint32_t x = 0; x < data.width(); ++x) {
            row += QString("%1").arg(data.matrix()[y][x], 4);
            if (x < data.width() - 1)
                row += ", ";
        }
        qDebug() << "  " << row;
    }
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    qDebug() << "========================================";
    qDebug() << "测试center_weighted加权均值滤波器";
    qDebug() << "========================================";

    ImageDataU32 testData = create5x5TestData();
    printImageData(testData, "原始数据");

    // 测试不同的权重类型
    QStringList weightTypes = {"uniform", "gaussian", "center_weighted", "edge_enhance", "smooth"};

    for (const QString &weightType : weightTypes) {
        qDebug() << "\n=== 测试" << weightType << "权重 ===";

        WeightedAverageFilter filter;
        filter.setPredefinedWeights(weightType);

        ImageDataU32 result  = testData;
        bool         success = filter.apply(result);

        if (success) {
            printImageData(result, QString("%1滤波结果").arg(weightType));
            qDebug() << QString("中心点(2,2)：原始值=%1，滤波后=%2").arg(testData.matrix()[2][2]).arg(result.matrix()[2][2]);
        } else {
            qDebug() << "滤波失败";
        }
    }

    qDebug() << "========================================";
    qDebug() << "手动计算验证";
    qDebug() << "========================================";

    // 手动计算center_weighted权重的左上角(0,0)和中心点(2,2)
    manualCalculationVerification(testData);

    qDebug() << "\n========================================";
    qDebug() << "测试完成";
    qDebug() << "========================================";

    return 0;
}

/**
 * @brief 手动计算验证函数
 */
void manualCalculationVerification(const ImageDataU32 &testData) {
    qDebug() << "\n=== center_weighted权重矩阵 ===";
    qDebug() << "权重矩阵:";
    qDebug() << "  1.0  2.0  1.0";
    qDebug() << "  2.0  8.0  2.0";
    qDebug() << "  1.0  2.0  1.0";
    qDebug() << "权重总和: 20";
    qDebug() << "归一化权重矩阵:";
    qDebug() << "  0.05  0.10  0.05";
    qDebug() << "  0.10  0.40  0.10";
    qDebug() << "  0.05  0.10  0.05";

    // 手动计算左上角(0,0)
    qDebug() << "\n=== 手动计算左上角(0,0) ===";
    qDebug() << "3x3邻域（使用边缘复制）:";
    qDebug() << "  271  271  882";
    qDebug() << "  271  271  882";
    qDebug() << " 1011 1011  908";

    double leftTop_weighted_sum = 271 * 0.05 + 271 * 0.10 + 882 * 0.05 + 271 * 0.10 + 271 * 0.40 + 882 * 0.10 + 1011 * 0.05 + 1011 * 0.10 + 908 * 0.05;

    qDebug() << "加权均值计算:";
    qDebug() << "  271×0.05 + 271×0.10 + 882×0.05 +";
    qDebug() << "  271×0.10 + 271×0.40 + 882×0.10 +";
    qDebug() << " 1011×0.05 + 1011×0.10 + 908×0.05";
    qDebug() << QString("  = %1 + %2 + %3 + %4 + %5 + %6 + %7 + %8 + %9")
                    .arg(271 * 0.05)
                    .arg(271 * 0.10)
                    .arg(882 * 0.05)
                    .arg(271 * 0.10)
                    .arg(271 * 0.40)
                    .arg(882 * 0.10)
                    .arg(1011 * 0.05)
                    .arg(1011 * 0.10)
                    .arg(908 * 0.05);
    qDebug() << QString("  = %1").arg(leftTop_weighted_sum);

    // 滤波强度处理
    double leftTop_original = 271.0;
    double leftTop_filtered = leftTop_original + 1.0 * (leftTop_weighted_sum - leftTop_original);
    qDebug() << QString("滤波强度处理: %1 + 1.0 × (%2 - %1) = %3").arg(leftTop_original).arg(leftTop_weighted_sum).arg(leftTop_filtered);
    qDebug() << QString("手动计算结果: %1").arg(int(leftTop_filtered));

    // 手动计算中心点(2,2)
    qDebug() << "\n=== 手动计算中心点(2,2) ===";
    qDebug() << "3x3邻域:";
    qDebug() << "  908  792  756";
    qDebug() << "  924  807  800";
    qDebug() << "  877  777  776";

    double center_weighted_sum = 908 * 0.05 + 792 * 0.10 + 756 * 0.05 + 924 * 0.10 + 807 * 0.40 + 800 * 0.10 + 877 * 0.05 + 777 * 0.10 + 776 * 0.05;

    qDebug() << "加权均值计算:";
    qDebug() << "  908×0.05 + 792×0.10 + 756×0.05 +";
    qDebug() << "  924×0.10 + 807×0.40 + 800×0.10 +";
    qDebug() << "  877×0.05 + 777×0.10 + 776×0.05";
    qDebug() << QString("  = %1 + %2 + %3 + %4 + %5 + %6 + %7 + %8 + %9")
                    .arg(908 * 0.05)
                    .arg(792 * 0.10)
                    .arg(756 * 0.05)
                    .arg(924 * 0.10)
                    .arg(807 * 0.40)
                    .arg(800 * 0.10)
                    .arg(877 * 0.05)
                    .arg(777 * 0.10)
                    .arg(776 * 0.05);
    qDebug() << QString("  = %1").arg(center_weighted_sum);

    // 滤波强度处理
    double center_original = 807.0;
    double center_filtered = center_original + 1.0 * (center_weighted_sum - center_original);
    qDebug() << QString("滤波强度处理: %1 + 1.0 × (%2 - %1) = %3").arg(center_original).arg(center_weighted_sum).arg(center_filtered);
    qDebug() << QString("手动计算结果: %1").arg(int(center_filtered));

    // 与测试程序结果对比
    qDebug() << "\n=== 与测试程序结果对比 ===";
    qDebug() << "注意：测试程序结果会因边界处理方式改变而更新";
    qDebug() << QString("左上角(0,0): 手动计算=%1").arg(int(leftTop_filtered));
    qDebug() << QString("中心点(2,2): 手动计算=%1").arg(int(center_filtered));

    qDebug() << "\n=== 边界处理方式改进说明 ===";
    qDebug() << "修复前：零填充 - 边界外像素值为0，导致边缘偏小";
    qDebug() << "修复后：边缘复制 - 使用最近边缘像素值，避免人为拉低";
    qDebug() << "预期效果：边缘像素值应该比零填充时更高，更符合实际光斑特性";
}
