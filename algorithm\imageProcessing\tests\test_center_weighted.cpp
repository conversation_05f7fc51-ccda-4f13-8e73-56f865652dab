#include <QCoreApplication>
#include <QDebug>
#include "../filters/WeightedAverageFilter.h"
#include "../common/ImageData.h"

using namespace ImageProcessing;

/**
 * @brief 创建5x5测试数据
 */
ImageDataU32 create5x5TestData() {
    ImageDataU32 data(5, 5);
    
    QVector<QVector<uint32_t>> testMatrix = {
        {271, 882, 826, 748, 58},
        {1011, 908, 792, 756, 738},
        {1074, 924, 807, 800, 859},
        {1021, 877, 777, 776, 855},
        {145, 887, 788, 740, 33}
    };
    
    for (uint32_t y = 0; y < 5; ++y) {
        for (uint32_t x = 0; x < 5; ++x) {
            data.matrix()[y][x] = testMatrix[y][x];
        }
    }
    
    return data;
}

/**
 * @brief 打印图像数据
 */
void printImageData(const ImageDataU32& data, const QString& title) {
    qDebug() << title << ":";
    for (uint32_t y = 0; y < data.height(); ++y) {
        QString row;
        for (uint32_t x = 0; x < data.width(); ++x) {
            row += QString("%1").arg(data.matrix()[y][x], 4);
            if (x < data.width() - 1) row += ", ";
        }
        qDebug() << "  " << row;
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "========================================";
    qDebug() << "测试center_weighted加权均值滤波器";
    qDebug() << "========================================";
    
    ImageDataU32 testData = create5x5TestData();
    printImageData(testData, "原始数据");
    
    // 测试不同的权重类型
    QStringList weightTypes = {"uniform", "gaussian", "center_weighted", "edge_enhance", "smooth"};
    
    for (const QString& weightType : weightTypes) {
        qDebug() << "\n=== 测试" << weightType << "权重 ===";
        
        WeightedAverageFilter filter;
        filter.setPredefinedWeights(weightType);
        
        ImageDataU32 result = testData;
        bool success = filter.apply(result);
        
        if (success) {
            printImageData(result, QString("%1滤波结果").arg(weightType));
            qDebug() << QString("中心点(2,2)：原始值=%1，滤波后=%2")
                        .arg(testData.matrix()[2][2])
                        .arg(result.matrix()[2][2]);
        } else {
            qDebug() << "滤波失败";
        }
    }
    
    qDebug() << "\n========================================";
    qDebug() << "测试完成";
    qDebug() << "========================================";
    
    return 0;
}
