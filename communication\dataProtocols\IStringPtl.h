#ifndef _ISTRING_PTL_H_
#define _ISTRING_PTL_H_

#include <QByteArray>
#include <QVector>

class IStringPtl
{
public:
  IStringPtl(){};
  virtual ~IStringPtl(){};

public:
  virtual void headerWrite(const QString &header) = 0;
  virtual QByteArray getControlCmd(const QString &cmd_id) = 0; //控制指令
  virtual QByteArray getWriteCmd(const QString &cmd_id, const QString &write_data) = 0; //写入单字数据
  virtual QByteArray getReadCmd(const QString &cmd_id) = 0; //读指令

};


#endif
