#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工作流可视化模块

单一职责：专门处理工作流相关的数据提取和可视化逻辑
"""

import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import DataAdapter, VisualizationData, VisualizationMode, Node, Edge
from core.component_utils import component_manager

class WorkflowDataExtractor(DataAdapter):
    """工作流数据提取器 - 单一职责：工作流可视化数据提取"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
    
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """提取工作流可视化数据"""
        self.project_path = Path(project_path)
        return self._extract_workflow_data()
    
    def _extract_workflow_data(self) -> VisualizationData:
        """提取工作流数据"""
        nodes = []
        edges = []
        
        # 从项目结构推断工作流组件
        inferred = self._infer_workflow_from_structure()
        nodes.extend([Node(**node_data) for node_data in inferred['nodes']])
        edges.extend([Edge(**edge_data) for edge_data in inferred['edges']])
        
        # 添加基础工作流组件
        basic_nodes = [
            {"id": "REQ001", "name": "需求导入", "type": "process", "component": "REQ", "properties": {"x": 100, "y": 100}},
            {"id": "DES001", "name": "方案设计", "type": "process", "component": "DES", "properties": {"x": 300, "y": 100}},
            {"id": "DEV001", "name": "开发实施", "type": "process", "component": "DEV", "properties": {"x": 500, "y": 100}},
            {"id": "QA001", "name": "测试验证", "type": "process", "component": "QA", "properties": {"x": 700, "y": 100}},
            {"id": "PROD001", "name": "生产交付", "type": "process", "component": "PROD", "properties": {"x": 900, "y": 100}}
        ]
        
        basic_edges = [
            {"source": "REQ001", "target": "DES001", "type": "流转", "properties": {}},
            {"source": "DES001", "target": "DEV001", "type": "流转", "properties": {}},
            {"source": "DEV001", "target": "QA001", "type": "流转", "properties": {}},
            {"source": "QA001", "target": "PROD001", "type": "流转", "properties": {}}
        ]
        
        # 避免重复添加相同ID的节点
        existing_ids = {node.id for node in nodes}
        for node_data in basic_nodes:
            if node_data["id"] not in existing_ids:
                nodes.append(Node(**node_data))
        
        edges.extend([Edge(**edge_data) for edge_data in basic_edges])
        
        return VisualizationData(
            title="工作流可视化",
            mode=VisualizationMode.WORKFLOW,
            nodes=nodes,
            edges=edges,
            metadata={
                "component_count": len(nodes),
                "connection_count": len(edges),
                "workflow_files_found": len(list(self.project_path.glob("**/workflow.json"))),
                "last_updated": datetime.now().isoformat()
            }
        )
    
    def _infer_workflow_from_structure(self) -> Dict[str, List[Dict[str, Any]]]:
        """从项目结构推断工作流组件"""
        nodes = []
        edges = []
        
        # 扫描项目目录结构，识别工作流组件
        for component_id, component_info in component_manager.components.items():
            component_path = self.project_path / f"0{component_info.order + 1}_{component_info.name.lower()}"
            
            if component_path.exists():
                node_data = {
                    "id": f"{component_id}001",
                    "name": f"{component_info.name}阶段", 
                    "type": "process",
                    "component": component_id,
                    "properties": {
                        "path": str(component_path),
                        "file_count": len(list(component_path.glob("**/*")))
                    }
                }
                nodes.append(node_data)
        
        # 构建组件间的流转关系
        ordered_components = component_manager.get_ordered_components()
        for i in range(len(ordered_components) - 1):
            source_comp = ordered_components[i]
            target_comp = ordered_components[i + 1]
            
            # 检查是否存在对应的节点
            source_node = next((n for n in nodes if n["component"] == source_comp), None)
            target_node = next((n for n in nodes if n["component"] == target_comp), None)
            
            if source_node and target_node:
                edge_data = {
                    "source": source_node["id"],
                    "target": target_node["id"],
                    "type": "流转",
                    "properties": {"weight": 1}
                }
                edges.append(edge_data)
        
        return {"nodes": nodes, "edges": edges}
    
    def get_workflow_templates(self) -> List[Dict[str, Any]]:
        """获取工作流模板"""
        return [
            {
                "name": "标准开发流程",
                "stages": ["需求", "设计", "开发", "测试", "部署"],
                "transitions": ["需求确认", "方案评审", "开发完成", "测试通过"]
            },
            {
                "name": "敏捷开发流程", 
                "stages": ["需求梳理", "迭代设计", "开发实现", "持续集成"],
                "transitions": ["需求细化", "设计确认", "功能完成"]
            }
        ]
    
    def analyze_workflow_metrics(self) -> Dict[str, Any]:
        """分析工作流指标"""
        workflow_files = list(self.project_path.glob("**/workflow.json"))
        process_files = list(self.project_path.glob("**/process.md"))
        
        return {
            "workflow_definitions": len(workflow_files),
            "process_documents": len(process_files),
            "automation_level": "中等" if workflow_files else "低",
            "coverage_analysis": self._analyze_process_coverage()
        }
    
    def _analyze_process_coverage(self) -> Dict[str, str]:
        """分析流程覆盖度"""
        coverage = {}
        for comp_id, comp_info in component_manager.components.items():
            comp_dir = self.project_path / f"0{comp_info.order + 1}_{comp_info.name.lower()}"
            if comp_dir.exists():
                coverage[comp_id] = "已覆盖"
            else:
                coverage[comp_id] = "未覆盖"
        return coverage 