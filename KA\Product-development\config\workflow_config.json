{"workflow": {"name": "产品开发工作流", "description": "完整的产品开发流程，支持事件驱动和自动化执行", "version": "1.0.0", "project_type": "single_layer", "created_date": "2025-07-09T15:06:19.742491", "components": [{"name": "需求导入", "type": "input", "script": "/requirements/import_requirements.py", "config": "requirements_import_config.json", "description": "从外部系统导入需求文档"}, {"name": "需求分析", "type": "process", "script": "/requirements/analyze_requirements.py", "config": "requirements_analysis_config.json", "description": "分析和分解需求"}, {"name": "方案设计", "type": "process", "script": "/design/generate_design.py", "config": "design_config.json", "description": "生成系统架构和技术方案"}, {"name": "开发实施", "type": "process", "script": "/development/manage_development.py", "config": "development_config.json", "description": "硬件、固件、软件开发"}, {"name": "测试验证", "type": "process", "script": "/quality/run_verification.py", "config": "quality_config.json", "description": "质量测试和验证"}, {"name": "生产准备", "type": "process", "script": "/production/prepare_production.py", "config": "production_config.json", "description": "生产BOM和流程准备"}, {"name": "项目输出", "type": "output", "script": "/deliverables/generate_deliverables.py", "config": "deliverables_config.json", "description": "生成最终交付物"}], "connections": [{"from": "需求导入", "to": "需求分析", "triggers": [{"name": "需求文档导入完成", "type": "event", "handlers": [{"type": "mcp_server", "name": "Context7 MCP", "script": "/mcp-server_local_integrations/analyze_requirements.py", "params": {"input": "${event.output_path}"}}]}]}, {"from": "需求分析", "to": "方案设计", "triggers": [{"name": "需求矩阵更新", "type": "event", "handlers": [{"type": "mcp_server", "name": "UML-MCP Server", "script": "/mcp-server_local_integrations/generate_architecture_diagrams.py", "params": {"requirements_matrix": "${event.file_path}"}}]}]}], "mcp_servers": [{"name": "Firecrawl MCP Server", "script": "/mcp-server_local_integrations/firecrawl.py", "purpose": "网页需求抓取", "enabled": true}, {"name": "IM Notifier MCP Server", "script": "/mcp-server_local_integrations/im_notifier.py", "purpose": "通知服务", "enabled": true}, {"name": "UML-MCP Server", "script": "/mcp-server_local_integrations/uml_mcp.py", "purpose": "UML图生成", "enabled": true}]}}