#ifndef TABBARFONTDIR_H
#define TABBARFONTDIR_H

#include <QWidget>
#include <QProxyStyle>
#include <QStyleOptionTab>
#include <QPainter>

namespace Ui {
class tabBarFontDir;
}

class tabBarFontDir : public QWidget
{
    Q_OBJECT

public:
    explicit tabBarFontDir(QWidget *parent = nullptr){};
    ~tabBarFontDir(){};

private:
    Ui::tabBarFontDir *ui;
};

//自定义tabbar的样式
class CustomTabStyle:public QProxyStyle{
public:
    CustomTabStyle(){};
    QSize sizeFromContents(ContentsType type, const QStyleOption *option, const QSize &size, const QWidget *widget) const;
    void drawControl(ControlElement element, const QStyleOption *option, QPainter *painter, const QWidget *widget) const;
};
#endif // TABBARFONTDIR_H
