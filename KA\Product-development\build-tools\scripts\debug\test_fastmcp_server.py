#!/usr/bin/env python3
"""
FastMCP 服务器测试脚本
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path

async def test_fastmcp_server():
    """测试FastMCP服务器"""
    print("=== 测试 FastMCP 服务器 ===")
    
    server_script = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_fastmcp.py"
    
    if not server_script.exists():
        print(f"❌ 服务器脚本不存在: {server_script}")
        return False
    
    print(f"✓ 服务器脚本存在: {server_script}")
    
    try:
        # 使用官方SDK的客户端测试
        from mcp.client.session import ClientSession
        from mcp.client.stdio import StdioServerParameters, stdio_client
        
        # 设置服务器参数
        server_params = StdioServerParameters(
            command=sys.executable,
            args=[str(server_script)]
        )
        
        print("正在连接到服务器...")
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化连接
                print("初始化连接...")
                await session.initialize()
                print("✓ 连接初始化成功")
                
                # 列出工具
                print("\n--- 测试工具列表 ---")
                tools = await session.list_tools()
                print(f"可用工具数量: {len(tools.tools) if tools.tools else 0}")
                
                if tools.tools:
                    for tool in tools.tools:
                        print(f"  - {tool.name}: {tool.description}")
                else:
                    print("❌ 没有发现工具")
                
                # 列出资源
                print("\n--- 测试资源列表 ---") 
                resources = await session.list_resources()
                print(f"可用资源数量: {len(resources.resources) if resources.resources else 0}")
                
                if resources.resources:
                    for resource in resources.resources:
                        print(f"  - {resource.uri}: {resource.name}")
                else:
                    print("❌ 没有发现资源")
                
                print("\n✅ FastMCP 服务器测试完成")
                return True
                
    except ImportError as e:
        print(f"❌ 无法导入MCP客户端模块: {e}")
        print("请确保已安装官方MCP SDK: pip install mcp")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_server_startup():
    """简单测试服务器启动"""
    print("\n=== 简单启动测试 ===")
    
    server_script = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_fastmcp.py"
    
    try:
        # 测试服务器是否可以导入
        cmd = [sys.executable, "-c", f"import sys; sys.path.append('{server_script.parent}'); print('✓ 路径设置成功')"]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', timeout=10)
        
        if result.returncode == 0:
            print("✓ 基础环境正常")
            print(result.stdout)
            return True
        else:
            print(f"❌ 环境测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 环境测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("FastMCP 服务器测试")
    print("==================")
    
    # 先测试简单启动
    test_server_startup()
    
    # 然后测试完整功能
    try:
        asyncio.run(test_fastmcp_server())
    except Exception as e:
        print(f"异步测试失败: {e}") 