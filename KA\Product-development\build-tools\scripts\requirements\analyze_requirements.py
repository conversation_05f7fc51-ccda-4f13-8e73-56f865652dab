#!/usr/bin/env python3
"""
需求分析脚本 - 实现T03/T04/T05功能
- T03: 技术需求整理 - 分析需求文档内容，生成需求统计、分类、优先级评估、依赖关系分析报告
- T04: 需求分解 - 分析需求文档内容，生成需求统计、分类、优先级评估、依赖关系分析报告  
- T05: 需求矩阵维护 - 创建需求追溯矩阵，建立需求间关联关系，生成矩阵文件和可视化图表
"""

import json
import argparse
import sys
import os
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd
from datetime import datetime

def analyze_requirements_content(input_path: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
    """
    分析需求文档内容
    
    Args:
        input_path: 需求文档路径
        analysis_type: 分析类型 (comprehensive, basic, matrix)
    
    Returns:
        分析结果字典
    """
    try:
        # 读取需求文档
        if not os.path.exists(input_path):
            return {
                "success": False,
                "error": f"需求文档不存在: {input_path}",
                "analysis_type": analysis_type
            }
        
        # 根据文件类型读取内容
        if input_path.endswith('.json'):
            with open(input_path, 'r', encoding='utf-8') as f:
                requirements_data = json.load(f)
        elif input_path.endswith('.xlsx'):
            df = pd.read_excel(input_path)
            requirements_data = df.to_dict('records')
        else:
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 简单解析文本内容
                requirements_data = [{"id": i+1, "content": line.strip(), "type": "text"} 
                                   for i, line in enumerate(content.split('\n')) if line.strip()]
        
        # 执行分析
        if analysis_type == "comprehensive":
            return comprehensive_analysis(requirements_data, input_path)
        elif analysis_type == "matrix":
            return create_requirements_matrix(requirements_data, input_path)
        else:
            return basic_analysis(requirements_data, input_path)
            
    except Exception as e:
        return {
            "success": False,
            "error": f"分析需求时出错: {str(e)}",
            "analysis_type": analysis_type
        }

def comprehensive_analysis(requirements_data: List[Dict], input_path: str) -> Dict[str, Any]:
    """执行综合需求分析"""
    total_requirements = len(requirements_data)
    
    # 需求分类统计
    categories = {}
    priorities = {"高": 0, "中": 0, "低": 0, "未定义": 0}
    
    for req in requirements_data:
        # 分类统计
        req_type = req.get('type', '未分类')
        categories[req_type] = categories.get(req_type, 0) + 1
        
        # 优先级统计
        priority = req.get('priority', '未定义')
        if priority in priorities:
            priorities[priority] += 1
        else:
            priorities['未定义'] += 1
    
    # 依赖关系分析
    dependencies = []
    for req in requirements_data:
        if 'dependencies' in req and req['dependencies']:
            dependencies.extend(req['dependencies'])
    
    return {
        "success": True,
        "analysis_type": "comprehensive",
        "input_file": input_path,
        "timestamp": datetime.now().isoformat(),
        "statistics": {
            "total_requirements": total_requirements,
            "categories": categories,
            "priorities": priorities,
            "dependencies_count": len(dependencies)
        },
        "classification": {
            "functional_requirements": categories.get('functional', 0),
            "non_functional_requirements": categories.get('non_functional', 0),
            "technical_requirements": categories.get('technical', 0),
            "business_requirements": categories.get('business', 0)
        },
        "priority_assessment": priorities,
        "dependency_analysis": {
            "total_dependencies": len(dependencies),
            "unique_dependencies": len(set(dependencies)),
            "dependency_list": list(set(dependencies))
        },
        "recommendations": [
            f"共识别 {total_requirements} 个需求",
            f"需求分类覆盖 {len(categories)} 个类别",
            f"高优先级需求 {priorities['高']} 个，需重点关注",
            f"发现 {len(set(dependencies))} 个依赖关系，需要协调管理"
        ]
    }

def create_requirements_matrix(requirements_data: List[Dict], input_path: str) -> Dict[str, Any]:
    """创建需求追溯矩阵"""
    matrix_data = []
    
    for req in requirements_data:
        req_id = req.get('id', 'REQ_' + str(len(matrix_data) + 1))
        matrix_row = {
            "requirement_id": req_id,
            "title": req.get('title', req.get('content', '')[:50]),
            "type": req.get('type', 'undefined'),
            "priority": req.get('priority', 'medium'),
            "status": req.get('status', 'draft'),
            "dependencies": req.get('dependencies', []),
            "stakeholders": req.get('stakeholders', []),
            "test_cases": req.get('test_cases', [])
        }
        matrix_data.append(matrix_row)
    
    # 生成矩阵文件
    output_dir = Path(input_path).parent / "requirements_matrix"
    output_dir.mkdir(exist_ok=True)
    
    matrix_file = output_dir / f"requirements_matrix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(matrix_file, 'w', encoding='utf-8') as f:
        json.dump(matrix_data, f, ensure_ascii=False, indent=2)
    
    # 创建Excel格式矩阵
    df = pd.DataFrame(matrix_data)
    excel_file = output_dir / f"requirements_matrix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(excel_file, index=False)
    
    return {
        "success": True,
        "analysis_type": "matrix",
        "input_file": input_path,
        "timestamp": datetime.now().isoformat(),
        "matrix_files": {
            "json_file": str(matrix_file),
            "excel_file": str(excel_file)
        },
        "matrix_statistics": {
            "total_requirements": len(matrix_data),
            "requirements_with_dependencies": len([r for r in matrix_data if r['dependencies']]),
            "requirements_with_tests": len([r for r in matrix_data if r['test_cases']]),
            "status_distribution": {
                status: len([r for r in matrix_data if r['status'] == status])
                for status in set(r['status'] for r in matrix_data)
            }
        },
        "traceability_links": {
            "requirement_to_requirement": sum(len(r['dependencies']) for r in matrix_data),
            "requirement_to_test": sum(len(r['test_cases']) for r in matrix_data),
            "stakeholder_involvement": sum(len(r['stakeholders']) for r in matrix_data)
        }
    }

def basic_analysis(requirements_data: List[Dict], input_path: str) -> Dict[str, Any]:
    """执行基础需求分析"""
    return {
        "success": True,
        "analysis_type": "basic",
        "input_file": input_path,
        "timestamp": datetime.now().isoformat(),
        "basic_statistics": {
            "total_requirements": len(requirements_data),
            "file_size": os.path.getsize(input_path),
            "file_type": Path(input_path).suffix
        }
    }

def main():
    parser = argparse.ArgumentParser(description='需求分析工具')
    parser.add_argument('--input-path', '-i', required=True, help='需求文档路径')
    parser.add_argument('--analysis-type', '-t', default='comprehensive', 
                       choices=['comprehensive', 'basic', 'matrix'],
                       help='分析类型')
    parser.add_argument('--output-format', '-f', default='json',
                       choices=['json', 'yaml'],
                       help='输出格式')
    
    args = parser.parse_args()
    
    # 执行分析
    result = analyze_requirements_content(args.input_path, args.analysis_type)
    
    # 输出结果
    if args.output_format == 'json':
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        import yaml
        print(yaml.dump(result, allow_unicode=True, default_flow_style=False))
    
    # 返回退出码
    sys.exit(0 if result.get('success', False) else 1)

if __name__ == "__main__":
    main()
