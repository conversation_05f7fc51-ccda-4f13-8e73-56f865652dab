#!/usr/bin/env python3
"""
设计配置生成器
负责生成项目的设计管理相关配置文件
"""

import os
import json
from pathlib import Path
import argparse


def generate_design_config(project_path=".", project_type="single_layer"):
    """生成设计配置文件"""
    
    # 创建配置目录
    config_dir = Path(project_path) / 'config'
    config_dir.mkdir(exist_ok=True)
    
    # 设计配置
    design_config = {
        "design_settings": {
            "auto_diagram_generation": True,
            "architecture_templates": [
                "layered",
                "microservices",
                "embedded"
            ],
            "review_required": True,
            "version_control": True
        },
        "outputs": {
            "architecture_doc": "design/architecture.md",
            "technical_spec": "design/technical_specification.md",
            "completion_event": "design_completed.json"
        }
    }
    
    # 写入配置文件
    config_file = config_dir / 'design_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(design_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 设计配置文件已生成: {config_file}")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成设计配置文件')
    parser.add_argument('--project-path', default='.', help='项目路径')
    
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'], help='项目类型')
    
    args = parser.parse_args()
    
    success = generate_design_config(args.project_path, args.project_type)
    
    if success:
        print("[+] 设计配置生成完成")
    else:
        print("[X] 设计配置生成失败")
        exit(1)


if __name__ == "__main__":
    main() 