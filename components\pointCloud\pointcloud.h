#ifndef POINTCLOUD_H
#define POINTCLOUD_H

//#include <QMainWindow>
#include <qcustomplot.h>

#define ANGLE_TO_RADIUS(a) a*M_PI/180.0
#define zoomCnt     10


class StPointCloud3D{
public:
    StPointCloud3D(double a, double d, uint i, double h, uint32_t c)
    {
        m_angle = a;
        m_distance = d;
        m_intensity = i;
        m_height = h;
        m_centroid = c;
    };
    double   m_angle;
    double   m_distance;
    uint     m_intensity;
    double   m_height;
    uint32_t m_centroid;
    //uint16_t m_mcuVoltage;
    //uint8_t  m_healthCode;
};

typedef struct{
    uint16_t m_version;
    uint8_t m_hardAndSoftwareVersion;
    float m_speed;
    uint8_t m_healthCode;
    uint16_t m_mcuVoltage;
    uint8_t m_proj;
    std::vector<StPointCloud3D> pointCloud2d;
}SCAN_POINTCLOUD;

class baudAndMode{
public:
  uint buad;
  uint mode;

};

typedef struct{
    bool    isDebug;
    float   angleRangeBegin;
    float   angleRangeEnd;
    uint    distance;
}ST_CONSOLE;

//namespace Ui {
//class pointCloud;
//}

//class pointCloud : public QMainWindow
//{
//    Q_OBJECT

//public:
//    explicit pointCloud(QWidget *parent = nullptr);
//    ~pointCloud();
//};

#endif // POINTCLOUD_H
