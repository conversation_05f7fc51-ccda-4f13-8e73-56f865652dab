#ifndef _LENS_READ_INI_H_
#define _LENS_READ_INI_H_

#include <QSettings>


#define DATACONFIG ClensReadIni::getInstance()->getIniConfig()

class ClensReadIni {
  public:
    static ClensReadIni *getInstance();

    typedef struct {
        QString  version;      //固件账号
        QString  userid;       //账号
        QString  op;           //工序号
        QString  work_number;  //工单号
        QString  work_domain;
        uint     sensor_device_buad;
        uint32_t solid_time;           //固化时间
        uint8_t  sensor_device;        //模组
        uint8_t  station_number;       //工位号
        uint8_t  clens_machine_brand;  //管路设备
        uint8_t  facula_center_loc_x;
        uint8_t  facula_center_loc_y;
        uint8_t  facula_ok_times;     //
        uint8_t  facula_ng_handle;    //光斑异常处理方式，0-手动处理；1-继续执行
        uint8_t  facula_handle_type;  // 0-原光斑调节; 1-启用处理后光斑调节

        // // 新增光斑处理配置
        // uint8_t interpolation_type;       // 插值类型
        // QString filter_types;             // 滤波器类型列表
        // float   interpolation_offset;     // 插值偏移量
        // float   kalman_strength;          // 卡尔曼滤波强度
        // uint8_t convolution_kernel_size;  // 卷积核大小
    } IniConfig;

    void             readIni();
    const IniConfig &getIniConfig();

  private:
    ClensReadIni();


    IniConfig iniConfig;


    static ClensReadIni *instance;
};

#endif
