# Canvas分组创建原则

## 🎯 核心原则

遵循**单一职责原则**、**单一来源原则**、**奥卡姆剃刀原则**，实现简洁高效的Canvas分组策略。

## 📊 分组层级结构

### 两层分组架构

```text
组件级组 (Component Level)
├── 项目级组 (Project Level)
│   ├── 文档节点 1
│   ├── 文档节点 2
│   └── ...
└── 项目级组 (Project Level)
    ├── 文档节点 3
    └── ...
```

**设计理念**：

- **第一层（组件级）**：按产品开发流程分组，显示组件代号（REQ、DES、DEV、DEL、PM、PROD等）, 见 shared_config.py.
- **第二层（子目录级）**：如果只有两层，第二层与目录层级有关
  - 如果目录只有两层，则按子目录缩写分组，显示子目录缩写（CUST、MAIN、FW、SW等）；
  - 如果目录有多层，则需要动态提取其中能做区分的目录作为第二层

## 🔍 核心问题：如何识别哪些文档属于同一个子目录组？

### 子目录分组算法

**算法核心**：基于文档路径的子目录提取和缩写映射

#### 1. 一层目录

```python
# 一层目录，直接显示在组件级组下
requirements/ → "REQ"
design/ → "DES"
development/ → "DEV"
deliverables/ → "DEL"
project_management/ → "PM"
product/ -> "PROD"
```

#### 2. 两层目录组件处理（子目录缩写）

```python
# 需求文档：按子目录缩写分组
requirements/custom_requirements/ → "CUST"
requirements/main_requirements/ → "MAIN"
requirements/market_requirements/ → "MARK"

# 设计文档：按子目录缩写分组
design/interface_specifications/ → "INTF"
design/principle_Information/ → "PRIN"

# 交付物：按子目录缩写分组
deliverables/documents/ → "DOC"
deliverables/firmware/ → "FW"

# 项目管理：按子目录缩写分组
project_management/meeting_notes/ → "MEET"
project_management/resources/ → "RES"
```

#### 3. 多层目录组件处理 

例如：开发项目识别（项目类型缩写）

```python
# 路径模式：development/{项目类型}_projects/{项目ID}/docs/
development/firmware/fw_project_1/docs/ → "FW-fw_project_1"
development/software/app_project/docs/ → "SW-app_project"
```

**子目录分组规则**：

1. **路径层级分析**：提取文档路径的第二层目录
2. **缩写映射查找**：在shared_config.py中查找对应缩写
3. **组标识符生成**：`{缩写}` 或 `{项目类型缩写}-{项目标识符}`

### 分组记录



## 📐 布局策略

> **注意**: 详细的布局策略已迁移到专门的文档 [`canvas_layout_strategy.md`](./canvas_layout_strategy.md)，本节仅保留核心原则。

### 核心布局原则

- **层次清晰**: 组件级组和项目级组在视觉上明确区分
- **空间高效**: 合理利用Canvas空间，避免重叠和浪费
- **扩展性**: 支持动态添加文档和组，布局自动调整
- **一致性**: 相同类型的元素使用统一的样式和间距

## 🎨 视觉设计原则

### 层级区分

- **组件级组**：较大尺寸，明显边框，组件代码标签（REQ、DES、DEV等）
- **项目级组**：嵌套在内部，中等尺寸，项目名称标签

### 间距控制

- **组件组间距**：500px（水平）
- **项目组间距**：80px（垂直）
- **组内边距**：20px
- **节点间距**：20px

### 颜色编码

- **REQ组件**：#4ECDC4（青色）
- **DES组件**：#45B7D1（蓝色）
- **DEV组件**：#96CEB4（绿色）
- **DEL组件**：#FFEAA7（黄色）
- **PM组件**：#DDA0DD（紫色）

## 🔧 实现要点

### 关键技术决策

1. **避免组重复**：层级分组模式下，只创建组件级和项目级组
2. **智能项目识别**：特殊目录统一归类，开发项目按路径分离
3. **动态尺寸计算**：根据内容数量自动调整组尺寸
4. **多列布局**：支持组内文档多列排列，提高空间利用率

### 配置参数

```python
node_width = 400px          # 文档节点宽度
node_height = 300px         # 文档节点高度
max_nodes_per_column = 10   # 每列最大节点数
group_margin = 80px         # 组间距
nested_margin = 20px        # 嵌套组边距
```

## ✅ 实际效果验证

### 测试结果（test_single_layer项目）

- **文档节点**：11个
- **目录组**：13个（4个组件级 + 9个子目录级）
- **层级结构**：

  ```text
  REQ (2000px) → MAK [1个文档]
               → CUST [2个文档]
               → MAIN [1个文档]
  DEV (1040px) → FW-fw_project_1 [2个文档]
               → SW-app_project [1个文档]
  DEL (400px) → DELI [1个文档]
  PM (400px) → PROJ [1个文档]
  ```

### 解决的关键问题

1. **✅ 组分类正确**：第一层显示组件代号（REQ、DEV、DEL、PM），第二层显示子目录缩写
2. **✅ 缩写映射**：使用shared_config.py中定义的子目录缩写（CUST、MAIN、FW、SW等）
3. **✅ 层级清晰**：两层嵌套结构，组件级→子目录级，视觉层次分明
4. **✅ 文档定位**：文档正确放置在对应子目录组内部

### 文档类型分组（第三层，暂无）

在项目内部，可按文档类型进一步分组：

| 文档类型 | 分组名称 | 包含文档 |
|---------|----------|----------|
| 设计文档 | Design Docs | 架构设计、接口设计、数据库设计 |
| API文档 | API Docs | 接口文档、API参考、SDK文档 |
| 用户文档 | User Docs | 用户手册、操作指南、FAQ |
| 技术文档 | Tech Docs | 技术规范、实现细节、配置说明 |

## 🔧 实现算法

### 组ID生成算法

```python
def generate_group_id(component: str, project_type: str = None, project_name: str = None) -> str:
    """
    生成组的唯一ID

    Args:
        component: 组件代码 (REQ, DES, DEV等)
        project_type: 项目类型 (firmware, software等)
        project_name: 项目名称 (fw_project_1, app_project等)

    Returns:
        str: 组ID
    """
    if project_type and project_name:
        # 项目级组ID
        content = f"{component}_{project_type}_{project_name}"
    elif project_type:
        # 项目类型级组ID
        content = f"{component}_{project_type}"
    else:
        # 组件级组ID
        content = f"{component}"

    return hashlib.md5(content.encode()).hexdigest()[:12]
```

### 组名称生成算法

```python
def generate_group_name(component: str, project_type: str = None, project_name: str = None) -> str:
    """
    生成组的显示名称

    Returns:
        str: 组显示名称
    """
    component_names = {
        'REQ': '需求管理',
        'DES': '设计文档',
        'DEV': '开发实现',
        'QA': '质量保证',
        'PROD': '生产部署',
        'DEL': '交付物',
        'PM': '项目管理'
    }

    project_type_names = {
        'firmware': '固件项目',
        'software': '软件项目',
        'hardware': '硬件项目',
        'tool': '工具项目'
    }

    base_name = component_names.get(component, component)

    if project_type and project_name:
        type_name = project_type_names.get(project_type, project_type)
        return f"{base_name} > {type_name} > {project_name}"
    elif project_type:
        type_name = project_type_names.get(project_type, project_type)
        return f"{base_name} > {type_name}"
    else:
        return base_name
```

### 文档分组分配算法

```python
def assign_document_to_group(doc_path: str, component: str) -> Dict[str, str]:
    """
    为文档分配组

    Args:
        doc_path: 文档路径
        component: 组件代码

    Returns:
        Dict: 包含group_id和group_name的字典
    """
    path_parts = Path(doc_path).parts

    # 特殊处理development目录
    if component == 'DEV' and 'development' in path_parts:
        dev_index = path_parts.index('development')

        if len(path_parts) > dev_index + 2:
            # 路径格式: development/project_type/project_name/...
            project_type = path_parts[dev_index + 1]  # firmware
            project_name = path_parts[dev_index + 2]  # fw_project_1

            return {
                'group_id': generate_group_id(component, project_type, project_name),
                'group_name': generate_group_name(component, project_type, project_name)
            }
        elif len(path_parts) > dev_index + 1:
            # 路径格式: development/project_type/...
            project_type = path_parts[dev_index + 1]

            return {
                'group_id': generate_group_id(component, project_type),
                'group_name': generate_group_name(component, project_type)
            }

    # 默认组件级分组
    return {
        'group_id': generate_group_id(component),
        'group_name': generate_group_name(component)
    }
```

## 📊 组布局策略

### 位置计算原则

1. **组件级布局**：
   - 按工作流顺序排列：REQ → DES → DEV → QA → PROD → DEL
   - 每个组件占用固定的水平区域
   - 组件间保持足够间距

2. **项目级布局**：
   - 在组件区域内垂直排列项目组
   - 相关项目组靠近放置
   - 项目组大小根据包含文档数量调整

3. **自适应调整**：
   - 根据文档数量动态调整组大小
   - 避免组重叠
   - 保持整体布局的平衡性

### 组样式配置

```json
{
  "group_styles": {
    "component_level": {
      "border_color": "#2196F3",
      "border_width": 2,
      "background_color": "rgba(33, 150, 243, 0.1)",
      "corner_radius": 8
    },
    "project_level": {
      "border_color": "#4CAF50",
      "border_width": 1,
      "background_color": "rgba(76, 175, 80, 0.05)",
      "corner_radius": 4
    }
  }
}
```

## 🔄 动态更新机制

### 增量更新策略

1. **新文档添加**：
   - 自动识别文档所属项目
   - 查找或创建对应的组
   - 将文档节点添加到组中

2. **文档移除**：
   - 从组中移除文档节点
   - 检查组是否为空，空组自动删除
   - 重新计算组布局

3. **项目重组**：
   - 支持文档在不同项目间移动
   - 自动更新组归属关系
   - 保持组ID的稳定性

### 冲突解决机制

1. **组名冲突**：
   - 使用路径信息生成唯一后缀
   - 保持组名的可读性

2. **位置冲突**：
   - 自动调整重叠组的位置
   - 保持相对位置关系

3. **ID冲突**：
   - 使用哈希算法确保ID唯一性
   - 提供冲突检测和修复机制

## 📝 配置示例

### 完整配置示例

```json
{
  "canvas_grouping": {
    "version": "1.0.0",
    "grouping_strategy": "hierarchical",
    "max_documents_per_group": 15,
    "group_spacing": {
      "horizontal": 800,
      "vertical": 600
    },
    "component_layout": {
      "REQ": {"x": 0, "y": 0},
      "DES": {"x": 800, "y": 0},
      "DEV": {"x": 1600, "y": 0},
      "QA": {"x": 2400, "y": 0},
      "PROD": {"x": 3200, "y": 0},
      "DEL": {"x": 4000, "y": 0},
      "PM": {"x": 2000, "y": -800}
    },
    "project_type_mapping": {
      "firmware": "固件项目",
      "software": "软件项目",
      "hardware": "硬件项目",
      "tool": "工具项目"
    }
  }
}
```

## 🚀 使用指南

### 开发者使用

1. **实现组分配逻辑**：
   ```python
   from canvas.group_create_principle import assign_document_to_group

   # 为文档分配组
   group_info = assign_document_to_group(
       doc_path="development/firmware/fw_project_1/docs/api.md",
       component="DEV"
   )
   ```

2. **创建Canvas组**：
   ```python
   canvas_manager.create_group(
       group_id=group_info['group_id'],
       group_name=group_info['group_name'],
       documents=project_documents
   )
   ```

### 配置管理

1. **修改分组策略**：
   - 编辑 `project_type_mapping` 配置
   - 调整 `component_layout` 位置
   - 更新 `group_spacing` 间距

2. **添加新项目类型**：
   - 在 `project_type_mapping` 中添加映射
   - 更新分组算法逻辑
   - 测试新项目类型的分组效果

## ⚠️ 注意事项

1. **性能考虑**：
   - 大量文档时考虑分页或懒加载
   - 组布局计算的复杂度控制
   - Canvas渲染性能优化

2. **兼容性**：
   - 保持与现有Canvas格式的兼容
   - 支持旧版本组结构的迁移
   - 向后兼容的ID生成策略

3. **用户体验**：
   - 组名称的可读性和简洁性
   - 组结构的直观性和逻辑性
   - 支持用户手动调整组结构

---

**更新时间**: 2025-07-07
**版本**: v2.2 - 修复分组结构，实现正确的组件代号+子目录缩写两层架构