|      |        |           |                                                                                                                                                                      |     |
| ---- | ------ | --------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| 文档版本 | 软件版本号  | 变更日期      | 变更内容                                                                                                                                                                 | 变更人 |
| 1.0  | V1.0.0 | 2023/5/20 | 首次发布版本                                                                                                                                                               | x  |
| 1.1  | V1.0.5 | 2023/6/28 | 1. 光斑错误原因录入MES，rsn_code栏内容：改中文为错误码，详情见错误码解析  <br>2. 调试正常时，录入MES rsn_code内容为空  <br>3. 增加T4初始虚光斑的反向调节  <br>4. 修改模组版本号管控：Vx.y.z.m(日期不管控，例如V1.6.6.17.23.6.7，管控 1.6.6.17) | x  |
| 1.2  | V1.1.0 | 2023/9/13 | 1. 修复已知的软件问题  <br>2. 增加光斑中心配置参数，自设定光斑中心的位置  <br>3. 优化事务号索引速度，增加事务号本地存储  <br>4. 增加标签号异常重复获取次数                                                                         | x  |
| 1.3  | v1.2.7 | 2024/9/13 |                                                                                                                                                                      | x  |
## 1. 界面介绍
1. 打开软件: CSPC_Lidar_IA_vX.X.X.exe
2. 打开主界面，选择调焦功能
    
    ![Untitled 177.png](images/Untitled 177.png)
    
3. 调节界面
    
    ![Untitled 1 96.png](images/Untitled 1 96.png)
    
    1. 选择自动调节功能
    2. 选择相应端口
    3. 点击open开始调节
## 2. 配置说明
1. 打开根目录下 config文件夹
2. 配置以下三个文件
    1. clen_config.ini，主要配置调试设备信息与MES账号信息
    2. clen_config.xml，主要配置调节参数
    3. clen_config.xml，主要配置机台参数
## 3. 数据记录说明
1. 默认在桌面生成 ”镜片装调.csv”文件
    
    注意点:
    
    1. 该文件在打开软件时自动生成，若该文件被删除时，只需重开软件
    2. 调节过程中不能打开该文件，否则数据无法保存
2. 执行日志数据，在根目录下自动生成 error.log
    
    注：该文件需要定时删除，避免内存过大
    
## 4. 异常说明
异常主要分为两类
1. 导致调节异常错误，该类型错误直接在窗口日志显示框显示
    1. 步骤执行与指令接收错误，显示：
        
        ```C
        "task: " + step_name + "fatal";
        ```
        
    2. 过程异常
        
        ```C
        {0x0001, "串口打开失败 "},
        {0x0002, "版本异常 "},
        {0x0004, "芯片ID异常 "},
        {0x0008, "XY轴限位 "},
        {0x0010, "Z轴限位 "},
        {0x0020, "机台控制指令发送异常 "},
        ```
        
    3. 调节异常
        
        ```JavaScript
        {0x0001, "未找到光斑 "},
        {0x0002, "不能移动到目标区域 "},
        {0x0004, "无法对称分布 "},
        ```
        
2. 光斑判定异常
    
    ```C
    {0x0001, "光斑中心不在目标区域 "},
    {0x0002, "光斑十字不对称 "},
    {0x0004, "光斑外圈不对称 "},
    {0x0008, "中心光斑强度异常 "},
    {0x0010, "外圈光斑强度异常"},
    {0x0020, "光斑中心与十字区域强度差值异常"},
    ```
    
    - 举例
        
        ![Untitled 178.png](images/Untitled 178.png)
        
        异常码：20，对应错误："光斑中心与十字区域强度差值异常"