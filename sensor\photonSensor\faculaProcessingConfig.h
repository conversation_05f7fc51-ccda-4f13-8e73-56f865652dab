#ifndef FACULA_PROCESSING_CONFIG_H
#define FACULA_PROCESSING_CONFIG_H

// #include "../../components/lensAdjust/lensReadIni.h"  // 移除循环依赖
// 前向声明，避免头文件依赖
namespace ImageProcessing {
enum class FilterType;
enum class InterpolationType;
struct InterpolationParams;
struct KalmanParams;
struct ConvolutionParams;
}  // namespace ImageProcessing
#include <QDebug>
#include <QString>
#include <QVector>

// 本地配置结构体，避免循环依赖
struct ProcessingConfig {
    uint8_t interpolation_type;       // 插值类型
    QString filter_types;             // 滤波器类型列表
    float   interpolation_offset;     // 插值偏移量
    float   kalman_strength;          // 卡尔曼滤波强度
    uint8_t convolution_kernel_size;  // 卷积核大小
};

/**
 * @brief 光斑处理配置映射工具类
 *
 * 负责将配置文件中的数值映射到ImageProcessing模块的枚举类型
 */
class FaculaProcessingConfig {
  public:
    /**
     * @brief 将配置中的插值类型转换为ImageProcessing枚举
     * @param configType 配置文件中的插值类型数值
     * @return ImageProcessing::InterpolationType
     */
    static ImageProcessing::InterpolationType mapInterpolationType(uint8_t configType);

    /**
     * @brief 将配置中的滤波器类型字符串转换为枚举列表
     * @param filterTypesStr 配置文件中的滤波器类型字符串 (如 "0,1,2")
     * @return QVector<ImageProcessing::FilterType>
     */
    static QVector<ImageProcessing::FilterType> mapFilterTypes(const QString &filterTypesStr);

    /**
     * @brief 创建插值参数结构体
     * @param config 配置信息
     * @return ImageProcessing::InterpolationParams
     */
    static ImageProcessing::InterpolationParams createInterpolationParams(const ProcessingConfig &config);

    /**
     * @brief 创建卡尔曼滤波参数
     * @param config 配置信息
     * @return ImageProcessing::KalmanParams
     */
    static ImageProcessing::KalmanParams createKalmanParams(const ProcessingConfig &config);

    /**
     * @brief 创建卷积滤波参数
     * @param config 配置信息
     * @return ImageProcessing::ConvolutionParams
     */
    static ImageProcessing::ConvolutionParams createConvolutionParams(const ProcessingConfig &config);

    /**
     * @brief 创建中值滤波参数
     * @param config 配置信息
     * @return ImageProcessing::MedianParams
     */
    static ImageProcessing::MedianParams createMedianParams(const ProcessingConfig &config);

    /**
     * @brief 创建高斯滤波参数
     * @param config 配置信息
     * @return ImageProcessing::GaussianParams
     */
    static ImageProcessing::GaussianParams createGaussianParams(const ProcessingConfig &config);

    /**
     * @brief 创建双边滤波参数
     * @param config 配置信息
     * @return ImageProcessing::BilateralParams
     */
    static ImageProcessing::BilateralParams createBilateralParams(const ProcessingConfig &config);

    /**
     * @brief 创建加权均值滤波参数
     * @param config 配置信息
     * @return ImageProcessing::WeightedAverageParams
     */
    static ImageProcessing::WeightedAverageParams createWeightedAverageParams(const ProcessingConfig &config);

    /**
     * @brief 验证配置的有效性
     * @param config 配置信息
     * @return true if valid, false otherwise
     */
    static bool validateConfig(const ProcessingConfig &config);

    /**
     * @brief 获取插值类型的描述字符串
     * @param type 插值类型
     * @return 描述字符串
     */
    static QString getInterpolationTypeDescription(ImageProcessing::InterpolationType type);

    /**
     * @brief 获取滤波器类型的描述字符串
     * @param type 滤波器类型
     * @return 描述字符串
     */
    static QString getFilterTypeDescription(ImageProcessing::FilterType type);

  private:
    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    static void logDebug(const QString &message);

    /**
     * @brief 记录警告信息
     * @param message 警告信息
     */
    static void logWarning(const QString &message);
};

#endif  // FACULA_PROCESSING_CONFIG_H
