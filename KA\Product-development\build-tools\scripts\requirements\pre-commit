#!/bin/bash
# 文件路径: .git/hooks/pre-commit

# 获取被修改的需求矩阵文件
changed_matrices=$(git diff --cached --name-only | grep 'requirements_matrix.md')

if [ -n "$changed_matrices" ]; then
    echo "验证变更的需求矩阵..."
    
    # 执行验证脚本
    python scripts/requirements/validate_matrices.py --config=__level_config.json
    
    # 检查验证结果
    if [ $? -ne 0 ]; then
        echo "需求矩阵验证失败，请修复问题后再提交"
        exit 1
    fi
fi

exit 0 