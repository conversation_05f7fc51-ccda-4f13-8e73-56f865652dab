#!/usr/bin/env python3
"""
需求任务同步脚本 - 实现T10功能
T10: 任务跟踪系统 - 同步需求和任务状态，维护需求-任务映射关系
"""

import json
import argparse
import sys
import os
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import requests

def sync_requirements_tasks(req_file: str = "", task_file: str = "", sync_target: str = "local") -> Dict[str, Any]:
    """
    同步需求和任务状态
    
    Args:
        req_file: 需求文件路径
        task_file: 任务文件路径  
        sync_target: 同步目标 (local, github, linear)
    
    Returns:
        同步结果字典
    """
    try:
        # 读取需求数据
        requirements = []
        if req_file and os.path.exists(req_file):
            with open(req_file, 'r', encoding='utf-8') as f:
                requirements = json.load(f)
        
        # 读取任务数据
        tasks = []
        if task_file and os.path.exists(task_file):
            with open(task_file, 'r', encoding='utf-8') as f:
                tasks = json.load(f)
        
        if not requirements and not tasks:
            return {
                "success": False,
                "error": "未找到需求或任务数据文件",
                "req_file": req_file,
                "task_file": task_file
            }
        
        # 执行同步
        if sync_target == "local":
            return sync_local(requirements, tasks, req_file, task_file)
        elif sync_target == "github":
            return sync_github(requirements, tasks)
        elif sync_target == "linear":
            return sync_linear(requirements, tasks)
        else:
            return {
                "success": False,
                "error": f"不支持的同步目标: {sync_target}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": f"同步需求任务时出错: {str(e)}",
            "req_file": req_file,
            "task_file": task_file
        }

def sync_local(requirements: List[Dict], tasks: List[Dict], req_file: str, task_file: str) -> Dict[str, Any]:
    """本地同步需求和任务"""
    
    # 创建需求-任务映射
    req_task_mapping = {}
    task_req_mapping = {}
    
    for task in tasks:
        req_id = task.get('requirement_id')
        if req_id:
            if req_id not in req_task_mapping:
                req_task_mapping[req_id] = []
            req_task_mapping[req_id].append(task.get('task_id'))
            task_req_mapping[task.get('task_id')] = req_id
    
    # 更新需求状态
    updated_requirements = []
    for req in requirements:
        req_id = req.get('requirement_id', req.get('id'))
        related_tasks = req_task_mapping.get(req_id, [])
        
        # 根据关联任务状态更新需求状态
        if related_tasks:
            task_statuses = [t.get('status', 'not_started') for t in tasks if t.get('task_id') in related_tasks]
            req_status = calculate_requirement_status(task_statuses)
            req['status'] = req_status
            req['related_tasks'] = related_tasks
            req['task_progress'] = calculate_task_progress(task_statuses)
        
        req['last_sync'] = datetime.now().isoformat()
        updated_requirements.append(req)
    
    # 更新任务状态
    updated_tasks = []
    for task in tasks:
        req_id = task.get('requirement_id')
        if req_id:
            # 查找对应需求
            req = next((r for r in requirements if r.get('requirement_id', r.get('id')) == req_id), None)
            if req:
                task['requirement_status'] = req.get('status', 'unknown')
                task['requirement_priority'] = req.get('priority', 'medium')
        
        task['last_sync'] = datetime.now().isoformat()
        updated_tasks.append(task)
    
    # 保存更新后的数据
    sync_dir = Path(req_file).parent / "sync_results" if req_file else Path("sync_results")
    sync_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存同步后的需求
    if updated_requirements:
        synced_req_file = sync_dir / f"synced_requirements_{timestamp}.json"
        with open(synced_req_file, 'w', encoding='utf-8') as f:
            json.dump(updated_requirements, f, ensure_ascii=False, indent=2)
    
    # 保存同步后的任务
    if updated_tasks:
        synced_task_file = sync_dir / f"synced_tasks_{timestamp}.json"
        with open(synced_task_file, 'w', encoding='utf-8') as f:
            json.dump(updated_tasks, f, ensure_ascii=False, indent=2)
    
    # 生成同步报告
    sync_report = generate_sync_report(updated_requirements, updated_tasks, req_task_mapping)
    report_file = sync_dir / f"sync_report_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(sync_report, f, ensure_ascii=False, indent=2)
    
    return {
        "success": True,
        "sync_target": "local",
        "timestamp": datetime.now().isoformat(),
        "sync_statistics": {
            "requirements_updated": len(updated_requirements),
            "tasks_updated": len(updated_tasks),
            "mappings_created": len(req_task_mapping)
        },
        "output_files": {
            "synced_requirements": str(synced_req_file) if updated_requirements else None,
            "synced_tasks": str(synced_task_file) if updated_tasks else None,
            "sync_report": str(report_file)
        },
        "mapping_summary": {
            "requirements_with_tasks": len(req_task_mapping),
            "tasks_with_requirements": len(task_req_mapping),
            "orphaned_tasks": len([t for t in tasks if not t.get('requirement_id')])
        },
        "status_distribution": get_status_distribution(updated_requirements, updated_tasks)
    }

def calculate_requirement_status(task_statuses: List[str]) -> str:
    """根据任务状态计算需求状态"""
    if not task_statuses:
        return "draft"
    
    status_counts = {}
    for status in task_statuses:
        status_counts[status] = status_counts.get(status, 0) + 1
    
    total_tasks = len(task_statuses)
    completed_tasks = status_counts.get('completed', 0) + status_counts.get('done', 0)
    in_progress_tasks = status_counts.get('in_progress', 0) + status_counts.get('active', 0)
    
    if completed_tasks == total_tasks:
        return "completed"
    elif completed_tasks > 0 or in_progress_tasks > 0:
        return "in_progress"
    else:
        return "planned"

def calculate_task_progress(task_statuses: List[str]) -> Dict[str, Any]:
    """计算任务进度"""
    if not task_statuses:
        return {"total": 0, "completed": 0, "in_progress": 0, "percentage": 0}
    
    total = len(task_statuses)
    completed = len([s for s in task_statuses if s in ['completed', 'done']])
    in_progress = len([s for s in task_statuses if s in ['in_progress', 'active']])
    
    return {
        "total": total,
        "completed": completed,
        "in_progress": in_progress,
        "not_started": total - completed - in_progress,
        "percentage": round((completed / total) * 100, 1) if total > 0 else 0
    }

def sync_github(requirements: List[Dict], tasks: List[Dict]) -> Dict[str, Any]:
    """同步到GitHub Issues"""
    # 这里是GitHub同步的占位符实现
    # 实际实现需要GitHub API集成
    
    return {
        "success": True,
        "sync_target": "github",
        "timestamp": datetime.now().isoformat(),
        "message": "GitHub同步功能需要配置GitHub API访问权限",
        "requirements_count": len(requirements),
        "tasks_count": len(tasks),
        "next_steps": [
            "配置GitHub Personal Access Token",
            "设置目标仓库信息",
            "映射需求和任务到GitHub Issues",
            "设置同步规则和频率"
        ]
    }

def sync_linear(requirements: List[Dict], tasks: List[Dict]) -> Dict[str, Any]:
    """同步到Linear"""
    # 这里是Linear同步的占位符实现
    # 实际实现需要Linear API集成
    
    return {
        "success": True,
        "sync_target": "linear",
        "timestamp": datetime.now().isoformat(),
        "message": "Linear同步功能需要配置Linear API访问权限",
        "requirements_count": len(requirements),
        "tasks_count": len(tasks),
        "next_steps": [
            "配置Linear API Key",
            "设置目标团队和项目",
            "映射需求和任务到Linear Issues",
            "设置同步规则和标签"
        ]
    }

def generate_sync_report(requirements: List[Dict], tasks: List[Dict], req_task_mapping: Dict) -> Dict[str, Any]:
    """生成同步报告"""
    
    # 需求状态统计
    req_status_dist = {}
    for req in requirements:
        status = req.get('status', 'unknown')
        req_status_dist[status] = req_status_dist.get(status, 0) + 1
    
    # 任务状态统计
    task_status_dist = {}
    for task in tasks:
        status = task.get('status', 'unknown')
        task_status_dist[status] = task_status_dist.get(status, 0) + 1
    
    # 映射关系分析
    mapping_analysis = {
        "requirements_with_tasks": len(req_task_mapping),
        "requirements_without_tasks": len(requirements) - len(req_task_mapping),
        "average_tasks_per_requirement": sum(len(tasks) for tasks in req_task_mapping.values()) / len(req_task_mapping) if req_task_mapping else 0,
        "max_tasks_per_requirement": max(len(tasks) for tasks in req_task_mapping.values()) if req_task_mapping else 0
    }
    
    return {
        "sync_summary": {
            "total_requirements": len(requirements),
            "total_tasks": len(tasks),
            "sync_timestamp": datetime.now().isoformat()
        },
        "requirement_status_distribution": req_status_dist,
        "task_status_distribution": task_status_dist,
        "mapping_analysis": mapping_analysis,
        "recommendations": generate_sync_recommendations(requirements, tasks, req_task_mapping),
        "data_quality": assess_data_quality(requirements, tasks)
    }

def get_status_distribution(requirements: List[Dict], tasks: List[Dict]) -> Dict[str, Dict[str, int]]:
    """获取状态分布"""
    req_dist = {}
    task_dist = {}
    
    for req in requirements:
        status = req.get('status', 'unknown')
        req_dist[status] = req_dist.get(status, 0) + 1
    
    for task in tasks:
        status = task.get('status', 'unknown')
        task_dist[status] = task_dist.get(status, 0) + 1
    
    return {
        "requirements": req_dist,
        "tasks": task_dist
    }

def generate_sync_recommendations(requirements: List[Dict], tasks: List[Dict], req_task_mapping: Dict) -> List[str]:
    """生成同步建议"""
    recommendations = []
    
    # 检查孤立需求
    orphaned_reqs = len(requirements) - len(req_task_mapping)
    if orphaned_reqs > 0:
        recommendations.append(f"发现 {orphaned_reqs} 个需求没有关联任务，建议创建对应任务")
    
    # 检查孤立任务
    orphaned_tasks = len([t for t in tasks if not t.get('requirement_id')])
    if orphaned_tasks > 0:
        recommendations.append(f"发现 {orphaned_tasks} 个任务没有关联需求，建议关联到相应需求")
    
    # 检查状态一致性
    inconsistent_count = 0
    for req in requirements:
        req_id = req.get('requirement_id', req.get('id'))
        if req_id in req_task_mapping:
            related_tasks = [t for t in tasks if t.get('task_id') in req_task_mapping[req_id]]
            if related_tasks:
                task_statuses = [t.get('status') for t in related_tasks]
                expected_status = calculate_requirement_status(task_statuses)
                if req.get('status') != expected_status:
                    inconsistent_count += 1
    
    if inconsistent_count > 0:
        recommendations.append(f"发现 {inconsistent_count} 个需求状态与任务状态不一致，建议重新同步")
    
    return recommendations

def assess_data_quality(requirements: List[Dict], tasks: List[Dict]) -> Dict[str, Any]:
    """评估数据质量"""
    quality_issues = []
    
    # 检查必填字段
    req_missing_fields = 0
    for req in requirements:
        if not req.get('requirement_id') and not req.get('id'):
            req_missing_fields += 1
    
    task_missing_fields = 0
    for task in tasks:
        if not task.get('task_id'):
            task_missing_fields += 1
    
    if req_missing_fields > 0:
        quality_issues.append(f"{req_missing_fields} 个需求缺少ID字段")
    
    if task_missing_fields > 0:
        quality_issues.append(f"{task_missing_fields} 个任务缺少ID字段")
    
    return {
        "quality_score": max(0, 100 - len(quality_issues) * 10),
        "issues": quality_issues,
        "data_completeness": {
            "requirements_with_id": len(requirements) - req_missing_fields,
            "tasks_with_id": len(tasks) - task_missing_fields
        }
    }

def main():
    parser = argparse.ArgumentParser(description='需求任务同步工具')
    parser.add_argument('--req-file', '-r', default='', help='需求文件路径')
    parser.add_argument('--task-file', '-t', default='', help='任务文件路径')
    parser.add_argument('--sync-target', '-s', default='local',
                       choices=['local', 'github', 'linear'],
                       help='同步目标')
    
    args = parser.parse_args()
    
    # 执行同步
    result = sync_requirements_tasks(args.req_file, args.task_file, args.sync_target)
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 返回退出码
    sys.exit(0 if result.get('success', False) else 1)

if __name__ == "__main__":
    main()
