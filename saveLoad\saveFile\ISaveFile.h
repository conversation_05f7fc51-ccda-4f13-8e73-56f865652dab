#ifndef _ISAVE_FILE_H_
#define _ISAVE_FILE_H_

#include <qwidget.h>

class ISaveFile
{
public:
  ISaveFile(){};
  virtual ~ISaveFile(){};

  virtual void createFile(const QString &loc_name, const QString &fName, QMap<QString,QVariant> data) = 0;
  virtual void createOneFile(const QString &loc_name, const QString &fName, QMap<QString,QVariant> data) = 0;
  virtual void createRandomFile(void) = 0;
  virtual void saveFile(const QString &loc_name, const QString &fName, const QMap<QString,QVariant> &data) = 0;
  virtual void writeFile(QMap<QString,QVariant> data) = 0;

protected:
  QString m_dtStr;
  QWidget *m_message_box_ = nullptr;
};

#endif // PARAMLOAD_H
