{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "CMake: Configure Debug",
            "type": "shell",
            "command": "cmake",
            "args": [
                "-B",
                "${workspaceFolder}/build/Debug",
                "-DCMAKE_BUILD_TYPE=Debug"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": []
        },
        {
            "label": "CMake: Configure Release",
            "type": "shell",
            "command": "cmake",
            "args": [
                "-B",
                "${workspaceFolder}/build/Release",
                "-DCMAKE_BUILD_TYPE=Release"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": []
        },
        {
            "label": "CMake: Build Debug",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "${workspaceFolder}/build/Debug",
                "--config",
                "Debug"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "dependsOn": "CMake: Configure Debug"
        },
        {
            "label": "CMake: Build Release",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "${workspaceFolder}/build/Release",
                "--config",
                "Release"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "dependsOn": "CMake: Configure Release"
        },
        {
            "label": "CMake: Build MinSizeRel",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "${workspaceFolder}/build/MinSizeRel",
                "--config",
                "MinSizeRel"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            // "dependsOn": "CMake: Configure MinSizeRel"
        },
    ]
}