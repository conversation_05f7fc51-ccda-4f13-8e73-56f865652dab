#!/usr/bin/env python3
"""
交付物配置生成器
负责生成项目的交付物管理相关配置文件
"""

import os
import json
from pathlib import Path
import argparse


def generate_deliverables_config(project_path=".", project_type="single_layer"):
    """生成交付物配置文件"""
    
    # 创建配置目录
    config_dir = Path(project_path) / 'config'
    config_dir.mkdir(exist_ok=True)
    
    # 交付物配置
    deliverables_config = {
        "deliverables_settings": {
            "documentation_complete": True,
            "version_control": True,
            "package_signing": True,
            "release_notes": True
        },
        "package_types": [
            "source",
            "binary",
            "documentation"
        ],
        "distribution": {
            "internal_release": True,
            "customer_release": False,
            "archive_enabled": True
        }
    }
    
    # 写入配置文件
    config_file = config_dir / 'deliverables_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(deliverables_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 交付物配置文件已生成: {config_file}")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成交付物配置文件')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'], help='项目类型')
    
    args = parser.parse_args()
    
    success = generate_deliverables_config(args.project_path, args.project_type)
    
    if success:
        print("[+] 交付物配置生成完成")
    else:
        print("[X] 交付物配置生成失败")
        exit(1)


if __name__ == "__main__":
    main() 