#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工作流程图生成器 - 用于16. line lidar项目的流程可视化
"""

import os
import json
import argparse
from datetime import datetime

def get_color_by_completion(completion):
    """根据完成度返回颜色"""
    if completion >= 80:
        return "#4CAF50"  # 绿色
    elif completion >= 50:
        return "#2196F3"  # 蓝色
    elif completion >= 30:
        return "#FFC107"  # 黄色
    else:
        return "#F44336"  # 红色

def generate_workflow_diagram(status_data, output_dir="reports"):
    """生成项目流程图"""
    # 准备Mermaid图表
    mermaid = ["```mermaid", "graph LR"]
    
    # 添加阶段节点
    phases = status_data["phases"]
    for phase_name, phase_data in phases.items():
        completion = phase_data["completion"] * 100
        color = get_color_by_completion(completion)
        
        # 根据完成度设置样式
        style = "stroke:#333,stroke-width:2px"
        if completion > 80:
            style += ",fill:" + color
        elif completion > 30:
            style += ",fill:" + color + "77"  # 半透明
        else:
            style += ",fill:#f5f5f5"
            
        node_id = phase_name.replace(" ", "_")
        mermaid.append(f'    {node_id}["{phase_name.capitalize()}<br>{completion:.1f}%"]')
        mermaid.append(f'    style {node_id} {style}')
    
    # 添加阶段间连接
    phase_names = list(phases.keys())
    for i in range(len(phase_names) - 1):
        curr = phase_names[i].replace(" ", "_")
        next_phase = phase_names[i + 1].replace(" ", "_")
        mermaid.append(f"    {curr} --> {next_phase}")
    
    # 添加子文档节点（可选，如果流程图不需要太复杂可以注释掉）
    for phase_name, phase_data in phases.items():
        if not phase_data.get("exists", False):
            continue
            
        phase_id = phase_name.replace(" ", "_")
        for doc in phase_data.get("documents", [])[:3]:  # 限制每阶段最多显示3个文档，避免图表过于复杂
            doc_id = (phase_name + "_" + doc["filename"]).replace(" ", "_").replace(".", "_")
            doc_title = doc["title"] if len(doc["title"]) < 20 else doc["title"][:17] + "..."
            completion = doc["completion"] * 100
            
            mermaid.append(f'    {doc_id}["{doc_title}<br>{completion:.1f}%"]')
            mermaid.append(f"    {phase_id} -.-> {doc_id}")
            
            # 设置文档节点样式
            color = get_color_by_completion(completion)
            mermaid.append(f'    style {doc_id} fill:{color},stroke:#333,stroke-width:1px')
    
    mermaid.append("```")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存Mermaid图
    mermaid_content = "\n".join(mermaid)
    mermaid_path = os.path.join(output_dir, "workflow_diagram.md")
    with open(mermaid_path, 'w', encoding='utf-8') as f:
        f.write(f"# {status_data.get('project_name', '项目')}流程图\n\n")
        f.write(f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
        f.write(mermaid_content)
    
    return mermaid_path

def generate_gantt_chart(status_data, output_dir="reports"):
    """生成甘特图展示项目计划和进度"""
    mermaid = ["```mermaid", "gantt", "    title 16. line lidar项目计划", "    dateFormat  YYYY-MM-DD", "    axisFormat %m/%d", "    excludes weekends"]
    
    # 根据项目状态估算各阶段日期
    # 这里用模拟数据，实际项目中应该从项目计划或统计历史数据中获取
    
    today = datetime.now().strftime('%Y-%m-%d')
    mermaid.append(f"    section 项目整体")
    mermaid.append(f"    项目启动: done, a1, 2023-01-01, 2023-01-15")
    mermaid.append(f"    项目规划: done, a2, after a1, 30d")
    mermaid.append(f"    项目执行: active, a3, after a2, 120d")
    mermaid.append(f"    项目收尾: a4, after a3, 30d")
    
    # 各阶段进度
    phases = status_data["phases"]
    phase_order = ["product_info", "requirements", "design", "development", "quality"]
    
    start_date = "2023-01-15"  # 示例起始日期
    
    for i, phase_name in enumerate(phase_order):
        if phase_name not in phases:
            continue
            
        phase_data = phases[phase_name]
        completion = phase_data["completion"] * 100
        
        # 设置持续时间和状态
        duration = "30d"  # 示例持续时间
        status = "done" if completion >= 90 else "active" if completion > 10 else "crit" if completion < 10 else ""
        
        section_name = phase_name.capitalize()
        mermaid.append(f"    section {section_name}")
        
        # 对于存在的阶段添加任务
        if phase_data.get("exists", False):
            # 添加阶段主任务
            task_id = f"{phase_name}_task"
            after = f"after {phase_order[i-1]}_task" if i > 0 else f"after a1"
            mermaid.append(f"    {section_name}: {status}, {task_id}, {after}, {duration}")
            
            # 添加该阶段的主要文档任务
            for j, doc in enumerate(phase_data.get("documents", [])[:3]):
                doc_completion = doc["completion"] * 100
                doc_status = "done" if doc_completion >= 90 else "active" if doc_completion > 10 else "crit"
                doc_id = f"{phase_name}_doc{j}"
                doc_title = doc["title"] if len(doc["title"]) < 20 else doc["title"][:17] + "..."
                
                mermaid.append(f"    {doc_title}: {doc_status}, {doc_id}, after {task_id}, 10d")
    
    mermaid.append("```")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存Mermaid图
    mermaid_content = "\n".join(mermaid)
    mermaid_path = os.path.join(output_dir, "gantt_chart.md")
    with open(mermaid_path, 'w', encoding='utf-8') as f:
        f.write(f"# {status_data.get('project_name', '项目')}甘特图\n\n")
        f.write(f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
        f.write(mermaid_content)
    
    return mermaid_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成项目流程图")
    parser.add_argument("--workspace", required=True, help="工作区路径")
    parser.add_argument("--output", default="reports", help="输出目录")
    args = parser.parse_args()
    
    # 加载项目状态数据
    status_file = os.path.join(args.output, "project_status.json")
    if not os.path.exists(status_file):
        print(f"错误: 找不到项目状态文件 {status_file}")
        print("请先运行 generate_progress_dashboard.py 生成项目状态")
        return 1
        
    with open(status_file, 'r', encoding='utf-8') as f:
        status_data = json.load(f)
    
    # 生成流程图
    workflow_path = generate_workflow_diagram(status_data, args.output)
    print(f"流程图已生成: {workflow_path}")
    
    # 生成甘特图
    gantt_path = generate_gantt_chart(status_data, args.output)
    print(f"甘特图已生成: {gantt_path}")
    
    # 生成合并的可视化报告
    report_path = os.path.join(args.output, "visualization_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# {status_data.get('project_name', '项目')}可视化报告\n\n")
        f.write(f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
        
        # 添加整体进度
        f.write("## 整体进度\n\n")
        f.write(f"当前总体完成度: **{status_data['overall_completion']*100:.1f}%**\n\n")
        
        # 插入流程图
        f.write("## 流程状态图\n\n")
        with open(workflow_path, 'r', encoding='utf-8') as wf:
            # 跳过标题行
            next(wf)
            next(wf)
            next(wf)
            for line in wf:
                f.write(line)
        
        # 插入甘特图
        f.write("\n## 项目计划甘特图\n\n")
        with open(gantt_path, 'r', encoding='utf-8') as gf:
            # 跳过标题行
            next(gf)
            next(gf)
            next(gf)
            for line in gf:
                f.write(line)
        
        # 添加结论
        f.write("\n## 结论与建议\n\n")
        
        # 计算完成度高低的阶段
        phase_completion = [(p, d["completion"]) for p, d in status_data["phases"].items() if d.get("exists", False)]
        phase_completion.sort(key=lambda x: x[1])
        
        if phase_completion:
            lowest_phase = phase_completion[0]
            highest_phase = phase_completion[-1]
            
            f.write("### 重点关注项\n\n")
            f.write(f"- **最低完成度阶段**: {lowest_phase[0].capitalize()} ({lowest_phase[1]*100:.1f}%)\n")
            f.write(f"- **最高完成度阶段**: {highest_phase[0].capitalize()} ({highest_phase[1]*100:.1f}%)\n\n")
            
            f.write("### 下一步建议\n\n")
            if status_data["overall_completion"] < 0.3:
                f.write("项目处于初期阶段，建议优先完善基础文档和需求定义。\n\n")
            elif status_data["overall_completion"] < 0.7:
                f.write("项目处于中期阶段，建议加快推进低完成度模块，保持各阶段进度平衡。\n\n")
            else:
                f.write("项目处于后期阶段，建议关注质量验证工作，准备发布相关文档。\n\n")
    
    print(f"可视化报告已生成: {report_path}")
    return 0

if __name__ == "__main__":
    exit(main()) 