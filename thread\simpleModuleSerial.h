#ifndef _SIMPLE_MODULE_SERIAL_H_
#define _SIMPLE_MODULE_SERIAL_H_

#include <QString>
#include <QMessageBox>
#include <QDebug>
#include <QTime>
#include <QTimer>

#include "ISimpleModule.h"

class CSimpleModuleSerial: public QObject
{
    Q_OBJECT
public:
    explicit CSimpleModuleSerial(QObject *parent = nullptr);
    ~CSimpleModuleSerial();

    int8_t     m_task_id;

    void deviceChangeInterface(const uint8_t &device_id, ISimpleModule* simple_module_); //设备接口调整
    void addDevice(ISimpleModule *simple_module_);
    void taskIdChange(const uint8_t &id);
public slots:
    //void portInit(bool isOpen);
    void loop(int task_id); //接收串口数据API

private:
    //ISimpleModule *mi_simple_module_ = nullptr;
    QList<ISimpleModule*> ml_simple_module;
};

#endif
