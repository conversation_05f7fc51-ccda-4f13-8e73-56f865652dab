#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置文件合并器

专门负责合并多个配置文件。
遵循单一职责原则，只负责配置文件的合并功能。
"""

import json
import yaml
import argparse
import sys
from pathlib import Path
from typing import Dict, Any, List


def merge_configs(
    base_config: Dict[str, Any],
    override_config: Dict[str, Any],
    merge_strategy: str = "deep"
) -> Dict[str, Any]:
    """
    合并配置文件
    
    Args:
        base_config: 基础配置
        override_config: 覆盖配置
        merge_strategy: 合并策略 (shallow, deep)
    
    Returns:
        Dict[str, Any]: 合并结果
    """
    try:
        if merge_strategy == "shallow":
            # 浅合并：直接覆盖顶级键
            merged = base_config.copy()
            merged.update(override_config)
        else:
            # 深合并：递归合并嵌套字典
            merged = _deep_merge(base_config.copy(), override_config)
        
        return {
            "success": True,
            "merged_config": merged,
            "merge_strategy": merge_strategy,
            "changes": _get_config_changes(base_config, merged)
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"配置合并失败: {str(e)}"
        }


def _deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """深度合并两个字典"""
    result = base.copy()
    
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _deep_merge(result[key], value)
        else:
            result[key] = value
    
    return result


def _get_config_changes(original: Dict[str, Any], modified: Dict[str, Any]) -> List[str]:
    """获取配置变更列表"""
    changes = []
    
    # 检查新增和修改的键
    for key, value in modified.items():
        if key not in original:
            changes.append(f"新增: {key}")
        elif original[key] != value:
            changes.append(f"修改: {key}")
    
    # 检查删除的键
    for key in original:
        if key not in modified:
            changes.append(f"删除: {key}")
    
    return changes


def load_config_file(file_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"配置文件不存在: {file_path}")
    
    with open(path, 'r', encoding='utf-8') as f:
        if path.suffix.lower() in ['.yaml', '.yml']:
            return yaml.safe_load(f) or {}
        else:
            return json.load(f)


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='配置文件合并器')
    parser.add_argument('base_config', help='基础配置文件路径')
    parser.add_argument('override_config', help='覆盖配置文件路径')
    parser.add_argument('--output', help='输出文件路径（可选，默认输出到标准输出）')
    parser.add_argument('--strategy', choices=['shallow', 'deep'], default='deep',
                       help='合并策略（默认：deep）')
    parser.add_argument('--format', choices=['json', 'yaml'], default='json',
                       help='输出格式（默认：json）')
    
    args = parser.parse_args()
    
    try:
        # 加载配置文件
        base_config = load_config_file(args.base_config)
        override_config = load_config_file(args.override_config)
        
        # 合并配置
        result = merge_configs(base_config, override_config, args.strategy)
        
        if result["success"]:
            merged_config = result["merged_config"]
            changes = result["changes"]
            
            print(f"✅ 配置合并成功")
            print(f"   合并策略: {result['merge_strategy']}")
            print(f"   变更数量: {len(changes)}")
            
            if changes:
                print("   变更详情:")
                for change in changes:
                    print(f"     - {change}")
            print()
            
            # 输出合并结果
            if args.output:
                output_path = Path(args.output)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    if args.format == 'yaml':
                        yaml.dump(merged_config, f, default_flow_style=False, allow_unicode=True, indent=2)
                    else:
                        json.dump(merged_config, f, indent=2, ensure_ascii=False)
                
                print(f"✅ 合并结果已保存到: {args.output}")
            else:
                # 输出到标准输出
                if args.format == 'yaml':
                    print(yaml.dump(merged_config, default_flow_style=False, allow_unicode=True, indent=2))
                else:
                    print(json.dumps(merged_config, indent=2, ensure_ascii=False))
            
            return 0
        else:
            print(f"❌ 合并失败: {result['error']}")
            return 1
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
