import os
import re
import shutil
from pathlib import Path

# 文档图片迁移脚本
# 此脚本用于从Obsidian Vault中移动图片到文档目录，并更新Markdown文件中的图片链接
# 执行此脚本将完成以下操作：
# 1. 将Obsidian Vault中的图片复制到各个文档目录下的images文件夹
# 2. 更新Markdown文件中的图片链接格式，从Obsidian格式 ![[/image.png]] 转换为Markdown格式 ![image.png](images/image.png)

# 源目录和目标目录配置
source_dir = r"F:\101. link notebook\Obsidian Vault"  # Obsidian Vault路径
doc_dirs = [
    r"docs/output/usage/lenAdjust",  # 光路耦合软件文档目录
    r"docs/output/usage/lenMEMD",    # 手动录入MES软件文档目录
    r"docs/output/usage/lenRework",   # Rework软件文档目录
    r"docs/support/lenMachine",      # 设备支持文档目录
    r"docs/support/mes",             # MES支持文档目录
    r"docs/development/modules/lenAdjust"  # 开发文档中的lenAdjust模块文档
]

# 为每个文档目录创建images子目录
for doc_dir in doc_dirs:
    image_dir = os.path.join(doc_dir, "images")
    os.makedirs(image_dir, exist_ok=True)
    print(f"已创建图片目录: {image_dir}")

# 正则表达式模式，用于匹配Markdown中的图片链接
# 匹配Obsidian格式的图片链接，包括 ![[/image.png|image.png]] 和 ![[/image.png]] 两种格式
image_pattern = r'!\[\[(/[^|\]]+)(?:\|([^|\]]+))?\]\]'

# PDF文件和其他附件模式，处理如 ![[顺拓电子-智能系统-通讯口说明.pdf]] 格式的链接
attachment_pattern = r'!\[\[([^/][^|\]]+\.(?:pdf|doc|docx|xls|xlsx|ppt|pptx|txt))\]\]'

# 处理Markdown文件并更新图片链接的函数
def process_markdown_files(doc_dir):
    """
    处理指定目录下的所有Markdown文件，查找图片链接，
    复制相应的图片文件并更新链接格式
    
    参数:
        doc_dir (str): 要处理的文档目录路径
    """
    image_dir = os.path.join(doc_dir, "images")
    
    # 遍历目录中的所有.md文件
    for file_path in Path(doc_dir).glob("*.md"):
        print(f"正在处理文件: {file_path}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 处理图片链接
        updated_content = process_image_links(content, image_dir, doc_dir)
        
        # 处理PDF和其他附件链接（仅转换格式，不复制文件）
        updated_content = process_attachment_links(updated_content)
        
        # 将更新后的内容写回文件
        if content != updated_content:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(updated_content)
            print(f"已更新文件: {file_path}")
        else:
            print(f"{file_path} 无需更新")

def process_image_links(content, image_dir, doc_dir):
    """处理Markdown内容中的图片链接"""
    # 查找所有图片引用
    matches = re.findall(image_pattern, content)
    
    if not matches:
        print(f"未发现图片链接")
        return content
    
    updated_content = content
    
    # 处理每个图片引用
    for source_path, alt_text in matches:
        # 获取图片文件名
        image_name = os.path.basename(source_path)
        
        # 源图片在Obsidian vault中的完整路径
        full_source_path = os.path.join(source_dir, source_path.lstrip('/'))
        
        # 目标路径
        dest_path = os.path.join(image_dir, image_name)
        
        # 新的相对路径（用于Markdown链接）
        rel_path = f"images/{image_name}"
        
        # 如果图片文件存在，则复制
        if os.path.exists(full_source_path):
            try:
                shutil.copy2(full_source_path, dest_path)
                print(f"已复制: {full_source_path} -> {dest_path}")
                
                # 创建替换模式
                if alt_text:
                    old_pattern = f"![[{source_path}|{alt_text}]]"
                    new_pattern = f"![{alt_text}]({rel_path})"
                else:
                    old_pattern = f"![[{source_path}]]"
                    new_pattern = f"![{image_name}]({rel_path})"
                
                # 在内容中替换图片引用
                updated_content = updated_content.replace(old_pattern, new_pattern)
            except Exception as e:
                print(f"复制 {full_source_path} 时出错: {e}")
        else:
            print(f"未找到图片: {full_source_path}")
    
    return updated_content

def process_attachment_links(content):
    """处理PDF和其他附件链接，只转换格式不复制文件"""
    # 查找所有附件引用
    matches = re.findall(attachment_pattern, content)
    
    if not matches:
        return content
    
    updated_content = content
    
    # 处理每个附件引用
    for attachment_name in matches:
        old_pattern = f"![[{attachment_name}]]"
        # 转换为普通的Markdown链接格式
        new_pattern = f"[{attachment_name}]({attachment_name})"
        updated_content = updated_content.replace(old_pattern, new_pattern)
        print(f"已转换附件链接: {attachment_name}")
    
    return updated_content

# 处理每个文档目录中的所有Markdown文件
for doc_dir in doc_dirs:
    process_markdown_files(doc_dir)

print("图片迁移完成。") 