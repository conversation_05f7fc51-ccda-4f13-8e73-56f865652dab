#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Workflow Event Simulator
Simulates events in the workflow for testing purposes.
"""

import os
import sys
import json
import argparse
import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Import WorkflowManager if it's in the path
try:
    from workflow_manager import WorkflowManager
except ImportError:
    # If not, add the current directory to the path and try again
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.append(current_dir)
    try:
        from workflow_manager import WorkflowManager
    except ImportError:
        print("Error: Could not import WorkflowManager class.")
        sys.exit(1)

def generate_event_data(event_type: str, source_file: Optional[str] = None) -> Dict[str, Any]:
    """Generate event data based on event type."""
    event_data = {
        "event_id": f"event_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}",
        "event_type": event_type,
        "timestamp": datetime.datetime.now().isoformat(),
        "user": os.environ.get("USERNAME", "unknown"),
    }
    
    if source_file:
        event_data["file_path"] = source_file
        
        # If the file exists, add some basic metadata
        file_path = Path(source_file)
        if file_path.exists():
            event_data["file_name"] = file_path.name
            event_data["file_size"] = file_path.stat().st_size
            event_data["file_modified"] = datetime.datetime.fromtimestamp(
                file_path.stat().st_mtime).isoformat()
    
    # Add custom data based on event type
    if event_type == "document_change":
        event_data["output_path"] = source_file
        event_data["document_type"] = "requirement" if "requirement" in source_file.lower() else "design"
    
    elif event_type == "code_commit":
        event_data["commit_id"] = f"commit_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        event_data["commit_message"] = "Simulated code commit"
        
    elif event_type == "test_complete":
        event_data["test_results"] = {
            "total": 10,
            "passed": 8,
            "failed": 2,
            "skipped": 0
        }
        
    elif event_type == "production_ready":
        event_data["version"] = "1.0.0"
        event_data["release_notes_path"] = "documentation/release_notes/v1.0.0.md"
        event_data["product_name"] = "Demo Product"
    
    return event_data

def simulate_event(
    config_path: str,
    from_component: str,
    to_component: str,
    trigger_name: str,
    event_type: str,
    source_file: Optional[str] = None,
    custom_data_json: Optional[str] = None
) -> None:
    """Simulate an event in the workflow."""
    
    # Create workflow manager
    manager = WorkflowManager(config_path)
    
    # Generate basic event data
    event_data = generate_event_data(event_type, source_file)
    
    # Add custom data if provided
    if custom_data_json:
        try:
            custom_data = json.loads(custom_data_json)
            event_data.update(custom_data)
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON in custom data: {custom_data_json}")
            return
    
    # Print event information
    print("Simulating event:")
    print(f"  From: {from_component}")
    print(f"  To: {to_component}")
    print(f"  Trigger: {trigger_name}")
    print(f"  Event Type: {event_type}")
    print(f"  Event Data: {json.dumps(event_data, indent=2)}")
    
    # Process the event
    try:
        results = manager.process_event(from_component, to_component, trigger_name, event_data)
        
        # Print results
        print("\nEvent processing results:")
        for i, result in enumerate(results):
            print(f"\nHandler {i+1}: {result.get('handler', 'Unknown')}")
            print(f"  Type: {result.get('type', 'Unknown')}")
            print(f"  Status: {result.get('status', 'Unknown')}")
            
            if result.get('output'):
                print(f"  Output: {result.get('output')}")
            
            if result.get('error'):
                print(f"  Error: {result.get('error')}")
        
        if not results:
            print("  No handlers executed.")
    
    except Exception as e:
        print(f"Error processing event: {e}")

def main():
    parser = argparse.ArgumentParser(description="Workflow Event Simulator")
    parser.add_argument("--config", default="config/workflow_config.json", help="Path to workflow configuration file")
    parser.add_argument("--from", dest="from_component", required=True, help="Source component")
    parser.add_argument("--to", dest="to_component", required=True, help="Target component")
    parser.add_argument("--trigger", required=True, help="Trigger name")
    parser.add_argument("--event-type", default="document_change", 
                        choices=["document_change", "code_commit", "test_complete", "production_ready", "custom"],
                        help="Type of event to simulate")
    parser.add_argument("--source-file", help="Source file path for the event")
    parser.add_argument("--custom-data", help="JSON string with custom event data")
    
    args = parser.parse_args()
    
    simulate_event(
        args.config,
        args.from_component,
        args.to_component,
        args.trigger,
        args.event_type,
        args.source_file,
        args.custom_data
    )

if __name__ == "__main__":
    main() 