if(GetFocus()== GetDlgItem(IDC_EDIT_BIANHAO_INPUT) && Xsub2.permission !=1)
		{
			CString s,s1;
			GetDlgItem( IDC_EDIT_BIANHAO_INPUT )->GetWindowText(s);
			if(s.GetLength() == 8)
			{
				GetDlgItem( IDC_STATIC_BIANHAO_INFO )->SetWindowText("");
				GetDlgItem( IDC_STATIC_BIANHAO_INFO )->SetWindowText(s);
				GetDlgItem( IDC_EDIT_BIANHAO_INPUT )->SetWindowText("");
				Xsub2.nbr = s;
				if(Xsub2.cur_ng_ok_flag !=0)//有测试结果
				{
					//写入数据库
					CTime time = CTime::GetCurrentTime();
					CString m_strTime = time.Format("%Y-%m-%d");
					CString search_str;
					bool search_success_flag =false;
					SYSTEMTIME st;
					GetLocalTime(&st);
					Xsub2.dateYear = st.wYear;
					Xsub2.dateMonth = st.wMonth;
					Xsub2.dateDay = st.wDay;
					Xsub2.time = st.wHour*3600+st.wMinute*60+st.wSecond;
					
					if((Xsub2.nbr != Xsub2.pre_nbr) && (Xsub2.nbr != "00000000"))//两次产品编号不一样则可以写入
					{
						m_odbc.Open(NULL,false,false,_T(DATABASE_OPEN_SQL),true);
						if(m_odbc.IsOpen())
						{
							CString s;
							rs.m_pDatabase = &m_odbc;
							if(atoi(Xsub2.nbr) >= 60000000)
							{
								Xsub2.domain = "001";
								search_str.Format(_T("select MAX(xsub2_trnbr) from pub.xsub2_det where xsub2_date = '%s' and (xsub2_domain = '001')"),m_strTime);
							}
							else
							{
								Xsub2.domain = "003";
								search_str.Format(_T("select MAX(xsub2_trnbr) from pub.xsub2_det where xsub2_date = '%s' and (xsub2_domain = '003')"),m_strTime);
							}

							//先采用日期搜索，若日期未搜到，代表几台未开始测试，换成原最大搜索方式
							rs.Open(CRecordset::forwardOnly,search_str);//查找最大值 速度快
							if(rs.IsOpen())//
							{
								long temp=0;
								CString strVal;	
								while(!rs.IsEOF())
								{
									rs.GetFieldValue((short)0,strVal);
									if(strVal != "")
									{
										search_success_flag = true;
										Xsub2.trnbr = atol(strVal);
									}
									rs.MoveNext();
								}
								rs.Close();
				
							}
							//判断按日期搜索是否有找到，若没找到则采用最原始方式
							if(search_success_flag ==  false)
							{
								rs.Open(CRecordset::forwardOnly, _T("select MAX(xsub2_trnbr) from pub.xsub2_det"));//查找最大值  速度较慢
								if(rs.IsOpen())//
								{
									long temp=0;
									CString strVal;	
									while(!rs.IsEOF())
									{
										rs.GetFieldValue((short)0,strVal);
										Xsub2.trnbr = atol(strVal);
										rs.MoveNext();
									}
									rs.Close();
				
								}
							}
							++Xsub2.trnbr;//每一笔自加1 .需要重数据库中获取最大事务号
					/*		CString str;
							str.Format(_T("trnbr:%d "),Xsub2.trnbr);
							AfxMessageBox(str);*/
							//转换合成字符
							s.Format(_T("insert into pub.xsub2_det (xsub2_domain, xsub2_trnbr, xsub2_nbr, xsub2_op, xsub2_userid, xsub2_date, xsub2_time, xsub2_rslt, xsub2_rsn_code,xsub2__chr03, xsub2_param1, xsub2_param2) values('%s', %d, '%s', '%s', 'U%s', '%4d/%4d/%2d', '%d', '%d', '%s', '%s', '%.3f', '%.3f')"),
								Xsub2.domain, Xsub2.trnbr, Xsub2.nbr, Xsub2.op, Xsub2.userid, Xsub2.dateYear,Xsub2.dateMonth,Xsub2.dateDay, Xsub2.time, Xsub2.rslt, Xsub2.rsn_code,Xsub2.station, Xsub2.param1, Xsub2.param2);
							try
							{
								m_odbc.ExecuteSQL(s);
								Xsub2.pre_nbr = Xsub2.nbr;
								Xsub2.pre_ng_ok_flag = Xsub2.cur_ng_ok_flag;//保存上一笔
								//Xsub2.cur_ng_ok_flag =0;//不管数据库写入是否成功
								GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入成功");
								GetDlgItem( IDC_STATIC_BIANHAO_INFO )->SetWindowText("00000000");
								extern void RecordTestResult();
								RecordTestResult();
							}
							catch(CDBException *e)
							{    
								CString strsql;
								strsql.Format(_T("重新扫码，数据库交互异常：%s"),e->m_strError);
								AfxMessageBox(strsql);
								GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入失败");

							}
							m_odbc.Close();					
							
						}
						else 
						{
							GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入失败");
							AfxMessageBox("打开数据库失败！");
						}
					}
					//两次产品编号相同，但结果不同
					else if((Xsub2.nbr == Xsub2.pre_nbr) && (Xsub2.nbr !="00000000")  && (Xsub2.cur_ng_ok_flag != Xsub2.pre_ng_ok_flag))
					
					{
						m_odbc.Open(NULL,false,false,_T(DATABASE_OPEN_SQL),true);
						if(m_odbc.IsOpen())
						{
							CString s;
							rs.m_pDatabase = &m_odbc;
							if(atoi(Xsub2.nbr) >= 60000000)
							{
								Xsub2.domain = "001";
								search_str.Format(_T("select MAX(xsub2_trnbr) from pub.xsub2_det where xsub2_date = '%s' and (xsub2_domain = '001')"),m_strTime);
							}
							else
							{
								Xsub2.domain = "003";
								search_str.Format(_T("select MAX(xsub2_trnbr) from pub.xsub2_det where xsub2_date = '%s' and (xsub2_domain = '003')"),m_strTime);
							}

							//先采用日期搜索，若日期未搜到，代表几台未开始测试，换成原最大搜索方式
							rs.Open(CRecordset::forwardOnly,search_str);//查找最大值 速度快
							if(rs.IsOpen())//
							{
								long temp=0;
								CString strVal;	
								while(!rs.IsEOF())
								{
									rs.GetFieldValue((short)0,strVal);
									if(strVal != "")
									{
										search_success_flag = true;
										Xsub2.trnbr = atol(strVal);
									}
									rs.MoveNext();
								}
								rs.Close();
				
							}
							//判断按日期搜索是否有找到，若没找到则采用最原始方式
							if(search_success_flag ==  false)
							{
								rs.Open(CRecordset::forwardOnly, _T("select MAX(xsub2_trnbr) from pub.xsub2_det"));//查找最大值  速度较慢
								if(rs.IsOpen())//
								{
									long temp=0;
									CString strVal;	
									while(!rs.IsEOF())
									{
										rs.GetFieldValue((short)0,strVal);
										Xsub2.trnbr = atol(strVal);
										rs.MoveNext();
									}
									rs.Close();
				
								}
							}
							++Xsub2.trnbr;//每一笔自加1 .需要重数据库中获取最大事务号
							//转换合成字符
							s.Format(_T("insert into pub.xsub2_det (xsub2_domain, xsub2_trnbr, xsub2_nbr, xsub2_op, xsub2_userid, xsub2_date, xsub2_time, xsub2_rslt, xsub2_rsn_code, xsub2_param1, xsub2_param2) values('%s', %d, '%s', '%s', 'U%s', '%4d/%4d/%2d', '%d', '%d', '%s', '%.3f', '%.3f')"),
								Xsub2.domain, Xsub2.trnbr, Xsub2.nbr, Xsub2.op, Xsub2.userid, Xsub2.dateYear,Xsub2.dateMonth,Xsub2.dateDay, Xsub2.time, Xsub2.rslt, Xsub2.rsn_code, Xsub2.param1, Xsub2.param2);
							try
							{
								m_odbc.ExecuteSQL(s);
								Xsub2.pre_nbr = Xsub2.nbr;
								Xsub2.pre_ng_ok_flag = Xsub2.cur_ng_ok_flag;//保存上一笔
								GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入成功");
							}
							catch(CDBException *e)
							{    
								CString strsql;
								strsql.Format(_T("重新扫码，数据库交互异常：%s"),e->m_strError);
								AfxMessageBox(strsql);
								GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入失败");

							}
							m_odbc.Close();
							
						}
						else 
						{
							GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入失败");
							AfxMessageBox("打开数据库失败！");
						}
					}
					else
					{
						
						GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("写入失败");
						GetDlgItem(IDC_SET_TestResult)->SetWindowText("重复扫码");
						
					}
	
				}
				else
				{
					GetDlgItem(IDC_STATIC_WRITE_SQL)->SetWindowText("");
					GetDlgItem( IDC_STATIC_BIANHAO_INFO )->SetWindowText("00000000");
					GetDlgItem(IDC_SET_TestResult)->SetWindowText("未测试");
				}
				Xsub2.cur_ng_ok_flag =0;//不管数据库写入是否成功，测试一次只能写入一次
			}
		}
		
		
		
		
		void Login::OnBnClickedOk()
{
	int CheckUser = 0;
	GetDlgItemText(IDC_EDIT_USER, Xsub6.userid);
	// TODO: 在此添加控件通知处理程序代码
	UpdateData(TRUE);
	if(Xsub6.userid=="")
	{
		AfxMessageBox("用户不能为空");
		return;
	}
	if(Xsub6.userid=="CSPC-YAPHA")
	{
		CheckUser = 1;
		Xsub6.supersuser =1;
	}
	if(Xsub6.supersuser != 1)
	{
		m_odbc.Open(NULL,false,false,_T(DATABASE_OPEN_SQL),true);
		if(m_odbc.IsOpen())
		{
			rs.m_pDatabase = &m_odbc;
			CString s;
			s.Format(_T("select xuser_id from pub.xuser_det where xuser_id = '%s'"),Xsub6.userid);//查找用户名=特定值的地方
			rs.Open(CRecordset::forwardOnly,_T(s));
			if(rs.IsOpen())
			{
				CString strVal;		
				while(!rs.IsEOF())
				{
					rs.GetFieldValue("xuser_id",strVal);
					if(Xsub6.userid == strVal)
					{
						CheckUser = 1;
					}
					rs.MoveNext();
				}
			}
			m_odbc.Close();
		}
	}
	
	if(CheckUser == 1)
	{
		//m_odbc.Open(NULL,false,false,_T(DATABASE_OPEN_SQL),true);
		//if(m_odbc.IsOpen())
		//{
		//	rs.m_pDatabase = &m_odbc;
		//	rs.Open(CRecordset::forwardOnly, _T("select MAX(Xsub6_trnbr) from pub.Xsub6_det"));//查找最大值
		//	if(rs.IsOpen())
		//	{
		//		long temp=0;
		//		CString strVal;	
		//		while(!rs.IsEOF())
		//		{
		//			rs.GetFieldValue((short)0,strVal);
		//			Xsub6.trnbr = atol(strVal);
		//			rs.MoveNext();
		//		}
		//	}
		//	m_odbc.Close();
		//}
		CDialogEx::OnOK();
	}
	else
	{
		AfxMessageBox("用户不存在");
		return;
	}
}
		
		
		