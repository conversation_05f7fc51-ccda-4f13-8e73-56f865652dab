#!/usr/bin/env python3
"""
Canvas自动监控器 - 增强版

监控Canvas文件的变化，当检测到edges变更时自动同步到INDEX文档。
基于文件系统监控，提供实时的双向同步功能。

新增功能：
- 批处理优化：合并短时间内的多次变更
- 配置文件支持：支持YAML配置文件
- 性能统计：详细的同步性能和错误统计
- 智能重试：指数退避重试机制
- 健康检查：监控服务健康状态
"""

import time
import json
import yaml
import hashlib
import argparse
import signal
import threading
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import logging
from logging.handlers import RotatingFileHandler

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("错误: 请安装watchdog库")
    print("运行: pip install watchdog")
    exit(1)

# 导入公共库
import sys
sys.path.append(str(Path(__file__).parent.parent))

try:
    from common import IndexScanner, IndexManager
    from canvas.canvas_manager import CanvasManager
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    print("请确保项目结构正确并安装所需依赖")
    exit(1)


@dataclass
class WatcherConfig:
    """监控器配置"""
    watch_delay: float = 0.5
    sync_interval: int = 5
    max_retries: int = 3
    retry_delay: float = 1.0
    batch_timeout: int = 30
    log_level: str = "INFO"
    max_log_size: str = "10MB"
    backup_count: int = 5
    health_check_interval: int = 60
    stats_save_interval: int = 300  # 5分钟


@dataclass
class SyncStats:
    """同步统计信息"""
    total_syncs: int = 0
    successful_syncs: int = 0
    failed_syncs: int = 0
    total_duration: float = 0.0
    last_sync_time: Optional[str] = None
    last_error: Optional[str] = None
    error_count: int = 0
    avg_duration: float = 0.0
    uptime_start: Optional[str] = None
    
    def update_success(self, duration: float):
        """更新成功统计"""
        self.total_syncs += 1
        self.successful_syncs += 1
        self.total_duration += duration
        self.last_sync_time = datetime.now().isoformat()
        self.avg_duration = self.total_duration / self.successful_syncs if self.successful_syncs > 0 else 0.0
    
    def update_error(self, error_msg: str):
        """更新错误统计"""
        self.total_syncs += 1
        self.failed_syncs += 1
        self.error_count += 1
        self.last_error = error_msg


class CanvasFileHandler(FileSystemEventHandler):
    """Canvas文件变化处理器 - 增强版"""
    
    def __init__(self, project_path: Path, sync_callback, config: WatcherConfig):
        """
        初始化处理器
        
        Args:
            project_path: 项目根路径
            sync_callback: 同步回调函数
            config: 监控配置
        """
        self.project_path = project_path
        self.sync_callback = sync_callback
        self.config = config
        self.canvas_file = project_path / "product.canvas"
        self.last_edges_hash = self._get_edges_hash()
        
        # 批处理相关
        self.pending_changes = deque()
        self.last_change_time = None
        self.batch_timer = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def on_modified(self, event):
        """文件修改事件处理"""
        if event.is_directory:
            return
            
        # 检查是否是Canvas文件
        if Path(event.src_path).name == "product.canvas":
            self._queue_change()
    
    def _queue_change(self):
        """将变更加入批处理队列"""
        current_time = datetime.now()
        self.pending_changes.append(current_time)
        self.last_change_time = current_time
        
        # 启动或重置批处理定时器
        if self.batch_timer:
            self.batch_timer.cancel()
        
        self.batch_timer = threading.Timer(self.config.batch_timeout, self._process_batched_changes)
        self.batch_timer.start()
        
        self.logger.debug(f"变更已加入队列，当前队列长度: {len(self.pending_changes)}")
    
    def _process_batched_changes(self):
        """处理批量变更"""
        if not self.pending_changes:
            return
        
        change_count = len(self.pending_changes)
        self.pending_changes.clear()
        
        self.logger.info(f"处理批量变更，共 {change_count} 个变更事件")
        self._handle_canvas_change()
    
    def _handle_canvas_change(self):
        """处理Canvas文件变化"""
        try:
            # 等待文件写入完成
            time.sleep(self.config.watch_delay)
            
            # 检查edges是否真的发生了变化
            current_edges_hash = self._get_edges_hash()
            if current_edges_hash != self.last_edges_hash:
                self.logger.info("检测到Canvas edges变化")
                
                # 执行同步
                success = self._retry_sync()
                if success:
                    self.last_edges_hash = current_edges_hash
                    self.logger.info("✓ Canvas edges已同步到INDEX")
                else:
                    self.logger.error("✗ Canvas edges同步失败")
            else:
                self.logger.debug("Canvas edges未发生实际变化")
                    
        except Exception as e:
            self.logger.error(f"处理Canvas变化时出错: {e}")
    
    def _retry_sync(self) -> bool:
        """带重试机制的同步"""
        for attempt in range(self.config.max_retries):
            try:
                if attempt > 0:
                    delay = self.config.retry_delay * (2 ** (attempt - 1))  # 指数退避
                    self.logger.info(f"同步重试 {attempt + 1}/{self.config.max_retries}，等待 {delay:.1f}秒")
                    time.sleep(delay)
                
                success = self.sync_callback()
                if success:
                    if attempt > 0:
                        self.logger.info(f"同步在第 {attempt + 1} 次尝试时成功")
                    return True
                    
            except Exception as e:
                self.logger.error(f"同步尝试 {attempt + 1} 失败: {e}")
                
        self.logger.error(f"同步在 {self.config.max_retries} 次尝试后仍然失败")
        return False
    
    def _get_edges_hash(self) -> Optional[str]:
        """获取当前Canvas edges的哈希值"""
        try:
            if not self.canvas_file.exists():
                return None
                
            with open(self.canvas_file, 'r', encoding='utf-8') as f:
                canvas_data = json.load(f)
                
            edges = canvas_data.get('edges', [])
            
            # 创建edges的规范化字符串（排序确保一致性）
            edges_str = json.dumps(edges, sort_keys=True)
            
            # 计算哈希
            return hashlib.md5(edges_str.encode()).hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算edges哈希时出错: {e}")
            return None


class CanvasWatcher:
    """Canvas自动监控器 - 增强版"""
    
    def __init__(self, project_path: Path, config_file: Optional[Path] = None):
        """
        初始化监控器
        
        Args:
            project_path: 项目根路径
            config_file: 配置文件路径
        """
        self.project_path = project_path
        self.canvas_file = project_path / "product.canvas"
        
        # 加载配置
        self.config = self._load_config(config_file)
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        try:
            self.canvas_manager = CanvasManager(project_path)
            self.index_scanner = IndexScanner(project_path)
            self.index_manager = IndexManager(project_path)
        except Exception as e:
            self.logger.error(f"初始化组件失败: {e}")
            raise
        
        # 创建文件系统监控器
        self.observer = Observer()
        self.handler = CanvasFileHandler(project_path, self._sync_canvas_to_index, self.config)
        
        # 同步统计
        self.stats = SyncStats()
        self.stats.uptime_start = datetime.now().isoformat()
        self._load_stats()
        
        # 控制标志
        self.running = False
        self.health_check_timer = None
        self.stats_save_timer = None
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _load_config(self, config_file: Optional[Path]) -> WatcherConfig:
        """加载配置文件"""
        config = WatcherConfig()
        
        if config_file and config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    
                watcher_config = config_data.get('watcher', {})
                for key, value in watcher_config.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
                        
                print(f"已加载配置文件: {config_file}")
                
            except Exception as e:
                print(f"警告: 加载配置文件失败，使用默认配置: {e}")
        
        # 从环境变量覆盖配置
        import os
        env_mappings = {
            'CANVAS_WATCH_DELAY': 'watch_delay',
            'CANVAS_SYNC_INTERVAL': 'sync_interval',
            'CANVAS_MAX_RETRIES': 'max_retries',
            'CANVAS_LOG_LEVEL': 'log_level',
        }
        
        for env_var, attr_name in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                try:
                    if attr_name in ['watch_delay', 'retry_delay']:
                        setattr(config, attr_name, float(env_value))
                    elif attr_name in ['sync_interval', 'max_retries', 'batch_timeout']:
                        setattr(config, attr_name, int(env_value))
                    else:
                        setattr(config, attr_name, env_value)
                except ValueError:
                    print(f"警告: 环境变量 {env_var} 值无效: {env_value}")
        
        return config
    
    def _setup_logging(self):
        """设置日志系统"""
        log_dir = self.project_path / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 解析日志文件大小
        max_bytes = self._parse_size(self.config.max_log_size)
        
        # 配置根日志记录器
        logger = logging.getLogger()
        logger.setLevel(getattr(logging, self.config.log_level.upper()))
        
        # 清除现有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            log_dir / "canvas_watcher.log",
            maxBytes=max_bytes,
            backupCount=self.config.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        )
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串（如 '10MB'）"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def start_watching(self):
        """开始监控Canvas文件"""
        if not self.canvas_file.exists():
            self.logger.error(f"Canvas文件不存在: {self.canvas_file}")
            return False
        
        self.running = True
        self.logger.info(f"开始监控Canvas文件: {self.canvas_file}")
        self.logger.info("当Canvas中的连线发生变化时，将自动同步到INDEX文档")
        self.logger.info("按 Ctrl+C 停止监控")
        
        # 设置监控
        self.observer.schedule(self.handler, str(self.project_path), recursive=False)
        self.observer.start()
        
        # 启动健康检查
        self._start_health_check()
        
        # 启动统计保存
        self._start_stats_timer()
        
        try:
            # 显示初始状态
            self._show_initial_status()
            
            # 保持运行
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，正在关闭...")
        finally:
            self._cleanup()
        
        return True
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备停止监控器")
        self.running = False
    
    def _cleanup(self):
        """清理资源"""
        self.logger.info("正在清理资源...")
        
        # 停止定时器
        if self.health_check_timer:
            self.health_check_timer.cancel()
        if self.stats_save_timer:
            self.stats_save_timer.cancel()
        
        # 停止文件监控
        self.observer.stop()
        self.observer.join()
        
        # 保存最终统计
        self._save_stats()
        
        self.logger.info("监控器已停止")
    
    def _start_health_check(self):
        """启动健康检查"""
        def health_check():
            if self.running:
                self._perform_health_check()
                self.health_check_timer = threading.Timer(
                    self.config.health_check_interval, 
                    health_check
                )
                self.health_check_timer.start()
        
        health_check()
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            # 检查Canvas文件
            if not self.canvas_file.exists():
                self.logger.warning("健康检查: Canvas文件不存在")
                return
            
            # 检查文件可读性
            with open(self.canvas_file, 'r', encoding='utf-8') as f:
                json.load(f)
            
            # 检查内存使用
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > 500:  # 超过500MB警告
                self.logger.warning(f"健康检查: 内存使用较高 {memory_mb:.1f}MB")
            
            self.logger.debug(f"健康检查通过，内存使用: {memory_mb:.1f}MB")
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
    
    def _start_stats_timer(self):
        """启动统计保存定时器"""
        def save_stats():
            if self.running:
                self._save_stats()
                self.stats_save_timer = threading.Timer(
                    self.config.stats_save_interval,
                    save_stats
                )
                self.stats_save_timer.start()
        
        save_stats()
    
    def _load_stats(self):
        """加载统计数据"""
        stats_file = self.project_path / "logs" / "canvas_watcher_stats.json"
        if stats_file.exists():
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats_data = json.load(f)
                
                # 更新统计对象
                for key, value in stats_data.items():
                    if hasattr(self.stats, key):
                        setattr(self.stats, key, value)
                
                self.logger.debug("已加载历史统计数据")
                
            except Exception as e:
                self.logger.warning(f"加载统计数据失败: {e}")
    
    def _save_stats(self):
        """保存统计数据"""
        try:
            stats_file = self.project_path / "logs" / "canvas_watcher_stats.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.stats), f, indent=2, ensure_ascii=False)
            
            self.logger.debug("统计数据已保存")
            
        except Exception as e:
            self.logger.error(f"保存统计数据失败: {e}")
    
    def _sync_canvas_to_index(self) -> bool:
        """
        从Canvas同步edges到INDEX文档
        
        Returns:
            bool: 同步成功返回True
        """
        start_time = time.time()
        
        try:
            # 提取Canvas中的关联关系
            associations = self.canvas_manager.extract_edges_to_associations()
            
            if not associations:
                self.logger.debug("Canvas中未发现关联关系")
                return True
            
            self.logger.info(f"发现 {len(associations)} 个关联关系")
            
            # 按文档ID组织关联关系
            doc_associations = self._organize_associations(associations)
            
            # 更新INDEX文件
            success_count = self._update_index_files(doc_associations)
            
            # 更新统计
            duration = time.time() - start_time
            self.stats.update_success(duration)
            
            self.logger.info(f"同步完成: {success_count}/{len(doc_associations)} 个文档更新成功 (耗时: {duration:.2f}s)")
            return success_count > 0
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"同步过程中出错 (耗时: {duration:.2f}s): {e}"
            self.stats.update_error(error_msg)
            self.logger.error(error_msg)
            return False
    
    def _organize_associations(self, associations: List[Dict]) -> Dict[str, List[Dict]]:
        """组织关联关系按文档ID分组"""
        doc_associations = {}
        
        for assoc in associations:
            from_doc_id = assoc["from_doc_id"]
            to_doc_id = assoc["to_doc_id"]
            
            if from_doc_id not in doc_associations:
                doc_associations[from_doc_id] = []
                
            doc_associations[from_doc_id].append({
                "target_doc_id": to_doc_id,
                "relation_type": assoc.get("relation_type", "related"),
                "source": "canvas_auto",
                "sync_time": self.last_sync_time or datetime.now()
            })
        
        return doc_associations
    
    def _update_index_files(self, doc_associations: Dict[str, List[Dict]]) -> int:
        """更新INDEX文件中的关联信息"""
        success_count = 0
        
        for doc_id, relations in doc_associations.items():
            try:
                # 查找文档所属组件
                component = self._find_document_component(doc_id)
                if not component:
                    print(f"    警告: 无法找到文档 {doc_id} 的组件")
                    continue
                
                # 更新关联关系
                success = self._update_document_associations(component, doc_id, relations)
                if success:
                    success_count += 1
                    print(f"    ✓ 更新 {component}.{doc_id}: {len(relations)} 个关联")
                else:
                    print(f"    ✗ 更新 {component}.{doc_id} 失败")
                    
            except Exception as e:
                print(f"    处理文档 {doc_id} 时出错: {e}")
        
        return success_count
    
    def _find_document_component(self, doc_id: str) -> Optional[str]:
        """根据文档ID查找所属组件"""
        index_files = self.index_scanner.find_index_files()
        
        for component, index_path in index_files.items():
            try:
                documents = self.index_scanner.parse_index_file(index_path)
                for doc in documents:
                    if doc.get('doc_id') == doc_id:
                        return component
            except Exception as e:
                print(f"    扫描 {component} INDEX时出错: {e}")
                continue
        
        return None
    
    def _update_document_associations(self, component: str, doc_id: str, relations: List[Dict]) -> bool:
        """更新文档的关联关系到INDEX文件"""
        try:
            # 获取INDEX文件路径
            index_files = self.index_scanner.find_index_files()
            if component not in index_files:
                return False
            
            index_path = index_files[component]
            
            # 读取现有文档
            documents = self.index_scanner.parse_index_file(index_path)
            
            # 找到目标文档并更新关联
            for doc in documents:
                if doc.get('doc_id') == doc_id:
                    # 合并新的关联关系（避免重复）
                    existing_relations = doc.get('relations', [])
                    
                    # 创建现有关联的查找集合
                    existing_targets = {rel.get('target_doc_id') for rel in existing_relations if rel.get('source') == 'canvas_auto'}
                    
                    # 添加新的关联（避免重复）
                    new_relations = []
                    for rel in relations:
                        if rel['target_doc_id'] not in existing_targets:
                            new_relations.append(rel)
                    
                    if new_relations:
                        # 移除旧的canvas_auto关联，添加新的
                        filtered_relations = [rel for rel in existing_relations if rel.get('source') != 'canvas_auto']
                        doc['relations'] = filtered_relations + relations
                        doc['last_update'] = datetime.now().strftime("%Y-%m-%d")
                    
                    break
            
            # 重建INDEX文件
            return self.index_manager.rebuild_index_file(component, documents, "document")
            
        except Exception as e:
            print(f"      更新INDEX文件时出错: {e}")
            return False
    
    def _show_initial_status(self):
        """显示初始状态信息"""
        try:
            # 读取Canvas状态
            if self.canvas_file.exists():
                with open(self.canvas_file, 'r', encoding='utf-8') as f:
                    canvas_data = json.load(f)
                
                nodes_count = len(canvas_data.get('nodes', []))
                edges_count = len(canvas_data.get('edges', []))
                
                print(f"\n当前Canvas状态:")
                print(f"  节点数量: {nodes_count}")
                print(f"  连线数量: {edges_count}")
            
            # 显示INDEX文件状态
            index_files = self.index_scanner.find_index_files()
            print(f"\n监控的INDEX文件: {len(index_files)} 个")
            for component in index_files.keys():
                print(f"  - {component}")
            
            print(f"\n监控开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 50)
            
        except Exception as e:
            print(f"显示初始状态时出错: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Canvas自动监控器")
    parser.add_argument("--project-path", type=str, default=".", 
                       help="项目根路径 (默认: 当前目录)")
    parser.add_argument("--stats", action="store_true",
                       help="显示监控统计信息")
    
    args = parser.parse_args()
    
    # 解析项目路径
    project_path = Path(args.project_path).resolve()
    
    if not project_path.exists():
        print(f"错误: 项目路径不存在: {project_path}")
        return False
    
    # 创建监控器
    watcher = CanvasWatcher(project_path)
    
    if args.stats:
        # 显示统计信息
        stats = watcher.get_sync_stats()
        print("Canvas监控统计:")
        print(f"  同步次数: {stats['sync_count']}")
        print(f"  最后同步: {stats['last_sync_time'] or '未同步'}")
        print(f"  Canvas文件: {stats['canvas_file']}")
        return True
    
    # 开始监控
    try:
        success = watcher.start_watching()
        return success
    except Exception as e:
        print(f"监控过程中出错: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 