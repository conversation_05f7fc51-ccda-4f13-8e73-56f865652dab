#include "comCheckOperation.h"
#include <QDebug>
#include <QMessageBox>
#include <QTimer>
#include <QTimerEvent>


#include "loadXml.h"
#include "saveExcel.h"
#include "uart.h"


#include "elecTestB.h"
#include "motorTestB.h"
#include "transferB.h"

#include "pmsA1.h"
#include "pmsB1.h"

#include "bCoinD4.h"
#include "bCoinT4.h"
#include "bMINIYJ.h"

#include "sensorCoinD.h"
#include "sensorMiniYj.h"

#define TIMER_MS       5
#define MAIN_UART_BAUD 230400
//#define DEVICE_UART_BAUD    115200

CComCheckOpt::CComCheckOpt(const NComCheck::StUiConfig &st_config)
    : mi_load_(new CLoadXml),
      mst_config_(&st_config)  //
      ,
      m_timerId(0),
      m_port_update_cnt(0)
      //  , mc_processList_(new TClen_process)
      //  , mst_task_status_(new TClen_process::StStepRecord)
      ,
      mi_icomm_(new CUART("", MAIN_UART_BAUD))  //默认配置
      ,
      mi_test_board_(new CMotorTestB(mi_icomm_)),
      mi_spms_(new CPmsA1(mi_icomm_, nullptr)),
      mi_bottom_board_(new CBcoinD4(mi_icomm_)),
      mi_top_board_(new CSensorCoinD(mi_icomm_))  //设备端口
      ,
      mst_comm_status_(new StCommunicateStatus),
      mst_cur_cmd_(new StCommCmd),
      mst_result_(new StResult),
      m_message_box_(new QWidget),
      mi_save_file_(new CSaveExcel) {
    //* timer
    m_timerId = startTimer(TIMER_MS);


    //* 1.子线程（初始化就创建子线程？）
    qDebug() << "main thread id: " << QThread::currentThread();
    m_sub_thread_ = new QThread;  // 创建线程对象

    /* 1.1 创建工作的类对象，千万不要指定给创建的对象指定父对象
     * 如果指定了: QObject::moveToThread: Cannot move objects with a parent
     */
    m_test_board_thread_ = new CTestBoardSerial(nullptr, mi_test_board_);
    m_test_board_thread_->moveToThread(m_sub_thread_);  //将工作的类对象移动到创建的子线程对象中
    m_spms_thread_ = new CSpmsSerial(nullptr, mi_spms_);
    m_spms_thread_->moveToThread(m_sub_thread_);

    //* 1.2 connect
    connect(dynamic_cast<CMotorTestB *>(mi_test_board_),
            &CMotorTestB::dataOutput,  //无法用多态实现
            this,
            &CComCheckOpt::dataReceive);
    connect(this, &CComCheckOpt::subThreadSignal, m_test_board_thread_, &CTestBoardSerial::loop);
    connect(m_sub_thread_, &QThread::finished, m_test_board_thread_, &QObject::deleteLater);

    //* 1.3 启动线程
    m_sub_thread_->start();

    //* 2.task list init
    //    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::readyStep, &CMotorMonitorOperation::readyAck, 400, 0));
    //    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::startStep, &CMotorMonitorOperation::startAck, 0, 0));
    //    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::dataStep, &CMotorMonitorOperation::dataAck, 0, 0));
    //    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::compStep, &CMotorMonitorOperation::compAck, 0, 0));

    //* 3.配置


    //* 3.数据库
    mm_result_data.clear();
    mm_result_data.insert("time", "");  //无法排序
    mm_result_data.insert("device", "");
    mm_result_data.insert("send_cmd", "");
    mm_result_data.insert("ack_cmd", "");
    mm_result_data.insert("result", "");

    QString desk_path = QStandardPaths::writableLocation(QStandardPaths::DesktopLocation);
    mi_save_file_->createOneFile(desk_path, "/communication_check", mm_result_data);
}

CComCheckOpt::~CComCheckOpt() {
    delete mi_load_;


    //* devices
    if (mi_test_board_ != nullptr)
        delete mi_test_board_;
    if (mi_spms_ != nullptr)
        delete mi_spms_;
    if (mi_bottom_board_ != nullptr)
        delete mi_bottom_board_;
    if (mi_top_board_ != nullptr)
        delete mi_top_board_;

    //* port
    if (mi_icomm_ != nullptr)
        delete mi_icomm_;

    //* communicate status
    delete mst_comm_status_;

    //*
    delete mst_cur_cmd_;

    //*
    delete mst_result_;
    delete m_message_box_;
    delete mi_save_file_;
}

void CComCheckOpt::timerEvent(QTimerEvent *event) {
    //  Q_UNUSED(event);

    if (m_timerId == event->timerId()) {
        //* 非任务task
        m_port_update_cnt++;
        if (m_port_update_cnt == 400) {
            m_port_update_cnt = 0;
            portlistUpdate();
        }

        //**************************** 业务 tasks

        //* 状态界面显示更新
    }
}


void CComCheckOpt::varibleInit() {
    ;
}

/**
 * @brief CLenAdjustOpt::portlistUpdate
 * @return
 */
bool CComCheckOpt::portlistUpdate(void) {
    QStringList *port_list_ = new QStringList;

    bool port_flag = mi_icomm_->scanPort(port_list_, mst_config_->cur_port_name);  //列表名称变化
    emit portUpdateSignal(port_list_, port_flag);

    return true;
}

QMap<QString, QByteArray> *CComCheckOpt::getCmdList(const int16_t &proj_indexj) {
    switch ((EProject)proj_indexj) {
    case EProject::eMINI_YJ_BOTTOM:
        break;
    case EProject::eMINI_SAM_BOTTOM:
        break;
    case EProject::eCOIN_D4_SENSOR:
        break;
    case EProject::eCOIN_D4_BOTTOM:
        break;
    case EProject::eMINI_T4_SNESOR:
        break;
    case EProject::eMINI_T4_BOTTOM:
        break;
    case EProject::eNOVA_A1:
        if (mi_spms_ != nullptr)
            delete mi_spms_;
        if (mi_icomm_ != nullptr)
            delete mi_icomm_;
        mi_icomm_ = new CUART("", 9600);
        mi_spms_  = new CPmsA1(mi_icomm_, nullptr);
        return mi_spms_->getCmdList();
        break;
    case EProject::eNOVA_A1B:
        break;
    default:
        break;
    }
}

bool CComCheckOpt::writeCmdList(const int16_t &proj_index, const QMap<QString, QByteArray> &bytes) {
    switch ((EProject)proj_index) {
    case EProject::eMINI_YJ_BOTTOM:
        break;
    case EProject::eMINI_SAM_BOTTOM:
        break;
    case EProject::eCOIN_D4_SENSOR:
        break;
    case EProject::eCOIN_D4_BOTTOM:
        break;
    case EProject::eMINI_T4_SNESOR:
        break;
    case EProject::eMINI_T4_BOTTOM:
        break;
    case EProject::eNOVA_A1:
        return mi_spms_->writeCmdList(bytes);
        break;
    case EProject::eNOVA_A1B:
        break;
    default:
        break;
    }
}

void CComCheckOpt::cmdModifyUpdate_slot(const uint8_t index, QByteArray cmd) {
}


void CComCheckOpt::dataReceive(ITestBoard::ECommStep step, ECommStatus status, QByteArray bytes) {
    mst_comm_status_->comm_status = status;
    //    switch (step) {
    //    case ITestBoard::ECommStep::eSTART_STEP:
    //        mv_task_list[eSTART_STEP].flag.stop = true; //
    //        break;
    //    case ITestBoard::ECommStep::eDATA_STEP:
    //        m_origin_data = bytes;
    //        mv_task_list[eDATA_STEP].flag.stop = true; //data ack
    //        break;
    //    case ITestBoard::ECommStep::eCOMPLETE_STEP:
    //        mv_task_list[eCOMPLETE_STEP].flag.stop = true; //comp ack
    //        break;
    //    default:
    //        break;
    //    }
}
