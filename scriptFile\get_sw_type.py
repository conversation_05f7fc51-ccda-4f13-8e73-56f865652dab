'''
Author: 
Date: 
LastEditors: 
LastEditTime: 
Description: 从config.ini中获取软件生成类型
FilePath: ..\\scriptFile\\get_sw_type.py
'''
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import sys
import configparser


def read_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["fw_type"]
    missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    
    return {
        "fw_type": config.get("PROJECT_VER", "fw_type")
    }

def write_config(config_file):
    config = configparser.ConfigParser()
    config.read(config_file)

    required_options = ["fw_type"]
    missing_options = [opt for opt in required_options if not config.has_option("PROJECT_VER", opt)]
    
    if missing_options:
        raise ValueError(f"Missing required options in config file: {', '.join(missing_options)}")
    
    # 写入新的值到 fw_type
    config.set("PROJECT_VER", "fw_type", "test")
    
    # 将修改后的配置写回文件
    with open(config_file, 'w') as configfile:
        config.write(configfile)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python get_version_from_log.py <directory_path>")
        sys.exit(1)

    script_dir = os.path.dirname(os.path.abspath(__file__))
    # print(f"script_dir: {script_dir}")  # 添加调试信息
    project_dir = os.path.dirname(script_dir)
    config_path = os.path.join(script_dir, 'config.ini')
    # directory_path = sys.argv[1]
    # config_path = os.path.join(directory_path, 'scriptFile/config.ini')
    # print("config_path: ", config_path)

    # 读取
    config = read_config(config_path)
    sw_type = config["fw_type"]

    # 复位标志位
    write_config(config_path)
    print(sw_type)

