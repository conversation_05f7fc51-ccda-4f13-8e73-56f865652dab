#ifndef _VI_5300_H_
#define _VI_5300_H_

#include <QByteArray>
#include <QMap>

#include "ISpmsSoc.h"

class CSpmsVi5300:public ISpmsSoc{
public:
    CSpmsVi5300();
    ~CSpmsVi5300(){};

    //* calib param
    typedef struct {
        uint8_t xtalk_cal;
        uint16_t xtalk_peak;
        int16_t xtalk_tof;
    } VI5300_XTALK_Calib_Data;

    typedef struct VI5300_OFFSET_Calib_Data {
        int16_t offset_cal;
        int16_t ref_tof;
    } offset_datas;

    //*
//    enum class ECalibProcess {
//        eXTALK              = 0,
//        eREF_TOF            ,
//    };

    EExecStatus calibTask1() override;
    EExecStatus calibTask1Ack() override;
    EExecStatus calibTask2() override;
    EExecStatus calibTask2Ack() override;
    EExecStatus calibTask3() override;
    EExecStatus calibTask3Ack() override;
    EExecStatus calibTask4() override;
    EExecStatus calibTask4Ack() override;

protected:
    QString get_class_name() override;
};

#endif
