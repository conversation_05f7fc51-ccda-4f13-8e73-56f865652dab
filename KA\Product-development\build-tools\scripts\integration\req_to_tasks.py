#!/usr/bin/env python3
"""
需求转任务脚本 - 实现T09功能
T09: 项目计划制定 - 将需求转换为具体任务，生成任务列表、时间估算、依赖关系、分配建议
"""

import json
import argparse
import sys
import os
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime, timedelta
import uuid

def convert_requirements_to_tasks(matrix_file: str, output_path: str = "", task_format: str = "json") -> Dict[str, Any]:
    """
    将需求矩阵转换为任务列表
    
    Args:
        matrix_file: 需求矩阵文件路径
        output_path: 输出路径
        task_format: 任务格式 (json, yaml, excel)
    
    Returns:
        转换结果字典
    """
    try:
        # 读取需求矩阵
        if not os.path.exists(matrix_file):
            return {
                "success": False,
                "error": f"需求矩阵文件不存在: {matrix_file}",
                "matrix_file": matrix_file
            }
        
        with open(matrix_file, 'r', encoding='utf-8') as f:
            requirements = json.load(f)
        
        if not isinstance(requirements, list):
            return {
                "success": False,
                "error": "需求矩阵格式错误，应为需求列表",
                "matrix_file": matrix_file
            }
        
        # 转换需求为任务
        tasks = []
        task_dependencies = {}
        
        for req in requirements:
            req_id = req.get('requirement_id', f"REQ_{len(tasks)+1}")
            req_title = req.get('title', req.get('content', ''))
            req_type = req.get('type', 'undefined')
            req_priority = req.get('priority', 'medium')
            
            # 根据需求类型生成任务
            generated_tasks = generate_tasks_for_requirement(req_id, req_title, req_type, req_priority)
            
            for task in generated_tasks:
                task['requirement_id'] = req_id
                task['task_id'] = str(uuid.uuid4())[:8]
                tasks.append(task)
            
            # 处理依赖关系
            if req.get('dependencies'):
                task_dependencies[req_id] = req['dependencies']
        
        # 生成时间估算
        tasks_with_estimates = add_time_estimates(tasks)
        
        # 生成分配建议
        tasks_with_assignments = add_assignment_suggestions(tasks_with_estimates)
        
        # 处理任务依赖关系
        final_tasks = process_task_dependencies(tasks_with_assignments, task_dependencies)
        
        # 生成输出文件
        if not output_path:
            output_path = Path(matrix_file).parent / "generated_tasks"
        
        output_dir = Path(output_path)
        output_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存任务列表
        if task_format == "json":
            task_file = output_dir / f"tasks_{timestamp}.json"
            with open(task_file, 'w', encoding='utf-8') as f:
                json.dump(final_tasks, f, ensure_ascii=False, indent=2)
        elif task_format == "yaml":
            import yaml
            task_file = output_dir / f"tasks_{timestamp}.yaml"
            with open(task_file, 'w', encoding='utf-8') as f:
                yaml.dump(final_tasks, f, allow_unicode=True, default_flow_style=False)
        elif task_format == "excel":
            import pandas as pd
            task_file = output_dir / f"tasks_{timestamp}.xlsx"
            df = pd.DataFrame(final_tasks)
            df.to_excel(task_file, index=False)
        
        # 生成项目计划摘要
        project_summary = generate_project_summary(final_tasks, len(requirements))
        summary_file = output_dir / f"project_plan_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(project_summary, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "matrix_file": matrix_file,
            "output_path": str(output_dir),
            "timestamp": datetime.now().isoformat(),
            "task_statistics": {
                "total_requirements": len(requirements),
                "total_tasks": len(final_tasks),
                "tasks_per_requirement": len(final_tasks) / len(requirements) if requirements else 0
            },
            "generated_files": {
                "task_file": str(task_file),
                "summary_file": str(summary_file)
            },
            "time_estimates": {
                "total_hours": sum(task.get('estimated_hours', 0) for task in final_tasks),
                "total_days": sum(task.get('estimated_days', 0) for task in final_tasks),
                "critical_path_days": calculate_critical_path(final_tasks)
            },
            "assignment_distribution": get_assignment_distribution(final_tasks),
            "dependency_analysis": {
                "tasks_with_dependencies": len([t for t in final_tasks if t.get('dependencies')]),
                "total_dependencies": sum(len(t.get('dependencies', [])) for t in final_tasks)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"转换需求为任务时出错: {str(e)}",
            "matrix_file": matrix_file
        }

def generate_tasks_for_requirement(req_id: str, req_title: str, req_type: str, req_priority: str) -> List[Dict[str, Any]]:
    """为单个需求生成任务列表"""
    base_tasks = []
    
    # 根据需求类型生成不同的任务模板
    if req_type in ['functional', 'business']:
        base_tasks = [
            {"name": f"分析需求: {req_title}", "type": "analysis", "phase": "planning"},
            {"name": f"设计方案: {req_title}", "type": "design", "phase": "design"},
            {"name": f"开发实现: {req_title}", "type": "development", "phase": "implementation"},
            {"name": f"测试验证: {req_title}", "type": "testing", "phase": "testing"},
            {"name": f"文档编写: {req_title}", "type": "documentation", "phase": "documentation"}
        ]
    elif req_type in ['technical', 'non_functional']:
        base_tasks = [
            {"name": f"技术调研: {req_title}", "type": "research", "phase": "planning"},
            {"name": f"架构设计: {req_title}", "type": "architecture", "phase": "design"},
            {"name": f"技术实现: {req_title}", "type": "development", "phase": "implementation"},
            {"name": f"性能测试: {req_title}", "type": "testing", "phase": "testing"}
        ]
    else:
        base_tasks = [
            {"name": f"需求澄清: {req_title}", "type": "clarification", "phase": "planning"},
            {"name": f"方案制定: {req_title}", "type": "planning", "phase": "design"},
            {"name": f"执行实施: {req_title}", "type": "implementation", "phase": "implementation"}
        ]
    
    # 为每个任务添加基础属性
    for task in base_tasks:
        task.update({
            "priority": req_priority,
            "status": "not_started",
            "created_date": datetime.now().isoformat(),
            "description": f"针对需求 {req_id} 的 {task['type']} 任务"
        })
    
    return base_tasks

def add_time_estimates(tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """为任务添加时间估算"""
    time_mapping = {
        "analysis": {"hours": 8, "days": 1},
        "research": {"hours": 16, "days": 2},
        "design": {"hours": 16, "days": 2},
        "architecture": {"hours": 24, "days": 3},
        "development": {"hours": 40, "days": 5},
        "implementation": {"hours": 32, "days": 4},
        "testing": {"hours": 16, "days": 2},
        "documentation": {"hours": 8, "days": 1},
        "clarification": {"hours": 4, "days": 0.5},
        "planning": {"hours": 8, "days": 1}
    }
    
    for task in tasks:
        task_type = task.get('type', 'implementation')
        estimates = time_mapping.get(task_type, {"hours": 16, "days": 2})
        
        # 根据优先级调整估算
        priority_multiplier = {"high": 1.2, "medium": 1.0, "low": 0.8}.get(task.get('priority', 'medium'), 1.0)
        
        task['estimated_hours'] = int(estimates['hours'] * priority_multiplier)
        task['estimated_days'] = round(estimates['days'] * priority_multiplier, 1)
    
    return tasks

def add_assignment_suggestions(tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """为任务添加分配建议"""
    role_mapping = {
        "analysis": ["业务分析师", "产品经理"],
        "research": ["技术专家", "架构师"],
        "design": ["UI设计师", "产品设计师"],
        "architecture": ["系统架构师", "技术负责人"],
        "development": ["前端开发", "后端开发"],
        "implementation": ["开发工程师", "实施工程师"],
        "testing": ["测试工程师", "QA工程师"],
        "documentation": ["技术写作", "文档工程师"],
        "clarification": ["产品经理", "业务分析师"],
        "planning": ["项目经理", "产品经理"]
    }
    
    for task in tasks:
        task_type = task.get('type', 'implementation')
        suggested_roles = role_mapping.get(task_type, ["开发工程师"])
        
        task['suggested_assignees'] = suggested_roles
        task['required_skills'] = get_required_skills(task_type)
    
    return tasks

def get_required_skills(task_type: str) -> List[str]:
    """获取任务所需技能"""
    skills_mapping = {
        "analysis": ["需求分析", "业务理解", "沟通协调"],
        "research": ["技术调研", "方案评估", "文档编写"],
        "design": ["界面设计", "用户体验", "原型制作"],
        "architecture": ["系统设计", "技术架构", "性能优化"],
        "development": ["编程开发", "代码规范", "版本控制"],
        "implementation": ["系统实施", "配置管理", "问题解决"],
        "testing": ["测试设计", "缺陷跟踪", "质量保证"],
        "documentation": ["技术写作", "文档管理", "知识整理"]
    }
    
    return skills_mapping.get(task_type, ["通用技能"])

def process_task_dependencies(tasks: List[Dict[str, Any]], req_dependencies: Dict[str, List[str]]) -> List[Dict[str, Any]]:
    """处理任务依赖关系"""
    # 为每个任务添加依赖信息
    for task in tasks:
        req_id = task.get('requirement_id')
        if req_id in req_dependencies:
            task['dependencies'] = req_dependencies[req_id]
        else:
            task['dependencies'] = []
    
    return tasks

def calculate_critical_path(tasks: List[Dict[str, Any]]) -> float:
    """计算关键路径天数"""
    # 简化的关键路径计算
    max_days = max(task.get('estimated_days', 0) for task in tasks) if tasks else 0
    return max_days * 1.2  # 考虑依赖关系的缓冲

def get_assignment_distribution(tasks: List[Dict[str, Any]]) -> Dict[str, int]:
    """获取任务分配分布"""
    distribution = {}
    for task in tasks:
        for assignee in task.get('suggested_assignees', []):
            distribution[assignee] = distribution.get(assignee, 0) + 1
    return distribution

def generate_project_summary(tasks: List[Dict[str, Any]], total_requirements: int) -> Dict[str, Any]:
    """生成项目计划摘要"""
    return {
        "project_overview": {
            "total_requirements": total_requirements,
            "total_tasks": len(tasks),
            "estimated_duration_days": sum(task.get('estimated_days', 0) for task in tasks),
            "estimated_effort_hours": sum(task.get('estimated_hours', 0) for task in tasks)
        },
        "phase_breakdown": get_phase_breakdown(tasks),
        "resource_requirements": get_assignment_distribution(tasks),
        "timeline_milestones": generate_milestones(tasks),
        "risk_factors": [
            "任务间依赖关系可能导致延期",
            "资源分配需要根据实际团队调整",
            "需求变更可能影响任务计划",
            "技术难度评估需要专家确认"
        ]
    }

def get_phase_breakdown(tasks: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """获取阶段分解"""
    phases = {}
    for task in tasks:
        phase = task.get('phase', 'unknown')
        if phase not in phases:
            phases[phase] = {"task_count": 0, "estimated_days": 0, "estimated_hours": 0}
        
        phases[phase]["task_count"] += 1
        phases[phase]["estimated_days"] += task.get('estimated_days', 0)
        phases[phase]["estimated_hours"] += task.get('estimated_hours', 0)
    
    return phases

def generate_milestones(tasks: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """生成里程碑"""
    phases = ["planning", "design", "implementation", "testing", "documentation"]
    milestones = []
    
    current_date = datetime.now()
    for i, phase in enumerate(phases):
        phase_tasks = [t for t in tasks if t.get('phase') == phase]
        if phase_tasks:
            phase_days = sum(t.get('estimated_days', 0) for t in phase_tasks)
            milestone_date = current_date + timedelta(days=sum(
                sum(t.get('estimated_days', 0) for t in tasks if t.get('phase') == p)
                for p in phases[:i+1]
            ))
            
            milestones.append({
                "phase": phase,
                "milestone": f"{phase.title()} 阶段完成",
                "estimated_date": milestone_date.strftime('%Y-%m-%d'),
                "task_count": len(phase_tasks)
            })
    
    return milestones

def main():
    parser = argparse.ArgumentParser(description='需求转任务工具')
    parser.add_argument('--matrix-file', '-m', required=True, help='需求矩阵文件路径')
    parser.add_argument('--output-path', '-o', default='', help='输出路径')
    parser.add_argument('--task-format', '-f', default='json',
                       choices=['json', 'yaml', 'excel'],
                       help='任务格式')
    
    args = parser.parse_args()
    
    # 执行转换
    result = convert_requirements_to_tasks(args.matrix_file, args.output_path, args.task_format)
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 返回退出码
    sys.exit(0 if result.get('success', False) else 1)

if __name__ == "__main__":
    main()
