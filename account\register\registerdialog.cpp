#include "registerdialog.h"
#include "ui_registerdialog.h"
#include <qmessagebox.h>
#include <qsqlquery.h>
registerDialog::registerDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::registerDialog)
{
    ui->setupUi(this);
}

registerDialog::~registerDialog()
{
    delete ui;
}

void registerDialog::on_pButtonRegisterOK_clicked()
{
    QString user,pwd;
    user=ui->lEditRAccound->text(); //获取输入的用户名和密码
    pwd=ui->lEditRPword->text();
    if(user=="")    //判断用户名和密码是否为空
        QMessageBox::warning(this,"","用户名不能为空");
    else if (pwd=="")
        QMessageBox::warning(this,"","密码不能为空");
    else
    {
        QString i =QString("insert into user_table values(’%1’,’%2’);").arg(user).arg(pwd);//插入一条信息到数据库的user_table表中
        QString s =QString("select * from user_table where name = ‘%1’").arg(user);//在user表中查询是否有存在的用户名
        QSqlQuery query;
        query.exec(s);
        if(query.first())
        {
            QMessageBox::warning(this,"ERROR","用户名重复");//如果用户名重复，则提示用户名重复
        }
        else if (query.exec(i)) //如果用户名不重复，添加数据进入数据库
        {
            QMessageBox::information(this,"提示","注册成功！！",QMessageBox::Yes);
            accept();
        }
        else
            QMessageBox::warning(this,"ERROR","注册失败，请重试！！");
    }
}

void registerDialog::on_pButtonRegisterExit_clicked()
{
    this->close(); //显示主窗口
//    ->hide();//隐藏子窗口
}
