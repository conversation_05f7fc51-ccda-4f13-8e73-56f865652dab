#!/usr/bin/env python
# 脚本路径: scripts/requirements/init_requirements_matrix.py

import os
import json
import argparse

def create_matrix_template(level, level_name, output_path):
    """为指定层级创建需求矩阵模板"""
    if level == 1:  # 顶层模板
        template = f"""# {level_name}需求追溯矩阵

| 需求ID | 需求描述 | 状态 | 下层关联 |
|--------|---------|------|----------|
"""
    elif level == 4:  # 客户层模板
        template = f"""# {level_name}需求追溯矩阵

| 需求ID | 上层需求 | 需求描述 | 状态 | 测试标准 |
|--------|---------|---------|------|----------|
"""
    else:  # 中间层和产品层模板
        template = f"""# {level_name}需求追溯矩阵

| 需求ID | 上层需求 | 需求描述 | 状态 | 下层关联 |
|--------|---------|---------|------|----------|
"""
    
    # 创建目录（如果不存在）
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 写入模板文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(template)
    
    print(f"已创建需求矩阵模板: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='创建需求矩阵模板')
    parser.add_argument('--config', required=True, help='层级配置文件路径')
    args = parser.parse_args()
    
    # 读取配置文件
    with open(args.config, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 为每个层级实例创建需求矩阵
    for level in config['levels']:
        level_id = level['level_id']
        for instance in level['instances']:
            # 构建输出路径
            output_path = f"level_{level_id}_{instance}/requirements/requirements_matrix.md"
            create_matrix_template(level_id, instance, output_path)

if __name__ == "__main__":
    main() 