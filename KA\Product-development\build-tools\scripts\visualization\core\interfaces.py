#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化系统统一接口定义

定义了所有可视化组件需要遵循的标准接口
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from enum import Enum
from pathlib import Path

class VisualizationMode(Enum):
    """可视化模式枚举"""
    WORKFLOW = "workflow"
    DOCUMENTS = "documents"
    TRACEABILITY = "traceability"
    PROGRESS = "progress"
    STRUCTURE = "structure"
    ALL = "all"

@dataclass
class Node:
    """节点数据结构"""
    id: str
    name: str
    type: str
    component: str
    properties: Dict[str, Any]
    x: Optional[float] = None
    y: Optional[float] = None

@dataclass
class Edge:
    """边数据结构"""
    source: str
    target: str
    type: str
    properties: Dict[str, Any]

@dataclass
class VisualizationData:
    """可视化数据统一结构"""
    title: str
    mode: VisualizationMode
    nodes: List[Node]
    edges: List[Edge]
    metadata: Dict[str, Any]
    layout_config: Optional[Dict[str, Any]] = None

class DataAdapter(ABC):
    """数据适配器抽象基类"""
    
    @abstractmethod
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """从项目中提取指定模式的可视化数据"""
        pass

class VisualizationRenderer(ABC):
    """可视化渲染器抽象基类"""
    
    @abstractmethod
    def render(self, data: VisualizationData, **kwargs) -> str:
        """渲染可视化数据为指定格式"""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的渲染格式"""
        pass

class LayoutEngine(ABC):
    """布局引擎抽象基类"""
    
    @abstractmethod
    def apply_layout(self, data: VisualizationData) -> VisualizationData:
        """应用布局算法到可视化数据"""
        pass
    
    @abstractmethod
    def get_layout_config(self) -> Dict[str, Any]:
        """获取布局配置"""
        pass 