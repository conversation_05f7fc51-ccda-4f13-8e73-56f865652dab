# 产品体系MCP Server

基于Model Context Protocol (MCP) 标准的产品开发框架服务器集成方案，提供完整的产品开发生命周期管理功能。

## 🏗️ 架构概述

### MCP协议优势

Model Context Protocol (MCP) 是专为大语言模型设计的开放标准，具有以下优势：

- **AI原生设计**：专为LLM交互优化，支持自然语言操作
- **标准化协议**：开放标准，兼容多种AI开发环境
- **实时集成**：与AI工具深度集成，无需切换界面
- **上下文感知**：AI助手理解项目上下文，提供智能建议

### 市场mcp server

### 本地mcp server创建指南

封装 build-tools/scripts/ 目录下的脚本

[[mcp-server_create_guide.md]]

### 架构设计原则

1. **模块化分离**：按功能域划分独立MCP服务器
2. **统一集成**：提供完整功能的统一服务器
3. **标准兼容**：完全遵循MCP协议规范
4. **可扩展性**：支持自定义功能模块扩展

## 📁 MCP服务器架构

## 🚀 部署和使用

### 快速开始

1. **选择部署方式**

   **方式1：统一服务器（推荐）**

   ```json
   {
     "mcpServers": {
       "product-development-complete": {
         "command": "python",
         "args": ["F:/101_link-notebook/Obsidian-Vault/KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete/server.py"],
         "description": "产品开发完整功能集成MCP服务器"
       }
     }
   }
   ```

   **方式2：模块化部署**
   ```json
   {
     "mcpServers": {
       "project-init": {
         "command": "python",
         "args": ["./mcp-server_local_integrations/core/project-initialization/server.py"]
       },
       "config-mgmt": {
         "command": "python",
         "args": ["./mcp-server_local_integrations/core/configuration-management/server.py"]
       },
       "requirements": {
         "command": "python",
         "args": ["./mcp-server_local_integrations/core/requirements-management/server.py"]
       }
     }
   }
   ```

2. **安装依赖**
   ```bash
   pip install -r mcp-server_local_integrations/unified/product-development-complete/requirements.txt
   ```

3. **启动使用**
   - Cursor会自动启动MCP服务器
   - 通过AI助手自然语言交互
   - 无需记忆命令和参数

### 使用示例

#### 项目初始化
```
用户：请帮我创建一个名为"智能家居系统"的多层级项目

AI助手：我来帮您创建多层级项目结构...
[调用 init_project 工具]
✅ 项目初始化成功：智能家居系统
已创建标准的多层级目录结构，包含需求、设计、实现、验证、部署等阶段目录。
```

#### 配置管理
```
用户：请加载项目配置文件并检查是否有问题

AI助手：我来加载并验证配置文件...
[调用 load_config 和 validate_config 工具]
✅ 配置文件加载成功
⚠️ 发现2个警告：缺少数据库配置、日志级别未设置
```

#### 需求分析
```
用户：请导入requirements.xlsx文件并分析需求分布

AI助手：我来导入并分析需求文档...
[调用 import_requirements 和 analyze_requirements 工具]
✅ 需求导入成功：共45个需求
📊 分析结果：
- 功能需求：32个 (71%)
- 非功能需求：13个 (29%)
- 高优先级：15个，中优先级：20个，低优先级：10个
```

## 🔧 自建vs市场MCP服务器

### 自建MCP服务器

**位置**：`mcp-server_local_integrations/` 目录下的所有服务器

**特点**：
- 专为产品开发框架定制
- 与现有scripts深度集成
- 完全可控和可定制
- 支持本地化需求

**目录标识**：
```
mcp-server_local_integrations/
├── core/           # 自建核心功能服务器
├── development/    # 自建开发工具服务器
├── visualization/  # 自建可视化服务器
├── unified/        # 自建统一集成服务器
└── examples/       # 自建示例服务器
```

### 市场MCP服务器

**管理方式**：通过配置文件引用，不存储在本地目录

**推荐集成**：
```json
{
  "mcpServers": {
    // 自建服务器
    "product-development": {
      "command": "python",
      "args": ["./mcp-server_local_integrations/unified/product-development-complete/server.py"]
    },

    // 市场服务器
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"]
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"]
    },
    "web-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-web-search"]
    }
  }
}
```

### 集成策略

1. **核心功能**：使用自建MCP服务器，确保与产品开发框架完美集成
2. **通用功能**：使用市场成熟的MCP服务器，如文件系统、网络搜索等
3. **专业工具**：根据需要集成特定领域的市场服务器

## 📊 功能对比

| 功能类别 | 自建服务器 | 市场服务器 | 推荐策略 |
|----------|------------|------------|----------|
| 项目初始化 | ✅ 深度定制 | ❌ 通用性差 | 使用自建 |
| 配置管理 | ✅ 框架集成 | ⚠️ 需适配 | 使用自建 |
| 需求管理 | ✅ 专业化 | ❌ 功能有限 | 使用自建 |
| 文件操作 | ⚠️ 重复开发 | ✅ 成熟稳定 | 使用市场 |
| 网络搜索 | ❌ 开发成本高 | ✅ 功能完善 | 使用市场 |
| GitHub集成 | ⚠️ 维护成本 | ✅ 官方支持 | 使用市场 |

## 🛠️ 开发和维护

### 自建服务器开发规范

1. **目录结构标准**
   ```
   server-name/
   ├── server.py          # MCP服务器主文件
   ├── tools/             # 工具模块
   ├── prompts/           # 提示模板
   ├── docs/              # 文档
   ├── requirements.txt   # 依赖
   └── README.md         # 说明
   ```

2. **代码规范**
   - 使用FastMCP框架
   - 完整的类型注解
   - 详细的文档字符串
   - 错误处理和日志记录

3. **测试要求**
   - 单元测试覆盖率>90%
   - 集成测试验证
   - 性能测试基准

### 版本管理

- **自建服务器**：与产品开发框架同步版本
- **市场服务器**：定期更新，测试兼容性
- **配置管理**：版本化MCP配置文件

## 🔮 未来规划

### 短期目标（3个月）
- 完善所有核心功能MCP服务器
- 建立完整的测试体系
- 编写详细的使用文档

### 中期目标（6个月）
- 开发高级功能模块
- 集成更多市场MCP服务器
- 建立服务器注册和发现机制

### 长期目标（1年）
- 构建MCP服务器生态
- 支持插件化扩展
- 提供可视化管理界面

## 📚 相关资源

- [MCP协议规范](https://github.com/modelcontextprotocol)
- [FastMCP框架文档](https://github.com/modelcontextprotocol/python-sdk)
- [MCP服务器市场](https://github.com/modelcontextprotocol/servers)
- [产品开发框架文档](../README.md)