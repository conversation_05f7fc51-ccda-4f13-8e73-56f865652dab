#ifndef _LOAD_XML_H_
#define _LOAD_XML_H_

#include <ILoad.h>
#include <QtXml/QDomElement>

class CLoadXml:public ILoad
{
public:
    CLoadXml();
    ~CLoadXml();

    bool loadFile(const QString &filename) override;

    void writeXML(const QString &filename);
    void addXML(const QString &filename);
    void removeXML(const QString &filename);
    void updateXML(const QString &filename);

    void readParam(const QString &filename, QMap<QString, int> *xml_read) override;
    void readParam(const QString &filename, QMap<QString, QVariant> *xml_read) override;
    void readParam(const QString &filename, QMap<QString, QByteArray> *xml_read) override;
    void readParam(const QString &filename, QMap<QString, QString> *xml_read) override;
    bool readParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_read) override;
    bool writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QByteArray> *xml_write) override;
    bool writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_write) override;

private:
    QDomDocument m_doc; //node
};

#endif // PARAMLOAD_H
