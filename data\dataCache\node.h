#ifndef _NODE_H_
#define _NODE_H_

#include "stdint.h"



typedef struct _ListNode{
    uint16_t                            step_time; //单步时间
    int                                 val;
    void                                (* function)(void);

    _ListNode(int x) :
        val(x), next(nullptr){
    }

    _ListNode*                         next;
} StListNode;

class CNode{
public:
    CNode();
    ~CNode();

    void deleteNode(StListNode* head, const int &data);
    void insertNode(StListNode* head, const int &data);

};

#endif
