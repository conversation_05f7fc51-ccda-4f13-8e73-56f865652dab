#include "turnOnTime.h"
#include "ui_turnOnTime.h"
#include "mywidget.h"
#include <QSqlDatabase>
#include <qsqlquery.h>
#include "ILoad.h"
#include "loadXml.h"


const QString turnOnTime::m_red_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:red";
const QString turnOnTime::m_green_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:green";
const QString turnOnTime::m_grey_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:grey";
const QString turnOnTime::m_yellow_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:yellow";

#define MOTOR_SPEED_UP_NUM      50 //启动fg个数
#define MOTOR_SPEED_DOWN_NUM    2 //停止fg
#define MOTOR_ONE_PACK_NUM      256

turnOnTime::turnOnTime(QWidget *parent) :
    QDockWidget(parent)
  , ui(new Ui::turnOnTime)
  , m_status_flag_(new U_STATUS_FLAG)
  , m_timerId(0)
  , m_port_name("")
  , m_transBoard_name("")
  , m_function_select(1)
  , m_stStatus_rec_(new ST_STATUS_REC)
  , mst_config_(new StConfig)
  , m_bottom_comm_status(new ST_COMM_STATUS)
{
    ui->setupUi(this);

    /*1.0 界面初始化*/
    motorMonitorPlot = new motorMonitorCustPlot(*ui->motorPlot);
    ui->baudBox->setCurrentIndex(4); //230400
    ui->transBaudBox->setCurrentIndex(1); //115200
    ui->funcMode->setCurrentIndex(0); //启动时间监控
    ui->monitorMode->setCurrentIndex(0); //连续监控
    ui->protocolBox->setCurrentIndex(0); //协议-dt2

    ui->id->setValidator(new QRegExpValidator(QRegExp("[0-9]+$"))); //编号只能input 数字

    ui->id->setAlignment(Qt::AlignLeft);
    ui->resultStandard->setAlignment(Qt::AlignLeft);

    /*1.1 statusBar 初始化*/
    ui->startLamp->setStyleSheet(m_grey_SheetStyle); //改成
    ui->dataLamp->setStyleSheet(m_grey_SheetStyle);
    ui->adjustLamp->setStyleSheet(m_grey_SheetStyle);
    ui->compLamp->setStyleSheet(m_grey_SheetStyle);
    ui->startStep->setFont(QFont("黑体", 8, QFont::Medium));
    ui->dataStep->setFont(QFont("黑体", 8, QFont::Medium));
    ui->adjustStep->setFont(QFont("黑体", 8, QFont::Medium));
    ui->compStep->setFont(QFont("黑体", 8, QFont::Medium));

    /*1.2 variables init*/

    /*1.2.1 外部参数读取*/
    m_xml_param["monitor_time"] = 3;
    m_xml_param["monitor_cycle"] = 100;

    //QString filename = QFileDialog::getOpenFileName(this, tr("Open Xml"), ".", tr("Xml files (*.xml)"));
    QString filename = (QApplication::applicationDirPath() + "/config/speed_time_config.xml");
    CLoadXml::readParam(filename, &m_xml_param);

    mst_config_->monitor_limit_time = m_xml_param["monitor_time"];
    mst_config_->monitor_limit_cycle_num = m_xml_param["monitor_cycle"];

    //1.2.2 数据初始化
    m_timerId = startTimer(10);

    m_stStatus_rec_->step_time_limit = 0;

    /*1.2.3 注册*/


    /*2.2 任务集管理*/

    uint8_t num = 0;
    ST_LIST_NODE* head = new ST_LIST_NODE(num);
    ST_LIST_NODE* p = head;
//    processManage();

    /*3.0 数据库*/
}

turnOnTime::~turnOnTime()
{
    delete ui;
    delete m_status_flag_;
    delete mst_config_;
    delete mst_result_;
    delete m_stStatus_rec_;
    delete m_bottom_comm_status;
    if(mst_start_timer_data_ != nullptr)
        delete mst_start_timer_data_;
}

void turnOnTime::timerEvent(QTimerEvent *event)
{
    Q_UNUSED(event);
    //1.0
    if(m_timerId == event->timerId())
    {
        QStringList *port_list_ = new QStringList;
        /*1.1 显示*/

        /*1.2 刷新串口列表 */
        ++m_task_cnt->task_1500ms_cnt;
        if(m_task_cnt->task_1500ms_cnt == 150)//1.5s
        {
            m_task_cnt->task_1500ms_cnt = 0;
            port_list_->clear();
            *port_list_ = m_speedMonitorSerial_->detectPort(); //主线程？

            /*串口列表刷新*/
            ui->portBox->blockSignals(true);//必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错
            ui->portBox->clear(); //会触发currentIndexChanged回调函数
            ui->portBox->addItems(*port_list_); //会触发消息事件
            ui->portBox->blockSignals(false);

            ui->transPortBox->blockSignals(true);
            ui->transPortBox->clear(); //
            ui->transPortBox->addItems(*port_list_);
            //            ui->transPortBox->setCurrentText(m_transBoard_name);
            ui->transPortBox->blockSignals(false);
            uint8_t port_flag = false, device_port_flag = false;
            for(QList<QString>::Iterator it =  port_list_->begin(); it != port_list_->end(); ++it)
            {
                if((*it) == m_port_name)
                {
                    ui->portBox->setCurrentText(m_port_name);
                    port_flag = true;
                }
                if((*it) == m_transBoard_name) //设备端口
                {
                    ui->transPortBox->setCurrentText(m_transBoard_name);
                    device_port_flag = true;
                }
            }
            if(!port_flag)
            {
                /*串口丢失*/
                if(m_speedMonitorSerial_->m_serial_port->isOpen())
                {
                    qDebug()<<"-turnT: sensor port miss:" << m_port_name;
                    m_stStatus_rec_->cur_step = monitor_process_two::exit_step;
                    m_stStatus_rec_->cur_status = monitor_process_two::none;
                    //processLoop(m_stStatus_rec_->cur_step, m_stStatus_rec_->cur_status);
                }
            }
            if(!device_port_flag) //
            {
                /*串口丢失*/
                if(m_speedMonitorSerial_->m_transBoard_port->isOpen())
                {
                    qDebug()<<"-turnT: device port miss:" << m_transBoard_name;
                    m_stStatus_rec_->cur_step = monitor_process_two::exit_step;
                    m_stStatus_rec_->cur_status = monitor_process_two::none;
                    //processLoop(m_stStatus_rec_->cur_step, m_stStatus_rec_->cur_status);
                }
            }
        }
    }
}

void turnOnTime::closeEvent( QCloseEvent * event)
{
    Q_UNUSED(event);
//    setQuitAndLoop(true,false);
    //m_speedMonitorSerial_->task_id_change(0);
    speedMonitorSub_->quit();
    speedMonitorSub_->wait();
    delete speedMonitorSub_; //会析构 m_speedMonitorSerial_
    this->setAttribute(Qt::WA_DeleteOnClose); //释放窗口资源
    killTimer(m_timerId);
    QThread::msleep(50);
    emit turnOnTimeCloseSignal(true);
}

/*
 * @brief: 事件方式延迟固定ms，不影响UI响应
 * @param: ms
 */
inline void turnOnTime::threadDelayMs(const uint16_t &ms)
{
    QEventLoop eloop;
    QTimer::singleShot(ms, &eloop, &QEventLoop::quit);
    eloop.exec();
}



/*
 * @brief: 添加执行步骤
 * @param:
 */
void turnOnTime::process_register(const function_mode &mode, const uint16_t &stage)
{
    switch (mode) {
    case motor_monitor:

        break;
    case lidar_monitor:
        //if(stage == )
        break;
    default:
        break;
    };
}

void turnOnTime::process_loop(uint16_t &step, uint16_t &status)
{
    /*状态机更新*/
    QMetaEnum stepEnum = QMetaEnum::fromType<monitor_process_two::PROCESS_STEP>();
    QMetaEnum statusEnum = QMetaEnum::fromType<monitor_process_two::STEP_STATUS>();
    QString step_str = stepEnum.valueToKey(step);
    QString status_str = statusEnum.valueToKey(status);
    qDebug() << "-turnT: process step: " << step_str << "status: " << status_str;
//    if(monitor_process_two::ready_step == step )
//    {
//    }
//    else if(monitor_process_two::start_step == step)
//    {
//        /*2.wait device catch up mirror completed*/
//        statusBarUpdate(monitor_process_two::start_step, monitor_process_two::STEP_STATUS(status));


//        else if((status & monitor_process_two::error) == monitor_process_two::error) //ack check error
//        {
//            ++ m_bottom_comm_status->check_error_cnt;
//            qDebug() << "-turnT: start step error:" << m_bottom_comm_status->check_error_cnt;
//        }
//        else if((status & monitor_process_two::timeout) == monitor_process_two::timeout) //ack timeout
//        {
//            ++ m_bottom_comm_status->timeout_cnt;
//            qDebug() << "-turnT: start step timeout num:" << m_bottom_comm_status->timeout_cnt << m_stStatus_rec_->step_time_cnt;

//            m_speedMonitorSerial_->task_id_change(motorMonitorSerial::bottom);
//            threadDelayMs(10);
//            m_speedMonitorSerial_->m_serial_port->write(reinterpret_cast<const char*>(&m_read_cmdBuffer), 4); //读指令数据
//            step = monitor_process_two::exit_step;
//        }
//    }
//    else if(((step & monitor_process_two::data_step) == monitor_process_two::data_step) && (status != monitor_process_two::none))
//    {
//        statusBarUpdate(monitor_process_two::data_step, (monitor_process_two::STEP_STATUS)status);
//        if((status & monitor_process_two::ok) == monitor_process_two::ok) //data input
//        {

//        }
//        else if((status & monitor_process_two::error) == monitor_process_two::error) //sensor ack check error
//        {
//            ++ m_bottom_comm_status->check_error_cnt;
////            status = monitor_process_two::none; //
//            qDebug() << "-turnT: bottom ack error:" << m_bottom_comm_status->check_error_cnt;
//        }
//        else if((status & monitor_process_two::timeout) == monitor_process_two::timeout) //退出serial线程
//        {
//            ++ m_bottom_comm_status->timeout_cnt;

//            m_speedMonitorSerial_->task_id_change(motorMonitorSerial::bottom);
//            threadDelayMs(10);
//            m_speedMonitorSerial_->m_serial_port->write(reinterpret_cast<const char*>(&m_read_cmdBuffer), 4); //读指令数据
////            step = monitor_process_two::exit_step; //exit
//            qDebug() << "-turnT: bottom stop ack timeout num:" << m_bottom_comm_status->timeout_cnt;
//        }
//    }
//    else if(((step & monitor_process_two::adjust_step)== monitor_process_two::adjust_step) && (status != monitor_process_two::none)) //sensor数据接收和设备控制互锁？接收与控制一定交替？用步骤互锁
//    {
//        statusBarUpdate(monitor_process_two::adjust_step, monitor_process_two::STEP_STATUS(status));
//        if((status & monitor_process_two::ok) == monitor_process_two::ok) //device ack ok
//        {

//        }
//        else if((status & monitor_process_two::error) == monitor_process_two::error) //device ack check error
//        {
//            ++ m_bottom_comm_status->check_error_cnt;
//            qDebug() << "-turnT: device ack error:" << m_bottom_comm_status->check_error_cnt;
//        }
//        else if((status & monitor_process_two::timeout) == monitor_process_two::timeout) //device ack timeout
//        {
//            ++ m_bottom_comm_status->timeout_cnt;
//            step = monitor_process_two::exit_step;
////            status = monitor_process_two::none;
//            qDebug() << "-turnT: device ack timeout num:" << m_bottom_comm_status->timeout_cnt;
//        }
//    }
//    else if(step == monitor_process_two::complete_step)
//    {
//        if(monitor_process_two::ok == (status & monitor_process_two::ok))
//        {
//            m_status_flag_->status.comm_cnt = false;

//            dataHandle(start_state, m_stStatus_rec_->process_time, mst_start_timer_data_); //处理出图时间
//            resultShow(start_state, *mst_start_timer_data_);

//            step = monitor_process_two::ready_step;
//            status = monitor_process_two::none;\
//            threadDelayMs(1000); //延迟1s重新开始
//            processLoop(step, status); //重新开始
//        }
//        else if((monitor_process_two::error == (status & monitor_process_two::error)) || (monitor_process_two::timeout == (status & monitor_process_two::timeout)))
//        {
////            resultShow(false);
//            m_stStatus_rec_->process_time = m_stStatus_rec_->process_timer_cnt.elapsed();
//        }
//    }
//    if(step == monitor_process_two::exit_step) //退出
//    {
//        if(status == monitor_process_two::timeout)
//        {
////            resultShow(-2);
//            m_speedMonitorSerial_->task_id_change(-1);
//            status = monitor_process_two::none;
//        }
//        if(m_speedMonitorSerial_->m_serial_port->isOpen()) //串口关闭
//            m_speedMonitorSerial_->m_serial_port->close();
//        if(m_speedMonitorSerial_->m_transBoard_port->isOpen())
//            m_speedMonitorSerial_->m_transBoard_port->close();

//        m_speedMonitorSerial_->m_task_id = 0;
//        ui->startButton->setText("open");

//        statusBarUpdate(monitor_process_two::ready_step, monitor_process_two::none);
//    }
}

void turnOnTime::statusBarUpdate(monitor_process_two::PROCESS_STEP step, monitor_process_two::STEP_STATUS status)
{
    switch (step) {
    case monitor_process_two::ready_step:
        if(monitor_process_two::none == status)
        {
            ui->serialStep->setText("wait serial open");
            ui->serialLamp->setStyleSheet(m_grey_SheetStyle);
        }
        else
        {
            ui->serialStep->setText("ready"); //
            ui->serialLamp->setStyleSheet(m_green_SheetStyle);
        }
        break;
    case monitor_process_two::start_step:
        if(monitor_process_two::ok == status) //
        {
            ui->startLamp->setStyleSheet(m_green_SheetStyle);
            ui->dataLamp->setStyleSheet(m_grey_SheetStyle);
            ui->adjustLamp->setStyleSheet(m_grey_SheetStyle);
            ui->compStep->setStyleSheet(m_grey_SheetStyle);
            ui->startStep->setText("adjust start");
            ui->dataStep->setText("wait");
            ui->adjustStep->setText("wait");
            ui->compStep->setText("wait");
        }
        else if((monitor_process_two::stop == status) || (monitor_process_two::none == status))
        {
            ui->startLamp->setStyleSheet(m_grey_SheetStyle);
            ui->startStep->setText("wait");
        }
        else if(monitor_process_two::error == status)
        {
            ui->startLamp->setStyleSheet(m_red_SheetStyle); //
            ui->startStep->setText("device error");
        }
        else if(monitor_process_two::timeout == status)
        {
            ui->startLamp->setStyleSheet(m_red_SheetStyle); //
            ui->startStep->setText("device timeout");
        }
        break;
    case monitor_process_two::data_step:
        if(monitor_process_two::ok == status)
        {
            ui->dataLamp->setStyleSheet(m_green_SheetStyle);
            ui->dataStep->setText("data receiving");
        }
        else if(monitor_process_two::error == status)//timeout | error
        {
            ui->dataLamp->setStyleSheet(m_red_SheetStyle); //
            ui->dataStep->setText("sensor error");
        }
        else if(monitor_process_two::timeout == status)
        {
            ui->dataLamp->setStyleSheet(m_red_SheetStyle); //
            ui->dataStep->setText("data timeout");
        }
        break;
    case monitor_process_two::adjust_step:
        if(monitor_process_two::ok == status)
        {
            ui->adjustLamp->setStyleSheet(m_green_SheetStyle);
            ui->adjustStep->setText("adjusting");
        }
        else if(monitor_process_two::error == status){
            ui->adjustLamp->setStyleSheet(m_red_SheetStyle);
            ui->adjustStep->setText("device error");
        }
        else if(monitor_process_two::timeout == status)
        {
            ui->adjustLamp->setStyleSheet(m_red_SheetStyle);
            ui->adjustStep->setText("device timeout");
        }
        break;
    case monitor_process_two::complete_step:
        if(monitor_process_two::start == status)
        {
            ui->compLamp->setStyleSheet(m_green_SheetStyle);
            ui->compStep->setText("start");
        }
        else if(monitor_process_two::ok == status)
        {
            ui->compLamp->setStyleSheet(m_green_SheetStyle);
            ui->compStep->setText("complete");
        }
    default:
        break;
    }
}

/*
 * @BRIEF:
 * @PARAM: 阶段，结果状态
 */
void turnOnTime::resultShow(speed_state state, const ST_RESULT_DATA &timer_data)
{
#if 1
    switch (state) {
    case start_state:
        ui->startTmin->setText(QString::number((timer_data.min),10)); //0.1ms
        ui->startTmax->setText(QString::number((timer_data.max),10));
        ui->startTmean->setText(QString::number((timer_data.mean),10));
        break;
    case stable_state:
        break;
    case change_state:
        break;
    case down_state:
        break;
    }
#else
        if(m_function_select == 1)
        {
            ui->max->setText(QString::number(((float)motor_data.max/100),'f',2)); //0.1ms
            ui->min->setText(QString::number(((float)motor_data.min/100),'f',2));
            ui->mean->setText(QString::number(((float)motor_data.mean/100),'f',2));
        }
        else if(m_function_select == 2)
        {
            ui->max->setText(QString::number(motor_data.max,10)); //0.1ms
            ui->min->setText(QString::number(motor_data.min, 10));
            ui->mean->setText(QString::number(motor_data.mean, 10));
        }
#endif
//        ui->max->setText(QString::number(mst_timer_data_->max, 10));
//        ui->maxAccuracy->setText(QString::number(result.max_bias));
//        ui->minAccuracy->setText(QString::number(result.min_bias));
//        if(result.result) //差值过大
//        {
//            ui->monitorResult->setStyleSheet("color: green;");
//            ui->monitorResult->setText("PASS");
//        }
//        else
//        {
//            ui->monitorResult->setStyleSheet("color: red;");
//            ui->monitorResult->setText("FAIL"); //
//        }
}
/*
 * @BIREF: 进程对应指令
 */
void turnOnTime::startCmdSend()
{
    /*指令发送*/
    uint8_t xor_tmp = 0;
    uint16_t fg_timers = 0;
    switch (m_function_select) { //功能模式调整
    case 1: //motor monitor
        m_speedMonitorSerial_->task_id_change(motorMonitorSerial::motor);

        switch (mst_config_->mode_index + 1) {
        case 1: //循环
            m_start_cmd[2] = 0xA1;

            for(uint8_t i = 0; i < m_start_cmd.length(); ++i) //
            {
                if(i != 3)
                {
                    xor_tmp ^= m_start_cmd[i];
                }
            }
            m_start_cmd[3] = xor_tmp;
            m_speedMonitorSerial_->m_serial_port->write(m_start_cmd);
            break;
        case 2: //定时模式
            m_start_cmd[2] = 0xA0;
            m_start_cmd[7] = mst_config_->monitor_limit_time & 0xff;
            m_start_cmd[8] = 0x00;
            mst_config_->monitor_limit_time = m_start_cmd[7];

            for(uint8_t i = 0; i < m_start_cmd.length(); ++i) //
            {
                if(i != 3)
                {
                    xor_tmp ^= m_start_cmd[i];
                }
            }
            m_start_cmd[3] = xor_tmp;
            m_speedMonitorSerial_->m_serial_port->write(m_start_cmd);
            break;
        case 3: //定圈
            m_start_cmd[2] = 0xA2;
            fg_timers = mst_config_->monitor_limit_cycle_num*3;
            m_start_cmd[7] = fg_timers & 0xff;
            m_start_cmd[8] = (fg_timers>>8) & 0xff;
            for(uint8_t i = 0; i < m_start_cmd.length(); ++i) //
            {
                if(i != 3)
                {
                    xor_tmp ^= m_start_cmd[i];
                }
            }
            m_start_cmd[3] = xor_tmp;
            m_speedMonitorSerial_->m_serial_port->write(m_start_cmd);
    #ifdef  START_BOTTON_BLOCK
            ui->startButton->setDisabled(true);
    #endif
            break;
        default:
            break;
        }
        break;
     case 2: //lidar speed monitor
        m_speedMonitorSerial_->task_id_change(motorMonitorSerial::bottom);
        m_speedMonitorSerial_->m_serial_port->write(QByteArray((char *)YJProtocol::s_standby_cmd, 4));
        break;
     case 3: //启动时间、高低曝光切换时间记录

        break;
     case 4: //调参与转速监控
        break;
     default:
        break;
    }
}

/*
 * @BRIEF: 开始段启动时间，转速
 */
void turnOnTime::dataHandle(speed_state state, const uint16_t &time, ST_RESULT_DATA *timer_data_)
{
    switch(state)
    {
    case start_state:
        if(time > timer_data_->max)
            timer_data_->max = time;
        if(time < timer_data_->min)
            timer_data_->min = time;
        timer_data_->rec_num++;
        break;
    case stable_state:
        break;
    case change_state:
        break;
    case down_state:
        break;
    default:
        break;
    }
}

void turnOnTime::lidar_speed_dataHandle()
{

}

bool turnOnTime::resultCal(ST_RESULT_DATA *motor_data)
{
    uint16_t cal_data_num = motor_data->data.length();
    if(cal_data_num == 0 || motor_data->sum == 0)
    {
        QMessageBox::warning(this,tr("result cal faile"),tr("num = 0"));
        qDebug() << "-motor: receive num:" << cal_data_num << "sum:" << motor_data->sum;
        motor_data->mean = 0;
        return false;
    }
    motor_data->mean = motor_data->sum / cal_data_num; //

    if(motor_data->mean == 0)
    {
        QMessageBox::warning(this,tr("result cal faile"),tr("mean = 0"));
        return false;
    }
    mst_result_->max_bias = (motor_data->max - motor_data->mean)*1000.0f / motor_data->mean + 0.5f;
    mst_result_->min_bias = (motor_data->mean - motor_data->min)*1000.0f / motor_data->mean + 0.5f;
    uint16_t result_stand = ui->resultStandard->text().toUInt();
    mst_result_->result = (mst_result_->max_bias < result_stand) && (mst_result_->min_bias < result_stand);
    return mst_result_->result;
}

void turnOnTime::on_startButton_clicked()
{
    QString port_name, device_port_name;
    port_name = ui->portBox->currentText();
    device_port_name = ui->transPortBox->currentText();
    if(ui->startButton->text() == QString("open")) //
    {
        m_speedMonitorSerial_->m_serial_port->setPortName(port_name);
        m_speedMonitorSerial_->m_serial_port->setBaudRate(ui->baudBox->currentText().toInt());
        m_speedMonitorSerial_->m_serial_port->setDataBits(QSerialPort::Data8);
        m_speedMonitorSerial_->m_serial_port->setParity(QSerialPort::NoParity);
        m_speedMonitorSerial_->m_serial_port->setStopBits(QSerialPort::OneStop);
        m_speedMonitorSerial_->m_serial_port->setFlowControl(QSerialPort::NoFlowControl);
        if(!m_speedMonitorSerial_->m_serial_port->open(QIODevice::ReadWrite))
        {
            QMessageBox::information(this,"error","lidar port open failed");
            return;
        }
        if(device_port_name == port_name)
        {
            QMessageBox::information(this,"error","device port can't the same with lidar port");
            return;
        }
        else
        {
            m_speedMonitorSerial_->m_transBoard_port->setPortName(device_port_name);
            m_speedMonitorSerial_->m_transBoard_port->setBaudRate(ui->transBaudBox->currentText().toInt());
            m_speedMonitorSerial_->m_transBoard_port->setDataBits(QSerialPort::Data8);
            m_speedMonitorSerial_->m_transBoard_port->setParity(QSerialPort::NoParity);
            m_speedMonitorSerial_->m_transBoard_port->setStopBits(QSerialPort::OneStop);
            m_speedMonitorSerial_->m_transBoard_port->setFlowControl(QSerialPort::NoFlowControl);
            if(!m_speedMonitorSerial_->m_transBoard_port->open(QIODevice::ReadWrite))
            {
                QMessageBox::information(this,"error","adjust device port open failed");
                return;
            }
        }
        m_port_name = port_name;
        m_transBoard_name = device_port_name;

        emit subSerialLoop(3);
        //m_speedMonitorSerial_->task_id_change(-1); //待机
        m_stStatus_rec_->cur_step = monitor_process_two::ready_step;
        m_stStatus_rec_->cur_status = monitor_process_two::none;
        //statusBarUpdate(monitor_process_two::ready_step, monitor_process_two::ok);

        //processLoop(m_stStatus_rec_->cur_step, m_stStatus_rec_->cur_status);

        ui->startButton->setText("close");
    }
    else
    {
        m_stStatus_rec_->cur_step = monitor_process_two::exit_step;
        //processLoop(m_stStatus_rec_->cur_step, m_stStatus_rec_->cur_status);
    }
}

void turnOnTime::on_portBox_currentIndexChanged(int index)
{
    Q_UNUSED(index);
    m_port_name = ui->portBox->currentText();
}

void turnOnTime::on_transPortBox_currentIndexChanged(int index)
{
    Q_UNUSED(index);
    m_transBoard_name = ui->transPortBox->currentText();
}

void turnOnTime::on_id_textEdited(const QString &arg1)
{
    mst_result_->id = arg1.toInt();
}
