#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
项目自动更新与清理脚本

该脚本集成了所有更新和清理功能：
1. 文档注册更新（自动清理不存在的文档）
2. Canvas同步更新（只同步有效文档）
3. 块级追溯更新（清理无效块记录）
4. 完整性验证

用法:
python update_project_clean.py --project-path F:\19_Yapha-Spectrometer-MicroSensor --full-update
"""

import argparse
import sys
import subprocess
from pathlib import Path
from datetime import datetime


def run_command(command, description=""):
    """运行命令并处理结果"""
    print(f"\n🔄 {description}")
    print(f"执行命令: {' '.join(command)}")
    
    try:
        # 使用系统默认编码并忽略编码错误
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            errors='ignore'  # 忽略编码错误
        )
        
        if result.stdout:
            print("✅ 输出:")
            print(result.stdout)
            
        if result.stderr:
            print("⚠️ 警告:")
            print(result.stderr)
            
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败 (退出码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="项目自动更新与清理脚本")
    parser.add_argument("--project-path", required=True, help="项目路径")
    parser.add_argument("--full-update", action="store_true", help="执行完整更新（推荐）")
    parser.add_argument("--docs-only", action="store_true", help="仅更新文档关联")
    parser.add_argument("--canvas-only", action="store_true", help="仅更新Canvas")
    parser.add_argument("--blocks-only", action="store_true", help="仅更新块级追溯")
    
    args = parser.parse_args()
    
    project_path = Path(args.project_path)
    if not project_path.exists():
        print(f"❌ 项目路径不存在: {project_path}")
        sys.exit(1)
        
    scripts_dir = Path(__file__).parent
    
    print("🚀 开始项目更新与清理")
    print(f"📂 项目路径: {project_path}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success_count = 0
    total_tasks = 0
    
    if args.full_update or args.docs_only:
        # 1. 文档关联系统更新（自动清理不存在的文档）
        total_tasks += 1
        command = [
            "python", 
            str(scripts_dir / "links" / "auto_link_documents.py"),
            "--project-path", str(project_path),
            "--all", "--register"
        ]
        if run_command(command, "文档关联系统更新与清理"):
            success_count += 1
    
    if args.full_update or args.canvas_only:
        # 2. Canvas同步更新（自动跳过无效文档）
        total_tasks += 1
        command = [
            "python",
            str(scripts_dir / "canvas" / "auto_link_documents.py"),
            str(project_path),
            "--sync-to-canvas"
        ]
        if run_command(command, "Canvas可视化同步更新"):
            success_count += 1
    
    if args.full_update or args.blocks_only:
        # 3. 块级追溯系统更新（如果需要）
        total_tasks += 1
        command = [
            "python",
            str(scripts_dir / "infoTrace" / "auto_index_manager.py"),
            "--project-path", str(project_path),
            "--all", "--validate"
        ]
        if run_command(command, "块级追溯系统验证"):
            success_count += 1
    
    if args.full_update:
        # 4. 最终验证
        total_tasks += 1
        command = [
            "python",
            str(scripts_dir / "canvas" / "auto_link_documents.py"),
            str(project_path),
            "--validate-sync"
        ]
        if run_command(command, "Canvas同步状态验证"):
            success_count += 1
    
    # 总结
    print(f"\n📊 更新完成统计")
    print(f"✅ 成功: {success_count}/{total_tasks} 个任务")
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count == total_tasks:
        print("🎉 所有更新任务成功完成！")
        
        print("\n📋 清理摘要:")
        print("• 自动移除了不存在的文档记录")
        print("• Canvas同步跳过了无效文档")
        print("• 验证通过，系统状态一致")
        
        sys.exit(0)
    else:
        print("⚠️ 部分任务失败，请检查上述错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main() 