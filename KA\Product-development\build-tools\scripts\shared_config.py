#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
共享配置文件

统一管理所有脚本中使用的组件代号、目录映射、流程代号等配置
后续如果需要更改目录名称或者增删目录，只需要修改这个文件即可
"""

from typing import Dict, List, Tuple
from dataclasses import dataclass

@dataclass
class ComponentInfo:
    """组件信息数据类"""
    id: str           # 组件代号
    name: str         # 中文名称
    directory: str    # 对应目录名
    color: str        # 可视化颜色
    order: int        # 排序序号
    keywords: List[str]  # 关键词列表（用于自动识别）

# ========================= 核心组件配置 =========================

# 主要组件定义（按流程顺序）
CORE_COMPONENTS = {
    "REQ": ComponentInfo(
        id="REQ",
        name="需求",
        directory="requirements",
        color="#4CAF50",
        order=1,
        keywords=["requirements", "req", "需求", "requirement"]
    ),
    "DES": ComponentInfo(
        id="DES", 
        name="设计",
        directory="design",
        color="#2196F3",
        order=2,
        keywords=["design", "architecture", "des", "设计", "架构"]
    ),
    "DEV": ComponentInfo(
        id="DEV",
        name="开发", 
        directory="development",
        color="#FF9800",
        order=3,
        keywords=["development", "dev", "src", "code", "开发"]
    ),
    "QA": ComponentInfo(
        id="QA",
        name="质量",
        directory="quality", 
        color="#9C27B0",
        order=4,
        keywords=["test", "qa", "quality", "测试", "质量"]
    ),
    "PROD": ComponentInfo(
        id="PROD",
        name="生产",
        directory="production",
        color="#795548", 
        order=5,
        keywords=["production", "deploy", "release", "生产", "部署"]
    ),
    "DEL": ComponentInfo(
        id="DEL",
        name="交付物",
        directory="deliverables",
        color="#E91E63",
        order=6,
        keywords=["deliverables", "del", "delivery", "交付"]
    ),
    "PM": ComponentInfo(
        id="PM",
        name="项目管理",
        directory="project_management", 
        color="#607D8B",
        order=7,
        keywords=["project", "pm", "management", "项目"]
    )
}

# 辅助组件定义
AUXILIARY_COMPONENTS = {
    "PROD_INFO": ComponentInfo(
        id="PROD_INFO",
        name="产品信息",
        directory="product_info",
        color="#FF5722",
        order=0,
        keywords=["product", "info", "产品信息", "产品"]
    ),
    "DOC": ComponentInfo(
        id="DOC",
        name="文档", 
        directory="docs",
        color="#9E9E9E",
        order=8,
        keywords=["docs", "documentation", "文档"]
    ),
    "PROG": ComponentInfo(
        id="PROG",
        name="进度",
        directory="progress",
        color="#00BCD4",
        order=9,
        keywords=["progress", "进度"]
    )
}

# 合并所有组件
ALL_COMPONENTS = {**CORE_COMPONENTS, **AUXILIARY_COMPONENTS}

# ========================= 向后兼容性映射 =========================

# 兼容旧版本的TEST到QA映射
LEGACY_MAPPINGS = {
    "TEST": "QA"  # 旧版本中使用TEST的地方映射到QA
}

# ========================= 目录结构配置 =========================

# 项目类型配置
PROJECT_TYPES = {
    "single_layer": {
        "name": "单层项目",
        "components": ["REQ", "DES", "DEV", "QA", "DEL", "PM", "PROD"],
        "dev_subdirs": ["projects"]
    },
    "hardware": {
        "name": "硬件项目",
        "components": ["REQ", "DES", "DEV", "QA", "PROD", "DEL", "PM"],
        "dev_subdirs": ["hardware"]
    },
    "software": {
        "name": "软件项目", 
        "components": ["REQ", "DES", "DEV", "QA", "DEL", "PM"],
        "dev_subdirs": ["software"]
    },
    "firmware": {
        "name": "固件项目",
        "components": ["REQ", "DES", "DEV", "QA", "DEL", "PM"],
        "dev_subdirs": ["firmware"]
    },
    "hardware+software": {
        "name": "硬件和软件项目",
        "components": ["REQ", "DES", "DEV", "QA", "PROD", "DEL", "PM"],
        "dev_subdirs": ["hardware", "software"]
    },
    "hardware+firmware": {
        "name": "硬件和固件项目", 
        "components": ["REQ", "DES", "DEV", "QA", "PROD", "DEL", "PM"],
        "dev_subdirs": ["hardware", "firmware"]
    },
    "firmware+software": {
        "name": "固件和软件项目",
        "components": ["REQ", "DES", "DEV", "QA", "DEL", "PM"],
        "dev_subdirs": ["firmware", "software"]
    },
    "full": {
        "name": "完整项目",
        "components": ["REQ", "DES", "DEV", "QA", "PROD", "DEL", "PM"],
        "dev_subdirs": ["hardware", "firmware", "software", "tool"]
    }
}

# ========================= 关系和流程配置 =========================

# 组件间关系颜色配置
RELATION_COLORS = {
    "实现": "#4CAF50",
    "验证": "#2196F3", 
    "依赖": "#FF9800",
    "参考": "#9E9E9E",
    "继承": "#9C27B0",
    "配置": "#795548",
    "展开": "#607D8B",
    "包含": "#E91E63",
    "触发": "#FF5722",
    "生成": "#00BCD4",
    "流转": "#00BCD4",
    "default": "#666666"
}

# 默认组件关系配置
DEFAULT_COMPONENT_RELATIONS = {
    "components": [
        {
            "id": "REQ",
            "name": "需求",
            "directory": "requirements", 
            "related_components": ["DES", "DEV", "QA"]
        },
        {
            "id": "DES",
            "name": "设计",
            "directory": "design",
            "related_components": ["DEV", "QA"]
        },
        {
            "id": "DEV", 
            "name": "开发",
            "directory": "development",
            "related_components": ["QA", "PROD"]
        },
        {
            "id": "QA",
            "name": "质量",
            "directory": "quality",
            "related_components": ["DEV", "PROD"]
        },
        {
            "id": "PROD",
            "name": "生产",
            "directory": "production", 
            "related_components": ["DEL"]
        },
        {
            "id": "DEL",
            "name": "交付物",
            "directory": "deliverables",
            "related_components": []
        },
        {
            "id": "PM",
            "name": "项目管理",
            "directory": "project_management",
            "related_components": ["REQ", "DES", "DEV", "QA", "PROD", "DEL"]
        }
    ]
}

# ========================= 便利函数 =========================

def get_component_info(component_id: str) -> ComponentInfo:
    """获取组件信息，支持向后兼容性映射"""
    # 处理向后兼容性
    if component_id in LEGACY_MAPPINGS:
        component_id = LEGACY_MAPPINGS[component_id]
    
    return ALL_COMPONENTS.get(component_id)

def get_all_component_ids() -> List[str]:
    """获取所有组件ID列表"""
    return list(ALL_COMPONENTS.keys())

def get_core_component_ids() -> List[str]:
    """获取核心组件ID列表"""
    return list(CORE_COMPONENTS.keys())

def get_ordered_components() -> List[str]:
    """获取按顺序排列的组件ID列表"""
    return sorted(ALL_COMPONENTS.keys(), key=lambda x: ALL_COMPONENTS[x].order)

def get_component_codes() -> Dict[str, str]:
    """获取组件代号到中文名称的映射（向后兼容）"""
    codes = {comp_id: comp_info.name for comp_id, comp_info in ALL_COMPONENTS.items()}
    # 添加向后兼容性映射
    for old_id, new_id in LEGACY_MAPPINGS.items():
        if new_id in codes:
            codes[old_id] = codes[new_id]
    return codes

def get_directory_mapping() -> Dict[str, str]:
    """获取组件代号到目录名的映射（向后兼容）"""
    mapping = {comp_id: comp_info.directory for comp_id, comp_info in ALL_COMPONENTS.items()}
    # 添加向后兼容性映射
    for old_id, new_id in LEGACY_MAPPINGS.items():
        if new_id in mapping:
            mapping[old_id] = mapping[new_id]
    return mapping

def get_single_layer_mapping() -> Dict[str, str]:
    """获取单层级目录映射（向后兼容，主要用于links模块）"""
    return {comp_id: comp_info.directory for comp_id, comp_info in CORE_COMPONENTS.items()}

def get_multi_level_mapping() -> Dict[str, str]:
    """获取多层级目录映射（向后兼容）"""
    return {comp_id: comp_info.directory for comp_id, comp_info in CORE_COMPONENTS.items() 
            if comp_id in ["REQ", "DES", "DEV", "PM"]}

def get_component_color(component_id: str) -> str:
    """获取组件颜色"""
    info = get_component_info(component_id)
    return info.color if info else "#666666"

def get_relation_color(relation_type: str) -> str:
    """获取关系颜色"""
    return RELATION_COLORS.get(relation_type, RELATION_COLORS["default"])

def infer_component_from_directory(directory_name: str) -> str:
    """从目录名推断组件类型"""
    directory_name = directory_name.lower()
    for comp_id, comp_info in ALL_COMPONENTS.items():
        if comp_info.directory.lower() == directory_name:
            return comp_id
        for keyword in comp_info.keywords:
            if keyword.lower() in directory_name:
                return comp_id
    return None

def extract_component_from_id(item_id: str) -> str:
    """从ID中提取组件类型"""
    if not item_id:
        return None
    
    # 尝试直接匹配组件前缀
    for comp_id in ALL_COMPONENTS.keys():
        if item_id.upper().startswith(comp_id):
            return comp_id
    
    # 尝试关键词匹配
    item_id_lower = item_id.lower()
    for comp_id, comp_info in ALL_COMPONENTS.items():
        for keyword in comp_info.keywords:
            if keyword.lower() in item_id_lower:
                return comp_id
    
    return None

# ========================= 子目录缩写配置 =========================

# 子目录名称到缩写的映射
SUBDIRECTORY_ABBREVIATIONS = {
    # 需求相关子目录
    "custom_requirements": "CUST",
    "main_requirements": "MAIN",
    "market_requirements": "MARK",
    "imported_requirements": "IMPORT",

    # 设计相关子目录
    "interface_specifications": "INTF",
    "principle_Information": "PRIN",
    "project_solution": "PROJ",
    "specification_document": "SPEC",

    # 开发相关子目录
    "firmware": "FW",
    "hardware": "HW",
    "software": "SW",
    "tool": "TOOL",

    # 质量相关子目录
    "test_cases": "CASE",
    "test_plans": "PLAN",
    "test_reports": "REPT",
    "issues": "ISSUE",

    # 生产相关子目录
    "bom": "BOM",
    "manufacturing_process": "MFG",
    "quality_control": "QC",

    # 交付物相关子目录
    "documents": "DOC",
    "firmware": "FW",
    "hardware": "HW",
    "software": "SW",
    "tools": "TOOL",

    # 项目管理相关子目录
    "meeting_notes": "MEET",
    "resources": "RES",
    "risks": "RISK",
    "schedules": "SCHED",

    # 产品信息相关子目录
    "competitor_products": "COMP"
}

def get_subdirectory_abbreviation(subdir_name: str) -> str:
    """获取子目录的缩写"""
    # 首先查找精确匹配
    if subdir_name in SUBDIRECTORY_ABBREVIATIONS:
        return SUBDIRECTORY_ABBREVIATIONS[subdir_name]

    # 特殊处理常见的开发项目类型
    if 'firmware' in subdir_name.lower():
        return "FW"
    elif 'software' in subdir_name.lower():
        return "SW"
    elif 'hardware' in subdir_name.lower():
        return "HW"
    elif 'tool' in subdir_name.lower():
        return "TOOL"

    # 回退到前4个字符的大写
    return subdir_name.upper()[:4]

# ========================= 可视化配置 =========================

# 不同模式下显示的组件
VISUALIZATION_MODE_COMPONENTS = {
    "structure": ["REQ", "DES", "DEV", "PM", "QA", "PROD_INFO", "DEL"],
    "workflow": get_core_component_ids(),
    "documents": get_core_component_ids(),
    "traceability": get_core_component_ids(),
    "progress": get_core_component_ids(),
    "all": get_all_component_ids()
}