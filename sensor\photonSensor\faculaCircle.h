#ifndef _FACUAL_CIRCLE_H_
#define _FACULA_CIRCLE_H_

#include <QByteArray>

#include "IFaculaAdjust.h"
#include "qLog.h"

class CFaculaCircle : public IFaculaAdjust {
  public:
    CFaculaCircle(const IFaculaAdjust::StMapInfo &st_map_info);
    ~CFaculaCircle();


    void variblesInit() override;

    //    void targetMapUpdate(const StMapTargetInfo &target_map) override;
    //    int8_t faculaAdjust(StMapData *map_data_,
    //                        const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) override;

    bool faculaMp(StMapData *map_data_) override;

    uint8_t faculaAdjust(StMapData *map_data_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) override;

    uint16_t faculaTest(StMapData *map_data_, QVector<uint32_t> &target_facula, const EFaculaJudgeMode &test_mode, const bool &has_adjusted) override;

    void findMaxMP(StMapData *map_data_) override;

  private:
    enum class EAdjustStep {
        eDEFAULT_MOVE_STEP = 0,
        eRANDOM_FIND_STEP  = 1,
        eCENTRAL_STEP      = 2,
        eSYMMETRY_STEP     = 3,
        ePEAK_STEP         = 4,
    };

    enum class EZFindFunction {
        eMAX_PEAK = 1,
        eDISPERSE = 2,
    };

    typedef struct {
        uint16_t theta;
        uint16_t find_radius;
        uint8_t  find_facula_cnt;
    } StRandomFaculaInfo;

    typedef struct {
        uint32_t          max_peak;
        uint16_t          cross_aver_peak;
        QVector<uint32_t> edge_corner;
    } StZFaculaInfo;

    typedef struct {
        int16_t           max_around_peak;
        QVector<uint32_t> edge_corner;
        uint16_t          max_peak;
    } StZDisperseFacula;

    typedef struct {
        StSingleMp left;
        StSingleMp right;
        StSingleMp up;
        StSingleMp down;
        StSingleMp center;  //目标MP TF
    } StRoundJudgeMP;

    typedef struct {
        bool                sensor_direction;     //贴片方向
        bool                is_center_to_target;  // true: 先中心后目标点
        uint8_t             map_xlen;
        uint8_t             map_ylen;
        StRoundJudgeMP      center_round;  //中心区域
        StRoundJudgeMP      target_round;  //目标区域(中心与四周MP)
        ESymmetryAdjustType symm_type;
    } StTargetInfo;

    // 添加组件日志成员
    static const QMap<EAdjustStep, QString> mm_adjust_step_describe;

    IFaculaAdjust::UFaculaAdjustDds mu_facula_adjust;
    IFaculaAdjust::UFaculaJudgeDds  mu_facula_detect_items;

    EAdjustStep    m_adjust_len_step;
    EZFindFunction m_z_function_select;


    //* central find
    int32_t m_last_max_peak;
    int32_t m_z_peak_stable_cnt;
    //    C3dHandMachine::St3D<int16_t>         mst_max_move_delta;

    uint16_t m_find_radius;

    StTargetInfo mst_target_info;

    void                          faculaAdjustInfoHandle();
    bool                          targetFaculaMoveInfo(const IFaculaAdjust::StMapInfo &map_info, StTargetInfo *target_info_);  // override;
    C3dHandMachine::St3D<int16_t> getSymmetryPeakThreshold(const uint32_t &center_peak, const EFaculaJudgeMode &mode);
    void                          updateRoundMpPeak(StRoundJudgeMP *round_mp_, const QVector<QVector<uint32_t>> &map_matrix, const bool &has_adjusted);
    CFaculaCircle::StSymmetryInfo
    getSymmetryDelta(StRoundJudgeMP *round_mp_, const QVector<QVector<uint32_t>> &map_matrix, const StSymmetryInfo &mode, const bool &is_move_center);


    EFaculaStatus defaultFaculaMove(C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus
    randomFaculaFind(StRandomFaculaInfo *random_find_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus targetMove();
    EFaculaStatus centralMove(const C3dHandMachine::St3D<int16_t> &central_mp_delta, C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus
    symmetryMove(const C3dHandMachine::St3D<int16_t> &symmetry_mp_delta, const int16_t &xy_peak_delta_tor, C3dHandMachine::St3D<int16_t> *move_3d_);
    EFaculaStatus peakMove(int8_t &                             z_direction,
                           const StZFaculaInfo &                z_facula_info,
                           const EZFindFunction &               find_function,
                           C3dHandMachine::St3D<int16_t> *      move_3d_,
                           const C3dHandMachine::St3D<int16_t> &move_delta_step);

    EFaculaStatus zMaxPeakSingleStep(int8_t &                             z_direction,
                                     const StZFaculaInfo &                z_facula_info,
                                     const C3dHandMachine::St3D<int16_t> &move_delta_step,
                                     C3dHandMachine::St3D<int16_t> *      move_step_);
    EFaculaStatus zFindDisperseFacula(const StZDisperseFacula &z_disperse_info, C3dHandMachine::St3D<int16_t> *move_step_);
};


#endif
