#!/usr/bin/env python3
"""
需求追溯管理工具 - 管理需求之间的追溯关系
用法: python req_trace.py --matrix="requirements_matrix.md" --output="trace_report.md"
"""

import os
import sys
import argparse
import json
import pandas as pd
from pathlib import Path

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="需求追溯管理工具")
    parser.add_argument('--matrix', required=True, help="需求矩阵文件路径")
    parser.add_argument('--output', default="trace_report.md", help="输出报告路径")
    parser.add_argument('--format', default="markdown", choices=["markdown", "html", "excel"], help="输出格式")
    parser.add_argument('--level', help="指定层级进行追溯")
    parser.add_argument('--show-all', action='store_true', help="显示所有追溯关系")
    return parser.parse_args()

def load_requirements_matrix(matrix_path):
    """加载需求矩阵"""
    try:
        with open(matrix_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # 解析markdown表格
        df = pd.read_csv(pd.StringIO(content), sep='|', skiprows=1)
        df = df.iloc[:, 1:-1]  # 移除首尾的空列
        df.columns = df.columns.str.strip()
        return df
    except Exception as e:
        print(f"错误: 加载需求矩阵时出错: {str(e)}")
        sys.exit(1)

def analyze_traceability(df, level=None, show_all=False):
    """分析需求追溯关系"""
    trace_data = {
        'requirements': [],
        'relationships': [],
        'statistics': {
            'total_requirements': len(df),
            'traced_requirements': 0,
            'missing_traces': 0
        }
    }
    
    # 分析每个需求
    for _, row in df.iterrows():
        req_id = row.get('ID', '')
        req_level = row.get('Level', '')
        
        if level and req_level != level:
            continue
            
        req_data = {
            'id': req_id,
            'level': req_level,
            'description': row.get('Description', ''),
            'parent': row.get('Parent', ''),
            'children': [],
            'status': row.get('Status', '')
        }
        
        # 查找子需求
        children = df[df['Parent'] == req_id]
        req_data['children'] = children['ID'].tolist()
        
        trace_data['requirements'].append(req_data)
        
        # 更新统计信息
        if req_data['parent'] or req_data['children']:
            trace_data['statistics']['traced_requirements'] += 1
        else:
            trace_data['statistics']['missing_traces'] += 1
    
    return trace_data

def generate_report(trace_data, output_path, format_type):
    """生成追溯报告"""
    if format_type == "markdown":
        generate_markdown_report(trace_data, output_path)
    elif format_type == "html":
        generate_html_report(trace_data, output_path)
    elif format_type == "excel":
        generate_excel_report(trace_data, output_path)

def generate_markdown_report(trace_data, output_path):
    """生成Markdown格式报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        # 写入标题
        f.write("# 需求追溯分析报告\n\n")
        
        # 写入统计信息
        f.write("## 统计信息\n\n")
        stats = trace_data['statistics']
        f.write(f"- 总需求数: {stats['total_requirements']}\n")
        f.write(f"- 已追溯需求数: {stats['traced_requirements']}\n")
        f.write(f"- 未追溯需求数: {stats['missing_traces']}\n\n")
        
        # 写入需求追溯关系
        f.write("## 需求追溯关系\n\n")
        for req in trace_data['requirements']:
            f.write(f"### {req['id']} - {req['description']}\n\n")
            f.write(f"- 层级: {req['level']}\n")
            f.write(f"- 状态: {req['status']}\n")
            if req['parent']:
                f.write(f"- 父需求: {req['parent']}\n")
            if req['children']:
                f.write("- 子需求:\n")
                for child in req['children']:
                    f.write(f"  - {child}\n")
            f.write("\n")

def generate_html_report(trace_data, output_path):
    """生成HTML格式报告"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>需求追溯分析报告</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .stats { background-color: #f5f5f5; padding: 20px; border-radius: 5px; }
            .requirement { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h1>需求追溯分析报告</h1>
        
        <div class="stats">
            <h2>统计信息</h2>
            <p>总需求数: {total}</p>
            <p>已追溯需求数: {traced}</p>
            <p>未追溯需求数: {missing}</p>
        </div>
        
        <h2>需求追溯关系</h2>
    """.format(
        total=trace_data['statistics']['total_requirements'],
        traced=trace_data['statistics']['traced_requirements'],
        missing=trace_data['statistics']['missing_traces']
    )
    
    for req in trace_data['requirements']:
        html_content += f"""
        <div class="requirement">
            <h3>{req['id']} - {req['description']}</h3>
            <p>层级: {req['level']}</p>
            <p>状态: {req['status']}</p>
        """
        
        if req['parent']:
            html_content += f"<p>父需求: {req['parent']}</p>"
        
        if req['children']:
            html_content += "<p>子需求:</p><ul>"
            for child in req['children']:
                html_content += f"<li>{child}</li>"
            html_content += "</ul>"
        
        html_content += "</div>"
    
    html_content += """
    </body>
    </html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

def generate_excel_report(trace_data, output_path):
    """生成Excel格式报告"""
    # 创建需求数据
    req_data = []
    for req in trace_data['requirements']:
        req_data.append({
            'ID': req['id'],
            '描述': req['description'],
            '层级': req['level'],
            '状态': req['status'],
            '父需求': req['parent'],
            '子需求': ', '.join(req['children'])
        })
    
    # 创建统计信息
    stats_data = [{
        '指标': '总需求数',
        '数值': trace_data['statistics']['total_requirements']
    }, {
        '指标': '已追溯需求数',
        '数值': trace_data['statistics']['traced_requirements']
    }, {
        '指标': '未追溯需求数',
        '数值': trace_data['statistics']['missing_traces']
    }]
    
    # 写入Excel文件
    with pd.ExcelWriter(output_path) as writer:
        pd.DataFrame(req_data).to_excel(writer, sheet_name='需求追溯关系', index=False)
        pd.DataFrame(stats_data).to_excel(writer, sheet_name='统计信息', index=False)

def main():
    """主函数"""
    args = parse_arguments()
    
    # 加载需求矩阵
    df = load_requirements_matrix(args.matrix)
    
    # 分析追溯关系
    trace_data = analyze_traceability(df, args.level, args.show_all)
    
    # 确保输出目录存在
    output_path = Path(args.output)
    output_dir = output_path.parent
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成报告
    generate_report(trace_data, str(output_path), args.format)
    
    print(f"成功: 已生成需求追溯报告 {args.output}")

if __name__ == "__main__":
    main() 