#ifndef _ITABLE_H_
#define _ITABLE_H_

#include <QByteArray>
#include <QMap>


class ITable {
public:
    ITable(){};
    virtual ~ITable(){};

    //* MP顺序方向
    enum EMpOrder {
        left_2_right    = 0,
        up_2_down       ,
        right_2_left    ,
        down_2_up       ,
    };

    typedef struct {
//        QTableWidget* table_;
        uint8_t         x;
        uint8_t         y;
        uint8_t         target_mp_x;
        uint8_t         target_mp_y;
        EMpOrder        mp_order;
    } StTableInfo;

protected:

};

#endif
