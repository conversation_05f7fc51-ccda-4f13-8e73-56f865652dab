#ifndef QLOG_H
#define QLOG_H

#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QString>
#include <QTextStream>

#include "typeConvert.h"

// 日志级别定义

namespace MyLogger {
enum class LogLevel { DEBUG = 0, INFO, WARNING, ERR, FATAL };
// 日志类型定义
enum class LogType {
    DEFAULT = 0,
    INIT,               // 初始化信息
    COMM,               // 通信信息
    COMM_ACK,           // 通信应答
    PROCESS_STATUS,     // 处理流程
    PROCESS_DATA,       // 过程数据
    RESULT_CACHE_DATA,  // 结果数据
    ERROR_LOG           // 错误日志
};

class QLog {
  public:
    static void init();  // bool enableConsole = true, bool enableFile = true
    // static void setLogLevel(LogLevel level);
    static void setLogPath(const QString &path);
    static void installMessageHandler();


    template <typename... Args>
    static void
    log(LogLevel level = LogLevel::INFO, LogType type = LogType::INIT, const char *file = nullptr, int line = 0, const QString &format = "", Args &&...args) {
        // 根据编译选项检查是否需要输出该类型的日志
        if (!isLogTypeEnabled(type)) {
            return;
        }

        QString logMessage = formatLogMessage(type, file, line, format, std::forward<Args>(args)...);
        //* 输出内容：[类型] [文件名] [行] - 内容

        switch (level) {
        case LogLevel::DEBUG:
            qDebug().nospace() << logMessage;
            break;
        case LogLevel::INFO:
            qInfo().nospace() << logMessage;
            break;
        case LogLevel::WARNING:
            qWarning().nospace() << logMessage;
            break;
        case LogLevel::ERR:
            qCritical().nospace() << logMessage;
            break;
        case LogLevel::FATAL:
            qFatal("%s", logMessage.toLocal8Bit().constData());
            break;
        }
    }

    // QString        module_name = "",
    static void log(LogLevel level = LogLevel::INFO, LogType type = LogType::INIT, const char *file = nullptr, int line = 0, const QString &message = "") {
        // 根据编译选项检查是否需要输出该类型的日志
        if (!isLogTypeEnabled(type)) {
            return;
        }

        // QString formattedMessage = formatLogMessage(type, file, line, format, std::forward<Args>(args)...);
        //* 输出内容：[类型] [文件名] [行] - 内容
        QString logMessage = QString("[%1] [%2] [%3] - %4").arg(QString(file).section('/', -1)).arg(getLogTypeStr(type)).arg(line).arg(message);

        switch (level) {
        case LogLevel::DEBUG:
            qDebug().nospace() << logMessage;
            break;
        case LogLevel::INFO:
            qInfo().nospace() << logMessage;
            break;
        case LogLevel::WARNING:
            qWarning().nospace() << logMessage;
            break;
        case LogLevel::ERR:
            qCritical().nospace() << logMessage;
            break;
        case LogLevel::FATAL:
            qFatal("%s", logMessage.toLocal8Bit().constData());
            break;
        }
    }

  private:
    static const qint64 MAX_LOG_SIZE     = 10 * 1024 * 1024;  // 10MB
    static const int    MAX_BACKUP_COUNT = 5;                 // 最多保留5个备份文件

    static const QString DEFAULT_LOG_PATH;  // 默认日志路径
    static const QString LOG_FILE_PATTERN;  // 日志文件名模式
    static const QString DATE_FORMAT;       // 日期格式

    // static LogLevel s_logLevel;
    static QString s_logPath;
    static QString s_logFile;
    static bool    s_enableConsole;
    static bool    s_enableFile;

    static bool isLogTypeEnabled(LogType type) {
        switch (type) {
        case LogType::INIT:
#ifndef INIT_OUTPUT
            return false;
#endif
            break;
        case LogType::COMM:
#ifndef COMM_OUTPUT
            return false;
#endif
            break;
        case LogType::COMM_ACK:
#ifndef COMM_ACK_OUTPUT
            return false;
#endif
        case LogType::PROCESS_STATUS:
#ifndef PROCESS_STATUS_OUTPUT
            return false;
#endif
            break;

            break;
        case LogType::PROCESS_DATA:
#ifndef PROCESS_DATA_OUTPUT
            return false;
#endif
            break;
        case LogType::RESULT_CACHE_DATA:
#ifndef RESULT_CACHE_OUTPUT
            return false;
#endif
            break;

        case LogType::ERROR_LOG:
#ifndef ERROR_TO_LOG
            return false;
#endif
            break;
        default:
            return true;
        }
        return true;
    }

    static QString getColorCode(QtMsgType level);
    static QString getLogTypeStr(LogType type);
    static void    writeToFile(const QString &msg, bool addTimestamp = true);
    static void    rotateLogFile();
    static void    messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg);

    template <typename... Args> static QString formatLogMessage(LogType type, const char *file, int line, const QString &format, Args... args) {
        QString formattedContent;
        if constexpr (sizeof...(args) > 0) {
            // QTextStream stream(&formattedContent);

            // 格式化数据并写入流
            // (stream << ... << NsTypeConvert::toCStr(std::forward<Args>(args)));  // 逐个参数写入 stream

            formattedContent = QString::asprintf(format, std::forward<Args>(args)...);
        } else {
            formattedContent = QString(format);
        }

        return QString("[%1] %2:%3 - %4")
            .arg(getLogTypeStr(type))
            .arg(QString(file).section('/', -1))  // 只保留文件名
            .arg(line)
            .arg(formattedContent.trimmed());
    }

    static QString levelToString(LogLevel level);
    static QString typeToString(LogType type);

    static LogLevel stringToLevel(const QString &levelStr) {
        if (levelStr == "DEBUG")
            return LogLevel::DEBUG;
        if (levelStr == "INFO")
            return LogLevel::INFO;
        if (levelStr == "WARNING")
            return LogLevel::WARNING;
        if (levelStr == "ERROR")
            return LogLevel::ERR;
        if (levelStr == "FATAL")
            return LogLevel::FATAL;
        return LogLevel::INFO;  // 默认级别
    }
};

// 组件日志类
class CComponentLog {
  public:
    CComponentLog(const QString &component_name) : m_component_name(component_name) {
    }

    template <typename... Args> void debug(LogType type, const char *func, int line, const QString &format, Args &&...args) const {
        if constexpr (sizeof...(args) > 0) {
            QLog::log(LogLevel::DEBUG, type, m_component_name.toLocal8Bit().constData(), line, format, std::forward<Args>(args)...);
        } else {
            QLog::log(LogLevel::DEBUG, type, m_component_name.toLocal8Bit().constData(), line, format);
        }
    }
    template <typename... Args> void debug(LogType type, const char *func, int line, const QString &message) const {
        QLog::log(LogLevel::DEBUG, type, m_component_name.toLocal8Bit().constData(), line, message);
    }

    template <typename... Args> void info(LogType type, const char *func, int line, const QString &format, Args &&...args) const {
        if constexpr (sizeof...(args) > 0) {
            QLog::log(LogLevel::INFO, type, m_component_name.toLocal8Bit().constData(), line, format, std::forward<Args>(args)...);
        } else {
            QLog::log(LogLevel::INFO, type, m_component_name.toLocal8Bit().constData(), line, format);
        }
    }
    template <typename... Args> void info(LogType type, const char *func, int line, const QString &message) const {
        QLog::log(LogLevel::INFO, type, m_component_name.toLocal8Bit().constData(), line, message);
    }
    // template <typename... Args> void warn(LogType type, const char *func, int line, Args... args) const {
    //     QLog::log(LogLevel::WARNING, type, m_component_name.toLocal8Bit().constData(), line, args...);
    // }

    // template <typename... Args> void error(LogType type, const char *func, int line, Args... args) const {
    //     QLog::log(LogLevel::ERR, type, m_component_name.toLocal8Bit().constData(), line, args...);
    // }

    // template <typename... Args> void fatal(LogType type, const char *func, int line, Args... args) const {
    //     QLog::log(LogLevel::FATAL, type, m_component_name.toLocal8Bit().constData(), line, args...);
    // }

  private:
    QString m_component_name;
};
}  // namespace MyLogger

// 日志宏定义
#define LOG_DEBUG(type, message)              MyLogger::QLog::log(MyLogger::LogLevel::DEBUG, type, __FILE__, __LINE__, message)
#define LOG_INFO(type, message)               MyLogger::QLog::log(MyLogger::LogLevel::INFO, type, __FILE__, __LINE__, message)
#define LOG_WARN(type, message)               MyLogger::QLog::log(MyLogger::LogLevel::WARNING, type, __FILE__, __LINE__, message)
#define LOG_ERROR(type, message)              MyLogger::QLog::log(MyLogger::LogLevel::ERR, type, __FILE__, __LINE__, message)
#define LOG_FATAL(type, message)              MyLogger::QLog::log(MyLogger::LogLevel::FATAL, type, __FILE__, __LINE__, message)

// 组件日志宏定义
#define COMP_LOG_DEBUG(logger, type, message) logger.debug(type, __func__, __LINE__, message)
#define COMP_LOG_INFO(logger, type, message)  logger.info(type, __func__, __LINE__, message)


/**
 * @brief
 * @param logger
 * @param type
 * @param format QString
 * @param ... QString等qt类型，传入前数据要先转成 const char*(.toLocal8Bit().constData())
 */
// #define COMP_LOG_INFO(logger, type, format, ...) logger.info(type, __func__, __LINE__, format, __VA_ARGS__)

// #define COMP_LOG_WARN(logger, type, ...)  logger.warn(type, __func__, __LINE__, __VA_ARGS__)
// #define COMP_LOG_ERROR(logger, type, ...) logger.error(type, __func__, __LINE__, __VA_ARGS__)
// #define COMP_LOG_FATAL(logger, type, ...) logger.fatal(type, __func__, __LINE__, __VA_ARGS__)

#endif  // QLOG_H
