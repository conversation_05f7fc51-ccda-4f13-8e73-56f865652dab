# 图像滤波器配置说明

**版本**: LA-T5 v1.4.4  
**更新日期**: 2025-01-16  
**适用范围**: 所有图像滤波器配置  

## 概述

LA-T5系统支持多种图像滤波器，每种滤波器都有其特定的应用场景和配置参数。本文档提供了完整的滤波器配置指南，帮助用户根据实际需求选择和配置合适的滤波器。

## 支持的滤波器类型

### 1. WeightedAverageFilter - 加权均值滤波器 ⭐
**适用场景**：光斑处理、中心增强、平滑滤波
```ini
[WeightedAverage]
kernel_size=3
preset=center_weighted
strength=1.0
enabled=true
```
**详细说明**：[[WeightedAverageFilter使用指南]]

### 2. ConvolutionFilter - 卷积滤波器
**适用场景**：边缘检测、锐化、自定义滤波
```ini
[Convolution]
kernel_size=3
preset=sharpen
strength=1.0
enabled=true
```

### 3. MedianFilter - 中值滤波器
**适用场景**：噪声去除、保持边缘
```ini
[Median]
kernel_size=3
strength=1.0
enabled=true
```

### 4. GaussianFilter - 高斯滤波器
**适用场景**：平滑处理、模糊效果
```ini
[Gaussian]
sigma=1.5
kernel_size=5
strength=1.0
enabled=true
```

### 5. BilateralFilter - 双边滤波器
**适用场景**：保持边缘的平滑处理
```ini
[Bilateral]
sigma_color=75.0
sigma_space=75.0
kernel_size=5
strength=1.0
enabled=true
```

### 6. KalmanFilter - 卡尔曼滤波器
**适用场景**：时序滤波、动态噪声抑制
```ini
[Kalman]
process_noise=0.1
measurement_noise=0.1
enabled=true
```

## 通用配置参数

### 基础参数
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| enabled | bool | true | 是否启用滤波器 |
| strength | float | 1.0 | 滤波强度（0.0-1.0） |
| kernel_size | int | 3 | 滤波核大小（奇数） |

### 高级参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| preset | string | 预定义模式名称 |
| normalize | bool | 是否归一化处理 |

## 配置文件结构

### 完整配置示例
```ini
# 图像滤波器配置文件
# 版本: LA-T5 v1.4.4

[Global]
# 全局设置
filter_chain_enabled=true
default_strength=1.0

[WeightedAverage]
# 加权均值滤波器 - 推荐用于光斑处理
enabled=true
kernel_size=3
preset=center_weighted
strength=1.0
normalize=true

[Convolution]
# 卷积滤波器 - 用于边缘检测和锐化
enabled=false
kernel_size=3
preset=sharpen
strength=0.8
normalize=true

[Median]
# 中值滤波器 - 用于噪声去除
enabled=false
kernel_size=3
strength=1.0

[Gaussian]
# 高斯滤波器 - 用于平滑处理
enabled=false
sigma=1.5
kernel_size=5
strength=1.0

[Bilateral]
# 双边滤波器 - 保持边缘的平滑
enabled=false
sigma_color=75.0
sigma_space=75.0
kernel_size=5
strength=1.0

[Kalman]
# 卡尔曼滤波器 - 时序滤波
enabled=false
process_noise=0.1
measurement_noise=0.1
```

## 配置最佳实践

### 1. 滤波器选择指南

#### 光斑处理场景
```ini
# 推荐配置：中心加权滤波
[WeightedAverage]
enabled=true
preset=center_weighted
strength=1.0
```

#### 噪声抑制场景
```ini
# 推荐配置：中值滤波 + 高斯平滑
[Median]
enabled=true
kernel_size=3

[Gaussian]
enabled=true
sigma=1.0
kernel_size=3
```

#### 边缘检测场景
```ini
# 推荐配置：边缘增强
[WeightedAverage]
enabled=true
preset=edge_enhance
strength=0.8
```

### 2. 性能优化配置

#### 实时处理配置
```ini
# 优先性能
[WeightedAverage]
kernel_size=3  # 使用小核
strength=1.0
```

#### 高质量处理配置
```ini
# 优先质量
[WeightedAverage]
kernel_size=5  # 使用大核
strength=1.0

[Bilateral]
enabled=true
sigma_color=50.0
sigma_space=50.0
```

### 3. 滤波器链配置

#### 多级滤波
```ini
# 第一级：噪声去除
[Median]
enabled=true
kernel_size=3

# 第二级：中心增强
[WeightedAverage]
enabled=true
preset=center_weighted
strength=0.8

# 第三级：轻微平滑
[Gaussian]
enabled=true
sigma=0.5
strength=0.5
```

## 边界处理配置

### v1.4.4重要更新
从v1.4.4版本开始，WeightedAverageFilter和ConvolutionFilter采用改进的边界处理方式：

#### 边界处理模式
| 滤波器 | 边界处理方式 | 说明 |
|--------|-------------|------|
| WeightedAverage | 边缘复制 | 避免边缘偏小问题 |
| Convolution | 边缘复制 | 保持边缘特征 |
| Median | 对称填充 | 保持统计特性 |
| Gaussian | 边缘复制 | 自然平滑过渡 |
| Bilateral | 边缘复制 | 保持边缘清晰 |

#### 配置影响
- ✅ **无需修改配置**：现有配置文件完全兼容
- ✅ **自动优化**：系统自动选择最佳边界处理方式
- ✅ **性能提升**：边缘处理更准确

## 调试和验证

### 配置验证
```cpp
// 验证配置文件
ConfigValidator validator;
bool isValid = validator.validateFilterConfig("config.ini");
if (!isValid) {
    qDebug() << "配置文件验证失败:" << validator.getErrors();
}
```

### 参数范围检查
| 参数 | 有效范围 | 推荐值 |
|------|----------|--------|
| strength | 0.0-1.0 | 1.0 |
| kernel_size | 3,5,7,9... | 3 |
| sigma | 0.1-10.0 | 1.0-2.0 |
| sigma_color | 10.0-200.0 | 50.0-100.0 |
| sigma_space | 10.0-200.0 | 50.0-100.0 |

### 性能监控
```ini
[Debug]
# 启用性能监控
enable_timing=true
log_level=info
output_intermediate=false
```

## 故障排除

### 常见配置错误

#### 1. 核大小错误
```ini
# 错误：偶数核大小
kernel_size=4

# 正确：奇数核大小
kernel_size=3
```

#### 2. 强度参数超范围
```ini
# 错误：超出范围
strength=1.5

# 正确：在有效范围内
strength=1.0
```

#### 3. 预设名称错误
```ini
# 错误：不存在的预设
preset=unknown_preset

# 正确：有效的预设名称
preset=center_weighted
```

### 调试技巧

#### 启用详细日志
```ini
[Debug]
log_level=debug
enable_filter_logs=true
```

#### 分步验证
```cpp
// 逐个启用滤波器进行测试
config.weighted_avg_enabled = true;
config.convolution_enabled = false;
config.median_enabled = false;
```

## 版本兼容性

### v1.4.4更新内容
- 🔧 **边界处理优化**：WeightedAverage和Convolution滤波器
- 🔧 **配置流程修复**：所有预设现在可通过配置文件使用
- ✨ **新增验证**：更严格的参数验证
- 📈 **性能提升**：边缘处理准确性提升40-90%

### 迁移指南
从旧版本升级到v1.4.4：
1. ✅ **配置文件**：无需修改，完全兼容
2. ✅ **API调用**：无破坏性变更
3. ✅ **预期结果**：边缘效果会有改善

## 关联文档

- [[WeightedAverageFilter使用指南]] - 加权均值滤波器详细说明
- [[边界处理最佳实践]] - 边界处理技术详解
- [[性能优化指南]] - 滤波器性能优化建议
- [[../issues/weighted_average_boundary_fix.md]] - v1.4.4修复详情

---

**配置支持**: 如需配置帮助请参考具体滤波器使用指南  
**技术支持**: 遇到问题请查看故障排除章节或联系开发团队
