# 产品开发完整功能集成MCP服务器

这是产品开发框架的统一MCP服务器，集成了所有核心功能，提供一站式的产品开发支持。

## 🚀 功能特性

### 核心功能模块

1. **项目初始化** - 自动创建标准项目结构
2. **配置管理** - 配置文件的加载、保存和管理
3. **需求管理** - 需求导入、分析和追踪
4. **工作流管理** - 自定义工作流创建和执行
5. **可视化支持** - 架构图和流程图生成
6. **文档管理** - 文档模板和自动生成
7. **质量控制** - 代码审查和测试管理

### 集成优势

- **统一接口** - 一个MCP服务器提供所有功能
- **数据一致性** - 各模块间数据自动同步
- **简化配置** - 只需配置一个MCP服务器
- **性能优化** - 减少多服务器通信开销

## 📁 目录结构

```
product-development-complete/
├── server.py              # 统一MCP服务器
├── tools/                 # 工具模块集合
├── prompts/               # AI提示模板
│   └── templates.md
├── docs/                  # 文档
│   └── usage.md
├── requirements.txt       # Python依赖
└── README.md             # 项目说明
```

## 🛠️ 安装配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. Cursor配置

在 `.cursor/mcp.json` 中添加：

```json
{
  "mcpServers": {
    "prod-dev": {
      "command": "python",
      "args": ["./unified/product-development-complete/server.py"],
      "description": "产品开发完整功能集成MCP服务器"
    }
  }
}
```

## 🔧 工具使用示例

### 项目管理

#### init_project_tool
初始化新的产品项目

```python
init_project_tool(
    project_name="my-product",
    project_path="/path/to/projects",
    structure_type="single_layer"
)
```

### 配置管理

#### load_config_tool / save_config_tool
配置文件操作

```python
# 加载配置
config = load_config_tool("config/project.json", "json")

# 保存配置
save_config_tool("config/new_config.yaml", config_data, "yaml")
```

### 需求管理

#### import_requirements_tool / analyze_requirements_tool
需求导入和分析

```python
# 导入需求
import_requirements_tool("requirements.xlsx", "market")

# 分析需求
analysis = analyze_requirements_tool("requirements/", "comprehensive")
```

### 可视化

#### gen_arch_diag_tool
生成架构图

```python
gen_arch_diag_tool(
    "/path/to/project",
    "system"
)
```

## 📚 资源和提示

### 资源访问

- `product-dev://templates` - 获取所有模板列表

### AI提示

- `product_development_prompt` - 生成开发阶段提示

## 🔧 高级配置

### 环境变量

```bash
# 设置脚本基础路径
export SCRIPTS_BASE="/path/to/scripts"

# 设置配置目录
export CONFIG_DIR="/path/to/config"
```

### 自定义配置

创建 `config/server_config.json`:

```json
{
  "modules": {
    "project_init": true,
    "config_management": true,
    "requirements": true,
    "workflow": true,
    "visualization": true
  },
  "paths": {
    "scripts_base": "./scripts",
    "config_base": "./config",
    "output_base": "./output"
  }
}
```

## 🚨 故障排除

### 常见问题

1. **脚本路径错误**
   - 检查SCRIPTS_BASE路径设置
   - 确认相关脚本文件存在

2. **权限问题**
   - 确保有文件读写权限
   - 检查目录创建权限

3. **依赖缺失**
   - 运行 `pip install -r requirements.txt`
   - 检查Python版本兼容性

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 性能优化

### 缓存配置

```python
# 启用配置缓存
ENABLE_CONFIG_CACHE = True
CONFIG_CACHE_TTL = 300  # 5分钟
```

### 并发处理

```python
# 启用异步处理
ENABLE_ASYNC = True
MAX_CONCURRENT_TASKS = 5
```

## 🤝 扩展开发

### 添加新功能模块

1. 在 `tools/` 目录创建新的工具模块
2. 在 `server.py` 中导入并注册工具
3. 添加相应的测试和文档

### 自定义工具

```python
@mcp.tool()
def custom_tool(param1: str, param2: int) -> str:
    """自定义工具实现"""
    # 实现逻辑
    return "处理结果"
```

## 📄 许可证

本项目采用 MIT 许可证。

## 🔗 相关链接

- [产品开发框架文档](../../docs/)
- [MCP协议规范](https://github.com/modelcontextprotocol)
- [使用指南](docs/usage.md)
