#ifndef _TRANSFER_B_H_
#define _TRANSFER_B_H_

/*****
 * power on:
 *    send: AA 20 00 00 00
 *    ack: AA 20 0D 0A 50 6F 77 65 72 4F 4E 0D 0A
 * power off:
 *    send: AA 21 00 00 00
 *    ack: AA 21 0D 0A 50 6F 77 65 72 4F 46 46 0D 0A
 */

#include "ITestBoard.h"

class CTransferB:public ITestBoard{
public:
  CTransferB(IComm *ctrl_port_);
  ~CTransferB();

  uint getUartBaud() override;

  bool start(const uint8_t& id, const QByteArray &data) override; //开始
  bool stop(const QByteArray &data) override; //停止

  bool dataParsing(QByteArray str, const int &length) override;
signals:


private:
  IComm *im_ctrl_port_ = nullptr;

  static const uint8_t start_cmd[5];
  static const uint8_t stop_cmd[5];

//  QByteArray m_str_send; //指令数据


private:

};

#endif
