#ifndef CLEN_ENTER_DATA_MES_H
#define CLEN_ENTER_DATA_MES_H

#include "clenEnterDataMesOpt.h"
#include "lensReadIni.h"
#include "tableViewModule.h"
#include <QDockWidget>


namespace Ui {
class clenEnterDataMes;
}

class clenEnterDataMes : public QDockWidget {
    Q_OBJECT

  public:
    explicit clenEnterDataMes(QWidget *parent = nullptr);
    ~clenEnterDataMes();

  signals:
    void windowCloseSiganl(bool);
    //    void lensAdjustCloseSiganl(bool);


  private:
    Ui::clenEnterDataMes *ui;
    CTableViewModule *    mc_grey_map_ = nullptr;

    NClenEnterDataMes::StUiConfig *mst_config_   = nullptr;
    clenEnterDataMesOpt *          mc_operation_ = nullptr;
    //    clenEnterDataMes *mc_lens_data_mes_ = nullptr;

    //* ui config
    //    void greyMapShowInit(QTableWidget *table_, const uint8_t &xlen, const uint8_t &ylen);
    void updateConfig(NClenEnterDataMes::StUiConfig *config_);
    //    void greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data);

    //* 状态栏update
    void resultClean(void);
    void lenMesDataFB(bool closed);

  private slots:
    void portListShow(QStringList *port_list, bool port_flag);
    void readyShow(bool is_open);
    void dataAckShow(const uint &max, const QVector<QVector<uint32_t>> &matrix);
    void processTaskInfoShow_slot(const bool &is_error, const QString &info);
    void compAckShow(bool is_comp);
    void resultShow(EResult result, const uint8_t &result_index);

  private slots:
    void on_reload_clicked();

    void on_portBox_currentIndexChanged(int index);

  protected:
    virtual void closeEvent(QCloseEvent *event) override;
};

#endif  // clenEnterDataMes_H
