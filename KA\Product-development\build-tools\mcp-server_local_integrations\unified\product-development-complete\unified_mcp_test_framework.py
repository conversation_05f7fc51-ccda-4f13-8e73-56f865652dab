#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一MCP服务器测试框架 - 能够测试AI对话框中的MCP服务器调用
"""

import asyncio
import json
import sys
import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class UnifiedMCPTestFramework:
    """统一MCP测试框架"""
    
    def __init__(self):
        self.test_results = []
        self.test_base_dir = current_dir / "test_projects"
        self.test_base_dir.mkdir(exist_ok=True)
        
    def log_test_result(self, tool_name: str, success: bool, details: Dict[str, Any]):
        """记录测试结果"""
        result = {
            "tool_name": tool_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        self.test_results.append(result)
        
        status = "✅ 成功" if success else "❌ 失败"
        print(f"[{tool_name}] {status}")
        if not success and "error" in details:
            print(f"  错误: {details['error']}")
    
    async def test_init_project_tool(self, project_name: str = "unified_test_project") -> bool:
        """测试项目初始化工具"""
        print(f"\n🧪 测试 init_project_tool")
        print("="*60)
        
        try:
            # 清理之前的测试
            test_project_path = self.test_base_dir / project_name
            if test_project_path.exists():
                shutil.rmtree(test_project_path)
                print(f"🧹 清理之前的测试项目: {test_project_path}")
            
            # 导入MCP服务器工具
            from server_silent import init_project_tool
            
            # 调用工具
            result = await init_project_tool(
                project_name=project_name,
                project_path=str(test_project_path),
                structure_type="single_layer",
                description="统一测试项目",
                version="1.0.0"
            )
            
            # 验证结果
            success = result.get('success', False)
            details = {"result": result}
            
            if success:
                # 详细验证
                verification_results = self._verify_project_creation(test_project_path)
                details.update(verification_results)
                success = verification_results.get("all_checks_passed", False)
            
            self.log_test_result("init_project_tool", success, details)
            return success
            
        except Exception as e:
            details = {"error": str(e), "exception_type": type(e).__name__}
            self.log_test_result("init_project_tool", False, details)
            return False
    
    def _verify_project_creation(self, project_path: Path) -> Dict[str, Any]:
        """验证项目创建结果"""
        verification = {
            "project_dir_exists": project_path.exists(),
            "readme_exists": (project_path / "README.md").exists(),
            "canvas_exists": (project_path / "product.canvas").exists(),
            "config_dir_exists": (project_path / "config").exists(),
            "config_files_count": 0,
            "config_files": [],
            "key_config_exists": False,
            "key_config_valid": False
        }
        
        # 检查配置文件
        config_dir = project_path / "config"
        if config_dir.exists():
            config_files = list(config_dir.glob("*.json"))
            verification["config_files_count"] = len(config_files)
            verification["config_files"] = [f.name for f in config_files]
            
            # 检查关键配置文件
            key_config = config_dir / "document_links_config.json"
            if key_config.exists():
                verification["key_config_exists"] = True
                try:
                    with open(key_config, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    verification["key_config_valid"] = 'document_scanning' in config_data
                except Exception as e:
                    verification["key_config_error"] = str(e)
        
        # 综合判断
        verification["all_checks_passed"] = (
            verification["project_dir_exists"] and
            verification["readme_exists"] and
            verification["config_dir_exists"] and
            verification["config_files_count"] >= 10 and
            verification["key_config_exists"] and
            verification["key_config_valid"]
        )
        
        return verification
    
    async def test_path_resolution(self) -> bool:
        """测试路径解析功能"""
        print(f"\n🧪 测试路径解析功能")
        print("="*60)
        
        try:
            from server_silent import init_project_tool
            
            # 测试相对路径解析
            relative_path = "example/path_test_project"
            result = await init_project_tool(
                project_name="path_test_project",
                project_path=relative_path,
                structure_type="single_layer"
            )
            
            success = result.get('success', False)
            details = {
                "input_path": relative_path,
                "resolved_path": result.get('smart_detected_path', 'Unknown'),
                "result": result
            }
            
            self.log_test_result("path_resolution_test", success, details)
            return success
            
        except Exception as e:
            details = {"error": str(e), "exception_type": type(e).__name__}
            self.log_test_result("path_resolution_test", False, details)
            return False
    
    async def test_pythonpath_setting(self) -> bool:
        """测试PYTHONPATH设置功能"""
        print(f"\n🧪 测试PYTHONPATH设置功能")
        print("="*60)
        
        try:
            # 清除PYTHONPATH环境变量
            original_pythonpath = os.environ.get('PYTHONPATH')
            if 'PYTHONPATH' in os.environ:
                del os.environ['PYTHONPATH']
            
            # 重新导入服务器模块以触发PYTHONPATH设置
            import importlib
            if 'server_silent' in sys.modules:
                importlib.reload(sys.modules['server_silent'])
            else:
                import server_silent
            
            # 检查PYTHONPATH是否被正确设置
            current_pythonpath = os.environ.get('PYTHONPATH')
            success = current_pythonpath is not None and 'scripts' in current_pythonpath
            
            details = {
                "original_pythonpath": original_pythonpath,
                "current_pythonpath": current_pythonpath,
                "pythonpath_set": success
            }
            
            # 恢复原始PYTHONPATH
            if original_pythonpath:
                os.environ['PYTHONPATH'] = original_pythonpath
            
            self.log_test_result("pythonpath_setting_test", success, details)
            return success
            
        except Exception as e:
            details = {"error": str(e), "exception_type": type(e).__name__}
            self.log_test_result("pythonpath_setting_test", False, details)
            return False
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print("🧪 统一MCP服务器测试框架")
        print("="*80)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试目录: {self.test_base_dir}")
        
        # 运行各项测试
        tests = [
            ("PYTHONPATH设置", self.test_pythonpath_setting()),
            ("路径解析功能", self.test_path_resolution()),
            ("项目初始化", self.test_init_project_tool())
        ]
        
        results = {}
        for test_name, test_coro in tests:
            print(f"\n📋 开始测试: {test_name}")
            try:
                result = await test_coro
                results[test_name] = result
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                results[test_name] = False
        
        # 生成测试报告
        return self._generate_test_report(results)
    
    def _generate_test_report(self, results: Dict[str, bool]) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "success_rate": f"{success_rate:.1f}%"
            },
            "test_results": results,
            "detailed_results": self.test_results,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"\n📊 测试总结:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试: {passed_tests}")
        print(f"   失败测试: {total_tests - passed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        return report

async def main():
    """主测试函数"""
    framework = UnifiedMCPTestFramework()
    report = await framework.run_comprehensive_test()
    
    # 保存测试报告
    report_file = current_dir / f"unified_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 测试报告已保存: {report_file}")
    
    return report

if __name__ == "__main__":
    asyncio.run(main())
