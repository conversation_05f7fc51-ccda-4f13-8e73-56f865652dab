#ifndef _ITOP_BOARD_H_
#define _ITOP_BOARD_H_

#include <QObject>
#include <QByteArray>
#include <QMap>
#include "IComm.h"
#include "IProtocol.h"
#include "IFaculaAdjust.h"

class ITopBoard:public QObject {
    Q_OBJECT
public:
    ITopBoard();
    virtual ~ITopBoard(){};

    enum ECommStep {
        eCHIP_ID,
        eVERSION,
        eMODE_CHANGE,
        eMAP_DATA,
    };




protected:
    QMap<QString, QByteArray> m_cmd = {
        {"chipId", {0}}, //

        {"laserOn", {0}},
        {"laserOff", {0}},


        {"stop", {0}},
        {"test", {0}},
    };
    IProtocol *m_protocol_ = nullptr; //一个设备一种协议

public:
    QByteArray  m_strPre;

    virtual QByteArray portDataRead(void) = 0;
    virtual void icom_change_interface(IComm* port_) = 0;
    virtual bool unlockModule(void) = 0;
    virtual bool modeChange(const uint16_t &mode) = 0;

    virtual bool changeRigster() = 0;
    virtual bool readInfo(const uint8_t &id, const uint16_t &data) = 0;

    virtual bool interactionParsing(QByteArray str, int length) = 0; //交互指令解析
    virtual bool greyParsing(QByteArray str, int length) = 0; //灰度图
    virtual bool histPrasing(QByteArray str, int length) = 0; //柱图
    virtual bool cloudPrasing(QByteArray str, int length) = 0; //点云
};

#endif
