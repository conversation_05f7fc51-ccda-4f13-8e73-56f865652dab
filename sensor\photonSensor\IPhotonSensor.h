#ifndef _IPHOTON_SENSOR_H_
#define _IPHOTOM_SENSOR_H_

#include <QByteArray>
#include <QMap>
#include "ITable.h"


class IPhotonSensor {
public:
    IPhotonSensor(){};
    virtual ~IPhotonSensor(){};

    //* MP数量
    enum ESensorModel {
        eYC           = 0, //宇称
        e4300         = 1, //vi
        e4302         = 2, //vi
        eFS_FL1015    = 3, //阜时
    };

//    //* MP顺序方向
//    enum EMpOrder {
//        left_2_right    = 0,
//        up_2_down       ,
//        right_2_left    ,
//        down_2_up       ,
//    };

    enum EFaculaForm {
        eFOCUS,
        eDISPERSE, //
    };

    //*
    typedef struct {
        uint8_t                             xlens; //SPAD 通道排布
        uint8_t                             ylens; //
        ITable::EMpOrder                    order; //通道编号顺序
    } StSensorInfo;

    static const QMap<ESensorModel, StSensorInfo> sm_mpNum_link_dimension;

protected:

};

#endif
