#ifndef _SAVE_EXCEL_H_
#define _SAVE_EXCEL_H_

#include "ISaveFile.h"

class CSaveExcel:public ISaveFile
{
public:
  CSaveExcel();
  ~CSaveExcel();

  void createFile(const QString &loc_name, const QString &fName, QMap<QString,QVariant> data) override;
  void createOneFile(const QString &loc_name, const QString &fName, QMap<QString, QVariant> data) override;
  void createRandomFile(void) override;
  void saveFile(const QString &loc_name, const QString &fName, const QMap<QString,QVariant> &data) override;
  void writeFile(QMap<QString, QVariant> data) override;
};

#endif // PARAMLOAD_H
