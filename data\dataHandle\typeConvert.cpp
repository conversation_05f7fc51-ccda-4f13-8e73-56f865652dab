#include "typeConvert.h"

namespace NsTypeConvert {

void FloatToByte(float floatNum, unsigned char *byteArry) {
    char *pchar = (char *)&floatNum;
    for (int i = 0; i < 4; i++) {
        *byteArry = *pchar;
        pchar++;
        byteArry++;
    }
}

char ConvertCharToHex(char ch) {
    if ((ch >= '0') && (ch <= '9'))
        return ch - 0x30;
    else if ((ch >= 'A') && (ch <= 'F'))
        return ch - 'A' + 10;
    else if ((ch >= 'a') && (ch <= 'f'))
        return ch - 'a' + 10;
    else
        return ch - ch;  //不在0-f范围内的会发送成0
}

void stringToHex(QString str, QByteArray &send_data) {
    int hexdata, lowhexdata;
    int hexdatalen = 0;
    int len        = str.length();
    int numb       = len % 2;
    len            = len + numb;
    send_data.resize(len / 2 + numb);
    char lstr, hstr;
    for (int i = 0; i < len;) {
        hstr = str[i].toLatin1();
        if (hstr == ' ') {
            i++;
            continue;
        }
        i++;
        if (i >= len)
            break;
        lstr       = str[i].toLatin1();
        hexdata    = ConvertCharToHex(hstr);
        lowhexdata = ConvertCharToHex(lstr);
        if ((hexdata == 16) || (lowhexdata == 16))
            break;
        else
            hexdata = hexdata * 16 + lowhexdata;
        i++;
        send_data[hexdatalen] = (char)hexdata;
        hexdatalen++;
    }
    send_data.resize(hexdatalen);
}

//-------------------------------- QbyteArray ------------------------
QByteArray arrayToByteArray(const uint8_t *input_) {
    QByteArray out_data;
    out_data.clear();

    memcpy(out_data.data(), (char *)input_, sizeof(*input_));

    return out_data;
}

QByteArray enumToByteArray(const uint16_t &input_data) {
    QByteArray out_data;

    out_data.append((input_data >> 8) & 0xff);
    out_data.append(input_data & 0xff);

    //    uint8_t len = sizeof(input_data);
    //    out_data.resize(len);
    //    memcpy(out_data.data(), &input_data, len);

    return out_data;
}


QByteArray uintArrayToByteArray(uint16_t *input_data_) {
    QByteArray out_data;

    uint8_t len = sizeof(*input_data_);
    out_data.resize(len);
    memcpy(out_data.data(), input_data_, len);

    return out_data;
}

QString byteArrayToString(const QByteArray &ba) {
    QString str;
    for (int i = 0; i < ba.size(); i++) {
        str += QString("%1").arg((unsigned char)ba.at(i), 2, 16, QLatin1Char('0')) + ' ';  //加','会导致保存到excel自动换行
    }
    return str;
}

}  // namespace NsTypeConvert