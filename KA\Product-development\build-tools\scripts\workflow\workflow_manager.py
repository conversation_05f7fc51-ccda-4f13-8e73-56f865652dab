#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工作流管理器
负责协调整个产品开发工作流的执行，实现组件化、松耦合的流程管理
"""

import os
import sys
import json
import logging
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

class WorkflowManager:
    """Manages the product development workflow based on configuration."""
    
    def __init__(self, config_file: str = "config/workflow_config.json"):
        """Initialize the workflow manager with the given configuration."""
        self.config_file = config_file
        self.root_path = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        self.setup_logging()
        self.config = self.load_config()
        self.connections = self.config.get("workflow", {}).get("connections", [])
        logger = logging.getLogger("WorkflowManager")
        logger.info(f"Workflow Manager initialized with {len(self.connections)} connections")
    
    def setup_logging(self) -> None:
        """Set up logging for the workflow manager."""
        # Ensure logs directory exists
        logs_dir = "logs"
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(logs_dir, "workflow_manager.log"), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self) -> Dict:
        """Load the workflow configuration from the JSON file."""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info(f"Configuration loaded from {self.config_file}")
                return config
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            return {"workflow": {"connections": [], "components": [], "mcp_servers": []}}
    
    def run_workflow(self) -> None:
        """Run the complete workflow."""
        self.logger.info("开始执行产品开发工作流")
        
        components = self.config['workflow']['components']
        
        for component in components:
            self.logger.info(f"执行组件: {component['name']}")
            
            if self.execute_component(component):
                self.logger.info(f"组件 {component['name']} 执行成功")
                self.update_component_status(component['name'], 'completed')
            else:
                self.logger.error(f"组件 {component['name']} 执行失败")
                self.update_component_status(component['name'], 'failed')
                break
        
        self.logger.info("工作流执行完成")
    
    def execute_component(self, component: Dict) -> bool:
        """Execute a single workflow component."""
        try:
            script_path = component['script']
            if os.path.exists(script_path):
                result = subprocess.run(['python', script_path], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.logger.info(f"脚本 {script_path} 执行成功")
                    return True
                else:
                    self.logger.error(f"脚本 {script_path} 执行失败: {result.stderr}")
                    return False
            else:
                self.logger.warning(f"脚本 {script_path} 不存在，跳过执行")
                return True
        except Exception as e:
            self.logger.error(f"执行组件 {component['name']} 时发生错误: {str(e)}")
            return False
    
    def update_component_status(self, component_name: str, status: str) -> None:
        """Update the status of a component."""
        status_file = 'reports/workflow_status.json'
        
        if os.path.exists(status_file):
            with open(status_file, 'r', encoding='utf-8') as f:
                workflow_status = json.load(f)
        else:
            workflow_status = {
                'workflow_status': {
                    'last_updated': datetime.now().isoformat(),
                    'components': {}
                }
            }
        
        workflow_status['workflow_status']['components'][component_name] = {
            'status': status,
            'updated_at': datetime.now().isoformat()
        }
        workflow_status['workflow_status']['last_updated'] = datetime.now().isoformat()
        
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(workflow_status, f, ensure_ascii=False, indent=2)
    
    def get_workflow_status(self) -> Dict:
        """Get the status of the workflow."""
        status_file = 'reports/workflow_status.json'
        if os.path.exists(status_file):
            with open(status_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def run_mcp_servers(self) -> None:
        """Start configured MCP servers."""
        mcp_servers = self.config['workflow'].get('mcp_servers', [])
        
        for server in mcp_servers:
            self.logger.info(f"启动MCP服务器: {server['name']}")
            # 这里可以添加MCP服务器启动逻辑
            # 目前只记录日志
    
    def generate_workflow_report(self) -> None:
        """Generate a report of the workflow execution."""
        status = self.get_workflow_status()
        if not status:
            return
        
        report = f"""# 工作流执行报告

## 概览

- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 工作流版本: {self.config['workflow']['version']}
- 项目类型: {self.config['workflow']['project_type']}

## 组件执行状态

"""
        
        components = status['workflow_status']['components']
        for name, info in components.items():
            status_icon = "✅" if info['status'] == 'completed' else "❌"
            report += f"- {status_icon} {name}: {info['status']} (更新时间: {info['updated_at']})\n"
        
        report += f"""
## MCP服务器配置

"""
        
        mcp_servers = self.config['workflow'].get('mcp_servers', [])
        for server in mcp_servers:
            report += f"- **{server['name']}**: {server['purpose']}\n"
        
        with open('reports/workflow_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info("工作流报告已生成: reports/workflow_report.md")

def main():
    parser = argparse.ArgumentParser(description="工作流管理器")
    parser.add_argument("--config", default="config/workflow_config.json", help="工作流配置文件路径")
    parser.add_argument("--action", choices=["run", "status", "report"], default="run", help="执行的操作")
    
    args = parser.parse_args()
    
    wm = WorkflowManager(args.config)
    
    if args.action == "run":
        wm.run_workflow()
        wm.generate_workflow_report()
    elif args.action == "status":
        status = wm.get_workflow_status()
        if status:
            print(json.dumps(status, ensure_ascii=False, indent=2))
        else:
            print("未找到工作流状态信息")
    elif args.action == "report":
        wm.generate_workflow_report()

if __name__ == "__main__":
    main() 