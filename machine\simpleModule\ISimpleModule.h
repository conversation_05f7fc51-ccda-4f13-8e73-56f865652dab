#ifndef _I_SIMPLE_MODULE_H_
#define _I_SIMPLE_MODULE_H_

#include <QObject>
#include <QMap>
#include <QByteArray>
#include "IComm.h"
#include "IProtocol.h"
#include "IStringPtl.h"


class ISimpleModule:public QObject
{
    Q_OBJECT
public:
    ISimpleModule();
    virtual ~ISimpleModule(){};

    QByteArray  m_strPre;

    //* 交互步骤
    enum ECommStep{
        eSTART_ACK,
        eMAIN_INFO_ACK, //
        eSTOP_ACK,
//        eDATA_STEP,
//        eCOMPLETE_STEP,
    };

protected:
    QMap<QString, QByteArray> m_cmd =
    {
        {"start", {0}},
        {"stop", {0}},
        {"test", {0}},
    };

    IProtocol* m_protocol_ = nullptr; //一个设备一种协议
    IStringPtl *m_string_ptl = nullptr;

public:
    virtual uint getUartBaud(void) = 0;

    virtual bool start(const uint8_t& id, const QByteArray &data) = 0; //开始
    virtual bool stop(const QByteArray &data) = 0; //停止

    virtual QByteArray portDataRead() = 0; //
    virtual bool readInfo(const uint8_t &id, const uint16_t &data) = 0;

    virtual void changeIcomInterface(IComm* port_) = 0;
    virtual bool interactionParsing(QByteArray str, int length) = 0; //交互指令解析
    virtual bool dataParsing(QByteArray str, const int &length) = 0; //数据解析
    //signals:
    //  virtual void signalEmit(void) = 0; //无法实现多态
};


#endif
