#!/usr/bin/env bash
# 文件：${PRODUCT_DEVELOP_DIR}/scripts/canvas/on_canvas_save.sh
# 用途：检测product.canvas的edges变更并自动同步到INDEX文档
# 作者：产品开发团队
# 版本：2.0

# =============== 配置区域 ===============
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="${PRODUCT_DEVELOP_DIR:-$(dirname "$(dirname "$SCRIPT_DIR")")}"
LOG_DIR="${PROJECT_DIR}/logs"
CACHE_DIR="${HOME}/.obsidian_canvas_cache"
LOG_FILE="${LOG_DIR}/canvas_sync.log"

# 创建必要目录
mkdir -p "$LOG_DIR" "$CACHE_DIR"

# =============== 日志函数 ===============
log_info() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] INFO: $1" | tee -a "$LOG_FILE"
}

log_error() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] ERROR: $1" | tee -a "$LOG_FILE" >&2
}

log_debug() {
    if [[ "${DEBUG_MODE:-0}" == "1" ]]; then
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        echo "[$timestamp] DEBUG: $1" | tee -a "$LOG_FILE"
    fi
}

# =============== 参数验证 ===============
FILE_PATH="$1"

# 检查参数
if [[ -z "$FILE_PATH" ]]; then
    log_error "错误: 未提供文件路径参数"
    log_error "用法: $0 <canvas_file_path>"
    exit 1
fi

# 检查文件是否存在
if [[ ! -f "$FILE_PATH" ]]; then
    log_error "错误: 文件不存在 - $FILE_PATH"
    exit 1
fi

# =============== 文件类型检查 ===============
FILE_NAME="$(basename "$FILE_PATH")"
FILE_EXT="${FILE_NAME##*.}"

# 仅处理Canvas文件
if [[ "$FILE_EXT" != "canvas" ]]; then
    log_debug "跳过非Canvas文件: $FILE_NAME"
    exit 0
fi

# 检查是否为产品Canvas文件
if [[ "$FILE_NAME" != "product.canvas" ]] && [[ "$FILE_NAME" != *"product"* ]]; then
    log_debug "跳过非产品Canvas文件: $FILE_NAME"
    exit 0
fi

log_info "检测到Canvas文件变化: $FILE_NAME"

# =============== 环境检查 ===============
# 检查Python环境
if ! command -v python &> /dev/null; then
    log_error "错误: Python未安装或不在PATH中"
    exit 1
fi

# 检查项目脚本
SYNC_SCRIPT="${PROJECT_DIR}/scripts/canvas/auto_link_documents.py"
if [[ ! -f "$SYNC_SCRIPT" ]]; then
    # 尝试备用脚本
    SYNC_SCRIPT="${PROJECT_DIR}/scripts/canvas/sync_canvas_obsidian.py"
    if [[ ! -f "$SYNC_SCRIPT" ]]; then
        log_error "错误: 找不到同步脚本"
        log_error "查找路径: ${PROJECT_DIR}/scripts/canvas/"
        exit 1
    fi
fi

# =============== edges变化检测 ===============
CACHE_FILE="${CACHE_DIR}/${FILE_NAME}.edges"
TMP_DIFF="/tmp/canvas_edges.diff"
CURRENT_EDGES_FILE="/tmp/current_edges.json"

# 提取当前Canvas的edges部分
extract_edges() {
    local canvas_file="$1"
    local output_file="$2"
    
    if command -v jq &> /dev/null; then
        # 使用jq提取edges（更可靠）
        jq '.edges // []' "$canvas_file" > "$output_file" 2>/dev/null
    else
        # 使用Python提取edges（备用方案）
        python3 -c "
import json, sys
try:
    with open('$canvas_file', 'r', encoding='utf-8') as f:
        data = json.load(f)
    edges = data.get('edges', [])
    with open('$output_file', 'w', encoding='utf-8') as f:
        json.dump(edges, f, sort_keys=True, indent=2)
except Exception as e:
    sys.exit(1)
" || {
            log_error "提取edges失败: $canvas_file"
            exit 1
        }
    fi
}

# 提取当前edges
if ! extract_edges "$FILE_PATH" "$CURRENT_EDGES_FILE"; then
    log_error "无法提取Canvas edges"
    exit 1
fi

# 检查是否有缓存
NEEDS_SYNC=true
if [[ -f "$CACHE_FILE" ]]; then
    # 比较edges是否发生变化
    if diff -q "$CACHE_FILE" "$CURRENT_EDGES_FILE" > /dev/null 2>&1; then
        log_debug "Canvas edges未发生变化，跳过同步"
        NEEDS_SYNC=false
    else
        log_info "检测到Canvas edges变化"
        # 记录变化详情（用于调试）
        if [[ "${DEBUG_MODE:-0}" == "1" ]]; then
            diff -u "$CACHE_FILE" "$CURRENT_EDGES_FILE" > "$TMP_DIFF" 2>/dev/null || true
            log_debug "变化详情已保存到: $TMP_DIFF"
        fi
    fi
else
    log_info "首次检测Canvas文件，将执行同步"
fi

# =============== 执行同步 ===============
if [[ "$NEEDS_SYNC" == "true" ]]; then
    log_info "开始同步Canvas edges到INDEX文档..."
    
    # 切换到项目目录（确保相对路径正确）
    cd "$PROJECT_DIR" || {
        log_error "无法切换到项目目录: $PROJECT_DIR"
        exit 1
    }
    
    # 执行同步脚本
    SYNC_START_TIME=$(date +%s)
    
    if python "$SYNC_SCRIPT" --sync-from-canvas "$FILE_PATH"; then
        SYNC_END_TIME=$(date +%s)
        SYNC_DURATION=$((SYNC_END_TIME - SYNC_START_TIME))
        
        log_info "✓ Canvas edges同步成功 (耗时: ${SYNC_DURATION}秒)"
        
        # 更新缓存
        cp "$CURRENT_EDGES_FILE" "$CACHE_FILE"
        
        # 清理临时文件
        rm -f "$CURRENT_EDGES_FILE" "$TMP_DIFF"
        
        # 记录同步统计
        STATS_FILE="${LOG_DIR}/sync_stats.json"
        python3 -c "
import json, os
from datetime import datetime

stats_file = '$STATS_FILE'
stats = {}
if os.path.exists(stats_file):
    try:
        with open(stats_file, 'r') as f:
            stats = json.load(f)
    except:
        pass

stats['last_sync'] = datetime.now().isoformat()
stats['total_syncs'] = stats.get('total_syncs', 0) + 1
stats['last_duration'] = $SYNC_DURATION

with open(stats_file, 'w') as f:
    json.dump(stats, f, indent=2)
" || true
        
    else
        SYNC_END_TIME=$(date +%s)
        SYNC_DURATION=$((SYNC_END_TIME - SYNC_START_TIME))
        
        log_error "✗ Canvas edges同步失败 (耗时: ${SYNC_DURATION}秒)"
        log_error "请检查同步脚本和项目配置"
        
        # 不更新缓存，下次继续尝试同步
        rm -f "$CURRENT_EDGES_FILE" "$TMP_DIFF"
        exit 1
    fi
else
    # 清理临时文件
    rm -f "$CURRENT_EDGES_FILE"
fi

log_debug "Canvas同步脚本执行完成"
exit 0