#!/usr/bin/env python
# 脚本路径: scripts/requirements/link_requirements.py

import os
import re
import argparse

def link_requirements(parent_matrix, child_matrix, parent_id, child_id):
    """在上下层需求矩阵间建立链接"""
    # 更新父需求矩阵
    with open(parent_matrix, 'r', encoding='utf-8') as f:
        parent_content = f.read()
    
    # 在父需求中寻找并更新链接
    parent_pattern = rf"\| {parent_id} \|(.*?)\|(.*?)\|(.*?)\|(.*?)\|"
    parent_match = re.search(parent_pattern, parent_content, re.DOTALL)
    
    if parent_match:
        link_cell = parent_match.group(4).strip()
        if link_cell and link_cell != "-":
            if f"[{child_id}](#)" not in link_cell:
                updated_link = link_cell + f", [{child_id}](#)"
        else:
            updated_link = f"[{child_id}](#)"
        
        updated_row = f"| {parent_id} |{parent_match.group(1)}|{parent_match.group(2)}|{parent_match.group(3)}| {updated_link} |"
        parent_content = re.sub(parent_pattern, updated_row, parent_content)
        
        with open(parent_matrix, 'w', encoding='utf-8') as f:
            f.write(parent_content)
    
    # 更新子需求矩阵
    with open(child_matrix, 'r', encoding='utf-8') as f:
        child_content = f.read()
    
    # 在子需求中更新上层需求链接
    child_pattern = rf"\| {child_id} \|(.*?)\|(.*?)\|(.*?)\|"
    child_match = re.search(child_pattern, child_content, re.DOTALL)
    
    if child_match:
        updated_parent = f" [{parent_id}](#) "
        updated_row = f"| {child_id} |{updated_parent}|{child_match.group(2)}|{child_match.group(3)}|"
        child_content = re.sub(child_pattern, updated_row, child_content)
        
        with open(child_matrix, 'w', encoding='utf-8') as f:
            f.write(child_content)
    
    print(f"已建立需求链接: {parent_id} -> {child_id}")

def main():
    parser = argparse.ArgumentParser(description='建立需求间链接')
    parser.add_argument('--parent-matrix', required=True, help='上层需求矩阵路径')
    parser.add_argument('--child-matrix', required=True, help='下层需求矩阵路径')
    parser.add_argument('--parent-id', required=True, help='上层需求ID')
    parser.add_argument('--child-id', required=True, help='下层需求ID')
    args = parser.parse_args()
    
    link_requirements(
        args.parent_matrix, 
        args.child_matrix,
        args.parent_id,
        args.child_id
    )

if __name__ == "__main__":
    main() 