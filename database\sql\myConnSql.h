#ifndef _MYCONNSQL_H_
#define _MYCONNSQL_H_

#include <QDebug>
#include <QMessageBox>
#include <QtSql/QSqlDatabase>
#include <QtSql/QSqlError>
#include <QtSql/QSqlQuery>


class MyConnSql {  //:public QObject
                   //    Q_OBJECT
  public:
    explicit MyConnSql();
    ~MyConnSql();

    typedef struct {
        QString table_name;
        //        QSqlDatabase        db;
        QString conn;
    } ST_DATABASE;

    typedef struct {
        QString  conn_name;
        QString  host_name;
        uint32_t port;
        QString  base_name;
        QString  obdc_base_name;
        QString  user_name;
        QString  password;
    } StSqlConnInfo;

    //    static QString connection; //
    static const ST_DATABASE               m_motor_speed_db;    //电机转速
    static const ST_DATABASE               m_lidar_speed_db;    //雷达转速
    static const ST_DATABASE               m_turn_on_db;        // 启动时间
    static const ST_DATABASE               m_mirror_adjust_db;  //镜片调焦
    static QVector<MyConnSql::ST_DATABASE> m_database_v;

    static QSqlQuery excuteSQL(const QString &sqlcmd);
    static QSqlQuery excuteSQL(QSqlDatabase &db, const QString &sqlcmd);
    static bool      excuteSQL(QSqlQuery &query, const QString &sqlcmd);

    static bool originTableShow(QSqlDatabase &db, const QString &sqlcmd, const uint8_t &print_col_num);
    static bool queryOneValue(QSqlDatabase &db, const QString &sqlcmd, const QString &column, QString &get_data);
    static bool queryMaxValue(QSqlDatabase &db, const QString &sqlcmd, QString &get_data);

    static bool queryNValue(QSqlDatabase &db, const QString &sqlcmd, QMap<QString, QString> &m_get_values);
    static bool createConnection(const QString &connection, const QString &db_name);
    static bool createCloudConnection(QSqlDatabase &db, const StSqlConnInfo &sql_connect_info);
    static bool createCloudConnection();
    static void modelChange(QSqlDatabase *model);

  private:
};

#endif
