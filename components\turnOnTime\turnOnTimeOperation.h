#ifndef _TURN_ON_TIME_OPERATION_H_
#define _TURN_ON_TIME_OPERATION_H_

#include <QObject>
#include "processListB.h"

typedef struct{
    uint8_t                             mode_index; //
    uint8_t                             monitor_limit_time; //监测配置时间
    uint                                monitor_limit_cycle_num; //
}StConfig; //

typedef struct{
    QVector<uint16_t>                   data; //周期 转速
    QVector<uint16_t>                   distribute;
    uint16_t                            max;
    uint16_t                            min;
    uint16_t                            mean;
    uint16_t                            sigma;
    uint                                sum;
} StData;

typedef struct{
    int                     id;
    uint16_t                max_bias;
    uint16_t                min_bias;
    uint16_t                monitor_run_time; //监测运行时间
    bool                    result;
} StResult;

class CTurnOnTimeOperation:public QObject{
public:
  CTurnOnTimeOperation(StConfig *st_config_);
  ~CTurnOnTimeOperation();

private:
  void varibleInit();

private slots:
  //void dataReceive(monitor_process_two::PROCESS_STEP step, monitor_process_two::STEP_STATUS status, QByteArray data);
};



#endif
