#include <QDebug>
#include "sensorMiniYj.h"
#include "YJSensorP.h"
#include "qLog.h"


CSensorMiniYJ::CSensorMiniYJ(IComm *port_):
  im_port_(port_)
{
  m_protocol_ = new YJSensorP;

  //* 此处应该可以选择数据接收方式
  im_port_->setBufferSize(2000); //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

#ifdef INIT_OUTPUT
  qDebug() << "-i sensorYJ/ init: --------------------";
  qDebug() << "-i sensorYJ/ start cmd: " << m_cmd["start"];
  qDebug() << "-i sensorYJ/ stop cmd: " << m_cmd["stop"];
#endif
}

CSensorMiniYJ::~CSensorMiniYJ(){
  if(im_port_ != nullptr) delete im_port_;
  if(m_protocol_ != nullptr) delete m_protocol_;
}

QByteArray CSensorMiniYJ::portDataRead()
{
  return im_port_->read(1000);
}

void CSensorMiniYJ::icom_change_interface(IComm *port_){

}

bool CSensorMiniYJ::unlockModule() {

}

bool CSensorMiniYJ::modeChange(const uint16_t &mode){

}


bool CSensorMiniYJ::readInfo(const uint8_t &id, const uint16_t &data){

}

bool CSensorMiniYJ::changeRigster(){

}

bool CSensorMiniYJ::interactionParsing(QByteArray str, int length){

}

/**
 * @brief 光斑数据解析
 * @param str
 * @param length
 * @return
 */
bool CSensorMiniYJ::greyParsing(QByteArray str, int length)
{
//    Q_UNUSED(length);
  if(length == 0) return false;

    QByteArray array = m_strPre + str;

    if(array.size() == 0)
    {
        return false;
    }
    m_strPre.clear();
    if(array.size() < 100)
    {
        m_strPre = array;
        return false;
    }

    if(array.size() > 102400)//大于100k清零
    {
        array.clear();
        return false;
    }

    QByteArray strTmp;
    int wholeLength = array.length();
    //qDebug() << "size: " << wholeLength;
    int i=0;
    if(wholeLength >= 6)
    {
        for(i=0; i<wholeLength-6; i++)
        {
            if(array.at(i) == 0x11 && array.at(i+1) == 0x11 && array.at(i+2) == 0x11
               && array.at(i+3) == 0x11 && array.at(i+4) == 0x11 && array.at(i+5) == 0x11)
            {
                QByteArray tmpArr = array;
                tmpArr.remove(i,wholeLength-i);
                array.remove(0,i+6);
                //qDebug() << "size...: " << array.size();
                //补全一帧
//                g_byteArr.push_back(tmpArr);//(strTmp);
//                //qDebug() << "size0...: " << strTmp.size();
//                QMutexLocker lock(&muteFacula);
//                g_showFaculaWave.clear();
//                g_showFaculaWave = g_byteArr;
//                lock.unlock();
                //qDebug() <<  "length" << g_showFaculaWave.length();
                //清除
                m_strPre.clear();
//                g_byteArr.clear();
                m_strPre.push_back(array);

                return true;
            }
            //strTmp.push_back(array.at(i));
        }
        //array.remove(0,i);
        //g_byteArr.push_back(strTmp);
        m_strPre.clear();
        m_strPre.push_back(array);
       //qDebug() << "size3...: " << m_strPre.size();
        return false;
    }
    else
    {
        m_strPre.clear();
        m_strPre.push_back(array);
        return false;
    }

    return false;
}

/**
 * @brief 点云解析
 * @param str
 * @param length
 * @return
 */
bool CSensorMiniYJ::cloudPrasing(QByteArray str, int length){
  Q_UNUSED(length);
  QString ret;
  for (int i = 0; i < str.count(); ++i)
    {
      ret.append( QString("0x%1,").arg((quint8)str.at(i),2,16,QLatin1Char('0')));
    }
  qDebug() << "-yj-cloud -i: strSum" << ret;
  QByteArray stA = m_strPre + str;

  if(stA.length() < 10)
    {
      m_strPre = stA;
      if(m_strPre.size() > 102400)
        {
          m_strPre.clear();
        }
      return false;
    }
  m_strPre.clear();
  uint pointNumber = 0;
  //float spd = 0;
  float startAngle = 0;
  float endAngle = 0;

  uint8_t checkSumL = 0,checkSumH = 0;

  StPointCloud3D pointCld2d(0,0,0,0,0);
  uint16_t sum = 0;
  for(int n=0; n<stA.length(); n++)
    {
      if(stA.length() - n >= 85)
        {
          //1.0 接收设备数据        先移位再强制转化
          if((uchar)stA.at(n) == 0xAA && (uchar)stA.at(n+1) == 0x55 && (((uchar)stA.at(n+2))&0x01) == 0x00)
            {

              checkSumL = 0;
              checkSumH = 0;
              checkSumL ^= (uint8_t)stA.at(0 + n) ^(uint8_t)stA.at(2 + n) ^(uint8_t)stA.at(4 + n) ^(uint8_t)stA.at(6 + n);
              checkSumH ^= (uint8_t)stA.at(1 + n) ^(uint8_t)stA.at(3 + n) ^(uint8_t)stA.at(5 + n) ^(uint8_t)stA.at(7 + n);
              if((uchar)stA.at(n + 3) < 0x1A)
                {
                  for(int i=0; i<(uchar)stA.at(n+3); ++i)
                    {
                      checkSumL ^= (uint8_t)stA.at(3*i + 10 + n) ^(uint8_t)stA.at(3*i + 1 + 10 + n);
                      checkSumH ^= (uint8_t)stA.at(3*i + 2 + 10 + n) ;
                    }

                  if(checkSumL == (uint8_t)stA.at(8 + n) && checkSumH == (uint8_t)(stA.at(9 + n)))//校验通过
                    {
                      pointNumber = (uint8_t)stA.at(n+3);
                      //spd = (((uint8_t)stA.at(n+4) | uint16_t(stA.at(n+5)<<8))>>6)/60.0;

                      startAngle = (((uint8_t)stA.at(n+4) | (uint16_t(stA.at(n+5)<<8))) >> 1)/64.0;
                      endAngle = (((uint8_t)stA.at(n+6) | (uint16_t(stA.at(n+7)<<8))) >> 1)/64.0;
                      double incAngle = 0;
                      //2.0 计算角度
                      if(startAngle > endAngle)
                        {
                          incAngle = (360 - startAngle + endAngle)/(pointNumber-1);
                        }
                      else
                        {
                          incAngle = (endAngle - startAngle)/(pointNumber-1);
                        }
                      int m = n + 10;
                      for(uint i=0; i<pointNumber; i++)
                        {
                          //2.1 计算距离
                          quint16 down= ((uint8_t)stA.at(m+3*i+1)>>2)&0x3f;
                          uint16_t up = 0x00ff&stA.at(m+3*i+2);//左移高位会自动补1
                          up = up<<6;

                          float tmpdepth = up | down;
                          //float tmpdepth =  float(((uint8_t)(stA.at(m+3*i+1)>>2)&0x3f) | uint16_t((stA.at(m+3*i+2))<<6));
                          float tmpAngle = startAngle + i*incAngle ;

                          uint tmpIndensity = ((uint8_t)((stA.at(m+3*i+1)&0x03)<<6) | uint8_t(((stA.at(m+3*i))>>2)&0x3f));
                          tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                          //double tmpHeight = 0.0;

                          pointCld2d.m_angle = tmpAngle;
                          pointCld2d.m_distance = tmpdepth;
                          pointCld2d.m_intensity = tmpIndensity;
                          m_scanVec.push_back(pointCld2d);

                          if((uchar)stA.at(n+2) == 0x28)
                            {
                              m_scanPointCloud.m_hardAndSoftwareVersion = 0x28;
                            }

                        }
                      stA.remove(0,n+pointNumber*3+10);
                      n = -1;
                    }
                }
              else
                {
                  n += 3;
                  qDebug() << "-YJ_P: sensor data xor error";
                }
            }
          //1.1 接收设备的 信息
          else if((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n+1) == 0x5A && (uchar)stA.at(n+2) == 0x14 && (uchar)stA.at(n+3) == 0x00)
            {
              for(int i=0; i<27; i++)
                {
                  if(i == 4 || i == 5)
                    {
                      continue;
                    }
                  sum += (uint8_t)stA.at(i+n);
                }
              if(sum == ((uint8_t)stA.at(n+4) | uint16_t(stA.at(n+5)<<8)))
                {
                  m_scanPointCloud.m_version = (uint8_t)stA.at(n+26);
                  m_version = (uint8_t)stA.at(n+26);
                  stA.remove(0,n+27);
                  n = -1;
                }

            }
          //1.2 接收 固定的开始发送设备数据的 信息
          else if((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n+1) == 0x5A && (uchar)stA.at(n+6) == 0x81)
            {

            }
          //1.4 零度起始包
          else if((uchar)stA.at(n) == 0xAA && (uchar)stA.at(n+1) == 0x55 && (((uchar)stA.at(n+2))&0x01) == 0x01)
            {
              //4.0 校验 m_scanPointCloud
              checkSumL = 0;
              checkSumH = 0;
              checkSumL ^= (uint8_t)stA.at(0 + n) ^(uint8_t)stA.at(2 + n) ^(uint8_t)stA.at(4 + n) ^(uint8_t)stA.at(6 + n);
              checkSumH ^= (uint8_t)stA.at(1 + n) ^(uint8_t)stA.at(3 + n) ^(uint8_t)stA.at(5 + n) ^(uint8_t)stA.at(7 + n);

              if((uchar)stA.at(n+3) == 1)
                {
                  for(int i=0; i<(uchar)stA.at(n+3); i++)
                    {
                      checkSumL ^= (uint8_t)stA.at(3*i + 10 + n) ^(uint8_t)stA.at(3*i + 1 + 10 + n);
                      checkSumH ^= (uint8_t)stA.at(3*i + 2 + 10 + n) ;
                    }

                  if(checkSumL == (uint8_t)stA.at(8 + n) && checkSumH == (uint8_t)(stA.at(9 + n)))//校验通过
                    {
                      m_scanPointCloud.pointCloud2d = m_scanVec;
                      m_scanPointCloud.m_speed = (((uchar)stA.at(n+2))>>1)/10.0;
                      m_scanPointCloud.m_healthCode = m_healthCode;
                      m_scanPointCloud.m_mcuVoltage = m_mcuVoltage;
                      QByteArray speed_tmp;
                      speed_tmp.append((uchar)stA.at(n+2));
                      //emit dataOutput(MOTOR_MONITOR::data_step, MOTOR_MONITOR::data_flow, speed_tmp);
                      speed_tmp.clear();
                      m_scanVec.clear();
                      stA.remove(0,n+13);
                      m_strPre = stA;
                      //n = -1;
                      return true;
                    }
                  else
                    {
                      qDebug() << "checkSum err";
                      return false;
                    }
                }
              else
                {
                  n += 3;
                  qDebug() << "-motor: sensor F data xor e";
                }
            }
        }
      else
        {
          if(stA.length() - n >= 11)
            {
              if((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n+1) == 0x5A && (uchar)stA.at(n+2) == 0x04)
                {
                  //3.0 校验 m_scanPointCloud
                  uint8_t checkXor = 0;
                  for(int i=0; i<11; i++)
                    {
                      if(i == 7)
                        {
                          continue;
                        }
                      checkXor ^= (uint8_t)stA.at(n + i);
                    }
                  //3.1 获取健康信息 并删除该包长度信息
                  if(checkXor == (uchar)stA.at(n+7))
                    {
                      m_healthCode = (uint16_t)(stA.at(n+8)<<8) | (uchar)stA.at(n+9);
                      uint16_t voltage = (uint16_t)(stA.at(n+4)<<8) | (uchar)stA.at(n+5);
                      if(voltage != 0)
                        {
                          m_mcuVoltage = ~(voltage + 122);
                          //qDebug() << "MCU VOLTAGE: " << m_mcuVoltage;
                        }
                      stA.remove(0,n+11);
                      m_strPre = stA;
                      return false;
                    }
                }
            }
          else
            {
              m_strPre = stA;
              return false;
            }
        }

    }
  return false;
}
