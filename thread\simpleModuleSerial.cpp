/*******************************
 * 简单模组交互线程
 *******************/

#include "simpleModuleSerial.h"
#include <QThread>

CSimpleModuleSerial::CSimpleModuleSerial(QObject *parent):
    QObject(parent)
  , m_task_id(0)
{
    ml_simple_module.clear();
}

CSimpleModuleSerial::~CSimpleModuleSerial() {}

void CSimpleModuleSerial::deviceChangeInterface(const uint8_t &device_id, ISimpleModule* simple_module_)
{
    if(device_id > ml_simple_module.size()) {
        return;
    }

    if(simple_module_ != nullptr) {
        ml_simple_module.replace(device_id - 1, simple_module_);
        //ml_simple_module.at(device_id)->m_strPre.clear();
    }
}

void CSimpleModuleSerial::addDevice(ISimpleModule *simple_module_) {
    ml_simple_module.append(simple_module_);
}

void CSimpleModuleSerial::taskIdChange(const uint8_t &id)
{
    if(id > ml_simple_module.size()) {
        return;
    }

    m_task_id = id;
    if(ml_simple_module.at(id)->m_strPre.length() > 0) ml_simple_module.at(id)->m_strPre.clear();

//    qDebug() << "-i simpleModule thread/ task id:" << id;
}

void CSimpleModuleSerial::loop(int module_id)
{
    Q_UNUSED(module_id);
    QByteArray arr;
    QByteArray device_arr;
    for(;;) {
        if(m_task_id > 0) {
            if(m_task_id > ml_simple_module.size()) {
                qDebug() << "-e simpleModule thread/ module index out off range";
            }
            else {
                arr = ml_simple_module.at(m_task_id - 1)->portDataRead();
                if(arr.length() > 0) ml_simple_module.at(m_task_id - 1)->interactionParsing(arr, arr.length());
            }
                //            }
//            else if(2) {
//                arr = ml_simple_module.at(m_task_id)->portDataRead();
//                if(arr.length() > 0) ml_simple_module.at(m_task_id)->dataParsing(arr, arr.length());
//            }
        }
        else if(m_task_id == 0) {//空
            QThread::msleep(5);
        }
        else {//退出线程 //不能用else，时序上存在问题

            break; //return
        }
    }
}
