#!/usr/bin/env python3
"""
追溯系统配置生成器

按照单一原则，专门负责生成追溯系统的配置文件。
"""

import os
import json
import argparse
from pathlib import Path
from datetime import datetime


def generate_traceability_config(project_path, project_type="single_layer", non_interactive=False):
    """
    生成追溯系统配置文件
    
    Args:
        project_path: 项目根目录路径
        project_type: 项目类型(single_layer/multi_level)
    
    Returns:
        bool: 是否成功生成
    """
    try:
        config_dir = Path(project_path) / 'config'
        config_dir.mkdir(exist_ok=True)
        
        config_file = config_dir / 'traceability_config.json'
        
        if config_file.exists():
            if non_interactive:
                print(f"[!] 追溯系统配置文件已存在，非交互模式下直接覆盖: {config_file}")
            else:
                print(f"[!] 追溯系统配置文件已存在: {config_file}")
                response = input("是否覆盖现有配置文件？(y/N): ")
                if response.lower() != 'y':
                    print("操作已取消")
                    return True
        
        traceability_config = {
            "traceability": {
                "version": "1.0.0",
                "framework_type": project_type,
                "id_format": "[组件代号]_[文档类型]_[序号]_[文档名称]",
                "id_register_method": "index_file",
                "created_date": datetime.now().isoformat(),
                "components": {
                    "REQ": {
                        "name": "需求",
                        "directory": "requirements",
                        "index_file": "REQ_INDEX.md",
                        "types": ["MATRIX", "SPEC", "ANALYSIS", "CHANGE"]
                    },
                    "DES": {
                        "name": "设计",
                        "directory": "design",
                        "index_file": "DES_INDEX.md",
                        "types": ["ARCH", "DETAIL", "REVIEW", "CHANGE"]
                    },
                    "DEV": {
                        "name": "开发",
                        "directory": "development",
                        "index_file": "DEV_INDEX.md",
                        "types": ["PLAN", "CODE", "TEST", "REVIEW"]
                    },
                    "QA": {
                        "name": "质量",
                        "directory": "quality",
                        "index_file": "QA_INDEX.md",
                        "types": ["PLAN", "CASE", "REPORT", "ISSUE"]
                    },
                    "PROD": {
                        "name": "生产",
                        "directory": "production",
                        "index_file": "PROD_INDEX.md",
                        "types": ["BOM", "PROCESS", "TEST", "REPORT"]
                    },
                    "DEL": {
                        "name": "交付",
                        "directory": "deliverables",
                        "index_file": "DEL_INDEX.md",
                        "types": ["DOC", "SOFT", "HARD", "PACKAGE"]
                    }
                },
                "relationships": [
                    {
                        "from": "REQ",
                        "to": "DES",
                        "type": "需求追溯"
                    },
                    {
                        "from": "DES",
                        "to": "DEV",
                        "type": "设计实现"
                    },
                    {
                        "from": "DEV",
                        "to": "QA",
                        "type": "开发验证"
                    },
                    {
                        "from": "QA",
                        "to": "PROD",
                        "type": "质量确认"
                    },
                    {
                        "from": "PROD",
                        "to": "DEL",
                        "type": "生产交付"
                    }
                ],
                "block_management": {
                    "enabled": True,
                    "id_format": "[组件代号]_[序号]_[块类型]",
                    "traceability_matrix": {
                        "file": "reports/traceability_matrix.md",
                        "auto_update": True
                    }
                },
                "change_management": {
                    "enabled": True,
                    "change_log": "logs/change_log.md",
                    "impact_analysis": True
                }
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(traceability_config, f, ensure_ascii=False, indent=2)
        
        print(f"[+] 追溯系统配置文件已生成: {config_file}")
        print("  - 组件和文档类型配置")
        print("  - 追溯关系链条配置")
        print("  - 块级管理配置")
        print("  - 变更管理配置")
        
        return True
        
    except Exception as e:
        print(f"[X] 追溯系统配置文件生成失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='追溯系统配置生成器')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer',
                       choices=['single_layer', 'multi_level'],
                       help='项目类型')
    parser.add_argument('--non-interactive', action='store_true', help='非交互模式，自动覆盖现有文件')

    args = parser.parse_args()
    
    project_path = Path(args.project_path).resolve()
    
    if not project_path.exists():
        print(f"[X] 项目路径不存在: {project_path}")
        return 1
    
    success = generate_traceability_config(project_path, args.project_type, args.non_interactive)
    
    if success:
        print("\n[+] 追溯系统配置生成完成")
        return 0
    else:
        print("\n[X] 追溯系统配置生成失败")
        return 1


if __name__ == "__main__":
    exit(main()) 