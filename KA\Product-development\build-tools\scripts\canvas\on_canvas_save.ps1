#!/usr/bin/env pwsh
# 文件：on_canvas_save.ps1
# 功能：当 product.canvas 的 edges 改动时，调用 Python 脚本

param (
    [string]$FilePath
)

Write-Host "Canvas Auto Sync - 检测到文件变化" -ForegroundColor Green
Write-Host "文件路径: $FilePath" -ForegroundColor Cyan

# 设置路径
$CacheDir = "$env:USERPROFILE\.obsidian_canvas_cache"
$CacheFile = Join-Path $CacheDir "edges.json"
$TmpNew = "$env:TEMP\edges.new.json"

# 只处理 product.canvas
if ([System.IO.Path]::GetFileName($FilePath) -ne "product.canvas") {
    Write-Host "跳过非product.canvas文件: $([System.IO.Path]::GetFileName($FilePath))" -ForegroundColor Yellow
    exit 0
}

Write-Host "开始处理Canvas文件..." -ForegroundColor Green

# 创建缓存目录
if (-not (Test-Path $CacheDir)) {
    New-Item -ItemType Directory -Path $CacheDir | Out-Null
}

# 读取 .canvas JSON 文件
try {
    Write-Host "解析Canvas JSON文件..." -ForegroundColor Cyan
    $json = Get-Content $FilePath -Raw | ConvertFrom-Json
    Write-Host "JSON解析成功" -ForegroundColor Green
} catch {
    Write-Host "JSON解析失败: $FilePath" -ForegroundColor Red
    Write-Host "错误详情: $($_.Exception.Message)" -ForegroundColor Red
    exit 0
}

# 提取 edges 字段并写入临时文件
Write-Host "提取Canvas edges数据..." -ForegroundColor Cyan
$edgesJson = $json.edges | ConvertTo-Json -Depth 10
$edgesJson | Set-Content -Path $TmpNew -Encoding UTF8

# 从Canvas文件路径推导项目根目录
$ProjectPath = [System.IO.Path]::GetDirectoryName($FilePath)
Write-Host "项目路径: $ProjectPath" -ForegroundColor Cyan

# 判断缓存是否存在
if (-not (Test-Path $CacheFile)) {
    Write-Host "首次运行，创建edges缓存..." -ForegroundColor Yellow
    $edgesJson | Set-Content -Path $CacheFile -Encoding UTF8
    Write-Host "执行Canvas到INDEX同步..." -ForegroundColor Green
    & python "$env:PRODUCT_DEVELOP_DIR\scripts\canvas\auto_link_documents.py" --sync-from-canvas --project-path "$ProjectPath"
    Write-Host "首次同步完成" -ForegroundColor Green
    exit 0
}

# 比较新旧 edges 内容
Write-Host "检查edges是否有变化..." -ForegroundColor Cyan
$oldEdges = Get-Content $CacheFile -Raw
$newEdges = Get-Content $TmpNew -Raw

if ($oldEdges -ne $newEdges) {
    Write-Host "检测到edges变化，执行同步..." -ForegroundColor Yellow
    & python "$env:PRODUCT_DEVELOP_DIR\scripts\canvas\auto_link_documents.py" --sync-from-canvas --project-path "$ProjectPath"
    # 更新缓存
    $newEdges | Set-Content -Path $CacheFile -Encoding UTF8
    Write-Host "同步完成" -ForegroundColor Green
} else {
    Write-Host "edges无变化，跳过同步" -ForegroundColor Gray
}
