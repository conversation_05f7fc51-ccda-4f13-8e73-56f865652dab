# LA-T5 文档引用规范

**制定日期**: 2025-01-16  
**版本**: v1.0  
**适用范围**: 所有LA-T5项目文档  
**维护人员**: 文档管理员  

## 🎯 规范目标

### 核心目标
1. **统一引用格式**: 建立标准化的文档引用方式
2. **清晰关联关系**: 明确文档间的依赖和关联
3. **便于维护**: 支持文档重命名和移动
4. **提升可读性**: 让读者快速理解文档关系

### 适用场景
- 文档间相互引用
- 需求追溯链建立
- 版本发布说明编写
- 技术方案关联说明

## 📝 文档ID规范

### ID格式定义
```
格式: [类型前缀]-[模块代码]-[序号]
示例: REQ-FILTER-001
```

### 类型前缀对照表
| 文档类型 | 前缀 | 说明 | 示例 |
|---------|------|------|------|
| demands | REQ | 需求文档 | REQ-FILTER-001 |
| issues | ISS | 问题文档 | ISS-FILTER-001 |
| development/算法分析 | DEV-ALGO | 算法分析文档 | DEV-ALGO-001 |
| development/代码架构 | DEV-ARCH | 架构设计文档 | DEV-ARCH-001 |
| development/技术验证 | DEV-TEST | 技术验证文档 | DEV-TEST-001 |
| development/开发指南 | DEV-GUIDE | 开发指南文档 | DEV-GUIDE-001 |
| delivery/manual | MAN | 用户手册 | MAN-ADJUST-001 |
| delivery/release_notes | REL | 发布说明 | REL-V144-001 |
| delivery/reports | RPT | 报告文档 | RPT-QUAL-001 |
| support | SUP | 支持文档 | SUP-FAQ-001 |

### 模块代码对照表
| 功能模块 | 代码 | 说明 |
|---------|------|------|
| 光斑滤波 | FILTER | 滤波相关功能 |
| 光斑形状 | SHAPE | 非规则光斑处理 |
| 性能优化 | PERF | 系统性能相关 |
| 镜头调节 | ADJUST | 镜头调节功能 |
| MEMD功能 | MEMD | MEMD相关功能 |
| 返工功能 | REWORK | 返工相关功能 |
| 系统通用 | SYS | 系统级功能 |

### 序号规则
- 从001开始，按创建顺序递增
- 同一模块内序号连续
- 删除文档后序号不重用
- 重要里程碑版本可使用特殊序号（如V144表示v1.4.4版本）

## 🔗 引用格式规范

### 1. 标准引用格式
```markdown
[[文档ID]] - 简短描述

示例:
[[REQ-FILTER-001]] - 光斑滤波效果优化需求
[[DEV-ALGO-001]] - 边界处理算法研究
[[MAN-ADJUST-001]] - TOF接收镜片光路耦合软件使用文档
```

### 2. 带路径的引用格式
```markdown
[[路径/文档ID]] - 简短描述

示例:
[[demands/REQ-FILTER-001]] - 客户需求原始描述
[[development/算法分析/DEV-ALGO-001]] - 详细技术分析
[[delivery/manual/lenAdjust/MAN-ADJUST-001]] - 用户使用指南
```

### 3. 章节引用格式
```markdown
[[文档ID#章节]] - 章节描述

示例:
[[MAN-ADJUST-001#2.2]] - 光斑滤波配置章节
[[DEV-ALGO-001#实验验证]] - 算法验证结果
```

### 4. 版本引用格式
```markdown
[[文档ID@版本]] - 版本描述

示例:
[[MAN-ADJUST-001@v1.4]] - v1.4版本的用户手册
[[REL-V144-001@final]] - 最终发布版本
```

## 📊 关联关系标识

### 关联类型符号
| 符号 | 含义 | 使用场景 | 示例 |
|------|------|----------|------|
| 🔗 | 驱动关系 | A文档的需求驱动B文档创建 | REQ-FILTER-001 🔗 ISS-FILTER-001 |
| 📝 | 实现关系 | A文档的方案在B文档中实现 | DEV-ALGO-001 📝 MAN-ADJUST-001 |
| 📖 | 引用关系 | A文档引用B文档的内容 | MAN-ADJUST-001 📖 DEV-ALGO-001 |
| 🔄 | 同步关系 | A文档更新时B文档需要同步 | MAN-ADJUST-001 🔄 SUP-FAQ-001 |
| ⚡ | 依赖关系 | A文档依赖B文档的完成 | REL-V144-001 ⚡ DEV-ALGO-001 |
| 🎯 | 验证关系 | A文档验证B文档的正确性 | RPT-QUAL-001 🎯 DEV-ALGO-001 |

### 关联关系描述格式
```markdown
## 关联文档

### 输入依赖
- 🔗 [[REQ-FILTER-001]] - 客户需求驱动
- 📖 [[ISS-FILTER-001]] - 问题分析参考

### 输出影响
- 📝 [[MAN-ADJUST-001]] - 用户手册实现
- 🔄 [[SUP-FAQ-001]] - 支持文档同步
- ⚡ [[REL-V144-001]] - 发布说明依赖

### 验证文档
- 🎯 [[RPT-QUAL-001]] - 质量验证报告
```

## 📋 文档头部信息规范

### 标准文档头部
```markdown
# 文档标题

**文档ID**: DEV-ALGO-001  
**版本**: v1.0  
**创建日期**: 2025-01-16  
**最后更新**: 2025-01-16  
**状态**: 已完成  
**维护人员**: 算法团队  

## 关联文档
- 🔗 [[ISS-FILTER-001]] - 问题分析驱动
- 📝 [[MAN-ADJUST-001]] - 用户手册实现
- 🔄 [[REL-V144-001]] - 发布说明同步
```

### 必填字段说明
- **文档ID**: 唯一标识符，按规范格式
- **版本**: 文档版本号，遵循语义化版本
- **创建日期**: 文档首次创建日期
- **最后更新**: 最近一次重要更新日期
- **状态**: 文档当前状态（草稿/审查中/已完成/已废弃）
- **维护人员**: 负责维护的团队或个人

### 可选字段
- **审查人员**: 文档审查负责人
- **批准人员**: 文档批准发布人
- **有效期**: 文档有效期限
- **密级**: 文档保密级别

## 🔄 引用维护规范

### 引用检查清单
- [ ] 所有引用的文档ID是否存在
- [ ] 引用格式是否符合规范
- [ ] 关联关系类型是否正确
- [ ] 引用描述是否准确简洁

### 文档重命名处理
1. **更新文档ID**: 在文档关联矩阵中更新
2. **批量替换**: 在所有引用文档中替换旧ID
3. **通知相关人员**: 通知所有相关文档维护人员
4. **验证完整性**: 确保所有引用都已正确更新

### 文档移动处理
1. **更新路径引用**: 修改所有带路径的引用
2. **检查链接有效性**: 确保所有链接仍然有效
3. **更新目录结构**: 在相关索引文档中更新
4. **测试访问性**: 验证文档仍可正常访问

## 📊 引用质量指标

### 质量检查指标
- **引用完整性**: 所有引用都有效且可访问
- **格式一致性**: 所有引用都符合规范格式
- **关联准确性**: 关联关系类型正确标识
- **描述清晰性**: 引用描述简洁明了

### 定期检查任务
- **每周检查**: 验证新增引用的格式和有效性
- **每月检查**: 全面检查所有引用的完整性
- **版本发布前**: 重点检查发布相关文档的引用
- **季度审查**: 评估引用规范的有效性和改进建议

## 🛠️ 工具和自动化

### 推荐工具
- **Markdown编辑器**: 支持链接检查的编辑器
- **文档管理系统**: 支持ID管理和引用追踪
- **自动化脚本**: 批量检查和更新引用

### 自动化检查脚本示例
```bash
# 检查无效引用
grep -r "\[\[.*\]\]" docs/ | check_references.py

# 验证文档ID格式
find docs/ -name "*.md" | validate_doc_ids.py

# 生成引用报告
generate_reference_report.py docs/
```

## 📞 支持和联系

### 规范问题咨询
- **文档管理员**: 负责规范制定和维护
- **技术写作团队**: 负责规范培训和指导
- **开发团队**: 负责工具开发和自动化

### 改进建议
- 欢迎提出规范改进建议
- 定期收集使用反馈
- 持续优化规范内容

---

**规范状态**: ✅ 已发布  
**下次审查**: 2025-02-16  
**意见反馈**: 请联系文档管理员  
**培训计划**: 将在团队会议中进行规范培训
