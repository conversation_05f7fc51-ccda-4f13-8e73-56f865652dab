{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 加权均值滤波器算法验证与可视化\n", "\n", "本notebook用于验证加权均值滤波器的算法实现，并可视化展示不同权重模式的效果差异。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib import cm\n", "from matplotlib.patches import Rectangle\n", "import seaborn as sns\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 5x5测试数据矩阵\n", "test_data = np.array([\n", "    [271, 882, 826, 748, 58],\n", "    [1011, 908, 792, 756, 738],\n", "    [1074, 924, 807, 800, 859],\n", "    [1021, 877, 777, 776, 855],\n", "    [145, 887, 788, 740, 33]\n", "])\n", "\n", "print(\"原始测试数据:\")\n", "print(test_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 预定义权重矩阵\n", "\n", "WeightedAverageFilter支持5种预定义权重模式："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义所有预设权重矩阵\n", "weight_matrices = {\n", "    'uniform': np.array([\n", "        [1.0, 1.0, 1.0],\n", "        [1.0, 1.0, 1.0],\n", "        [1.0, 1.0, 1.0]\n", "    ]),\n", "    'gaussian': np.array([\n", "        [1.0, 2.0, 1.0],\n", "        [2.0, 4.0, 2.0],\n", "        [1.0, 2.0, 1.0]\n", "    ]),\n", "    'center_weighted': np.array([\n", "        [1.0, 2.0, 1.0],\n", "        [2.0, 8.0, 2.0],\n", "        [1.0, 2.0, 1.0]\n", "    ]),\n", "    'edge_enhance': np.array([\n", "        [0.0, -1.0, 0.0],\n", "        [-1.0, 5.0, -1.0],\n", "        [0.0, -1.0, 0.0]\n", "    ]),\n", "    'smooth': np.array([\n", "        [1.0, 4.0, 1.0],\n", "        [4.0, 12.0, 4.0],\n", "        [1.0, 4.0, 1.0]\n", "    ])\n", "}\n", "\n", "# 显示权重矩阵和归一化信息\n", "for name, weights in weight_matrices.items():\n", "    weight_sum = np.sum(weights)\n", "    normalized_weights = weights / weight_sum\n", "    \n", "    print(f\"\\n=== {name.upper()} 权重 ===\")\n", "    print(f\"原始权重矩阵:\")\n", "    print(weights)\n", "    print(f\"权重总和: {weight_sum}\")\n", "    print(f\"归一化后权重矩阵:\")\n", "    print(normalized_weights)\n", "    print(f\"归一化后总和: {np.sum(normalized_weights):.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 镜像边界处理\n", "\n", "加权均值滤波器使用镜像扩展来处理边界像素："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def mirror_pad(image, pad_width):\n", "    \"\"\"镜像扩展边界\"\"\"\n", "    return np.pad(image, pad_width, mode='reflect')\n", "\n", "def get_safe_pixel_value(image, x, y):\n", "    \"\"\"获取安全的像素值（C++实现的Python版本）\"\"\"\n", "    height, width = image.shape\n", "    \n", "    # 镜像边界处理\n", "    if x < 0:\n", "        x = -x\n", "    if y < 0:\n", "        y = -y\n", "    if x >= width:\n", "        x = 2 * (width - 1) - x\n", "    if y >= height:\n", "        y = 2 * (height - 1) - y\n", "    \n", "    # 确保在有效范围内\n", "    x = max(0, min(x, width - 1))\n", "    y = max(0, min(y, height - 1))\n", "    \n", "    return image[y, x]\n", "\n", "# 演示边界处理\n", "print(\"边界处理示例:\")\n", "print(f\"原始图像尺寸: {test_data.shape}\")\n", "print(f\"访问(-1, 0): {get_safe_pixel_value(test_data, -1, 0)} (实际访问(1, 0): {test_data[0, 1]})\")\n", "print(f\"访问(5, 2): {get_safe_pixel_value(test_data, 5, 2)} (实际访问(3, 2): {test_data[2, 3]})\")\n", "print(f\"访问(2, -1): {get_safe_pixel_value(test_data, 2, -1)} (实际访问(2, 1): {test_data[1, 2]})\")\n", "\n", "# 使用numpy的pad函数进行镜像扩展\n", "padded_image = mirror_pad(test_data, ((1, 1), (1, 1)))\n", "print(f\"\\n镜像扩展后的图像 (7x7):\")\n", "print(padded_image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 加权均值滤波算法实现"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def weighted_average_filter(image, weights, strength=1.0, normalize=True):\n", "    \"\"\"加权均值滤波器实现\"\"\"\n", "    if normalize:\n", "        weight_sum = np.sum(weights)\n", "        if weight_sum != 0:\n", "            weights = weights / weight_sum\n", "    \n", "    kernel_h, kernel_w = weights.shape\n", "    half_kernel = kernel_h // 2\n", "    \n", "    # 输出图像\n", "    output = np.zeros_like(image, dtype=np.float64)\n", "    \n", "    # 对每个像素应用加权均值\n", "    for y in range(image.shape[0]):\n", "        for x in range(image.shape[1]):\n", "            # 计算加权均值\n", "            weighted_sum = 0.0\n", "            for ky in range(kernel_h):\n", "                for kx in range(kernel_w):\n", "                    src_x = x + kx - half_kernel\n", "                    src_y = y + ky - half_kernel\n", "                    \n", "                    pixel_value = get_safe_pixel_value(image, src_x, src_y)\n", "                    weighted_sum += pixel_value * weights[ky, kx]\n", "            \n", "            # 应用滤波强度\n", "            original_value = float(image[y, x])\n", "            filtered_value = original_value + strength * (weighted_sum - original_value)\n", "            output[y, x] = filtered_value\n", "    \n", "    return output\n", "\n", "# 测试所有权重模式\n", "results = {}\n", "for name, weights in weight_matrices.items():\n", "    result = weighted_average_filter(test_data, weights, strength=1.0, normalize=True)\n", "    results[name] = result\n", "    \n", "    print(f\"\\n=== {name.upper()} 滤波结果 ===\")\n", "    print(result.astype(int))\n", "    print(f\"中心点(2,2): 原始值={test_data[2,2]}, 滤波后={int(result[2,2])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 结果对比与可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建可视化对比\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "# 显示原始数据\n", "im0 = axes[0].imshow(test_data, cmap='viridis', aspect='equal')\n", "axes[0].set_title('原始数据', fontsize=14)\n", "for i in range(5):\n", "    for j in range(5):\n", "        axes[0].text(j, i, str(test_data[i, j]), ha='center', va='center', \n", "                    color='white', fontsize=10, weight='bold')\n", "plt.colorbar(im0, ax=axes[0])\n", "\n", "# 显示各种滤波结果\n", "weight_names = ['uniform', 'gaussian', 'center_weighted', 'edge_enhance', 'smooth']\n", "for idx, name in enumerate(weight_names):\n", "    result = results[name]\n", "    im = axes[idx + 1].imshow(result, cmap='viridis', aspect='equal')\n", "    axes[idx + 1].set_title(f'{name.replace(\"_\", \" \").title()} 滤波结果', fontsize=14)\n", "    \n", "    # 添加数值标注\n", "    for i in range(5):\n", "        for j in range(5):\n", "            axes[idx + 1].text(j, i, str(int(result[i, j])), ha='center', va='center',\n", "                              color='white', fontsize=10, weight='bold')\n", "    plt.colorbar(im, ax=axes[idx + 1])\n", "\n", "# 标记中心点\n", "for ax in axes:\n", "    ax.add_patch(Rectangle((1.5, 1.5), 1, 1, fill=False, edgecolor='red', linewidth=3))\n", "    ax.set_xticks(range(5))\n", "    ax.set_yticks(range(5))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 权重矩阵可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化权重矩阵\n", "fig, axes = plt.subplots(1, 5, figsize=(20, 4))\n", "\n", "for idx, (name, weights) in enumerate(weight_matrices.items()):\n", "    # 归一化权重用于显示\n", "    normalized_weights = weights / np.sum(weights) if np.sum(weights) != 0 else weights\n", "    \n", "    im = axes[idx].imshow(normalized_weights, cmap='RdBu_r', aspect='equal')\n", "    axes[idx].set_title(f'{name.replace(\"_\", \" \").title()}\\n权重矩阵', fontsize=12)\n", "    \n", "    # 添加权重数值\n", "    for i in range(3):\n", "        for j in range(3):\n", "            axes[idx].text(j, i, f'{normalized_weights[i, j]:.3f}', \n", "                          ha='center', va='center', fontsize=10, weight='bold')\n", "    \n", "    axes[idx].set_xticks(range(3))\n", "    axes[idx].set_yticks(range(3))\n", "    plt.colorbar(im, ax=axes[idx])\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 中心点数值分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析中心点(2,2)的计算过程\n", "center_x, center_y = 2, 2\n", "original_value = test_data[center_y, center_x]\n", "\n", "print(f\"中心点(2,2)原始值: {original_value}\")\n", "print(f\"3x3邻域:\")\n", "neighborhood = np.zeros((3, 3))\n", "for ky in range(3):\n", "    for kx in range(3):\n", "        src_x = center_x + kx - 1\n", "        src_y = center_y + ky - 1\n", "        neighborhood[ky, kx] = get_safe_pixel_value(test_data, src_x, src_y)\n", "\n", "print(neighborhood.astype(int))\n", "\n", "print(\"\\n=== 各权重模式的中心点计算 ===\")\n", "print(f\"{'权重模式':<15} {'加权均值':<10} {'滤波后值':<10} {'变化量':<10}\")\n", "print(\"-\" * 50)\n", "\n", "for name, weights in weight_matrices.items():\n", "    # 归一化权重\n", "    normalized_weights = weights / np.sum(weights) if np.sum(weights) != 0 else weights\n", "    \n", "    # 计算加权均值\n", "    weighted_sum = np.sum(neighborhood * normalized_weights)\n", "    \n", "    # 滤波强度处理\n", "    filtered_value = original_value + 1.0 * (weighted_sum - original_value)\n", "    \n", "    change = filtered_value - original_value\n", "    \n", "    print(f\"{name:<15} {weighted_sum:<10.1f} {filtered_value:<10.1f} {change:<+10.1f}\")\n", "\n", "# 与C++实际输出对比\n", "cpp_results = {\n", "    'uniform': 824,\n", "    'gaussian': 821,\n", "    'center_weighted': 818,\n", "    'edge_enhance': 742,\n", "    'smooth': 818\n", "}\n", "\n", "print(\"\\n=== Python计算 vs C++实际输出 ===\")\n", "print(f\"{'权重模式':<15} {'Python':<10} {'C++':<10} {'差异':<10}\")\n", "print(\"-\" * 50)\n", "\n", "for name in weight_names:\n", "    python_result = int(results[name][2, 2])\n", "    cpp_result = cpp_results[name]\n", "    diff = abs(python_result - cpp_result)\n", "    \n", "    print(f\"{name:<15} {python_result:<10} {cpp_result:<10} {diff:<10}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "### 加权均值滤波器特点：\n", "\n", "1. **权重归一化**：所有权重矩阵都会被归一化，确保总和为1\n", "2. **边界处理**：使用镜像扩展处理边界像素，保持图像连续性\n", "3. **滤波强度**：支持滤波强度参数，控制滤波效果的强弱\n", "\n", "### 不同权重模式效果：\n", "\n", "- **uniform**：均匀平滑，所有邻域像素权重相等\n", "- **gaussian**：高斯平滑，中心权重较高，边缘权重较低\n", "- **center_weighted**：中心加权，突出中心像素的重要性\n", "- **edge_enhance**：边缘增强，类似锐化效果\n", "- **smooth**：强平滑，中心权重很高，平滑效果显著\n", "\n", "### 配置问题：\n", "\n", "**发现问题**：`faculaProcessingConfig.cpp`中的`createWeightedAverageParams`函数缺少对\"center_weighted\"、\"edge_enhance\"、\"smooth\"预设的处理，导致配置文件中的这些设置无法生效。\n", "\n", "**建议修复**：在`createWeightedAverageParams`函数中添加对所有预设模式的支持。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 2}