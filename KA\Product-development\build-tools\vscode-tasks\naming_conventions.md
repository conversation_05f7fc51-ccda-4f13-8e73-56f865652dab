# 产品命名规则指南

本文档定义了产品和系列的命名规范，确保在整个开发流程中保持命名的一致性和规范性。

## 1. 产品命名规则

### 1.1 一般原则

- 产品名称应简洁明了，反映产品的核心功能或特性
- 避免使用临时性或内部代号作为正式产品名称
- 名称应兼顾技术准确性和市场营销需求

### 1.2 命名格式

- **格式**: `[前缀]-[产品类别]-[功能描述]`
- **示例**: `KA-IoT-Gateway`

### 1.3 命名组成部分

| 组成部分 | 描述 | 可选值示例 | 是否必须 |
|---------|------|-----------|---------|
| 前缀 | 公司或部门代号 | KA, RS, XT | 必须 |
| 产品类别 | 产品技术领域 | IoT, AI, Cloud, Edge | 必须 |
| 功能描述 | 产品具体功能 | Gateway, Sensor, Controller | 必须 |

### 1.4 项目缩写表

| 产品类别 | 缩写 | 适用范围 |
|---------|------|---------|
| 物联网 | IoT | 物联网设备、网关、平台 |
| 人工智能 | AI | AI模型、智能分析系统 |
| 云服务 | Cloud | 云平台、云应用 |
| 边缘计算 | Edge | 边缘计算设备、边缘网关 |
| 工业控制 | ICS | 工业控制系统、PLC |
| 消费电子 | CE | 面向消费者的电子产品 |
| 医疗设备 | Med | 医疗设备、健康监测 |
| 安防系统 | Sec | 安防监控、门禁系统 |

### 1.5 命名禁忌

- 不使用过于宽泛的名称（如 "System", "Platform" 等）
- 不使用难以拼写或发音的名称
- 避免使用可能引起知识产权争议的名称
- 避免文件系统特殊字符（如 `/`, `\`, `:`, `*`, `?`, `"`, `<`, `>`, `|`）

## 2. 系列命名规则

### 2.1 系列定义

系列是指具有共同技术框架但在功能、形态或市场定位上存在差异的产品集合。

### 2.2 命名格式

- **格式**: `[功能级别]-[目标市场]-[特性]`
- **示例**: `基础版`, `企业版`, `专业版`, `高性能版`

### 2.3 标准系列命名

推荐使用以下标准系列命名：

| 系列名称 | 适用场景 | 特点描述 |
|---------|---------|---------|
| 基础版 (Basic) | 入门级应用 | 核心功能集，成本优化 |
| 标准版 (Standard) | 一般应用 | 平衡功能与成本 |
| 专业版 (Professional) | 专业应用 | 完整功能集，性能优化 |
| 企业版 (Enterprise) | 企业级应用 | 高级功能，扩展性强 |
| 高性能版 (Performance) | 高要求场景 | 性能优化，高可靠性 |
| 紧凑版 (Compact) | 空间受限场景 | 体积小，集成度高 |
| 增强版 (Enhanced) | 功能升级场景 | 在标准版基础上增加功能 |
| 行业版 (Industry) | 特定行业应用 | 针对特定行业定制 |

### 2.4 系列版本演进

当产品系列随版本迭代时，应遵循以下命名原则：

- 主要版本更新：添加数字标识，如 `标准版 V2`
- 次要版本更新：使用年份，如 `专业版 2023`
- 特定功能强化：添加功能描述词，如 `高性能版 Plus`

### 2.5 命名建议

- 系列名称应能清晰表达产品定位差异
- 命名应具有连续性和可扩展性，便于未来添加新系列
- 避免使用过于技术性的术语，使非技术人员也能理解
- 在同一产品族中，系列命名风格应保持一致

## 3. 示例

### 3.1 完整产品族命名示例

**产品族名称**: `KA-IoT-Gateway`

**系列划分**:

- `KA-IoT-Gateway-Basic`: 基础版，核心网关功能
- `KA-IoT-Gateway-Standard`: 标准版，增加数据处理能力
- `KA-IoT-Gateway-Pro`: 专业版，增加安全特性和高级分析
- `KA-IoT-Gateway-Enterprise`: 企业版，增加集群支持和高可用特性

### 3.2 市场化命名转换

在内部开发时使用标准命名，面向市场时可以使用更具吸引力的名称：

| 内部开发名称 | 市场营销名称 | 描述 |
|------------|------------|------|
| KA-IoT-Gateway-Basic | SimpliGate | 简单易用的物联网网关 |
| KA-IoT-Gateway-Pro | ProConnect | 专业级物联网连接解决方案 |
| KA-IoT-Gateway-Enterprise | EnterpriseHub | 企业级物联网中央处理中心 |

## 4. 在VSCode任务中使用

当使用VSCode任务初始化产品项目时，请参考本文档中的命名规则：

1. 产品名称应遵循 `[前缀]-[产品类别]-[功能描述]` 格式
2. 系列名称应使用标准系列名称，并用逗号分隔，如 `基础版,标准版,专业版`

## 5. 命名审核流程

新产品和系列命名应经过以下审核流程：

1. 提案阶段：开发团队提出候选名称
2. 技术审核：确保名称符合技术规范
3. 营销审核：确保名称符合市场定位
4. 法律审核：确保没有知识产权风险
5. 最终确认：项目负责人确认最终名称
