#include "logindialog.h"
#include "ui_logindialog.h"
#include <QMessageBox>
#include <QSqlQuery>

loginDialog::loginDialog(QWidget *parent) :
    QDialog(parent)
  , ui(new Ui::loginDialog)
  , m_regDialog_ptr(new registerDialog)
{
    ui->setupUi(this);
    ui->versionLabel->setText(tr("版本：1.0"));
}

loginDialog::~loginDialog()
{
    delete ui;
    delete m_regDialog_ptr;
}

void loginDialog::on_pButtonLogin_clicked()
{
    QString user,pwd;
    user = ui->lEditAccound->text();    //获取用户名
    pwd = ui->lEditPword->text();   //获取密码

    /*判断用户名密码是否为空，为空则提示不能为空*/
    if(user=="")
        QMessageBox::warning(this,"","用户名不能为空");
    else if (pwd=="")
        QMessageBox::warning(this,"","密码不能为空");
    else
    {
#ifdef LOGIN_LINK_DATABASE
        QString S = QString("select * from user_table where name=’%1’ and password=’%2’").arg(user).arg(pwd);
        QSqlQuery query;//执行查询语句
        if(query.exec(S))
        {
#else
        if(user == tr("1") && pwd == tr("1"))
        {
#endif
            //QMessageBox::information(NULL, "登陆成功", "登陆成功！！！", QMessageBox::Yes);
            accept();
        }
        else
            QMessageBox::warning(this, "error", "用户名或者密码错误！！");

        /* 清空内容并定位光标*/
        ui->lEditAccound->clear();
        ui->lEditPword->clear();
        ui->lEditAccound->setFocus(); //将光标定位到用户名输入框
    }
}

void loginDialog::on_pButtonRegister_clicked()
{
//    m_regDialog_ptr->show();//显示子窗口
    m_regDialog_ptr->exec();
//    this->hide();//隐藏当前窗口
}
