#ifndef PROCESSLIST_H
#define PROCESSLIST_H

#include "stdint.h"

//* exec status
enum EExecStatus {
    eFATAL        = -3,  //无法修复错误
    eERROR        = -2,  //错误
    eWAIT         = 0,   //无
    eOK           = 1,   //流程正常
    eCOMP         = 2,   //单任务流程完成、
    ePROCESS_COMP = 3,   //任务集流程完成
};

//* 运行结果
enum EResult {
    eNG   = 0,
    ePASS = 1,
};


class CProcessList {
  public:
    CProcessList(){};
    virtual ~CProcessList(){};

  protected:
    virtual void taskList();  //
};

#endif  // PROCESSLIST_H
