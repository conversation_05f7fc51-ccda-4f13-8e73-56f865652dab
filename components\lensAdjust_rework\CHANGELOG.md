
## [v2.1.3] - 2025-07-08
- docs(config): readd  config files in MinSizeRel to git

## [v2.1.2] - 2025-07-08
- docs(config): add config to git

## [v2.1.1] - 2025-05-09
- fix(debug): fix execute program pwd in launch.json
- fix(log): fix date repeats problem in output messagge
## [v2.1.0] - 2025-05-09

- feat(ui): add new user interface theme
- fix(db): fix database connection timeout issues
- refactor: optimize event handling

## [v2.0.0] - 2025-01-04

- feat(mes): update mes connect info(BREAKING CHANGE)

## [v1.1.3] - 2024-06-19

+ refactor: sensor切换模式需要先解锁

V1.1.2_2024.5.15
- feature: 增加D6调节

V1.1.1 2024-3-23
- refactor: 工单号索添加域名配置

V1.1.0 2023_9_13
- refactor: 事务号索引增加domain条件
- refactor: 增加标签获取次数

V1.0.4 2023_8_3
- fix: 事务号索引，全部数据索引未负值问题

V1.0.3 2023_6_14
- style: 修改版本号解析

V1.0.2 2023_6_5
- fix: mst_dds_ 未释放内存BUG

V1.0.1 2023_5_18
- fix: 原始与新数据显示字体颜色修改
- fix: 新标签生成设置等待时间
- feature: 增加logo
- style: 旧标签不进行标签查询（标签非空）

# <center>镜片调节 返工
