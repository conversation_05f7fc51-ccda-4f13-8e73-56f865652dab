{"summary": {"total_tests": 3, "passed_tests": 3, "failed_tests": 0, "success_rate": "100.0%"}, "test_results": {"PYTHONPATH设置": true, "路径解析功能": true, "项目初始化": true}, "detailed_results": [{"tool_name": "pythonpath_setting_test", "success": true, "timestamp": "2025-07-03T11:49:25.034500", "details": {"original_pythonpath": null, "current_pythonpath": "F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts", "pythonpath_set": true}}, {"tool_name": "path_resolution_test", "success": true, "timestamp": "2025-07-03T11:49:27.183013", "details": {"input_path": "example/path_test_project", "resolved_path": "F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete\\example\\path_test_project", "result": {"success": true, "message": "✅ 项目 'path_test_project' 初始化成功", "project_path": "F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete\\example\\path_test_project", "structure_type": "single_layer", "components_created": ["REQ", "DES", "DEV", "QA", "DEL", "PM"], "configs_generated": ["workflow_config.json", "document_links_config.json", "traceability_config.json", "block_analysis_config.json"], "smart_detected_path": "F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete\\example\\path_test_project"}}}, {"tool_name": "init_project_tool", "success": true, "timestamp": "2025-07-03T11:49:30.604337", "details": {"result": {"success": true, "message": "✅ 项目 'unified_test_project' 初始化成功", "project_path": "F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete\\test_projects\\unified_test_project", "structure_type": "single_layer", "components_created": ["REQ", "DES", "DEV", "QA", "DEL", "PM"], "configs_generated": ["workflow_config.json", "document_links_config.json", "traceability_config.json", "block_analysis_config.json"], "smart_detected_path": "F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete\\test_projects\\unified_test_project"}, "project_dir_exists": true, "readme_exists": true, "canvas_exists": true, "config_dir_exists": true, "config_files_count": 10, "config_files": ["deliverables_config.json", "design_config.json", "development_config.json", "document_links_config.json", "production_config.json", "quality_config.json", "requirements_analysis_config.json", "requirements_import_config.json", "traceability_config.json", "workflow_config.json"], "key_config_exists": true, "key_config_valid": true, "all_checks_passed": true}}], "timestamp": "2025-07-03T11:49:30.604866"}