#ifndef _NOVA_CALIBRATION_H_
#define _NOVA_CALIBRATION_H_

#include <QMainWindow>
#include "novaCalibrationOperation.h"
#include <QCheckBox>
#include <QLineEdit>
#include <QPushButton>
#include <QStandardItemModel>
#include <QItemSelectionModel>
#include <QComboBox>

#include "IPms.h"
#include "IPlot.h"


namespace Ui {
class CNovaCalibration;
}

class CNovaCalibration : public QMainWindow
{
    Q_OBJECT

public:
    explicit CNovaCalibration(QWidget *parent = nullptr);
    ~CNovaCalibration();

signals:
    void windowCloseSiganl(bool);

private:
    //* UI
    Ui::CNovaCalibration *ui;
    QStandardItemModel* m_table_model_;
    QItemSelectionModel* m_select_model_;
//    StCmdView *mst_cmd_view_ = nullptr;
//    QVector<StCmdView*> mv_cmd_view;

    QStringList m_header_list;

    //*
    NNovaCalibOpt::StUiConfig *mst_config_ = nullptr;
    CNovaCalibOpt *mc_operation_ = nullptr;


    //* ui config
    void updateConfig(NNovaCalibOpt::StUiConfig *config_);

    void portListUpdate(QComboBox* port_box_, QStringList *port_list_, QString cur_port_name);

    //* plot
    void plotShow();

    //* 状态栏update
    void resultClean(void);

protected:
    virtual void closeEvent( QCloseEvent *event) override;

signals:
    void openSerial(void);

    void closeSerial(void);

    void calib1SingleTmpSignal(void);

    void calib2SingleTmpSingnal(void);

    void triggerTestTmpSignal(void);

private slots:
    void processStatusShow_slot(const int16_t &step, const int16_t &status);

    void serialShow_slot(bool is_open);
    void portListShow_slot(QStringList *port_list);
//    void portListShow_slot(QStringList *port_list, bool port_flag);
//    void codeScanPortListShow_slot(QStringList *port_list_, bool port_flag);
//    void stepMotorPortShow_slot(QStringList *port_list_, bool port_flag);
//    void currentPortShow_slot(QStringList *port_list_, bool port_flag);

    void calibTableShow_slot(const uint8_t &calib_index, const uint16_t &calib_value1, const uint16_t &calib_value2);
    void verifyTableShow_slot();
    void functionTestShow_slot(const uint8_t &cnt, const int16_t &dist, const int16_t &ref_dist);

    void chipIdShow_slot(const QByteArray &id);

    void resultShow_slot();

    void on_DeviceSelect_currentIndexChanged(int index);

    void on_serialOpen_clicked();
    void on_novaPort_currentIndexChanged(int index);
    void on_currentPort_currentIndexChanged(int index);
    void on_boardPort_currentIndexChanged(int index);
    void on_codeScannerPort_currentIndexChanged(int index);
    void on_reservePort_currentIndexChanged(int index);
    void on_start_clicked();
    void on_calib1_tmp_clicked();
    void on_calib2_tmp_clicked();
    void on_trigger_tmp_clicked();
};

#endif // COMCHECK_H
