<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CNovaCalibration</class>
 <widget class="QMainWindow" name="CNovaCalibration">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>761</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>nova_V1.2</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDockWidget
{
	background-color: rgb(255,255,255);
	
	font: 12pt &quot;黑体&quot;;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 6px;
}

QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

QPushButton{
	min-width: 150px;
	min-height: 25px;

	background-color: rgb(255,255,255); /*white*/
	
	font: 75 15pt &quot;Agency FB&quot;;
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}

QLineEdit
{
	/*min-width: 150px;*/
	min-height: 25px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	min-height: 25px;
    max-height: 25px;

	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
QCustomPlot{
	/*min-width: 1200px;
	min-height: 1000px;*/
}
#result{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	max-width: 200px;
	min-height:30px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 26pt &quot;黑体&quot;;
}
</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,0">
      <property name="spacing">
       <number>6</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="label_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>NOVA端口：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>测距板端口：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="codeScannerPort">
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="currentPort">
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="boardPort">
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_10">
        <property name="text">
         <string>预留端口：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_9">
        <property name="text">
         <string>扫码器端口：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>电流端口：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="novaPort">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QComboBox" name="reservePort">
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="QPushButton" name="serialOpen">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>29</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>open</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="0">
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout_3" columnstretch="0,0">
      <property name="spacing">
       <number>6</number>
      </property>
      <item row="0" column="1">
       <widget class="QComboBox" name="projectSelect">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>Nova-A1</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Nova-A1B</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Nova-B1</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_13">
        <property name="text">
         <string>预留：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="reserveSelect">
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_5">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>项目：</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="3" column="0" rowspan="2">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="5" column="0" colspan="2">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QTextEdit" name="processView">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>302</width>
          <height>52</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>160</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#processView{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 300px;
	min-height:50px;

	font: 10pt &quot;黑体&quot;;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="result">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>304</width>
          <height>36</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>304</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#result{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	max-width: 200px;
	min-height:30px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 26pt &quot;黑体&quot;;
}</string>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="start">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>29</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>start</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="5" column="2">
     <widget class="QTableView" name="calibrationView"/>
    </item>
    <item row="5" column="3">
     <widget class="QTableView" name="finalTestView"/>
    </item>
    <item row="4" column="1">
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <widget class="QPushButton" name="calib1_tmp">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>29</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>calib1</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="calib2_tmp">
        <property name="text">
         <string>calib2</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="trigger_tmp">
        <property name="text">
         <string>空测监控</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="0" column="2" rowspan="5" colspan="2">
     <widget class="QCustomPlot" name="measurePlot" native="true">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>500</width>
        <height>0</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="cursor">
       <cursorShape>PointingHandCursor</cursorShape>
      </property>
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1138</width>
     <height>25</height>
    </rect>
   </property>
   <widget class="QMenu" name="menucomCheck_V1_1">
    <property name="title">
     <string>file</string>
    </property>
   </widget>
   <addaction name="menucomCheck_V1_1"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="windowTitle">
    <string>toolBar_2</string>
   </property>
   <attribute name="toolBarArea">
    <enum>LeftToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="separator"/>
   <addaction name="ActionRecord"/>
   <addaction name="actionImport"/>
   <addaction name="ActionClean"/>
  </widget>
  <action name="ActionClean">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../../resource.qrc">
     <normaloff>:/icon/w_b/clean.png</normaloff>:/icon/w_b/clean.png</iconset>
   </property>
   <property name="text">
    <string>clean</string>
   </property>
  </action>
  <action name="ActionRecord">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../../resource.qrc">
     <normaloff>:/icon/w_b/record-2.jpg</normaloff>:/icon/w_b/record-2.jpg</iconset>
   </property>
   <property name="text">
    <string>record</string>
   </property>
  </action>
  <action name="actionImport">
   <property name="icon">
    <iconset resource="../../resource.qrc">
     <normaloff>:/icon/w_b/import.png</normaloff>:/icon/w_b/import.png</iconset>
   </property>
   <property name="text">
    <string>import</string>
   </property>
   <property name="toolTip">
    <string>import</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>customPlot/qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../resource.qrc"/>
 </resources>
 <connections/>
</ui>
