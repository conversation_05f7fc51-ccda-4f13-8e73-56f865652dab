#include "faculaContext.h"
#include "QMessageBox"
#include "factories/FilterFactory.h"
#include "factories/InterpolationFactory.h"
#include "faculaDataAdapter.h"
#include "faculaProcessingConfig.h"
#include <QSettings>


#define MP_MERGE_TIMES   2  //光斑数据整合次数
#define DISCARD_PACK_NUM 0  // greymap 舍弃包数

CFaculaContext::CFaculaContext(const ISensorBoardFactory::StFaculaSensorInfo &facula_config) {
    mst_facula_sensor_info = facula_config;
    m_map_data.map_matrix.resize(mst_facula_sensor_info.sensor_info.ylens);  //

    //* 不判定，只显示
    mst_map_info.xlens            = mst_facula_sensor_info.sensor_info.xlens;
    mst_map_info.ylens            = mst_facula_sensor_info.sensor_info.ylens;
    mst_map_info.target_tf        = mst_facula_sensor_info.target_tf;
    mst_map_info.sensor_direction = mst_facula_sensor_info.sensor_direction;
    mst_map_info.facula_form      = mst_facula_sensor_info.facula_form;
    mst_map_info.symm_type        = mst_facula_sensor_info.symm_adjust_type;

    // externalMapInfoUpdate();
    // //* 选择判定光斑：原光斑/处理后光斑
    // if (mst_facula_sensor_info.facula_handle_type) {
    //     mi_facula_ = IFaculaFactory::getInstance().faculaAdjustCreate(facula_config.facula_type, mst_interpolation_map_info);
    // } else {
    mi_facula_ = IFaculaFactory::getInstance().faculaAdjustCreate(facula_config.facula_type, mst_map_info);
    // }
    m_map_interpolation_data.map_matrix.resize(mst_map_info.ylens);  //
    for (uint i = 0; i < mst_map_info.ylens; i++) {
        m_map_interpolation_data.map_matrix[i].resize(mst_map_info.xlens);
    }

    // 初始化新的图像处理算法
    loadProcessingConfig();
    initializeProcessingAlgorithms(m_processing_config);

    if (!targetFaculaArea()) {
        //        QMessageBox::warning(this,"", "光斑中心配置异常");
    }
}

CFaculaContext::~CFaculaContext() {
    delete mi_facula_;
}

void CFaculaContext::varibleInit() {
    mi_facula_->variblesInit();
}

bool CFaculaContext::targetFaculaArea() {
    if ((mst_facula_sensor_info.sensor_info.xlens < 3 || mst_facula_sensor_info.sensor_info.ylens < 2) ||
        (mst_facula_sensor_info.target_tf.ax >= mst_facula_sensor_info.sensor_info.xlens ||
         mst_facula_sensor_info.target_tf.ay >= mst_facula_sensor_info.sensor_info.ylens)) {

        return false;
    }
    return true;
}

// void CFaculaContext::targetFaculaAreaShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_) {
//    /*4. 目标区域凸显 (0,0),(1,0),(0,1),(-1,0),(0,-1)*/
//    volatile const uint8_t xlen = map_info_->xlens, ylen = map_info_->ylens;
//    volatile const uint8_t x_tf = map_info_->target_tf.ax, y_tf = map_info_->target_tf.ay;
//    int8_t coordinate_x = 0, coordinate_y = 0;
//    uint8_t tip_num = 0;

//    /*释放内存*/
//    uint8_t item_lens = map_info_->table_item.length();
//    if(item_lens != 0) {
//        for(uint i = 0; i < item_lens; i++) {
//            if(map_info_->table_item.at(i) != nullptr)
//                delete map_info_->table_item.at(i);
//        }
//    }
//    map_info_->table_item.clear();

//    for (uint y = 0; y < ylen; y++) {
////            if(y > y_tf)
////                coordinate_y = y - y_tf;
////            else
//            coordinate_y = y_tf - y;
//            coordinate_y = coordinate_y>0? coordinate_y:(-coordinate_y);
//        for (uint x = 0; x < xlen; x++) {
////                if(x > x_tf)
////                    coordinate_x = x - x_tf;
////                else
//            coordinate_x = x_tf - x;
//            coordinate_x = coordinate_x>0? coordinate_x:(-coordinate_x);

//            QTableWidgetItem *item = new QTableWidgetItem();
//            item->setForeground(Qt::yellow); //darkred
//            item->setFont(QFont("Times", 20, QFont::Black)); //加粗
//            if((coordinate_x <= 1) && (coordinate_y <= 1) && ((coordinate_x + coordinate_y) <= 1)) {
//                item->setForeground(Qt::green);
//                item->setFont(QFont("Times", 26, QFont::Black)); //加粗
//            }
//            item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);

//            /*2. MP提示值*/
//            if(mst_facula_sensor_info.sensor_info.order == ITable::EMpOrder::left_2_right) //左往右
//                tip_num = y*xlen + x + 1;
//            else
//                tip_num = x*ylen + y + 1;
//            item->setToolTip(QString::number(tip_num)); //提示 VI4300
//            map_info_->table_item.push_back(item);
//            map->setItem(y, x, item);
//        }
//    }
//}

// void CFaculaContext::greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data)
//{
//    uint8_t alpha_tmp = 180, rgb_tmp;
//    uint32_t data_tmp = 0;

//    uint8_t xlens = map_info_->xlens;
//    uint8_t ylens = map_info_->ylens;
//    uint32_t data_max = map_data.max_peak>10?map_data.max_peak:10; //peak 最小值

//    for (int y = 0; y < ylens; ++y)
//    {
//        for (int x = 0; x < xlens; ++x)
//        {
//            /*1. 数值 update*/
//            data_tmp = map_data.map_matrix.at(y).at(x);
//            map_info_->table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

//            /*3. 背景颜色*/
//            QColor color;
//            rgb_tmp = 255 - (data_tmp * 255 / data_max);
//            alpha_tmp = data_tmp * 255 / data_max;
//            color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
//            map->item(y,x)->setBackground(QBrush(color));
//        }
//    }
//}

void CFaculaContext::mergeDataClean() {
    mst_merge_data.map_data_cache.clear();
    mst_merge_data.merge_data_cnt = 0;
}

/**
 * @brief: 数据合并，均值，中值
 * @param: 原值，合并个数，处理方式
 * @result: 处理结果
 */
QVector<uint32_t> CFaculaContext::dataMerge(const QVector<uint32_t> &origin_data, const uint8_t &times, const uint8_t &types) {
    uint32_t          data_tmp = 0;
    QVector<uint32_t> data;
    uint8_t           num;
    if (times == 0)
        return data;
    num = origin_data.length() / times;
    if ((num * times) != origin_data.length())
        return data;
    if (types == 1) {
        for (uint16_t n = 0; n < num; ++n) {
            for (uint8_t j = 0; j < times; ++j)
                data_tmp += origin_data.at(j * num + n);
            data_tmp = data_tmp / times;
            data.push_back(data_tmp);
            data_tmp = 0;
        }
    }
    return data;
}

/**
 * @brief 数据合并处理
 * @return
 */
bool CFaculaContext::mapDataMerge(QByteArray &mp_origin_byte, StMergeData *merge_data_, QVector<uint32_t> *map_data_) {
    uint16_t num = mp_origin_byte.length() >> 2;

    if (merge_data_->merge_data_cnt >= DISCARD_PACK_NUM) {  //舍弃前几包
        for (uint16_t n = 0; n < num; ++n) {                //
            uint32_t data_tmp = (uchar)mp_origin_byte.at((n << 2) + 0) | ((uchar)mp_origin_byte.at((n << 2) + 1) << 8) |
                                ((uchar)mp_origin_byte.at((n << 2) + 2) << 16) | ((uchar)mp_origin_byte.at((n << 2) + 3) << 24);

            merge_data_->map_data_cache.push_back(data_tmp);  //
        }
    }

    merge_data_->merge_data_cnt++;
    mp_origin_byte.clear();

    if (merge_data_->merge_data_cnt == (MP_MERGE_TIMES + DISCARD_PACK_NUM)) {             // MP_MERGE_TIMES
        *map_data_ = dataMerge(merge_data_->map_data_cache, (uint8_t)MP_MERGE_TIMES, 1);  //

        merge_data_->merge_data_cnt = 0;
        merge_data_->map_data_cache.clear();

        return true;
    }

    return false;
}


/**
 * @brief CLenAdjustOpt::originDataHanlde
 * @param mp_origin_bytes -> 光斑矩阵数据
 * @return
 */
bool CFaculaContext::originDataHanlde(QByteArray &mp_origin_bytes) {
    uint16_t          num  = mp_origin_bytes.length() >> 2;
    uint8_t           ylen = mst_facula_sensor_info.sensor_info.ylens;
    uint8_t           xlen = mst_facula_sensor_info.sensor_info.xlens;
    QVector<uint32_t> map_data;  //

    if ((num == 0) || (num != (ylen * xlen))) {
        mergeDataClean();
        return false;
    }
    // 判断数据有效性
    //    if((num > 0) && num != m_cell_num_first) {//单元格更新
    //        m_cell_num_first = num;

    //        mergeDataClean();
    ////        merge_data_cnt = 0;
    ////        m_map_data_cache.clear();
    //        if(!cellNumUpdate(num)) return false; //
    //    }

    if (mapDataMerge(mp_origin_bytes, &mst_merge_data, &map_data)) {  //数据合并完成
        if (map_data.length() != ylen * xlen) {
            qDebug() << "-e clen/ map data length error" << map_data.length() << ylen * xlen;
            return false;
        }

        //* 添加数据,
        if (mst_facula_sensor_info.sensor_info.order == ITable::EMpOrder::left_2_right) {  //光斑顺序 左->右 YC
            for (int y = 0; y < ylen; ++y) {
                m_map_data.map_matrix[y].clear();
                for (int x = 0; x < xlen; ++x) {
                    m_map_data.map_matrix[y].push_back(map_data.at((y * xlen) + x));
                }
            }
        } else {  //上->下 4300 4302
            for (int i = 0; i < ylen; ++i) {
                m_map_data.map_matrix[i].clear();  //;
            }
            for (int x = 0; x < xlen; ++x) {
                for (int y = 0; y < ylen; ++y) {
                    m_map_data.map_matrix[y].push_back(map_data.at((x * ylen) + y));
                }
            }
        }
        return true;
    }
    return false;
}

bool CFaculaContext::faculaHandle(uint16_t &                           adjust_status,
                                  QByteArray &                         facula_origin_data,
                                  const C3dHandMachine::St3D<int16_t> &move_delta_step,
                                  C3dHandMachine::St3D<int16_t> *      move_dis_) {
    bool handled_facula_ok = false;

    //* 循环接收->数据处理  原始数据->处理后的数据
    if (originDataHanlde(facula_origin_data)) {
        mi_facula_->findMaxMP(&m_map_data);

        // 启用处理后光斑调节：使用新的图像处理算法
        if (processWithNewAlgorithms()) {
            handled_facula_ok = true;

            // 拓展光斑 最大值计算
            mi_facula_->findMaxMP(&m_map_interpolation_data);
        }
        // logProcessingInfo("新算法处理失败，回退到传统算法");
        // processWithLegacyAlgorithms();

        if (mst_facula_sensor_info.facula_handle_type && handled_facula_ok) {
            logProcessingInfo("使用优化后光斑 调节模式");
            adjust_status = mi_facula_->faculaAdjust(&m_map_interpolation_data, move_delta_step, move_dis_);
        } else {
            adjust_status = mi_facula_->faculaAdjust(&m_map_data, move_delta_step, move_dis_);
        }
        return true;
    } else {
        return false;
    }
}

//* 调节数据 - 优化后保持原始尺寸
// void CFaculaContext::externalMapInfoUpdate() {
//     uint8_t ylens = mst_facula_sensor_info.sensor_info.ylens;
//     uint8_t xlens = mst_facula_sensor_info.sensor_info.xlens;

//     /*保持与原始数据相同的尺寸，插值的目的是提高数据质量而不是改变尺寸*/
//     mst_interpolation_map_info.xlens = xlens;  // 保持原始宽度
//     mst_interpolation_map_info.ylens = ylens;  // 保持原始高度

//     // 目标中心点坐标保持不变
//     mst_interpolation_map_info.target_tf.ax = mst_facula_sensor_info.target_tf.ax;
//     mst_interpolation_map_info.target_tf.ay = mst_facula_sensor_info.target_tf.ay;

//     // 复制其他属性
//     mst_interpolation_map_info.sensor_direction = mst_facula_sensor_info.sensor_direction;
//     mst_interpolation_map_info.facula_form      = mst_facula_sensor_info.facula_form;
//     mst_interpolation_map_info.symm_type        = mst_facula_sensor_info.symm_adjust_type;

//     logProcessingInfo(QString("插值目标尺寸设置为: %1x%2 (与原始尺寸保持一致)").arg(xlens).arg(ylens));
// }

IFaculaAdjust::StMapInfo CFaculaContext::getExpandMapInfo() {
    return mst_map_info;
}

IFaculaAdjust::StMapData CFaculaContext::getOriginMapData() {
    return m_map_data;
}

IFaculaAdjust::StMapData CFaculaContext::getExpandMapData() {
    return m_map_interpolation_data;
}
// void CFaculaContext::targetMapUpdate(const IFaculaAdjust::StMapTargetInfo &target_map) {
//    mi_facula_->targetMapUpdate(target_map);
//}

// uint8_t CFaculaContext::faculaAdjust(IFaculaAdjust::StMapData *map_data_,
//                                     const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) {

//    return mi_facula_->faculaAdjust(map_data_, move_delta_step, move_dis_);
//}

/**
 * @brief CFaculaContext::faculaTest
 * @param facula_origin_data
 * @param adjust_status
 * @param target_facula
 * @param is_auto_adjust  自动调节
 * @return
 */
bool CFaculaContext::faculaTest(QByteArray &                           facula_origin_data,
                                uint16_t &                             adjust_status,
                                QVector<uint32_t> &                    target_facula,
                                const IFaculaAdjust::EFaculaJudgeMode &test_mode,
                                const bool &                           is_auto_adjust) {
    if (originDataHanlde(facula_origin_data)) {
        mi_facula_->findMaxMP(&m_map_data);
        adjust_status = mi_facula_->faculaTest(&m_map_data, target_facula, test_mode, is_auto_adjust);
        return true;
    } else {
        return false;
    }
}

bool CFaculaContext::localFaculaTest(const QVector<QVector<uint32_t>> &     map_matrix,
                                     uint16_t &                             adjust_status,
                                     QVector<uint32_t> &                    target_facula,
                                     const IFaculaAdjust::EFaculaJudgeMode &test_mode) {
    IFaculaAdjust::StMapData map_data;
    map_data.map_matrix = map_matrix;

    mi_facula_->findMaxMP(&map_data);
    adjust_status = mi_facula_->faculaTest(&map_data, target_facula, test_mode, false);
    return true;
}

QVector<QVector<uint32_t>> CFaculaContext::getMap() {
    return m_map_data.map_matrix;
}

void CFaculaContext::initializeProcessingAlgorithms(const ProcessingConfig &config) {
    logProcessingInfo("初始化图像处理算法");

    // 验证配置
    if (!FaculaProcessingConfig::validateConfig(config)) {
        logProcessingInfo("配置验证失败，使用默认配置");
    }

    try {
        // 创建插值器
        ImageProcessing::InterpolationType interpType = FaculaProcessingConfig::mapInterpolationType(config.interpolation_type);
        if (interpType != ImageProcessing::InterpolationType::None) {
            m_interpolator = ImageProcessing::InterpolationFactory::getInstance().createInterpolation(interpType);

            if (m_interpolator) {
                // 设置插值参数
                ImageProcessing::InterpolationParams interpParams = FaculaProcessingConfig::createInterpolationParams(config);
                m_interpolator->setParameters(interpParams);
                logProcessingInfo(QString("创建插值器成功: %1").arg(FaculaProcessingConfig::getInterpolationTypeDescription(interpType)));
            } else {
                logProcessingInfo("创建插值器失败");
            }
        }

        // 创建滤波器
        logProcessingInfo(QString("开始创建滤波器，filter_types配置: '%1'").arg(config.filter_types));
        QVector<ImageProcessing::FilterType> filterTypes = FaculaProcessingConfig::mapFilterTypes(config.filter_types);
        logProcessingInfo(QString("解析得到%1个滤波器类型").arg(filterTypes.size()));

        if (filterTypes.isEmpty()) {
            logProcessingInfo("无滤波器配置（filter_types=0或无效配置）");
        } else {
            for (const auto &filterType : filterTypes) {
                auto filter = ImageProcessing::FilterFactory::getInstance().createFilter(filterType);
                if (filter) {
                    // 根据滤波器类型设置参数
                    if (filterType == ImageProcessing::FilterType::Kalman) {
                        ImageProcessing::KalmanParams kalmanParams = FaculaProcessingConfig::createKalmanParams(config);
                        filter->setParameters(kalmanParams);
                    } else if (filterType == ImageProcessing::FilterType::Convolution) {
                        ImageProcessing::ConvolutionParams convParams = FaculaProcessingConfig::createConvolutionParams(config);
                        filter->setParameters(convParams);

                        // 设置预定义卷积核
                        ImageProcessing::ConvolutionFilter *convFilter = dynamic_cast<ImageProcessing::ConvolutionFilter *>(filter.get());
                        if (convFilter) {
                            convFilter->setPredefinedKernel("sharpen");  // 默认使用锐化核
                            logProcessingInfo("设置卷积滤波器核: sharpen");
                        } else {
                            logProcessingInfo("警告: 无法转换为ConvolutionFilter类型");
                        }
                    } else if (filterType == ImageProcessing::FilterType::Median) {
                        ImageProcessing::MedianParams medianParams = FaculaProcessingConfig::createMedianParams(config);
                        filter->setParameters(medianParams);
                    } else if (filterType == ImageProcessing::FilterType::Gaussian) {
                        ImageProcessing::GaussianParams gaussianParams = FaculaProcessingConfig::createGaussianParams(config);
                        filter->setParameters(gaussianParams);
                    } else if (filterType == ImageProcessing::FilterType::Bilateral) {
                        ImageProcessing::BilateralParams bilateralParams = FaculaProcessingConfig::createBilateralParams(config);
                        filter->setParameters(bilateralParams);
                    } else if (filterType == ImageProcessing::FilterType::WeightedAverage) {
                        ImageProcessing::WeightedAverageParams weightedParams = FaculaProcessingConfig::createWeightedAverageParams(config);
                        filter->setParameters(weightedParams);

                        // 设置预定义权重矩阵
                        ImageProcessing::WeightedAverageFilter *weightedFilter = dynamic_cast<ImageProcessing::WeightedAverageFilter *>(filter.get());
                        if (weightedFilter) {
                            weightedFilter->setPredefinedWeights(config.weighted_avg_preset);
                            logProcessingInfo(QString("设置加权均值滤波器权重: %1").arg(config.weighted_avg_preset));
                        } else {
                            logProcessingInfo("警告: 无法转换为WeightedAverageFilter类型");
                        }
                    }

                    m_filters.push_back(std::move(filter));
                    logProcessingInfo(QString("创建滤波器成功: %1").arg(FaculaProcessingConfig::getFilterTypeDescription(filterType)));
                } else {
                    logProcessingInfo(QString("创建滤波器失败: %1").arg(FaculaProcessingConfig::getFilterTypeDescription(filterType)));
                }
            }
        }

    } catch (const std::exception &e) {
        logProcessingInfo(QString("初始化处理算法时发生异常: %1").arg(e.what()));
    }
}

bool CFaculaContext::processWithNewAlgorithms() {
    try {
        // 转换数据格式
        ImageProcessing::ImageDataU32 srcImageData = FaculaDataAdapter::qvectorToImageData(m_map_data.map_matrix);
        // 目标尺寸与原始尺寸保持一致
        ImageProcessing::ImageDataU32 processedData(mst_map_info.xlens, mst_map_info.ylens);

        logProcessingInfo(QString("开始新算法处理: %1x%2").arg(srcImageData.width()).arg(srcImageData.height()));

        // 应用插值（如果配置了插值器）
        if (m_interpolator) {
            if (!m_interpolator->interpolate(srcImageData, processedData)) {
                logProcessingInfo("插值处理失败");
                return false;
            }
            logProcessingInfo("插值处理完成");
        } else {
            // 没有插值器时，直接复制数据
            processedData = srcImageData;
            logProcessingInfo("跳过插值处理（未配置插值器）");
        }

        // 应用滤波器（如果配置了滤波器）
        if (!m_filters.empty()) {
            for (auto &filter : m_filters) {
                if (filter && !filter->apply(processedData)) {
                    logProcessingInfo(QString("滤波器应用失败: %1").arg(filter->getAlgorithmName()));
                    // 继续处理其他滤波器，不直接返回失败
                }
            }
            logProcessingInfo(QString("滤波处理完成，应用了%1个滤波器").arg(m_filters.size()));
        } else {
            logProcessingInfo("跳过滤波处理（未配置滤波器）");
        }

        // 转换回QVector格式
        if (!FaculaDataAdapter::copyImageDataToQVector(processedData, m_map_interpolation_data.map_matrix)) {
            logProcessingInfo("数据格式转换失败");
            return false;
        }

        logProcessingInfo("新算法处理成功");
        return true;

    } catch (const std::exception &e) {
        logProcessingInfo(QString("新算法处理时发生异常: %1").arg(e.what()));
        return false;
    }
}

bool CFaculaContext::processWithLegacyAlgorithms() {
    try {
        // 使用传统的插值方法
        m_my_interPolation.bilinear_interpolation(m_map_data.map_matrix, m_map_interpolation_data.map_matrix);
        logProcessingInfo("传统算法处理成功");
        return true;
    } catch (const std::exception &e) {
        logProcessingInfo(QString("传统算法处理时发生异常: %1").arg(e.what()));
        return false;
    }
}

void CFaculaContext::loadProcessingConfig() {
    // 直接读取配置文件，避免循环依赖
    QString   configPath = "config/clen_config.ini";
    QSettings settings(configPath, QSettings::IniFormat);

    // 设置默认值
    m_processing_config.interpolation_type   = settings.value("FACULA_PROCESSING/interpolation_type", 0).toUInt();
    m_processing_config.filter_types         = settings.value("FACULA_PROCESSING/filter_types", "2").toString();
    m_processing_config.interpolation_offset = settings.value("FACULA_PROCESSING/interpolation_offset", 0.5).toFloat();

    // 滤波器参数
    m_processing_config.kalman_strength          = settings.value("FACULA_PROCESSING/kalman_strength", 1.0).toFloat();
    m_processing_config.convolution_kernel_size  = settings.value("FACULA_PROCESSING/convolution_kernel_size", 3).toUInt();
    m_processing_config.convolution_preset       = settings.value("FACULA_PROCESSING/convolution_preset", "sharpen").toString();
    m_processing_config.median_kernel_size       = settings.value("FACULA_PROCESSING/median_kernel_size", 3).toUInt();
    m_processing_config.median_preset            = settings.value("FACULA_PROCESSING/median_preset", "noise_reduction").toString();
    m_processing_config.gaussian_sigma           = settings.value("FACULA_PROCESSING/gaussian_sigma", 1.0).toFloat();
    m_processing_config.gaussian_kernel_size     = settings.value("FACULA_PROCESSING/gaussian_kernel_size", 5).toUInt();
    m_processing_config.gaussian_preset          = settings.value("FACULA_PROCESSING/gaussian_preset", "medium_blur").toString();
    m_processing_config.bilateral_sigma_color    = settings.value("FACULA_PROCESSING/bilateral_sigma_color", 75.0).toFloat();
    m_processing_config.bilateral_sigma_space    = settings.value("FACULA_PROCESSING/bilateral_sigma_space", 75.0).toFloat();
    m_processing_config.bilateral_kernel_size    = settings.value("FACULA_PROCESSING/bilateral_kernel_size", 5).toUInt();
    m_processing_config.bilateral_preset         = settings.value("FACULA_PROCESSING/bilateral_preset", "smooth").toString();
    m_processing_config.weighted_avg_kernel_size = settings.value("FACULA_PROCESSING/weighted_avg_kernel_size", 3).toUInt();
    m_processing_config.weighted_avg_preset      = settings.value("FACULA_PROCESSING/weighted_avg_preset", "uniform").toString();
    m_processing_config.filter_strength          = settings.value("FACULA_PROCESSING/filter_strength", 1.0).toFloat();

    logProcessingInfo(QString("配置加载完成: interpolation_type=%1, filter_types=%2, filter_strength=%3")
                          .arg(m_processing_config.interpolation_type)
                          .arg(m_processing_config.filter_types)
                          .arg(m_processing_config.filter_strength));
}

void CFaculaContext::logProcessingInfo(const QString &message) {
    qDebug() << "[CFaculaContext]" << message;
}

// void CFaculaContext::findMaxMP(IFaculaAdjust::StMapData *map_data_) {
//    mi_facula_->findMaxMP(map_data_);
//}
