# 本地MCP服务器集成

本目录包含本地MCP服务器的实现，用于集成产品开发工具链。遵循[[产品体系构建工具关联表]]定义的工具规范。

## 文档管理原则

- **奥卡姆剃刀原则**：选择最简单有效的实现方案，减少无效信息
- **单一职责原则**：避免功能重叠
- **单一来源原则**：避免多处重复信息

### 标准返回格式
所有工具遵循统一的返回格式：

```json
{
  "success": true|false,
  "timestamp": "ISO格式时间戳",
  "data": "具体结果数据",
  "error": "错误信息（仅在success=false时）",
  "metadata": {
    "tool_name": "工具名称",
    "execution_time": "执行时间",
    "script_path": "调用的脚本路径"
  }
}
```

### 错误处理
- 脚本执行失败：返回stderr内容
- 参数验证失败：返回参数错误信息
- 文件不存在：返回文件路径错误
- JSON解析失败：返回解析错误详情

## 配置方法

在`.cursor/mcp.json`中添加：

```json
{
  "mcpServers": {
    "product-development-complete": {
      "command": "python",
      "args": ["build-tools/mcp-server_local_integrations/unified/product-development-complete/server.py"],
      "cwd": "."
    }
  }
}
```

## 相关文档

- [[产品体系构建工具关联表]] - 工具定义的唯一来源
- [[build-tools/scripts/README.md]] - 被包装的本地脚本文档
- [[mcp-servers_test_report]] - 工具测试记录

## 目录结构

```text
mcp-server_local_integrations/
├── README.md                    # 本文档
├── tools/                       # 业务流程工具模块
│   ├── __init__.py             # 模块导入配置
│   ├── project_lifecycle.py    # 项目生命周期流程 (T06,T07,T08)
│   ├── requirements_flow.py    # 需求管理流程 (T01,T03,T04,T05,T09)
│   ├── development_flow.py     # 开发质量流程 (T15,T16,T22)
│   ├── document_flow.py        # 文档关联流程 (T23,T24,T25,T26)
│   ├── config_flow.py          # 配置管理流程 (T28相关6个工具)
│   ├── silent_wrapper.py       # 静默脚本执行包装器
│   ├── smart_params.py         # 智能参数处理
│   └── [向后兼容模块]           # 保留原有技术分组模块
│       ├── project_tools.py
│       ├── config_tools.py
│       ├── requirements_tools.py
│       ├── document_tools.py
│       ├── analysis_tools.py
│       └── visualization_tools.py
├── unified/                     # 统一集成服务器 (唯一实现)
│   └── product-development-complete/
│       ├── server.py            # 主服务器文件
│       ├── requirements.txt     # 依赖文件
│       ├── docs/               # 服务器文档
│       └── config/             # 服务器配置
├── examples/                   # 示例服务器
│   └── hello-world-mcp/
├── docs/                       # 文档目录
├── config/                     # 配置文件
└── mcp-servers_test_report.md  # 测试报告
```

## 开发约束

1. **禁止凭空增加工具**：所有工具必须在[[产品体系构建工具关联表]]中有明确定义
2. **严格脚本包装**：不得在MCP服务器中直接实现业务逻辑
3. **保持索引一致性**：工具索引T01-T28必须与关联表完全对应
4. **遵循MCP协议**：返回值格式、错误处理必须符合MCP规范
5. **共享工具模块**：所有服务器可使用 `tools/` 目录下的共享工具模块

## 服务器实现状态

### 统一集成服务器 (unified/) ⭐唯一实现

| 服务器 | 状态 | 架构特点 | 业务流程模块 | 文档链接 |
|--------|------|---------|-------------|----------|
| **product-development-complete/** | ✅ 已重构 | 业务流程导向架构 | 5个业务流程模块，24个核心工具 | [README](unified/product-development-complete/README.md) |

**业务流程模块详情：**

| 业务流程模块 | 包含工具 | 对应Scripts模块 | 主要功能 |
|-------------|---------|----------------|----------|
| **project_lifecycle.py** | T06, T07, T08 | `scripts/directory_initialization/`, `scripts/init_product_project.py` | 项目信息管理、初始化、结构创建 |
| **requirements_flow.py** | T01, T03, T04, T05, T09 | `scripts/requirements/`, `scripts/integration/` | 需求收集、分析、矩阵、转任务 |
| **development_flow.py** | T15, T16, T22 | `scripts/workflow/`, `scripts/quality/` | 开发支持、质量检查、验证报告 |
| **document_flow.py** | T23, T24, T25, T26 | `scripts/links/`, `scripts/canvas/`, `scripts/infoTrace/`, `scripts/visualization/` | 文档关联、Canvas同步、追溯、可视化 |
| **config_flow.py** | T28相关6个工具 | `scripts/config/` | 配置加载、保存、验证、合并、模板、备份 |

### 架构重构说明

**✅ 已完成的重构：**

- 移除重复实现：删除core/、development/、visualization/目录
- 业务流程重组：按产品开发流程组织工具模块
- 保持向后兼容：原有工具函数名称和调用方式不变
- Scripts脚本不变：底层实现完全不受影响

**🎯 重构优势：**

- 符合奥卡姆剃刀原则：消除不必要的复杂性
- 符合单一职责原则：每个业务流程职责明确
- 降低维护成本：统一服务器，统一维护
- 提高开发效率：按业务流程组织，更符合实际使用场景

**核心功能包括：**

- 🚀 项目初始化：`init_project()`
- ⚙️ 配置管理：`load_config()`, `save_config()`
- 📋 需求管理：`import_requirements()`, `analyze_requirements()`, `req_to_tasks()`
- 🔗 文档关联：`generate_document_links_config()`, `link_documents()`
- 🎨 Canvas同步：`sync_canvas()`
- 🔍 信息追溯：`manage_trace()`

### 示例服务器 (examples/)

| 服务器 | 状态 | 主要功能 | 用途 |
|--------|------|---------|------|
| **hello-world-mcp/** | ✅ 已实现 | 基础MCP服务器示例 | 学习和测试 |

## 🔧 快速配置

### 推荐配置：统一集成服务器 ⭐

```json
{
  "mcpServers": {
    "product-development-complete": {
      "command": "python",
      "args": ["F:/101_link-notebook/Obsidian-Vault/KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete/server.py"],
      "description": "产品开发完整功能集成MCP服务器"
    }
  }
}
```

**包含所有核心功能：**
- 项目初始化、配置管理、需求管理
- 文档关联、Canvas同步、信息追溯
- 一个服务器解决所有需求

### 模块化配置（可选）

```json
{
  "mcpServers": {
    "project-init": {
      "command": "python",
      "args": ["./mcp-server_local_integrations/core/project-initialization/server.py"]
    },
    "config-mgmt": {
      "command": "python",
      "args": ["./mcp-server_local_integrations/core/configuration-management/server.py"]
    },
    "requirements": {
      "command": "python",
      "args": ["./mcp-server_local_integrations/core/requirements-management/server.py"]
    }
  }
}
```

### 混合配置：自建+市场服务器

```json
{
  "mcpServers": {
    "product-development": {
      "command": "python",
      "args": ["./mcp-server_local_integrations/unified/product-development-complete/server.py"]
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/allowed/path"]
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"]
    }
  }
}
```

## 🛠️ 使用方法

### 安装依赖

```bash
# 安装统一服务器依赖
pip install -r unified/product-development-complete/requirements.txt

# 或安装特定服务器依赖
pip install -r core/project-initialization/requirements.txt
```

### 启动服务器

MCP服务器由Cursor自动启动，无需手动启动。配置完成后，直接与AI助手对话即可使用功能。

### 使用示例

```
# 项目初始化
"请帮我创建一个名为'新产品'的多层级项目"

# 配置管理
"请加载项目配置文件并验证其正确性"

# 需求分析
"请导入requirements.xlsx文件并分析需求分布"
```

## 📚 相关文档

- [[产品体系MCP Server.md]] - 详细架构和使用说明
- [MCP服务器创建指南](docs/mcp-server_create_guide.md) - 开发规范
- [配置指南](docs/configuration_guide.md) - 配置详解

## 🔧 自建vs市场服务器区分

### 自建MCP服务器
- **位置**：存储在 `mcp-server_local_integrations/` 目录下
- **特点**：专为产品开发框架定制，与scripts深度集成
- **管理**：版本控制，本地维护

### 市场MCP服务器
- **位置**：通过npm/npx引用，不存储在本地
- **特点**：通用功能，社区维护
- **管理**：通过配置文件引用，定期更新

### 推荐策略
- **核心业务功能**：使用自建服务器
- **通用工具功能**：使用市场服务器
- **混合部署**：根据需要灵活组合