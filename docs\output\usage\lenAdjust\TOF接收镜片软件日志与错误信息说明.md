## 1. 数据记录说明
1. 默认在桌面生成 ”镜片装调.csv”文件
    
    注意点:
    
    1. 该文件在打开软件时自动生成，若该文件被删除时，只需重开软件
    2. 调节过程中不能打开该文件，否则数据无法保存
2. 执行日志数据，在根目录下自动生成 error.log
    
    注：该文件需要定时删除，避免内存过大
    
## 2. 异常说明
异常主要分为两类
1. 导致调节异常错误，该类型错误直接在窗口日志显示框显示
    1. 步骤执行与指令接收错误，显示：
        
        ```C
        "task: " + step_name + "fatal";
        ```
        
    2. 过程异常
        
        ```C
        {0x0001, "串口打开失败 "},
        {0x0002, "版本异常 "},
        {0x0004, "芯片ID异常 "},
        {0x0008, "XY轴限位 "},
        {0x0010, "Z轴限位 "},
        {0x0020, "机台控制指令发送异常 "},
        ```
        
    3. 调节异常
        
        ```JavaScript
        {0x0001, "未找到光斑 "},
        {0x0002, "不能移动到目标区域 "},
        {0x0004, "无法对称分布 "},
        ```
        
2. 光斑判定异常
    
    ```C
    {0x0001, "光斑中心不在目标区域 "},
    {0x0002, "光斑十字不对称 "},
    {0x0004, "光斑外圈不对称 "},
    {0x0008, "中心光斑强度异常 "},
    {0x0010, "外圈光斑强度异常"},
    {0x0020, "光斑中心与十字区域强度差值异常"},
    ```
    
    - 举例
        
        ![Untitled 178.png](images/Untitled 178.png)
        
        异常码：20，对应错误："光斑中心与十字区域强度差值异常"