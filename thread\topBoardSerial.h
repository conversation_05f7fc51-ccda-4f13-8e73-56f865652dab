#ifndef _TOP_BOARD_SERIAL_H_
#define _TOP_BOARD_SERIAL_H_

#include <QString>
#include <QMessageBox>
#include <QDebug>
#include <QTime>
#include <QTimer>

#include "ITopBoard.h"

class CTopBoardSerial: public QObject
{
    Q_OBJECT
public:
    explicit CTopBoardSerial(QObject *parent = nullptr, ITopBoard *top_board_ = nullptr);
    ~CTopBoardSerial();

    uint16_t     m_task_id;

    void device_change_interface(ITopBoard* top_board_); //设备接口调整
    void task_id_change(const uint8_t &id);
public slots:
    //void portInit(bool isOpen);
    void loop(int task_id); //接收串口数据API

private:
    ITopBoard *mc_top_board_ = nullptr;
};

#endif
