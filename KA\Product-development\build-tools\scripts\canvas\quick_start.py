#!/usr/bin/env python3
"""
Canvas自动同步快速启动脚本
一键配置和启动Canvas自动同步系统
"""

import os
import sys
import platform
from pathlib import Path

def main():
    print("🎯 Canvas自动同步快速启动")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    print(f"📁 项目根目录: {project_root}")
    
    # 检查依赖
    try:
        import watchdog
        print("🐍 Watchdog: ✓ 已安装")
    except ImportError:
        print("🐍 Watchdog: ✗ 未安装")
        print("   请运行: pip install watchdog")
        return
    
    # 检查Canvas文件
    canvas_file = project_root / "product.canvas"
    if canvas_file.exists():
        print(f"🎨 Canvas文件: ✓ {canvas_file}")
    else:
        print("🎨 Canvas文件: ✗ 未找到product.canvas")
    
    # 选择方法
    print("\n🚀 选择启动方法:")
    print("1. Obsidian Plugin集成")
    print("2. Watchdog后台监控")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        print("\nObsidian配置说明:")
        print("1. 安装Shell Commands插件")
        print("2. 添加命令:")
        print(f"   Command: bash \"{project_root}/scripts/canvas/on_canvas_save.sh\" \"{{{{file_path:absolute}}}}\"")
        print("3. 启用: File content modified")
        
    elif choice == "2":
        print("\n🚀 启动Canvas监控器...")
        watcher_script = project_root / "scripts" / "canvas" / "canvas_watcher.py"
        
        if watcher_script.exists():
            os.system(f"python \"{watcher_script}\"")
        else:
            print(f"✗ 监控脚本不存在: {watcher_script}")
    
    print("\n✨ 完成！")

if __name__ == "__main__":
    main() 