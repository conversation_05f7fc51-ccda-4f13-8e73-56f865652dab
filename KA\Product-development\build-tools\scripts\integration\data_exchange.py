#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统集成数据交换标准
定义了系统间数据交换的标准格式和验证方法
"""

import os
import json
import jsonschema
from datetime import datetime
from typing import Dict, List, Any, Optional


class DataExchangeFormat:
    """系统间数据交换标准格式"""
    
    @staticmethod
    def task_data_schema() -> Dict:
        """任务数据模式
        
        Returns:
            任务数据JSON模式
        """
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "required": ["tasks", "metadata"],
            "properties": {
                "tasks": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["id", "title", "status"],
                        "properties": {
                            "id": {"type": "string"},
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "status": {"type": "string"},
                            "estimate": {"type": ["number", "null"]},
                            "actual": {"type": ["number", "null"]},
                            "assignee": {"type": ["string", "null"]},
                            "created_at": {"type": "string", "format": "date-time"},
                            "updated_at": {"type": "string", "format": "date-time"}
                        }
                    }
                },
                "metadata": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "string"},
                        "timestamp": {"type": "string", "format": "date-time"},
                        "version": {"type": "string"}
                    }
                }
            }
        }
    
    @staticmethod
    def document_data_schema() -> Dict:
        """文档数据模式
        
        Returns:
            文档数据JSON模式
        """
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "required": ["documents", "metadata"],
            "properties": {
                "documents": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["path", "title", "completion"],
                        "properties": {
                            "path": {"type": "string"},
                            "title": {"type": "string"},
                            "completion": {"type": "number"},
                            "word_count": {"type": "integer"},
                            "status": {"type": "string"},
                            "last_modified": {"type": "string", "format": "date-time"}
                        }
                    }
                },
                "metadata": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "string"},
                        "timestamp": {"type": "string", "format": "date-time"},
                        "version": {"type": "string"}
                    }
                }
            }
        }
        
    @staticmethod
    def validate_task_data(data: Dict) -> bool:
        """验证任务数据是否符合schema
        
        Args:
            data: 要验证的数据
            
        Returns:
            验证通过返回True，否则返回False
        """
        try:
            jsonschema.validate(data, DataExchangeFormat.task_data_schema())
            return True
        except jsonschema.exceptions.ValidationError as e:
            print(f"任务数据验证失败: {e}")
            return False
            
    @staticmethod
    def validate_document_data(data: Dict) -> bool:
        """验证文档数据是否符合schema
        
        Args:
            data: 要验证的数据
            
        Returns:
            验证通过返回True，否则返回False
        """
        try:
            jsonschema.validate(data, DataExchangeFormat.document_data_schema())
            return True
        except jsonschema.exceptions.ValidationError as e:
            print(f"文档数据验证失败: {e}")
            return False
            
    @staticmethod
    def create_task_data(tasks: List[Dict], source: str = "unknown") -> Dict:
        """创建符合标准的任务数据
        
        Args:
            tasks: 任务列表
            source: 数据源名称
            
        Returns:
            符合标准的任务数据
        """
        data = {
            "tasks": tasks,
            "metadata": {
                "source": source,
                "timestamp": datetime.now().isoformat(),
                "version": "1.0"
            }
        }
        
        if not DataExchangeFormat.validate_task_data(data):
            raise ValueError("生成的任务数据不符合schema")
            
        return data
        
    @staticmethod
    def create_document_data(documents: List[Dict], source: str = "unknown") -> Dict:
        """创建符合标准的文档数据
        
        Args:
            documents: 文档列表
            source: 数据源名称
            
        Returns:
            符合标准的文档数据
        """
        data = {
            "documents": documents,
            "metadata": {
                "source": source,
                "timestamp": datetime.now().isoformat(),
                "version": "1.0"
            }
        }
        
        if not DataExchangeFormat.validate_document_data(data):
            raise ValueError("生成的文档数据不符合schema")
            
        return data


class DataExchangeManager:
    """数据交换管理器
    
    负责系统间数据交换的导入、导出、转换等操作
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化数据交换管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config() if config_path else {}
        
    def _load_config(self) -> Dict:
        """加载配置
        
        Returns:
            配置字典
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载配置失败: {e}")
            return {}
            
    def export_data(self, data: Dict, format_type: str, destination: str) -> bool:
        """导出数据
        
        Args:
            data: 要导出的数据
            format_type: 数据类型，task或document
            destination: 目标路径
            
        Returns:
            导出成功返回True，否则返回False
        """
        try:
            # 验证数据格式
            if format_type == "task":
                if not DataExchangeFormat.validate_task_data(data):
                    return False
            elif format_type == "document":
                if not DataExchangeFormat.validate_document_data(data):
                    return False
            else:
                print(f"不支持的数据类型: {format_type}")
                return False
                
            # 确保目录存在
            os.makedirs(os.path.dirname(destination), exist_ok=True)
            
            # 导出数据
            with open(destination, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            return True
        except Exception as e:
            print(f"导出数据失败: {e}")
            return False
            
    def import_data(self, source: str, format_type: str) -> Optional[Dict]:
        """导入数据
        
        Args:
            source: 源文件路径
            format_type: 数据类型，task或document
            
        Returns:
            导入的数据，失败返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(source):
                print(f"文件不存在: {source}")
                return None
                
            # 读取数据
            with open(source, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 验证数据格式
            if format_type == "task":
                if not DataExchangeFormat.validate_task_data(data):
                    return None
            elif format_type == "document":
                if not DataExchangeFormat.validate_document_data(data):
                    return None
            else:
                print(f"不支持的数据类型: {format_type}")
                return None
                
            return data
        except Exception as e:
            print(f"导入数据失败: {e}")
            return None
            
    def convert_task_to_document(self, task_data: Dict) -> Dict:
        """将任务数据转换为文档数据格式
        
        Args:
            task_data: 任务数据
            
        Returns:
            文档数据
        """
        # 验证任务数据
        if not DataExchangeFormat.validate_task_data(task_data):
            raise ValueError("任务数据不符合schema")
            
        # 转换为文档数据
        documents = []
        for task in task_data.get("tasks", []):
            # 创建一个虚拟文档
            document = {
                "path": f"tasks/{task['id']}.md",
                "title": task["title"],
                "completion": 1.0 if task["status"].lower() == "done" else 0.5,
                "word_count": len(task.get("description", "").split()) + len(task["title"].split()),
                "status": "completed" if task["status"].lower() == "done" else "in_progress",
                "last_modified": task.get("updated_at", datetime.now().isoformat())
            }
            documents.append(document)
            
        # 创建文档数据
        return DataExchangeFormat.create_document_data(
            documents=documents,
            source=task_data.get("metadata", {}).get("source", "task_conversion")
        )


# 使用示例
if __name__ == "__main__":
    # 创建任务数据
    tasks = [
        {
            "id": "TASK-101",
            "title": "实现用户认证模块",
            "description": "开发基于JWT的用户认证系统",
            "status": "in_progress",
            "estimate": 24.0,
            "actual": 12.5,
            "assignee": "developer1",
            "created_at": "2025-05-15T08:30:00Z",
            "updated_at": "2025-05-18T15:45:00Z"
        },
        {
            "id": "TASK-102",
            "title": "设计数据库模型",
            "description": "设计用户和权限相关数据库模型",
            "status": "done",
            "estimate": 8.0,
            "actual": 6.0,
            "assignee": "developer2",
            "created_at": "2025-05-14T10:15:00Z",
            "updated_at": "2025-05-16T11:30:00Z"
        }
    ]
    
    # 创建标准任务数据
    task_data = DataExchangeFormat.create_task_data(tasks, "task_master")
    
    # 数据交换管理器
    manager = DataExchangeManager()
    
    # 导出任务数据
    export_path = "temp/task_data_export.json"
    if manager.export_data(task_data, "task", export_path):
        print(f"任务数据导出到 {export_path} 成功")
        
    # 转换为文档数据
    document_data = manager.convert_task_to_document(task_data)
    
    # 导出文档数据
    export_path = "temp/document_data_export.json"
    if manager.export_data(document_data, "document", export_path):
        print(f"文档数据导出到 {export_path} 成功") 