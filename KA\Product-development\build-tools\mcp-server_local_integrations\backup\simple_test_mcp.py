#!/usr/bin/env python3
"""
简单的产品开发MCP服务器测试版本
"""

import subprocess
import sys
from pathlib import Path

# 尝试导入mcp库
try:
    from mcp.server.fastmcp import FastMCP
except ImportError:
    print("❌ 无法导入mcp库，请安装: pip install mcp", file=sys.stderr)
    sys.exit(1)

# 创建MCP服务器实例
mcp = FastMCP("Product Development Test")

# 设置脚本基础路径
SCRIPTS_BASE = Path(__file__).parent.parent

@mcp.tool()
def init_project(project_name: str, project_path: str = "") -> str:
    """
    初始化新的产品项目
    
    Args:
        project_name: 项目名称
        project_path: 项目路径（可选）
    """
    script_path = SCRIPTS_BASE / "init_product_project.py"
    
    if not script_path.exists():
        return f"❌ 脚本不存在: {script_path}"
    
    # 构建命令
    cmd = [sys.executable, str(script_path), "--name", project_name]
    
    if project_path:
        cmd.extend(["--path", project_path])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            return f"✅ 项目初始化成功\n{result.stdout}"
        else:
            return f"❌ 项目初始化失败\n错误: {result.stderr}\n输出: {result.stdout}"
            
    except Exception as e:
        return f"❌ 执行脚本时出错: {str(e)}"

@mcp.tool()
def test_connection() -> str:
    """测试MCP连接"""
    return "✅ MCP服务器连接正常！当前时间: " + str(Path(__file__).stat().st_mtime)

@mcp.tool()
def get_scripts_info() -> str:
    """获取脚本信息"""
    info = []
    info.append(f"脚本基础路径: {SCRIPTS_BASE}")
    info.append(f"路径存在: {SCRIPTS_BASE.exists()}")
    
    # 检查关键脚本
    key_scripts = [
        "init_product_project.py",
        "config/generate_all_configs.py",
        "links/auto_link_documents.py"
    ]
    
    for script_name in key_scripts:
        script_path = SCRIPTS_BASE / script_name
        info.append(f"{script_name}: {'✅' if script_path.exists() else '❌'}")
    
    return "\n".join(info)

if __name__ == '__main__':
    mcp.run() 