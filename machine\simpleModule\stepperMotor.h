#ifndef _STEPPRE_MOTOR_H_
#define _STEPPER_MOTOR_H_

#include <QMap>
#include <QVector>
#include "ISimpleModule.h"
#include "IComm.h"

class CStepperMotor:public ISimpleModule
{
  Q_OBJECT
public:
  CStepperMotor(IComm *port_); //设备端口与PC端口绑定，构造时直接绑定 PC端口
  ~CStepperMotor();

  //static const QMap<QString, QVector<unsigned char>> s_motorB_cmd;

  uint getUartBaud() override;
  bool start(const uint8_t& id, const QByteArray &data) override; //开始
  bool stop(const QByteArray &data) override; //停止

  QByteArray portDataRead() override;
  bool readInfo(const uint8_t &id, const uint16_t &data) override;

  void changeIcomInterface(IComm *port_) override;
  bool interactionParsing(QByteArray str, int length) override;
  bool dataParsing(QByteArray str, const int &length) override;
signals:
  void dataOutput(ISimpleModule::ECommStep, ECommStatus, QByteArray);

private:
  enum EProtocolId{
    eCONTINUAL          = 0xa1, //连续
    eCYCLE              = 0xa2, //定圈
    eTIME               = 0xa3, //定时

    eSTOP               = 0xB1, //
  };

  IComm* im_port_ = nullptr;
  QByteArray m_str_send; //指令数据

};


#endif
