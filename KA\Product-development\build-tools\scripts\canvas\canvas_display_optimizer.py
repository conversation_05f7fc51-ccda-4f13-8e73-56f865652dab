#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Canvas显示优化器
根据配置文件决定文档的显示模式（卡片/链接/折叠）
"""

import json
import re
import os
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta

class DocumentImportanceScorer:
    """文档重要性评分器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.scoring_config = config.get("importance_scoring", {})
        
    def calculate_importance_score(self, doc_info: Dict[str, Any]) -> int:
        """
        计算文档重要性评分
        
        Args:
            doc_info: 文档信息
            
        Returns:
            int: 重要性评分
        """
        score = 0
        
        # 文件类型权重
        score += self._get_file_type_score(doc_info)
        
        # 修改时间权重
        score += self._get_modification_time_score(doc_info)
        
        # 文件大小权重
        score += self._get_file_size_score(doc_info)
        
        # 路径深度权重
        score += self._get_path_depth_score(doc_info)
        
        # 文件模式匹配加分
        score += self._get_pattern_bonus(doc_info)
        
        return max(0, score)  # 确保评分不为负数
    
    def _get_file_type_score(self, doc_info: Dict[str, Any]) -> int:
        """获取文件类型评分"""
        file_name = doc_info.get('doc_name', '').lower()
        file_type_weights = self.scoring_config.get("file_type_weights", {})
        
        # 检查特殊文件名
        if 'readme' in file_name:
            return file_type_weights.get("README.md", 0)
        
        # 检查文件扩展名
        file_path = doc_info.get('doc_path', '')
        if file_path.endswith('.md'):
            if any(keyword in file_name for keyword in ['设计', 'design']):
                return file_type_weights.get("设计文档", 0)
            elif any(keyword in file_name for keyword in ['需求', 'requirement']):
                return file_type_weights.get("需求文档", 0)
            elif any(keyword in file_name for keyword in ['api', 'interface']):
                return file_type_weights.get("API文档", 0)
        elif file_path.endswith(('.py', '.js', '.java', '.cpp', '.c')):
            return file_type_weights.get("代码文件", 0)
        elif file_path.endswith(('.json', '.yaml', '.yml', '.xml', '.ini')):
            return file_type_weights.get("配置文件", 0)
        elif file_path.endswith(('.log', '.txt')):
            return file_type_weights.get("日志文件", 0)
        
        return 0
    
    def _get_modification_time_score(self, doc_info: Dict[str, Any]) -> int:
        """获取修改时间评分"""
        # 这里需要实际的文件修改时间，暂时返回默认值
        # 实际实现中需要检查文件的修改时间
        modification_weights = self.scoring_config.get("modification_time_weights", {})
        return modification_weights.get("recent_30_days", 0)
    
    def _get_file_size_score(self, doc_info: Dict[str, Any]) -> int:
        """获取文件大小评分"""
        # 这里需要实际的文件大小，暂时返回默认值
        # 实际实现中需要检查文件大小
        size_weights = self.scoring_config.get("file_size_weights", {})
        return size_weights.get("optimal_size", 0)
    
    def _get_path_depth_score(self, doc_info: Dict[str, Any]) -> int:
        """获取路径深度评分"""
        doc_path = doc_info.get('doc_path', '')
        depth = len(doc_path.split('/')) - 1
        
        depth_weights = self.scoring_config.get("path_depth_weights", {})
        
        if depth <= 1:
            return depth_weights.get("root_or_first_level", 0)
        elif depth == 2:
            return depth_weights.get("second_level", 0)
        else:
            return depth_weights.get("third_level_or_deeper", 0)
    
    def _get_pattern_bonus(self, doc_info: Dict[str, Any]) -> int:
        """获取文件模式匹配加分"""
        doc_name = doc_info.get('doc_name', '')
        pattern_rules = self.config.get("file_pattern_rules", [])
        
        total_bonus = 0
        for rule in pattern_rules:
            pattern = rule.get("pattern", "")
            if re.match(pattern, doc_name, re.IGNORECASE):
                total_bonus += rule.get("importance_bonus", 0)
        
        return total_bonus

class CanvasDisplayOptimizer:
    """Canvas显示优化器"""
    
    def __init__(self, project_path: Path, config_path: Path = None):
        self.project_path = Path(project_path)
        self.config_path = config_path or (self.project_path / "config" / "canvas_display_config.json")
        self.config = self._load_config()
        self.scorer = DocumentImportanceScorer(self.config)
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"配置文件不存在: {self.config_path}")
                return self._get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "global_settings": {
                "max_cards_per_group": 15,
                "max_total_cards": 50,
                "default_display_mode": "auto"
            },
            "display_thresholds": {
                "always_card": 8,
                "prefer_card": 5,
                "prefer_link": 3,
                "always_collapsed": 1
            }
        }
    
    def determine_display_mode(self, doc_info: Dict[str, Any], 
                             component: str, project_group_size: int) -> str:
        """
        确定文档的显示模式
        
        Args:
            doc_info: 文档信息
            component: 组件名称
            project_group_size: 项目组文档数量
            
        Returns:
            str: 显示模式 ('card', 'link', 'collapsed')
        """
        # 检查自定义覆盖规则
        override_mode = self._check_custom_overrides(doc_info)
        if override_mode:
            return override_mode
        
        # 计算重要性评分
        importance_score = self.scorer.calculate_importance_score(doc_info)
        
        # 获取阈值配置
        thresholds = self.config.get("display_thresholds", {})
        
        # 根据重要性评分决定显示模式
        if importance_score >= thresholds.get("always_card", 8):
            return "card"
        
        # 根据项目组大小调整策略
        if project_group_size <= 10:
            # 小项目组：优先显示为卡片
            if importance_score >= thresholds.get("prefer_card", 5):
                return "card"
            else:
                return "link"
        elif project_group_size <= 30:
            # 中项目组：混合显示
            if importance_score >= thresholds.get("prefer_card", 5):
                return "card"
            elif importance_score >= thresholds.get("prefer_link", 3):
                return "link"
            else:
                return "collapsed"
        else:
            # 大项目组：主要显示为链接
            if importance_score >= 7:  # 更高的卡片阈值
                return "card"
            elif importance_score >= thresholds.get("prefer_link", 3):
                return "link"
            else:
                return "collapsed"
    
    def _check_custom_overrides(self, doc_info: Dict[str, Any]) -> str:
        """检查自定义覆盖规则"""
        doc_path = doc_info.get('doc_path', '')
        doc_name = doc_info.get('doc_name', '')
        
        overrides = self.config.get("custom_overrides", {})
        
        # 检查强制卡片
        if any(pattern in doc_path or pattern in doc_name 
               for pattern in overrides.get("force_card", [])):
            return "card"
        
        # 检查强制链接
        if any(pattern in doc_path or pattern in doc_name 
               for pattern in overrides.get("force_link", [])):
            return "link"
        
        # 检查强制折叠
        if any(pattern in doc_path or pattern in doc_name 
               for pattern in overrides.get("force_collapsed", [])):
            return "collapsed"
        
        return None
    
    def optimize_component_display(self, component: str, 
                                 documents: List[Dict[str, Any]]) -> Dict[str, List[Dict]]:
        """
        优化组件的文档显示
        
        Args:
            component: 组件名称
            documents: 文档列表
            
        Returns:
            Dict: 按显示模式分组的文档
        """
        result = {
            "cards": [],
            "links": [],
            "collapsed": []
        }
        
        # 按项目组分组
        project_groups = {}
        for doc in documents:
            group = doc.get('group', 'default')
            if group not in project_groups:
                project_groups[group] = []
            project_groups[group].append(doc)
        
        # 为每个项目组优化显示
        for group_name, group_docs in project_groups.items():
            group_size = len(group_docs)
            
            for doc in group_docs:
                display_mode = self.determine_display_mode(doc, component, group_size)
                result[f"{display_mode}s"].append({
                    "document": doc,
                    "display_mode": display_mode,
                    "importance_score": self.scorer.calculate_importance_score(doc)
                })
        
        return result

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Canvas显示优化器")
    parser.add_argument("--project-path", default=".", help="项目路径")
    parser.add_argument("--test", action="store_true", help="测试模式")
    
    args = parser.parse_args()
    
    optimizer = CanvasDisplayOptimizer(args.project_path)
    
    if args.test:
        # 测试文档
        test_doc = {
            "doc_name": "README.md",
            "doc_path": "README.md",
            "doc_id": "TEST001"
        }
        
        score = optimizer.scorer.calculate_importance_score(test_doc)
        mode = optimizer.determine_display_mode(test_doc, "REQ", 15)
        
        print(f"测试文档: {test_doc['doc_name']}")
        print(f"重要性评分: {score}")
        print(f"显示模式: {mode}")

if __name__ == "__main__":
    main()
