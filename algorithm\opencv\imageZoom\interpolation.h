#ifndef _INTERPOLATION_H_
#define _INTERPOLATION_H_

#include <QVector>

class my_interPolation {
public:
    explicit my_interPolation();
    ~my_interPolation();

    /*插值*/
    void bilinear_interpolation(const QVector<QVector<uint32_t>> &src_array, QVector<QVector<uint32_t>> &dst_array); //
    void biCubic_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num);
    void nonlinear_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num);
    /*图像处理*/
    void median_filter();
    void gaussian_filter();
    void kalman_filter();
    void bilateral_filter();
    void guide_filter();
    void Convolution_filter();
private:
    inline float src_index_cal(const uint8_t &dst_index, const uint8_t &src_board_len, const uint8_t &dst_board_len, const float &offset);
    inline float src_index_cal(const uint8_t &dst_index, const uint8_t &src_board_len, const uint8_t &dst_board_len);
    inline uint32_t linear_iterpolation(const float &rate, const uint32_t &point_f, const uint32_t &point_b);
    inline uint32_t weight_iterpolation(const float &rate, const uint32_t &point_f, const uint32_t &point_b);
    inline uint32_t bilinear_interpolation_cal(const float &rateX, const float &rateY, const uint32_t &f1, const uint32_t &f2, const uint32_t &f3, const uint32_t &f4);
};

#endif
