#include "saveTxt.h"

#include <QFile>
#include <QIODevice>
#include <QTextStream>
#include <QMessageBox>
#include <QDateTime>

CSaveTxt::CSaveTxt(){
    m_message_box_ = new QWidget;
}

CSaveTxt::~CSaveTxt(){
    if(m_message_box_ != nullptr) delete m_message_box_;
}


void CSaveTxt::saveFile(const QString &loc_name, const QString &fName, const QMap<QString,QVariant> &data) {
    QString dtStr=loc_name + fName+"DbToExcel" + QDateTime::currentDateTime().toString("yyyyMMddHHmmss") + ".txt";
    //*
    QFile fileOut(dtStr);
    if(!fileOut.open(QIODevice::Append|QIODevice::ReadWrite)) {
        QMessageBox::warning(m_message_box_, QString("保存文件"),
                             QString("打开保存文件失败：") + fileOut.errorString());
        return;
    }
    QTextStream tsOut(&fileOut);
    //recordFrameCnt++;
    //tsOut<<"#" << recordFrameCnt << " " << pointNum <<endl;
    //tsOut<<data.at(0).speed <<endl;

    fileOut.close();
}

