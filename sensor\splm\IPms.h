#ifndef _IPMS_H_
#define _IPMS_H_

#include <QObject>
#include <QByteArray>
#include <QMap>
#include <QApplication>

#include "ILoad.h"
#include "IComm.h"
#include "IProtocol.h"
#include "ISpmsSoc.h"

#include "processListA.h"

class IPms:public QObject {
    //  Q_OBJECT
public:
    IPms();
    virtual ~IPms(){};

    enum ECommStep{
        eCHIP_ID,
        eVERSION,
        eMODE_CHANGE,
        eALL_INFO,
        eDIST,
        eDDS,
    };


    enum class EProjectName
    {
        eNOVA_A1            = 0,
        eNOVA_A1B           ,
        eNOVA_B1            ,
    };

protected:
    //* 全部交互指令
    QMap<QString, QByteArray> m_interaction_cmd;
//    = {
//        {"eCALIB1", {0}},
//        {"eCALIB2", {0}},
//        {"eCALIB3", {0}},
//        {"eCALIB4", {0}},

//        {"modify_param", {0}},
//        {"mode_change", {0}},

//        {"data_output", {0}},
//        {"log_output", {0}},

//        {"read_version", {0}},
//        {"read_param",{0}},
//        {"chip_id", {0}}, //
//    };

    //*
    typedef struct {
        bool        is_test; //
        QString     item_name; //
        uint16_t    min_standard_value;
        uint16_t    max_standard_value;
        float       Standard_deviation;
    } StTestItem;


    typedef EExecStatus (IPms::*typeFptr_)(void);
    typedef struct {
        typeFptr_                       ptr_;
        typeFptr_                       ack_ptr_;
    } StCalibFunc;

    typedef struct {
        QByteArray                      cmd;
//        typeFptr_                       ptr_;
//        typeFptr_                       ack_ptr_;

        ISpmsSoc::StCalibItems          calib_items;
    } StCalibTask;

    QMap<QString, QString> mm_calib_param;
    QVector<ISpmsSoc::StCalibItems> mv_calib_items;
    QVector<StCalibFunc> mv_calib_func;

//    QMap<QString, QByteArray> mm_calib_flow;
//    QMap<QString, StCalibTask> mm_calib_flow;
    QMap<ISpmsSoc::ECalibProcess, StCalibTask> mm_calib_flow;
//    QMap<QString, QByteArray> m_calib_cmd;

    ILoad* mi_load_ = nullptr;
    IProtocol *m_general_protocol_ = nullptr; //交互指令
    IProtocol *m_protocol_ = nullptr; //一个设备一种协议
    ISpmsSoc *mi_spms_soc_ = nullptr; //一种传感器

    virtual EExecStatus calibTask1(void) = 0;
    virtual EExecStatus calibTask1Ack(void) = 0;
    virtual EExecStatus calibTask2(void) = 0;
    virtual EExecStatus calibTask2Ack(void) = 0;
    virtual EExecStatus calibTask3(void) = 0;
    virtual EExecStatus calibTask3Ack(void) = 0;
    virtual EExecStatus calibTask4(void) = 0;
    virtual EExecStatus calibTask4Ack(void) = 0;

public:
    QByteArray  m_strPre;

    virtual QByteArray portDataRead(void) = 0;
    virtual void icom_change_interface(IComm* port_) = 0;

    //*
    virtual QMap<QString, QByteArray>* getCmdList(void) = 0;
    virtual bool writeCmdList(const QMap<QString, QByteArray> &cmd_list) = 0;

    //* Functional function
    virtual bool modeChange(const uint16_t &mode) = 0;
    virtual bool changeRigster() = 0;
    virtual bool readInfo(const uint8_t &id, const uint16_t &data) = 0;

//    virtual void calibTasksInit(void) = 0;

//    virtual void verifyTasksInit(void) = 0;

    virtual EExecStatus calibTasksRun(void) = 0;
    virtual EExecStatus calibSingleTaskRun(ISpmsSoc::ECalibProcess calib_step) = 0;

    virtual EExecStatus verifyTasksRun(void) = 0;
    virtual EExecStatus functionTestRun(void) = 0;

    virtual bool interactionParsing(QByteArray str, int length) = 0; //交互指令解析
    virtual bool dataParsing(QByteArray str, int length) = 0; //灰度图
};

#endif
