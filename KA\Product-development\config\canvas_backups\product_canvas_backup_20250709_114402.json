{"nodes": [{"id": "2fab43022c4e", "type": "group", "label": "DEV", "x": 100, "y": 50, "width": 1360, "height": 1670, "color": "#2196F3"}, {"id": "0c63d961877f", "type": "group", "label": "SOFT", "x": 130, "y": 430, "width": 880, "height": 720, "color": "#4CAF50"}, {"id": "5a6172a4f5d5", "type": "group", "label": "嵌入式软", "x": 130, "y": 880, "width": 880, "height": 720, "color": "#4CAF50"}, {"id": "552360ade977", "type": "group", "label": "DEL", "x": 1400, "y": 50, "width": 1000, "height": 1350, "color": "#2196F3"}, {"id": "DEV001F59A9D", "type": "file", "file": "/development/issue system.md", "x": 130, "y": 110, "width": 400, "height": 280, "color": "#96CEB4"}, {"id": "DEV002C3074D", "type": "file", "file": "/development/software_process/MCP服务器集成与AI调用技术方案.md", "x": 160, "y": 470, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV0032C5E1D", "type": "file", "file": "/development/software_process/README_ENV.md", "x": 580, "y": 470, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV00442519F", "type": "file", "file": "/development/software_process/工业软件设计文档.md", "x": 160, "y": 780, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV005B17BEE", "type": "file", "file": "/development/software_process/跨设备访问与安全方案.md", "x": 580, "y": 780, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV006558BB8", "type": "file", "file": "/development/嵌入式软件开发流程/embedded_issue-demo.md", "x": 160, "y": 920, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV007136B3D", "type": "file", "file": "/development/嵌入式软件开发流程/嵌入式项目文档结构.md", "x": 580, "y": 920, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV008D2805C", "type": "file", "file": "/development/嵌入式软件开发流程/嵌入式项目设计文档.md", "x": 160, "y": 1230, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEV009596BEF", "type": "file", "file": "/development/嵌入式软件开发流程/开发项到输出闭环追溯框架.md", "x": 580, "y": 1230, "width": 400, "height": 290, "color": "#96CEB4"}, {"id": "DEL001EDB505", "type": "file", "file": "/deliverables/新产品体系构建完整流程.md", "x": 1430, "y": 110, "width": 400, "height": 280, "color": "#F7DC6F"}], "edges": []}