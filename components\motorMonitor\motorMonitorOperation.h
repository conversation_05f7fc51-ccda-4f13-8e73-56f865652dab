#ifndef _MOTOR_MONITOR_OPERATION_H_
#define _MOTOR_MONITOR_OPERATION_H_

#include <stdint.h>
#include <QVector>
#include <QThread>
#include "processListA.h"
#include "IComm.h"
#include "ITestBoard.h"
#include "testBoardSerial.h"
#include "ISaveFile.h"

namespace NMotorMonitor {
enum EJudgeMode {
    eEXTREME_MODE               = 0,
    eCONFIDENT_INTERVAL,
    eSTANDARD_DEVIATION,
};

typedef struct{
    QString                     cur_port_name; //实时显示端口
    QString                     port_name; //使用端口

    uint8_t                     mode; //
    uint16_t                    limit_time; //监测配置时间
    uint16_t                    limit_cycle_num; //
    uint8_t                     pwm_radio;
    uint                        id;
    EJudgeMode                  judge_mode;
    uint16_t                    result_stand;
}StUiConfig; //

}

class CMotorMonitorOperation:public QObject
{
    Q_OBJECT
public:
    CMotorMonitorOperation(const NMotorMonitor::StUiConfig &config);
    ~CMotorMonitorOperation();

    enum EProcessStep{
        eREADY_STEP,
        eSTART_STEP,
        eDATA_STEP,
        eCOMPLETE_STEP,
    };

private:
    //* 启动段配置
    typedef struct {
        uint16_t                up_filter;
        uint16_t                down_filter;
        uint16_t                cycle_fg_num; //一圈fg个数
    } StMotorConfig;

    //* 配置输入
    typedef struct{
        uint16_t                max;
        uint16_t                min;
        uint16_t                mean;
        uint                    sum;
        QVector<uint16_t>       data; //周期 转速
        QVector<uint16_t>       distribute;
    } StMotorData;

    typedef struct{
        int                     id;
        uint16_t                max_bias;
        uint16_t                min_bias;
        float                   sigma;
        uint16_t                monitor_run_time; //监测运行时间
        bool                    result;
    } StResult;

    //* 子线程
    CTestBoardSerial *m_serial_thread_ = nullptr;
    QThread *m_sub_thread_ = nullptr;

    //* 运行/状态
    int m_timerId;
    uint16_t m_port_update_cnt;

    typedef CProcessListA<CMotorMonitorOperation, EExecStatus, EProcessStep> TClen_process;
    TClen_process* mc_processList_ = nullptr;
    TClen_process::StStepRecord *mst_task_status_ = nullptr;

public:
    QVector<TClen_process::StTask>  mv_task_list;

private:
    //* 端口接口
    IComm *mc_icomm_ = nullptr;

    //* devices
    ITestBoard* mc_itest_board_ = nullptr;
    uint  m_uart_baud;

    //* 输入配置
    const NMotorMonitor::StUiConfig *mst_config_ = nullptr;

    //* communication status
    StCommunicateStatus* mst_comm_status_ = nullptr;

    //* 数据
    StMotorConfig mst_motor_config;
    bool m_first_data;
    QByteArray m_origin_data;

public:
    StMotorData *mst_motor_fg_data_ = nullptr;
    StMotorData *mst_cycle_data_ = nullptr;
    StResult *mst_result_ = nullptr;

private:
    //* 数据存储
    ISaveFile* mi_save_file_ = nullptr;
    QMap<QString, QVariant> mm_result_data; // = nullptr;

    //* debug
    QWidget *m_message_box_ = nullptr;

    void varibleInit(void);

    bool portlistUpdate();

    void fgToCycle(const uint8_t &fg_num, StMotorData* const motor_data_, StMotorData* const cycle_data_);
    void fgToCycle(const StMotorConfig &motor_config, StMotorData* const motor_data_, StMotorData* const cycle_data_);

    void distribute(const uint16_t &start_cycle_num, StMotorData* const cycle_data_);

    void extremeValue(StMotorData *motor_data_);
    void standardDeviation(StMotorData *motor_data_);
    void confidentInterval(StMotorData *motor_data_);

    void dataShow(void);
    bool getResult(StResult *st_result_, const uint16_t &standar);

    //*********************************** 循环任务集 **********************
    EExecStatus readyStep(void);
    EExecStatus readyAck(void);

    EExecStatus startStep(void);
    EExecStatus startAck(void);

    EExecStatus dataStep(void);
    EExecStatus dataAck(void);

    EExecStatus compStep(void);
    EExecStatus compAck(void);

    void errorHandle(); //异常情况处理

    virtual void timerEvent(QTimerEvent *event) override;
signals:
    void subThreadSignal(const bool &is_exc);

    void portUpdateSignals(QStringList *port_list_, bool port_flag);

    void readySignal(bool is_open);

    //* status update signal
    void startAckSignal(EExecStatus status);

    void dataAckSignal(EExecStatus status);

    void compAckSignal(EExecStatus status);

    //* result signals
    void resultSignal(uint16_t mean);

private slots:
    void dataRecive(ITestBoard::ECommStep step, ECommStatus status, QByteArray bytes);
};

#endif
