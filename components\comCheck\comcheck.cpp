#include "comcheck.h"
#include "QFileDialog"
#include "ui_comcheck.h"
#include <QMenu>
#include <QMetaEnum>
#include <QThread>


#include "loadTxt.h"
#include "typeConvert.h"


#define CMD_TABLE_LINE_NUM 20

CComCheck::CComCheck(QWidget *parent)
    : Q<PERSON>ain<PERSON><PERSON>ow(parent),
      ui(new Ui::CComCheck),
      mst_config_(new NComCheck::StUiConfig),
      mc_operation_(new CComCheckOpt(*mst_config_))
      //  , mi_load_(new CLoadTxt)
      ,
      mst_ack_parse_(new QMap<QString, QString>) {
    //-------------------------------------- ui init -------------------------------
    ui->setupUi(this);

    //******************************* ui init ******************************
    //  QMenu *fileMenu = new QMenu(tr("&File"), this);
    //  QAction *newAction = fileMenu->addAction(tr("&New..."));
    //  newAction->setShortcuts(QKeySequence::New);

    const QString cmd_line_sheetStyle = "QLineEdit{"
                                        "min-width: 500px;"
                                        "min-height: 25px;"
                                        "border-radius: 6px;"
                                        "border:2px solid black;"
                                        "background: rgb(255,255,255)"
                                        "color: rgb(25, 25, 25);}"; /*浅黑*/
    //                                        "text-align: right;}";

    const QString cmd_pushbnt_sheetStyle = "QPushButton{"
                                           "min-width: 40px;"
                                           "min-height: 25px;"
                                           "max-width: 40px; "
                                           //                                           "max-height: 16px;"
                                           "background-color: rgb(255,255,255);"
                                           "color: rgb(25, 25, 25);"
                                           "border: 2px solid black;"
                                           "border-radius: 6px;}"

                                           "QPushButton:hover{"
                                           "background-color: rgb(25, 25, 25);"
                                           "color: rgb(255,255,255);"
                                           "border: 2px solid white;}";

    const QString priority_line_sheetStyle = "QLineEdit{"
                                             "min-width: 30px;"
                                             "min-height: 50px;"
                                             "max-width: 30px;"
                                             "border-radius: 6px;"
                                             "border: 2px solid black;"
                                             "background: rgb(25,25,25)"
                                             "color: rgb(25, 25, 25);}"; /*浅黑*/
    //                                             "text-align: right;}";

    for (uint8_t for_i = 0; for_i <= CComCheckOpt::EProject::eNOVA_A1B; for_i++) {
        QMetaEnum stepEnum = QMetaEnum::fromType<CComCheckOpt::EProject>();
        QString   step_str = stepEnum.valueToKey(for_i);

        ui->device_select->addItem(step_str);
    }
    mst_config_->project_index = (int)CComCheckOpt::EProject::eNOVA_A1;
    ui->device_select->setCurrentIndex(mst_config_->project_index);

    //*********************************** cmd view table init ***************
    //* get project cmdList
    mst_cmd_list_ = mc_operation_->getCmdList(mst_config_->project_index);

    if (mst_cmd_list_->size() > CMD_TABLE_LINE_NUM) {
        qDebug() << "-e comcheck/ cmd list num too few";
    }

    for (uint8_t for_i = 0; for_i < CMD_TABLE_LINE_NUM; for_i++) {
        mst_cmd_view_ = new StCmdView;

        mst_cmd_view_->is_hex_check_ = new QCheckBox;

        mst_cmd_view_->line_cmd_ = new QLineEdit;
        //        st_cmd_view_->line_cmd_->setGeometry(50, 100*for_i, 100, 25);
        if (for_i < mst_cmd_list_->size()) {
            //            QString cmd_name_tmp = (mst_cmd_list_->begin() + for_i).key();

            mst_cmd_view_->line_cmd_->setObjectName(QString("cmd:" + (mst_cmd_list_->begin() + for_i).key()));
            mst_cmd_view_->line_cmd_->setText(NsTypeConvert::byteArrayToString((mst_cmd_list_->begin() + for_i).value()));
        } else {
            mst_cmd_view_->line_cmd_->setObjectName("cmd" + QString::number(for_i));
            mst_cmd_view_->line_cmd_->setText("input cmd" + QString::number(for_i));
        }
        mst_cmd_view_->line_cmd_->setStyleSheet(cmd_line_sheetStyle);
        mst_cmd_view_->line_cmd_->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);

        mst_cmd_view_->send_btn_ = new QPushButton;
        if (for_i < mst_cmd_list_->size()) {
            //            QString cmd_name_tmp = (mst_cmd_list_->begin() + for_i).key();

            mst_cmd_view_->send_btn_->setObjectName(QString("send_btn:" + (mst_cmd_list_->begin() + for_i).key()));
            mst_cmd_view_->send_btn_->setText((mst_cmd_list_->begin() + for_i).key());
        } else {
            mst_cmd_view_->send_btn_->setObjectName("send_btn" + QString::number(for_i));
            mst_cmd_view_->send_btn_->setText("send" + QString::number(for_i));
        }
        mst_cmd_view_->send_btn_->setStyleSheet(cmd_pushbnt_sheetStyle);


        mst_cmd_view_->priority_ = new QLineEdit;
        mst_cmd_view_->priority_->setObjectName("priority" + QString::number(for_i));
        mst_cmd_view_->priority_->setText("0");
        mst_cmd_view_->priority_->setStyleSheet(priority_line_sheetStyle);

        mst_cmd_view_->interval_time_ = new QLineEdit;
        mst_cmd_view_->interval_time_->setObjectName("interval_time" + QString::number(for_i));
        mst_cmd_view_->interval_time_->setText("0");
        mst_cmd_view_->interval_time_->setStyleSheet(priority_line_sheetStyle);

        mv_cmd_view.append(mst_cmd_view_);

        ui->gridLayout->addWidget(mv_cmd_view[for_i]->is_hex_check_);
        ui->gridLayout->addWidget(mv_cmd_view[for_i]->line_cmd_);
        ui->gridLayout->addWidget(mv_cmd_view[for_i]->send_btn_);
        ui->gridLayout->addWidget(mv_cmd_view[for_i]->priority_);
        ui->gridLayout->addWidget(mv_cmd_view[for_i]->interval_time_);
    };
    ui->cmdScrollArea->widget()->setLayout(ui->gridLayout);

    //********************************** connect
    //* connect pushbutton-> cmd send

    //* cmd'modify update
    connect(this, &CComCheck::cmdModifySignal, mc_operation_, &CComCheckOpt::cmdModifyUpdate_slot);

    //* tasks
    connect(mc_operation_, &CComCheckOpt::portUpdateSignal, this, &CComCheck::portListShow_slot);  //列表更新

    connect(mc_operation_, &CComCheckOpt::stepStatusSignal, this, &CComCheck::processStatusShow_slot);

    //    connect(mc_operation_, &CComCheckOpt::chipIdAckSignal,
    //            this, &CComCheck::chipIdShow);


    //* cmd show
    connect(mc_operation_, &CComCheckOpt::cmdListShowSignal, this, &CComCheck::cmdListShow_slot);  // origin cmd

    connect(mc_operation_, &CComCheckOpt::sendedCmdShowSignal, this, &CComCheck::sendedCmdShow_slot);  // send cmd show
    connect(mc_operation_, &CComCheckOpt::ackedCmdShowSignal, this, &CComCheck::ackedCmdShow_slot);    //
    connect(mc_operation_, &CComCheckOpt::cmdPrasedSignal, this, &CComCheck::cmdPrasedShow_slow);      // cmd prased show

    connect(mc_operation_, &CComCheckOpt::resultShowSignal, this, &CComCheck::resultShow_slot);  // results show
}

CComCheck::~CComCheck() {
    delete ui;
    delete mst_config_;
    delete mc_operation_;
    //    delete mi_load_;
    delete mst_ack_parse_;

    for (uint8_t i = 0; i < mv_cmd_view.size(); ++i) {
        delete mv_cmd_view[i]->is_hex_check_;
        delete mv_cmd_view[i]->line_cmd_;
        delete mv_cmd_view[i]->send_btn_;
        delete mv_cmd_view[i]->priority_;
        delete mv_cmd_view[i]->interval_time_;
        delete mv_cmd_view[i];
    }
    mv_cmd_view.clear();
    mv_cmd_view.shrink_to_fit();
}

void CComCheck::closeEvent(QCloseEvent *event) {
    Q_UNUSED(event);

    this->setAttribute(Qt::WA_DeleteOnClose);  //释放窗口资源
    QThread::msleep(50);
    emit comCheckCloseSiganl(true);
}

/**
 * @brief 更新端口列表
 */
void CComCheck::portListShow_slot(QStringList *port_list_, bool port_flag) {
    ui->port_select->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->port_select->clear();                //会触发currentIndexChanged回调函数
    ui->port_select->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->port_select->setCurrentIndex(0);
    else
        ui->port_select->setCurrentText(mst_config_->cur_port_name);

    ui->port_select->blockSignals(false);
}

/**
 * @brief 配置更新
 */
void CComCheck::updateConfig(NComCheck::StUiConfig *config_) {
    config_->port_name     = ui->port_select->currentText();
    config_->cur_port_name = ui->port_select->currentText();
    config_->baud          = ui->baud_select->currentText().toUInt();

    config_->project_index = ui->device_select->currentIndex();
    config_->project_name  = ui->device_select->currentText();
    config_->isWhileSend   = ui->whileCheckBox;
}

void CComCheck::cmdListShowUpdate() {
    for (uint8_t for_i = 0; for_i < CMD_TABLE_LINE_NUM; for_i++) {
        if (for_i < mst_cmd_list_->size()) {
            mst_cmd_view_->line_cmd_->setObjectName("cmd:" + (mst_cmd_list_->begin() + for_i).key());
            mst_cmd_view_->line_cmd_->setText((mst_cmd_list_->begin() + for_i).value());
        } else {
            mst_cmd_view_->line_cmd_->setObjectName("cmd" + QString::number(for_i));
            mst_cmd_view_->line_cmd_->setText("input cmd" + QString::number(for_i));
        }
    }
}

void CComCheck::processStatusShow_slot(const int16_t &step, const int16_t &status) {
}

/**
 * @brief add changed/new cmd
 */
void CComCheck::addCmdView_slot() {
}

/**
 * @brief show cmd list
 */
void CComCheck::cmdListShow_slot() {
    QPushButton *btn = qobject_cast<QPushButton *>(sender());
    //* 获取按钮显示文本
    QString text = btn->text();
    //* 获取按钮对象名称
    QString name = sender()->objectName();

    //* cmd send signals
}

/**
 * @brief CComCheck::sendedCmdShow_slot
 * @param cmd
 */
void CComCheck::sendedCmdShow_slot(QByteArray cmd) {
}

void CComCheck::ackedCmdShow_slot(QByteArray cmd) {
}

void CComCheck::resultShow_slot() {
}

void CComCheck::cmdPrasedShow_slow() {
}

void CComCheck::dataParsingShow_slot() {
}


void CComCheck::on_device_select_currentIndexChanged(int index) {
    mst_config_->project_index = index;
    //    mst_config_->project_name = ui->device_select->currentText();

    mst_cmd_list_ = mc_operation_->getCmdList(mst_config_->project_index);
}

void CComCheck::on_port_select_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_port_name = ui->port_select->currentText();
}

void CComCheck::on_baud_select_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_baud = ui->baud_select->currentText().toUInt();
}

void CComCheck::on_start_btn_clicked() {
    if (ui->start_btn->text() == QString("start")) {
        updateConfig(mst_config_);                                           //配置更新
        mc_operation_->mv_task_list[CComCheckOpt::eSTART].flag.exec = true;  //开始任务
    } else {
        mc_operation_->mv_task_list[CComCheckOpt::eCOMPLETE].flag.exec = true;  //结束任务
    }
}

void CComCheck::on_actionImport_triggered() {
    QString filename = QFileDialog::getOpenFileName(this, tr("Open txt"), ".", tr("txt files (*.txt)"));
    //    mi_load_->loadFile(filename);

    mst_ack_parse_->clear();
    //    mi_load_->readParam(filename, mst_ack_parse_);
    //    QByteArray test_hex;
    //    uint8_t test_buff[16] = {0xA5, 0x0F, 0xB2, 0x86, 0x00, 0x90, 0x36, 0x05, 0x12, 0x50, 0x4E, 0x56, 0x38, 0x35, 0x34, 0x18};
    //    test_hex.append((char*)test_buff, 16);

    //    QString test_str = NsTypeConvert::byteArrayToString(test_hex);
}
