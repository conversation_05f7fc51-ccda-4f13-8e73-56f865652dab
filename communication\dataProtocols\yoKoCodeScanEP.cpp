#include "yoKoCodeScanEP.h"
#include <qdebug.h>
#include "qLog.h"

CYoKoCodeScannerEP::CYoKoCodeScannerEP():
    mst_interaction_frame_(new StInteractionFrame)
  , mst_ack_frame_(new StAckFrame)
{
    mst_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    mst_interaction_frame_->tail = (uint16_t)EInteractionFrame::eTAIL;

    mst_ack_frame_->header = (uint16_t)EInteractionAckFrame::eHEADER;
}

CYoKoCodeScannerEP::~CYoKoCodeScannerEP(){
    delete mst_interaction_frame_;
    delete mst_ack_frame_;
}

QByteArray CYoKoCodeScannerEP::getControlCmd(const char &id)
{
    QByteArray cmd;
    StInteractionFrame* st_interaction_frame_ = nullptr;

//    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
//    st_interaction_frame_->cmd = (uint8_t)ECmd::eW | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
//    st_interaction_frame_->id = id;
//    st_interaction_frame_->num = 0;

//    st_interaction_frame_->data.resize(0);

//    st_interaction_frame_->check_sum = checkSum((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN - 1);

//    cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CYoKoCodeScannerEP::getWriteCmd(const char &id, const QByteArray &w_data)
{
    QByteArray w_cmd;

    mst_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    mst_interaction_frame_->id = id;
    mst_interaction_frame_->num = w_data.length();

    //mst_interaction_frame_->data.resize(mst_interaction_frame_->num);
    mst_interaction_frame_->data = w_data.at(0);
    //  w_cmd.append((char*)mst_receive_frame_, (EFrame::eHEADER_LEN )); //+ w_data.length())); //直接拷贝整个 qbytearray数据错乱
    mst_interaction_frame_->crc16 = 0; //calXOR((uint8_t*)mst_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN - 1);

    w_cmd.append((char*)mst_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CYoKoCodeScannerEP::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{
    QByteArray r_cmd;
    StInteractionFrame* st_interaction_frame_ = nullptr;

//    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
//    st_interaction_frame_->cmd = (uint8_t)ECmd::eR | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
//    st_interaction_frame_->id = id;
//    st_interaction_frame_->num = 0;
//    st_interaction_frame_->data.resize(0);

//    st_interaction_frame_->check_sum = checkSum((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN - 1);

//    r_cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return r_cmd;
}


