#ifndef IMAGEPROCESSING_KALMANFILTER_H
#define IMAGEPROCESSING_KALMANFILTER_H

#include "../interfaces/IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QVector>
#include <QDebug>

namespace ImageProcessing {

/**
 * @brief 卡尔曼滤波器实现
 * 
 * 实现简化的卡尔曼滤波算法，用于图像噪声抑制
 * 基于原有my_interPolation::kalman_filter的逻辑重构
 */
class KalmanFilter : public IImageFilter {
public:
    /**
     * @brief 构造函数
     */
    KalmanFilter();

    /**
     * @brief 析构函数
     */
    ~KalmanFilter() override = default;

    // IImageFilter接口实现
    bool apply(ImageDataU32& data) override;
    bool apply(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const FilterParams& params) override;
    std::unique_ptr<FilterParams> getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t width, uint32_t height) const override;
    uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;
    bool supportsInPlace() const override;

private:
    KalmanParams params_;                    ///< 卡尔曼滤波参数
    QVector<QVector<float>> estimates_;      ///< 状态估计值
    QVector<QVector<float>> errorCovariance_; ///< 误差协方差
    bool initialized_;                       ///< 是否已初始化

    /**
     * @brief 初始化卡尔曼滤波器状态
     * @param width 图像宽度
     * @param height 图像高度
     */
    void initializeFilter(uint32_t width, uint32_t height);

    /**
     * @brief 卡尔曼滤波预测步骤
     * @param x 列索引
     * @param y 行索引
     * @param measurement 测量值
     * @return 滤波后的值
     */
    float kalmanPredict(uint32_t x, uint32_t y, float measurement);

    /**
     * @brief 卡尔曼滤波更新步骤
     * @param x 列索引
     * @param y 行索引
     * @param measurement 测量值
     * @param prediction 预测值
     * @return 更新后的估计值
     */
    float kalmanUpdate(uint32_t x, uint32_t y, float measurement, float prediction);

    /**
     * @brief 验证滤波参数
     * @param params 参数对象
     * @throws InvalidParameterException 如果参数无效
     */
    void validateKalmanParams(const KalmanParams& params) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_KALMANFILTER_H
