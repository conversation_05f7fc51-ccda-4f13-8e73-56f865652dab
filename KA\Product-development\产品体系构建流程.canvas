{"nodes": [{"id": "da9c99118856a5e0", "type": "text", "text": "\t1. 项目文件夹初始化", "x": -384, "y": -245, "width": 250, "height": 60}, {"id": "dfca221c5cbbff94", "type": "file", "file": "KA/Product development/单层级产品目录框架.md", "x": -2177, "y": -319, "width": 400, "height": 400}, {"id": "ed5b5ff533d0f530", "type": "file", "file": "KA/Product development/多层级产品目录框架.md", "x": -1680, "y": -319, "width": 400, "height": 400}, {"id": "64f468d2597e6997", "type": "text", "text": "框架", "x": -2027, "y": -700, "width": 250, "height": 60, "color": "4"}, {"id": "58f134250e2c4c39", "type": "text", "text": "项目相关文件，但项目文件可能多位置多平台多位置存放，需要进行链接", "x": -720, "y": -315, "width": 220, "height": 130}, {"id": "0808aee989ac5673", "type": "text", "text": "构建流程", "x": -180, "y": -740, "width": 240, "height": 80, "color": "4"}, {"id": "a922532273926539", "type": "file", "file": "KA/Product development/scripts/project_initialization/init_project_structure.py", "x": 220, "y": -400, "width": 190, "height": 85, "color": "5"}, {"id": "f883aa72ec110bcb", "type": "file", "file": "KA/Product development/scripts/project_initialization/init_multilevel_structure.py", "x": 220, "y": -215, "width": 190, "height": 75, "color": "5"}, {"id": "d14181c5ec6a90d2", "type": "file", "file": "KA/Product development/scripts/workflow/init_workflow_config.py", "x": 220, "y": 59, "width": 190, "height": 71, "color": "5"}, {"id": "6df12fd61e91f2dc", "type": "file", "file": "KA/Product development/config/workflow_config.json", "x": 220, "y": -40, "width": 190, "height": 60, "color": "3"}, {"id": "a59adb9cf8c2dece", "type": "text", "text": "脚本", "x": 660, "y": -740, "width": 160, "height": 50, "color": "5"}, {"id": "a9845df49462636b", "type": "text", "text": "mcp server", "x": 660, "y": -660, "width": 160, "height": 50, "color": "6"}, {"id": "5aff1b0d5980ccb0", "type": "text", "text": "配置", "x": 660, "y": -580, "width": 160, "height": 50, "color": "3"}, {"id": "13a312360fad35c2", "type": "file", "file": "KA/Product development/scripts/workflow/workflow_manager.py", "x": 220, "y": 172, "width": 190, "height": 68, "color": "5"}, {"id": "7e4d256b8ee28906", "type": "file", "file": "KA/Product development/scripts/workflow/generate_workflow_diagram.py", "x": 220, "y": 280, "width": 190, "height": 60, "color": "5"}, {"id": "4b515acf9715e77e", "type": "text", "text": "1. 硬件开发", "x": -60, "y": 1180, "width": 250, "height": 60}, {"id": "85e3c2b96ac1f20a", "type": "text", "text": "1. 软件开发细项", "x": 270, "y": 1240, "width": 250, "height": 60}, {"id": "a4e22efeed57c1fe", "type": "text", "text": "2. 项目软件开发", "x": -60, "y": 1300, "width": 250, "height": 60}, {"id": "4c068889771f0fc6", "type": "text", "text": "2. 测试数据", "x": 270, "y": 1340, "width": 250, "height": 60}, {"id": "071cc4ce4ebfef38", "type": "text", "text": "3. 生产工具开发", "x": -60, "y": 1420, "width": 250, "height": 60}, {"id": "915f6f12fc66bb60", "type": "text", "text": "4. 辅助工具开发", "x": -60, "y": 1540, "width": 250, "height": 60}, {"id": "12472a61efc426b0", "type": "text", "text": "5. 开发", "x": -384, "y": 1390, "width": 250, "height": 60}, {"id": "024687308b931917", "type": "text", "text": "6. 项目输出", "x": -384, "y": 1800, "width": 250, "height": 60}, {"id": "32bf10acc18c69f8", "x": 220, "y": 390, "width": 190, "height": 60, "type": "text", "text": "追踪信息表格初始化"}, {"id": "7b04c4523e1b8ad8", "type": "text", "text": "信息追踪初始化", "x": -384, "y": 480, "width": 250, "height": 60}, {"id": "23a2e48b88484336", "type": "text", "text": "id关联", "x": 220, "y": 480, "width": 190, "height": 56}, {"id": "2684af38d2522835", "type": "text", "text": "id更新", "x": 220, "y": 580, "width": 190, "height": 56}, {"id": "c4375ce533c52b09", "type": "file", "file": "产品体系一体化流程框架.md", "x": -384, "y": 142, "width": 250, "height": 60}, {"id": "560a246b0b05a967", "type": "text", "text": "网络技术方案", "x": 220, "y": 940, "width": 190, "height": 60, "color": "6"}, {"id": "b2435abbab69afc5", "type": "text", "text": "3. 需求分析与方案(design)", "x": -384, "y": 940, "width": 250, "height": 60}, {"id": "56375a121431fc0e", "type": "text", "text": "4. . 开发细项与管理", "x": -384, "y": 1060, "width": 250, "height": 60}, {"id": "a3935789b94c5f7f", "type": "text", "text": "网络市场信息", "x": 220, "y": 700, "width": 190, "height": 50, "color": "6"}, {"id": "7439df4e91b3dfc9", "type": "text", "text": "技术指标", "x": 220, "y": 790, "width": 190, "height": 50, "color": "6"}, {"id": "b604fe05bbb3549a", "type": "text", "text": "2. 需求导入(requirements)", "x": -384, "y": 730, "width": 250, "height": 60}], "edges": [{"id": "edcb08f1cade5fed", "fromNode": "da9c99118856a5e0", "fromSide": "left", "toNode": "58f134250e2c4c39", "toSide": "right", "fromEnd": "arrow"}, {"id": "af3b675f0e802a45", "fromNode": "b604fe05bbb3549a", "fromSide": "right", "toNode": "a3935789b94c5f7f", "toSide": "left", "fromEnd": "arrow", "label": "外部导入"}, {"id": "2cd63c9447691d2f", "fromNode": "b2435abbab69afc5", "fromSide": "right", "toNode": "560a246b0b05a967", "toSide": "left", "fromEnd": "arrow"}, {"id": "1d5bd754241fe6d0", "fromNode": "a4e22efeed57c1fe", "fromSide": "right", "toNode": "4c068889771f0fc6", "toSide": "left"}, {"id": "b3629d333bd8e6c2", "fromNode": "a4e22efeed57c1fe", "fromSide": "right", "toNode": "85e3c2b96ac1f20a", "toSide": "left"}, {"id": "2c1a610cc5eb3c41", "fromNode": "56375a121431fc0e", "fromSide": "bottom", "toNode": "12472a61efc426b0", "toSide": "top"}, {"id": "97edbe1982411af6", "fromNode": "56375a121431fc0e", "fromSide": "bottom", "toNode": "85e3c2b96ac1f20a", "toSide": "top"}, {"id": "95113ce97358aba8", "fromNode": "12472a61efc426b0", "fromSide": "bottom", "toNode": "024687308b931917", "toSide": "top"}, {"id": "694448101e4e22d2", "fromNode": "da9c99118856a5e0", "fromSide": "right", "toNode": "a922532273926539", "toSide": "left", "label": "单层初始化"}, {"id": "c861d06e9f04ec21", "fromNode": "da9c99118856a5e0", "fromSide": "right", "toNode": "f883aa72ec110bcb", "toSide": "left", "label": "多层初始化"}, {"id": "81ebd2df00b3de1b", "fromNode": "c4375ce533c52b09", "fromSide": "right", "toNode": "d14181c5ec6a90d2", "toSide": "left", "label": "初始化"}, {"id": "941e11ed376c7962", "fromNode": "c4375ce533c52b09", "fromSide": "right", "toNode": "6df12fd61e91f2dc", "toSide": "left", "label": "手动配置"}, {"id": "d3490f381af8f82b", "fromNode": "c4375ce533c52b09", "fromSide": "right", "toNode": "13a312360fad35c2", "toSide": "left", "label": "调整"}, {"id": "aafd3bf85b83efb2", "fromNode": "c4375ce533c52b09", "fromSide": "right", "toNode": "7e4d256b8ee28906", "toSide": "left", "label": "可视化"}, {"id": "8029e3b1dd13e2f3", "fromNode": "b604fe05bbb3549a", "fromSide": "right", "toNode": "7439df4e91b3dfc9", "toSide": "left", "label": "手动导入"}, {"id": "efc1ac74cbb9a9ed", "fromNode": "7b04c4523e1b8ad8", "fromSide": "right", "toNode": "32bf10acc18c69f8", "toSide": "left"}, {"id": "054cec282fbf46ed", "fromNode": "7b04c4523e1b8ad8", "fromSide": "right", "toNode": "23a2e48b88484336", "toSide": "left"}, {"id": "4c4d5bfb21d36bd5", "fromNode": "7b04c4523e1b8ad8", "fromSide": "right", "toNode": "2684af38d2522835", "toSide": "left"}]}