project_template/
├── docs/
│   ├── tasks_list/         # 开发细项列表
│   ├── design/             # 设计文档(软件相关，如 软件架构...)
│   ├── support/            # 参考文件
│   ├── issues/             # 问题跟踪
│   ├── tests/              # 测试文档
│   └── output/             # 输出文件
├── scripts/
│   ├── project_init.py     # 工程初始化脚本
│   ├── issue_tracker.py    # 问题跟踪脚本
│   └── build_tools/        # 构建工具脚本
├── firmware/
│   ├── CMSIS/              #ww
│   └── xxx_driver/         #
├── MDK-ARM/
│   ├── /                   # mdk项目
│   └── /                   #
├── sys/
│   ├── inc                 # 系统代码头文件，例如rtos
│   └── src                 # 源文件
├── user/
├── app/
│   ├── inc/                # 应用头文件
│   └── src/                # 应用源码
├── API/
│   ├── inc/                # 外部代码
│   └── src/                #
├── algorithm/
│   ├── inc/                # 算法文件
│   └── src/                #
├── libs/                   # 外部库
└── bsp/
    ├── inc/                #
    └── src/                #

1. 开发细项同步(单独一个子项目，但管理方式与上级“开发规划与管理”的管理方式相同，并且)
2.
