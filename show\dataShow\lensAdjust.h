#ifndef _LENSADJUST_H_
#define _LENSADJUST_H_

#include <QWidget>
#include <QMessageBox>
#include <QDebug>
#include <qcustomplot.h>

class lensAdjust: public QWidget
{
    Q_OBJECT
public:
    lensAdjust(QCustomPlot *customPlot);
    ~lensAdjust();
    float StandardDeviation(QVector<double> array, uint8_t n);
    float StandardDeviation(QVector<uint16_t> array, uint8_t n);
    QSharedPointer<QCPBarsDataContainer> dataHandle(const QVector<uint16_t> &data);
    void customPlotShow(QVector<uint32_t> greymap);
private:
    QCustomPlot *m_customPlots = nullptr;
    QCPAxisRect *m_mainAxisRect = nullptr;
    QCPColorMap *mainMap = nullptr;
    QCPAxis *main_key_Axis2 = nullptr;
//    QCPAxisRect *subRectLeft = nullptr;

signals:

private slots:
    void mouseDouClick(QMouseEvent* event);
};
#endif
