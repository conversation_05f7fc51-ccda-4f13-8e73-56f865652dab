#ifndef  _COIN_SENSOR_P_H_
#define  _COIN_SENSOR_P_H_

#include <QByteArray>
#include "IProtocol.h"

class CCoinSensorP:public IProtocol
{
public:
    CCoinSensorP();
    ~CCoinSensorP();

    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

    /*协议cmd枚举*/
    enum EFrame{
        eHEADER     = 0xA5,
        eHEADER_LEN = 0x06,
        eXOR_INDEX  = 0x03,
        eNUM_INDEX  = 0x04,
    };

    /*协议cmd枚举*/
    enum {
        kH2D = 0x01,	/*host->device*/
        kD2H = 0X02,	/*device->host*/
        kHWS = 0x04,	/*host write success*/
        kHRS = 0x08,	/*host read success*/
        kHSS = 0x10,	/*host send ok   check err is err*/
        kDFF = 0x20,      /*device fixed frequency send*/
        kDNP = 0x40,      /*device not parse logic*/
    };

#define     DATA_CACHE      100
    typedef struct {
        uint8_t     header; //帧头
        uint8_t     cmd; //
        uint8_t     id;
        uint8_t     check_xor;
        uint16_t    num; //LSB
        uint8_t     data[DATA_CACHE]; //
    } StFrame;

private:
    StFrame *mst_frame_ = nullptr;
};


#endif
