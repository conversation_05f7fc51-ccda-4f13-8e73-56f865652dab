#ifndef _ILOAD_H_
#define _ILOAD_H_

#include <QString>
#include <QMap>
#include <QByteArray>
#include <QVariant>

class ILoad
{
public:
    ILoad(){};
    virtual ~ILoad(){};

    virtual bool loadFile(const QString &filename) = 0;
    virtual void readParam(const QString &filename, QMap<QString, int> *xml_read) = 0;
    virtual void readParam(const QString &filename, QMap<QString, QVariant> *xml_read) = 0;
    virtual void readParam(const QString &filename, QMap<QString, QByteArray> *xml_read) = 0;
    virtual void readParam(const QString &filename, QMap<QString, QString> *xml_read) = 0;
    virtual bool readParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_read) = 0;
    virtual bool writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QByteArray> *xml_write) = 0;
    virtual bool writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_write) = 0;
};

#endif // PARAMLOAD_H
