#!/usr/bin/env python3
"""
产品开发MCP服务器

提供产品体系构建框架的核心功能接口，遵循标准MCP协议。
支持Cursor自动启动和集成。

- 项目初始化
- 配置生成  
- 文档关联
- Canvas同步
- 信息追溯
- 可视化管理
"""

import asyncio
import json
import sys
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional
from dataclasses import dataclass


@dataclass
class ScriptInfo:
    """脚本信息"""
    name: str
    path: str
    description: str
    parameters: List[Dict[str, Any]]


class ProductDevelopmentMCPServer:
    """产品开发MCP服务器 - 标准MCP协议实现"""
    
    def __init__(self):
        self.scripts_base = Path(__file__).parent.parent
        self.scripts = self._register_scripts()
    
    def _register_scripts(self) -> Dict[str, ScriptInfo]:
        """注册可用的脚本"""
        return {
            "init_project": ScriptInfo(
                name="init_project",
                path=str(self.scripts_base / "init_product_project.py"),
                description="初始化新的产品项目",
                parameters=[
                    {"name": "project_name", "type": "string", "description": "项目名称", "required": True},
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": False},
                    {"name": "structure_type", "type": "string", "description": "项目结构类型(single_layer/multi_level)", "required": False}
                ]
            ),
            "generate_all_configs": ScriptInfo(
                name="generate_all_configs",
                path=str(self.scripts_base / "config" / "generate_all_configs.py"),
                description="生成所有项目配置文件",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "project_type", "type": "string", "description": "项目类型(single_layer/multi_level)", "required": False},
                    {"name": "config_types", "type": "array", "description": "要生成的配置类型列表", "required": False},
                    {"name": "scripts_base", "type": "string", "description": "脚本基础路径", "required": False}
                ]
            ),
            "generate_workflow_config": ScriptInfo(
                name="generate_workflow_config",
                path=str(self.scripts_base / "config" / "generate_workflow_config.py"),
                description="生成工作流配置文件",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "project_type", "type": "string", "description": "项目类型", "required": False},
                    {"name": "scripts_base", "type": "string", "description": "脚本基础路径", "required": False}
                ]
            ),
            "generate_traceability_config": ScriptInfo(
                name="generate_traceability_config", 
                path=str(self.scripts_base / "config" / "generate_traceability_config.py"),
                description="生成追溯系统配置文件",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "project_type", "type": "string", "description": "项目类型", "required": False}
                ]
            ),
            "generate_document_links_config": ScriptInfo(
                name="generate_document_links_config",
                path=str(self.scripts_base / "config" / "generate_document_links_config.py"),
                description="生成文档关联配置文件",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "project_type", "type": "string", "description": "项目类型", "required": False}
                ]
            ),
            "link_documents": ScriptInfo(
                name="link_documents",
                path=str(self.scripts_base / "links" / "auto_link_documents.py"),
                description="自动进行文档关联和注册",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "register", "type": "boolean", "description": "是否注册文档", "required": False},
                    {"name": "all", "type": "boolean", "description": "是否处理所有文档", "required": False}
                ]
            ),
            "sync_canvas": ScriptInfo(
                name="sync_canvas",
                path=str(self.scripts_base / "canvas" / "auto_link_documents.py"),
                description="同步INDEX文件到Canvas",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "sync_to_canvas", "type": "boolean", "description": "同步到Canvas", "required": False},
                    {"name": "validate_sync", "type": "boolean", "description": "验证同步状态", "required": False}
                ]
            ),
            "manage_trace": ScriptInfo(
                name="manage_trace",
                path=str(self.scripts_base / "infoTrace" / "auto_index_manager.py"),
                description="管理信息追溯和块级INDEX",
                parameters=[
                    {"name": "project_path", "type": "string", "description": "项目路径", "required": True},
                    {"name": "scan", "type": "boolean", "description": "扫描文档内容", "required": False},
                    {"name": "all", "type": "boolean", "description": "处理所有组件", "required": False}
                ]
            )
        }

    async def handle_message(self, message: dict) -> dict:
        """处理MCP消息"""
        method = message.get("method")
        params = message.get("params", {})
        msg_id = message.get("id")
        
        if method == "initialize":
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "result": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "serverInfo": {
                        "name": "product-development",
                        "version": "1.0.0"
                    }
                }
            }
        
        elif method == "tools/list":
            tools = []
            for script_name, script_info in self.scripts.items():
                tools.append({
                    "name": script_name,
                    "description": script_info.description,
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            param["name"]: {
                                "type": param["type"], 
                                "description": param["description"]
                            }
                            for param in script_info.parameters
                        },
                        "required": [
                            param["name"] 
                            for param in script_info.parameters 
                            if param.get("required", False)
                        ]
                    }
                })
            
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "result": {
                    "tools": tools
                }
            }
        
        elif method == "tools/call":
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name not in self.scripts:
                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "error": {
                        "code": -32602,
                        "message": f"Unknown tool: {tool_name}"
                    }
                }
            
            result = await self._execute_script(tool_name, arguments)
            
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "result": {
                    "content": [{
                        "type": "text",
                        "text": self._format_execution_result(result)
                    }]
                }
            }
        
        else:
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }

    async def _execute_script(self, script_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """执行脚本"""
        try:
            script_info = self.scripts[script_name]
            script_path = script_info.path
            
            if not Path(script_path).exists():
                return {
                    "success": False,
                    "error": f"脚本文件不存在: {script_path}",
                    "returncode": -1,
                    "stdout": "",
                    "stderr": ""
                }
            
            # 构建命令
            cmd = [sys.executable, script_path]
            
            # 添加参数
            for param in script_info.parameters:
                param_name = param["name"]
                if param_name in arguments:
                    value = arguments[param_name]
                    
                    # 处理不同类型的参数
                    if param["type"] == "boolean" and value:
                        cmd.append(f"--{param_name.replace('_', '-')}")
                    elif param["type"] == "array" and value:
                        cmd.append(f"--{param_name.replace('_', '-')}")
                        if isinstance(value, list):
                            cmd.extend(value)
                        else:
                            cmd.extend(str(value).split(','))
                    elif param["type"] == "string" and value:
                        cmd.extend([f"--{param_name.replace('_', '-')}", str(value)])
            
            # 执行脚本
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.scripts_base
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": (process.returncode == 0),
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore'),
                "command": ' '.join(cmd)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"执行异常: {str(e)}",
                "returncode": -1,
                "stdout": "",
                "stderr": ""
            }

    def _format_execution_result(self, result: Dict[str, Any]) -> str:
        """格式化执行结果"""
        output = f"=== 脚本执行结果 ===\n"
        output += f"命令: {result.get('command', '未知')}\n"
        output += f"返回码: {result.get('returncode', -1)}\n\n"
        
        if result.get('stdout'):
            output += f"标准输出:\n{result['stdout']}\n"
        
        if result.get('stderr'):
            output += f"错误输出:\n{result['stderr']}\n"
        
        if result.get('error'):
            output += f"错误: {result['error']}\n"
        
        if result.get('success'):
            output += "\n✅ 执行成功"
        else:
            output += f"\n❌ 执行失败 (返回码: {result.get('returncode', -1)})"
        
        return output

    async def run(self):
        """运行MCP服务器（stdio模式）"""
        while True:
            try:
                # 读取一行输入
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )
                
                if not line:
                    break
                
                # 解析JSON-RPC消息
                try:
                    message = json.loads(line.strip())
                    response = await self.handle_message(message)
                    
                    # 发送响应
                    print(json.dumps(response), flush=True)
                    
                except json.JSONDecodeError:
                    # 忽略无效的JSON
                    continue
                    
            except EOFError:
                break
            except KeyboardInterrupt:
                break


async def main():
    """主函数"""
    # 检查是否在stdio模式
    if len(sys.argv) > 1 and sys.argv[1] == "--stdio":
        # 标准MCP stdio模式
        server = ProductDevelopmentMCPServer()
        await server.run()
    else:
        # 后备HTTP模式（用于独立测试）
        print("🚀 产品开发MCP服务器")
        print("使用 --stdio 参数启动标准MCP模式")
        print("或者使用HTTP模式进行测试...")
        
        # 导入并启动HTTP服务器
        from http.server import HTTPServer, BaseHTTPRequestHandler
        from urllib.parse import urlparse, parse_qs
        import threading
        
        class SimpleHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                
                html = """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <title>产品开发MCP服务器</title>
                    <style>body{font-family:Arial,sans-serif;margin:20px;}</style>
                </head>
                <body>
                    <h1>🚀 产品开发MCP服务器</h1>
                    <p>服务器运行正常！</p>
                    <h2>配置信息</h2>
                    <pre>
{
  "servers": {
    "product-development": {
      "command": "python",
      "args": ["scripts/mcp-server_local_integrations/product_development_mcp_server.py", "--stdio"],
      "cwd": "."
    }
  }
}
                    </pre>
                    <p>将以上配置添加到您的 .cursor/mcp.json 文件中。</p>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
            
            def log_message(self, format, *args):
                pass
        
        httpd = HTTPServer(('localhost', 8080), SimpleHandler)
        print(f"HTTP服务器已启动: http://localhost:8080")
        print("按 Ctrl+C 停止服务器")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")


if __name__ == "__main__":
    asyncio.run(main()) 