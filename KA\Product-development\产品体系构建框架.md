# 产品体系构建流程框架

整个产品构建框架遵循以下原则：

1. 一体化
2. 单一来源(使用双链引用其他文档内容)
3. 内容唯一
4. 单一职责
5. 奥卡姆剃刀原则
6. 输入到输出闭环
7. 开闭原则
8. 内容可追溯
9. 工具可拓展，接口隔离原则
10. 流程解耦
11. AI辅助
12. 数据安全
13. 文档即代码
14. 统一可视化（单一可视化平台接口）
15. 合成/聚合复用原则
16. 脚本和mcp server、配置等，放在公共目录，新项目从公共库拷贝或者直接调用公共库文件

**输入**
手动传入必要需求

**输出**
- 描述：构建一个新产品项目需要的资料
- 位置：./deliverables
- 产品体系构建的实施步骤(包含子功能方案)
- 构建的各种配置
    - 配置说明文档
    - 所需各种工具，包含mcp server等等
    - 依赖库
    - 
- 手动指令合集
    - 方式一: vscode tasks
    - 方式二: 本地 mcp server

## 1. VSCode一体化管理平台

[[产品体系一体化平台]]

### 1.1 MCP服务器集成架构

产品体系构建框架采用混合MCP服务器架构，结合自建和市场MCP服务器：

#### 自建MCP服务器
专为产品开发框架定制的MCP服务器，详见：[[产品体系MCP Server.md]]

#### 市场MCP服务器
第三方成熟的MCP服务器，提供通用功能支持，详见：[[mcp-server_market_integrations/README.md]]

**集成策略**：
- **核心业务功能**：使用自建MCP服务器（项目初始化、配置管理、需求管理等）
- **通用工具功能**：使用市场MCP服务器（文件系统、GitHub集成、网络搜索等）
- **混合部署**：在配置文件中灵活组合两种类型的服务器

## 2. 单instance主要组件

### 2.1 需求导入

- **市场需求收集**：市场调研报告、客户反馈汇总、竞品分析
  - 竞品资料收集：竞争产品规格、性能参数、市场定位
  - 竞品技术分析：拆解报告、技术方案推测、优缺点分析
- **技术需求整理**：功能需求列表、性能指标、兼容性要求
- **需求导入工具**：详见 [[build-tools/scripts/README.md]] 和 [[build-tools/mcp-server_local_integrations/core/requirements-management/README.md]]

### 2.2 需求分析与方案设计

#### 2.2.1 需求分解与需求矩阵维护

- **需求分解**：
  - 将高层需求细化为具体功能规格
  - 进行功能分解、用例分析、优先级排序
  - 关注需求的完整性、正确性和可测试性
  - 输出结构化的需求规格说明
- **需求矩阵文档更新**：[[多层架需求矩阵维护系统]]
  - 建立多层级需求追溯关系
  - 将需求按技术平台层、应用领域层、产品层和客户层组织
  - 确保上下层需求的一致性和可追溯性
  - 管理需求状态和变更

#### 2.2.2 方案设计与技术评估

- **可行性评估**：技术可行性、成本评估、风险分析
- **系统架构设计**：整体架构、模块划分、接口定义
- **技术路线选择**：技术栈评估、关键技术验证、组件选型
- **方案文档生成**：详见 [[build-tools/scripts/README.md]] 和 [[build-tools/mcp-server_market_integrations/README.md]]

**可视化方案**：

所有架构图表、流程图等可视化内容的生成和交互功能由[[产品体系可视化与交互系统集成框架]]统一实现。

### 2.3 开发规划与管理

| 系统模块 | 系统设计文档 | 实施方案文档 | 主要功能 |
|---------|------------|------------|---------|
| 开发规划与管理 | [开发规划与管理系统](开发规划与管理系统.md) | [开发规划与管理实施方案](开发规划与管理实施方案.md) | 任务管理、进度跟踪、Linear集成 |

- **项目计划制定**：
  - 里程碑设定、任务分解、资源分配、测试文档、问题追踪闭环
- **任务跟踪系统**：
  - 任务状态管理、自动化进度报告、资源优化调配
- **风险管理**：
  - 风险识别与应对措施
  - AI 辅助风险预警

**项目管理工具**：详见 [[build-tools/scripts/README.md]] 和 [[build-tools/mcp-server_market_integrations/README.md]]

**可视化方案**：

任务状态可视化、进度追踪仪表板等功能由[[产品体系可视化与交互系统集成框架]]统一实现。

### 2.4 开发

#### 2.4.1 硬件开发

- 原理图设计
- PCB设计
- 硬件测试与验证
- 硬件文档生成

```bash
# 初始化硬件项目（示例）
python scripts/hardware/init_hw_project.py --project="development/hardware/hw_project_1"

# 硬件设计检查（示例）
python scripts/quality/hw_design_check.py --design="development/hardware/hw_project_1/schematic.sch"
```

**硬件开发工具**：详见 [[build-tools/scripts/README.md]]

#### 2.4.2 项目软件开发

- **嵌入式软件开发**：引用《嵌入式软件开发流程》标准，驱动与固件开发、功能验证。
- **应用软件开发**：引用《应用软件开发流程》标准，UI/UX 设计、业务逻辑实现与测试。
**软件开发工具**：详见 [[build-tools/scripts/README.md]] 和 [[build-tools/mcp-server_market_integrations/README.md]]

#### 2.4.3 生产工具开发

- 生产测试工具
- 烧录工具
- 校准工具
- 生产数据分析工具

#### 2.4.4 辅助工具开发

- 调试工具
- 数据分析工具
- 性能测试工具
- 用户工具

### 2.5 生产

- **生产准备**：BOM 清单确认，生产工艺流程制定，生产测试方案确认
- **小批量试产**：试产数据分析，工艺优化建议
- **批量生产**：生产过程数据监控、质量控制、生产效率分析

**生产管理工具**：详见 [[build-tools/scripts/README.md]]

### 2.6 项目输出

- **软件版本发布**：
  - 版本控制与标签管理
  - 发布说明自动生成
  - 发布管理工具：详见 [[build-tools/scripts/README.md]] 和 [[build-tools/mcp-server_market_integrations/README.md]]
  - 安装包与升级包构建
- **文档输出**：
  - 用户手册生成
  - 技术文档整合
  - API 文档生成
- **质量验证报告**：
  - 测试报告汇总
  - 性能测试结果
  - 兼容性测试报告
  - 竞品对比测试：与竞争产品的功能、性能对比报告

## 3. 产品项目文档结构

### 3.1 主项目文档目录

有两种目录结构：

单层框架：
[[单层级产品目录框架]]

多层框架：
[[多层级产品目录框架]]

### 3.2 开发工程文档结构

各开发工程内部文档结构请参考：[开发文档管理系统](../开发文档/开发文档管理系统.md)

**注意：** 所有开发工程相关的输出文档（包括版本历史、发布说明、用户手册、API文档等）应按照开发文档管理系统的要求在各自的开发工程中维护，避免在产品级别重复维护这些文档。

### 3.3 脚本共享机制

项目开发使用公共脚本目录中的脚本，而不是在每个项目中重复创建脚本目录。例如：

```
F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/  # 公共脚本目录
```

```bash
# 在开发工程中调用公共脚本示例
python "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/project_initialization/init_product.py" --name="新组件" --type="firmware"
```

在VSCode工作区配置中，可以添加以下设置简化公共脚本的访问：

```json
{
  "settings": {
    "terminal.integrated.env.windows": {
      "SCRIPTS_PATH": "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts"
    }
  }
}
```

然后在项目中可以通过环境变量访问：

```bash
python "%SCRIPTS_PATH%/project_initialization/init_product.py" --name="新组件"
```

## 4. 工作流程搭建

[[产品体系工作流程框架]]

### 4.1 工作流程概述

产品体系一体化流程以组件化设计为基础，将整个产品开发流程拆分为多个独立但相互连接的组件。各组件之间通过事件驱动机制实现松耦合的连接，支持自动化工作流和数据流转。

详细的工作流程配置、事件机制、工作流管理器等内容请参考：[[产品体系工作流程框架]]

### 4.2 主要特性

- **组件化设计**：将产品开发流程拆分为独立组件，便于配置和管理
- **松耦合连接**：组件间通过事件机制松耦合连接，提高系统弹性  
- **双向自动化**：支持工作流自动前进和变更影响分析回溯
- **多层级兼容**：同时支持单层级和多层级目录结构

**可视化支持**：

工作流可视化与配置、流程图可视化和进度追踪仪表板由[[产品体系可视化与交互系统集成框架]]统一实现。

### 4.3 核心组件

- **需求导入**：负责从外部系统导入需求文档，支持多种来源格式
- **需求分析**：处理需求分解、需求矩阵维护和需求标准化
- **方案设计**：基于需求生成系统架构和技术方案
- **开发规划**：根据方案设计生成任务列表和开发计划
- **开发实施**：执行硬件、固件和软件开发
- **测试验证**：执行单元测试、集成测试和系统测试
- **生产准备**：管理生产BOM和生产测试流程
- **项目输出**：处理版本发布和文档生成

## 5. 文档关联与知识管理

[[产品文档关联系统]]

维护文档之间的双链链接。

### 5.1 系统定位与职责

文档关联系统作为**文档注册和基础关联发现**的核心组件：

- **文档注册管理**：自动扫描并注册文档到各组件的`[组件代号]_INDEX.md`文件
- **语义关联发现**：通过AI分析发现文档间的潜在语义关系
- **双链网络构建**：建立`[[文档名]]`格式的双向知识链接
- **关联关系建议**：为INDEX表格提供智能化的关联关系建议

## 6. 产品信息追溯

产品信息追溯系统负责建立从**需求输入到最终输出**的完整块级追溯体系，遵循**输入到输出闭环**和**内容可追溯**原则。

### 6.1 系统定位与职责

追溯系统作为**块级管理和精确追溯**的核心组件：

- **块级内容管理**：管理文档内部的具体内容块和块级追溯关系
- **精确追溯链条**：建立从deliverables到原始需求的精确追溯路径
- **变更影响分析**：基于块级关系分析变更的影响范围
- **关系验证维护**：验证INDEX文件中关联关系的有效性

### 6.2 系统协作约束

**重要约束**：内容追溯关系建立在文档关联基础上，如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

**协作流程**：

1. **文档关联系统**：注册文档并发现基础关联关系
2. **追溯系统**：在已建立关联的文档基础上建立块级追溯
3. **共同维护**：两系统协作维护INDEX文件的完整性

### 6.3 技术实现

**遵循原则2(单一来源)和原则14(公共库调用)**：

### 6.4 块级追溯管理

**遵循原则5(内容可追溯)和原则15(奥卡姆剃刀)**：

信息追溯从输出(deliverables)的具体内容出发，追溯所有中间信息直到原始输入。支持：

- **内容块识别**：自动识别标题、列表、代码块等结构化内容
- **块级引用语法**：`[[REF:DOC_ID.BLOCK_ID:关系类型]]`格式的精确引用
- **影响传播分析**：基于块级关系的变更影响评估

### 6.5 系统集成原则

**遵循原则1(一体化)和原则7(流程解耦)**：

- **VSCode集成**：通过tasks.json配置统一操作界面
- **MCP服务支持**：可扩展的智能化处理能力
- **流程解耦**：文档关联与追溯系统独立运行，通过INDEX文件协作

详细实施方案请参考：

- [产品体系内容追溯系统框架](产品体系内容追溯系统框架.md)  
- [产品文档关联系统](产品文档关联系统.md)
- [产品体系构建实施方案](产品体系构建实施方案.md)

## 7. 测试一体化

[[功能测试一体化框架]]

## 8. 可视化

遵循**单一职责**和**内容唯一**原则，整个产品体系的可视化实现统一由可视化系统管理：

**系统链接**：

- [[产品体系可视化与交互系统集成框架]]
- [[产品体系可视化与交互系统开发指南]]

**可视化内容**：

- 工作流可视化与配置
- 文档关联网络可视化与文件跳转
- 信息追溯链可视化与内容预览
- 开发计划与进度追踪仪表板
- 架构图表、流程图等技术文档可视化
- 其他扩展可视化内容

**统一接口**：

**原则**：

- 一体化可视化集成：在统一交互平台上显示
- 可扩展性：显示内容和显示工具MCP servers可扩展
- 奥卡姆剃刀原则：保持简洁高效
- AI辅助：智能布局和关系推荐

## 9. 新产品体系构建完整资料

### 9.1 流程
[[新产品体系构建完整流程]]

## 注

### 1. 从需求到任务的转换

- **任务分解**：
  - 基于已确认的需求矩阵进行开发任务分解
  - 识别开发工作单元，包含具体实现步骤
  - 关注任务的可执行性、时间估计和资源需求
  - 输出结构化的任务列表与依赖关系
  
- **需求与任务的关联**：
  - 每个开发任务关联到对应的需求ID
  - 建立需求矩阵到任务列表的映射关系
  - 确保所有需求都转化为可执行的任务
  - 维护需求变更与任务更新的同步机制

### 2. 需求管理与任务分解集成

#### 2.1 需求与任务的区别

| 特性 | 需求分解 | 任务分解 |
|------|---------|---------|
| 目的 | 明确产品应该做什么 | 明确如何实现产品功能 |
| 阶段 | 产品定义阶段 | 开发计划阶段 |
| 关注点 | 功能特性、用户体验、技术规格 | 实现步骤、工作量、所需资源 |
| 组织方式 | 多层级垂直追溯结构 | 水平展开的开发计划结构 |
| 输出产物 | 需求矩阵文档 | 任务列表、甘特图 |

#### 2.2 需求到任务的转换流程

1. **需求确认**：完成需求矩阵文档并通过评审
2. **任务生成**：使用脚本或MCP工具将需求转换为初始任务列表
3. **任务细化**：开发团队细化任务步骤、估算工作量
4. **任务调度**：确定任务顺序、依赖关系和资源分配
5. **双向追踪**：建立需求ID与任务ID的映射关系

#### 2.3 状态同步机制

- **需求状态变更**：自动触发关联任务的状态更新
- **任务完成反馈**：任务完成后自动更新对应需求的状态
- **变更影响分析**：需求变更时自动分析并更新受影响的任务

#### 2.4 集成工具

```bash
# 从需求矩阵生成初始任务
python scripts/integration/req_to_tasks.py --matrix="requirements/requirements_matrix.md" --output="project_management/initial_tasks.json"

# 同步需求和任务状态
python scripts/integration/sync_req_tasks.py --requirements="requirements/requirements_matrix.md" --tasks="project_management/task_list.json"

# 生成需求-任务追踪报告
python scripts/reporting/generate_req_task_report.py --output="reports/req_task_traceability.md"
```

**链接工具**:

- [需求任务转换工具](scripts/integration/req_to_tasks.py)
- [状态同步工具](scripts/integration/sync_req_tasks.py)
- [追踪报告生成器](scripts/reporting/generate_req_task_report.py)

### 9.2 资料

deliverables/