#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
产品开发完整功能集成MCP服务器 - 工具模块

该模块包含所有MCP工具的实现，按业务流程组织：
- project_lifecycle: 项目生命周期流程 (T06, T07, T08)
- requirements_flow: 需求管理流程 (T01, T03, T04, T05, T09)
- development_flow: 开发质量流程 (T15, T16, T22)
- document_flow: 文档关联流程 (T23, T24, T25, T26)
- config_flow: 配置管理流程 (T28相关6个工具)

为保持向后兼容性，同时保留原有的技术分组模块。
"""

# 新的业务流程模块 - 主要使用
from .project_lifecycle import *
from .requirements_flow import *
from .development_flow import *
from .document_flow import *
from .config_flow import *

# 保持向后兼容性 - 原有技术分组模块
from .project_tools import *
from .config_tools import *
from .requirements_tools import *
from .document_tools import *
from .analysis_tools import *
from .visualization_tools import *

__all__ = [
    # 项目生命周期流程 (T06, T07, T08)
    'get_project_info',           # T06
    'init_project',               # T07 - 项目初始化
    'create_project_structure',   # T08

    # 需求管理流程 (T01, T03, T04, T05, T09)
    'import_requirements',        # T01
    'analyze_requirements',       # T03, T04
    'create_requirements_matrix', # T05
    'req_to_tasks',              # T09

    # 开发质量流程 (T15, T16, T22)
    'create_project_dashboard',   # T15
    'analyze_code_quality',       # T16
    'generate_document_report',   # T22

    # 文档关联流程 (T23, T24, T25, T26)
    'link_documents',            # T23
    'sync_canvas',               # T24
    'manage_trace',              # T25
    'create_flow_chart',         # T26

    # 配置管理流程 (T28相关6个工具)
    'load_config',
    'save_config',
    'validate_config',
    'merge_configs',
    'create_config_template',
    'backup_config',

    # 向后兼容性 - 保留原有工具名称
    'generate_document_links_config',
    'check_code_style',
    'scan_security_issues',
    'generate_architecture_diagram',
    'generate_uml_diagram',
]

__version__ = '3.0.0'
__author__ = 'Product Development Team'
__description__ = 'Business process-oriented product development MCP server tools'
