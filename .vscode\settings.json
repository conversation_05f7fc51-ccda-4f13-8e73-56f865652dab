{"cmake.buildDirectory": "${workspaceFolder}/build/${buildType}", "cmake.configureOnOpen": true, "cmake.generator": "Ninja", "cmake.configureArgs": ["-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"], "cmake.configureSettings": {"CMAKE_BUILD_TYPE": "${buildType}"}, "files.associations": {"qdialog": "cpp", "*.tcc": "cpp", "qdir": "cpp", "qprocess": "cpp", "list": "cpp", "qstringlist": "cpp", "qtimerevent": "cpp", "new": "cpp", "qshortcut": "cpp", "qsqldatabase": "cpp", "qapplication": "cpp", "qdebug": "cpp", "qmessagebox": "cpp", "qthread": "cpp", "qcoreapplication": "cpp", "qtimer": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "system_error": "cpp"}}