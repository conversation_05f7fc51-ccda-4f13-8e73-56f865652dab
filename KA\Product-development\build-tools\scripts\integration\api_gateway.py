#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统集成API网关
提供统一的API接口访问不同系统的数据
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify
import logging
import requests


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("api_gateway")

app = Flask(__name__)

# 从环境变量加载配置
TASK_MASTER_URL = os.environ.get("TASK_MASTER_URL", "http://localhost:3001")
TASK_MASTER_API_KEY = os.environ.get("TASK_MASTER_API_KEY", "")
VISUALIZATION_URL = os.environ.get("VISUALIZATION_URL", "http://localhost:5000")
VISUALIZATION_API_KEY = os.environ.get("VISUALIZATION_API_KEY", "")


@app.route('/api/status', methods=['GET'])
def get_status():
    """API网关状态接口"""
    return jsonify({
        "status": "ok",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    })


@app.route('/api/data/<source_type>', methods=['GET'])
def get_data(source_type):
    """API数据访问接口
    
    Args:
        source_type: 数据源类型，如tasks、documents、git等
        
    Returns:
        JSON响应
    """
    if source_type == 'tasks':
        return jsonify(get_task_data())
    elif source_type == 'documents':
        return jsonify(get_document_data())
    elif source_type == 'git':
        return jsonify(get_git_data())
    else:
        return jsonify({"error": "Unknown data source type"}), 400


def get_task_data() -> Dict:
    """获取任务数据
    
    从Task Master获取任务数据
    
    Returns:
        任务数据字典
    """
    try:
        # 首先尝试从文件获取数据
        data_file = os.environ.get("TASKS_DATA_FILE", "/shared/data/tasks_export.json")
        if os.path.exists(data_file):
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"从文件加载任务数据失败: {e}")
                
        # 如果文件不存在或读取失败，则从API获取
        response = requests.get(
            f"{TASK_MASTER_URL}/api/tasks",
            headers={
                "Authorization": f"Bearer {TASK_MASTER_API_KEY}",
                "Content-Type": "application/json"
            }
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"获取任务数据失败: {e}")
        return {"error": str(e), "tasks": []}


def get_document_data() -> Dict:
    """获取文档数据
    
    从可视化系统获取文档数据
    
    Returns:
        文档数据字典
    """
    try:
        # 首先尝试从文件获取数据
        data_file = os.environ.get("DOCUMENTS_DATA_FILE", "/shared/data/documents_export.json")
        if os.path.exists(data_file):
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"从文件加载文档数据失败: {e}")
                
        # 如果文件不存在或读取失败，则从API获取
        response = requests.get(
            f"{VISUALIZATION_URL}/api/documents",
            headers={
                "Authorization": f"Bearer {VISUALIZATION_API_KEY}",
                "Content-Type": "application/json"
            }
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"获取文档数据失败: {e}")
        return {"error": str(e), "documents": []}


def get_git_data() -> Dict:
    """获取Git仓库数据
    
    从可视化系统获取Git仓库数据
    
    Returns:
        Git仓库数据字典
    """
    try:
        response = requests.get(
            f"{VISUALIZATION_URL}/api/git",
            headers={
                "Authorization": f"Bearer {VISUALIZATION_API_KEY}",
                "Content-Type": "application/json"
            }
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        logger.error(f"获取Git数据失败: {e}")
        return {"error": str(e), "git_data": {}}


@app.route('/api/data/import/<data_type>', methods=['POST'])
def import_data(data_type):
    """数据导入接口
    
    接收外部系统导入的数据
    
    Args:
        data_type: 数据类型，如tasks、documents等
        
    Returns:
        JSON响应
    """
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    try:
        # 导入数据到对应目标
        if data_type == 'tasks':
            success = import_task_data(data)
        elif data_type == 'documents':
            success = import_document_data(data)
        else:
            return jsonify({"error": f"Unsupported data type: {data_type}"}), 400
            
        if success:
            return jsonify({"status": "success", "message": f"Data imported successfully"})
        else:
            return jsonify({"error": "Failed to import data"}), 500
    except Exception as e:
        logger.error(f"导入数据失败: {e}")
        return jsonify({"error": str(e)}), 500


def import_task_data(data: Dict) -> bool:
    """导入任务数据
    
    将任务数据导入到文件或系统
    
    Args:
        data: 任务数据
        
    Returns:
        导入成功返回True，否则返回False
    """
    try:
        # 保存到文件
        data_file = os.environ.get("TASKS_IMPORT_FILE", "/shared/data/tasks_import.json")
        os.makedirs(os.path.dirname(data_file), exist_ok=True)
        
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        # TODO: 如果需要，还可以将数据推送到Task Master系统
        
        return True
    except Exception as e:
        logger.error(f"导入任务数据失败: {e}")
        return False


def import_document_data(data: Dict) -> bool:
    """导入文档数据
    
    将文档数据导入到文件或系统
    
    Args:
        data: 文档数据
        
    Returns:
        导入成功返回True，否则返回False
    """
    try:
        # 保存到文件
        data_file = os.environ.get("DOCUMENTS_IMPORT_FILE", "/shared/data/documents_import.json")
        os.makedirs(os.path.dirname(data_file), exist_ok=True)
        
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        # TODO: 如果需要，还可以将数据推送到可视化系统
        
        return True
    except Exception as e:
        logger.error(f"导入文档数据失败: {e}")
        return False


@app.route('/api/webhook/<source>', methods=['POST'])
def webhook_handler(source):
    """Webhook处理接口
    
    处理来自各系统的Webhook事件
    
    Args:
        source: 事件源，如taskmaster、linear等
        
    Returns:
        JSON响应
    """
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    try:
        # 根据事件源分发处理
        if source == 'taskmaster':
            handle_taskmaster_event(data)
        elif source == 'linear':
            handle_linear_event(data)
        elif source == 'github':
            handle_github_event(data)
        else:
            return jsonify({"error": f"Unknown event source: {source}"}), 400
            
        return jsonify({"status": "success"})
    except Exception as e:
        logger.error(f"处理Webhook事件失败: {e}")
        return jsonify({"error": str(e)}), 500


def handle_taskmaster_event(data: Dict):
    """处理来自Task Master的事件
    
    Args:
        data: 事件数据
    """
    event_type = data.get("type")
    logger.info(f"收到Task Master事件: {event_type}")
    
    # TODO: 根据事件类型进行不同处理


def handle_linear_event(data: Dict):
    """处理来自Linear的事件
    
    Args:
        data: 事件数据
    """
    event_type = data.get("type")
    logger.info(f"收到Linear事件: {event_type}")
    
    # TODO: 根据事件类型进行不同处理


def handle_github_event(data: Dict):
    """处理来自GitHub的事件
    
    Args:
        data: 事件数据
    """
    event_type = data.get("type")
    logger.info(f"收到GitHub事件: {event_type}")
    
    # TODO: 根据事件类型进行不同处理


if __name__ == '__main__':
    # 获取运行端口
    port = int(os.environ.get("API_GATEWAY_PORT", 3010))
    
    # 启动服务
    app.run(host='0.0.0.0', port=port, debug=False)
    logger.info(f"API网关已启动，监听端口: {port}") 