#!/usr/bin/env python3
"""
需求导入配置生成器
负责生成项目的需求导入相关配置文件
"""

import os
import json
from pathlib import Path
import argparse


def generate_requirements_import_config(project_path=".", project_type="single_layer"):
    """生成需求导入配置文件"""
    
    # 创建配置目录
    config_dir = Path(project_path) / 'config'
    config_dir.mkdir(exist_ok=True)
    
    # 需求导入配置
    requirements_import_config = {
        "import_settings": {
            "supported_formats": [
                "xlsx",
                "csv",
                "json",
                "md"
            ],
            "auto_validation": True,
            "backup_enabled": True,
            "event_trigger": True
        },
        "sources": {
            "jira": {
                "enabled": False,
                "url": "",
                "auth": ""
            },
            "confluence": {
                "enabled": False,
                "url": "",
                "auth": ""
            },
            "excel": {
                "enabled": True,
                "template_path": "templates/requirements_template.xlsx"
            }
        },
        "output": {
            "format": "markdown",
            "location": "requirements/",
            "event_file": "requirements_import_completed.json"
        }
    }
    
    # 写入配置文件
    config_file = config_dir / 'requirements_import_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(requirements_import_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 需求导入配置文件已生成: {config_file}")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成需求导入配置文件')
    parser.add_argument('--project-path', default='.', help='项目路径')
    
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'], help='项目类型')
    
    args = parser.parse_args()
    
    success = generate_requirements_import_config(args.project_path, args.project_type)
    
    if success:
        print("[+] 需求导入配置生成完成")
    else:
        print("[X] 需求导入配置生成失败")
        exit(1)


if __name__ == "__main__":
    main() 