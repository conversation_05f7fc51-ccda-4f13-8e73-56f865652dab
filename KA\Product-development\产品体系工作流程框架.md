# 产品体系工作流程框架

## 1. 框架概述

产品体系工作流程框架旨在将产品开发全生命周期中各个组件有机连接，实现自动化流程和数据流转。该框架基于事件驱动模型，通过配置文件定义组件间的连接关系、触发条件和处理方式，同时兼容单层级和多层级目录结构。

### 1.1 核心原则

遵循《产品体系构建框架》中定义的13项核心原则，重点关注以下工作流程相关原则：

1. **流程解耦**：组件间通过事件机制松耦合连接
2. **工具可拓展**：支持MCP服务器和自定义脚本扩展
3. **AI辅助**：集成MCP服务器提供AI增强功能
4. **可视化**：提供流程图可视化和进度追踪

### 1.2 架构特性

- **组件化设计**：将产品开发流程拆分为独立组件，便于配置和管理
- **松耦合连接**：组件间通过事件机制松耦合连接，提高系统弹性
- **双向自动化**：支持工作流自动前进和变更影响分析回溯
- **可视化管理**：提供流程图可视化和进度追踪仪表板
- **多层级兼容**：同时支持单层级和多层级目录结构

### 1.3 架构图

![产品体系一体化流程架构图](reports/workflow_diagram.png)

## 2. 工作流组件

### 2.1 核心组件

- **需求导入组件**：处理需求文档的导入和初步分析
- **需求分析组件**：执行需求分解和需求矩阵维护
- **方案设计组件**：生成系统架构和技术方案
- **开发规划组件**：创建任务列表和开发计划
- **开发实施组件**：管理硬件、固件和软件开发
- **测试验证组件**：执行各类测试和验证
- **生产准备组件**：处理生产BOM和测试流程
- **项目输出组件**：管理版本发布和文档生成

### 2.2 组件间关系

```json
{
  "component_relations": {
    "需求导入": {
      "outputs": ["需求分析"],
      "events": ["需求文档导入完成"]
    },
    "需求分析": {
      "inputs": ["需求导入"],
      "outputs": ["方案设计"],
      "events": ["需求分析完成"]
    }
  }
}
```

## 3. 事件系统

### 3.1 事件类型

- **文档变更事件**：需求文档、设计文档等更新时触发
- **状态变更事件**：组件状态变更时触发（如需求审核通过）
- **时间触发事件**：按计划时间自动触发
- **手动触发事件**：由用户手动触发
- **ID表格更新事件**：当组件的ID表格更新时触发（单层级）
- **层级关系变更事件**：当层级间关系变更时触发（多层级）

### 3.2 事件处理流程

1. **事件触发**：由组件状态变更或用户操作触发
2. **事件分发**：工作流管理器接收并分发事件
3. **处理器执行**：执行配置的事件处理器
4. **状态更新**：更新相关组件状态
5. **通知发送**：发送处理结果通知

## 4. 配置系统

### 4.1 工作流配置

工作流配置通过JSON格式定义，支持灵活配置产品开发流程顺序和组件间关系：

```json
{
  "workflow": {
    "name": "产品体系一体化流程",
    "version": "1.0",
    "components": {
      "需求导入": {
        "handlers": [
          {
            "type": "mcp_server",
            "name": "Context7 MCP",
            "script": "scripts/mcp-server_local_integrations/analyze_requirements.py"
          }
        ]
      },
      "需求分析": {
        "handlers": [
          {
            "type": "mcp_server",
            "name": "Task Master MCP",
            "script": "scripts/mcp-server_local_integrations/requirement_analysis.py"
          }
        ]
      },
      "方案设计": {
        "handlers": [
          {
            "type": "mcp_server",
            "name": "UML MCP",
            "script": "scripts/mcp-server_local_integrations/generate_design.py"
          }
        ]
      }
    },
    "connections": [
      {
        "from": "需求导入",
        "to": "需求分析",
        "triggers": ["需求文档导入完成"],
        "conditions": {
          "status": "completed",
          "validation": "required"
        }
      },
      {
        "from": "需求分析",
        "to": "方案设计",
        "triggers": ["需求分析完成"],
        "conditions": {
          "status": "approved",
          "validation": "required"
        }
      }
    ],
    "sequence": [
      {
        "step": 1,
        "component": "需求导入",
        "next": ["需求分析"]
      },
      {
        "step": 2,
        "component": "需求分析",
        "next": ["方案设计"]
      },
      {
        "step": 3,
        "component": "方案设计",
        "next": ["开发规划"]
      }
    ]
  }
}
```

### 4.2 流程显示配置

流程显示通过专门的配置定义可视化效果：

```json
{
  "visualization": {
    "workflow_diagram": {
      "type": "mermaid",
      "style": {
        "theme": "default",
        "direction": "LR",
        "node_spacing": 100,
        "rank_spacing": 200
      },
      "components": {
        "需求导入": {
          "color": "#4CAF50",
          "icon": "📥"
        },
        "需求分析": {
          "color": "#2196F3",
          "icon": "📊"
        },
        "方案设计": {
          "color": "#FFC107",
          "icon": "📝"
        }
      },
      "connections": {
        "style": "curved",
        "color": "#666666",
        "thickness": 2
      }
    },
    "status_display": {
      "colors": {
        "pending": "#FFA726",
        "in_progress": "#42A5F5",
        "completed": "#66BB6A",
        "blocked": "#EF5350"
      },
      "indicators": {
        "show_progress": true,
        "show_duration": true,
        "show_dependencies": true
      }
    }
  }
}
```

### 4.3 流程显示实现

工作流框架提供多种方式显示流程状态：

1. **流程图显示**：
   - 使用Mermaid语法自动生成流程图
   - 支持交互式操作和状态更新
   - 可自定义样式和布局

```mermaid
graph LR
    需求导入[📥 需求导入] -->|需求文档导入完成| 需求分析[📊 需求分析]
    需求分析 -->|需求分析完成| 方案设计[📝 方案设计]
    方案设计 -->|方案设计完成| 开发规划[📋 开发规划]
    
    style 需求导入 fill:#4CAF50,stroke:#388E3C
    style 需求分析 fill:#2196F3,stroke:#1976D2
    style 方案设计 fill:#FFC107,stroke:#FFA000
    style 开发规划 fill:#9C27B0,stroke:#7B1FA2
```

2. **状态面板**：
   - 实时显示各组件状态
   - 显示进度和完成情况
   - 提供详细的状态信息

3. **进度追踪**：
   - 甘特图显示时间线
   - 里程碑标记
   - 依赖关系可视化

4. **交互式操作**：
   - 点击组件查看详情
   - 拖拽调整流程顺序
   - 手动触发状态转换

### 4.4 流程控制命令

```bash
# 生成流程图
python scripts/workflow/generate_diagram.py --config="config/workflow_config.json" --output="reports/workflow_diagram.png"

# 更新流程状态
python scripts/workflow/update_status.py --component="需求分析" --status="completed"

# 查看流程状态
python scripts/workflow/show_status.py --format="dashboard"
```

## 5. 工作流管理器

### 5.1 核心功能

- **事件处理**：接收、分发和处理各类事件
- **状态管理**：维护组件状态和转换规则
- **流程控制**：控制工作流的执行和暂停
- **错误处理**：处理异常情况和恢复机制
- **日志记录**：记录工作流执行过程

### 5.2 接口定义

```python
class WorkflowManager:
    def __init__(self, config_path):
        self.config = self.load_config(config_path)
        self.directory_adapter = self.create_directory_adapter()
    
    def process_event(self, event):
        """处理工作流事件"""
        adapted_event = self.directory_adapter.adapt_event(event)
        return self.execute_handlers(adapted_event)
    
    def get_workflow_status(self):
        """获取工作流状态"""
        return self.status_manager.get_status()
    
    def handle_error(self, error):
        """处理工作流错误"""
        return self.error_handler.handle(error)
```

## 6. 事件机制

### 6.1 事件类型

- **文档变更事件**：需求文档、设计文档等更新时触发
- **状态变更事件**：组件状态变更时触发（如需求审核通过）
- **时间触发事件**：按计划时间自动触发
- **手动触发事件**：由用户手动触发
- **ID表格更新事件**：当组件的ID表格更新时触发（单层级）
- **层级关系变更事件**：当层级间关系变更时触发（多层级）

### 6.2 事件数据结构

```json
{
  "event_id": "唯一标识",
  "event_type": "事件类型",
  "source": "源组件",
  "timestamp": "触发时间",
  "file_path": "相关文件路径",
  "user": "触发用户",
  "directory_structure": "single_layer/multi_layer",
  "layer_info": {
    "current_layer": "当前层级",
    "affected_layers": ["受影响的层级"]
  },
  "data": {
    "自定义数据字段": "值"
  }
}
```

### 6.3 兼容性处理器

处理器可以根据目录结构类型调整行为：

```json
{
  "type": "mcp_server",
  "name": "需求变更分析",
  "script": "scripts/mcp-server_local_integrations/requirement_change_impact.py",
  "params": {
    "changed_file": "${event.file_path}",
    "directory_type": "${config.directory_structure.type}",
    "layer_info": "${event.layer_info}",
    "output_dir": "reports/impact_analysis"
  }
}
```

## 7. 工作流管理器

### 7.1 核心接口

工作流管理器提供统一接口，自动适配不同目录结构：

```python
class WorkflowManager:
    def __init__(self, config_path):
        self.config = self.load_config(config_path)
        self.directory_adapter = self.create_directory_adapter()
    
    def process_event(self, from_component, to_component, trigger_name, event_data):
        """处理事件，自动适配目录结构"""
        adapted_event = self.directory_adapter.adapt_event(event_data)
        return self.execute_handlers(from_component, to_component, trigger_name, adapted_event)
    
    def create_directory_adapter(self):
        """根据配置创建目录适配器"""
        if self.config['directory_structure']['type'] == 'single_layer':
            return SingleLayerAdapter(self.config)
        else:
            return MultiLayerAdapter(self.config)
```

### 7.2 目录适配器

#### 7.2.1 单层级适配器

```python
class SingleLayerAdapter:
    def adapt_event(self, event_data):
        """适配单层级事件数据"""
        event_data['directory_structure'] = 'single_layer'
        event_data['component_id'] = self.extract_component_id(event_data['file_path'])
        return event_data
```

#### 7.2.2 多层级适配器

```python
class MultiLayerAdapter:
    def adapt_event(self, event_data):
        """适配多层级事件数据"""
        event_data['directory_structure'] = 'multi_layer'
        event_data['layer_info'] = self.extract_layer_info(event_data['file_path'])
        return event_data
    
    def update_requirements_matrix(self, layer_id, component_id):
        """更新需求矩阵"""
        # 实现多层级需求矩阵更新逻辑
        pass
```

### 7.3 命令行接口

```bash
# 自动检测目录结构并触发事件
python scripts/workflow/workflow_manager.py --action event \
  --from "需求分析" --to "方案设计" \
  --trigger "需求矩阵更新" \
  --event-data '{"file_path": "requirements/requirements_matrix.md"}'

# 生成适配的流程图
python scripts/workflow/enhanced_workflow_diagram.py \
  --type all --output "reports/workflow_diagram.png" \
  --directory-structure auto
```

## 8. 用例示例

### 8.1 单层级场景：需求变更影响分析

1. **检测到需求文档变更**：`01_requirements/REQ_MATRIX_产品需求矩阵.md`
2. **触发事件**：`requirements_matrix_updated`
3. **执行处理器**：
   - 更新ID表格：`scripts/update_id_tables.py --component REQ`
   - 分析影响：`scripts/mcp-server_local_integrations/requirement_change_impact.py`
   - 生成关系图：`scripts/generate_relation_graph.py`
4. **通知相关组件**：设计、开发、测试团队
5. **自动更新任务计划**

### 8.2 多层级场景：客户需求到技术平台影响

1. **客户层需求变更**：`客户层/客户A/需求/客户特定需求.md`
2. **触发事件**：`customer_requirement_updated`
3. **执行处理器**：
   - 分析层级影响：评估对产品层、应用层、技术平台层的影响
   - 更新需求矩阵：在各受影响层级更新需求追溯关系
   - 生成影响报告：包含层级影响分析和建议
4. **通知各层级负责人**
5. **更新相关层级的任务计划**

### 8.3 混合场景：跨目录结构协作

当团队同时使用单层级和多层级结构时：

1. **统一接口**：通过工作流管理器统一处理
2. **数据转换**：自动转换不同结构间的数据格式
3. **同步机制**：保持不同结构间的数据一致性

## 6. 可视化系统

[[产品体系可视化与交互系统集成框架]]

## 7. 扩展机制

### 7.1 MCP服务器集成

```json
{
  "mcp_servers": {
    "context7": {
      "name": "Context7 MCP",
      "script": "scripts/mcp-server_local_integrations/analyze_requirements.py",
      "capabilities": ["requirements_analysis", "document_processing"]
    }
  }
}
```

### 7.2 自定义处理器

```python
class CustomEventHandler:
    def handle(self, event):
        """处理自定义事件"""
        # 实现自定义处理逻辑
        pass
```

## 8. 实施指南

### 8.1 配置步骤

1. 创建工作流配置文件
2. 定义组件和关系
3. 配置事件处理器
4. 设置目录结构
5. 启动工作流管理器

### 8.2 最佳实践

- 保持组件职责单一
- 使用事件驱动通信
- 实现错误恢复机制
- 定期备份配置
- 监控工作流状态

### 8.3 故障排除

- 检查配置文件格式
- 验证事件处理器
- 查看错误日志
- 测试组件连接
- 验证权限设置

#### 9.1.1 自动检测初始化

```bash
# 自动检测目录结构并初始化
python scripts/workflow/init_workflow_config.py --auto-detect

# 手动指定目录结构类型
python scripts/workflow/init_workflow_config.py --directory-type single_layer
python scripts/workflow/init_workflow_config.py --directory-type multi_layer
```

#### 9.1.2 配置文件生成

脚本会根据检测到的目录结构生成相应的配置文件：

- `config/workflow_config.json`：主工作流配置
- `config/component_relations.json`：组件关系配置
- `config/relation_rules.json`：关系规则配置
- `config/mcp_servers.json`：MCP服务器配置

### 9.2 目录结构迁移

#### 9.2.1 从多层级到单层级

```bash
# 迁移多层级到单层级
python scripts/migration/multi_to_single.py \
  --source . \
  --output ./single_layer_project \
  --preserve-relationships
```

#### 9.2.2 从单层级到多层级

```bash
# 迁移单层级到多层级
python scripts/migration/single_to_multi.py \
  --source . \
  --output ./multi_layer_project \
  --layer-config config/layer_definition.json
```

### 9.3 混合环境配置

对于需要同时支持两种结构的团队：

```json
{
  "hybrid_mode": {
    "enabled": true,
    "primary_structure": "single_layer",
    "sync_rules": [
      {
        "from": "single_layer_components",
        "to": "multi_layer_components",
        "mapping": {
          "REQ": "各层级/需求",
          "DES": "各层级/设计"
        }
      }
    ]
  }
}
```
