#!/usr/bin/env bash
# Canvas监控器启动脚本
# 用途：启动、停止、重启Canvas监控器，支持多种运行模式
# 版本：1.0

set -euo pipefail

# =============== 配置区域 ===============
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="${PRODUCT_DEVELOP_DIR:-$(dirname "$(dirname "$SCRIPT_DIR")")}"
WATCHER_SCRIPT="${SCRIPT_DIR}/canvas_watcher.py"
CONFIG_FILE="${PROJECT_DIR}/config/canvas_watcher.yml"
PID_FILE="${PROJECT_DIR}/logs/canvas_watcher.pid"
LOG_DIR="${PROJECT_DIR}/logs"

# 创建必要目录
mkdir -p "$LOG_DIR" "${PROJECT_DIR}/config"

# =============== 颜色定义 ===============
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============== 日志函数 ===============
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "${DEBUG:-0}" == "1" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# =============== 功能函数 ===============

show_usage() {
    cat << EOF
Canvas监控器启动脚本

用法: $0 <命令> [选项]

命令:
  start     启动监控器
  stop      停止监控器
  restart   重启监控器
  status    查看运行状态
  logs      查看日志
  stats     查看统计信息
  config    生成配置文件

选项:
  -d, --daemon      后台运行模式
  -c, --config      指定配置文件路径
  -p, --project     指定项目路径
  -v, --verbose     详细输出模式
  -h, --help        显示帮助信息

示例:
  $0 start                          # 前台启动
  $0 start --daemon                 # 后台启动
  $0 start --config custom.yml      # 使用自定义配置
  $0 logs --follow                  # 实时查看日志
  $0 stats --json                   # JSON格式输出统计
EOF
}

check_dependencies() {
    log_debug "检查依赖..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        log_error "Python未安装或不在PATH中"
        exit 1
    fi
    
    # 检查watchdog库
    if ! python -c "import watchdog" 2>/dev/null; then
        log_error "watchdog库未安装"
        log_info "请运行: pip install watchdog"
        exit 1
    fi
    
    # 检查监控脚本
    if [[ ! -f "$WATCHER_SCRIPT" ]]; then
        log_error "监控脚本不存在: $WATCHER_SCRIPT"
        exit 1
    fi
    
    log_debug "依赖检查完成"
}

generate_config() {
    log_info "生成配置文件: $CONFIG_FILE"
    
    if [[ -f "$CONFIG_FILE" ]]; then
        read -p "配置文件已存在，是否覆盖？ (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过配置文件生成"
            return 0
        fi
    fi
    
    mkdir -p "$(dirname "$CONFIG_FILE")"
    
    # 复制配置文件模板
    if [[ -f "${SCRIPT_DIR}/canvas_watcher_config.yml" ]]; then
        cp "${SCRIPT_DIR}/canvas_watcher_config.yml" "$CONFIG_FILE"
        log_info "配置文件已生成: $CONFIG_FILE"
    else
        log_warn "配置模板不存在，创建基本配置"
        cat > "$CONFIG_FILE" << 'EOF'
watcher:
  watch_delay: 0.5
  sync_interval: 5
  max_retries: 3
  log_level: INFO
  max_log_size: 10MB
  batch_timeout: 30
EOF
        log_info "基本配置文件已生成: $CONFIG_FILE"
    fi
}

is_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            # PID文件存在但进程不存在，清理PID文件
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

start_watcher() {
    local daemon_mode=false
    local config_file="$CONFIG_FILE"
    local verbose=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--daemon)
                daemon_mode=true
                shift
                ;;
            -c|--config)
                config_file="$2"
                shift 2
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查是否已运行
    if is_running; then
        log_warn "Canvas监控器已在运行 (PID: $(cat "$PID_FILE"))"
        return 0
    fi
    
    # 检查依赖
    check_dependencies
    
    # 构建命令
    local cmd="python '$WATCHER_SCRIPT'"
    
    if [[ -f "$config_file" ]]; then
        cmd="$cmd --config '$config_file'"
        log_info "使用配置文件: $config_file"
    fi
    
    if [[ "$verbose" == "true" ]]; then
        cmd="$cmd --verbose"
    fi
    
    # 切换到项目目录
    cd "$PROJECT_DIR"
    
    if [[ "$daemon_mode" == "true" ]]; then
        log_info "启动Canvas监控器（后台模式）..."
        
        # 后台启动
        nohup bash -c "$cmd" > "${LOG_DIR}/canvas_watcher_daemon.log" 2>&1 &
        local pid=$!
        
        # 保存PID
        echo "$pid" > "$PID_FILE"
        
        # 等待确认启动
        sleep 2
        if is_running; then
            log_info "✓ Canvas监控器已启动 (PID: $pid)"
            log_info "日志文件: ${LOG_DIR}/canvas_watcher_daemon.log"
        else
            log_error "✗ Canvas监控器启动失败"
            return 1
        fi
    else
        log_info "启动Canvas监控器（前台模式）..."
        log_info "按 Ctrl+C 停止监控"
        
        # 前台启动
        exec bash -c "$cmd"
    fi
}

stop_watcher() {
    if ! is_running; then
        log_warn "Canvas监控器未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    log_info "停止Canvas监控器 (PID: $pid)..."
    
    # 发送TERM信号
    if kill -TERM "$pid" 2>/dev/null; then
        # 等待进程结束
        local count=0
        while kill -0 "$pid" 2>/dev/null && [[ $count -lt 30 ]]; do
            sleep 1
            ((count++))
        done
        
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "进程未响应TERM信号，发送KILL信号..."
            kill -KILL "$pid" 2>/dev/null || true
        fi
        
        rm -f "$PID_FILE"
        log_info "✓ Canvas监控器已停止"
    else
        log_error "无法停止进程 $pid"
        return 1
    fi
}

restart_watcher() {
    log_info "重启Canvas监控器..."
    stop_watcher
    sleep 2
    start_watcher "$@"
}

show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        log_info "Canvas监控器正在运行 (PID: $pid)"
        
        # 显示进程信息
        if command -v ps &> /dev/null; then
            echo "进程信息:"
            ps -p "$pid" -o pid,ppid,cpu,mem,etime,cmd 2>/dev/null || true
        fi
        
        # 显示最近日志
        if [[ -f "${LOG_DIR}/canvas_watcher.log" ]]; then
            echo
            echo "最近日志:"
            tail -5 "${LOG_DIR}/canvas_watcher.log" 2>/dev/null || true
        fi
    else
        log_warn "Canvas监控器未运行"
    fi
}

show_logs() {
    local follow=false
    local lines=50
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow)
                follow=true
                shift
                ;;
            -n|--lines)
                lines="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    local log_file="${LOG_DIR}/canvas_watcher.log"
    
    if [[ ! -f "$log_file" ]]; then
        log_warn "日志文件不存在: $log_file"
        return 1
    fi
    
    if [[ "$follow" == "true" ]]; then
        log_info "实时监控日志 (按 Ctrl+C 退出):"
        tail -f "$log_file"
    else
        log_info "显示最近 $lines 行日志:"
        tail -n "$lines" "$log_file"
    fi
}

show_stats() {
    local format="human"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --json)
                format="json"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    local stats_file="${LOG_DIR}/canvas_watcher_stats.json"
    
    if [[ ! -f "$stats_file" ]]; then
        log_warn "统计文件不存在: $stats_file"
        return 1
    fi
    
    if [[ "$format" == "json" ]]; then
        cat "$stats_file"
    else
        log_info "Canvas监控器统计信息:"
        python3 -c "
import json
import sys
from datetime import datetime

try:
    with open('$stats_file', 'r') as f:
        stats = json.load(f)
    
    print(f'总同步次数: {stats.get(\"total_syncs\", 0)}')
    print(f'成功同步: {stats.get(\"successful_syncs\", 0)}')
    print(f'失败同步: {stats.get(\"failed_syncs\", 0)}')
    print(f'平均耗时: {stats.get(\"avg_duration\", 0):.2f}秒')
    
    if stats.get('last_sync_time'):
        print(f'最后同步: {stats[\"last_sync_time\"]}')
    
    if stats.get('uptime_start'):
        start_time = datetime.fromisoformat(stats['uptime_start'])
        uptime = datetime.now() - start_time
        print(f'运行时间: {uptime}')
    
    if stats.get('last_error'):
        print(f'最后错误: {stats[\"last_error\"]}')

except Exception as e:
    print(f'解析统计文件失败: {e}', file=sys.stderr)
    sys.exit(1)
"
    fi
}

# =============== 主程序 ===============

# 解析全局参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -p|--project)
            PROJECT_DIR="$2"
            shift 2
            ;;
        -*)
            log_error "未知全局参数: $1"
            show_usage
            exit 1
            ;;
        *)
            break
            ;;
    esac
done

# 检查命令
if [[ $# -eq 0 ]]; then
    log_error "请指定命令"
    show_usage
    exit 1
fi

command="$1"
shift

# 执行命令
case "$command" in
    start)
        start_watcher "$@"
        ;;
    stop)
        stop_watcher
        ;;
    restart)
        restart_watcher "$@"
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    stats)
        show_stats "$@"
        ;;
    config)
        generate_config
        ;;
    *)
        log_error "未知命令: $command"
        show_usage
        exit 1
        ;;
esac 