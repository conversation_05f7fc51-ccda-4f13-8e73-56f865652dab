#include "dichotomy.h"

///**
// * @brief 二分法寻找
// */
//CClenAdjustOpt::e_status CDichotomy::z_peak_dichotomy_find(const uint16_t &max_tmp, uint32_t* last_max_peak, ST_3D *move_dis)
//{
//    if(max_tmp > *last_max_peak) //最大peak
//    {
//        *last_max_peak = max_tmp;
//        m_device_info_->last_move_delta = m_device_info_->move_delta;

//        move_dis->z = move_dis_cal(1, m_device_info_->z_direction * 10, m_move_z_step);  //二分法步进10
//    }
//    if(max_tmp < (int)(*last_max_peak - Z_PEAK_DELTA_LIMIT)) //最强位置反向找
//    {
//        m_device_info_->z_direction = - m_device_info_->z_direction;
//        move_dis->z = m_device_info_->last_move_delta.z - m_device_info_->move_delta.z + m_device_info_->z_direction * m_xml_param["z_move_step"] * m_move_z_step;

//        if(m_device_info_->z_direction == m_xml_param["default_z_direct"]) //搜寻完毕
//        {
//            move_dis->x = m_device_info_->last_move_delta.x - m_device_info_->move_delta.x;
//            move_dis->y = m_device_info_->last_move_delta.y - m_device_info_->move_delta.y;
//            move_dis->z = m_device_info_->last_move_delta.z - m_device_info_->move_delta.z;

//            if(*last_max_peak < m_config_->peak_throld)
//            {
//                qDebug() << "-clen -f: center max peak too low" << *last_max_peak;
//                return e_status::fail;
//            }
//            else
//            {
//                return e_status::ok;
//            }
//        }
//    }
//}

