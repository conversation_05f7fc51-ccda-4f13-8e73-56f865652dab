# test_path_debug

## 项目概述

本项目采用产品体系构建框架，遵循一体化、单一来源、内容唯一等基本原则。

### 项目信息

- 项目名称: test_path_debug
- 框架类型: 单层级产品目录框架  
- 创建日期: 2025-07-07
- 版本: 0.1.0
- 架构设计: 模块化组件架构

## 架构特点

### 组件化设计
- **目录结构组件**: 负责项目目录结构初始化
- **工作流组件**: 负责工作流程配置和管理
- **追溯系统组件**: 负责文档追溯和关联管理
- **各组件独立**: 每个组件负责自己的配置文件创建和维护

### 配置文件管理
- `workflow_config.json`: 由工作流组件创建和维护
- `traceability_config.json`: 由追溯系统组件创建和维护
- `document_links_config.json`: 由追溯系统组件创建和维护
- 各组件配置独立管理，便于维护和扩展

## 目录结构

- `product_info/`: 产品基本信息和描述
- `requirements/`: 需求相关文档（需求矩阵、规格说明等）
- `design/`: 设计方案文档（架构设计、详细设计等） 
- `development/`: 开发实施文档（开发计划、代码等）
- `quality/`: 测试验证文档（测试计划、测试报告等）
- `production/`: 生产相关文档（BOM、工艺流程等）
- `deliverables/`: 交付物文档（用户手册、发布包等）
- `project_management/`: 项目管理文档（进度计划、风险管理等）
- `scripts/`: 项目脚本（调用公共库脚本）
- `config/`: 配置文件（工作流、追溯、链接等配置）
- `reports/`: 报告输出（追溯报告、关系图等）

## 追溯系统

本项目采用基于INDEX文件的追溯系统：
- 每个组件目录下有对应的 XX_INDEX.md 文件
- 文档无需严格命名，通过INDEX文件注册ID实现关联
- 支持灵活的文档管理和追溯关系维护

## 快速开始

### 1. 运行工作流

```bash
python scripts/run_workflow.py
```

### 2. 更新文档关联（文档关联系统）

```bash
python scripts/link_documents.py
```

### 3. 更新追溯系统（追溯系统）

```bash
python scripts/manage_traceability.py
```

### 4. 生成块级关系图

```bash
python ../scripts/infoTrace/change_impact.py --project-path . --visualize
```

## 工作流程

本项目遵循以下工作流程：

1. **需求导入** - 从外部系统导入需求
2. **需求分析** - 分解和分析需求 
3. **方案设计** - 生成系统架构和技术方案
4. **开发实施** - 硬件、固件、软件开发
5. **测试验证** - 质量测试和验证
6. **生产准备** - 生产BOM和流程准备
7. **项目输出** - 生成最终交付物

## 双重管理系统

本项目采用文档关联系统和追溯系统协作的双重管理模式：

### 文档关联系统
- **文档注册**: 自动扫描并注册文档到XX_INDEX.md表格
- **语义关联**: 通过AI分析发现文档间的语义关系  
- **双链网络**: 建立[[文档名]]格式的双向链接
- **关联建议**: 为INDEX表格提供关联关系建议

### 追溯系统
- **块级管理**: 管理文档内部的内容块和块级追溯关系
- **精确追溯**: 建立从输出到输入的精确追溯链条
- **变更影响分析**: 基于块级关系分析变更影响范围
- **关系验证**: 验证INDEX中关联关系的有效性

### INDEX文件特点
- 每个组件目录下有对应的 XX_INDEX.md 文件
- 支持文档级和块级两种追溯粒度
- 文档无需严格命名，通过INDEX文件注册ID实现关联
- 两个系统协作维护，确保关联关系的准确性和完整性

## 块级追溯管理

项目支持文档内部的块级精确追溯：

### 内容块识别
- **自动识别**: 标题、列表项、代码块、表格、引用块
- **手动标记**: 使用`<!-- BLOCK_ID: DOC001.XXX.001 -->`标记关键内容
- **块ID格式**: `DOC_ID.BLOCK_TYPE.SEQUENCE`

### 块级引用语法
```markdown
[[REF:REQ001.FUNC.001]]                # 引用需求功能块
[[REF:DES001.ARCH.001:实现]]            # 实现设计架构块
[[REF:DEV001.CODE.001:依赖]]            # 依赖代码实现块
[[REF:QA001.TEST.001:验证]]             # 测试验证块
```

### 追溯粒度
- **文档级追溯**: 文档间的整体关联关系
- **块级追溯**: 文档内部具体内容块的精确追溯
- **变更影响**: 基于块级关系的精确影响分析

## 组件维护

### 工作流组件
- 配置文件: `config/workflow_config.json`
- 维护脚本: `scripts/workflow/init_workflow.py`
- 负责工作流程定义和MCP服务器配置

### 追溯系统组件  
- 配置文件: `config/traceability_config.json`, `config/block_analysis_config.json`
- 维护脚本: `scripts/infoTrace/init_trace.py`, `scripts/infoTrace/auto_index_manager.py`
- 负责块级内容管理和精确追溯，在XX_INDEX.md文件基础上添加块级信息

### 文档关联系统组件
- 配置文件: `config/document_links_config.json`, `config/semantic_analysis_config.json`
- 维护脚本: `scripts/links/init_links.py`, `scripts/links/auto_link_documents.py`
- 负责文档注册和语义关联发现，创建和维护XX_INDEX.md文件

### 目录结构组件
- 维护脚本: `scripts/directory_initialization/`
- 负责项目目录结构初始化

## 系统协作关系

### 重要约束
**内容追溯关系建立在文档关联基础上**。如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

### INDEX文件协作管理
每个组件的XX_INDEX.md文件包含10个字段，由两个系统协作管理：

| 字段 | 责任系统 | 描述 |
|------|---------|------|
| 文档ID | 文档关联系统 | 唯一文档标识符 |
| 文档名称 | 文档关联系统 | 文档显示名称 |
| 文档路径 | 文档关联系统 | 文档文件路径 |
| 文档类型 | 文档关联系统 | 文档分类类型 |
| 块ID | 追溯系统 | 文档内容块标识 |
| 块类型 | 追溯系统 | 内容块分类 |
| 块标题/描述 | 追溯系统 | 内容块描述 |
| 追溯来源块ID | 追溯系统 | 块级追溯来源 |
| 关系类型 | 追溯系统 | 块级关系类型 |
| 最后更新时间 | 两系统共享 | 最后修改时间 |

### 使用流程

1. **文档创建**: 首先由文档关联系统注册文档并创建基础INDEX记录
2. **语义分析**: 文档关联系统分析文档间的语义关联
3. **块级管理**: 追溯系统在已关联文档基础上进行块级内容管理
4. **精确追溯**: 追溯系统建立块级的精确追溯关系
5. **协作维护**: 两系统协作维护INDEX文件的完整性

## 更多信息

详细框架说明请参考：`../产品体系构建框架.md`
