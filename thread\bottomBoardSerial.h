#ifndef _BOTTOM_BOARD_SERIAL_H_
#define _BOTTOM_BOARD_SERIAL_H_

#include <QString>
#include <QMessageBox>
#include <QDebug>
#include <QTime>
#include <QTimer>

#include "IBottom.h"

class CBottomBoardSerial: public QObject
{
    Q_OBJECT
public:
    explicit CBottomBoardSerial(QObject *parent = nullptr, IBottom *bottom_board_ = nullptr);
    ~CBottomBoardSerial();

    uint16_t     m_task_id;

    void device_change_interface(IBottom* top_board_); //设备接口调整
    void task_id_change(const uint8_t &id);
public slots:
    //void portInit(bool isOpen);
    void loop(int task_id); //接收串口数据API

private:
    IBottom *mi_bottom_board_ = nullptr;
};

#endif
