#!/usr/bin/env python3
"""
产品开发MCP服务器 - 标准MCP协议实现

支持Cursor自动启动和集成，遵循标准MCP协议规范。
"""

import asyncio
import json
import sys
import subprocess
import logging
from pathlib import Path
from typing import Any, Dict, List

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_server.log'),
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)


class ProductDevelopmentMCPServer:
    """标准MCP协议服务器"""
    
    def __init__(self):
        self.scripts_base = Path(__file__).parent.parent
        self.tools = self._define_tools()
        logger.info(f"MCP服务器初始化，脚本基础路径: {self.scripts_base}")
    
    def _define_tools(self) -> List[Dict[str, Any]]:
        """定义可用工具"""
        return [
            {
                "name": "init_project",
                "description": "初始化新的产品项目",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "project_name": {"type": "string", "description": "项目名称"},
                        "project_path": {"type": "string", "description": "项目路径"},
                        "structure_type": {"type": "string", "description": "项目结构类型(single_layer/multi_level)"}
                    },
                    "required": ["project_name"]
                }
            },
            {
                "name": "generate_all_configs",
                "description": "生成所有项目配置文件",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "project_path": {"type": "string", "description": "项目路径"},
                        "project_type": {"type": "string", "description": "项目类型"},
                        "config_types": {"type": "array", "items": {"type": "string"}, "description": "配置类型列表"}
                    },
                    "required": ["project_path"]
                }
            },
            {
                "name": "link_documents",
                "description": "执行文档关联和注册",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "project_path": {"type": "string", "description": "项目路径"},
                        "register": {"type": "boolean", "description": "是否注册文档"},
                        "all": {"type": "boolean", "description": "是否处理所有文档"}
                    },
                    "required": ["project_path"]
                }
            },
            {
                "name": "sync_canvas",
                "description": "同步INDEX文件到Canvas",
                "inputSchema": {
                    "type": "object", 
                    "properties": {
                        "project_path": {"type": "string", "description": "项目路径"},
                        "sync_to_canvas": {"type": "boolean", "description": "同步到Canvas"}
                    },
                    "required": ["project_path"]
                }
            }
        ]

    def handle_message(self, message: dict) -> dict:
        """处理MCP消息"""
        try:
            method = message.get("method")
            params = message.get("params", {})
            msg_id = message.get("id")
            
            logger.info(f"收到消息: method={method}, id={msg_id}")
            
            if method == "initialize":
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {}
                        },
                        "serverInfo": {
                            "name": "product-development",
                            "version": "1.0.0"
                        }
                    }
                }
                logger.info("发送初始化响应")
                return response
            
            elif method == "tools/list":
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {"tools": self.tools}
                }
                logger.info(f"发送工具列表，共{len(self.tools)}个工具")
                return response
            
            elif method == "tools/call":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                
                logger.info(f"调用工具: {tool_name}, 参数: {arguments}")
                result = self._call_tool(tool_name, arguments)
                
                response = {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "result": {
                        "content": [{
                            "type": "text",
                            "text": result
                        }]
                    }
                }
                logger.info("工具调用完成")
                return response
            
            else:
                logger.warning(f"未知方法: {method}")
                return {
                    "jsonrpc": "2.0",
                    "id": msg_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }
                
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            return {
                "jsonrpc": "2.0",
                "id": message.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }

    def _call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """调用工具"""
        script_map = {
            "init_project": "init_product_project.py",
            "generate_all_configs": "config/generate_all_configs.py", 
            "link_documents": "links/auto_link_documents.py",
            "sync_canvas": "canvas/auto_link_documents.py"
        }
        
        if tool_name not in script_map:
            return f"❌ 未知工具: {tool_name}"
        
        script_path = self.scripts_base / script_map[tool_name]
        
        if not script_path.exists():
            return f"❌ 脚本不存在: {script_path}"
        
        # 构建命令
        cmd = [sys.executable, str(script_path)]
        
        # 添加参数
        for key, value in arguments.items():
            if isinstance(value, bool) and value:
                cmd.append(f"--{key.replace('_', '-')}")
            elif isinstance(value, str) and value:
                cmd.extend([f"--{key.replace('_', '-')}", value])
            elif isinstance(value, list) and value:
                cmd.append(f"--{key.replace('_', '-')}")
                cmd.extend(value)
        
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 同步执行脚本
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.scripts_base,
                timeout=300  # 5分钟超时
            )
            
            result = f"=== {tool_name} 执行结果 ===\n"
            result += f"命令: {' '.join(cmd)}\n"
            result += f"返回码: {process.returncode}\n\n"
            
            if process.stdout:
                result += f"输出:\n{process.stdout}\n"
            
            if process.stderr:
                result += f"错误:\n{process.stderr}\n"
            
            if process.returncode == 0:
                result += "\n✅ 执行成功"
            else:
                result += f"\n❌ 执行失败 (返回码: {process.returncode})"
            
            logger.info(f"工具执行完成，返回码: {process.returncode}")
            return result
            
        except subprocess.TimeoutExpired:
            return f"❌ 执行超时 (超过5分钟)"
        except Exception as e:
            logger.error(f"执行工具时出错: {e}")
            return f"❌ 执行异常: {str(e)}"

    def run(self):
        """运行服务器 - 同步版本"""
        logger.info("MCP服务器启动")
        
        try:
            while True:
                try:
                    # 从stdin读取一行
                    line = sys.stdin.readline()
                    
                    if not line:
                        logger.info("收到EOF，服务器退出")
                        break
                    
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        message = json.loads(line)
                        logger.debug(f"解析消息: {message}")
                        
                        response = self.handle_message(message)
                        
                        # 输出响应
                        output = json.dumps(response, ensure_ascii=False)
                        print(output, flush=True)
                        logger.debug(f"发送响应: {output}")
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析错误: {e}, 输入: {line}")
                        continue
                        
                except (EOFError, KeyboardInterrupt):
                    logger.info("服务器收到中断信号")
                    break
                except Exception as e:
                    logger.error(f"处理输入时出错: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"服务器运行时出错: {e}")
        finally:
            logger.info("MCP服务器关闭")


def main():
    """主函数"""
    logger.info("启动产品开发MCP服务器")
    server = ProductDevelopmentMCPServer()
    server.run()


if __name__ == "__main__":
    main() 