#include "IProtocol.h"

IProtocol::IProtocol(){

}


IProtocol::~IProtocol(){

}

/**
 * @brief 计算xor
 * @param xor_id: xor byte loc
 * @param: array
 * @return
 */
void IProtocol::calXOR(const uint8_t &xor_id, QByteArray* array_)
{
  char xor_tmp = 0;

  for(uint8_t i = 0; i < array_->length(); ++i) {
      if(i != xor_id) {
          xor_tmp ^= array_->at(i);
      }
  }
  //array_->at(3) = xor_tmp; //?无法赋值
  array_->replace(xor_id, 1, &xor_tmp, 1);
}

/**
 * @brief calXOR
 * @param 计算数据
 * @param 计算字节长度
 */
uint8_t IProtocol::calXOR(uint8_t *data, const uint16_t &len)
{
  uint8_t xor_tmp = 0;

  for(uint8_t i = 0; i < len; ++i) {
      if(i != 3)
      {
          xor_tmp ^= *(data+i);
      }
  }
  return xor_tmp;
}

uint8_t IProtocol::calXOR(uint8_t *data, const uint16_t &len, const uint8_t &xor_index)
{
  uint8_t xor_tmp = 0;

  for(uint8_t i = 0; i < len; ++i) {
      if(i != xor_index) {
          xor_tmp ^= *(data+i);
      }
  }
  return xor_tmp;
}

uint8_t IProtocol::checkSum(uint8_t *p_data, uint8_t data_len) {
    uint16_t sum = 0;
    while (data_len--) {
        sum += *p_data++;
    }

    if(sum > 0xff) {
        sum=~sum;
        sum+=1;
    }

    return sum;
}
