#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
布局引擎模块

提供不同的图形布局算法
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from .interfaces import LayoutEngine, VisualizationData
from .component_utils import component_manager

class ComponentLayoutEngine(LayoutEngine):
    """组件布局引擎 - 按组件区域布局节点"""
    
    def apply_layout(self, data: VisualizationData) -> VisualizationData:
        """应用组件布局"""
        # 获取所有组件类型
        components_in_use = list(set(node.component for node in data.nodes))
        
        # 计算组件区域
        total_width = 900.0
        total_height = 650.0
        areas = component_manager.calculate_component_areas(
            components_in_use, total_width, total_height
        )
        
        # 为每个节点分配区域内的位置
        component_counts = {comp: 0 for comp in components_in_use}
        
        for node in data.nodes:
            if node.component in areas:
                area = areas[node.component]
                count = component_counts[node.component]
                
                # 在区域内均匀分布
                cols = max(1, int((len([n for n in data.nodes if n.component == node.component]) ** 0.5)))
                row = count // cols
                col = count % cols
                
                node.x = area["x"] + area["width"] * 0.1 + (col * area["width"] * 0.8 / max(1, cols))
                node.y = area["y"] + area["height"] * 0.2 + (row * area["height"] * 0.6 / max(1, cols))
                
                component_counts[node.component] += 1
        
        return data
    
    def get_layout_config(self) -> Dict[str, Any]:
        """获取布局配置"""
        return {
            "name": "component_layout",
            "description": "按组件区域布局节点",
            "supports_areas": True,
            "supports_constraints": True
        } 