
## [v2.3.1] - 2025-07-08
- docs(config): readd  config files in MinSizeRel to git

## [v2.3.0] - 2025-07-08
- docs(config): add config to git
- feat(task): add new task to control len machine start

## [v2.2.0] - 2025-05-09
- fix(debug): fix execute program pwd in launch.json
- fix(log): fix date repeats problem in output messagge
- feat(T5): 修改光路耦合流程以适应T5，增加T5光路调节设备

## [v2.1.0] - 2025-02-14
- feat(device): add new len adjust machine "qingHe"
- feat(communicate): add device communicate ack timeout log output
- feat(len_machine): combine machine move distance and enable flag to one cmd
- feat(function): add manual get location button
- refactor(function):  refactor roundPeakCal() function
- refactor(local_param): Integrate Asymmetry_type into Amp_select, and modify the corresponding processing function
- feat(facula_threshold): add local params to config symmetry judge peak threshold

## [v2.0.0] - 2025-01-02
- feat(judge): add center peak max peak threshold
- feat(mes_info): update mes connect info(BREAKING CHANGE)
- feat(log): optimize log output function

## [v1.3.0] - 2024-10-15

- chore(generate_log): master commit msg add to every component CHANGELOG file
- feat(docs): user manual to assitant
- fix(logic): limit motor step in  symmetryMove for D4
- fix(logic): m_xml_param["ARR_peak_delta"] represents  minimum value of xy_peak_delta_tor
- fix(comm): add calibMode's  interval_time times and reexec_times

## [v1.2.6] - 2024-06-19

+ refactor: sensor切换模式需要先解锁
- refactor: 改变手动XY轴移动方向

Len_adjust V1.2.5_2024.5.15
- fix: 手动调节，不同项目调节方向未兼容
- feature: 增加D6调节，特定通道(right mp) 固定比例调节
- feature: 增加D6每个调节步骤独立判定值
- feature: 增加D6每个判定步骤独立判定容差

Len_adjust V1.2.4_2024.4.19
- refactor: 增加光斑异常时处理方式选择。由本地../clen_config.ini facula_ng_handle参数配置

Len_adjust V1.2.3_2024.3.15
- refactor: change minimum step from 2 to 3 within CFaculaCircle::symmetryMove

Len_adjust V1.2.2_2024.3.15
- test: 增加 机台交互屏蔽，用于测试模组
- feature: 增加工单显示
- refactor: z轴调节最大值增加幅值限制，增加peak幅值变化小的计数，大于4次时退出
- refactor: 中心最大值与四周值 差值变化，2倍->1.5倍
- refactor: 四周值差值容差最大值， 40->外部配置(默认100)
- fix: random move step cal erro within randomFaculaFind function
- fix: 增加获取固化状态超时时间与次数

Len_adjust V1.2.1_2024.1.22
- feature: 增加固定值对称判定方式
- fix: 工单号索引时限制域名(未加域名，003域系统维护时，会导致索引错误)，域名由外部配置

Len_adjust V1.2.0_2023.12.15
- refactor: 调节len adjust结构
- refactor: 调整table ui显示部分

V1.1.0_len_adjust_2023.9.13
- fix: 子线程共享参数调用冲突问题
- fix: sensor异常模式ack导致解析异常
- refactor: 最后一项PASS的，都录入本地和MES数据

V1.0.9_len_adjust_2023.8.30
- refactor: 增加本地事务号保存
- refactor: 事务号索引增加domin条件
- refactor: 增加标签获取次数

V1.0.8_len_adjust_2023.8.25
- refactor: 增加模式切换后的等待时间

V1.0.7_len_adjust_2023.8.14

- feature: 增加光斑中心位置自设定

V1.0.6_len_adjust_2023.8.3

- fix: 事务号索引，全部数据索引未赋值问题

V1.0.5_len_adjust_2023.6.12

- style: 增加初始虚光斑，反向调节
- style: 修改版本号解析 Vx.y.z.m(软件 + 硬件 + 料号 + 修改次数)

V1.0.4_len_adjust_2023.6.8

- fix: 错误原因太长，mes写入异常,改用错误码
- style: 调节窗口
- fix: 结果正常时，rsn_code 错误码不能写入 0

V1.0.3_len_adjust_2023.6.7

- style: close->open, 通信异常，不增加计数
- style: 限位和判定异常，保留手动继续调节
- style: map info 初始化调整
- fix: mst_dds_ 内存未释放

V1.0.2_len_adjust_2023.6.1

- fix: MES数据写入异常，重复写入成功后还保留异常状态的BUG
- fix: MES数据写入，用日期索引事务号当天无结果，返回值 "0"，处理异常
- fix: MES数据录入：限位但最终结果合格，录入

len_data_mes_V1.0.1_2023.5.31

- style: 修改波特率可配置

len_adjust_V1.0.1_2023.5.31

- style: 修改波特率可配置

## old version log

V2.0B.07_2023.5.20

- style: 保存到MES的map修改：D4情况保存右边map数据

V2.0B.06_2023.5.19

- style: 已有标签后，保存数据中不查询标签
- feature: 添加步骤执行异常显示

V2.0B.05_2023.5.16

- style: 增加手动点击open 按钮不存储数据的保护
- style: 屏蔽每个流程的日志输出

V2.0B.04_2023.5.16

- style: 异常处理机制修改
  1. 异常进行拆分：测试项异常、交互异常、调节异常、判定异常、MES异常
  2. 前三项异常不会记录数据（本地，MES，件数），MES存储异常不会记录MES数据
  3. 除判定异常外的其他异常，在界面执行步骤显示窗口显示相应错误
- style: 修改移动到目标区域的处理方式
  - 非边缘目标点：直接移动到目标区域
  - 边缘目标点：移动到中心，再移动到边缘（目前只适用于最右边点）

V2.0B.03_2023.4.22

- feature: 监控日志输出
- feature: 增加数据重录功能界面
- fix: 顺拓解析BUG修复
- feature: 增加步骤异常重复执行
- style: sql数据写入方式修改

V2.0B.02_2023.4.25

- fix: mes数据录入BUG修复
- fix: 过程异常窗口显示：未显示具体原因
- style: 保存文件调整，错误信息调整（临时，未完成）

V2.0B.01_2023.4.17
- feature: 增加MES相关信息录入
- feature: 增加版本号判定
- style: 中文界面

V2.0A.04_2023.3.16
- style: 左右上下offset，调节与判定分开
- style: 增加max peak判定最大阈值（超过则仍需往下移动）
- fix: 未清除串口原始缓存数据BUG
- style: 调节判定参数

V2.0A.02_2023.3.16
- fix: 找光斑step one， 初始半径未初始化 BUG
- fix: 找光斑step one，初始半径初始化为0 BUG
- fix: 放气code日志名称错误 BUG
- feat: 增加最后一帧光斑数据日志存储
- feat: 增加原移动距离延迟等待
- refactor: 虚光斑z轴移动步进线性调整

V2.0A.01_2023.3.13
- fix: 空位移移动，无指令发出却一直等待ack BUG
- fix: 找光斑与移动异常->执行test stop函数BUG

V2.09.8_2023.3.2
- fix: 顺拓移动指令bug->先发 位移指令，再发 轴指令
- fix: 关闭再开启clen界面，接收map 数据的bug
- style: 光斑找寻顺序: 内到外
- fix: 源码中增加open 按钮快捷键

V2.09.07_2023.3.1
- feature: 增加clen adjust, open按钮快捷方式 ctrl+空格

V2.09.06_2023.3.1
- fix: 修复xml多节点读问题
- fix: 修改中心peak与四周peak的判定
- fix: xml 写入bug
- fix: 不良率计算问题

V2.09.04_2023.2.26
- fix: 运行时间存储位置bug

V2.09.03_2023.2.25
- feat: 新增计数显示

V2.09.02_2023.2.24
- fix: adjust test BUG
- feat: 增加 CSPC logo
- rafactor: 修改运行日志
- feature: 增加运行时间
- fix：交互BUG

V2.7_2023.2.9
- 增加顺拓设备单脉冲步进，限位范围配置
- 增加调节时默认移动 Z轴 距离的配置
- 增大 调节完毕 peak最大值与历史最大值之间的 阈值

# <center>镜片调节
