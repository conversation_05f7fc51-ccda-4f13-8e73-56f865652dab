#include "BilateralFilter.h"

namespace ImageProcessing {

BilateralFilter::BilateralFilter() {
    // 设置默认参数
    params_.strength = 1.0f;
    params_.enabled = true;
    params_.sigmaColor = 75.0f;
    params_.sigmaSpace = 75.0f;
    params_.kernelSize = 5;
    
    logDebug("BilateralFilter initialized with sigmaColor=75.0, sigmaSpace=75.0, kernelSize=5");
}

bool BilateralFilter::apply(ImageDataU32& data) {
    try {
        validateInput(data);
        
        if (!params_.enabled) {
            logDebug("Filter disabled, skipping processing");
            return true;
        }
        
        logDebug(QString("Applying bilateral filter to %1x%2 image with sigmaColor=%3, sigmaSpace=%4, kernelSize=%5")
                .arg(data.width()).arg(data.height())
                .arg(params_.sigmaColor).arg(params_.sigmaSpace).arg(params_.kernelSize));
        
        // 创建临时图像存储结果
        ImageDataU32 temp(data.width(), data.height());
        
        // 对每个像素应用双边滤波
        for (uint32_t y = 0; y < data.height(); ++y) {
            for (uint32_t x = 0; x < data.width(); ++x) {
                float bilateralResult = applyBilateralAtPixel(data, x, y);
                
                // 应用滤波强度
                float originalValue = static_cast<float>(data.matrix()[y][x]);
                float filteredValue = originalValue + params_.strength * (bilateralResult - originalValue);
                
                temp.matrix()[y][x] = safeFloatToUint32(filteredValue);
            }
        }
        
        // 复制结果回原图像
        data = std::move(temp);
        
        logDebug("Bilateral filter applied successfully");
        return true;
        
    } catch (const ProcessingException& e) {
        qWarning() << "BilateralFilter::apply failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "BilateralFilter::apply failed:" << e.what();
        return false;
    }
}

bool BilateralFilter::apply(const ImageDataU32& src, ImageDataU32& dst) {
    try {
        validateInput(src);
        
        // 确保目标图像尺寸正确
        if (dst.width() != src.width() || dst.height() != src.height()) {
            dst.resize(src.width(), src.height());
        }
        
        // 复制源数据到目标
        dst = src;
        
        // 应用滤波
        return apply(dst);
        
    } catch (const ProcessingException& e) {
        qWarning() << "BilateralFilter::apply (src->dst) failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "BilateralFilter::apply (src->dst) failed:" << e.what();
        return false;
    }
}

void BilateralFilter::setParameters(const FilterParams& params) {
    const BilateralParams* bilateralParams = dynamic_cast<const BilateralParams*>(&params);
    if (!bilateralParams) {
        throw InvalidParameterException("params", "must be BilateralParams type");
    }
    
    validateBilateralParams(*bilateralParams);
    params_ = *bilateralParams;
    
    logDebug("Parameters updated: " + params_.toString());
}

std::unique_ptr<FilterParams> BilateralFilter::getParameters() const {
    return std::make_unique<BilateralParams>(params_);
}

QString BilateralFilter::getAlgorithmName() const {
    return "BilateralFilter";
}

QString BilateralFilter::getDescription() const {
    return "Bilateral filter for edge-preserving smoothing, considers both spatial and color similarity";
}

bool BilateralFilter::isSupported(uint32_t width, uint32_t height) const {
    try {
        ValidationUtils::validatePositive(width, "width");
        ValidationUtils::validatePositive(height, "height");
        
        // 检查图像是否足够大以应用滤波核
        uint32_t minSize = static_cast<uint32_t>(params_.kernelSize);
        return width >= minSize && height >= minSize;
    } catch (const ProcessingException&) {
        return false;
    }
}

uint32_t BilateralFilter::estimateProcessingTime(uint32_t width, uint32_t height) const {
    // 双边滤波计算复杂度较高
    uint64_t totalPixels = static_cast<uint64_t>(width) * height;
    uint64_t kernelOps = static_cast<uint64_t>(params_.kernelSize) * params_.kernelSize;
    // 假设每个双边操作需要0.005毫秒（包含指数计算）
    return static_cast<uint32_t>((totalPixels * kernelOps) / 200);
}

void BilateralFilter::reset() {
    params_.reset();
    logDebug("BilateralFilter reset to default state");
}

QString BilateralFilter::getVersion() const {
    return "1.0.0";
}

bool BilateralFilter::isThreadSafe() const {
    return true; // 无状态操作，线程安全
}

bool BilateralFilter::supportsInPlace() const {
    return false; // 需要临时缓冲区
}

void BilateralFilter::setPreset(const QString& preset) {
    if (preset == "smooth") {
        params_.sigmaColor = 80.0f;
        params_.sigmaSpace = 80.0f;
        params_.kernelSize = 5;
        params_.strength = 1.0f;
    } else if (preset == "detail_preserve") {
        params_.sigmaColor = 50.0f;
        params_.sigmaSpace = 50.0f;
        params_.kernelSize = 5;
        params_.strength = 0.8f;
    } else if (preset == "noise_reduce") {
        params_.sigmaColor = 100.0f;
        params_.sigmaSpace = 100.0f;
        params_.kernelSize = 7;
        params_.strength = 1.0f;
    } else if (preset == "edge_enhance") {
        params_.sigmaColor = 30.0f;
        params_.sigmaSpace = 30.0f;
        params_.kernelSize = 3;
        params_.strength = 0.6f;
    } else {
        throw InvalidParameterException("preset", QString("unsupported preset: %1").arg(preset));
    }
    
    logDebug(QString("Set preset: %1 (sigmaColor=%2, sigmaSpace=%3, kernelSize=%4, strength=%5)")
            .arg(preset).arg(params_.sigmaColor).arg(params_.sigmaSpace)
            .arg(params_.kernelSize).arg(params_.strength));
}

QStringList BilateralFilter::getSupportedPresets() {
    return {"smooth", "detail_preserve", "noise_reduce", "edge_enhance"};
}

float BilateralFilter::applyBilateralAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const {
    float result = 0.0f;
    float weightSum = 0.0f;
    int halfKernel = params_.kernelSize / 2;
    
    uint32_t centerValue = src.matrix()[y][x];
    
    for (int ky = 0; ky < params_.kernelSize; ++ky) {
        for (int kx = 0; kx < params_.kernelSize; ++kx) {
            int srcX = static_cast<int>(x) + kx - halfKernel;
            int srcY = static_cast<int>(y) + ky - halfKernel;
            
            uint32_t pixelValue = getSafePixelValue(src, srcX, srcY);
            
            // 计算空间权重
            int dx = kx - halfKernel;
            int dy = ky - halfKernel;
            float spatialWeight = calculateSpatialWeight(dx, dy, params_.sigmaSpace);
            
            // 计算颜色权重
            float colorDiff = qAbs(static_cast<float>(pixelValue) - static_cast<float>(centerValue));
            float colorWeight = calculateColorWeight(colorDiff, params_.sigmaColor);
            
            // 组合权重
            float totalWeight = spatialWeight * colorWeight;
            
            result += static_cast<float>(pixelValue) * totalWeight;
            weightSum += totalWeight;
        }
    }
    
    return (weightSum > 0.0f) ? (result / weightSum) : static_cast<float>(centerValue);
}

uint32_t BilateralFilter::getSafePixelValue(const ImageDataU32& src, int x, int y) const {
    // 边界处理：镜像扩展
    if (x < 0) x = -x;
    if (y < 0) y = -y;
    if (x >= static_cast<int>(src.width())) x = 2 * (src.width() - 1) - x;
    if (y >= static_cast<int>(src.height())) y = 2 * (src.height() - 1) - y;
    
    // 确保在有效范围内
    x = qBound(0, x, static_cast<int>(src.width() - 1));
    y = qBound(0, y, static_cast<int>(src.height() - 1));
    
    return src.matrix()[y][x];
}

float BilateralFilter::calculateSpatialWeight(int dx, int dy, float sigmaSpace) const {
    float distance = qSqrt(static_cast<float>(dx * dx + dy * dy));
    return qExp(-(distance * distance) / (2.0f * sigmaSpace * sigmaSpace));
}

float BilateralFilter::calculateColorWeight(float colorDiff, float sigmaColor) const {
    return qExp(-(colorDiff * colorDiff) / (2.0f * sigmaColor * sigmaColor));
}

void BilateralFilter::validateBilateralParams(const BilateralParams& params) const {
    params.validate();
}

void BilateralFilter::logDebug(const QString& message) const {
    qDebug() << "[BilateralFilter]" << message;
}

} // namespace ImageProcessing
