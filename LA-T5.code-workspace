{"folders": [{"path": "."}, {"path": "build/Debug/bin/config"}, {"path": "build/MinSizeRel/bin/config"}, {"path": "../../../../101_link-notebook/Obsidian-Vault/KA/Product-development"}], "settings": {"files.associations": {"qdialog": "cpp", "*.tcc": "cpp", "qdir": "cpp", "qprocess": "cpp", "list": "cpp", "qstringlist": "cpp", "qtimerevent": "cpp", "new": "cpp", "qshortcut": "cpp", "qsqldatabase": "cpp", "qapplication": "cpp", "qdebug": "cpp", "qmessagebox": "cpp", "qthread": "cpp", "qcoreapplication": "cpp", "qtimer": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "system_error": "cpp"}}}