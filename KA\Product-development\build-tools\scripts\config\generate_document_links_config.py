#!/usr/bin/env python3
"""
文档关联系统配置生成器

按照单一原则，专门负责生成文档关联系统的配置文件。
遵循用户要求：
1. 项目初始化时可以从公共库模板拷贝
2. 但运行时使用项目本地的config/document_links_config.json
3. 不使用公共库下的配置文件
"""

import os
import json
import argparse
from pathlib import Path


def generate_document_links_config(project_path, project_type="auto_detected", non_interactive=False):
    """
    生成完整的文档关联配置文件

    Args:
        project_path: 项目根目录路径
        project_type: 项目类型(single_layer/multi_level)
        non_interactive: 是否为非交互模式

    Returns:
        bool: 是否成功生成
    """
    try:
        config_dir = Path(project_path) / 'config'
        config_dir.mkdir(exist_ok=True)

        config_file = config_dir / 'document_links_config.json'

        if config_file.exists():
            if non_interactive:
                print(f"[!] 配置文件已存在，非交互模式下直接覆盖: {config_file}")
            else:
                print(f"[!] 配置文件已存在: {config_file}")
                response = input("是否覆盖现有配置文件？(y/N): ")
                if response.lower() != 'y':
                    print("操作已取消")
                    return True
        
        # 完整的项目级配置模板
        project_config = {
            "document_association": {
                "version": "1.0.0",
                "system_type": "semantic_analysis_double_link",
                "project_type": project_type,
                "responsibilities": [
                    "文档注册管理到INDEX.md文件",
                    "语义关联发现通过AI分析", 
                    "双链网络构建使用[[document_name]]格式",
                    "关联关系建议为INDEX表格"
                ],
                "semantic_analysis": {"enabled": True},
                "double_link_system": {"enabled": True},
                "auto_registration": {"enabled": True}
            },
            "document_scanning": {
                "description": "文档扫描配置 - 支持传统配置和Git风格规则",
                "mode": "gitignore_style",
                "comment": "mode可选值: traditional(传统配置) 或 gitignore_style(Git风格规则)",

                "gitignore_rules": [
                    "# Git风格的文档扫描规则 - 类似.gitignore语法",
                    "# 语法说明:",
                    "# - 每行一个模式",
                    "# - # 开头的行是注释",
                    "# - 支持通配符 * 和 **",
                    "# - 使用 ! 前缀来包含被排除的文件",
                    "# - 以 / 结尾表示目录",
                    "",
                    "# ==================== 全局排除规则 ====================",
                    "",
                    "# 系统文件和目录",
                    ".git/",
                    ".vscode/",
                    ".idea/",
                    "__pycache__/",
                    "node_modules/",
                    ".venv/",
                    "build/",
                    "dist/",
                    "target/",
                    "",
                    "# 临时文件",
                    "*.tmp",
                    "*.temp",
                    "*.pyc",
                    "*.pyo",
                    "~$*",
                    ".DS_Store",
                    "Thumbs.db",
                    "*.log",
                    "",
                    "# INDEX文件（由系统自动生成）",
                    "*INDEX.md",
                    "*_INDEX.md",
                    "",
                    "# ==================== 开发目录特殊规则 ====================",
                    "",
                    "# 排除所有源代码文件",
                    "development/**/*.c",
                    "development/**/*.h",
                    "development/**/*.cpp",
                    "development/**/*.py",
                    "development/**/*.js",
                    "development/**/*.java",
                    "",
                    "# 但包含docs目录下的所有文档",
                    "!development/**/docs/**",
                    "",
                    "# 也包含散落的文档文件",
                    "!development/**/*.md",
                    "!development/**/*.txt",
                    "!development/**/*.pdf",
                    "!development/**/*.doc",
                    "!development/**/*.docx"
                ],

                "traditional_config": {
                    "included_file_types": [
                        ".md", ".txt", ".rtf", ".doc", ".docx", ".xls", ".xlsx",
                        ".xlsm", ".ppt", ".pptx", ".pptm", ".vsd", ".vsdx",
                        ".pdf", ".csv", ".xml", ".json"
                    ],
                    "excluded_directories": [
                        ".git", ".vscode", ".idea",
                        "__pycache__", "node_modules", ".venv",
                        "build", "dist", "target",
                        "logs", "temp", "cache", "tmp",
                        ".sass-cache", ".pytest_cache"
                    ],
                    "excluded_files": [
                        "INDEX.md", "_INDEX.md", "README.md", "GUIDE.md",
                        "~$*", ".*", ".DS_Store", "Thumbs.db"
                    ]
                },

                "component_specific": {
                    "说明": "为特定组件设置专门的扫描规则",
                    "DEV": {
                        "description": "开发组件支持白名单扫描模式，只扫描docs目录",
                        "additional_types": [],
                        "additional_excluded_dirs": ["__pycache__", "node_modules", ".gradle", "build", "dist", "target"],
                        "scan_only_subdirectories": {
                            "enabled": True,
                            "description": "启用后，在development目录的项目子目录中只扫描指定的子目录",
                            "directories": ["docs"],
                            "apply_to_depth": 2,
                            "comment": "apply_to_depth=2表示在development/*/子目录中应用此规则"
                        }
                    },
                    "QA": {
                        "description": "质量保证组件可能需要扫描测试相关文件",
                        "additional_types": [".feature", ".spec"],
                        "additional_excluded_dirs": ["test-results", "coverage"]
                    }
                }
            },
            
            "link_discovery": {
                "description": "关联发现配置",
                "semantic_similarity_threshold": 0.1,
                "max_associations": 50,
                "enable_ai_analysis": True
            },
            
            "canvas_integration": {
                "description": "Canvas集成配置", 
                "auto_sync": True,
                "layout_spacing": 700,
                "node_size": {"width": 400, "height": 300}
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(project_config, f, ensure_ascii=False, indent=2)
        
        print(f"[+] 文档关联配置文件已生成: {config_file}")
        print("  - 文档关联系统配置")
        print("  - 文件扫描规则配置")
        print("  - 语义分析和Canvas集成配置")
        print("  - 组件特定扫描规则")
        print("")
        print("[i] 重要说明:")
        print("  1. 运行时系统将使用项目本地的config/document_links_config.json")
        print("  2. 不会使用公共库下的配置文件")
        print("  3. 请根据项目需要调整配置内容")
        
        return True
        
    except Exception as e:
        print(f"[X] 配置文件生成失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='文档关联配置生成器')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='auto_detected',
                       choices=['single_layer', 'multi_level', 'auto_detected'],
                       help='项目类型')
    parser.add_argument('--non-interactive', action='store_true',
                       help='非交互模式，自动覆盖现有配置')

    args = parser.parse_args()

    project_path = Path(args.project_path).resolve()

    print(f"[+] 项目路径: {project_path}")

    if not project_path.exists():
        print(f"[X] 项目路径不存在: {project_path}")
        return 1

    success = generate_document_links_config(project_path, args.project_type, args.non_interactive)
    
    if success:
        print("\n[+] 配置生成完成")
        print(f"\n使用示例:")
        print(f"cd {project_path}")
        print("python ../../scripts/links/auto_link_documents.py --register --all")
        return 0
    else:
        print("\n[X] 配置生成失败")
        return 1


if __name__ == "__main__":
    exit(main()) 