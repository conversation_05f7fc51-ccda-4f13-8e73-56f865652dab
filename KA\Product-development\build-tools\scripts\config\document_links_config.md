# document_links_config

文档关联配置

### 1. 项目初始化时生成配置文件

**方式一：通过项目初始化脚本（推荐）**
```bash
# 初始化新项目时自动生成配置(实际也是调用)
python scripts/init_product_project.py --name my_project --structure single_layer --path /path/to/project
```

**方式二：单独生成配置文件**
```bash
# 在项目根目录执行专门的配置生成脚本
python scripts/config/generate_document_links_config.py --project-path . --project-type single_layer
```

### 2. 自定义配置

编辑项目本地的 `config/document_links_config.json` 文件来满足您的项目需求。

### 重要说明

- ✅ **项目初始化时**：可以从公共库模板拷贝配置
- ✅ **运行时使用**：项目本地的 `config/document_links_config.json` 
- ❌ **不使用**：公共库下的配置文件

## 配置结构详解

### 基础配置（传统模式）

```json
{
  "document_scanning": {
    "mode": "traditional",
    "included_file_types": [
      ".md", ".txt", ".doc", ".docx", ".pdf"
    ],
    "excluded_directories": [
      ".git", ".vscode", "node_modules", "logs"
    ],
    "excluded_files": [
      "INDEX.md", "README.md", "~$*", ".*"
    ]
  }
}
```

### Git风格配置（推荐）

```json
{
  "document_scanning": {
    "description": "文档扫描配置 - 支持传统配置和Git风格规则",
    "mode": "gitignore_style",
    "gitignore_rules": [
      "# 排除所有源代码文件",
      "development/**/*.c",
      "development/**/*.h",
      "development/**/*.py",
      "development/**/*.js",
      "# 但包含docs目录下的所有文档",
      "!development/**/docs/**",
      "!development/**/*.md",
      "# 排除缓存和临时文件",
      "**/__pycache__/**",
      "**/.git/**",
      "**/node_modules/**",
      "# 包含所有文档类型",
      "*.md",
      "*.pdf",
      "*.doc",
      "*.docx"
    ],
    "traditional_config": {
      "included_file_types": [".md", ".txt", ".doc", ".docx", ".pdf"],
      "excluded_directories": [".git", ".vscode", "node_modules", "logs"],
      "excluded_files": ["INDEX.md", "README.md", "~$*", ".*"]
    }
  }
}
```
### 组件特定配置

为不同组件设置特殊的扫描规则：

```json
{
  "document_scanning": {
    "component_specific": {
      "DEV": {
        "description": "开发组件额外扫描代码文件",
        "additional_types": [".py", ".js", ".cpp", ".h"],
        "additional_excluded_dirs": ["__pycache__", "build"]
      },
      "QA": {
        "description": "测试组件扫描测试文件",
        "additional_types": [".feature", ".spec"],
        "additional_excluded_dirs": ["test-results"]
      }
    }
  }
}
```

## 配置选项说明

### 模式选择 (mode)

系统支持两种配置模式：

- **`traditional`**: 传统的包含/排除列表模式
- **`gitignore_style`**: Git风格的忽略规则模式（推荐）

### Git风格规则 (gitignore_rules)

当 `mode` 设置为 `gitignore_style` 时，使用此配置项。支持标准Git忽略语法：

**基本语法：**
- `*.py` - 匹配所有Python文件
- `development/` - 匹配development目录
- `development/**/*.c` - 匹配development目录下所有.c文件
- `!development/**/docs/**` - 排除规则，包含docs目录下的所有文件
- `# 注释` - 注释行

**高级模式：**
- `**` - 匹配任意层级目录
- `*` - 匹配单层文件名
- `!` - 否定规则（包含而非排除）
- `/` - 目录标识符

**实际应用示例：**
```json
"gitignore_rules": [
  "# 排除开发文件但保留文档",
  "development/**/*.c",
  "development/**/*.h",
  "development/**/*.py",
  "!development/**/docs/**",
  "!**/*.md"
]
```

### 传统配置选项

#### included_file_types
指定要扫描的文件扩展名列表。

**支持的格式类型：**
- 文档：`.md`, `.txt`, `.rtf`, `.pdf`
- Office：`.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`
- 数据：`.csv`, `.xml`, `.json`
- 代码：`.py`, `.js`, `.cpp`, `.java` 等

#### excluded_directories
指定要排除的目录名称列表。

**常见排除目录：**
- 版本控制：`.git`, `.svn`
- 编辑器：`.vscode`, `.idea`
- 依赖：`node_modules`, `.venv`
- 构建：`build`, `dist`, `target`
- 缓存：`cache`, `tmp`, `logs`

#### excluded_files
指定要排除的文件模式列表。

**支持通配符模式：**
- `INDEX.md` - 精确匹配
- `~$*` - Office临时文件
- `.*` - 隐藏文件

### component_specific
为特定组件设置额外的规则。

**可用选项：**
- `additional_types` - 额外的文件类型
- `additional_excluded_dirs` - 额外的排除目录

## 使用示例

### 示例1：纯文档项目

```json
{
  "document_scanning": {
    "included_file_types": [
      ".md", ".txt", ".doc", ".docx", ".pdf"
    ],
    "excluded_directories": [
      ".git", "temp", "archive"
    ],
    "excluded_files": [
      "INDEX.md", "~$*"
    ]
  }
}
```

### 示例2：软件开发项目

```json
{
  "document_scanning": {
    "included_file_types": [
      ".md", ".txt", ".pdf"
    ],
    "excluded_directories": [
      ".git", ".vscode", "node_modules", "__pycache__"
    ],
    "excluded_files": [
      "INDEX.md", "README.md", ".*"
    ],
    "component_specific": {
      "DEV": {
        "additional_types": [".py", ".js", ".ts", ".jsx"],
        "additional_excluded_dirs": ["build", "dist", ".next"]
      },
      "QA": {
        "additional_types": [".test.js", ".spec.ts"],
        "additional_excluded_dirs": ["coverage", "test-results"]
      }
    }
  }
}
```

### 示例3：硬件项目

```json
{
  "document_scanning": {
    "included_file_types": [
      ".md", ".doc", ".docx", ".pdf", ".dwg", ".sch"
    ],
    "excluded_directories": [
      ".git", "simulation", "temp"
    ],
    "component_specific": {
      "DES": {
        "additional_types": [".dwg", ".sch", ".pcb"],
        "additional_excluded_dirs": ["backup", "gerber"]
      }
    }
  }
}
```

## 验证配置

运行文档注册时，系统会显示已加载的配置信息：

```bash
python scripts/links/auto_link_documents.py --register --all
```

输出示例：
```
✓ 已加载文档扫描配置: config/document_links_config.json
  - 支持文件类型: 17 种
  - 排除模式: 26 个
```