#ifndef CLENDATAMES_H
#define CLENDATAMES_H

#include <QDockWidget>
#include "clenDataMesOpt.h"
#include "lensReadIni.h"

namespace Ui {
class clenDataMes;
}

class clenDataMes : public QDockWidget
{
    Q_OBJECT

public:
    explicit clenDataMes(QWidget *parent = nullptr);
    ~clenDataMes();

signals:
    void windowCloseSiganl(bool);
    //    void lensAdjustCloseSiganl(bool);


private:
    Ui::clenDataMes *ui;

    NClenMes::StUiConfig *mst_config_ = nullptr;
    CLenDataMesOpt *mc_operation_ = nullptr;
    clenDataMes *mc_lens_data_mes_ = nullptr;

    //* ui config
    void updateConfig(NClenMes::StUiConfig *config_);

    //* 状态栏update
    void resultClean(void);
    void lenMesDataFB(bool closed);

private slots:
    void portListShow(QStringList *port_list, bool port_flag);
    void readyShow(bool is_open);

    void processTaskInfoShow_slot(const bool &is_error, const QString &info);
    void compAckShow(bool is_comp);

private slots:
    void on_reload_clicked();

    void on_portBox_currentIndexChanged(int index);

protected:
    virtual void closeEvent( QCloseEvent * event) override;
};

#endif // CLENDATAMES_H
