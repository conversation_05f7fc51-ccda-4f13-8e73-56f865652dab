#!/usr/bin/env python3
"""
产品项目初始化主控脚本
遵循产品体系构建框架的基本原则，实现一体化、单一来源、内容唯一等原则

该脚本作为主入口，调用各个组件的初始化脚本：
- 目录结构初始化（单层/多层）
- 统一配置文件生成（遵循单一职责原则）
- 项目脚本和文档生成

## 系统协作架构

### 文档关联系统 (@links)
- **职责**: AI语义分析和双链知识管理系统
- **功能**: 
  * 文档注册管理到XX_INDEX.md文件
  * 语义关联发现通过AI分析
  * 双链网络构建使用[[document_name]]格式
  * 关联关系建议为INDEX表格

### 追溯系统 (@infoTrace)  
- **职责**: 完整的块级追溯系统
- **功能**: 
  * 块级内容管理within文档
  * 精确追溯链条从deliverables到原始需求
  * 变更影响分析基于块级关系
  * 关系验证in INDEX文件

### 重要约束
内容追溯关系建立在文档关联基础上。如果两个文档之间不存在关联，
则也必然不会存在块级内容的关联。

### INDEX文件协作
XX_INDEX.md文件的10个字段分工：
- 前4列（文档ID、文档名称、文档路径、文档类型）由文档关联系统管理
- 后6列（块ID、块类型、块标题/描述、追溯来源块ID、关系类型、最后更新时间）由追溯系统管理
"""

import os
import sys
import json
import shutil
import argparse
import subprocess
import runpy
import io
import contextlib
from pathlib import Path
import datetime

# 导入子模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'directory_initialization'))
from init_project_structure import initialize_project_structure

def get_scripts_base_path():
    """获取公共脚本库基础路径"""
    return os.path.dirname(os.path.abspath(__file__))

def run_script_with_subprocess(script_path, args, timeout=60):
    """
    使用subprocess执行脚本，避免嵌套调用问题
    """
    print(f"[DEBUG] 准备执行脚本: {script_path}")
    print(f"[DEBUG] 脚本参数: {args}")

    if not os.path.exists(script_path):
        print(f"[X] 脚本文件不存在: {script_path}")
        return False

    try:
        # 构建命令
        cmd = [sys.executable, script_path]
        if args:
            cmd.extend(args)

        print(f"[DEBUG] 执行命令: {' '.join(cmd)}")

        # 使用subprocess执行
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=os.path.dirname(script_path)
        )

        # 输出结果
        if result.stdout:
            print(f"[OUTPUT] {result.stdout}")
        if result.stderr:
            print(f"[STDERR] {result.stderr}")

        print(f"[DEBUG] 脚本执行完成，返回码: {result.returncode}")
        return result.returncode == 0

    except subprocess.TimeoutExpired:
        print(f"[X] 脚本执行超时({timeout}秒): {script_path}")
        return False
    except Exception as e:
        print(f"[X] 脚本执行异常: {e}")
        return False

def validate_project_path(project_path, force=False):
    """验证项目路径"""
    if os.path.exists(project_path) and os.listdir(project_path):
        if not force:
            # 在MCP环境下，自动继续而不是交互式询问
            print(f"[警告] 目录 {project_path} 已存在且不为空，自动继续...")
        else:
            print(f"[强制模式] 目录 {project_path} 已存在且不为空，继续执行...")

    os.makedirs(project_path, exist_ok=True)
    return os.path.abspath(project_path)

def initialize_directory_structure(project_path, project_name, structure_type, scripts_base):
    """初始化目录结构"""
    print(f"\n[1/3] 初始化{structure_type}目录结构...")

    try:
        if structure_type == "single_layer":
            # 直接调用模块函数，避免runpy嵌套调用
            # 修复：传入父目录路径，避免嵌套目录问题
            parent_path = os.path.dirname(project_path)
            result = initialize_project_structure(
                name=os.path.basename(project_path),
                project_type="full",
                path=parent_path,
                json_output=True  # 使用JSON输出避免打印干扰
            )
            if result["success"]:
                print("[+] 目录结构初始化完成")
                return True
            else:
                print("[X] 目录结构初始化失败")
                return False

        elif structure_type == "multi_level":
            # 多层级结构暂时使用原来的runpy方式（后续可以重构）
            script_path = os.path.join(scripts_base, "directory_initialization", "init_multilevel_structure.py")
            config_path = create_multilevel_config(project_path, project_name)
            cmd = [
                sys.executable, script_path,
                "--config", config_path,
                "--path", os.path.dirname(project_path)
            ]
            script_args = cmd[2:]
            success = run_script_with_subprocess(cmd[1], script_args)
            if success:
                print("[+] 目录结构初始化完成")
                return True
            else:
                print("[X] 目录结构初始化失败")
                return False
        else:
            raise ValueError(f"不支持的结构类型: {structure_type}")

    except Exception as e:
        print(f"[X] 目录结构初始化失败: {e}")
        return False

def create_multilevel_config(project_path, project_name):
    """为多层级结构创建配置文件"""
    config = {
        "product_name": project_name,
        "description": f"{project_name} 多层级产品",
        "levels": [
            {
                "level_id": 1,
                "level_name": "技术平台层",
                "description": "提供通用技术平台和核心组件",
                "instances": ["platform"],
                "parent_mapping": {}
            },
            {
                "level_id": 2,
                "level_name": "应用领域层",
                "description": "基于技术平台的应用领域实现",
                "instances": ["application"],
                "parent_mapping": {
                    "application": "platform"
                }
            }
        ]
    }
    
    config_path = os.path.join(project_path, "level_config.json")
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    return config_path

def run_config_generator(generator_script, project_path, project_type, scripts_base=""):
    """
    运行指定的配置生成器
    
    Args:
        generator_script: 配置生成器脚本路径
        project_path: 项目路径
        project_type: 项目类型
        scripts_base: 脚本基础路径
    
    Returns:
        bool: 是否成功
    """
    try:
        cmd = [
            sys.executable, generator_script,
            "--project-path", str(project_path),
            "--project-type", project_type
        ]
        
        # 某些生成器需要scripts_base参数
        if "workflow" in generator_script and scripts_base:
            cmd.extend(["--scripts-base", scripts_base])
        
        # 使用subprocess替代runpy，避免嵌套调用死锁
        script_args = cmd[2:]  # 移除python和脚本路径，只保留参数
        success = run_script_with_subprocess(cmd[1], script_args)
        if success:
            return True
        else:
            print(f"✗ 运行配置生成器失败: {generator_script}")
            return False
    except Exception as e:
        print(f"✗ 运行配置生成器失败: {generator_script}")
        print(f"    错误: {e}")
        return False

def generate_all_project_configs(project_path, project_type, scripts_base):
    """
    生成所有项目配置文件（整合自generate_all_configs.py）
    
    Args:
        project_path: 项目路径
        project_type: 项目类型
        scripts_base: 脚本基础路径
    
    Returns:
        bool: 是否成功
    """
    print(f"\n[2/3] 生成项目配置文件...")
    
    # 获取配置脚本目录
    config_script_dir = Path(scripts_base) / "config"
    
    # 各配置生成器映射
    generators = {
        "document_links": {
            "script": config_script_dir / "generate_document_links_config.py",
            "description": "文档关联系统配置"
        },
        "workflow": {
            "script": config_script_dir / "generate_workflow_config.py", 
            "description": "工作流系统配置"
        },
        "traceability": {
            "script": config_script_dir / "generate_traceability_config.py",
            "description": "追溯系统配置"
        },
        "deliverables": {
            "script": config_script_dir / "generate_deliverables_config.py",
            "description": "交付物配置"
        },
        "production": {
            "script": config_script_dir / "generate_production_config.py",
            "description": "生产配置"
        },
        "quality": {
            "script": config_script_dir / "generate_quality_config.py",
            "description": "质量保证配置"
        },
        "development": {
            "script": config_script_dir / "generate_development_config.py",
            "description": "开发配置"
        },
        "design": {
            "script": config_script_dir / "generate_design_config.py",
            "description": "设计配置"
        },
        "requirements_analysis": {
            "script": config_script_dir / "generate_requirements_analysis_config.py",
            "description": "需求分析配置"
        },
        "requirements_import": {
            "script": config_script_dir / "generate_requirements_import_config.py",
            "description": "需求导入配置"
        }
    }
    
    success_count = 0
    total_count = len(generators)
    
    print(f"[*] 使用专门的配置生成器生成项目配置...")
    print(f"   项目路径: {project_path}")
    print(f"   项目类型: {project_type}")
    print(f"   配置类型数量: {total_count}")
    print("")
    
    for config_type, generator_info in generators.items():
        script_path = generator_info["script"]
        description = generator_info["description"]
        
        print(f"[-] 生成 {description}...")
        
        if not script_path.exists():
            print(f"[X] 配置生成器不存在: {script_path}")
            continue
        
        success = run_config_generator(str(script_path), project_path, project_type, scripts_base)
        
        if success:
            success_count += 1
            print(f"[+] {description} 生成完成")
        else:
            print(f"[X] {description} 生成失败")
        print("")
    
    # 验证配置文件
    config_dir = Path(project_path) / 'config'
    if config_dir.exists():
        config_files = list(config_dir.glob('*.json'))
        print(f"[#] 配置生成总结:")
        print(f"   成功: {success_count}/{total_count}")
        print(f"   配置文件数: {len(config_files)}")
        print(f"   预期文件数: 10")
        
        if len(config_files) == 10:
            print(f"   状态: [+] 配置文件数量正确")
        else:
            print(f"   状态: [!] 配置文件数量不匹配")
    
    if success_count == total_count:
        print(f"\n[OK] 所有配置文件生成完成！")
        return True
    else:
        print(f"\n[!] 有 {total_count - success_count} 个配置生成失败")
        return False

def create_project_scripts(project_path, scripts_base):
    """创建项目控制脚本"""
    print(f"\n[5/5] 创建项目控制脚本...")
    
    scripts_dir = os.path.join(project_path, 'scripts')
    os.makedirs(scripts_dir, exist_ok=True)
    
    # 主控制脚本
    main_script = f'''#!/usr/bin/env python3
"""
项目主控制脚本 - 调用公共库脚本
"""

import sys
import os
from pathlib import Path

# Add the scripts directory to the Python path
current_dir = Path(__file__).parent.absolute()
scripts_base = current_dir.parent.parent / "scripts"
sys.path.insert(0, str(scripts_base))

try:
    from workflow.workflow_manager import WorkflowManager
    workflow_available = True
except ImportError:
    workflow_available = False
    print("[!] 工作流管理器模块未找到")

def main():
    """主控制函数"""
    if workflow_available:
        try:
            wm = WorkflowManager("config/workflow_config.json")
            if hasattr(wm, 'run_workflow'):
                wm.run_workflow()
            else:
                print("[!] 工作流管理器不支持run_workflow方法")
        except Exception as e:
            print(f"[X] 工作流执行错误: {{e}}")
    else:
        print("[!] 工作流管理器不可用，无法运行工作流")

if __name__ == "__main__":
    main()
'''
    
    with open(os.path.join(scripts_dir, 'run_workflow.py'), 'w', encoding='utf-8') as f:
        f.write(main_script)
    
    # 追溯系统管理脚本
    trace_script = f'''#!/usr/bin/env python3
"""
追溯系统管理脚本 - 调用公共库脚本  
"""

import sys
import os
from pathlib import Path

# Add the scripts directory to the Python path
current_dir = Path(__file__).parent.absolute()
scripts_base = current_dir.parent.parent / "scripts"
sys.path.insert(0, str(scripts_base))

try:
    from infoTrace.traceability_manager import TraceabilityManager
    trace_available = True
except ImportError:
    trace_available = False
    print("[!] 追溯管理器模块未找到")

def main():
    """追溯系统主函数"""
    if trace_available:
        try:
            tm = TraceabilityManager("config/traceability_config.json")
            if hasattr(tm, 'update_tracking_tables'):
                tm.update_tracking_tables()
            if hasattr(tm, 'generate_reports'):
                tm.generate_reports()
            print("[+] 追溯系统更新完成")
        except Exception as e:
            print(f"[X] 追溯系统错误: {{e}}")
    else:
        print("[!] 追溯管理器不可用，无法更新追溯系统")

if __name__ == "__main__":
    main()
'''
    
    with open(os.path.join(scripts_dir, 'manage_traceability.py'), 'w', encoding='utf-8') as f:
        f.write(trace_script)
    
    # 文档关联脚本
    link_script = f'''#!/usr/bin/env python3
"""
文档关联管理脚本 - 调用公共库脚本
"""

import sys
import os
from pathlib import Path

# Add the scripts directory to the Python path
current_dir = Path(__file__).parent.absolute()
scripts_base = current_dir.parent.parent / "scripts"
sys.path.insert(0, str(scripts_base))

try:
    from links.auto_link_documents import DocumentLinker
    linker_available = True
except ImportError:
    linker_available = False
    print("[!] 文档关联器模块未找到")

def main():
    """文档关联主函数"""
    if linker_available:
        try:
            linker = DocumentLinker("config/document_links_config.json")
            if hasattr(linker, 'create_document_links'):
                linker.create_document_links()
            if hasattr(linker, 'generate_link_report'):
                linker.generate_link_report()
            print("[+] 文档关联更新完成")
        except Exception as e:
            print(f"[X] 文档关联错误: {{e}}")
    else:
        print("[!] 文档关联器不可用，无法创建文档关联")

if __name__ == "__main__":
    main()
'''
    
    with open(os.path.join(scripts_dir, 'link_documents.py'), 'w', encoding='utf-8') as f:
        f.write(link_script)
    
    print("[+] 项目控制脚本创建完成")
    return True

def create_readme(project_path, project_name, structure_type):
    """创建项目README文件"""
    readme_content = f'''# {project_name}

## 项目概述

本项目采用产品体系构建框架，遵循一体化、单一来源、内容唯一等基本原则。

### 项目信息

- 项目名称: {project_name}
- 框架类型: {"单层级" if structure_type == "single_layer" else "多层级"}产品目录框架  
- 创建日期: {datetime.datetime.now().strftime("%Y-%m-%d")}
- 版本: 0.1.0
- 架构设计: 模块化组件架构

## 架构特点

### 组件化设计
- **目录结构组件**: 负责项目目录结构初始化
- **工作流组件**: 负责工作流程配置和管理
- **追溯系统组件**: 负责文档追溯和关联管理
- **各组件独立**: 每个组件负责自己的配置文件创建和维护

### 配置文件管理
- `workflow_config.json`: 由工作流组件创建和维护
- `traceability_config.json`: 由追溯系统组件创建和维护
- `document_links_config.json`: 由追溯系统组件创建和维护
- 各组件配置独立管理，便于维护和扩展

## 目录结构

'''
    
    if structure_type == "single_layer":
        readme_content += '''- `product_info/`: 产品基本信息和描述
- `requirements/`: 需求相关文档（需求矩阵、规格说明等）
- `design/`: 设计方案文档（架构设计、详细设计等） 
- `development/`: 开发实施文档（开发计划、代码等）
- `quality/`: 测试验证文档（测试计划、测试报告等）
- `production/`: 生产相关文档（BOM、工艺流程等）
- `deliverables/`: 交付物文档（用户手册、发布包等）
- `project_management/`: 项目管理文档（进度计划、风险管理等）
- `scripts/`: 项目脚本（调用公共库脚本）
- `config/`: 配置文件（工作流、追溯、链接等配置）
- `reports/`: 报告输出（追溯报告、关系图等）

## 追溯系统

本项目采用基于INDEX文件的追溯系统：
- 每个组件目录下有对应的 XX_INDEX.md 文件
- 文档无需严格命名，通过INDEX文件注册ID实现关联
- 支持灵活的文档管理和追溯关系维护
'''
    else:
        readme_content += '''- `level_*_*/`: 各层级实例目录
  - `requirements/`: 需求文档及 REQ_INDEX.md
  - `design/`: 设计文档及 DES_INDEX.md
  - `development/`: 开发文档及 DEV_INDEX.md
  - `level_management/`: 层级管理文档
  - `project_management/`: 项目管理
- `deliverables/`: 交付物目录
- `config/`: 配置文件
- `__level_config.json`: 层级配置文件

## 多层级管理

本项目采用多层级结构，支持：
- 层级间需求追溯
- 层级间资源共享
- 灵活的组件配置
'''
    
    readme_content += f'''
## 快速开始

### 1. 运行工作流

```bash
python scripts/run_workflow.py
```

### 2. 更新文档关联（文档关联系统）

```bash
python scripts/link_documents.py
```

### 3. 更新追溯系统（追溯系统）

```bash
python scripts/manage_traceability.py
```

### 4. 生成块级关系图

```bash
python ../scripts/infoTrace/change_impact.py --project-path . --visualize
```

## 工作流程

本项目遵循以下工作流程：

1. **需求导入** - 从外部系统导入需求
2. **需求分析** - 分解和分析需求 
3. **方案设计** - 生成系统架构和技术方案
4. **开发实施** - 硬件、固件、软件开发
5. **测试验证** - 质量测试和验证
6. **生产准备** - 生产BOM和流程准备
7. **项目输出** - 生成最终交付物

## 双重管理系统

本项目采用文档关联系统和追溯系统协作的双重管理模式：

### 文档关联系统
- **文档注册**: 自动扫描并注册文档到XX_INDEX.md表格
- **语义关联**: 通过AI分析发现文档间的语义关系  
- **双链网络**: 建立[[文档名]]格式的双向链接
- **关联建议**: 为INDEX表格提供关联关系建议

### 追溯系统
- **块级管理**: 管理文档内部的内容块和块级追溯关系
- **精确追溯**: 建立从输出到输入的精确追溯链条
- **变更影响分析**: 基于块级关系分析变更影响范围
- **关系验证**: 验证INDEX中关联关系的有效性

### INDEX文件特点
- 每个组件目录下有对应的 XX_INDEX.md 文件
- 支持文档级和块级两种追溯粒度
- 文档无需严格命名，通过INDEX文件注册ID实现关联
- 两个系统协作维护，确保关联关系的准确性和完整性

## 块级追溯管理

项目支持文档内部的块级精确追溯：

### 内容块识别
- **自动识别**: 标题、列表项、代码块、表格、引用块
- **手动标记**: 使用`<!-- BLOCK_ID: DOC001.XXX.001 -->`标记关键内容
- **块ID格式**: `DOC_ID.BLOCK_TYPE.SEQUENCE`

### 块级引用语法
```markdown
[[REF:REQ001.FUNC.001]]                # 引用需求功能块
[[REF:DES001.ARCH.001:实现]]            # 实现设计架构块
[[REF:DEV001.CODE.001:依赖]]            # 依赖代码实现块
[[REF:QA001.TEST.001:验证]]             # 测试验证块
```

### 追溯粒度
- **文档级追溯**: 文档间的整体关联关系
- **块级追溯**: 文档内部具体内容块的精确追溯
- **变更影响**: 基于块级关系的精确影响分析

## 组件维护

### 工作流组件
- 配置文件: `config/workflow_config.json`
- 维护脚本: `scripts/workflow/init_workflow.py`
- 负责工作流程定义和MCP服务器配置

### 追溯系统组件  
- 配置文件: `config/traceability_config.json`, `config/block_analysis_config.json`
- 维护脚本: `scripts/infoTrace/init_trace.py`, `scripts/infoTrace/auto_index_manager.py`
- 负责块级内容管理和精确追溯，在XX_INDEX.md文件基础上添加块级信息

### 文档关联系统组件
- 配置文件: `config/document_links_config.json`, `config/semantic_analysis_config.json`
- 维护脚本: `scripts/links/init_links.py`, `scripts/links/auto_link_documents.py`
- 负责文档注册和语义关联发现，创建和维护XX_INDEX.md文件

### 目录结构组件
- 维护脚本: `scripts/directory_initialization/`
- 负责项目目录结构初始化

## 系统协作关系

### 重要约束
**内容追溯关系建立在文档关联基础上**。如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

### INDEX文件协作管理
每个组件的XX_INDEX.md文件包含10个字段，由两个系统协作管理：

| 字段 | 责任系统 | 描述 |
|------|---------|------|
| 文档ID | 文档关联系统 | 唯一文档标识符 |
| 文档名称 | 文档关联系统 | 文档显示名称 |
| 文档路径 | 文档关联系统 | 文档文件路径 |
| 文档类型 | 文档关联系统 | 文档分类类型 |
| 块ID | 追溯系统 | 文档内容块标识 |
| 块类型 | 追溯系统 | 内容块分类 |
| 块标题/描述 | 追溯系统 | 内容块描述 |
| 追溯来源块ID | 追溯系统 | 块级追溯来源 |
| 关系类型 | 追溯系统 | 块级关系类型 |
| 最后更新时间 | 两系统共享 | 最后修改时间 |

### 使用流程

1. **文档创建**: 首先由文档关联系统注册文档并创建基础INDEX记录
2. **语义分析**: 文档关联系统分析文档间的语义关联
3. **块级管理**: 追溯系统在已关联文档基础上进行块级内容管理
4. **精确追溯**: 追溯系统建立块级的精确追溯关系
5. **协作维护**: 两系统协作维护INDEX文件的完整性

## 更多信息

详细框架说明请参考：`../产品体系构建框架.md`
'''
    
    with open(os.path.join(project_path, 'README.md'), 'w', encoding='utf-8') as f:
        f.write(readme_content)

def run_document_linking(project_path, scripts_base):
    """运行文档关联脚本"""
    print(f"\n[4/4] 运行文档关联脚本...")

    # 文档关联脚本路径
    link_script_path = os.path.join(scripts_base, "links", "auto_link_documents.py")

    if not os.path.exists(link_script_path):
        print(f"[!] 文档关联脚本不存在: {link_script_path}")
        return False

    try:
        # 运行文档关联脚本（初始化模式）
        cmd = [
            sys.executable, link_script_path,
            "--project-path", project_path,
            "--register", "--all", "--mode", "init"
        ]

        script_args = cmd[2:]  # 移除python和脚本路径，只保留参数
        success = run_script_with_subprocess(cmd[1], script_args)

        if success:
            print("[+] 文档关联脚本执行成功")
            return True
        else:
            print("[!] 文档关联脚本执行失败，但不影响项目初始化")
            return True  # 不阻止项目初始化完成

    except Exception as e:
        print(f"[!] 文档关联脚本执行异常: {e}")
        return True  # 不阻止项目初始化完成

def create_demo_script(project_path, scripts_base):
    """创建演示脚本"""
    demo_script = '''#!/usr/bin/env python3
"""
产品体系构建框架完整演示脚本
展示从项目初始化到文档追溯的完整流程
"""

import os
import sys
import time
from pathlib import Path

# Add scripts path
current_dir = Path(__file__).parent.absolute()
scripts_base = current_dir.parent / "scripts"
sys.path.insert(0, str(scripts_base))

try:
    from workflow.workflow_manager import WorkflowManager
except ImportError:
    WorkflowManager = None

try:
    from infoTrace.traceability_manager import TraceabilityManager
except ImportError:
    TraceabilityManager = None

def print_section(section_title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"  {section_title}")
    print('='*60)

def print_step(step, description):
    """打印步骤信息"""
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def main():
    """主演示函数"""
    print_section("产品体系构建框架完整演示")
    
    print("本演示将展示框架的核心功能：")
    print("1. 工作流管理系统")
    print("2. 信息追溯系统") 
    print("3. 文档关联系统")
    print("4. 报告生成系统")
    
    # 步骤1: 工作流系统演示
    print_step(1, "工作流管理系统演示")
    try:
        if WorkflowManager:
            wm = WorkflowManager("config/workflow_config.json")
            print("[+] 工作流管理器初始化成功")
            
            # 生成工作流报告
            if hasattr(wm, 'generate_workflow_report'):
                wm.generate_workflow_report()
                print("[+] 工作流报告生成完成")
        else:
            print("[!] 工作流管理器模块未找到，跳过演示")
        
    except Exception as e:
        print(f"[X] 工作流系统错误: {e}")
    
    time.sleep(1)
    
    # 步骤2: 信息追溯系统演示
    print_step(2, "信息追溯系统演示")
    try:
        if TraceabilityManager:
            tm = TraceabilityManager("config/traceability_config.json")
            print("[+] 追溯管理器初始化成功")
            
            # 扫描文档
            if hasattr(tm, 'scan_project_documents'):
                documents = tm.scan_project_documents()
                print(f"[+] 扫描到 {len(documents)} 个文档")
            
            # 更新追溯表格
            if hasattr(tm, 'update_tracking_tables'):
                tm.update_tracking_tables()
                print("[+] 追溯表格更新完成")
            
            # 生成追溯报告
            if hasattr(tm, 'generate_reports'):
                tm.generate_reports()
                print("[+] 追溯报告生成完成")
        else:
            print("[!] 追溯管理器模块未找到，跳过演示")
        
    except Exception as e:
        print(f"[X] 追溯系统错误: {e}")
    
    print_section("演示完成")
    print("产品体系构建框架核心功能演示完成！")
    print("\n各组件配置文件由对应组件维护：")
    print("- 工作流配置: workflow组件负责")
    print("- 追溯配置: infoTrace组件负责")
    print("- 目录结构: directory_initialization组件负责")

if __name__ == "__main__":
    main()
'''
    
    with open(os.path.join(project_path, 'demo_complete_framework.py'), 'w', encoding='utf-8') as f:
        f.write(demo_script)

def main():
    parser = argparse.ArgumentParser(description='产品项目初始化主控脚本')
    parser.add_argument('--project_path', required=True, help='项目路径')
    parser.add_argument('--project_name', required=True, help='项目名称')
    parser.add_argument('--structure_type', choices=['single_layer', 'multi_level'], 
                       default='single_layer', help='目录结构类型：single_layer(单层级) 或 multi_level(多层级)')
    parser.add_argument('--scripts_base', help='公共脚本基础路径', 
                       default=get_scripts_base_path())
    
    args = parser.parse_args()
    
    print(f"开始初始化产品项目: {args.project_name}")
    print(f"项目路径: {args.project_path}")
    print(f"结构类型: {args.structure_type}")
    print(f"公共脚本路径: {args.scripts_base}")
    print("架构设计: 模块化组件架构")
    
    # 验证项目路径
    project_path = validate_project_path(args.project_path, force=True)
    
    success = True
    
    # 执行初始化步骤
    try:
        # 1. 初始化目录结构
        if not initialize_directory_structure(project_path, args.project_name, args.structure_type, args.scripts_base):
            success = False
        
        # 2. 生成所有项目配置文件
        if success and not generate_all_project_configs(project_path, args.structure_type, args.scripts_base):
            success = False
        
        # 3. 创建项目控制脚本和文档
        if success:
            create_project_scripts(project_path, args.scripts_base)
            create_readme(project_path, args.project_name, args.structure_type)
            create_demo_script(project_path, args.scripts_base)
            print("[+] 项目文档创建完成")

        # 4. 运行文档关联脚本
        if success:
            run_document_linking(project_path, args.scripts_base)
            print("[+] 文档关联处理完成")
        
    except Exception as e:
        print(f"[X] 初始化过程中发生错误: {str(e)}")
        success = False
    
    if success:
        print(f"\n[OK] 项目 {args.project_name} 初始化完成!")
        print(f"项目路径: {project_path}")
        print(f"结构类型: {args.structure_type}")
        print("架构设计: 模块化组件架构")
        print("\n组件配置文件分布:")
        print("- 工作流配置: 由 workflow 组件创建和维护")
        print("- 追溯配置: 由 infoTrace 组件创建和维护")
        print("- 目录结构: 由 directory_initialization 组件管理")
        print("\n下一步:")
        print("1. 进入项目目录")
        print("2. 运行 python scripts/run_workflow.py 开始工作流")
        print("3. 运行 python demo_complete_framework.py 查看完整演示")
        print("4. 查看 README.md 了解组件架构信息")
    else:
        print(f"\n[FAIL] 项目 {args.project_name} 初始化失败!")
        sys.exit(1)

if __name__ == "__main__":
    main() 