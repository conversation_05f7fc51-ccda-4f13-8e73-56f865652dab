#include "FilterFactory.h"

namespace ImageProcessing {

FilterFactory &FilterFactory::getInstance() {
    static FilterFactory instance;
    return instance;
}

FilterFactory::FilterFactory() {
    initializeDefaultFilters();
    logDebug("FilterFactory initialized");
}

std::unique_ptr<IImageFilter> FilterFactory::createFilter(FilterType type) {
    auto it = creators_.find(type);
    if (it == creators_.end()) {
        throw UnsupportedOperationException(QString("Filter type not supported: %1").arg(static_cast<int>(type)));
    }

    auto filter = it.value()();
    logDebug(QString("Created filter: %1").arg(filter->getAlgorithmName()));
    return filter;
}

QVector<FilterType> FilterFactory::getSupportedTypes() const {
    QVector<FilterType> types;
    for (auto it = creators_.begin(); it != creators_.end(); ++it) {
        types.append(it.key());
    }
    return types;
}

QString FilterFactory::getTypeDescription(FilterType type) const {
    switch (type) {
    case FilterType::Median:
        return "Median filter - effective noise reduction while preserving edges";
    case FilterType::Gaussian:
        return "Gaussian filter - smooth blurring with natural appearance";
    case FilterType::Kalman:
        return "Kalman filter - temporal noise reduction and signal smoothing";
    case FilterType::Bilateral:
        return "Bilateral filter - edge-preserving smoothing";
    case FilterType::Convolution:
        return "Convolution filter - general-purpose filtering with custom kernels";
    case FilterType::WeightedAverage:
        return "Weighted average filter - customizable weight matrices for smoothing";
    default:
        return "Unknown filter type";
    }
}

void FilterFactory::registerFilter(FilterType type, std::function<std::unique_ptr<IImageFilter>()> creator) {
    creators_[type] = creator;
    logDebug(QString("Registered filter type: %1").arg(static_cast<int>(type)));
}

bool FilterFactory::isSupported(FilterType type) const {
    return creators_.contains(type);
}

QString FilterFactory::getVersion() const {
    return "1.0.0";
}

std::unique_ptr<IImageFilter> FilterFactory::createPresetFilter(FilterType type, const QString &preset) {
    auto filter = createFilter(type);
    applyPresetConfiguration(filter.get(), type, preset);

    logDebug(QString("Created preset filter: %1 with preset: %2").arg(filter->getAlgorithmName()).arg(preset));
    return filter;
}

QStringList FilterFactory::getSupportedPresets(FilterType type) const {
    switch (type) {
    case FilterType::Kalman:
        return {"low_noise", "medium_noise", "high_noise", "temporal_smooth"};
    case FilterType::Convolution:
        return {"sharpen", "blur", "edge_detect", "emboss", "identity"};
    case FilterType::Gaussian:
        return {"light_blur", "medium_blur", "heavy_blur"};
    case FilterType::Median:
        return {"noise_reduction", "edge_preserve", "artifact_removal"};
    case FilterType::Bilateral:
        return {"smooth", "detail_preserve", "noise_reduce"};
    case FilterType::WeightedAverage:
        return {"uniform", "gaussian", "center_weighted", "edge_enhance", "smooth"};
    default:
        return {};
    }
}

void FilterFactory::initializeDefaultFilters() {
    // 注册卡尔曼滤波器
    registerFilter(FilterType::Kalman, []() { return std::make_unique<KalmanFilter>(); });

    // 注册卷积滤波器
    registerFilter(FilterType::Convolution, []() { return std::make_unique<ConvolutionFilter>(); });

    // 注册加权均值滤波器
    registerFilter(FilterType::WeightedAverage, []() { return std::make_unique<WeightedAverageFilter>(); });

    // TODO: 注册其他滤波器
    // registerFilter(FilterType::Median, []() {
    //     return std::make_unique<MedianFilter>();
    // });

    // registerFilter(FilterType::Gaussian, []() {
    //     return std::make_unique<GaussianFilter>();
    // });

    // registerFilter(FilterType::Bilateral, []() {
    //     return std::make_unique<BilateralFilter>();
    // });

    logDebug("Default filters initialized");
}

void FilterFactory::applyPresetConfiguration(IImageFilter *filter, FilterType type, const QString &preset) {
    if (!filter)
        return;

    switch (type) {
    case FilterType::Kalman: {
        KalmanParams params;
        if (preset == "low_noise") {
            params.processNoise     = 0.05f;
            params.measurementNoise = 0.05f;
            params.strength         = 0.5f;
        } else if (preset == "medium_noise") {
            params.processNoise     = 0.1f;
            params.measurementNoise = 0.1f;
            params.strength         = 1.0f;
        } else if (preset == "high_noise") {
            params.processNoise     = 0.2f;
            params.measurementNoise = 0.2f;
            params.strength         = 1.5f;
        } else if (preset == "temporal_smooth") {
            params.processNoise     = 0.01f;
            params.measurementNoise = 0.05f;
            params.strength         = 0.8f;
        }
        filter->setParameters(params);
        break;
    }
    case FilterType::Convolution: {
        ConvolutionFilter *convFilter = dynamic_cast<ConvolutionFilter *>(filter);
        if (convFilter) {
            convFilter->setPredefinedKernel(preset);
        }
        break;
    }
    case FilterType::WeightedAverage: {
        WeightedAverageFilter *avgFilter = dynamic_cast<WeightedAverageFilter *>(filter);
        if (avgFilter) {
            avgFilter->setPredefinedWeights(preset);
        }
        break;
    }
    // TODO: 添加其他滤波器的预设配置
    default:
        logDebug(QString("No preset configuration available for filter type: %1").arg(static_cast<int>(type)));
        break;
    }
}

void FilterFactory::logDebug(const QString &message) const {
    qDebug() << "[FilterFactory]" << message;
}

}  // namespace ImageProcessing
