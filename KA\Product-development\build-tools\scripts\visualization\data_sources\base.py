#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化系统数据源基类
定义标准化的数据获取接口
"""

import os
import json
from abc import ABC, abstractmethod
from datetime import datetime


class DataSourceAdapter(ABC):
    """数据源适配器基类
    
    所有数据源适配器都应继承此类并实现其抽象方法
    """
    
    def __init__(self, config=None):
        """初始化数据源适配器
        
        Args:
            config: 配置字典，包含数据源的配置信息
        """
        self.config = config or {}
        self.name = self.config.get("name", "未命名数据源")
        self.type = self.__class__.__name__
        self.last_update = None
        
    @abstractmethod
    def connect(self):
        """连接到数据源
        
        Returns:
            bool: 连接成功返回True，否则返回False
        """
        raise NotImplementedError("子类必须实现connect方法")
        
    @abstractmethod
    def fetch_data(self, params=None):
        """获取数据
        
        Args:
            params: 查询参数字典
            
        Returns:
            获取的数据
        """
        raise NotImplementedError("子类必须实现fetch_data方法")
        
    def get_metadata(self):
        """获取数据源元数据
        
        Returns:
            数据源的元数据字典
        """
        return {
            "name": self.name,
            "type": self.type,
            "capabilities": self.get_capabilities(),
            "last_update": self.last_update.isoformat() if self.last_update else None
        }
        
    def get_capabilities(self):
        """获取数据源能力
        
        返回数据源支持的操作列表
        
        Returns:
            字符串列表，表示数据源支持的操作
        """
        return ["read"]
        
    def validate_config(self):
        """验证配置是否有效
        
        Returns:
            bool: 配置有效返回True，否则返回False
        """
        # 基类实现简单验证，子类可以覆盖此方法进行更详细的验证
        required_fields = self.get_required_config_fields()
        for field in required_fields:
            if field not in self.config:
                return False
        return True
        
    def get_required_config_fields(self):
        """获取必要的配置字段列表
        
        Returns:
            字符串列表，表示必要的配置字段
        """
        return []
        
    def update_last_update_time(self):
        """更新最后更新时间"""
        self.last_update = datetime.now()


class FileBasedDataSource(DataSourceAdapter):
    """基于文件的数据源基类"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.file_path = self.config.get("file_path")
        
    def connect(self):
        """检查文件是否存在且可读"""
        if not self.file_path:
            return False
            
        return os.path.exists(self.file_path) and os.access(self.file_path, os.R_OK)
        
    def get_required_config_fields(self):
        """获取必要的配置字段列表"""
        return ["file_path"]


class ApiBasedDataSource(DataSourceAdapter):
    """基于API的数据源基类"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.api_url = self.config.get("api_url")
        self.api_key = self.config.get("api_key")
        
    def connect(self):
        """测试API连接"""
        # 基类只检查配置是否存在，具体连接测试由子类实现
        return bool(self.api_url)
        
    def get_required_config_fields(self):
        """获取必要的配置字段列表"""
        return ["api_url"]


class DatabaseDataSource(DataSourceAdapter):
    """基于数据库的数据源基类"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.connection_string = self.config.get("connection_string")
        self.db_name = self.config.get("db_name")
        self.connection = None
        
    def connect(self):
        """连接到数据库"""
        # 基类只检查配置是否存在，具体连接由子类实现
        return bool(self.connection_string and self.db_name)
        
    def get_required_config_fields(self):
        """获取必要的配置字段列表"""
        return ["connection_string", "db_name"] 