#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TaskMaster数据提供接口
定义了标准化的数据访问接口，供可视化系统使用
"""

import os
import json
from datetime import datetime


class TaskDataProvider:
    """提供任务数据访问的标准接口，供可视化系统使用"""
    
    def __init__(self, config=None):
        """初始化数据提供者"""
        self.config = config or {}
        self.api_base = self.config.get("api_base", "http://localhost:3001")
        self.api_key = self.config.get("api_key", os.environ.get("TASK_MASTER_API_KEY", ""))
        
    def get_projects(self):
        """获取所有项目"""
        # 实际实现中会调用API获取项目列表
        # 此处为示例实现
        return [
            {"id": "proj-1", "name": "智能产品A", "description": "新一代智能设备"},
            {"id": "proj-2", "name": "消费电子B", "description": "高端消费电子产品"},
            {"id": "proj-3", "name": "企业解决方案C", "description": "企业级应用解决方案"}
        ]
        
    def get_tasks(self, project_id=None, filters=None):
        """获取任务列表，支持过滤"""
        # 实际实现中会调用API获取任务列表
        # 此处为示例实现
        tasks = [
            {
                "id": "TASK-101",
                "title": "实现用户认证模块",
                "description": "开发基于JWT的用户认证系统",
                "status": "in_progress",
                "estimate": 24.0,  # 小时
                "actual": 12.5,
                "assignee": "developer1",
                "project_id": "proj-1",
                "created_at": "2025-05-15T08:30:00Z",
                "updated_at": "2025-05-18T15:45:00Z"
            },
            {
                "id": "TASK-102",
                "title": "设计数据库模型",
                "description": "设计用户和权限相关数据库模型",
                "status": "done",
                "estimate": 8.0,
                "actual": 6.0,
                "assignee": "developer2",
                "project_id": "proj-1",
                "created_at": "2025-05-14T10:15:00Z",
                "updated_at": "2025-05-16T11:30:00Z"
            },
            {
                "id": "TASK-103",
                "title": "实现产品搜索功能",
                "description": "实现基于ElasticSearch的产品搜索",
                "status": "todo",
                "estimate": 16.0,
                "actual": 0.0,
                "assignee": "developer3",
                "project_id": "proj-2",
                "created_at": "2025-05-17T09:00:00Z",
                "updated_at": "2025-05-17T09:00:00Z"
            }
        ]
        
        # 应用过滤条件
        if project_id:
            tasks = [task for task in tasks if task["project_id"] == project_id]
            
        if filters:
            if "status" in filters:
                tasks = [task for task in tasks if task["status"] == filters["status"]]
            if "assignee" in filters:
                tasks = [task for task in tasks if task["assignee"] == filters["assignee"]]
                
        return tasks
        
    def get_task_metrics(self, project_id):
        """获取任务相关指标数据"""
        # 实际实现中会根据数据库计算指标
        # 此处为示例实现
        return {
            "completion_rate": 0.65,  # 完成率
            "estimated_hours": 240.5,  # 估计总工时
            "actual_hours": 180.2,    # 实际已用工时
            "remaining_hours": 60.3,   # 剩余工时
            "tasks_by_status": {      # 各状态任务数量
                "todo": 5,
                "in_progress": 8,
                "review": 3,
                "done": 20
            },
            "velocity": 12.5,  # 每周完成任务量
            "updated_at": datetime.now().isoformat()
        }
    
    def get_task_timeline(self, project_id):
        """获取任务时间线数据"""
        # 实际实现中会从数据库获取历史数据
        # 此处为示例实现
        return {
            "timeline": [
                {
                    "date": "2025-05-10",
                    "metrics": {
                        "todo": 25,
                        "in_progress": 5,
                        "review": 0,
                        "done": 10
                    }
                },
                {
                    "date": "2025-05-15",
                    "metrics": {
                        "todo": 20,
                        "in_progress": 7,
                        "review": 3,
                        "done": 15
                    }
                },
                {
                    "date": "2025-05-20",
                    "metrics": {
                        "todo": 5,
                        "in_progress": 8,
                        "review": 3,
                        "done": 20
                    }
                }
            ],
            "project_id": project_id,
            "updated_at": datetime.now().isoformat()
        }
        
    def export_task_data(self, format="json", destination=None):
        """导出任务数据
        
        Args:
            format: 导出格式，支持json和csv
            destination: 导出目标，如文件路径或URL
        """
        # 收集所有项目的所有任务
        all_tasks = []
        for project in self.get_projects():
            project_tasks = self.get_tasks(project_id=project["id"])
            all_tasks.extend(project_tasks)
            
        # 准备导出数据
        export_data = {
            "tasks": all_tasks,
            "metadata": {
                "source": "task_master",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0"
            }
        }
        
        # 根据格式导出
        if format == "json":
            if destination:
                with open(destination, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
            return export_data
        elif format == "csv":
            # 实际实现中需要转换为CSV格式
            # 此处省略实现
            pass
            
        return export_data


# 使用示例
if __name__ == "__main__":
    provider = TaskDataProvider()
    
    # 导出数据到文件
    provider.export_task_data(
        format="json",
        destination="/shared/data/tasks_export.json"
    )
    
    print("任务数据导出完成") 