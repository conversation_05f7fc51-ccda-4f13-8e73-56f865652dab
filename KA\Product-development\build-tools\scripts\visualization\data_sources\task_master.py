#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Task Master数据源适配器
用于从Task Master获取任务数据
"""

import os
import json
import requests
from datetime import datetime

from .base import ApiBasedDataSource


class TaskMasterDataSource(ApiBasedDataSource):
    """Task Master数据源适配器"""
    
    def __init__(self, config=None):
        """初始化Task Master数据源
        
        Args:
            config: 配置字典，包含api_url和api_key等
        """
        super().__init__(config)
        self.refresh_interval = self.config.get("refresh_interval", 300)  # 默认5分钟刷新一次
        self.cache = {}  # 用于缓存数据
        self.cache_time = {}  # 用于记录缓存时间
        
    def connect(self):
        """测试与Task Master的连接"""
        try:
            response = requests.get(
                f"{self.api_url}/api/status",
                headers=self._get_headers(),
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"连接Task Master失败: {e}")
            return False
            
    def _get_headers(self):
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
    def fetch_data(self, params=None):
        """获取Task Master数据
        
        Args:
            params: 查询参数
                endpoint: API端点
                project_id: 项目ID
                filters: 过滤条件
                
        Returns:
            获取的数据
        """
        params = params or {}
        endpoint = params.get("endpoint", "tasks")
        
        # 检查缓存
        cache_key = self._get_cache_key(params)
        if cache_key in self.cache:
            cache_time = self.cache_time.get(cache_key)
            if cache_time and (datetime.now() - cache_time).total_seconds() < self.refresh_interval:
                return self.cache[cache_key]
        
        # 构建API URL
        url = f"{self.api_url}/api/{endpoint}"
        
        # 添加查询参数
        query_params = {}
        if "project_id" in params:
            query_params["project_id"] = params["project_id"]
        if "filters" in params:
            for k, v in params["filters"].items():
                query_params[k] = v
                
        try:
            response = requests.get(
                url,
                headers=self._get_headers(),
                params=query_params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            
            # 更新缓存
            self.cache[cache_key] = data
            self.cache_time[cache_key] = datetime.now()
            self.update_last_update_time()
            
            return data
        except Exception as e:
            print(f"从Task Master获取数据失败: {e}")
            # 如果请求失败但缓存存在，则返回缓存数据
            if cache_key in self.cache:
                return self.cache[cache_key]
            return None
            
    def _get_cache_key(self, params):
        """根据查询参数生成缓存键"""
        # 将params字典转换为排序后的字符串，用作缓存键
        sorted_items = sorted(params.items()) if isinstance(params, dict) else []
        params_str = json.dumps(sorted_items)
        return f"taskmaster_{hash(params_str)}"
        
    def get_projects(self):
        """获取项目列表"""
        return self.fetch_data({"endpoint": "projects"})
        
    def get_tasks(self, project_id=None, filters=None):
        """获取任务列表"""
        params = {
            "endpoint": "tasks",
            "project_id": project_id
        }
        if filters:
            params["filters"] = filters
            
        return self.fetch_data(params)
        
    def get_task_metrics(self, project_id):
        """获取任务指标数据"""
        params = {
            "endpoint": "metrics",
            "project_id": project_id
        }
        return self.fetch_data(params)
        
    def get_task_timeline(self, project_id):
        """获取任务时间线数据"""
        params = {
            "endpoint": "timeline",
            "project_id": project_id
        }
        return self.fetch_data(params)
        
    def get_capabilities(self):
        """获取数据源能力"""
        return ["read", "metrics", "timeline"]
        
    def get_required_config_fields(self):
        """获取必要的配置字段列表"""
        return ["api_url", "api_key"]
        
    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()
        self.cache_time.clear()


# 文件数据源版本（从导出文件读取）
class TaskMasterFileDataSource(TaskMasterDataSource):
    """从文件读取Task Master数据的数据源适配器"""
    
    def __init__(self, config=None):
        """初始化基于文件的Task Master数据源
        
        Args:
            config: 配置字典，包含data_file路径
        """
        super().__init__(config)
        self.data_file = self.config.get("data_file", "/shared/data/tasks_export.json")
        
    def connect(self):
        """检查文件是否存在且可读"""
        return os.path.exists(self.data_file) and os.access(self.data_file, os.R_OK)
        
    def fetch_data(self, params=None):
        """从文件获取数据
        
        Args:
            params: 查询参数
                
        Returns:
            从文件加载的数据
        """
        params = params or {}
        
        # 检查缓存
        cache_key = self._get_cache_key(params)
        if cache_key in self.cache:
            cache_time = self.cache_time.get(cache_key)
            if cache_time:
                # 检查文件是否比缓存更新
                file_mtime = os.path.getmtime(self.data_file)
                file_mtime_dt = datetime.fromtimestamp(file_mtime)
                if file_mtime_dt <= cache_time:
                    return self.cache[cache_key]
        
        try:
            # 从文件加载全部数据
            with open(self.data_file, 'r', encoding='utf-8') as f:
                all_data = json.load(f)
                
            # 根据endpoint处理数据
            endpoint = params.get("endpoint", "tasks")
            if endpoint == "tasks":
                data = all_data.get("tasks", [])
                
                # 应用过滤条件
                if "project_id" in params:
                    data = [task for task in data if task.get("project_id") == params["project_id"]]
                if "filters" in params:
                    filters = params["filters"]
                    for key, value in filters.items():
                        data = [task for task in data if task.get(key) == value]
                        
                result = data
            elif endpoint == "projects":
                # 从任务中提取不重复的项目ID
                projects = {}
                for task in all_data.get("tasks", []):
                    project_id = task.get("project_id")
                    if project_id and project_id not in projects:
                        projects[project_id] = {
                            "id": project_id,
                            "name": f"Project {project_id}"  # 文件中可能没有项目详情
                        }
                result = list(projects.values())
            elif endpoint == "metrics":
                # 实现简单的指标计算
                project_id = params.get("project_id")
                tasks = all_data.get("tasks", [])
                if project_id:
                    tasks = [task for task in tasks if task.get("project_id") == project_id]
                    
                # 计算各种指标
                total_tasks = len(tasks)
                completed_tasks = len([t for t in tasks if t.get("status") == "done"])
                estimated_hours = sum(t.get("estimate", 0) for t in tasks)
                actual_hours = sum(t.get("actual", 0) for t in tasks)
                
                # 按状态统计任务数量
                tasks_by_status = {}
                for task in tasks:
                    status = task.get("status", "unknown")
                    tasks_by_status[status] = tasks_by_status.get(status, 0) + 1
                    
                result = {
                    "completion_rate": completed_tasks / total_tasks if total_tasks > 0 else 0,
                    "estimated_hours": estimated_hours,
                    "actual_hours": actual_hours,
                    "remaining_hours": estimated_hours - actual_hours,
                    "tasks_by_status": tasks_by_status,
                    "total_tasks": total_tasks
                }
            else:
                result = all_data
                
            # 更新缓存
            self.cache[cache_key] = result
            self.cache_time[cache_key] = datetime.now()
            self.update_last_update_time()
            
            return result
        except Exception as e:
            print(f"从文件加载Task Master数据失败: {e}")
            # 如果加载失败但缓存存在，则返回缓存数据
            if cache_key in self.cache:
                return self.cache[cache_key]
            return None
            
    def get_required_config_fields(self):
        """获取必要的配置字段列表"""
        return ["data_file"] 