# 多层级产品文档架构使用指南

本文档提供多层级产品文档架构的具体使用方法和操作步骤，帮助您快速创建和管理多层级产品结构。

## 准备工作

1. 确保您已安装Python 3.6或更高版本
2. 确保脚本目录已添加到环境变量，或准备使用完整路径调用脚本
3. 确保您已了解多层级产品框架的基本概念（参见`multilevel_product_guide.md`）

## 基本命令

### 初始化多层级产品结构

```bash
python scripts/project_initialization/init_multilevel_structure.py --config="your_config.json"
```

参数说明：

- `--config`：配置文件路径，包含层级结构定义
- `--path`（可选）：项目创建路径，默认为当前目录

### 修改多层级产品结构

添加新层级实例：

```bash
python scripts/project_initialization/modify_multilevel_product.py --config="level_config.json" --action="add_instance" --level="2" --name="新系列名称"
```

添加新层级：

```bash
python scripts/project_initialization/modify_multilevel_product.py --config="level_config.json" --action="add_level" --level="4" --name="新层级名称"
```

添加客户实例：

```bash
python scripts/project_initialization/modify_multilevel_product.py --config="level_config.json" --action="add_instance" --level="4" --name="新客户名称" --parent="产品名称"
```

更新层级关系：

```bash
python scripts/project_initialization/modify_multilevel_product.py --config="level_config.json" --action="update_relation" --child="客户名称" --parent="产品名称"
```

## 配置文件格式

多层级产品框架使用JSON格式的配置文件定义产品结构。配置文件应包含以下内容：

```json
{
  "product_name": "产品名称",
  "description": "产品描述",
  "levels": [
    {
      "level_id": 1,
      "level_name": "层级名称",
      "instances": ["实例1", "实例2"],
      "description": "层级描述"
    },
    {
      "level_id": 2,
      "level_name": "层级名称",
      "instances": ["实例1", "实例2"],
      "parent_mapping": {
        "实例1": "上级实例名",
        "实例2": "上级实例名"
      },
      "description": "层级描述"
    }
  ]
}
```

关键字段说明：

- `product_name`：产品名称
- `description`：产品描述
- `levels`：层级定义数组
  - `level_id`：层级ID（从1开始顺序编号）
  - `level_name`：层级名称
  - `instances`：该层级的实例列表
  - `parent_mapping`：（非顶层必需）子实例与父实例的映射关系
  - `description`：层级描述

## 使用VSCode任务

为简化操作，可以使用VSCode任务面板执行多层级产品管理功能：

1. 按`Ctrl+Shift+P`打开命令面板
2. 输入"Tasks: Run Task"并选择
3. 选择以下相关任务：
   - "初始化多层级产品" - 从配置文件创建新项目
   - "使用DTOF传感器示例配置" - 使用预设示例配置
   - "添加多层级实例" - 添加新的实例
   - "添加多层级层级" - 添加新的层级

## 实例演示

### 示例1：创建DTOF激光测距传感器系统

#### 1. 创建配置文件 `dtof_sensor_config.json`

```json
{
  "product_name": "激光测距传感器系统",
  "description": "基于DTOF技术的多场景激光测距解决方案",
  "levels": [
    {
      "level_id": 1,
      "level_name": "技术平台层",
      "instances": ["DTOF核心平台"],
      "description": "基础DTOF传感技术平台，共享核心测距算法和基础硬件设计"
    },
    {
      "level_id": 2,
      "level_name": "应用领域层",
      "instances": ["距离测量领域", "人体感应领域"],
      "description": "不同应用领域的传感器技术方案"
    },
    {
      "level_id": 3,
      "level_name": "产品型号层",
      "instances": ["测身高模块", "马桶脚感", "水龙头感应"],
      "parent_mapping": {
        "测身高模块": "距离测量领域",
        "马桶脚感": "人体感应领域",
        "水龙头感应": "人体感应领域"
      },
      "description": "具体产品实现，对应特定应用场景"
    },
    {
      "level_id": 4,
      "level_name": "客户定制层",
      "instances": ["客户A", "客户B", "客户C"],
      "parent_mapping": {
        "客户A": "马桶脚感",
        "客户B": "马桶脚感",
        "客户C": "水龙头感应"
      },
      "description": "针对特定客户的定制化实现"
    }
  ]
}
```

#### 2. 执行初始化命令

```bash
python scripts/project_initialization/init_multilevel_structure.py --config="dtof_sensor_config.json"
```

#### 3. 生成的目录结构

执行上述命令后，将生成完整的目录结构，包括：

- 顶层配置文件 `__level_config.json`
- 各层级目录（技术平台层、应用领域层、产品型号层、客户定制层）
- 每个层级目录下的功能子目录（requirements、design、development等）
- 中间层级的层级管理文件（level_links.md）用于维护层级关系
- 交付物目录（deliverables）用于存放最终产品资料

### 示例2：简化的两层结构

#### 1. 创建配置文件 `software_config.json`

```json
{
  "product_name": "分析软件套件",
  "description": "数据分析软件套件",
  "levels": [
    {
      "level_id": 1,
      "level_name": "核心平台",
      "instances": ["分析引擎平台"],
      "description": "核心分析引擎"
    },
    {
      "level_id": 2,
      "level_name": "行业解决方案",
      "instances": ["金融解决方案", "医疗解决方案", "零售解决方案"],
      "description": "面向不同行业的解决方案"
    }
  ]
}
```

#### 2. 执行初始化命令

```bash
python scripts/project_initialization/init_multilevel_structure.py --config="software_config.json"
```

## 增量更新项目结构

### 1. 为现有项目添加新的客户

如果您需要为已有项目添加新的客户实例，可以使用以下命令：

```bash
# 添加新客户实例
python scripts/project_initialization/modify_multilevel_product.py --config="__level_config.json" --action="add_instance" --level="4" --name="客户D" --parent="马桶脚感"
```

### 2. 为现有项目添加新的产品

如果您需要添加新的产品型号，可以使用以下命令：

```bash
# 添加新产品实例
python scripts/project_initialization/modify_multilevel_product.py --config="__level_config.json" --action="add_instance" --level="3" --name="新产品" --parent="应用领域名称"
```

### 3. 添加新的层级

如果您的项目初始没有客户层，但后续需要添加，可以使用以下命令：

```bash
# 添加新层级
python scripts/project_initialization/modify_multilevel_product.py --config="__level_config.json" --action="add_level" --level="4" --name="客户定制层"

# 添加客户实例
python scripts/project_initialization/modify_multilevel_product.py --config="__level_config.json" --action="add_instance" --level="4" --name="客户A" --parent="产品名称"
```

## 需求追溯矩阵使用指南

需求追溯矩阵是多层级框架的核心文件，以下是正确使用方法：

### 1. 编写顶层需求

在顶层（如技术平台层）的需求矩阵中，创建基础需求项：

```markdown
# 技术平台层需求追溯矩阵

| 需求ID | 需求描述 | 状态 | 下层关联 |
|--------|---------|------|----------|
| L1_R001 | DTOF测距精度需达到±5mm | 已实现 | [L2_R002](#), [L2_R003](#) |
| L1_R002 | 功耗标准不超过1W | 进行中 | [L2_R005](#) |
```

### 2. 编写中间层需求

在中间层（如应用领域层）的需求矩阵中，引用上层需求并添加本层特定需求：

```markdown
# 距离测量领域需求追溯矩阵

| 需求ID | 上层需求 | 需求描述 | 状态 | 下层关联 |
|--------|---------|---------|------|----------|
| L2_R001 | - | 测量范围10cm-2m | 已实现 | [L3_R001](#) |
| L2_R002 | [L1_R001](#) | 近距离精度提升至±3mm | 进行中 | [L3_R002](#) |
```

### 3. 编写产品层需求

在产品层的需求矩阵中，进一步细化需求：

```markdown
# 测身高模块需求追溯矩阵

| 需求ID | 上层需求 | 需求描述 | 状态 | 下层关联 |
|--------|---------|---------|------|----------|
| L3_R001 | [L2_R001](#) | 测量高度范围80cm-220cm | 已实现 | [L4_R001](#) |
| L3_R002 | [L2_R002](#) | 测量精度±2mm | 进行中 | [L4_R002](#) |
```

### 4. 编写客户层需求

客户层需求矩阵包含最终的具体实现需求：

```markdown
# 客户A需求追溯矩阵

| 需求ID | 上层需求 | 需求描述 | 状态 | 测试标准 |
|--------|---------|---------|------|----------|
| L4_R001 | [L3_R001](#) | 测量高度下限调整为75cm | 已实现 | TS001 |
| L4_R002 | [L3_R002](#) | 显示单位可切换（cm/inch） | 进行中 | TS002 |
```

## 客户层特有文件管理

在客户层目录中，有一些特殊的子目录用于管理客户特有资源：

1. **硬件定制**
   - `design/structural_customization/` - 存放客户特定结构设计文件
   - `development/hardware_customization/` - 存放客户特定硬件修改资料

2. **品牌资产**
   - `production/branding_assets/` - 存放客户品牌相关资源（如LOGO、标识等）
   - `production/customer_packaging/` - 存放客户特定包装设计和要求

3. **测试规范**
   - `quality/customer_test_specs/` - 存放客户特定的验收测试标准
   - `quality/certification_requirements/` - 存放客户要求的特殊认证文件

4. **定制配置**
   - `development/configs/` - 存放客户特定的配置文件和参数设置

## 最佳实践

1. **文件命名规范**
   - 文件名应包含层级标识，例如`L1_design_spec.md`
   - 使用统一的前缀表示不同层级，如L1_、L2_、L3_、L4_

2. **目录结构维护**
   - 避免在生成的目录结构中随意添加非标准目录
   - 如需添加目录，考虑修改配置文件并重新生成

3. **版本管理**
   - 每个层级的关键文件应进行版本控制
   - 配置文件变更时记录变更历史

4. **需求追溯**
   - 定期更新需求追溯矩阵，保持上下层需求的链接有效
   - 明确标记需求状态变更

5. **资源共享**
   - 上层组件的使用应通过引用而非复制
   - 更新上层组件时，评估对所有下层的影响

## 常见问题

1. **Q: 如何处理跨层级的共享资源？**  
   A: 共享资源应放在最高的公共层级，并使用相对路径引用。

2. **Q: 如何管理客户层的特殊需求？**  
   A: 客户特殊需求应记录在客户层的需求矩阵中，并明确链接到上层产品需求。

3. **Q: 多层级结构中如何处理版本迭代？**  
   A: 每层有独立的版本号，上层版本变更时应评估对下层的影响并更新下层版本。

4. **Q: 如何避免层级间职责混淆？**  
   A: 严格遵循层级职责定义，确保每个需求和组件只在适当的层级开发和维护。

## 项目实践注意事项

1. 设计层级结构时注意层级职责明确，避免过多层级导致管理复杂
2. 确保每个层级的需求追溯矩阵定期更新，保持需求可追溯性
3. 上层组件修改需评估对所有下层的影响
4. 避免在下层修改上层组件，应通过配置或扩展实现功能
5. 文档和资源命名应包含层级信息，确保唯一性
6. 客户层应尽量通过配置而非修改实现定制需求，保持核心功能的一致性

## 可视化多层级产品结构

多层级产品框架提供了可视化工具，帮助您直观地查看和理解产品结构。这对于团队协作、项目演示和文档编制特别有用。

### 使用可视化工具

可以通过以下命令生成产品结构图：

```bash
python scripts/project_initialization/visualize_product_structure.py --config="__level_config.json" --output="product_structure.png"
```

您也可以通过VSCode任务面板使用可视化功能：

1. 按`Ctrl+Shift+P`打开命令面板
2. 输入"Tasks: Run Task"并选择
3. 选择以下相关任务：
   - "生成产品结构图" - 生成基本产品结构图
   - "生成高亮产品结构图" - 生成高亮特定实例的结构图
   - "生成矢量格式产品结构图" - 生成SVG格式的结构图
   - "一键生成产品结构图与需求矩阵" - 同时生成结构图和需求矩阵摘要

### 可视化定制选项

可视化工具支持多种定制选项：

- **输出格式**：支持PNG、SVG和PDF格式
- **主题风格**：提供default、light、dark和colorful四种主题
- **高亮功能**：可突出显示特定产品实例
- **描述显示**：可选择显示产品实例的详细描述

详细的可视化工具使用说明请参考 [product_visualization_guide.md](../../product_visualization_guide.md)。

### 可视化最佳实践

1. **项目初始化**：在项目初始化后立即生成结构图，并纳入项目文档
2. **结构演示**：在团队讨论时使用结构图解释产品架构
3. **进度报告**：结合需求矩阵，在进度报告中使用结构图展示开发状态
4. **客户演示**：使用高亮功能在客户演示中强调特定产品路线
