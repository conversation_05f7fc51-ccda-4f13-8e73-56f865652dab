#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Canvas显示配置生成器
用于生成Canvas文档显示模式的配置文件
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any

class CanvasDisplayConfigGenerator:
    """Canvas显示配置生成器"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
        self.config_dir = self.project_path / "config"
        self.config_file = self.config_dir / "canvas_display_config.json"
        
    def generate_default_config(self) -> Dict[str, Any]:
        """
        生成默认的Canvas显示配置
        
        Returns:
            Dict: 默认配置
        """
        return {
            "version": "1.0",
            "description": "Canvas文档显示模式配置",
            "global_settings": {
                "max_cards_per_group": 15,
                "max_total_cards": 50,
                "default_display_mode": "auto",
                "enable_importance_scoring": True
            },
            "node_sizes": {
                "card": {
                    "width": 400,
                    "height": 280
                },
                "link": {
                    "width": 200,
                    "height": 60
                },
                "collapsed_group": {
                    "width": 300,
                    "height": 100
                }
            },
            "importance_scoring": {
                "file_type_weights": {
                    "README.md": 3,
                    "设计文档": 3,
                    "需求文档": 2,
                    "API文档": 2,
                    "代码文件": 1,
                    "配置文件": 1,
                    "日志文件": 0,
                    "临时文件": 0
                },
                "modification_time_weights": {
                    "recent_7_days": 2,
                    "recent_30_days": 1,
                    "older_than_30_days": 0
                },
                "file_size_weights": {
                    "optimal_size": 1,  # 1KB-1MB
                    "too_small_or_large": 0  # <1KB or >10MB
                },
                "path_depth_weights": {
                    "root_or_first_level": 2,
                    "second_level": 1,
                    "third_level_or_deeper": 0
                }
            },
            "display_thresholds": {
                "always_card": 8,
                "prefer_card": 5,
                "prefer_link": 3,
                "always_collapsed": 1
            },
            "component_rules": {
                "REQ": {
                    "display_mode": "mixed",
                    "max_cards": 10,
                    "collapse_threshold": 20
                },
                "DES": {
                    "display_mode": "mixed",
                    "max_cards": 15,
                    "collapse_threshold": 30
                },
                "DEV": {
                    "display_mode": "link_priority",
                    "max_cards": 8,
                    "collapse_threshold": 25
                },
                "QA": {
                    "display_mode": "card_priority",
                    "max_cards": 12,
                    "collapse_threshold": 15
                },
                "PROD": {
                    "display_mode": "mixed",
                    "max_cards": 10,
                    "collapse_threshold": 20
                },
                "PM": {
                    "display_mode": "card_priority",
                    "max_cards": 8,
                    "collapse_threshold": 12
                },
                "DEL": {
                    "display_mode": "mixed",
                    "max_cards": 10,
                    "collapse_threshold": 18
                }
            },
            "project_group_rules": {
                "small_group": {
                    "max_documents": 10,
                    "default_mode": "all_cards"
                },
                "medium_group": {
                    "max_documents": 30,
                    "default_mode": "mixed"
                },
                "large_group": {
                    "max_documents": 999,
                    "default_mode": "link_priority"
                }
            },
            "file_pattern_rules": [
                {
                    "pattern": "README.*",
                    "display_mode": "card",
                    "importance_bonus": 3
                },
                {
                    "pattern": ".*\\.md$",
                    "display_mode": "card",
                    "importance_bonus": 1
                },
                {
                    "pattern": ".*\\.pdf$",
                    "display_mode": "link",
                    "importance_bonus": 0
                },
                {
                    "pattern": ".*\\.log$",
                    "display_mode": "collapsed",
                    "importance_bonus": -2
                },
                {
                    "pattern": "temp_.*",
                    "display_mode": "collapsed",
                    "importance_bonus": -3
                }
            ],
            "custom_overrides": {
                "force_card": [],
                "force_link": [],
                "force_collapsed": [],
                "exclude_from_canvas": []
            }
        }
    
    def create_config_file(self, custom_config: Dict = None) -> bool:
        """
        创建配置文件
        
        Args:
            custom_config: 自定义配置（可选）
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            # 确保配置目录存在
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成配置
            if custom_config:
                config = custom_config
            else:
                config = self.generate_default_config()
            
            # 写入配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✓ Canvas显示配置文件已创建: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"✗ 创建配置文件失败: {e}")
            return False
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            Dict: 配置内容
        """
        try:
            if not self.config_file.exists():
                print(f"配置文件不存在，创建默认配置: {self.config_file}")
                self.create_config_file()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.generate_default_config()
    
    def update_config(self, updates: Dict[str, Any]) -> bool:
        """
        更新配置文件
        
        Args:
            updates: 要更新的配置项
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            config = self.load_config()
            
            # 深度合并配置
            def deep_merge(base: Dict, updates: Dict) -> Dict:
                for key, value in updates.items():
                    if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                        deep_merge(base[key], value)
                    else:
                        base[key] = value
                return base
            
            updated_config = deep_merge(config, updates)
            
            # 写回文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(updated_config, f, ensure_ascii=False, indent=2)
            
            print(f"✓ 配置文件已更新: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"✗ 更新配置文件失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Canvas显示配置生成器")
    parser.add_argument("--project-path", default=".", help="项目路径")
    parser.add_argument("--create", action="store_true", help="创建默认配置文件")
    parser.add_argument("--show", action="store_true", help="显示当前配置")
    
    args = parser.parse_args()
    
    generator = CanvasDisplayConfigGenerator(args.project_path)
    
    if args.create:
        generator.create_config_file()
    elif args.show:
        config = generator.load_config()
        print(json.dumps(config, ensure_ascii=False, indent=2))
    else:
        print("请指定操作: --create 或 --show")

if __name__ == "__main__":
    main()
