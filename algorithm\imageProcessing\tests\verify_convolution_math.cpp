#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include <cmath>

/**
 * @brief 验证卷积滤波器的数学计算过程
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "========================================";
    qDebug() << "卷积滤波器数学计算验证";
    qDebug() << "========================================";
    
    // 5x5测试数据
    int testData[5][5] = {
        {271, 882, 826, 748, 58},
        {1011, 908, 792, 756, 738},
        {1074, 924, 807, 800, 859},
        {1021, 877, 777, 776, 855},
        {145, 887, 788, 740, 33}
    };
    
    // 3x3锐化核
    float sharpenKernel[3][3] = {
        {0, -1, 0},
        {-1, 5, -1},
        {0, -1, 0}
    };
    
    qDebug() << "\n=== 步骤1：计算核的绝对值总和 ===";
    float absSum = 0.0f;
    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; ++j) {
            absSum += std::abs(sharpenKernel[i][j]);
            qDebug() << QString("abs(%1) = %2").arg(sharpenKernel[i][j]).arg(std::abs(sharpenKernel[i][j]));
        }
    }
    qDebug() << QString("绝对值总和: %1").arg(absSum);
    
    qDebug() << "\n=== 步骤2：归一化核 ===";
    float normalizedKernel[3][3];
    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; ++j) {
            normalizedKernel[i][j] = sharpenKernel[i][j] / absSum;
            qDebug() << QString("normalized[%1][%2] = %3/%4 = %5")
                        .arg(i).arg(j)
                        .arg(sharpenKernel[i][j])
                        .arg(absSum)
                        .arg(normalizedKernel[i][j], 0, 'f', 6);
        }
    }
    
    qDebug() << "\n=== 步骤3：提取中心点(2,2)的3x3邻域 ===";
    int centerX = 2, centerY = 2;
    int neighborhood[3][3];
    
    // 镜像边界处理
    for (int ky = 0; ky < 3; ++ky) {
        for (int kx = 0; kx < 3; ++kx) {
            int srcX = centerX + kx - 1;  // -1 是因为核的中心在(1,1)
            int srcY = centerY + ky - 1;
            
            // 镜像边界处理
            if (srcX < 0) srcX = -srcX;
            if (srcY < 0) srcY = -srcY;
            if (srcX >= 5) srcX = 2 * (5 - 1) - srcX;
            if (srcY >= 5) srcY = 2 * (5 - 1) - srcY;
            
            // 确保在有效范围内
            srcX = std::max(0, std::min(srcX, 4));
            srcY = std::max(0, std::min(srcY, 4));
            
            neighborhood[ky][kx] = testData[srcY][srcX];
            qDebug() << QString("neighborhood[%1][%2] = testData[%3][%4] = %5")
                        .arg(ky).arg(kx).arg(srcY).arg(srcX).arg(neighborhood[ky][kx]);
        }
    }
    
    qDebug() << "\n3x3邻域矩阵:";
    for (int i = 0; i < 3; ++i) {
        QString row;
        for (int j = 0; j < 3; ++j) {
            row += QString("%1").arg(neighborhood[i][j], 4);
            if (j < 2) row += ", ";
        }
        qDebug() << "  " << row;
    }
    
    qDebug() << "\n=== 步骤4：卷积计算 ===";
    float convResult = 0.0f;
    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; ++j) {
            float contribution = neighborhood[i][j] * normalizedKernel[i][j];
            convResult += contribution;
            qDebug() << QString("%1 × %2 = %3")
                        .arg(neighborhood[i][j])
                        .arg(normalizedKernel[i][j], 0, 'f', 6)
                        .arg(contribution, 0, 'f', 6);
        }
    }
    qDebug() << QString("卷积结果总和: %1").arg(convResult, 0, 'f', 6);
    
    qDebug() << "\n=== 步骤5：滤波强度处理 ===";
    float originalValue = static_cast<float>(testData[centerY][centerX]);
    float strength = 1.0f;
    float filteredValue = originalValue + strength * (convResult - originalValue);
    
    qDebug() << QString("原始值: %1").arg(originalValue);
    qDebug() << QString("卷积结果: %1").arg(convResult, 0, 'f', 6);
    qDebug() << QString("滤波强度: %1").arg(strength);
    qDebug() << QString("最终结果: %1 + %2 × (%3 - %1) = %4")
                .arg(originalValue)
                .arg(strength)
                .arg(convResult, 0, 'f', 6)
                .arg(filteredValue, 0, 'f', 6);
    qDebug() << QString("四舍五入: %1").arg(static_cast<int>(std::round(filteredValue)));
    
    qDebug() << "\n=== 验证结果 ===";
    qDebug() << QString("理论计算结果: %1").arg(filteredValue, 0, 'f', 2);
    qDebug() << "C++实际输出: 82";
    qDebug() << QString("差异: %1").arg(std::abs(filteredValue - 82.0f), 0, 'f', 2);
    
    if (std::abs(filteredValue - 82.0f) < 1.0f) {
        qDebug() << "✅ 理论计算与实际输出一致！";
        return 0;
    } else {
        qDebug() << "❌ 理论计算与实际输出存在差异";
        return 1;
    }
}
