#include "IPms.h"

IPms::IPms()
{
  qRegisterMetaType<IPms::ECommStep>("IPms::ECommStep"); //注册自定义类型

  //* default calib config
  mm_calib_param["calib1_param1_name"] = "xtalk_peak"; //
  mm_calib_param["calib1_param1"] = "900"; //
  mm_calib_param["calib1_param2_name"] = "xtalk_tof"; //
  mm_calib_param["calib1_param2"] = "150"; //
  mm_calib_param["calib2_param1_name"] = "ref_offset"; //
  mm_calib_param["calib2_param1"] = "0"; //
  mm_calib_param["calib2_param2_name"] = "ref_tof"; //
  mm_calib_param["calib2_param2"] = "0"; //

  ISpmsSoc::StCalibItems calib_items_tmp;
  //    if(pms_soc_name == "class CSpmsVi5300") {

  calib_items_tmp.param1_name = mm_calib_param["calib1_param1_name"];
  calib_items_tmp.param1 = mm_calib_param["calib1_param1"].toUInt();
  calib_items_tmp.param2_name = mm_calib_param["calib1_param2_name"];
  calib_items_tmp.param2 = mm_calib_param["calib1_param1"].toInt();
  mv_calib_items.append(calib_items_tmp);

  calib_items_tmp.param1_name = mm_calib_param["calib1_param1_name"];
  calib_items_tmp.param1 = mm_calib_param["calib1_param1"].toUInt();
  calib_items_tmp.param2_name = mm_calib_param["calib1_param2_name"];
  calib_items_tmp.param2 = mm_calib_param["calib1_param1"].toInt();
  mv_calib_items.append(calib_items_tmp);

  calib_items_tmp.param1_name = mm_calib_param["calib2_param1_name"];
  calib_items_tmp.param1 = mm_calib_param["calib2_param1"].toUInt();
  calib_items_tmp.param2_name = mm_calib_param["calib2_param2_name"];
  calib_items_tmp.param2 = mm_calib_param["calib2_param1"].toInt();
  mv_calib_items.append(calib_items_tmp);

  calib_items_tmp.param1_name = mm_calib_param["calib3_param1_name"];
  calib_items_tmp.param1 = mm_calib_param["calib3_param1"].toUInt();
  calib_items_tmp.param2_name = mm_calib_param["calib3_param2_name"];
  calib_items_tmp.param2 = mm_calib_param["calib3_param1"].toInt();
  mv_calib_items.append(calib_items_tmp);

  calib_items_tmp.param1_name = mm_calib_param["calib4_param1_name"];
  calib_items_tmp.param1 = mm_calib_param["calib4_param1"].toUInt();
  calib_items_tmp.param2_name = mm_calib_param["calib4_param2_name"];
  calib_items_tmp.param2 = mm_calib_param["calib4_param1"].toInt();
  mv_calib_items.append(calib_items_tmp);

  //* 区分不同芯片的校正流程与标准
  StCalibFunc calib_func_tmp;
  calib_func_tmp.ptr_ = (typeFptr_)&IPms::calibTask1;
  calib_func_tmp.ack_ptr_ = (typeFptr_)&IPms::calibTask1Ack;
  mv_calib_func.append(calib_func_tmp);

  calib_func_tmp.ptr_ = (typeFptr_)&IPms::calibTask2;
  calib_func_tmp.ack_ptr_ = (typeFptr_)&IPms::calibTask2Ack;
  mv_calib_func.append(calib_func_tmp);

  calib_func_tmp.ptr_ = (typeFptr_)&IPms::calibTask3;
  calib_func_tmp.ack_ptr_ = (typeFptr_)&IPms::calibTask3Ack;
  mv_calib_func.append(calib_func_tmp);

  calib_func_tmp.ptr_ = (typeFptr_)&IPms::calibTask4;
  calib_func_tmp.ack_ptr_ = (typeFptr_)&IPms::calibTask4Ack;
  mv_calib_func.append(calib_func_tmp);
}
