#include "pmsB1.h"

#include <QDebug>

#include "novaP.h"
#include "qLog.h"
#include "typeConvert.h"
#include "vi5300.h"
#include "vl53l4cd.h"



CPmsB1::CPmsB1(IComm *port_) : im_port_(port_) {
    m_protocol_  = new CNovaP;
    mi_spms_soc_ = new CSpmsVi5300;

    //* 此处应该可以选择数据接收方式
    im_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

    //* read cmd from loc file
    QString filename = QApplication::applicationDirPath() + "/cmdList/clen_config.xml";
    mi_load_->readParam(filename, &m_interaction_cmd);


    //* get calib step
    //  QMapIterator<QString, QByteArray> m_iter(mi_spms_soc_->mm_calib_flow);
    //  while (m_iter.hasNext()) {

    //      m_calib_cmd.insert(m_iter.key(), m_iter.value()); //添加 cmd
    //      m_iter.next();
    //  }


#ifdef INIT_OUTPUT
    qDebug() << "-i pmsA1/ init: --------------------";
    qDebug() << "-i sensorC/ start cmd: " << m_interaction_cmd["start"];
    qDebug() << "-i sensorC/ stop cmd: " << m_interaction_cmd["stop"];
#endif
}

CPmsB1::~CPmsB1() {
    if (m_protocol_ != nullptr)
        delete m_protocol_;
    if (mi_spms_soc_ != nullptr)
        delete mi_spms_soc_;
}

/**
 * @brief 固定指令直接初始化，有变化指令用成员变量，只初始化固定部分
 *
 */
void CPmsB1::cmd_init(void) {
    uint16_t data;
    //*******************固定指令初始化*******************
    //  m_cmd["start"] = m_protocol_->getControlCmd();

    //  m_cmd["chipId"] = m_protocol_->getWriteCmd(kMcuId, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(KNone)));
    //  m_cmd["caliMode"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofCalibrationMode))); //
    //  m_cmd["greyMap"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofFaculaMode))); //
    //  m_cmd["cloudMode"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofScanMode))); //

    //  m_cmd["stop"] = m_protocol_->getControlCmd();

#ifdef COMM_OUTPUT
    qDebug() << "-i lensST cmd/ start: " << m_interaction_cmd["start"];
    qDebug() << "-i lensST cmd/ chipId: " << m_interaction_cmd["chipId"];
    qDebug() << "-i lensST cmd/ caliMode: " << m_interaction_cmd["caliMode"];
    qDebug() << "-i lensST cmd/ greyMap: " << m_interaction_cmd["greyMap"];
    qDebug() << "-i lensST cmd/ cloudMode: " << m_interaction_cmd["cloudMode"];
#endif

    //******************变化指令初始化********************
}

QByteArray CPmsB1::portDataRead() {
    return im_port_->read(5);
}

/**
 * @brief icom_change_interface
 * @param port_
 */
void CPmsB1::icom_change_interface(IComm *port_) {
    im_port_ = port_;
}

#if 0
/****************
 * 对于同一类型有多个选项，采用直接写入选项的方式
 * 1. 枚举切换方式：增加了枚举变量，而且添加模式会改动现有函数
 * 2. 每个模式对应一个函数：单一性，但函数会过多
 * *******************************/
/**
 * @brief 切换模式
 * @return
 */
bool CPmsJamooz::modeChange(const EModeType &mode)
{
  bool flag = false;
  switch (mode) {
    case eSINGLE_MODE:  break;
    case eCALI_MODE: flag = im_port_->write(m_cmd["caliMode"]); break;
    case eGREY_MODE: flag = im_port_->write(m_cmd["caliMode"]); break;
    case eCLOUD_MODE: flag = im_port_->write(m_cmd["caliMode"]); break;
    default: flag = false; break;

    }
  return flag;
}
#else
/**
 * @brief 直接写入模式
 * @param mode
 * @return
 */
bool CPmsB1::modeChange(const uint16_t &mode) {
    m_strPre.clear();
    //  return im_port_->write(m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(mode))); //
}
#endif
/**
 * @brief 读信息
 * @return
 */
bool CPmsB1::readInfo(const uint8_t &id, const uint16_t &data) {
    QByteArray cmd = m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data));
    qDebug() << "-i clen/ mcu_id: " << cmd;
    m_strPre.clear();
    return im_port_->write(m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data)));  //
}

/**
 * @brief CPmsJamooz::changeRigster
 * @return
 */
bool CPmsB1::changeRigster(){

};

EExecStatus CPmsB1::calibTasksRun() {
}


EExecStatus CPmsB1::verifyTasksRun() {
}

/**
 * @brief 交互指令解析
 * @param str
 * @param length
 * @return
 */
bool CPmsB1::interactionParsing(QByteArray str, int length) {
    Q_UNUSED(length);
    //  if(str.length() == 0) return false;

    //  QByteArray strSum = m_strPre + str;
    //  if(strSum.length() < (uint16_t)CNovaP::EReceiveFrame::eHEADER_LEN) //固定部分长度6
    //  {
    //      m_strPre = strSum;
    //      return false;
    //  }

    //  qDebug() << "-i senCoinD/ interaction ack:" << strSum;
    //  int i = 0;
    /*1.1 parse*/
    //  for(;;) //留6个数
    //    {
    //      if(strSum.length() >= (uint16_t)CNovaP::EReceiveFrame::eHEADER_LEN) {
    //          if((uchar)strSum.at(0) == (uint16_t)CNovaP::EReceiveFrame::eHEADER) {//帧头  && ((uchar)strSum.at(i + 1) == (CNovaP::kD2H | CNovaP::kHRS |
    //          CNovaP::kHSS))
    //              //*
    //              uint8_t xor_cal = 0;
    //              CNovaP::StFrame* data_receive_ = new CNovaP::StFrame;

    //              //*
    //              data_receive_ = reinterpret_cast<CNovaP::StFrame *>(strSum.data());
    //              uint16_t num = data_receive_->num << 1; //字节数

    //              if(strSum.length() >= (num + CNovaP::eHEADER_LEN)) {//数量符合
    //                  QByteArray array_tmp;
    //                  memcmp(&array_tmp, &strSum, CNovaP::eHEADER_LEN);

    ////                  data_receive_->data.resize(num);
    ////                  data_receive_ = reinterpret_cast<CNovaP::StFrame*>(array_tmp.data()); //qbytearray 无法 copy

    //                  QByteArray strTmp; //指令数据
    //                  for(uint16_t n = 0; n < num + CNovaP::eHEADER_LEN; ++n) {
    //                      if(n != (3)) {
    //                          xor_cal ^= (uchar)strSum.at(n);
    //                        }
    //                      if(n >= CNovaP::eHEADER_LEN) {

    //                          strTmp.append((uchar)strSum.at(n));
    //                        }
    //                    }
    //                  if(xor_cal == data_receive_->check_xor)
    //                    {
    //                      switch (data_receive_->id) {
    //                        case kMcuId:

    ////                          strTmp = data_receive_->data;
    //                          emit dataOutput(ECommStep::eCHIP_ID, ECommStatus::eCOMM_COMP, strTmp);
    //                          break;
    //                        case kTofMode:
    //                          strTmp.clear();
    //                          emit dataOutput(ECommStep::eMODE_CHANGE, ECommStatus::eCOMM_COMP, strTmp);
    //                          break;
    //                        default:
    //                          break;
    //                        }

    //                      strSum.remove(0, (num + CNovaP::eHEADER_LEN));
    //                      m_strPre.clear();
    //                      m_strPre.push_back(strSum); //存储剩余数据
    //                      //                  return true;
    //                    }
    //                  //              else
    //                  //                {
    //                  //                  strSum.remove(0, 1); //i-1
    //                  //                  i = 0;
    //                  //                }
    //                }
    //              else {
    //                  m_strPre.clear();
    //                  m_strPre.push_back(strSum); //存储剩余数据
    //                  return false;
    //                }
    //            }
    //          strSum.remove(0, 1);
    //        }
    //      else {
    //          m_strPre.clear();
    //          m_strPre.push_back(strSum); //存储剩余数据
    //          return false;
    //        }
    //    }
}

/**
 * @brief 距离解析 迭代
 */
bool CPmsB1::dataParsing(QByteArray str, int length) {
    if (length == 0)
        return false;

    //  QByteArray strSum = m_strPre + str;
    //  if(strSum.length() < 6) //固定部分长度6
    //    {
    //      m_strPre = strSum;
    //      return false;
    //    }

    //  QByteArray strTmp; //指令数据
    //  int i = 0;
    //  uint16_t num;
    //  uint8_t xor_cal;
    //  uint32_t data_tmp;

    //  /*1.1 parse*/
    //  for(i = 0; i < (strSum.length() - 5); ++i) //留6个数
    //    {
    //      if(((uchar)strSum.at(i) == 0xA5) && ((uchar)strSum.at(i + 1) == (CNovaP::kD2H | CNovaP::kDFF))) //帧头
    //        {
    //          switch ((uchar)strSum.at(i + 2)) {    //
    //            case 0xAD: //数据反馈
    //              num = (uchar)strSum.at(i + 4) + uint16_t((uchar)strSum.at(i + 5)<<8);
    //              xor_cal = 0;
    //              if((strSum.length() - i) >= (2*num + 6)) //数量符合
    //                {
    //                  strTmp.clear();
    //                  for(uint16_t n = i; n < i + 2*num + 6; ++n)
    //                    {
    //                      if(n != (3 + i))
    //                        {
    //                          xor_cal ^= (uchar)strSum.at(n);
    //                        }
    //                      if(n > (i + 5))
    //                        strTmp.push_back(strSum.at(n)); //
    //                    }
    //                  if(xor_cal == (uchar)strSum.at(i + 3))
    //                    {
    //                      //                        for(uint16_t n = 0; n < (num>>1); ++n)
    //                      //                        {
    //                      //                            data_tmp = (uchar)strSum.at(i + 6 + (n<<2) + 0) | ((uchar)strSum.at(i + 6 + (n<<2) + 1)<<8) |
    //                      ((uchar)strSum.at(i + 6 + (n<<2) + 2)<<16) | ((uchar)strSum.at(i + 6 + (n<<2) + 3)<<24);
    //                      //                            data_tmp_v.push_back(data_tmp); //
    //                      //                        }
    //                      //                        data_tmp_v.clear(); //
    //#if 0 //发送解析后的值
    //                      for(uint16_t n = 0; n < (num>>1); ++n) //
    //                        {
    //                          data_tmp = (uchar)strTmp.at((n<<2) + 0) | ((uchar)strTmp.at((n<<2) + 1)<<8) | ((uchar)strTmp.at((n<<2) + 2)<<16) |
    //                          ((uchar)strTmp.at((n<<2) + 3)<<24);
    //                        }
    //#endif
    //                      emit dataOutput(ECommStep::eMAP_DATA, ECommStatus::eCOMM_COMP, strTmp);

    //                      strSum.remove(0, (i + 2*num + 6));
    //                      m_strPre.clear();
    //                      m_strPre.push_back(strSum); //存储剩余数据
    //                      return true;
    //                    }
    //                  else
    //                    {
    //                      i += 2;
    //                      qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-clen xor: " << xor_cal;
    //                      strTmp.clear();
    //                      emit dataOutput(ECommStep::eMAP_DATA, ECommStatus::eCOMM_ERROR, strTmp);
    //                    }
    //                }
    //              else
    //                {
    //                  strSum.remove(0, i); //i-1
    //                  m_strPre.clear();
    //                  m_strPre.push_back(strSum); //存储剩余数据
    //                  return false;
    //                }
    //              break;
    //            default:   //无效指令
    //              ++i; //
    //              break;
    //            } //
    //        }
    //    }
    //  strSum.remove(0, i - 1);
    //  m_strPre.clear();
    //  m_strPre.push_back(strSum); //存储剩余数据
    //  return false;
}
