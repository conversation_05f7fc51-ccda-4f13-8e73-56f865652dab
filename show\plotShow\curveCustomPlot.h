#ifndef _CURVE_CUSTOMPLOT_H_
#define _CURVE_CUSTOMPLOT_H_

#include <QMessageBox>
#include <QDebug>
#include <qcustomplot.h>

#include "IPlot.h"

//typedef struct{
////    QVector<QCPGraphData> fgHandled; //fg数据
////    QVector<QCPGraphData> cycleHandled; //单圈数

////    QCPBarsDataContainer fgHandledData;
//    QSharedPointer<QCPBarsDataContainer> fgHandleData;
//    QSharedPointer<QCPBarsDataContainer> cycleHandleData;

//    double centroidPos[10]; //不同光斑处理方式质心数据
//    uint16_t dist[10];
//    uint8_t percentValue;
//    uint16_t dist_max[10];
//    uint16_t dist_min[10];
//}CentroidDistData;

class CCurveCustomPlot : public IPlot
{
    Q_OBJECT
public:
    CCurveCustomPlot(QCustomPlot &customPlot, const StFigureInfo &st_figure_info);
    ~CCurveCustomPlot();

//    bool dataHandle(const QByteArray &fg_data_original, const QByteArray &cycle_data_original, CentroidDistData* centroid_dist);
    QSharedPointer<QCPBarsDataContainer> dataHandle(const QVector<uint16_t> &data);
    QSharedPointer<QCPBarsDataContainer> dataHandle1(uint16_t start_index, const QVector<uint16_t> &data);
    void customPlotShow(QVector<double> x, QVector<double> y);

    void afterGlowGraphNumChange(QVector<QCPGraph*> &mainGraphCosPoint, QPen *pen, uint8_t graph_num, uint8_t &graph_num_cur);
//    void wavePointTextShow(const uint8_t &index, QCPItemText** wavePointText, QPen *pen, QVector<double> centroid_arr, QVector<uint16_t> dist_arr, float position_offset, CentroidDistData* centroid_dist);
    void subCentroidShow(QCPGraph** subCentroidGraph, QPen *pen, QVector<QCPGraphData> dataCentroidSca);

    void plotSampleCurve(QCPGraph* sub_graph, QVector<St2DiPoint> vec);
    void plotFitCurve(QVector<St2DiPoint> vec);

    void createFigure() override;
private:
    uint m_length;

    QCustomPlot *m_customPlot_ = nullptr;
    StFigureInfo mst_figure_info;

    QCPGraph *m_curve_graph_ = nullptr; //绘图元素 折线图(QCPGraph) 实时

//    QCPGraph *m_mainGraphCosPoint = nullptr;
//    QCPGraph *subGraphRandoPoint = nullptr;
    QCPItemText *wavePacketText = nullptr;
    QCPAxisRect *mainRectRight = nullptr;
    QCPAxisRect *subRectLeft = nullptr;
    QCPAxisRect *subRectRight = nullptr;

signals:

private slots:
    void mouseDouClick(QMouseEvent* event);
};

#endif // CUSTOMPLOTWAVE_H
