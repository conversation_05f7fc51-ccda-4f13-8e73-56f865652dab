#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文档关联可视化模块

单一职责：专门处理文档关联相关的数据提取和可视化逻辑
"""

import re
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
from io import StringIO
import os

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import DataAdapter, VisualizationData, VisualizationMode, Node, Edge
from core.component_utils import component_manager

class DocumentsDataExtractor(DataAdapter):
    """文档关联数据提取器 - 单一职责：文档关联可视化数据提取"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
    
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """提取文档关联可视化数据"""
        self.project_path = Path(project_path)
        return self._extract_documents_data()
    
    def _extract_documents_data(self) -> VisualizationData:
        """提取文档关联数据"""
        nodes = []
        edges = []
        
        # 扫描所有INDEX文件
        index_files = list(self.project_path.glob("**/*_INDEX.md"))
        
        for index_file in index_files:
            try:
                content = self._read_file_with_fallback(index_file)
                if content is None:
                    continue
                
                relations = self._parse_index_relations(content, index_file)
                nodes.extend([Node(**node_data) for node_data in relations['nodes']])
                edges.extend([Edge(**edge_data) for edge_data in relations['edges']])
                
            except Exception as e:
                print(f"解析INDEX文件失败 {index_file}: {e}")
        
        # 总是扫描双链引用，作为补充数据源
        double_links = self._scan_double_links()
        nodes.extend([Node(**node_data) for node_data in double_links['nodes']])
        edges.extend([Edge(**edge_data) for edge_data in double_links['edges']])
        
        return VisualizationData(
            title="文档关联网络",
            mode=VisualizationMode.DOCUMENTS,
            nodes=nodes,
            edges=edges,
            metadata={
                "document_count": len(nodes),
                "relation_count": len(edges),
                "index_files_found": len(index_files),
                "scan_time": datetime.now().isoformat()
            }
        )
    
    def _read_file_with_fallback(self, file_path: Path) -> str:
        """读取文件内容，支持编码回退"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 {file_path}: {e}")
                break
        
        return None
    
    def _parse_index_relations(self, content: str, index_file: Path) -> Dict[str, List[Dict[str, Any]]]:
        """解析INDEX文件中的关联关系"""
        nodes = []
        edges = []
        
        try:
            # 找到表格开始的行
            lines = content.split('\n')
            table_start = 0
            for i, line in enumerate(lines):
                if line.strip().startswith("|") and "文档ID" in line:
                    table_start = i
                    break
            
            if table_start == 0:
                return {"nodes": nodes, "edges": edges}
            
            # 提取表格内容
            table_lines = []
            for line in lines[table_start:]:
                if line.strip().startswith("|"):
                    table_lines.append(line)
                elif table_lines:  # 表格结束
                    break
            
            if len(table_lines) < 2:  # 至少需要表头和一行数据
                return {"nodes": nodes, "edges": edges}
            
            # 使用pandas解析表格
            table_content = '\n'.join(table_lines)
            df = pd.read_csv(StringIO(table_content), sep='|', skipinitialspace=True)
            
            # 清理列名并移除完全空白的行
            df.columns = [col.strip() for col in df.columns if col.strip()]
            df = df.dropna(how='all').reset_index(drop=True)
            
            # 安全处理第一列数据类型
            if len(df.columns) > 0 and len(df) > 0:
                # 重建DataFrame避免类型冲突
                first_col_name = df.columns[0]
                first_col_data = df[first_col_name].fillna('').astype(str)
                
                # 过滤掉空行和分隔行
                valid_rows = ~first_col_data.str.contains(r'^[\s\-\|]*$', na=False)
                df = df[valid_rows].reset_index(drop=True)
            
            # 提取节点
            for _, row in df.iterrows():
                if len(row) > 0 and pd.notna(row.iloc[0]):
                    doc_id = str(row.iloc[0]).strip()
                    doc_name = str(row.iloc[1]).strip() if len(row) > 1 and pd.notna(row.iloc[1]) else doc_id
                    
                    if doc_id and not doc_id.startswith('-') and not doc_id.isspace():
                        component = component_manager.extract_component_from_id(doc_id)
                        node_data = {
                            "id": doc_id,
                            "name": doc_name,
                            "type": "document",
                            "component": component,
                            "properties": {
                                "source_file": str(index_file),
                                "file_path": str(index_file.parent / f"{doc_id}.md")
                            }
                        }
                        nodes.append(node_data)
            
        except Exception as e:
            print(f"解析INDEX表格失败 {index_file}: {e}")
        
        return {"nodes": nodes, "edges": edges}
    
    def _scan_double_links(self) -> Dict[str, List[Dict[str, Any]]]:
        """扫描双链引用关系"""
        nodes = []
        edges = []
        
        md_files = list(self.project_path.glob("**/*.md"))
        link_pattern = re.compile(r'\[\[([^\]]+)\]\]')
        
        for md_file in md_files:
            try:
                content = self._read_file_with_fallback(md_file)
                if content is None:
                    continue
                
                # 提取文档ID作为源节点
                doc_id = md_file.stem  # 使用文件名作为ID
                
                # 智能识别组件类型
                component = self._identify_component_from_path_and_content(md_file, content)
                
                # 添加源文档节点
                source_node = {
                    "id": doc_id,
                    "name": md_file.stem,
                    "type": "document",
                    "component": component,
                    "properties": {"file_path": str(md_file)}
                }
                
                # 避免重复添加
                if not any(n["id"] == doc_id for n in nodes):
                    nodes.append(source_node)
                
                # 查找双链引用
                links = link_pattern.findall(content)
                for link in links:
                    # 清理链接内容
                    link = link.strip()
                    
                    # 跳过REF语法（这是追溯模块的职责）
                    if link.startswith('REF:'):
                        continue
                    
                    # 创建目标节点ID
                    target_id = link.replace(' ', '_')  # 替换空格
                    
                    # 智能识别目标文档的组件类型
                    target_component = self._identify_component_from_name(link)
                    
                    # 添加目标节点
                    target_node = {
                        "id": target_id,
                        "name": link,
                        "type": "document",
                        "component": target_component,
                        "properties": {"referenced_from": str(md_file)}
                    }
                    
                    if not any(n["id"] == target_id for n in nodes):
                        nodes.append(target_node)
                    
                    # 添加关联边
                    edge_data = {
                        "source": doc_id,
                        "target": target_id,
                        "type": "引用",
                        "properties": {"context": "双链引用"}
                    }
                    edges.append(edge_data)
                        
            except Exception as e:
                print(f"扫描双链引用失败 {md_file}: {e}")
        
        return {"nodes": nodes, "edges": edges}
    
    def _identify_component_from_path_and_content(self, file_path: Path, content: str) -> str:
        """根据文件路径和内容识别组件类型"""
        path_str = str(file_path).lower()
        
        # 首先根据路径识别
        if 'requirement' in path_str or 'req' in path_str:
            return 'REQ'
        elif 'design' in path_str or 'des' in path_str:
            return 'DES'
        elif 'development' in path_str or 'dev' in path_str or '开发' in path_str:
            return 'DEV'
        elif 'test' in path_str or 'qa' in path_str or 'quality' in path_str:
            return 'QA'
        elif 'production' in path_str or 'prod' in path_str:
            return 'PROD'
        elif 'deliverable' in path_str or 'del' in path_str:
            return 'DEL'
        elif 'project' in path_str or 'pm' in path_str or 'management' in path_str:
            return 'PM'
        
        # 然后根据文件名识别
        filename = file_path.name.lower()
        if any(keyword in filename for keyword in ['需求', 'requirement', 'req']):
            return 'REQ'
        elif any(keyword in filename for keyword in ['设计', 'design', 'des']):
            return 'DES'
        elif any(keyword in filename for keyword in ['开发', 'development', 'dev']):
            return 'DEV'
        elif any(keyword in filename for keyword in ['测试', 'test', 'qa', 'quality']):
            return 'QA'
        elif any(keyword in filename for keyword in ['生产', 'production', 'prod']):
            return 'PROD'
        elif any(keyword in filename for keyword in ['交付', 'deliverable', 'del']):
            return 'DEL'
        elif any(keyword in filename for keyword in ['管理', 'project', 'pm']):
            return 'PM'
        
        # 最后根据内容识别
        content_lower = content.lower()
        if any(keyword in content_lower for keyword in ['需求规格', '功能需求', 'requirement']):
            return 'REQ'
        elif any(keyword in content_lower for keyword in ['系统设计', '架构设计', 'design']):
            return 'DES'
        elif any(keyword in content_lower for keyword in ['代码实现', '开发计划', 'development']):
            return 'DEV'
        elif any(keyword in content_lower for keyword in ['测试计划', '质量保证', 'test', 'quality']):
            return 'QA'
        elif any(keyword in content_lower for keyword in ['生产部署', 'production']):
            return 'PROD'
        elif any(keyword in content_lower for keyword in ['交付物', 'deliverable']):
            return 'DEL'
        elif any(keyword in content_lower for keyword in ['项目管理', 'project management']):
            return 'PM'
        
        # 默认返回DOC
        return 'DOC'
    
    def _identify_component_from_name(self, name: str) -> str:
        """根据文档名称识别组件类型"""
        name_lower = name.lower()
        
        # 检查是否包含组件标识
        if any(keyword in name_lower for keyword in ['需求', 'requirement', 'req']):
            return 'REQ'
        elif any(keyword in name_lower for keyword in ['设计', 'design', 'des']):
            return 'DES'
        elif any(keyword in name_lower for keyword in ['开发', 'development', 'dev']):
            return 'DEV'
        elif any(keyword in name_lower for keyword in ['测试', 'test', 'qa', 'quality']):
            return 'QA'
        elif any(keyword in name_lower for keyword in ['生产', 'production', 'prod']):
            return 'PROD'
        elif any(keyword in name_lower for keyword in ['交付', 'deliverable', 'del']):
            return 'DEL'
        elif any(keyword in name_lower for keyword in ['管理', 'project', 'pm']):
            return 'PM'
        
        # 默认返回DOC
        return 'DOC'
    
    def _extract_doc_id(self, filename: str, content: str) -> str:
        """从文件名或内容中提取文档ID"""
        # 首先尝试从文件名提取
        id_match = re.match(r'([A-Z]+\d+)', filename)
        if id_match:
            return id_match.group(1)
        
        # 然后尝试从内容中提取
        id_patterns = [
            r'(?:文档ID|ID)[：:\s]*([A-Z]+\d+)',
            r'(?:编号|Number)[：:\s]*([A-Z]+\d+)',
            r'^([A-Z]+\d+)',  # 开头的ID
        ]
        
        for pattern in id_patterns:
            match = re.search(pattern, content, re.MULTILINE | re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def analyze_document_network(self) -> Dict[str, Any]:
        """分析文档网络特征"""
        data = self._extract_documents_data()
        
        # 构建邻接表
        adjacency = {}
        for edge in data.edges:
            source = edge.source
            target = edge.target
            
            if source not in adjacency:
                adjacency[source] = []
            adjacency[source].append(target)
        
        # 计算网络指标
        total_nodes = len(data.nodes)
        total_edges = len(data.edges)
        
        # 计算度分布
        in_degrees = {}
        out_degrees = {}
        
        for node in data.nodes:
            node_id = node.id
            out_degrees[node_id] = len(adjacency.get(node_id, []))
            in_degrees[node_id] = sum(1 for adj_list in adjacency.values() if node_id in adj_list)
        
        return {
            "network_size": total_nodes,
            "connection_density": total_edges / (total_nodes * (total_nodes - 1)) if total_nodes > 1 else 0,
            "average_out_degree": sum(out_degrees.values()) / total_nodes if total_nodes > 0 else 0,
            "average_in_degree": sum(in_degrees.values()) / total_nodes if total_nodes > 0 else 0,
            "most_referenced": max(in_degrees.items(), key=lambda x: x[1]) if in_degrees else None,
            "most_referencing": max(out_degrees.items(), key=lambda x: x[1]) if out_degrees else None,
            "component_distribution": self._analyze_component_distribution(data.nodes)
        }
    
    def _analyze_component_distribution(self, nodes: List[Node]) -> Dict[str, int]:
        """分析组件分布"""
        distribution = {}
        for node in nodes:
            component = node.component or "未知"
            distribution[component] = distribution.get(component, 0) + 1
        return distribution
    
    def generate_component_relation_graph(self, output_path: str = None) -> str:
        """生成组件间关系图 - 整合自generate_relation_graph.py"""
        try:
            import networkx as nx
            import matplotlib.pyplot as plt
            from matplotlib.font_manager import FontProperties
            
            # 创建图
            G = nx.DiGraph()
            
            # 从文档数据构建组件关系
            data = self._extract_documents_data()
            
            # 统计组件间的文档关联
            component_relations = {}
            for edge in data.edges:
                source_comp = None
                target_comp = None
                
                # 找到源和目标的组件
                for node in data.nodes:
                    if node.id == edge.source:
                        source_comp = node.component
                    if node.id == edge.target:
                        target_comp = node.component
                
                if source_comp and target_comp and source_comp != target_comp:
                    if source_comp not in component_relations:
                        component_relations[source_comp] = set()
                    component_relations[source_comp].add(target_comp)
            
            # 添加节点
            all_components = set(component_relations.keys())
            for relations in component_relations.values():
                all_components.update(relations)
            
            for comp_id in all_components:
                comp_info = component_manager.get_component_info(comp_id)
                comp_name = comp_info.name if comp_info else comp_id
                G.add_node(comp_id, name=comp_name)
            
            # 添加边
            for source_comp, target_comps in component_relations.items():
                for target_comp in target_comps:
                    G.add_edge(source_comp, target_comp)
            
            # 绘制图
            plt.figure(figsize=(12, 8))
            pos = nx.spring_layout(G, seed=42)
            
            # 绘制节点
            node_colors = [component_manager.get_component_color(node) for node in G.nodes()]
            nx.draw_networkx_nodes(G, pos, node_size=2000, node_color=node_colors, alpha=0.8)
            
            # 绘制边
            nx.draw_networkx_edges(G, pos, width=1.5, alpha=0.7, arrows=True, arrowstyle='->', arrowsize=20)
            
            # 绘制标签
            labels = {node: f"{node}\n({G.nodes[node]['name']})" for node in G.nodes()}
            try:
                font = FontProperties(fname=r"c:\windows\fonts\simsun.ttc", size=12) if os.name == 'nt' else None
                nx.draw_networkx_labels(G, pos, labels=labels, font_weight='bold', 
                                      font_family='SimSun' if font else 'sans-serif')
            except:
                nx.draw_networkx_labels(G, pos, labels=labels, font_weight='bold')
            
            plt.title("组件关系图", fontsize=16, fontweight='bold')
            plt.axis('off')
            plt.tight_layout()
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                plt.close()
                return f"组件关系图已保存到 {output_path}"
            else:
                # 保存到临时文件
                import tempfile
                temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
                plt.savefig(temp_file.name, dpi=300, bbox_inches='tight')
                plt.close()
                return temp_file.name
                
        except ImportError:
            return "需要安装 networkx 和 matplotlib: pip install networkx matplotlib"
        except Exception as e:
            return f"生成组件关系图失败: {e}" 