#include "clendatames.h"
#include "ui_clendatames.h"
#include <QShortcut>
#include <QThread>


clenDataMes::clenDataMes(QWidget *parent)
    : QDockWidget(parent), ui(new Ui::clenDataMes), mst_config_(new NClenMes::StUiConfig), mc_operation_(new CLenDataMesOpt(*mst_config_)) {
    ui->setupUi(this);
    //    this->setWindowIcon(QIcon("cspc1.jpg")); //与ui配置的会冲突
    this->setWindowTitle(LEN_REWORK_APP_WINDOW_TITLE);

    //* 3.信号与槽
    QShortcut *temp = new QShortcut(this);
    //设置键值，也就是设置快捷键.
    //    temp->setKey(tr("ctrl+space"));
    temp->setKey(Qt::CTRL + Qt::Key_Space);
    //这个成员函数挺关键的，设置是否会自动反复按键.也就是说，当你一直按住键盘ctrl+空格时，会一直不停的调用对应的槽函数.
    temp->setAutoRepeat(false);

    //连接信号与槽，showSlot()是自定义的槽函数!
    connect(temp, SIGNAL(activated()), this, SLOT(on_reload_clicked()));

    connect(mc_operation_, &CLenDataMesOpt::moduleInfoShowSignal, this, &clenDataMes::processTaskInfoShow_slot);

    connect(mc_operation_, &CLenDataMesOpt::portUpdateSignal, this, &clenDataMes::portListShow);  //列表更新

    connect(mc_operation_, &CLenDataMesOpt::readySignal, this, &clenDataMes::readyShow);  // ready
    //    connect(mc_operation_, &CLenDataMesOpt::startAckSignal,
    //            this, &clenDataMes::startAckShow); //start ack

    connect(mc_operation_, &CLenDataMesOpt::compAckSignal, this, &clenDataMes::compAckShow);  // complete ack
}

clenDataMes::~clenDataMes() {
    delete ui;
    delete mst_config_;
    delete mc_operation_;
}


void clenDataMes::closeEvent(QCloseEvent *event) {
    Q_UNUSED(event);

    this->setAttribute(Qt::WA_DeleteOnClose);  //释放窗口资源

    QThread::msleep(50);

    emit windowCloseSiganl(true);
}

/**
 * @brief 配置更新
 */
void clenDataMes::updateConfig(NClenMes::StUiConfig *config_) {
    config_->port_name     = ui->portBox->currentText();
    config_->cur_port_name = ui->portBox->currentText();

    config_->work_number     = ui->workOrder->text();
    config_->nbr_flag        = ui->nbrFlag->isChecked();
    config_->is_button_close = false;
}

/**
 * @brief 更新窗口列表
 */
void clenDataMes::portListShow(QStringList *port_list_, bool port_flag) {
    ui->portBox->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->portBox->clear();                //会触发currentIndexChanged回调函数
    ui->portBox->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->portBox->setCurrentIndex(0);
    else
        ui->portBox->setCurrentText(mst_config_->cur_port_name);

    ui->portBox->blockSignals(false);
}


void clenDataMes::processTaskInfoShow_slot(const bool &is_error, const QString &info) {
    if (is_error) {
        ui->processView->setTextColor(Qt::red);
        //        ui->processView->append("<font color=\"#FF0000\"");
        //        ui->processView->setStyleSheet(process_unnormal_sheetStyle);
    } else {
        ui->processView->setTextColor(Qt::black);
        //        ui->processView->setStyleSheet(process_normal_sheetStyle);
    }

    ui->processView->append(info);
}

/**
 * @brief 显示界面清空
 */
void clenDataMes::resultClean(void) {
    //* status bar clean
    ui->processView->setTextColor(Qt::black);
    //    ui->processView->setStyleSheet(process_normal_sheetStyle);
    ui->processView->clear();
}


void clenDataMes::readyShow(bool is_open) {
    if (is_open) {
        ui->reload->setText("loadding");
        resultClean();
    }
}

/**
 * @brief
 */
void clenDataMes::compAckShow(bool is_comp) {
    Q_UNUSED(is_comp);
    //* 切换界面
    //  if((mst_config_->mode == IClensMachine::EMode::manual_mode) && (ui->modeBox->currentIndex() == 0)) //切换界面
    //  on_modeBox_currentIndexChanged(0);

    //  on_modeBox_currentIndexChanged(1);

    ui->reload->setText("reload");  //
}

/**
 * @brief: 串口开关
 */
void clenDataMes::on_reload_clicked() {
    if (ui->reload->text() == QString("reload")) {                                   //
        updateConfig(mst_config_);                                                   //配置更新
        mc_operation_->mv_task_list[CLenDataMesOpt::eOPEN_SERIAL].flag.exec = true;  //开始任务
    } else {                                                                         //关闭->退出
        // if(mst_config_->mode) mc_operation_->mv_task_list[CLenDataMesOpt::eCOMPLETE].flag.stop = true; //结束任务
        mst_config_->is_button_close                                     = true;
        mc_operation_->mv_task_list[CLenDataMesOpt::eCOMPLETE].flag.stop = true;  //结束任务
    }
}

void clenDataMes::on_portBox_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_port_name = ui->portBox->currentText();
}


// void clenDataMes::on_mesDataInput_clicked()
//{
//    if(mc_lens_data_mes_ == nullptr) {
//        mc_lens_data_mes_ = new clenDataMes;
//        mc_lens_data_mes_->show();
//        this->showMinimized();
//        QObject::connect(mc_lens_data_mes_, &clenDataMes::windowCloseSiganl,
//                         this, &clenDataMes::lenMesDataFB);
//    }
//}

// void clenDataMes::lenMesDataFB(bool closed) {
//    if(closed && mc_lens_data_mes_ != nullptr) {
//        QObject::disconnect(mc_lens_data_mes_, &clenDataMes::windowCloseSiganl, this, &clenDataMes::novaCalibFB); //断开连接
//        //        timeId_ptr_->id6 = false;
//        delete mc_lens_data_mes_;
//        mc_lens_data_mes_ = nullptr;
//        this->showNormal();
//    }
//}
