#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
单层项目结构初始化工具

该脚本用于初始化单层级产品目录框架的目录结构和配置文件。
支持不同类型的项目初始化，包括硬件、软件、固件等。
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime

# 临时定义PROJECT_TYPES（避免导入问题）
PROJECT_TYPES = {
    "single_layer": {
        "name": "单层级产品",
        "dev_subdirs": ["hardware", "firmware", "software", "tool"]
    },
    "multi_level": {
        "name": "多层级产品",
        "dev_subdirs": ["hardware", "firmware", "software", "tool"]
    },
    "full": {
        "name": "完整产品",
        "dev_subdirs": ["hardware", "firmware", "software", "tool"]
    }
}

# 全局变量控制输出
SILENT_MODE = False

def safe_print(*args, **kwargs):
    """安全的打印函数，在静默模式下不输出"""
    if not SILENT_MODE:
        print(*args, **kwargs)

def create_directory_structure(project_type, project_path):
    """创建目录结构"""
    # 创建基本目录
    base_dirs = [
        "product_info",
        "requirements",
        "design",
        "development",
        "quality",
        "production",
        "deliverables",
        "project_management",
        "scripts",
        "config",
        "reports"
    ]
    
    for dir_name in base_dirs:
        dir_path = os.path.join(project_path, dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")
    
    # 创建产品信息子目录
    product_info_dirs = [
        "competitor_products/competitor_A",
        "competitor_products/competitor_B"
    ]
    for dir_name in product_info_dirs:
        dir_path = os.path.join(project_path, "product_info", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")

    # 创建需求子目录
    req_dirs = ["main_requirements", "custom_requirements", "market_requirements"]
    for dir_name in req_dirs:
        dir_path = os.path.join(project_path, "requirements", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")

    # 创建设计子目录
    design_dirs = [
        "components",
        "project_solution",
        "specification_document",
        "interface_specifications",
        "principle_Information"
    ]
    for dir_name in design_dirs:
        dir_path = os.path.join(project_path, "design", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")
    
    # 创建开发子目录
    dev_subdirs = PROJECT_TYPES[project_type]["dev_subdirs"]
    for subdir in dev_subdirs:
        if subdir == "hardware":
            for i in range(1, 3):
                dir_path = os.path.join(project_path, "development", subdir, f"hw_project_{i}")
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                    safe_print(f"创建目录: {dir_path}")
        elif subdir == "firmware":
            for i in range(1, 3):
                dir_path = os.path.join(project_path, "development", subdir, f"fw_project_{i}")
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                    safe_print(f"创建目录: {dir_path}")
        elif subdir == "software":
            for proj in ["app_project", "cloud_project"]:
                dir_path = os.path.join(project_path, "development", subdir, proj)
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                    safe_print(f"创建目录: {dir_path}")
        elif subdir == "tool":
            for tool in ["production_tools", "debug_tools"]:
                dir_path = os.path.join(project_path, "development", subdir, tool)
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                    safe_print(f"创建目录: {dir_path}")
    
    # 创建测试子目录
    test_dirs = [
        "test_plans",
        "test_cases",
        "test_reports/internal_tests",
        "test_reports/competitive_tests",
        "issues"
    ]
    for dir_name in test_dirs:
        dir_path = os.path.join(project_path, "quality", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")
    
    # 创建生产子目录
    prod_dirs = ["bom", "manufacturing_process", "quality_control"]
    for dir_name in prod_dirs:
        dir_path = os.path.join(project_path, "production", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")

    # 创建交付物子目录
    del_dirs = ["documents", "firmware", "hardware", "software", "tools"]
    for dir_name in del_dirs:
        dir_path = os.path.join(project_path, "deliverables", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")

    # 创建项目管理子目录
    pm_dirs = ["schedules", "resources", "risks", "meeting_notes"]
    for dir_name in pm_dirs:
        dir_path = os.path.join(project_path, "project_management", dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            safe_print(f"创建目录: {dir_path}")

def create_readme(project_path, project_name, project_type, description, version):
    """创建README文件"""
    readme_path = os.path.join(project_path, "README.md")
    if not os.path.exists(readme_path):
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(f"# {project_name}\n\n")
            f.write(f"## 项目概述\n\n")
            f.write(f"这是一个{PROJECT_TYPES[project_type]['name']}，使用单层级产品目录框架进行管理。\n\n")
            if description:
                f.write(f"### 项目描述\n\n{description}\n\n")
            f.write(f"### 版本信息\n\n- 当前版本: {version}\n")
            f.write(f"- 创建日期: {datetime.now().strftime('%Y-%m-%d')}\n\n")
            f.write("## 目录结构\n\n")
            f.write("- `product_info/`: 产品基本信息\n")
            f.write("- `requirements/`: 需求相关文档\n")
            f.write("- `design/`: 设计方案文档\n")
            f.write("- `development/`: 开发实施文档\n")
            f.write("- `quality/`: 测试验证文档\n")
            f.write("- `production/`: 生产相关文档\n")
            f.write("- `deliverables/`: 交付物文档\n")
            f.write("- `project_management/`: 项目管理文档\n")
            f.write("- `scripts/`: 脚本工具\n")
            f.write("- `config/`: 配置文件\n")
            f.write("- `reports/`: 报告输出\n\n")
            f.write("## 使用指南\n\n")
            f.write("### 创建新文档\n\n")
            f.write("```bash\n")
            f.write("python scripts/create_document.py --component REQ --type SPEC --name \"产品需求规格\"\n")
            f.write("```\n\n")
            f.write("### 更新ID表格\n\n")
            f.write("```bash\n")
            f.write("python scripts/auto_index_manager.py --component REQ\n")
            f.write("```\n\n")
            f.write("### 生成关系图\n\n")
            f.write("```bash\n")
            f.write("python scripts/generate_relation_graph.py --output reports/relation_graph.png\n")
            f.write("```\n\n")
            f.write("## 更多信息\n\n")
            f.write("详细的框架说明请参考 `单层级产品目录框架.md` 文档。\n")
        safe_print(f"创建文件: {readme_path}")

def create_canvas_file(project_path):
    """创建product.canvas文件"""
    canvas_path = os.path.join(project_path, "product.canvas")
    if not os.path.exists(canvas_path):
        # 创建初始的Canvas结构
        initial_canvas = {
            "nodes": [],
            "edges": []
        }

        try:
            with open(canvas_path, 'w', encoding='utf-8') as f:
                json.dump(initial_canvas, f, ensure_ascii=False, indent=2)
            safe_print(f"创建文件: {canvas_path}")
        except Exception as e:
            safe_print(f"创建Canvas文件失败: {canvas_path}, 错误: {e}")

def initialize_project_structure(name, project_type="full", path=".", description=None, version="0.1.0", json_output=False):
    """
    初始化项目结构的核心函数（模块化版本）

    Args:
        name: 产品族名称
        project_type: 项目类型
        path: 项目路径
        description: 产品描述
        version: 初始版本号
        json_output: 是否以JSON格式输出

    Returns:
        dict: 包含创建结果的字典
    """
    # 创建项目目录
    project_path = os.path.abspath(path)
    if not os.path.exists(project_path):
        os.makedirs(project_path)

    # 创建完整项目路径
    full_project_path = os.path.join(project_path, name)

    # 设置静默模式
    global SILENT_MODE
    SILENT_MODE = json_output

    if not json_output:
        print(f"开始初始化{PROJECT_TYPES[project_type]['name']}...")

    # 记录创建的目录和文件
    created_dirs = []
    created_files = []

    # 创建目录结构
    create_directory_structure(project_type, full_project_path)

    # 创建README
    readme_path = os.path.join(full_project_path, "README.md")
    create_readme(full_project_path, name, project_type, description, version)
    created_files.append(readme_path)

    # 创建Canvas文件
    canvas_path = os.path.join(full_project_path, "product.canvas")
    create_canvas_file(full_project_path)
    created_files.append(canvas_path)

    # 收集创建的目录信息
    for root, dirs, files in os.walk(full_project_path):
        for dir_name in dirs:
            created_dirs.append(os.path.join(root, dir_name))

    # 构建结果
    result = {
        "success": True,
        "project_name": name,
        "project_path": full_project_path,
        "structure_type": project_type,
        "created_directories": created_dirs[:10],  # 限制输出数量
        "created_files": created_files,
        "creation_time": datetime.now().isoformat()
    }

    if json_output:
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        safe_print("初始化完成！")
        safe_print("请查看 README.md 获取使用指南。")
        safe_print("使用 python scripts/canvas/auto_link_documents.py 同步文档到Canvas。")

    return result

def main():
    """主函数（CLI入口）"""
    parser = argparse.ArgumentParser(description="项目结构初始化工具")
    parser.add_argument("--name", required=True, help="产品族名称")
    parser.add_argument("--type", default="full", choices=PROJECT_TYPES.keys(),
                       help="项目类型，可选值包括：hardware, software, firmware, hardware+software, hardware+firmware, firmware+software, full")
    parser.add_argument("--path", default=".", help="项目路径，默认为当前目录")
    parser.add_argument("--description", help="产品描述")
    parser.add_argument("--version", default="0.1.0", help="初始版本号，默认为0.1.0")
    parser.add_argument("--json", action="store_true", help="以JSON格式输出结果")

    args = parser.parse_args()

    # 调用核心函数
    result = initialize_project_structure(
        name=args.name,
        project_type=args.type,
        path=args.path,
        description=args.description,
        version=args.version,
        json_output=args.json
    )

    return 0 if result["success"] else 1

if __name__ == "__main__":
    sys.exit(main()) 