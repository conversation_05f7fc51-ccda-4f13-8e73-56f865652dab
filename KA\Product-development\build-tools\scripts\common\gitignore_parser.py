#!/usr/bin/env python3
"""
Git风格的文档忽略规则解析器
类似.gitignore的语法，用于文档扫描过滤
"""

import re
import fnmatch
from pathlib import Path
from typing import List, Optional


class DocIgnoreParser:
    """文档忽略规则解析器 - 类似git的.gitignore"""
    
    def __init__(self, project_path: Path, ignore_file: str = ".docignore", rules_list: Optional[List[str]] = None):
        """
        初始化解析器

        Args:
            project_path: 项目根路径
            ignore_file: 忽略规则文件名
            rules_list: 可选的规则列表，如果提供则直接使用
        """
        self.project_path = Path(project_path)
        self.ignore_file = self.project_path / ignore_file
        self.rules = []

        if rules_list is not None:
            self._parse_rules_from_list(rules_list)
        else:
            self.load_rules()

    @classmethod
    def from_rules(cls, rules_list: List[str], project_path: Path):
        """
        从规则列表创建解析器实例

        Args:
            rules_list: Git风格的规则列表
            project_path: 项目根目录路径

        Returns:
            DocIgnoreParser实例
        """
        return cls(project_path, rules_list=rules_list)

    def _parse_rules_from_list(self, rules_list: List[str]):
        """从规则列表解析规则"""
        self.rules = []

        for line_num, line in enumerate(rules_list, 1):
            line = line.strip()

            # 跳过空行和注释
            if not line or line.startswith('#'):
                continue

            # 解析规则
            rule = self._parse_rule(line, line_num)
            if rule:
                self.rules.append(rule)

        print(f"✓ 已从配置加载 {len(self.rules)} 条Git风格文档扫描规则")

    def load_rules(self):
        """加载忽略规则"""
        self.rules = []
        
        if not self.ignore_file.exists():
            print(f"忽略规则文件不存在: {self.ignore_file}")
            return
        
        try:
            with open(self.ignore_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳过空行和注释
                if not line or line.startswith('#'):
                    continue
                
                # 解析规则
                rule = self._parse_rule(line, line_num)
                if rule:
                    self.rules.append(rule)
            
            print(f"✓ 已加载 {len(self.rules)} 条文档忽略规则")
            
        except Exception as e:
            print(f"加载忽略规则失败: {e}")
    
    def _parse_rule(self, line: str, line_num: int) -> dict:
        """
        解析单条规则
        
        Args:
            line: 规则行内容
            line_num: 行号
            
        Returns:
            dict: 解析后的规则信息
        """
        # 检查是否是否定规则（以!开头）
        is_negation = line.startswith('!')
        if is_negation:
            line = line[1:]  # 移除!前缀
        
        # 检查是否是目录规则（以/结尾）
        is_directory = line.endswith('/')
        if is_directory:
            line = line[:-1]  # 移除/后缀
        
        # 检查是否是绝对路径（以/开头）
        is_absolute = line.startswith('/')
        if is_absolute:
            line = line[1:]  # 移除/前缀
        
        # 转换为正则表达式模式
        pattern = self._glob_to_regex(line)
        
        return {
            'original': line,
            'pattern': pattern,
            'is_negation': is_negation,
            'is_directory': is_directory,
            'is_absolute': is_absolute,
            'line_num': line_num
        }
    
    def _glob_to_regex(self, pattern: str) -> str:
        """
        将glob模式转换为正则表达式
        
        Args:
            pattern: glob模式
            
        Returns:
            str: 正则表达式模式
        """
        # 转义特殊字符，但保留通配符
        pattern = re.escape(pattern)
        
        # 恢复通配符
        pattern = pattern.replace(r'\*\*', '.*')  # ** 匹配任意路径
        pattern = pattern.replace(r'\*', '[^/]*')  # * 匹配除/外的任意字符
        pattern = pattern.replace(r'\?', '[^/]')   # ? 匹配除/外的单个字符
        
        return pattern
    
    def should_ignore(self, file_path: Path) -> bool:
        """
        检查文件是否应该被忽略
        
        Args:
            file_path: 文件路径（相对于项目根目录）
            
        Returns:
            bool: True表示应该忽略
        """
        # 转换为相对路径字符串
        try:
            rel_path = file_path.relative_to(self.project_path)
            path_str = str(rel_path).replace('\\', '/')
        except ValueError:
            # 如果不是相对路径，直接使用原路径
            path_str = str(file_path).replace('\\', '/')
        
        is_ignored = False
        
        # 按顺序应用规则
        for rule in self.rules:
            if self._match_rule(path_str, file_path.is_dir(), rule):
                if rule['is_negation']:
                    is_ignored = False  # 否定规则：不忽略
                else:
                    is_ignored = True   # 普通规则：忽略
        
        return is_ignored
    
    def _match_rule(self, path_str: str, is_dir: bool, rule: dict) -> bool:
        """
        检查路径是否匹配规则
        
        Args:
            path_str: 路径字符串
            is_dir: 是否是目录
            rule: 规则信息
            
        Returns:
            bool: 是否匹配
        """
        # 如果规则指定了目录，但文件不是目录，则不匹配
        if rule['is_directory'] and not is_dir:
            return False
        
        pattern = rule['pattern']
        
        if rule['is_absolute']:
            # 绝对路径：从根目录开始匹配
            return re.match(f"^{pattern}$", path_str) is not None
        else:
            # 相对路径：可以匹配路径中的任意部分
            # 检查完整路径匹配
            if re.match(f"^{pattern}$", path_str):
                return True
            
            # 检查路径组件匹配
            path_parts = path_str.split('/')
            for i in range(len(path_parts)):
                sub_path = '/'.join(path_parts[i:])
                if re.match(f"^{pattern}$", sub_path):
                    return True
        
        return False
    
    def get_matching_rules(self, file_path: Path) -> List[dict]:
        """
        获取匹配指定文件的所有规则
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[dict]: 匹配的规则列表
        """
        try:
            rel_path = file_path.relative_to(self.project_path)
            path_str = str(rel_path).replace('\\', '/')
        except ValueError:
            path_str = str(file_path).replace('\\', '/')
        
        matching_rules = []
        
        for rule in self.rules:
            if self._match_rule(path_str, file_path.is_dir(), rule):
                matching_rules.append(rule)
        
        return matching_rules
    
    def debug_path(self, file_path: Path):
        """
        调试指定路径的匹配情况
        
        Args:
            file_path: 要调试的文件路径
        """
        print(f"\n=== 调试路径: {file_path} ===")
        print(f"是否忽略: {self.should_ignore(file_path)}")
        
        matching_rules = self.get_matching_rules(file_path)
        if matching_rules:
            print("匹配的规则:")
            for rule in matching_rules:
                action = "排除" if not rule['is_negation'] else "包含"
                print(f"  行{rule['line_num']}: {rule['original']} ({action})")
        else:
            print("没有匹配的规则")
