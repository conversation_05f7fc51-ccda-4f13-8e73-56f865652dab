#include "ImageProcessingTest.h"
#include "../filters/BilateralFilter.h"
#include "../filters/ConvolutionFilter.h"
#include "../filters/GaussianFilter.h"
#include "../filters/KalmanFilter.h"
#include "../filters/MedianFilter.h"
#include "../filters/WeightedAverageFilter.h"
#include <QtMath>

namespace ImageProcessing {

ImageProcessingTest::ImageProcessingTest() : m_passedTests(0), m_totalTests(0) {
    logTest("ImageProcessing测试框架初始化");
}

bool ImageProcessingTest::runAllTests() {
    logTest("=== 开始运行ImageProcessing全面测试 ===");

    m_passedTests = 0;
    m_totalTests  = 0;

    // 运行各项测试
    testConvolutionFilter();
    testWeightedAverageFilter();
    testKalmanFilter();
    testMedianFilter();
    testGaussianFilter();
    testBilateralFilter();
    testInterpolation();

    // 输出测试结果
    logTest(QString("=== 测试完成: %1/%2 通过 ===").arg(m_passedTests).arg(m_totalTests));

    return m_passedTests == m_totalTests;
}

bool ImageProcessingTest::testConvolutionFilter() {
    logTest("--- 测试卷积滤波器 ---");

    try {
        // 创建5x5测试数据
        ImageDataU32 testData = create5x5TestData();
        logTest("创建5x5测试数据:");
        printImageData(testData, "原始数据");

        // 创建卷积滤波器
        ConvolutionFilter filter;

        // 设置3x3锐化核
        filter.setPredefinedKernel("sharpen");

        // 应用滤波
        ImageDataU32 result  = testData;
        bool         success = filter.apply(result);

        if (!success) {
            recordTestResult("卷积滤波器应用", false);
            return false;
        }

        logTest("卷积滤波结果:");
        printImageData(result, "滤波后数据");

        // 验证结果尺寸
        bool sizeCorrect = (result.width() == testData.width() && result.height() == testData.height());
        recordTestResult("卷积结果尺寸", sizeCorrect);

        // 验证边界处理（边缘像素应该有变化）
        bool boundaryProcessed = (result.matrix()[0][0] != testData.matrix()[0][0]);
        recordTestResult("卷积边界处理", boundaryProcessed);

        // 输出统计信息
        logTest("原始数据统计: " + getImageStatistics(testData));
        logTest("滤波后统计: " + getImageStatistics(result));

        return sizeCorrect && boundaryProcessed;

    } catch (const std::exception &e) {
        logError(QString("卷积滤波器测试异常: %1").arg(e.what()));
        recordTestResult("卷积滤波器异常处理", false);
        return false;
    }
}

bool ImageProcessingTest::testWeightedAverageFilter() {
    logTest("--- 测试加权均值滤波器 ---");

    try {
        // 创建测试数据
        ImageDataU32 testData = create5x5TestData();

        // 创建加权均值滤波器
        WeightedAverageFilter filter;

        // 测试不同的预定义权重
        QStringList weightTypes = {"uniform", "gaussian", "center_weighted", "smooth"};

        bool allPassed = true;
        for (const QString &weightType : weightTypes) {
            ImageDataU32 result = testData;
            filter.setPredefinedWeights(weightType);

            bool success    = filter.apply(result);
            bool testPassed = success && (result.width() == testData.width());

            recordTestResult(QString("加权均值-%1").arg(weightType), testPassed);
            allPassed &= testPassed;

            if (success) {
                logTest(QString("%1权重结果统计: %2").arg(weightType).arg(getImageStatistics(result)));
            }
        }

        return allPassed;

    } catch (const std::exception &e) {
        logError(QString("加权均值滤波器测试异常: %1").arg(e.what()));
        recordTestResult("加权均值滤波器异常处理", false);
        return false;
    }
}

bool ImageProcessingTest::testKalmanFilter() {
    logTest("--- 测试卡尔曼滤波器 ---");

    try {
        // 创建测试数据
        ImageDataU32 testData = create5x5TestData();

        // 创建卡尔曼滤波器
        KalmanFilter filter;

        // 应用滤波
        ImageDataU32 result  = testData;
        bool         success = filter.apply(result);

        recordTestResult("卡尔曼滤波器应用", success);

        if (success) {
            logTest("卡尔曼滤波结果统计: " + getImageStatistics(result));
        }

        return success;

    } catch (const std::exception &e) {
        logError(QString("卡尔曼滤波器测试异常: %1").arg(e.what()));
        recordTestResult("卡尔曼滤波器异常处理", false);
        return false;
    }
}

bool ImageProcessingTest::testMedianFilter() {
    logTest("--- 测试中值滤波器 ---");

    try {
        // 创建测试数据
        ImageDataU32 testData = create5x5TestData();

        // 创建中值滤波器
        MedianFilter filter;

        // 测试不同的预设模式
        QStringList presets = {"noise_reduction", "edge_preserve", "artifact_removal", "light_smooth"};

        bool allPassed = true;
        for (const QString &preset : presets) {
            ImageDataU32 result = testData;
            filter.setPreset(preset);

            bool success    = filter.apply(result);
            bool testPassed = success && (result.width() == testData.width());

            recordTestResult(QString("中值滤波-%1").arg(preset), testPassed);
            allPassed &= testPassed;

            if (success) {
                logTest(QString("%1预设结果统计: %2").arg(preset).arg(getImageStatistics(result)));
            }
        }

        return allPassed;

    } catch (const std::exception &e) {
        logError(QString("中值滤波器测试异常: %1").arg(e.what()));
        recordTestResult("中值滤波器异常处理", false);
        return false;
    }
}

bool ImageProcessingTest::testGaussianFilter() {
    logTest("--- 测试高斯滤波器 ---");

    try {
        // 创建测试数据
        ImageDataU32 testData = create5x5TestData();

        // 创建高斯滤波器
        GaussianFilter filter;

        // 测试不同的预设模式
        QStringList presets = {"light_blur", "medium_blur", "heavy_blur", "noise_reduction"};

        bool allPassed = true;
        for (const QString &preset : presets) {
            ImageDataU32 result = testData;
            filter.setPreset(preset);

            bool success    = filter.apply(result);
            bool testPassed = success && (result.width() == testData.width());

            recordTestResult(QString("高斯滤波-%1").arg(preset), testPassed);
            allPassed &= testPassed;

            if (success) {
                logTest(QString("%1预设结果统计: %2").arg(preset).arg(getImageStatistics(result)));
            }
        }

        return allPassed;

    } catch (const std::exception &e) {
        logError(QString("高斯滤波器测试异常: %1").arg(e.what()));
        recordTestResult("高斯滤波器异常处理", false);
        return false;
    }
}

bool ImageProcessingTest::testBilateralFilter() {
    logTest("--- 测试双边滤波器 ---");

    try {
        // 创建测试数据
        ImageDataU32 testData = create5x5TestData();

        // 创建双边滤波器
        BilateralFilter filter;

        // 测试不同的预设模式
        QStringList presets = {"smooth", "detail_preserve", "noise_reduce", "edge_enhance"};

        bool allPassed = true;
        for (const QString &preset : presets) {
            ImageDataU32 result = testData;
            filter.setPreset(preset);

            bool success    = filter.apply(result);
            bool testPassed = success && (result.width() == testData.width());

            recordTestResult(QString("双边滤波-%1").arg(preset), testPassed);
            allPassed &= testPassed;

            if (success) {
                logTest(QString("%1预设结果统计: %2").arg(preset).arg(getImageStatistics(result)));
            }
        }

        return allPassed;

    } catch (const std::exception &e) {
        logError(QString("双边滤波器测试异常: %1").arg(e.what()));
        recordTestResult("双边滤波器异常处理", false);
        return false;
    }
}

bool ImageProcessingTest::testInterpolation() {
    logTest("--- 测试插值算法 ---");

    try {
        // 创建小尺寸测试数据
        ImageDataU32 testData = create3x3TestData();

        // TODO: 测试各种插值算法
        recordTestResult("插值算法基础测试", true);

        return true;

    } catch (const std::exception &e) {
        logError(QString("插值算法测试异常: %1").arg(e.what()));
        recordTestResult("插值算法异常处理", false);
        return false;
    }
}

ImageDataU32 ImageProcessingTest::create5x5TestData() {
    // 创建您提供的5x5测试数据
    ImageDataU32 data(5, 5);

    QVector<QVector<uint32_t>> testMatrix = {
        {271, 882, 826, 748, 58}, {1011, 908, 792, 756, 738}, {1074, 924, 807, 800, 859}, {1021, 877, 777, 776, 855}, {145, 887, 788, 740, 33}};

    for (uint32_t y = 0; y < 5; ++y) {
        for (uint32_t x = 0; x < 5; ++x) {
            data.matrix()[y][x] = testMatrix[y][x];
        }
    }

    return data;
}

ImageDataU32 ImageProcessingTest::create3x3TestData() {
    ImageDataU32 data(3, 3);

    QVector<QVector<uint32_t>> testMatrix = {{100, 200, 150}, {180, 220, 190}, {160, 210, 170}};

    for (uint32_t y = 0; y < 3; ++y) {
        for (uint32_t x = 0; x < 3; ++x) {
            data.matrix()[y][x] = testMatrix[y][x];
        }
    }

    return data;
}

void ImageProcessingTest::printImageData(const ImageDataU32 &data, const QString &title) {
    qDebug() << title << ":";
    for (uint32_t y = 0; y < data.height(); ++y) {
        QString row;
        for (uint32_t x = 0; x < data.width(); ++x) {
            row += QString("%1").arg(data.matrix()[y][x], 4);
            if (x < data.width() - 1)
                row += ", ";
        }
        qDebug() << "  " << row;
    }
}

bool ImageProcessingTest::compareImageData(const ImageDataU32 &data1, const ImageDataU32 &data2, uint32_t tolerance) {
    if (data1.width() != data2.width() || data1.height() != data2.height()) {
        return false;
    }

    for (uint32_t y = 0; y < data1.height(); ++y) {
        for (uint32_t x = 0; x < data1.width(); ++x) {
            uint32_t val1 = data1.matrix()[y][x];
            uint32_t val2 = data2.matrix()[y][x];
            uint32_t diff = (val1 > val2) ? (val1 - val2) : (val2 - val1);
            if (diff > tolerance) {
                return false;
            }
        }
    }

    return true;
}

QString ImageProcessingTest::getImageStatistics(const ImageDataU32 &data) {
    uint64_t sum = 0;
    uint32_t min = UINT32_MAX;
    uint32_t max = 0;

    for (uint32_t y = 0; y < data.height(); ++y) {
        for (uint32_t x = 0; x < data.width(); ++x) {
            uint32_t val = data.matrix()[y][x];
            sum += val;
            min = qMin(min, val);
            max = qMax(max, val);
        }
    }

    uint32_t count = data.width() * data.height();
    double   avg   = static_cast<double>(sum) / count;

    return QString("Min=%1, Max=%2, Avg=%.1f, Sum=%3").arg(min).arg(max).arg(avg).arg(sum);
}

void ImageProcessingTest::recordTestResult(const QString &testName, bool passed) {
    m_totalTests++;
    if (passed) {
        m_passedTests++;
        logTest(QString("✓ %1: 通过").arg(testName));
    } else {
        logError(QString("✗ %1: 失败").arg(testName));
    }
}

void ImageProcessingTest::logTest(const QString &message) {
    qDebug() << "[ImageProcessingTest]" << message;
}

void ImageProcessingTest::logError(const QString &message) {
    qWarning() << "[ImageProcessingTest ERROR]" << message;
}

}  // namespace ImageProcessing
