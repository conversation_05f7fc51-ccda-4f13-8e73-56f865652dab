#!/usr/bin/env python3
"""
配置流程工具 - FastMCP原生实现
包含配置加载、保存、验证、合并、模板创建、备份等功能
对应工具：T28相关的6个配置管理工具

使用FastMCP原生的Annotated + Field方式定义参数
集成智能参数处理系统
"""

import json
import os
import subprocess
import sys
import shutil
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Annotated, Union
from pydantic import Field

# 导入智能参数处理工具
try:
    from .fastmcp_param_utils import smart_param_helper
except ImportError:
    try:
        from fastmcp_param_utils import smart_param_helper
    except ImportError:
        # 提供简单的替代实现
        class SimpleParamHelper:
            async def process_params(self, ctx, tool_name, params):
                return params
            def cache_successful_params(self, tool_name, params):
                pass
        smart_param_helper = SimpleParamHelper()

# 导入脚本执行函数
try:
    from .project_lifecycle import run_script_with_subprocess
except ImportError:
    try:
        from project_lifecycle import run_script_with_subprocess
    except ImportError:
        # 如果无法导入，使用本地的run_script_silently
        run_script_with_subprocess = None

# 设置脚本基础路径
SCRIPTS_BASE = Path(__file__).parent.parent.parent / "scripts"
# 定义项目根目录 - 统一路径解析基准
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent  # 项目根目录
DEBUG_MODE = False  # 调试模式：True=模拟执行，False=真实执行

def _get_vscode_workspace_folder() -> Optional[str]:
    """
    动态获取VSCode工作区文件夹
    支持多种方式获取当前工作区路径

    Returns:
        Optional[str]: VSCode工作区路径，如果无法获取则返回None
    """
    # 方式1: 环境变量（用户手动设置）
    workspace_folder = os.environ.get('VSCODE_WORKSPACE_FOLDER')
    if workspace_folder and Path(workspace_folder).exists():
        return workspace_folder

    # 方式2: 尝试从VSCode进程信息获取
    try:
        import psutil
        current_pid = os.getpid()
        current_process = psutil.Process(current_pid)

        # 向上查找VSCode进程
        parent = current_process.parent()
        while parent:
            if 'code' in parent.name().lower() or 'vscode' in parent.name().lower():
                # 尝试从VSCode进程的工作目录获取
                try:
                    cwd = parent.cwd()
                    if cwd and Path(cwd).exists():
                        return cwd
                except:
                    pass
            parent = parent.parent()
    except:
        pass

    # 方式3: 尝试从当前工作目录的.vscode文件夹推断
    current_dir = Path.cwd()
    while current_dir.parent != current_dir:  # 避免无限循环
        if (current_dir / '.vscode').exists():
            return str(current_dir)
        current_dir = current_dir.parent

    # 方式4: 检查常见的项目标识文件
    current_dir = Path.cwd()
    project_indicators = ['.git', 'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod']
    while current_dir.parent != current_dir:
        for indicator in project_indicators:
            if (current_dir / indicator).exists():
                return str(current_dir)
        current_dir = current_dir.parent

    return None

def _resolve_project_path(project_path: str) -> Path:
    """
    健壮的项目路径解析函数
    支持多种环境和IDE的路径解析策略

    Args:
        project_path: 用户输入的项目路径

    Returns:
        Path: 解析后的绝对路径

    Raises:
        ValueError: 如果路径无法解析或不存在
    """
    # 策略1: 如果是绝对路径，直接使用
    if os.path.isabs(project_path):
        path_obj = Path(project_path)
        if path_obj.exists():
            return path_obj
        else:
            raise ValueError(f"绝对路径不存在: {project_path}")

    # 策略2: 尝试使用VSCode工作区变量（动态获取）
    workspace_folder = _get_vscode_workspace_folder()
    if workspace_folder:
        path_obj = Path(workspace_folder) / project_path
        if path_obj.exists():
            return path_obj

    # 策略3: 尝试使用当前工作目录
    try:
        path_obj = Path.cwd() / project_path
        if path_obj.exists():
            return path_obj
    except Exception:
        pass

    # 策略4: 尝试使用PROJECT_ROOT（兼容性）
    try:
        path_obj = PROJECT_ROOT / project_path
        if path_obj.exists():
            return path_obj
    except Exception:
        pass

    # 策略5: 如果所有策略都失败，抛出详细错误并建议使用绝对路径
    raise ValueError(f"""
无法解析项目路径: {project_path}

尝试的解析策略:
1. 绝对路径: {'✓' if os.path.isabs(project_path) else '✗'}
2. VSCode工作区: {'✓' if workspace_folder else '✗'} ({workspace_folder or 'N/A'})
3. 当前工作目录: {Path.cwd()}
4. MCP服务器根目录: {PROJECT_ROOT}

建议解决方案:
1. 使用绝对路径，例如: 
2. 设置VSCODE_WORKSPACE_FOLDER环境变量
3. 确保项目路径存在且可访问

当前环境信息:
- 当前工作目录: {Path.cwd()}
- MCP服务器根目录: {PROJECT_ROOT}
- VSCode工作区: {workspace_folder or 'N/A'}
""")

def run_script_silently(script_path: Path, args: Optional[List[str]] = None, cwd: Optional[str] = None) -> Dict[str, Any]:
    """
    静默运行脚本并返回结果

    Args:
        script_path: 脚本路径
        args: 脚本参数
        cwd: 工作目录

    Returns:
        Dict: 包含success, stdout, stderr的结果字典
    """
    if DEBUG_MODE:
        # 调试模式：返回模拟结果
        return {
            "success": True,
            "stdout": json.dumps({
                "success": True,
                "message": f"[调试模式] 模拟执行脚本: {script_path.name}",
                "script_path": str(script_path),
                "args": args or [],
                "simulated": True
            }, ensure_ascii=False, indent=2),
            "stderr": "",
            "returncode": 0
        }

    try:
        cmd = [sys.executable, str(script_path)]
        if args:
            cmd.extend(args)

        # 修改subprocess调用方式，避免capture_output死锁
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=cwd or str(PROJECT_ROOT)  # 使用项目根目录作为工作目录
        )

        try:
            stdout, stderr = process.communicate(timeout=60)
            returncode = process.returncode
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
            raise subprocess.TimeoutExpired(cmd, 60)

        return {
            "success": returncode == 0,
            "stdout": stdout,
            "stderr": stderr,
            "returncode": returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "脚本执行超时",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"脚本执行异常: {str(e)}",
            "returncode": -1
        }

async def load_config(
    ctx,
    config_file: Annotated[str, Field(description="配置文件路径")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    config_type: Annotated[str, Field(description="配置类型，可选值：auto（自动检测）, json, yaml, toml")] = "auto",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    配置加载工具
    从指定文件加载配置，支持JSON/YAML/TOML格式，返回配置内容和验证结果
    
    Args:
        ctx: FastMCP Context对象
        config_file: 配置文件路径
        project_path: 项目路径
        config_type: 配置类型
        kwargs: 其他参数
    
    Returns:
        Dict: 包含配置加载结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "load_config", {
            "config_file": config_file,
            "project_path": project_path,
            "config_type": config_type,
            "kwargs": kwargs
        })
        
        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}
        
        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }
        
        # 解析配置文件路径
        if Path(params["config_file"]).is_absolute():
            config_file_path = Path(params["config_file"])
        else:
            config_file_path = project_path_obj / params["config_file"]
        
        if not config_file_path.exists():
            return {
                "success": False,
                "error": f"配置文件不存在: {config_file_path}",
                "config_file": str(config_file_path)
            }
        
        # 调用脚本 - 路径配置参考关联表T28行Scripts链接列
        script_path = SCRIPTS_BASE / "config" / "load_config.py"
        
        if script_path.exists():
            # 准备脚本参数
            script_args = [
                str(config_file_path),
                params["project_path"],
                params["config_type"],
                json.dumps(kwargs_dict)
            ]
            
            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )
            
            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])
                    
                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("load_config", params)
                    
                    return {
                        "success": True,
                        "data": output_data,
                        "config_file": str(config_file_path),
                        "metadata": {
                            "tool_name": "load_config",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "load_config",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "load_config",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"配置加载时发生错误: {str(e)}",
            "config_file": config_file,
            "metadata": {
                "tool_name": "load_config",
                "execution_time": "< 1s"
            }
        }

async def save_config(
    ctx,
    config_data: Annotated[Union[str, Dict[str, Any]], Field(description="配置数据，JSON格式字符串、字典对象或文件路径")],
    output_file: Annotated[str, Field(description="输出文件路径")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    config_format: Annotated[str, Field(description="配置格式，可选值：json, yaml, toml")] = "json",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    配置保存工具
    将配置数据保存到指定文件，支持格式转换，返回保存路径和文件信息

    Args:
        ctx: FastMCP Context对象
        config_data: 配置数据
        output_file: 输出文件路径
        project_path: 项目路径
        config_format: 配置格式
        kwargs: 其他参数

    Returns:
        Dict: 包含配置保存结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "save_config", {
            "config_data": config_data,
            "output_file": output_file,
            "project_path": project_path,
            "config_format": config_format,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 解析输出文件路径
        if Path(params["output_file"]).is_absolute():
            output_file_path = Path(params["output_file"])
        else:
            output_file_path = project_path_obj / params["output_file"]

        # 确保输出目录存在
        output_file_path.parent.mkdir(parents=True, exist_ok=True)

        # 处理config_data - 支持字典、字符串或文件路径
        config_data_str = params["config_data"]
        if isinstance(params["config_data"], dict):
            # 如果是字典，转换为JSON字符串
            config_data_str = json.dumps(params["config_data"], ensure_ascii=False, indent=2)
        elif isinstance(params["config_data"], str):
            # 如果是字符串，检查是否是文件路径
            if Path(params["config_data"]).exists():
                # 是文件路径，读取文件内容
                with open(params["config_data"], 'r', encoding='utf-8') as f:
                    config_data_str = f.read()
            else:
                # 是JSON字符串，直接使用
                config_data_str = params["config_data"]

        # 调用脚本 - 路径配置参考关联表T28行Scripts链接列
        script_path = SCRIPTS_BASE / "config" / "save_config.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                config_data_str,
                str(output_file_path),
                params["project_path"],
                params["config_format"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("save_config", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "output_file": str(output_file_path),
                        "metadata": {
                            "tool_name": "save_config",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "save_config",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "save_config",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"配置保存时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "save_config",
                "execution_time": "< 1s"
            }
        }



async def validate_config(
    ctx,
    config_file: Annotated[str, Field(description="配置文件路径")],
    schema_file: Annotated[Optional[str], Field(description="模式文件路径，可选")] = None,
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    配置验证工具
    验证配置文件格式和内容，检查必需字段，返回验证结果和错误列表

    Args:
        ctx: FastMCP Context对象
        config_file: 配置文件路径
        schema_file: 模式文件路径
        project_path: 项目路径
        kwargs: 其他参数

    Returns:
        Dict: 包含配置验证结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "validate_config", {
            "config_file": config_file,
            "schema_file": schema_file,
            "project_path": project_path,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 解析配置文件路径
        if Path(params["config_file"]).is_absolute():
            config_file_path = Path(params["config_file"])
        else:
            config_file_path = project_path_obj / params["config_file"]

        if not config_file_path.exists():
            return {
                "success": False,
                "error": f"配置文件不存在: {config_file_path}",
                "config_file": str(config_file_path)
            }

        # 调用脚本 - 路径配置参考关联表T28行Scripts链接列
        script_path = SCRIPTS_BASE / "config" / "validate_config.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                str(config_file_path),
                params["schema_file"] or "",
                params["project_path"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("validate_config", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "config_file": str(config_file_path),
                        "metadata": {
                            "tool_name": "validate_config",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "validate_config",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "validate_config",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"配置验证时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "validate_config",
                "execution_time": "< 1s"
            }
        }


async def merge_configs(
    ctx,
    config_files: Annotated[List[str], Field(description="配置文件列表")],
    output_file: Annotated[str, Field(description="输出文件路径")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    merge_strategy: Annotated[str, Field(description="合并策略，可选值：deep（深度合并）, shallow（浅合并）, override（覆盖）")] = "deep",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    配置合并工具
    合并多个配置文件，处理冲突，返回合并后的配置和冲突报告

    Args:
        ctx: FastMCP Context对象
        config_files: 配置文件列表
        output_file: 输出文件路径
        project_path: 项目路径
        merge_strategy: 合并策略
        kwargs: 其他参数

    Returns:
        Dict: 包含配置合并结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "merge_configs", {
            "config_files": config_files,
            "output_file": output_file,
            "project_path": project_path,
            "merge_strategy": merge_strategy,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 验证配置文件存在
        config_file_paths = []
        for config_file in params["config_files"]:
            if Path(config_file).is_absolute():
                config_path = Path(config_file)
            else:
                config_path = project_path_obj / config_file

            if not config_path.exists():
                return {
                    "success": False,
                    "error": f"配置文件不存在: {config_path}",
                    "config_file": str(config_path)
                }
            config_file_paths.append(config_path)

        # 解析输出文件路径
        if Path(params["output_file"]).is_absolute():
            output_file_path = Path(params["output_file"])
        else:
            output_file_path = project_path_obj / params["output_file"]

        # 确保输出目录存在
        output_file_path.parent.mkdir(parents=True, exist_ok=True)

        # 调用脚本 - 路径配置参考关联表T28行Scripts链接列
        script_path = SCRIPTS_BASE / "config" / "merge_configs.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                json.dumps([str(p) for p in config_file_paths]),
                str(output_file_path),
                params["project_path"],
                params["merge_strategy"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("merge_configs", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "output_file": str(output_file_path),
                        "metadata": {
                            "tool_name": "merge_configs",
                            "execution_time": "< 10s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "merge_configs",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "merge_configs",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"配置合并时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "merge_configs",
                "execution_time": "< 1s"
            }
        }


async def create_config_template(
    ctx,
    template_type: Annotated[str, Field(description="模板类型，可选值：document_links（文档关联）, workflow（工作流）, traceability（追溯）, deliverables（交付物）, production（生产）, quality（质量）, development（开发）, design（设计）, requirements_analysis（需求分析）, requirements_import（需求导入）, all（全部）")],
    output_file: Annotated[str, Field(description="输出文件路径")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    template_params: Annotated[Optional[Dict[str, Any]], Field(description="模板参数字典")] = None,
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    配置模板创建工具
    创建标准配置模板，支持多种项目类型，返回模板文件路径和参数说明

    Args:
        ctx: FastMCP Context对象
        template_type: 模板类型
        output_file: 输出文件路径
        project_path: 项目路径
        template_params: 模板参数字典
        kwargs: 其他参数

    Returns:
        Dict: 包含模板创建结果的字典
    """
    try:
        # 简化参数处理 - 暂时禁用智能参数系统
        params = {
            "template_type": template_type,
            "output_file": output_file,
            "project_path": project_path,
            "template_params": template_params or {},
            "kwargs": kwargs
        }

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在 - 支持多种路径解析策略
        project_path_obj = _resolve_project_path(params["project_path"])

        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 解析输出文件路径
        if Path(params["output_file"]).is_absolute():
            output_file_path = Path(params["output_file"])
        else:
            output_file_path = project_path_obj / params["output_file"]

        # 确保输出目录存在
        output_file_path.parent.mkdir(parents=True, exist_ok=True)

        # 调用脚本 - 路径配置参考关联表T28行Scripts链接列
        script_path = SCRIPTS_BASE / "config" / "generate_all_configs.py"

        if script_path.exists():
            # 准备脚本参数 - 使用命令行参数格式
            script_args = [
                "--project-path", str(project_path_obj),  # 传递解析后的绝对路径
                "--project-type", "single_layer"  # 默认项目类型
            ]

            # 如果指定了特定配置类型，添加--config-types参数
            if params["template_type"] != "all":
                script_args.extend(["--config-types", params["template_type"]])

            # 使用与其他工具一致的脚本执行方式
            if run_script_with_subprocess:
                result = run_script_with_subprocess(script_path, script_args)
            else:
                result = run_script_silently(
                    script_path,
                    args=script_args,
                    cwd=str(Path.cwd())  # 使用当前工作目录而不是固定的PROJECT_ROOT
                )

            # 检查脚本是否成功或部分成功
            output_text = result.get('stdout', '') + result.get('stderr', '')
            has_success = result.get('success') or ("配置生成完成" in output_text or "✓" in output_text)

            if has_success:
                return {
                    "success": True,
                    "message": "配置模板创建完成",
                    "output": result.get('stdout', ''),
                    "output_file": str(output_file_path),
                    "project_path": params["project_path"],
                    "metadata": {
                        "tool_name": "create_config_template",
                        "execution_time": "< 5s",
                        "script_path": str(script_path)
                    }
                }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "create_config_template",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "create_config_template",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"模板创建时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "create_config_template",
                "execution_time": "< 1s"
            }
        }


async def backup_config(
    ctx,
    config_file: Annotated[str, Field(description="配置文件路径")],
    backup_dir: Annotated[Optional[str], Field(description="备份目录，可选")] = None,
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    配置备份工具
    备份配置文件，支持版本管理，返回备份文件路径和版本信息

    Args:
        ctx: FastMCP Context对象
        config_file: 配置文件路径
        backup_dir: 备份目录
        project_path: 项目路径
        kwargs: 其他参数

    Returns:
        Dict: 包含配置备份结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "backup_config", {
            "config_file": config_file,
            "backup_dir": backup_dir,
            "project_path": project_path,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 解析配置文件路径
        if Path(params["config_file"]).is_absolute():
            config_file_path = Path(params["config_file"])
        else:
            config_file_path = project_path_obj / params["config_file"]

        if not config_file_path.exists():
            return {
                "success": False,
                "error": f"配置文件不存在: {config_file_path}",
                "config_file": str(config_file_path)
            }

        # 确定备份目录
        if params["backup_dir"]:
            if Path(params["backup_dir"]).is_absolute():
                backup_dir_path = Path(params["backup_dir"])
            else:
                backup_dir_path = project_path_obj / params["backup_dir"]
        else:
            backup_dir_path = project_path_obj / "backups" / "configs"

        # 确保备份目录存在
        backup_dir_path.mkdir(parents=True, exist_ok=True)

        # 调用脚本 - 路径配置参考关联表T28行Scripts链接列
        script_path = SCRIPTS_BASE / "config" / "backup_config.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                str(config_file_path),
                str(backup_dir_path),
                params["project_path"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("backup_config", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "config_file": str(config_file_path),
                        "metadata": {
                            "tool_name": "backup_config",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "backup_config",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "backup_config",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"配置备份时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "backup_config",
                "execution_time": "< 1s"
            }
        }

