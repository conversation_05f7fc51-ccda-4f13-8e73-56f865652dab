<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>clenDataMes</class>
 <widget class="QDockWidget" name="clenDataMes">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>960</width>
    <height>640</height>
   </rect>
  </property>
  <property name="windowIcon">
   <iconset resource="sub_resource.qrc">
    <normaloff>:/icon/cspc1.jpg</normaloff>:/icon/cspc1.jpg</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QDockWidget
{
	background-color: rgb(255,255,255);
	
	font: 12pt &quot;黑体&quot;;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 6px;
}

QPushButton{
	min-width: 200px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}

QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}

</string>
  </property>
  <property name="windowTitle">
   <string>CSPC_光路耦合_返工</string>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <widget class="QTableView" name="mesDataShow">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>941</width>
      <height>421</height>
     </rect>
    </property>
   </widget>
   <widget class="QWidget" name="horizontalLayoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>560</y>
      <width>941</width>
      <height>41</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>工单号：</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="workOrder">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>302</width>
         <height>34</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>302</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QCheckBox" name="nbrFlag">
       <property name="text">
        <string>新标签</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QComboBox" name="portBox">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="reload">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>204</width>
         <height>39</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>reload</string>
       </property>
       <property name="shortcut">
        <string>Ctrl+Space</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
   <widget class="QTextEdit" name="processView">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>440</y>
      <width>941</width>
      <height>111</height>
     </rect>
    </property>
   </widget>
  </widget>
 </widget>
 <resources>
  <include location="sub_resource.qrc"/>
  <include location="sub_resource.qrc"/>
 </resources>
 <connections/>
</ui>
