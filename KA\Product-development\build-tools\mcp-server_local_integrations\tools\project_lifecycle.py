#!/usr/bin/env python3
"""
产品开发生命周期管理工具 - 简化版
只保留核心功能，移除所有复杂的日志和直接导入逻辑
"""

import os
import sys
import json
import subprocess  # 重新启用subprocess用于彻底隔离进程
import threading   # 重新启用threading用于避免管道阻塞
import queue       # 重新启用queue用于线程间通信
import runpy
import io
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
# 尝试不同的FastMCP导入方式
try:
    from fastmcp import FastMCP
    from fastmcp.utilities import create_server
except ImportError:
    try:
        from mcp.server.fastmcp import FastMCP
        # create_server 可能不存在于MCP SDK版本中
        create_server = None
    except ImportError:
        raise ImportError("FastMCP 模块不可用，请安装 fastmcp 包")
from pydantic import Field
from typing_extensions import Annotated

# 配置
SCRIPTS_BASE = Path(__file__).parent.parent.parent / "scripts"
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent  # 项目根目录
SCRIPT_TIMEOUT = 60  # 脚本执行超时时间60秒足够
DEBUG_MODE = False  # 已修复脚本中的subprocess调用，可以执行真实脚本

# 简单的日志记录器
import logging
logger = logging.getLogger(__name__)
logger.addHandler(logging.StreamHandler(sys.stderr))
logger.setLevel(logging.ERROR)

def run_script_with_runpy(script_path: Path, args: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    使用runpy.run_path()直接执行脚本，避免subprocess问题
    """
    if DEBUG_MODE:
        # 调试模式：返回模拟结果
        return {
            "success": True,
            "stdout": json.dumps({
                "success": True,
                "message": f"[调试模式] 模拟执行脚本: {script_path.name}",
                "script_path": str(script_path),
                "args": args or [],
                "simulated": True
            }, ensure_ascii=False, indent=2),
            "stderr": "",
            "returncode": 0
        }

    if not script_path.exists():
        return {
            "success": False,
            "stdout": "",
            "stderr": f"脚本文件不存在: {script_path}",
            "returncode": 1
        }

    try:
        # 保存原始sys.argv
        original_argv = sys.argv.copy()

        # 设置新的sys.argv
        new_argv = [str(script_path)]
        if args:
            new_argv.extend(args)
        sys.argv = new_argv

        # 捕获stdout和stderr
        original_stdout = sys.stdout
        original_stderr = sys.stderr

        captured_stdout = io.StringIO()
        captured_stderr = io.StringIO()

        sys.stdout = captured_stdout
        sys.stderr = captured_stderr

        try:
            # 使用runpy执行脚本
            logger.error(f"[DEBUG] 设置sys.argv: {sys.argv}")
            logger.error(f"[DEBUG] 开始执行模块...")

            runpy.run_path(str(script_path), run_name="__main__")

            logger.error(f"[DEBUG] 模块执行完成")
            returncode = 0

        except SystemExit as e:
            logger.error(f"[DEBUG] 脚本调用SystemExit: {e.code}")
            returncode = e.code if e.code is not None else 0
        except Exception as e:
            logger.error(f"[DEBUG] 脚本执行异常: {str(e)}")
            captured_stderr.write(f"脚本执行异常: {str(e)}\n")
            returncode = 1
        finally:
            # 恢复原始stdout/stderr
            sys.stdout = original_stdout
            sys.stderr = original_stderr
            # 恢复原始sys.argv
            sys.argv = original_argv

        # 获取捕获的输出
        stdout_output = captured_stdout.getvalue()
        stderr_output = captured_stderr.getvalue()

        logger.error(f"[DEBUG] 脚本执行完成，返回码: {returncode}")
        logger.error(f"[DEBUG] stdout长度: {len(stdout_output)}")
        logger.error(f"[DEBUG] stderr长度: {len(stderr_output)}")

        return {
            'success': returncode == 0,
            'stdout': stdout_output,
            'stderr': stderr_output,
            'returncode': returncode
        }

    except Exception as e:
        logger.error(f"[DEBUG] runpy执行异常: {str(e)}")
        return {
            'success': False,
            'stdout': '',
            'stderr': f'runpy执行失败: {str(e)}',
            'returncode': -1
        }

def run_script_with_subprocess(script_path: Path, args: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    使用subprocess.Popen()和独立线程执行脚本，避免Windows上的阻塞问题
    """
    if DEBUG_MODE:
        # 调试模式：返回模拟结果
        return {
            "success": True,
            "stdout": json.dumps({
                "success": True,
                "message": f"[调试模式] 模拟执行脚本: {script_path.name}",
                "script_path": str(script_path),
                "args": args or [],
                "simulated": True
            }, ensure_ascii=False, indent=2),
            "stderr": "",
            "returncode": 0
        }

    if not script_path.exists():
        return {
            "success": False,
            "stdout": "",
            "stderr": f"脚本文件不存在: {script_path}",
            "returncode": 1
        }

    try:
        import threading
        import queue

        # 构建命令
        cmd = [sys.executable, str(script_path)]
        if args:
            cmd.extend(args)

        logger.error(f"[DEBUG] 执行命令: {' '.join(cmd)}")

        # 使用Popen启动进程，避免阻塞
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            stdin=subprocess.PIPE,
            text=True,
            encoding='utf-8',  # 明确指定UTF-8编码
            errors='replace',  # 遇到编码错误时替换为占位符
            cwd=PROJECT_ROOT,  # 使用项目根目录作为工作目录
            # Windows特定设置
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        )

        # 使用队列收集输出
        stdout_queue = queue.Queue()
        stderr_queue = queue.Queue()

        def reader(pipe, q):
            """读取管道输出到队列"""
            try:
                for line in iter(pipe.readline, ''):
                    q.put(line)
                pipe.close()
            except Exception as e:
                q.put(f"读取错误: {e}")

        # 启动独立线程读取输出，避免管道缓冲区满导致死锁
        stdout_thread = threading.Thread(target=reader, args=(proc.stdout, stdout_queue), daemon=True)
        stderr_thread = threading.Thread(target=reader, args=(proc.stderr, stderr_queue), daemon=True)

        stdout_thread.start()
        stderr_thread.start()

        # 等待进程完成，设置超时
        try:
            returncode = proc.wait(timeout=SCRIPT_TIMEOUT)
        except subprocess.TimeoutExpired:
            logger.error(f"[DEBUG] 脚本执行超时，强制终止进程")
            proc.kill()
            proc.wait()
            return {
                'success': False,
                'stdout': '',
                'stderr': f'脚本执行超时（{SCRIPT_TIMEOUT}秒）',
                'returncode': -1
            }

        # 等待线程完成
        stdout_thread.join(timeout=5)
        stderr_thread.join(timeout=5)

        # 收集输出
        stdout_lines = []
        stderr_lines = []

        while not stdout_queue.empty():
            stdout_lines.append(stdout_queue.get())

        while not stderr_queue.empty():
            stderr_lines.append(stderr_queue.get())

        stdout_output = ''.join(stdout_lines)
        stderr_output = ''.join(stderr_lines)

        logger.error(f"[DEBUG] 脚本执行完成，返回码: {returncode}")
        logger.error(f"[DEBUG] stdout长度: {len(stdout_output)}")
        logger.error(f"[DEBUG] stderr长度: {len(stderr_output)}")

        return {
            'success': returncode == 0,
            'stdout': stdout_output,
            'stderr': stderr_output,
            'returncode': returncode
        }

    except Exception as e:
        logger.error(f"[DEBUG] 脚本执行异常: {str(e)}")
        return {
            'success': False,
            'stdout': '',
            'stderr': f'脚本执行失败: {str(e)}',
            'returncode': -1
        }

# 创建FastMCP应用
mcp = FastMCP("Product Lifecycle Management - Simple")

@mcp.tool()
async def get_project_info(
    ctx,
    project_path: Annotated[str, Field(description="项目路径，支持相对路径和绝对路径。默认为当前目录")] = "."
) -> Dict[str, Any]:
    """
    获取项目信息管理 (T06)
    扫描项目目录，返回项目基本信息、文件统计、配置状态、目录结构等详细信息
    """
    try:
        script_path = SCRIPTS_BASE / "directory_initialization" / "get_project_info.py"
        script_args = [project_path, "--json"]

        result = run_script_with_subprocess(script_path, script_args)
        
        if result["success"]:
            return {
                "success": True,
                "data": json.loads(result["stdout"]) if result["stdout"].strip() else {},
                "metadata": {
                    "tool_name": "get_project_info",
                    "execution_time": "< 1s",
                    "script_path": str(script_path)
                }
            }
        else:
            return {
                "success": False,
                "error": result["stderr"] or "获取项目信息失败",
                "metadata": {
                    "tool_name": "get_project_info",
                    "execution_time": "< 1s",
                    "script_path": str(script_path)
                }
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"获取项目信息时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "get_project_info",
                "execution_time": "< 1s"
            }
        }

@mcp.tool()
async def init_project(
    ctx,
    project_name: Annotated[str, Field(description="项目名称，用于创建项目目录和配置文件")],
    structure_type: Annotated[str, Field(description="项目结构类型，可选值：single_layer（单层结构）或 multi_level（多层结构）")] = "single_layer",
    project_path: Annotated[str, Field(description="项目创建路径，默认为当前目录")] = ".",
    kwargs: Annotated[Union[str, Dict[str, Any], None], Field(description="其他参数，支持JSON字符串、字典对象或None")] = None
) -> Dict[str, Any]:
    """
    项目初始化 (T27)
    创建项目目录结构，生成配置文件，返回项目路径、结构类型、创建的组件列表
    """
    try:
        logger.error(f"[DEBUG] init_project函数被调用: project_name={project_name}")
        
        # 处理kwargs参数
        if kwargs is None:
            kwargs_dict = {}
            kwargs_str = "{}"
        elif isinstance(kwargs, dict):
            kwargs_dict = kwargs
            kwargs_str = json.dumps(kwargs)
        elif isinstance(kwargs, str):
            try:
                kwargs_dict = json.loads(kwargs) if kwargs else {}
                kwargs_str = kwargs
            except json.JSONDecodeError:
                kwargs_dict = {}
                kwargs_str = "{}"
        else:
            kwargs_dict = {}
            kwargs_str = "{}"

        # 构建脚本参数 - 使用修改后的原始脚本（已将subprocess改为runpy）
        script_path = SCRIPTS_BASE / "init_product_project.py"
        script_args = [
            "--project_path", project_path,
            "--project_name", project_name,
            "--structure_type", structure_type
        ]
        
        logger.error(f"[DEBUG] DEBUG_MODE={DEBUG_MODE}")
        logger.error(f"[DEBUG] 执行命令: {script_path} {' '.join(script_args)}")

        # 使用subprocess执行脚本，彻底避免嵌套调用问题
        logger.error(f"[DEBUG] 开始使用subprocess执行脚本...")
        result = run_script_with_subprocess(script_path, script_args)
        logger.error(f"[DEBUG] 脚本执行完成，结果: success={result.get('success')}, stdout长度={len(result.get('stdout', ''))}")

        if result["success"]:
            try:
                # 尝试解析JSON输出
                output_data = json.loads(result["stdout"]) if result["stdout"].strip() else {}
                return {
                    "success": True,
                    "data": output_data,
                    "metadata": {
                        "tool_name": "init_project",
                        "execution_method": "subprocess_isolated",
                        "script_path": str(script_path)
                    }
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回原始输出
                return {
                    "success": True,
                    "data": {
                        "raw_output": result["stdout"],
                        "project_name": project_name,
                        "structure_type": structure_type,
                        "project_path": project_path
                    },
                    "metadata": {
                        "tool_name": "init_project",
                        "execution_method": "subprocess_isolated",
                        "script_path": str(script_path)
                    }
                }
        else:
            return {
                "success": False,
                "error": result["stderr"] or "项目初始化失败",
                "metadata": {
                    "tool_name": "init_project",
                    "execution_method": "subprocess_isolated",
                    "script_path": str(script_path)
                }
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"项目初始化时发生错误: {str(e)}",
            "metadata": {
                "tool_name": "init_project",
                "execution_time": "< 1s"
            }
        }

if __name__ == "__main__":
    mcp.run()
