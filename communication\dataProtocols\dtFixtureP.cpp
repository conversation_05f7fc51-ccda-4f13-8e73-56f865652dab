#include "dtFixtureP.h"
#include <qdebug.h>
#include "qLog.h"

dtFixture::dtFixture(QObject *parent):QObject(parent){
    Q_UNUSED(parent)

}

dtFixture::~dtFixture(){

}

/**
 * @brief 获取控制指令
 * @return
 */
QByteArray dtFixture::getControlCmd(const char &id)
{

}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray dtFixture::getWriteCmd(const char &id, const QByteArray &w_data)
{


}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray dtFixture::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{


}
