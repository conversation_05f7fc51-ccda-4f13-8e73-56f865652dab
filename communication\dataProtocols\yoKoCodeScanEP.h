#ifndef  _YOKO_CODE_SCANNER_EP_H_
#define  _Y<PERSON><PERSON>_CODE_SCANNER_EP_H_

#include <QByteArray>
#include "IProtocol.h"
#include <QVector>

class CYoKoCodeScannerEP:public IProtocol
{
public:
    CYoKoCodeScannerEP();
    ~CYoKoCodeScannerEP();

    //*协议cmd枚举
    enum class EInteractionFrame {
        eHEADER     = 0x0057, //LSB
        eHEADER_LEN = 0x06,
        eTAIL       = 0x4150,
    };

    //* data info frame
    enum class EInteractionAckFrame {
        eHEADER     = 0x0031,
        eHEADER_LEN = 0x06,
        eTAIL       = 0x0050,
    };

    enum class EFunctionCode {
        eSINGLE_ACK         = 0x06,
        eAUTO_ACK           = 0x10,
    };

    typedef struct {
        uint16_t            header; //LSB
        uint16_t            id; //LSB
        uint16_t            num; //LSB
        uint8_t             data;
        uint16_t            crc16; //
        uint16_t            tail;
    } StInteractionFrame;


    typedef struct {
      uint16_t              header; //LSB
      uint16_t              cmd; //LSB
      uint16_t              num;
      uint16_t              crc16;
      uint16_t              tail;
    } StAckFrame;

    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

private:
    StInteractionFrame* mst_interaction_frame_ = nullptr;
    StAckFrame* mst_ack_frame_ = nullptr;
};


#endif
