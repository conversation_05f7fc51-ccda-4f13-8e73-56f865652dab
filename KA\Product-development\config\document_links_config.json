{"document_association": {"version": "1.0.0", "system_type": "semantic_analysis_double_link", "project_type": "single_layer", "responsibilities": ["文档注册管理到INDEX.md文件", "语义关联发现通过AI分析", "双链网络构建使用[[document_name]]格式", "关联关系建议为INDEX表格"], "semantic_analysis": {"enabled": true}, "double_link_system": {"enabled": true}, "auto_registration": {"enabled": true}}, "document_scanning": {"description": "文档扫描配置 - 支持传统配置和Git风格规则", "mode": "gitignore_style", "comment": "mode可选值: traditional(传统配置) 或 gitignore_style(Git风格规则)", "gitignore_rules": ["# Git风格的文档扫描规则 - 类似.gitignore语法", "# 语法说明:", "# - 每行一个模式", "# - # 开头的行是注释", "# - 支持通配符 * 和 **", "# - 使用 ! 前缀来包含被排除的文件", "# - 以 / 结尾表示目录", "", "# ==================== 全局排除规则 ====================", "", "# 系统文件和目录", ".git/", ".vscode/", ".idea/", "__pycache__/", "node_modules/", ".venv/", "build/", "dist/", "target/", "", "# 临时文件", "*.tmp", "*.temp", "*.pyc", "*.pyo", "~$*", ".DS_Store", "Thumbs.db", "*.log", "", "# INDEX文件（由系统自动生成）", "*INDEX.md", "*_INDEX.md", "", "# ==================== 开发目录特殊规则 ====================", "", "# 排除所有源代码文件", "development/**/*.c", "development/**/*.h", "development/**/*.cpp", "development/**/*.py", "development/**/*.js", "development/**/*.java", "", "# 但包含docs目录下的所有文档", "!development/**/docs/**", "", "# 也包含散落的文档文件", "!development/**/*.md", "!development/**/*.txt", "!development/**/*.pdf", "!development/**/*.doc", "!development/**/*.docx"], "traditional_config": {"included_file_types": [".md", ".txt", ".rtf", ".doc", ".docx", ".xls", ".xlsx", ".xlsm", ".ppt", ".pptx", ".pptm", ".vsd", ".vsdx", ".pdf", ".csv", ".xml", ".json"], "excluded_directories": [".git", ".vscode", ".idea", "__pycache__", "node_modules", ".venv", "build", "dist", "target", "logs", "temp", "cache", "tmp", ".sass-cache", ".pytest_cache"], "excluded_files": ["INDEX.md", "_INDEX.md", "README.md", "GUIDE.md", "~$*", ".*", ".DS_Store", "Thumbs.db"]}, "component_specific": {"说明": "为特定组件设置专门的扫描规则", "DEV": {"description": "开发组件支持白名单扫描模式，只扫描docs目录", "additional_types": [], "additional_excluded_dirs": ["__pycache__", "node_modules", ".gradle", "build", "dist", "target"], "scan_only_subdirectories": {"enabled": true, "description": "启用后，在development目录的项目子目录中只扫描指定的子目录", "directories": ["docs"], "apply_to_depth": 2, "comment": "apply_to_depth=2表示在development/*/子目录中应用此规则"}}, "QA": {"description": "质量保证组件可能需要扫描测试相关文件", "additional_types": [".feature", ".spec"], "additional_excluded_dirs": ["test-results", "coverage"]}}}, "link_discovery": {"description": "关联发现配置", "semantic_similarity_threshold": 0.1, "max_associations": 50, "enable_ai_analysis": true}, "canvas_integration": {"description": "Canvas集成配置", "auto_sync": true, "layout_spacing": 700, "node_size": {"width": 400, "height": 300}}}