#include "assistant.h"
#include <QByteArray>
#include <QCoreApplication>
#include <QDir>
#include <QMessageBox>
#include <QtCore/QProcess>


Assistant::Assistant() : proc_(0) {
}
Assistant::~Assistant() {
    if (proc_ && proc_->state() == QProcess::Running) {
        // 试图终止进程
        proc_->terminate();
        proc_->waitForFinished(3000);
    }
    delete proc_;  // 销毁proc
}

/* 显示文档 */
void Assistant::showDocumentation(const QString &page) {
    if (!startAssistant())
        return;
    QByteArray ba("SetSource ");
    ba.append("qthelp://IA.myHelp/doc/");
    proc_->write(ba + page.toLocal8Bit() + '\n');
}

/* 启动Qt Assistant*/
bool Assistant::startAssistant() {
    // 如果没有创建进程，则新创建一个
    if (!proc_)
        proc_ = new QProcess();
    // 如果进程没有运行，则运行assistant，并添加参数
    if (proc_->state() != QProcess::Running) {
        QStringList args;
        QString     projectRoot = QCoreApplication::applicationDirPath();
        QDir        projectDir(projectRoot);
        QString     app           = projectDir.filePath("document/assistant.exe");  // 拼接路径
        QString     help_file_loc = projectDir.filePath("document/myHelp.qhc");     // 拼接路径
        // QString     app = QLatin1String("../LA/document/assistant.exe");

        args << QLatin1String("-collectionFile") << help_file_loc;
        proc_->start(app, args);
        if (!proc_->waitForStarted()) {  // wait 30s
            QMessageBox::critical(0, QObject::tr("my help"), QObject::tr("Unable to launch Qt Assistant (%1)").arg(app));
            return false;
        }
    }
    return true;
}
