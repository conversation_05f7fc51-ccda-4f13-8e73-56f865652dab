#include "clensadjust.h"
#include "itemFlowBottom.h"
#include "qLog.h"
#include "ui_clensadjust.h"


#include <QMetaEnum>
#include <QShortcut>

#define Z_MIN_DISTANCE     1   // um
#define Z_SEND_DISTANCE    10  //

#define PROCESS_TIME_LIMIT 10000  //调节时间显示 2min

cLensAdjust::cLensAdjust(QWidget *parent)
    : QDockWidget(parent),
      ui(new Ui::cLensAdjust),
      mst_config_(new NClen::StUiConfig),
      mc_operation_(new CLenAdjustOpt(*mst_config_)),
      m_is_unnormal_mode(false),
      m_move_multiple(0) {
    /*1.ui init*/
    ui->setupUi(this);
    this->setWindowTitle(LENS_ADJUST_APP_WINDOW_TITLE);
    //    this->setWindowIcon(QIcon("cspc.png"));
    //    this->setGeometry(100, 200, 1138, 761);
    //    this->show();

    ui->modeBox->setCurrentIndex(1);     // 1-手动模式
    ui->faculaType->setCurrentIndex(0);  //自匹配
    ui->faculaArea->setEnabled(false);

    tableInit();
    modeTableChange();

    ui->faculaArea->setAlignment(Qt::AlignLeft);
    //    ui->peakThreshold->setAlignment(Qt::AlignLeft);

    ui->handleCtrlDevice->installEventFilter(this);  //事件过滤器

    //* 3.信号与槽
    QShortcut *temp = new QShortcut(this);
    //设置键值，也就是设置快捷键.
    //    temp->setKey(tr("ctrl+space"));
    temp->setKey(Qt::CTRL + Qt::Key_Space);
    //这个成员函数挺关键的，设置是否会自动反复按键.也就是说，当你一直按住键盘ctrl+空格时，会一直不停的调用对应的槽函数.
    temp->setAutoRepeat(false);

    //连接信号与槽，showSlot()是自定义的槽函数!
    connect(temp, SIGNAL(activated()), this, SLOT(on_serialOpen_clicked()));

    connect(mc_operation_, &CLenAdjustOpt::initErrorSignal, this, &cLensAdjust::optInitErrorShow_slog);
    connect(mc_operation_, &CLenAdjustOpt::stepStatusSignal, this, &cLensAdjust::processStatusShow);
    //    connect(mc_operation_, &CLenAdjustOpt::stepErrorSignal,
    //            this, &cLensAdjust::processDdsShow);
    connect(mc_operation_, &CLenAdjustOpt::moduleInfoShowSignal, this, &cLensAdjust::processTaskInfoShow_slot);


    connect(mc_operation_, &CLenAdjustOpt::portUpdateSignal, this, &cLensAdjust::portListShow);              //列表更新
    connect(mc_operation_, &CLenAdjustOpt::devicePortUpdateSignal, this, &cLensAdjust::devicePortListShow);  //列表更新

    connect(mc_operation_, &CLenAdjustOpt::readySignal, this, &cLensAdjust::readyShow);        // ready
    connect(mc_operation_, &CLenAdjustOpt::startAckSignal, this, &cLensAdjust::startAckShow);  // start ack
    connect(mc_operation_, &CLenAdjustOpt::unnormalViewSignal, this, &cLensAdjust::autoUnnormalModeShow);

    connect(mc_operation_, &CLenAdjustOpt::originLocSignal, this, &cLensAdjust::originCoordinateShow);


    //在构造里面添加即可
    qRegisterMetaType<QVector<QVector<uint32_t>>>("map_matrix");                                //注册自定义类型信号槽
    connect(mc_operation_, &CLenAdjustOpt::dataAckSignal, this, &cLensAdjust::dataAckShow);     // data ack
    connect(mc_operation_, &CLenAdjustOpt::adjustAckSignal, this, &cLensAdjust::movedLocShow);  // move distance

    connect(mc_operation_, &CLenAdjustOpt::adjustedLocSignal, this, &cLensAdjust::adjustedCoordinatShow);

    connect(mc_operation_, &CLenAdjustOpt::resultSignal, this, &cLensAdjust::resultShow);                    // result
    connect(mc_operation_, &CLenAdjustOpt::productTestInfoSignal, this, &cLensAdjust::productTestInfoShow);  // product test info

    connect(mc_operation_, &CLenAdjustOpt::compAckSignal, this, &cLensAdjust::compAckShow);  // complete ack

    //    connect(mc_operation_, &CLenAdjustOpt::errorAckShow,
    //            this, &cLensAdjust::errorAckShow);
    //    qRegisterMetaType<ECommStatus>("ECommStatus");
    //    qRegisterMetaType<EExecStatus>("EExecStatus");
    //    qRegisterMetaType<EResult>("EResult");
}

cLensAdjust::~cLensAdjust() {
    delete ui;
    delete mst_config_;
    delete mc_auto_grey_map_;
    delete mc_manual_grey_map_;
    delete mc_expand_grey_map_;
    delete mc_operation_;
}

void cLensAdjust::closeEvent(QCloseEvent *event) {
    Q_UNUSED(event);

    this->setAttribute(Qt::WA_DeleteOnClose);  //释放窗口资源

    QThread::msleep(50);

    emit lensAdjustCloseSiganl(true);
    emit windowCloseSiganl(true);
}


void cLensAdjust::mousePressEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {  //鼠标左键
        //        ui->handleCtrlDevice->mapFromGlobal(QCursor::pos());
        //        QPoint point = event->pos(); //局部坐标？
        auto   btn_mast = ST_MASK::None;
        QPoint point    = event->globalPos();                          //全局坐标
        point           = ui->handleCtrlDevice->mapFromGlobal(point);  //获取当前窗口坐标
        if (isPointInCir(point, drawRect.toRect())) {
            if (isPointInCir(point, centerCircularRect.toRect())) {  //
                btn_mast = ST_MASK::center;
            } else {
                switch (isPointInCirRing(point, drawRect.toRect())) {
                case static_cast<uint32_t>(ST_MASK::inner):
                    btn_mast        = ST_MASK::inner;
                    m_move_multiple = 1;
                    break;
                case static_cast<uint32_t>(ST_MASK::middle):
                    btn_mast        = ST_MASK::middle;
                    m_move_multiple = 10;
                    break;
                case static_cast<uint32_t>(ST_MASK::out):
                    btn_mast        = ST_MASK::out;
                    m_move_multiple = 100;
                    break;
                default:
                    break;
                }
                QPoint centerPoint = drawRect.toRect().center();                                       //大圆
                double angle       = atan2(point.y() - centerPoint.y(), point.x() - centerPoint.x());  //两点之间的角度（弧度）
                angle              = -angle * (180 / 3.1415926);                                       // 0°~180° - -180°~0°
                if (angle < 0.0) {
                    angle = 360.0 - abs(angle);
                }
                if (angle < 22.5 || angle > 337.5)  //
                {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::right);
                } else if (angle >= 22.5 && angle < 67.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::right | ST_MASK::up);
                } else if (angle >= 67.5 && angle < 112.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::up);
                } else if (angle >= 112.5 && angle < 157.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::up | ST_MASK::left);
                } else if (angle >= 157.5 && angle < 202.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::left);
                } else if (angle >= 202.5 && angle < 247.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::left | ST_MASK::down);
                } else if (angle >= 247.5 && angle < 292.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::down);
                } else if (angle >= 292.5 && angle < 337.5) {
                    btn_mast = (ST_MASK::pressBtnType)(btn_mast | ST_MASK::down | ST_MASK::right);
                }
            }
            update();
        }
        pressedBtn = btn_mast;
    }
    return QWidget::mousePressEvent(event);
}

void cLensAdjust::mouseReleaseEvent(QMouseEvent *event) {
    if (pressedBtn != ST_MASK::None) {
        pressedBtn = ST_MASK::None;
        update();
    }
    return QWidget::mouseReleaseEvent(event);
}

QPainterPath cLensAdjust::gradientArc(double startAngle, double angleLength, double arcHeight) {
    QPainterPath path;
    path.moveTo(drawRect.center());
    path.arcTo(drawRect, startAngle, angleLength);  //画弧

    QPainterPath subPath;
    subPath.addEllipse(drawRect.adjusted(arcHeight, arcHeight, -arcHeight, -arcHeight));  //矩形上下点位置

    // path为扇形 subPath为椭圆
    path -= subPath;
    return path;
}

bool cLensAdjust::eventFilter(QObject *watched, QEvent *event) {
    if (watched == ui->handleCtrlDevice && event->type() == QEvent::Paint && (mst_config_->mode == IClensMachine::EMode::manual_mode || m_is_unnormal_mode)) {
        paintWidget();
        //        qDebug() << "-c event filter";
    }
    return QWidget::eventFilter(watched, event);
}


void cLensAdjust::paintWidget() {
    QPainter painter(ui->handleCtrlDevice);
    painter.setBrush(Qt::black);  //先画成黑色
    //    painter.drawRect(0, 0, ui->handleCtrlDevice->width(), ui->handleCtrlDevice->height());
    painter.setPen(Qt::NoPen);

    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    QPoint centerPoint = ui->handleCtrlDevice->rect().center();
    centerPoint.setX(centerPoint.x() / 2);  //取1/4处

    painter.save();
    painter.setPen(QColor("#222222"));
    painter.setBrush(QColor("#EAEAEA"));
    double radius     = (std::min(ui->handleCtrlDevice->width(), ui->handleCtrlDevice->height()) - 10 * 2) / 2;
    drawRect          = QRectF(centerPoint.x() - radius, centerPoint.y() - radius, radius * 2, radius * 2);
    double arcHeight  = radius / 4;      //最外圈 - 100us
    double arcHeight1 = radius / 2;      //
    double arcHeight2 = 3 * radius / 4;  //

    float delta_angle = 45.0;
    for (uint i = 0; i < 8; ++i) {
        fanShaped[0 + 3 * i] = gradientArc(22.5 + delta_angle * i, delta_angle, arcHeight);                                                 //上
        fanShaped[1 + 3 * i] = gradientArc(22.5 + delta_angle * i, delta_angle, arcHeight1) - fanShaped[0 + 3 * i];                         //上
        fanShaped[2 + 3 * i] = gradientArc(22.5 + delta_angle * i, delta_angle, arcHeight2) - fanShaped[0 + 3 * i] - fanShaped[1 + 3 * i];  //上
        //        fanShaped[0 + 3*i].c //无法改变颜色
    }

    for (int i = 0; i < 24; ++i) {
        painter.drawPath(fanShaped[i]);
    }
    painter.restore();

    centerCircularRect =
        QRectF(centerPoint.x() - (radius / 4), centerPoint.y() - (radius / 4), radius / 2, radius / 2).adjusted(2, 2, -2, -2);  //（左上角坐标，长，宽）->中心圆
    painter.save();
    painter.setPen(Qt::transparent);
    painter.setBrush(QColor("#EAEAEA"));
    painter.drawEllipse(centerCircularRect);
    painter.restore();

    //绘制文字
    double textRectWeight = radius / 2;
    double textRectHeight = radius / 2;

    painter.save();
    QPen p(Qt::SolidLine);
    p.setColor("#000000");
    p.setWidth(2);
    painter.setPen(p);
    QFont font = painter.font();
    font.setPixelSize(24);
    painter.setFont(font);

    //左
    QRectF textRect = QRectF(drawRect.x(), centerCircularRect.y(), textRectWeight, textRectHeight);
    painter.drawText(textRect, Qt::AlignCenter, "〈 -Y");  //在此区域的中间位置绘制文字 〉

    //上
    textRect = QRectF(centerCircularRect.x(), drawRect.y(), textRectWeight, textRectHeight);
    painter.drawText(textRect, Qt::AlignCenter, "︿\r\n-X");

    //右
    textRect = QRectF(drawRect.x() + 2 * radius - textRectWeight, centerCircularRect.topRight().y(), textRectWeight, textRectHeight);
    painter.drawText(textRect, Qt::AlignCenter, "+Y 〉");

    //下
    textRect = QRectF(centerCircularRect.bottomLeft().x(), drawRect.y() + 2 * radius - textRectWeight, textRectWeight, textRectHeight);
    painter.drawText(textRect, Qt::AlignCenter, "+X\r\n﹀");

    painter.drawText(centerCircularRect, Qt::AlignCenter, "RST");
    painter.restore();

    if (pressedBtn != ST_MASK::None) {
        painter.save();
        QColor slightlyOpaqueBlack(0, 0, 0, 63);
        painter.setBrush(slightlyOpaqueBlack);
        painter.setPen(Qt::transparent);
        if (pressedBtn == ST_MASK::center) {
            painter.drawEllipse(centerCircularRect);

            mc_operation_->clenMoveDelta();
            //            m_device_info_->move_delta.x = 0;
            //            m_device_info_->move_delta.y = 0;
            //            m_device_info_->move_delta.z = 0;
            C3dHandMachine::St3D<int16_t> move_delta_tmp = {0, 0, 0};
            movedLocShow(move_delta_tmp.x, move_delta_tmp.y, move_delta_tmp.z);
        } else {
            int index          = -1;
            index              = static_cast<int>(pressedBtn);
            uint8_t ring_index = 0, path_index = 0;
            if (index >= 0) {
                if ((index & ST_MASK::inner) == ST_MASK::inner) {
                    ring_index = 2;
                } else if ((index & ST_MASK::middle) == ST_MASK::middle) {
                    ring_index = 1;
                } else if ((index & ST_MASK::out) == ST_MASK::out) {
                    ring_index = 0;
                }

                mc_operation_->mst_move_distance_->x = 0;
                mc_operation_->mst_move_distance_->y = 0;
                mc_operation_->mst_move_distance_->z = 0;

                //                m_device_info_->cur_move_distance.x = 0;
                //                m_device_info_->cur_move_distance.y = 0;
                //                m_device_info_->cur_move_distance.z = 0;
                /*1. 左右互斥*/
                if ((index & ST_MASK::right) == ST_MASK::right) {
                    mc_operation_->mst_move_distance_->y = -m_move_multiple;
                    path_index                           = 7;
                } else if ((index & ST_MASK::left) == ST_MASK::left) {
                    mc_operation_->mst_move_distance_->y = m_move_multiple;  //
                    path_index                           = 3;
                }

                /*2. 上下*/
                if ((index & ST_MASK::up) == ST_MASK::up) {
                    mc_operation_->mst_move_distance_->x = m_move_multiple;
                    path_index                           = 1;

                    if ((index & ST_MASK::right) == ST_MASK::right) {
                        path_index = 0;
                    } else if ((index & ST_MASK::left) == ST_MASK::left) {
                        path_index = 2;
                    }
                } else if ((index & ST_MASK::down) == ST_MASK::down) {
                    mc_operation_->mst_move_distance_->x = -m_move_multiple;  //
                    path_index                           = 5;

                    if ((index & ST_MASK::right) == ST_MASK::right) {
                        path_index = 6;
                    } else if ((index & ST_MASK::left) == ST_MASK::left) {
                        path_index = 4;
                    }
                }
                painter.drawPath(fanShaped[path_index * 3 + ring_index]);
            }
        }
        painter.restore();
        mc_operation_->mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.exec = true;
        //        m_status_flag_->status.manual_move = true;
        qDebug() << "-c painter";
        pressedBtn = ST_MASK::None;
    }
    //    painter.restore();
}

bool cLensAdjust::isPointInCir(const QPoint &point, const QRect &rect)  //判断点是否在圆范围内
{
    const QPoint &centerPoint = rect.center();
    int           x           = point.x() - centerPoint.x();
    int           y           = point.y() - centerPoint.y();
    if (sqrt(pow(x, 2) + pow(y, 2)) <= static_cast<double>(rect.width() / 2)) {
        return true;
    }
    return false;
}

int cLensAdjust::isPointInCirRing(const QPoint &point, const QRect &rect)  //判断点是否在圆环范围内
{
    double cir_radius = static_cast<double>(rect.width() / 2);
    double ring_inner = cir_radius / 4;
    double ring_mid   = 2 * ring_inner;
    double ring_out   = 3 * ring_inner;

    const QPoint &centerPoint = rect.center();
    int           x           = point.x() - centerPoint.x();
    int           y           = point.y() - centerPoint.y();
    double        length      = sqrt(pow(x, 2) + pow(y, 2));
    if (length <= cir_radius) {
        if (length > ring_out)
            return 1 << 15;
        else if (length > ring_mid)
            return 1 << 14;
        else if (length > ring_inner)
            return 1 << 13;
        else
            return 1 << 12;
    }
    return -1;
}

/**
 * @brief 配置更新
 */
void cLensAdjust::updateConfig(NClen::StUiConfig *config_) {
    config_->port_name         = ui->portBox->currentText();
    config_->dev_port_name     = ui->devicePortBox->currentText();
    config_->cur_port_name     = ui->portBox->currentText();
    config_->cur_dev_port_name = ui->devicePortBox->currentText();

    config_->mode        = IClensMachine::EMode(ui->modeBox->currentIndex());
    config_->facula_type = IFaculaFactory::ERxFaculaType(ui->faculaType->currentIndex());
    config_->facula_form = IPhotonSensor::EFaculaForm(ui->faculaForm->currentIndex());
    config_->target_area = ui->faculaArea->text();  //后续解析
    //  config_->peak_throld = ui->peakThreshold->text().toUInt();

    //  config_->mp_order = EMpOrder::up_2_down;

    //  m_status_flag_->flag = 0;
}

void cLensAdjust::tableInit() {
    ITable::StTableInfo origin_table_info = mc_operation_->getOriginTableInfo();
    ITable::StTableInfo expand_table_info = mc_operation_->getExpandTableInfo();

    mc_auto_grey_map_   = new CTableViewModule(ui->greyMap, origin_table_info);
    mc_manual_grey_map_ = new CTableViewModule(ui->handleGreyMap, origin_table_info);
    mc_expand_grey_map_ = new CTableViewModule(ui->greyMapExpand, expand_table_info);
}

void cLensAdjust::modeTableChange() {
    if (mc_auto_grey_map_ != nullptr && mc_expand_grey_map_ != nullptr && mc_manual_grey_map_ != nullptr) {
        if (!m_is_unnormal_mode && mst_config_->mode == IClensMachine::EMode::auto_mode) {
            mc_auto_grey_map_->targetFaculaAreaShow();
            mc_expand_grey_map_->targetFaculaAreaShow();
        } else {
            mc_manual_grey_map_->targetFaculaAreaShow();
        }
    }
}

/*
 * @BRIEF: 数据显示
 */
// void cLensAdjust::greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data)
//{
//    uint8_t alpha_tmp = 180, rgb_tmp;
//    uint32_t data_tmp = 0;

//    uint8_t xlens = map_info_->xlens;
//    uint8_t ylens = map_info_->ylens;
//    uint32_t data_max = map_data.max_peak>10?map_data.max_peak:10; //peak 最小值

//    for (int y = 0; y < ylens; ++y)
//    {
//        for (int x = 0; x < xlens; ++x)
//        {
//            /*1. 数值 update*/
//            data_tmp = map_data.map_matrix.at(y).at(x);
//            map_info_->table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

//            /*3. 背景颜色*/
//            QColor color;
//            rgb_tmp = 255 - (data_tmp * 255 / data_max);
//            alpha_tmp = data_tmp * 255 / data_max;
//            color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
//            map->item(y,x)->setBackground(QBrush(color));
//            //            map->setStyleSheet( //QTableView::item
//            //                            "#m_table_item_test_{border:1px solid red;}"
//            //                        );
//            //            qDebug() << "-i clen color/ data data_max RGB:" << data_tmp << data_max << rgb_tmp;
//        }
//    }
//}

/*
 * @brief: 异常处理
 */
// void cLensAdjust::unnormalHandle()
//{
//    ;
//}


/**
 * @brief cLensAdjust::targetFaculaAreaShow
 * @param map
 * @param map_info_
 */
// void cLensAdjust::targetFaculaAreaShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_) //
//{
//    /*4. 目标区域凸显 (0,0),(1,0),(0,1),(-1,0),(0,-1)*/
//    uint8_t xlen = map_info_->xlens;
//    uint8_t ylen = map_info_->ylens;
//    uint8_t x_tf = map_info_->target_tf.ax;
//    uint8_t y_tf = map_info_->target_tf.ay;
//    int8_t coordinate_x = 0, coordinate_y = 0;
//    uint8_t tip_num = 0;

//    /*释放内存*/
//    uint8_t item_lens = map_info_->table_item.length();
//    if(item_lens != 0) {
//        for(uint i = 0; i < item_lens; i++) {
//            if(map_info_->table_item.at(i) != nullptr)
//                delete map_info_->table_item.at(i);
//        }
//    }
//    map_info_->table_item.clear();

//    for (uint y = 0; y < ylen; y++) {
//        if(y > y_tf)
//            coordinate_y = y - y_tf;
//        else
//            coordinate_y = y_tf - y;
//        for (uint x = 0; x < xlen; x++) {
//            if(x > x_tf)
//                coordinate_x = x - x_tf;
//            else
//                coordinate_x = x_tf - x;

//            QTableWidgetItem *item = new QTableWidgetItem();
//            item->setForeground(Qt::yellow); //darkred
//            item->setFont(QFont("Times", 20, QFont::Black)); //加粗
//            if( (coordinate_x <= 1) && (coordinate_y <= 1) && ((coordinate_x + coordinate_y) <= 1)) {
//                item->setForeground(Qt::green);
//                item->setFont(QFont("Times", 26, QFont::Black)); //加粗
//            }
//            item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);

//            /*2. MP提示值*/
//            if(mc_operation_->mst_map_info_->mp_order == IPhotonSensor::EMpOrder::left_2_right) //左往右
//                tip_num = y*xlen + x + 1;
//            else
//                tip_num = x*ylen + y + 1;
//            item->setToolTip(QString::number(tip_num)); //提示 VI4300
//            map_info_->table_item.push_back(item);
//            map->setItem(y, x, item);
//        }
//    }
//}

/**
 * @brief 更新窗口列表
 */
void cLensAdjust::portListShow(QStringList *port_list_, bool port_flag) {
    ui->portBox->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->portBox->clear();                //会触发currentIndexChanged回调函数
    ui->portBox->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->portBox->setCurrentIndex(0);
    else
        ui->portBox->setCurrentText(mst_config_->cur_port_name);

    ui->portBox->blockSignals(false);
}

void cLensAdjust::devicePortListShow(QStringList *port_list_, bool port_flag) {
    ui->devicePortBox->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->devicePortBox->clear();                //会触发currentIndexChanged回调函数
    ui->devicePortBox->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->devicePortBox->setCurrentIndex(0);
    else
        ui->devicePortBox->setCurrentText(mst_config_->cur_dev_port_name);

    ui->devicePortBox->blockSignals(false);
}

void cLensAdjust::optInitErrorShow_slog(const QString &info) {
    QMessageBox::warning(this, "", info);
}

/**
 * @brief 任务集运行状态更新
 */
void cLensAdjust::processStatusShow(const int16_t &step, const int16_t &status) {
    //* 枚举转字符
    QMetaEnum stepEnum = QMetaEnum::fromType<CLenAdjustOpt::EProcessStep>();

    QString step_str = stepEnum.valueToKey(step);
#if 0
    //    QMetaEnum statusEnum = QMetaEnum::fromType<EExecStatus>(); //非集成 qobject 类中 enum，无法注册
    //    QString status_str = statusEnum.valueToKey(status);
#else
    QString status_str;
    switch (status) {
    case eFATAL:
        status_str = "fatal";
        break;
    case eERROR:
        status_str = "error";
        break;
    case eWAIT:  //无
        status_str = "watting";
        break;
    case eOK:  //流程正常
        status_str = "executed";
        break;
    case eCOMP:  //流程完成
        status_str = "complete";
        break;
    case ePROCESS_COMP:  //流程完成
        status_str = "all complete";
        break;
    default:
        break;
    }
#endif
    ui->processView->setTextColor(Qt::black);
    //* 状态添加
    if ((CLenAdjustOpt::EProcessStep)step == CLenAdjustOpt::EProcessStep::eMAP_DATA ||
        (CLenAdjustOpt::EProcessStep)step == CLenAdjustOpt::EProcessStep::eADJUST_LEN) {
        ui->processView->undo();
        ui->processView->append(QString(step_str + ": " + status_str));
    } else
        ui->processView->append(QString(step_str + ": " + status_str));
}

// void cLensAdjust::processDdsShow(const int16_t &step, const QString &error) {
//    QMetaEnum stepEnum = QMetaEnum::fromType<CLenAdjustOpt::EProcessStep>();
//    QString step_str = stepEnum.valueToKey(step);

//    ui->processView->append(QString(step_str + ": " + error));
//}

void cLensAdjust::processTaskInfoShow_slot(const bool &is_error, const QString &info) {
    if (is_error) {
        ui->processView->setTextColor(Qt::red);
        //        ui->processView->append("<font color=\"#FF0000\"");
        //        ui->processView->setStyleSheet(process_unnormal_sheetStyle);
    } else {
        ui->processView->setTextColor(Qt::black);
        //        ui->processView->setStyleSheet(process_normal_sheetStyle);
    }
    LOG_INFO(MyLogger::LogType::RESULT_CACHE_DATA, QString("process view: %1").arg(info));
    ui->processView->append(info);
}

/**
 * @brief 显示界面清空
 */
void cLensAdjust::resultClean(void) {
    //* status bar clean
    ui->processView->setTextColor(Qt::black);
    //    ui->processView->setStyleSheet(process_normal_sheetStyle);
    ui->processView->clear();

    //* loc clean
    ui->move_x->setText("");
    ui->move_y->setText("");
    ui->move_z->setText("");
    ui->origin_coor_X->setText("");
    ui->origin_coor_Y->setText("");
    ui->origin_coor_Z->setText("");
    ui->cur_coor_X->setText("");
    ui->cur_coor_Y->setText("");
    ui->cur_coor_Z->setText("");


    //* result clean
    ui->adjustResult->setStyleSheet("color: black;");
    ui->adjustResult->setText("wait");

    ui->solidResult->setStyleSheet("color: black;");
    ui->solidResult->setText("wait");

    ui->finalResult->setStyleSheet("color: black;");
    ui->finalResult->setText("wait");
}


void cLensAdjust::readyShow(bool is_open) {
    if (is_open) {
        ui->serialOpen->setText("close");
        resultClean();

        if ((mst_config_->mode == IClensMachine::EMode::auto_mode) && m_is_unnormal_mode) {
            autoUnnormalModeShow(false);
        }
    }
}

/**
 * @brief ack status update
 */
void cLensAdjust::startAckShow() {
    //  if(status == EStepStatus::eFATAL || status == EStepStatus::eTIMEOUT)
    //    ui->serialOpen->setText("start");
}


void cLensAdjust::autoUnnormalModeShow(const bool &is_unnormal) {
    m_is_unnormal_mode = is_unnormal;
    if (!is_unnormal) {
        ui->mapShow->setCurrentIndex(0);  //

        ////        if(mc_operation_->mst_map_info_->xlens != 0 && mc_operation_->mst_map_info_->ylens != 0)
        //            IFaculaAdjust::targetFaculaAreaShow(ui->greyMap, mc_operation_->mst_map_info_);

        ////        if(mc_operation_->mst_interpolation_map_info_->xlens != 0 && mc_operation_->mst_interpolation_map_info_->ylens != 0)
        //            IFaculaAdjust::targetFaculaAreaShow(ui->greyMapExpand, mc_operation_->mst_interpolation_map_info_);
    } else {
        ui->mapShow->setCurrentIndex(1);  //

        //        if(mc_operation_->mst_map_info_->xlens != 0 && mc_operation_->mst_map_info_->ylens != 0)
        //            IFaculaAdjust::targetFaculaAreaShow(ui->handleGreyMap, mc_operation_->mst_map_info_);
    }
    modeTableChange();
}


// void cLensAdjust::faculaViewShow(const bool &show_view){
//  if(show_view) {
//      targetFaculaAreaShow(ui->greyMap, mc_operation_->mst_map_info_);
//      targetFaculaAreaShow(ui->greyMapExpand, mc_operation_->mst_interpolation_map_info_);
//    }
//  else {
//      targetFaculaAreaShow(ui->handleGreyMap, mc_operation_->mst_map_info_);
//    }
//}

/**
 * @brief 数据接收正常与异常 status update
 */
void cLensAdjust::dataAckShow(const uint &max, const QVector<QVector<uint32_t>> &matrix, const uint &max2, const QVector<QVector<uint32_t>> &matrix2) {
    if (mst_config_->mode == IClensMachine::EMode::auto_mode && !m_is_unnormal_mode) {
        mc_auto_grey_map_->greyMapShow(max, matrix);
        mc_expand_grey_map_->greyMapShow(max2, matrix2);

        //        IFaculaAdjust::greyMapShow(ui->greyMap, mc_operation_->mst_map_info_, *mc_operation_->m_map_data_); //正常图
        //        IFaculaAdjust::greyMapShow(ui->greyMapExpand, mc_operation_->mst_interpolation_map_info_, *mc_operation_->m_map_interpolation_data_); //拓展图
    } else {
        mc_manual_grey_map_->greyMapShow(max, matrix);
        //        IFaculaAdjust::greyMapShow(ui->handleGreyMap, mc_operation_->mst_map_info_, *mc_operation_->m_map_data_); //正常图
    }
}

/**
 * @brief cLensAdjust::movedLocShow
 * @param loc_x
 * @param loc_y
 * @param loc_z
 */
void cLensAdjust::movedLocShow(const int16_t &loc_x, const int16_t &loc_y, const int16_t &loc_z) {
    ui->move_x->setText(QString::number(loc_x, 10));
    ui->move_y->setText(QString::number(loc_y, 10));
    ui->move_z->setText(QString::number(loc_z, 10));
}

/**
 * @brief cLensAdjust::originCoordinateShow
 * @param loc_x
 * @param loc_y
 * @param loc_z
 */
void cLensAdjust::originCoordinateShow(const float &loc_x, const float &loc_y, const float &loc_z) {
    ui->origin_coor_X->setText(QString::number(loc_x, 'f', 3));
    ui->origin_coor_Y->setText(QString::number(loc_y, 'f', 3));
    ui->origin_coor_Z->setText(QString::number(loc_z, 'f', 3));
}

/**
 *@brief: manual mode coordinates show
 */
void cLensAdjust::adjustedCoordinatShow(const float &loc_x, const float &loc_y, const float &loc_z) {
    ui->cur_coor_X->setText(QString::number(loc_x, 'f', 3));
    ui->cur_coor_Y->setText(QString::number(loc_y, 'f', 3));
    ui->cur_coor_Z->setText(QString::number(loc_z, 'f', 3));
}

/**
 * @brief
 */
void cLensAdjust::compAckShow(bool is_comp) {
    Q_UNUSED(is_comp);
    //* 切换界面
    //  if((mst_config_->mode == IClensMachine::EMode::manual_mode) && (ui->modeBox->currentIndex() == 0)) //切换界面
    //  on_modeBox_currentIndexChanged(0);

    //  on_modeBox_currentIndexChanged(1);

    ui->serialOpen->setText("open");  //
}

/**
 * @brief 光斑结果显示
 * @param result
 * @param result_index
 */
void cLensAdjust::resultShow(EResult result, const uint8_t &result_index) {
    switch (result_index) {
    case 0:
        if (result == EResult::ePASS) {
            ui->adjustResult->setStyleSheet("color: green;");
            ui->adjustResult->setText("PASS");
        } else {
            ui->adjustResult->setStyleSheet("color: red;");
            ui->adjustResult->setText("NG");
        }
        break;
    case 1:
        if (result == EResult::ePASS) {
            ui->solidResult->setStyleSheet("color: green;");
            ui->solidResult->setText("PASS");
        } else {
            ui->solidResult->setStyleSheet("color: red;");
            ui->solidResult->setText("NG");
        }
        break;
    case 2:
        if (result == EResult::ePASS) {
            ui->finalResult->setStyleSheet("color: green;");
            ui->finalResult->setText("PASS");

        } else {
            ui->finalResult->setStyleSheet("color: red;");
            ui->finalResult->setText("NG");
        }
        break;
    default:
        break;
    }
}

// void cLensAdjust::errorAckShow(const QString &error_tring) {
//    QTextCharFormat color_format;
//    color_format.setForeground(QColor("red"));
//    QTextCursor cursor = ui->processView->textCursor();
//    cursor.insertText(error_tring, color_format);
//    color_format.setForeground(QColor("black"));
//    cursor.insertText(" ", color_format);
//}

/**
 * @brief cLensAdjust::productTestInfoShow
 */
void cLensAdjust::productTestInfoShow(const bool &is_clean) {
    ui->productTotalNum->setText(QString::number(mc_operation_->mst_product_detect_info_->product_total_num, 10));
    ui->goodProductNum->setText(QString::number(mc_operation_->mst_product_detect_info_->good_product_num, 10));
    ui->badProductNum->setText(QString::number(mc_operation_->mst_product_detect_info_->bad_product_num, 10));
    ui->badProductRate->setText(QString::number(mc_operation_->mst_product_detect_info_->bad_product_rate * 100, 'f', 2) + "%");

    ui->processTime->setText(QString::number(mc_operation_->mst_product_detect_info_->process_time, 'f', 1));
    ui->averProcessTime->setText(QString::number(mc_operation_->mst_product_detect_info_->aver_process_time, 'f', 1));
}

/**
 * @brief: 串口开关
 */
void cLensAdjust::on_serialOpen_clicked() {
    if (ui->serialOpen->text() == QString("open")) {  //
        updateConfig(mst_config_);                    //配置更新
        if (mst_config_->port_name == mst_config_->dev_port_name) {
            QMessageBox::information(this, "error", "device port can't the same with lidar port");
            return;
        } else {
            mst_config_->is_button_close = false;
            //            mc_operation_->mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.exec = true;
            mc_operation_->mv_task_list[CLenAdjustOpt::eOPEN_SERIAL].flag.exec = true;  //开始任务
        }
    } else {  //关闭->退出
        // if(mst_config_->mode) mc_operation_->mv_task_list[CLenAdjustOpt::eCOMPLETE].flag.stop = true; //结束任务
        mst_config_->is_button_close                                    = true;
        mc_operation_->mv_task_list[CLenAdjustOpt::eCOMPLETE].flag.stop = true;  //结束任务
    }
}

void cLensAdjust::on_portBox_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_port_name = ui->portBox->currentText();
}

void cLensAdjust::on_devicePortBox_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_dev_port_name = ui->devicePortBox->currentText();
}

void cLensAdjust::on_modeBox_currentIndexChanged(int index) {
    mst_config_->mode = (IClensMachine::EMode)index;
    ui->mapShow->setCurrentIndex(index);  //
    modeTableChange();

    //    if(mst_config_->mode == IClensMachine::EMode::auto_mode) {
    ////        if(mc_operation_->mst_map_info_->xlens != 0 && mc_operation_->mst_map_info_->ylens != 0)
    //            IFaculaAdjust::targetFaculaAreaShow(ui->greyMap, mc_operation_->mst_map_info_);

    ////        if(mc_operation_->mst_interpolation_map_info_->xlens != 0 && mc_operation_->mst_interpolation_map_info_->ylens != 0)
    //            IFaculaAdjust::targetFaculaAreaShow(ui->greyMapExpand, mc_operation_->mst_interpolation_map_info_);
    //    }
    //    else if(mst_config_->mode == IClensMachine::EMode::manual_mode) {
    //        //      mc_operation_->mv_task_list[CLenAdjustOpt::eMAP_DATA].flag.exec = true; //receive map data
    //        //      faculaViewShow(false);

    ////        if(mc_operation_->mst_map_info_->xlens != 0 && mc_operation_->mst_map_info_->ylens != 0)
    //            IFaculaAdjust::targetFaculaAreaShow(ui->handleGreyMap, mc_operation_->mst_map_info_);
    //    }
}

void cLensAdjust::on_pushButton_clicked() {
    mc_operation_->mst_move_distance_->x                              = 0;
    mc_operation_->mst_move_distance_->y                              = 0;
    mc_operation_->mst_move_distance_->z                              = Z_MIN_DISTANCE;
    mc_operation_->mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.exec = true;
}

void cLensAdjust::on_pushButton_6_clicked() {
    mc_operation_->mst_move_distance_->x                              = 0;
    mc_operation_->mst_move_distance_->y                              = 0;
    mc_operation_->mst_move_distance_->z                              = -Z_MIN_DISTANCE;
    mc_operation_->mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.exec = true;
}

void cLensAdjust::on_faculaType_currentIndexChanged(int index) {
    if (index == 0)
        ui->faculaArea->setEnabled(false);
    else
        ui->faculaArea->setEnabled(true);
}

void cLensAdjust::on_pushButton_2_clicked() {
    mc_operation_->mst_move_distance_->x                              = 0;
    mc_operation_->mst_move_distance_->y                              = 0;
    mc_operation_->mst_move_distance_->z                              = Z_SEND_DISTANCE;
    mc_operation_->mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.exec = true;

    //  m_device_info_->cur_move_distance.x = 0;
    //  m_device_info_->cur_move_distance.y = 0;
    //  m_device_info_->cur_move_distance.z = Z_SEND_DISTANCE;
    //  m_status_flag_->status.manual_move = true;
}

void cLensAdjust::on_pushButton_7_clicked() {
    mc_operation_->mst_move_distance_->x                              = 0;
    mc_operation_->mst_move_distance_->y                              = 0;
    mc_operation_->mst_move_distance_->z                              = -Z_SEND_DISTANCE;
    mc_operation_->mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.exec = true;
}

void cLensAdjust::on_mesDataInput_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eCALIB_MODE].flag.exec = true;
}

void cLensAdjust::lenMesDataFB(bool closed) {
    //    if(closed && mc_lens_data_mes_ != nullptr) {
    //        QObject::disconnect(mc_lens_data_mes_, &clenDataMes::windowCloseSiganl, this, &cLensAdjust::lenMesDataFB); //断开连接
    ////        timeId_ptr_->id6 = false;
    //        delete mc_lens_data_mes_;
    //        mc_lens_data_mes_ = nullptr;
    //        this->showNormal();
    //    }
}


void cLensAdjust::on_manualAdjust_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eMAP_DATA].flag.exec = true;
    //    mc_operation_->mv_task_list[CLenAdjustOpt::eMAP_DATA].flag.stop = true;
}

void cLensAdjust::on_manualSolid_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eSOLID].flag.exec = true;
}

void cLensAdjust::on_manualDeflate_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eDEFLATE].flag.exec = true;
}

void cLensAdjust::on_manualExit_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eCOMPLETE].flag.exec = true;
}

void cLensAdjust::on_retest_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eRETEST2].flag.exec = true;
}

void cLensAdjust::on_greyMode_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eGREY_MODE].flag.exec = true;
}

void cLensAdjust::on_productionInfoClean_clicked() {
    mc_operation_->mst_product_detect_info_->product_total_num = 0;
    mc_operation_->mst_product_detect_info_->good_product_num  = 0;
    mc_operation_->mst_product_detect_info_->bad_product_num   = 0;
    mc_operation_->mst_product_detect_info_->bad_product_rate  = 0;
    mc_operation_->mst_product_detect_info_->process_time      = 0;
    mc_operation_->mst_product_detect_info_->aver_process_time = 0;

    productTestInfoShow(true);
}

void cLensAdjust::on_coordinate_clicked() {
    mc_operation_->mv_task_list[CLenAdjustOpt::eORIGIN_LOC].flag.exec = true;
}
