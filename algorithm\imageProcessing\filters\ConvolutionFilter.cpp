#include "ConvolutionFilter.h"
#include <QtMath>

namespace ImageProcessing {

ConvolutionFilter::ConvolutionFilter() {
    // 设置默认参数和3x3锐化核
    params_.strength   = 1.0f;
    params_.enabled    = true;
    params_.kernelSize = 3;
    params_.normalize  = true;

    // 默认锐化核
    params_.kernel.resize(3);
    for (auto &row : params_.kernel) {
        row.resize(3);
    }
    params_.kernel[0] = {0.0f, -1.0f, 0.0f};
    params_.kernel[1] = {-1.0f, 5.0f, -1.0f};
    params_.kernel[2] = {0.0f, -1.0f, 0.0f};

    logDebug("ConvolutionFilter initialized with sharpening kernel");
}

bool ConvolutionFilter::apply(ImageDataU32 &data) {
    try {
        validateInput(data);

        if (!params_.enabled) {
            logDebug("Filter disabled, skipping processing");
            return true;
        }

        logDebug(QString("Applying convolution filter to %1x%2 image").arg(data.width()).arg(data.height()));

        // 创建临时图像存储结果
        ImageDataU32 temp(data.width(), data.height());

        // 对每个像素应用卷积
        for (uint32_t y = 0; y < data.height(); ++y) {
            for (uint32_t x = 0; x < data.width(); ++x) {
                float convResult = applyConvolutionAtPixel(data, x, y);

                // 应用滤波强度
                float originalValue = static_cast<float>(data.matrix()[y][x]);
                float filteredValue = originalValue + params_.strength * (convResult - originalValue);

                temp.matrix()[y][x] = safeFloatToUint32(filteredValue);
            }
        }

        // 复制结果回原图像
        data = std::move(temp);

        logDebug("Convolution filter applied successfully");
        return true;

    } catch (const ProcessingException &e) {
        qWarning() << "ConvolutionFilter::apply failed:" << e.qMessage();
        return false;
    } catch (const std::exception &e) {
        qWarning() << "ConvolutionFilter::apply failed:" << e.what();
        return false;
    }
}

bool ConvolutionFilter::apply(const ImageDataU32 &src, ImageDataU32 &dst) {
    try {
        validateInput(src);

        // 确保目标图像尺寸正确
        if (dst.width() != src.width() || dst.height() != src.height()) {
            dst.resize(src.width(), src.height());
        }

        // 复制源数据到目标
        dst = src;

        // 应用滤波
        return apply(dst);

    } catch (const ProcessingException &e) {
        qWarning() << "ConvolutionFilter::apply (src->dst) failed:" << e.qMessage();
        return false;
    } catch (const std::exception &e) {
        qWarning() << "ConvolutionFilter::apply (src->dst) failed:" << e.what();
        return false;
    }
}

void ConvolutionFilter::setParameters(const FilterParams &params) {
    const ConvolutionParams *convParams = dynamic_cast<const ConvolutionParams *>(&params);
    if (!convParams) {
        throw InvalidParameterException("params", "must be ConvolutionParams type");
    }

    validateConvolutionParams(*convParams);
    params_ = *convParams;

    if (params_.normalize) {
        normalizeKernel();
    }

    logDebug("Parameters updated: " + params_.toString());
}

std::unique_ptr<FilterParams> ConvolutionFilter::getParameters() const {
    return std::make_unique<ConvolutionParams>(params_);
}

QString ConvolutionFilter::getAlgorithmName() const {
    return "ConvolutionFilter";
}

QString ConvolutionFilter::getDescription() const {
    return "General-purpose convolution filter with customizable kernels";
}

bool ConvolutionFilter::isSupported(uint32_t width, uint32_t height) const {
    try {
        ValidationUtils::validatePositive(width, "width");
        ValidationUtils::validatePositive(height, "height");

        // 检查图像是否足够大以应用卷积核
        uint32_t minSize = static_cast<uint32_t>(params_.kernelSize);
        return width >= minSize && height >= minSize;
    } catch (const ProcessingException &) {
        return false;
    }
}

uint32_t ConvolutionFilter::estimateProcessingTime(uint32_t width, uint32_t height) const {
    // 卷积操作复杂度与核大小相关
    uint64_t totalPixels = static_cast<uint64_t>(width) * height;
    uint64_t kernelOps   = static_cast<uint64_t>(params_.kernelSize) * params_.kernelSize;
    // 假设每个卷积操作需要0.001毫秒
    return static_cast<uint32_t>((totalPixels * kernelOps) / 1000);
}

void ConvolutionFilter::reset() {
    params_.reset();
    logDebug("ConvolutionFilter reset to default state");
}

QString ConvolutionFilter::getVersion() const {
    return "1.0.0";
}

bool ConvolutionFilter::isThreadSafe() const {
    return true;  // 无状态操作，线程安全
}

bool ConvolutionFilter::supportsInPlace() const {
    return false;  // 卷积需要临时缓冲区
}

void ConvolutionFilter::setPredefinedKernel(const QString &type) {
    qDebug() << "ConvolutionFilter: 开始设置预定义核" << type;

    params_.kernel     = createPredefinedKernel(type);
    params_.kernelSize = params_.kernel.size();

    qDebug() << "ConvolutionFilter: 核矩阵大小" << params_.kernelSize << "x" << params_.kernelSize;

    if (params_.normalize) {
        normalizeKernel();
        qDebug() << "ConvolutionFilter: 核矩阵已归一化";
    }

    logDebug(QString("ConvolutionFilter: 成功设置预定义核: %1").arg(type));
}

QStringList ConvolutionFilter::getSupportedKernelTypes() {
    return {"sharpen", "blur", "edge_detect", "emboss", "identity"};
}

float ConvolutionFilter::applyConvolutionAtPixel(const ImageDataU32 &src, uint32_t x, uint32_t y) const {
    float result     = 0.0f;
    int   halfKernel = params_.kernelSize / 2;

    for (int ky = 0; ky < params_.kernelSize; ++ky) {
        for (int kx = 0; kx < params_.kernelSize; ++kx) {
            int srcX = static_cast<int>(x) + kx - halfKernel;
            int srcY = static_cast<int>(y) + ky - halfKernel;

            uint32_t pixelValue = getSafePixelValue(src, srcX, srcY);
            result += static_cast<float>(pixelValue) * params_.kernel[ky][kx];
        }
    }

    return result;
}

uint32_t ConvolutionFilter::getSafePixelValue(const ImageDataU32 &src, int x, int y) const {
    // 边界处理：镜像扩展
    if (x < 0)
        x = -x;
    if (y < 0)
        y = -y;
    if (x >= static_cast<int>(src.width()))
        x = 2 * (src.width() - 1) - x;
    if (y >= static_cast<int>(src.height()))
        y = 2 * (src.height() - 1) - y;

    // 确保在有效范围内
    x = qBound(0, x, static_cast<int>(src.width() - 1));
    y = qBound(0, y, static_cast<int>(src.height() - 1));

    return src.matrix()[y][x];
}

void ConvolutionFilter::normalizeKernel() {
    float sum = 0.0f;

    // 计算核的总和
    for (const auto &row : params_.kernel) {
        for (float value : row) {
            sum += qAbs(value);
        }
    }

    // 如果总和为0，不进行归一化
    if (qFuzzyIsNull(sum)) {
        logDebug("Kernel sum is zero, skipping normalization");
        return;
    }

    // 归一化
    for (auto &row : params_.kernel) {
        for (float &value : row) {
            value /= sum;
        }
    }

    logDebug(QString("Kernel normalized with sum: %1").arg(sum));
}

QVector<QVector<float>> ConvolutionFilter::createPredefinedKernel(const QString &type) const {
    QVector<QVector<float>> kernel;

    if (type == "sharpen") {
        kernel = {{0.0f, -1.0f, 0.0f}, {-1.0f, 5.0f, -1.0f}, {0.0f, -1.0f, 0.0f}};
    } else if (type == "blur") {
        kernel = {{1.0f, 1.0f, 1.0f}, {1.0f, 1.0f, 1.0f}, {1.0f, 1.0f, 1.0f}};
    } else if (type == "edge_detect") {
        kernel = {{-1.0f, -1.0f, -1.0f}, {-1.0f, 8.0f, -1.0f}, {-1.0f, -1.0f, -1.0f}};
    } else if (type == "emboss") {
        kernel = {{-2.0f, -1.0f, 0.0f}, {-1.0f, 1.0f, 1.0f}, {0.0f, 1.0f, 2.0f}};
    } else if (type == "identity") {
        kernel = {{0.0f, 0.0f, 0.0f}, {0.0f, 1.0f, 0.0f}, {0.0f, 0.0f, 0.0f}};
    } else {
        throw InvalidParameterException("kernel type", QString("unsupported type: %1").arg(type));
    }

    return kernel;
}

void ConvolutionFilter::validateConvolutionParams(const ConvolutionParams &params) const {
    params.validate();
}

void ConvolutionFilter::logDebug(const QString &message) const {
    qDebug() << "[ConvolutionFilter]" << message;
}

}  // namespace ImageProcessing
