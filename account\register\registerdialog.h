#ifndef REGISTERDIALOG_H
#define REGISTERDIALOG_H

#include <QDialog>

namespace Ui {
class registerDialog;
}

class registerDialog : public QDialog
{
    Q_OBJECT

public:
    explicit registerDialog(QWidget *parent = nullptr);
    ~registerDialog();

private slots:
    void on_pButtonRegisterOK_clicked();

    void on_pButtonRegisterExit_clicked();

private:
    Ui::registerDialog *ui;
};

#endif // REGISTERDIALOG_H
