# 项目名称

本项目基于 mcp-feedback-enhanced 包，使用 Python（官方版本，非store版本） 虚拟环境管理依赖，支持快速测试和开发。

## 目录结构

```
.
├── .venv/                     # 项目虚拟环境
├── mcp-feedback-enhanced/     # 源码包（可选）
├── test_reports/              # 测试报告
├── README.md                  # 项目说明文件
└── ...
```

## 环境配置

1. 创建虚拟环境：

```powershell
python -m venv .venv
```

2. 激活虚拟环境：
- Windows PowerShell:
  
  ```powershell
  .venv\Scripts\activate
  ```

- Windows CMD:
  
  ```cmd
  .\.venv\Scriptsctivate.bat
  ```

- Linux/macOS:
  
  ```bash
  source .venv/bin/activate
  ```

## 注意事项

- 

### 为什么依赖包下载失败？

可能网络原因导致下载超时，尝试增加超时时间或使用镜像源。

## 联系

如有问题，请联系项目维护者。
