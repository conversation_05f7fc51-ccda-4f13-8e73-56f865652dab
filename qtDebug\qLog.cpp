/**
 * @file qLog.cpp
 * <AUTHOR> name (<EMAIL>)
 * @brief QT类型数据日志处理
 * @version 0.1
 * @date 2024-12-25
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "qLog.h"

// 添加命名空间
namespace MyLogger {

// 静态成员初始化
// LogLevel QLog::s_logLevel      = LogLevel::DEBUG;
QString QLog::s_logPath       = "logs/";
QString QLog::s_logFile       = "error.log";  //"logs/app.log";
bool    QLog::s_enableConsole = false;
bool    QLog::s_enableFile    = false;

// bool enableConsole, bool enableFile
void QLog::init() {
#ifdef LOG_TO_CONSOLE
    s_enableConsole = true;
    s_enableFile    = false;
#else
    s_enableConsole = false;
    s_enableFile    = true;
#endif
    // if (s_enableFile) {
    //     QDir dir;
    //     dir.mkpath(QFileInfo(s_logFile).absolutePath());
    // }
}

void QLog::installMessageHandler() {
    // Remove any existing message handler first
    qInstallMessageHandler(nullptr);
    // Install our custom handler
    qInstallMessageHandler(QLog::messageHandler);
}

// void QLog::setLogLevel(LogLevel level) {
//     s_logLevel = level;
// }

void QLog::setLogPath(const QString &path) {
    s_logPath = path;
    s_logFile = path + "app.log";
    if (s_enableFile) {
        QDir dir;
        dir.mkpath(QFileInfo(s_logFile).absolutePath());
    }
}

QString QLog::getColorCode(QtMsgType level) {
    switch (level) {
    case QtDebugMsg:
        return "\033[37m";  // 白色
    case QtInfoMsg:
        return "\033[32m";  // 绿色
    case QtWarningMsg:
        return "\033[33m";  // 黄色
    case QtCriticalMsg:
        return "\033[31m";  // 红色
    case QtFatalMsg:
        return "\033[35m";  // 紫色
    default:
        return "\033[0m";  // 重置
    }
}

QString QLog::getLogTypeStr(LogType type) {
    switch (type) {
    case LogType::INIT:
        return "INIT";
    case LogType::COMM:
        return "COMM";
    case LogType::COMM_ACK:
        return "ACK";
    case LogType::PROCESS_DATA:
        return "PROC_DATA";
    case LogType::RESULT_CACHE_DATA:
        return "RES_CACHE";
    case LogType::PROCESS_STATUS:
        return "PROC_STA";
    case LogType::ERROR_LOG:
        return "ERROR";
    default:
        return "DEF";
    }
}

void QLog::rotateLogFile() {
    QFile file(s_logFile);
    if (!file.exists() || file.size() < MAX_LOG_SIZE) {
        return;
    }

    // 删除最老的备份文件（如果存在）
    QString oldestBackup = s_logFile + "." + QString::number(MAX_BACKUP_COUNT);
    QFile::remove(oldestBackup);

    // 重命名现有的备份文件
    for (int i = MAX_BACKUP_COUNT - 1; i >= 1; --i) {
        QString oldName = s_logFile + "." + QString::number(i);
        QString newName = s_logFile + "." + QString::number(i + 1);
        QFile::rename(oldName, newName);
    }

    // 重命名当前日志文件为.1
    file.rename(s_logFile + ".1");
}

void QLog::writeToFile(const QString &msg, bool addTimestamp) {
    if (!s_enableFile)
        return;

    rotateLogFile();

    QFile file(s_logFile);
    if (file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        QTextStream stream(&file);
        if (addTimestamp) {
            stream << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz ");
        }
        stream << msg << "\n";
        file.close();
    }
}

void QLog::messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg) {
    QString log_level_tr;

    switch (type) {
    case QtDebugMsg:
        log_level_tr = "[D]";
        break;
    case QtInfoMsg:
        log_level_tr = "[I]";
        break;
    case QtWarningMsg:
        log_level_tr = "[W]";
        break;
    case QtCriticalMsg:
        log_level_tr = "[C]";
        break;
    case QtFatalMsg:
        log_level_tr = "[F]";
        break;
    }
    // add extra date time
    // QDateTime currentDateTime = QDateTime::currentDateTime();
    // QString log_msg = QString("%1 %2 %3").arg(currentDateTime.toString("yyyy-MM-dd hh:mm:ss")).arg(log_level_tr).arg(msg);
    QString log_msg = QString("%1 %2").arg(log_level_tr).arg(msg);

    // 添加文件名、行号、函数信息
    // if (context.file) {
    //     log_msg += QString(" (%1:%2, %3)").arg(context.file).arg(context.line).arg(context.function);
    // }

    // 输出到控制台
    if (s_enableConsole) {
        fprintf(stderr, "%s%s\033[0m\n", getColorCode(type).toLocal8Bit().constData(), log_msg.toLocal8Bit().constData());
    }

    // 写入日志文件
    if (s_enableFile) {
        writeToFile(log_msg);
    }

    if (type == QtFatalMsg) {
        abort();
    }
}

// 显式实例化，正确的语法
// template QString QLog::formatLogMessage(LogType, const char *, int, const char *);
// template QString QLog::formatLogMessage(LogType, const char *, int, const QString &);
// template QString QLog::formatLogMessage(LogType, const char *, int, int);
// template QString QLog::formatLogMessage(LogType, const char *, int, double);
// template QString QLog::formatLogMessage(LogType, const char *, int, const char *, int);
// template QString QLog::formatLogMessage(LogType, const char *, int, const QString &, int);
// template QString QLog::formatLogMessage(LogType, const char *, int, const char *, const QString &);
}  // namespace MyLogger
