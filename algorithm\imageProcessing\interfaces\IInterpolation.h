#ifndef IMAGEPROCESSING_IINTERPOLATION_H
#define IMAGEPROCESSING_IINTERPOLATION_H

#include "../common/ImageData.h"
#include "../common/ProcessingException.h"
#include <QString>
#include <memory>

namespace ImageProcessing {

/**
 * @brief 插值参数结构体
 */
struct InterpolationParams {
    float    offset        = 0.5f;        ///< 插值偏移量（用于缩小时的对齐）
    bool     preserveEdges = true;        ///< 是否保持边缘
    bool     clampValues   = true;        ///< 是否限制数值范围
    uint32_t minValue      = 0;           ///< 最小值限制
    uint32_t maxValue      = UINT32_MAX;  ///< 最大值限制

    /**
     * @brief 验证参数有效性
     * @throws InvalidParameterException 如果参数无效
     */
    void validate() const;

    /**
     * @brief 重置为默认值
     */
    void reset();

    /**
     * @brief 比较操作符
     */
    bool operator==(const InterpolationParams &other) const;
    bool operator!=(const InterpolationParams &other) const;

    /**
     * @brief 调试输出
     */
    QString toString() const;
};

/**
 * @brief 插值算法抽象接口
 *
 * 定义了所有插值算法必须实现的接口，遵循接口隔离原则
 */
class IInterpolation {
  public:
    virtual ~IInterpolation() = default;

    /**
     * @brief 执行插值操作
     * @param src 源图像数据
     * @param dst 目标图像数据（会被修改）
     * @return true if successful, false otherwise
     * @throws ProcessingException 如果插值失败
     */
    virtual bool interpolate(const ImageDataU32 &src, ImageDataU32 &dst) = 0;

    /**
     * @brief 设置插值参数
     * @param params 插值参数
     * @throws InvalidParameterException 如果参数无效
     */
    virtual void setParameters(const InterpolationParams &params) = 0;

    /**
     * @brief 获取当前插值参数
     * @return 当前参数
     */
    virtual InterpolationParams getParameters() const = 0;

    /**
     * @brief 获取算法名称
     * @return 算法名称
     */
    virtual QString getAlgorithmName() const = 0;

    /**
     * @brief 获取算法描述
     * @return 算法描述
     */
    virtual QString getDescription() const = 0;

    /**
     * @brief 检查是否支持指定的尺寸变换
     * @param srcWidth 源图像宽度
     * @param srcHeight 源图像高度
     * @param dstWidth 目标图像宽度
     * @param dstHeight 目标图像高度
     * @return true if supported, false otherwise
     */
    virtual bool isSupported(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const = 0;

    /**
     * @brief 预估处理时间（毫秒）
     * @param srcWidth 源图像宽度
     * @param srcHeight 源图像高度
     * @param dstWidth 目标图像宽度
     * @param dstHeight 目标图像高度
     * @return 预估处理时间（毫秒）
     */
    virtual uint32_t estimateProcessingTime(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const = 0;

    /**
     * @brief 重置算法状态
     */
    virtual void reset() = 0;

    /**
     * @brief 获取算法版本
     * @return 版本字符串
     */
    virtual QString getVersion() const {
        return "1.0.0";
    }

    /**
     * @brief 是否为线程安全的实现
     * @return true if thread-safe, false otherwise
     */
    virtual bool isThreadSafe() const {
        return false;
    }

  protected:
    /**
     * @brief 验证输入参数的通用方法
     * @param src 源图像
     * @param dst 目标图像
     * @throws InvalidImageDataException 如果数据无效
     */
    void validateInputs(const ImageDataU32 &src, const ImageDataU32 &dst) const;

    /**
     * @brief 限制数值范围
     * @param value 输入值
     * @param minVal 最小值
     * @param maxVal 最大值
     * @return 限制后的值
     */
    uint32_t clampValue(uint32_t value, uint32_t minVal, uint32_t maxVal) const;

    /**
     * @brief 安全的浮点数到整数转换
     * @param value 浮点数值
     * @return 转换后的整数值
     */
    uint32_t safeFloatToUint32(float value) const;
};

/**
 * @brief 插值算法类型枚举
 */
enum class InterpolationType {
    None,             ///< 不插值（直接使用原始数据）
    Bilinear,         ///< 双线性插值
    Bicubic,          ///< 双三次插值
    Nonlinear,        ///< 非线性插值
    NearestNeighbor,  ///< 最近邻插值
    InterpolationTypeLast,
};

/**
 * @brief 插值算法工厂接口
 */
class IInterpolationFactory {
  public:
    virtual ~IInterpolationFactory() = default;

    /**
     * @brief 创建插值算法实例
     * @param type 插值类型
     * @return 插值算法实例的智能指针
     * @throws UnsupportedOperationException 如果不支持指定类型
     */
    virtual std::unique_ptr<IInterpolation> createInterpolation(InterpolationType type) = 0;

    /**
     * @brief 获取支持的插值类型列表
     * @return 支持的类型列表
     */
    virtual QVector<InterpolationType> getSupportedTypes() const = 0;

    /**
     * @brief 获取指定类型的描述
     * @param type 插值类型
     * @return 类型描述
     */
    virtual QString getTypeDescription(InterpolationType type) const = 0;
};

}  // namespace ImageProcessing

#endif  // IMAGEPROCESSING_IINTERPOLATION_H
