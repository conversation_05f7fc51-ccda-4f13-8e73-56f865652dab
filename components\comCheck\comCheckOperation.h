#ifndef _COMCHECK_OPERATION_H_
#define _COMCHECK_OPERATION_H_

#include <QObject>
#include <QMap>

#include "processListB.h"

#include "ITestBoard.h"
#include "IPms.h"
#include "IBottom.h"
#include "ITopBoard.h"

#include "testBoardSerial.h"
#include "spmsSerial.h"
#include "bottomBoardSerial.h"
#include "topBoardSerial.h"

#include "ILoad.h"
#include "ISaveFile.h"
#include "qLog.h"

namespace NComCheck {
typedef struct {
    QString                     cur_port_name; //实时显示端口
    QString                     port_name; //使用端口
    uint16_t                    cur_baud;
    uint16_t                    baud;

    int16_t                     project_index;
    QString                     project_name;
    bool                        isWhileSend;   //
} StUiConfig;
}

//*
//class IBottom;

//class ITopBoard;

class CComCheckOpt:public QObject{
    Q_OBJECT

public:
    CComCheckOpt(const NComCheck::StUiConfig &st_config);
    ~CComCheckOpt();

    enum EProject {
        eMINI_YJ_BOTTOM         = 0,
        eMINI_SAM_BOTTOM,
        eCOIN_D4_BOTTOM,
        eCOIN_D4_SENSOR,
        eMINI_T4_BOTTOM,
        eMINI_T4_SNESOR,
        eDETECT_BOARD_D4,
        eDETECT_BOARD_T4,
        eNOVA_A1,
        eNOVA_A1B,
    };
    //*************************************** 运行步骤 *******************
    enum EProcessStep {
//        eOPEN_SERIAL        = 0,
        eSTART              = 0,
        eCOMPLETE           = 0,

    };
    Q_ENUM(EProcessStep);
    Q_ENUM(EProject);

private:
    typedef struct {
        bool exec; //执行
        bool stop; //
        bool have_ack; //

    } StTaskFlag; //任务状态

    typedef struct{
        StTaskFlag flag; //运行状态
        uint interval_time; //运行间隔
        uint task_limit_time; //运行时间限制
        uint time_cnt; //运行时间计数
    } StTask;

    //* cmd view status
    typedef struct {
        bool       isCirculation;
        QString    cmd;
        uint8_t    priority; //small num, high priority
        uint16_t   interval; //ms
    } StCmdInfo;

    //************************************* basic ***************
    //* config
    ILoad* mi_load_ = nullptr;
    QMap<QString, int> m_xml_param;
    const NComCheck::StUiConfig *mst_config_ = nullptr;

    //* process/status
    int m_timerId;
    uint16_t m_port_update_cnt;

    //* sub thread
    QThread* m_sub_thread_ = nullptr;
    CTestBoardSerial *m_test_board_thread_ = nullptr;
    CSpmsSerial *m_spms_thread_ = nullptr;
    CBottomBoardSerial *m_bottom_board_thread_ = nullptr;
    CTopBoardSerial *m_top_board_thread_ = nullptr;



public:
    QVector<StTask>  mv_task_list;

    //* port
    IComm* mi_icomm_ = nullptr; //main port
    IComm* mi_icomm_sub_ = nullptr; //sub port

    //* devices
    ITestBoard *mi_test_board_ = nullptr;
    IPms *mi_spms_ = nullptr;
    IBottom *mi_bottom_board_ = nullptr;
    ITopBoard *mi_top_board_ = nullptr;

    //* communication status
    StCommunicateStatus* mst_comm_status_ = nullptr;

    //* data
    typedef struct {
        QByteArray         send_cmd;
        QByteArray         ack_cmd;
    } StCommCmd;

    QVector<StCmdInfo> mvst_cmd_list;
    StCommCmd *mst_cur_cmd_ = nullptr;

    //* communite result
    typedef struct {
        uint                check_error_cnt;
        uint                timeout_cnt;

        QList<StCommCmd>     ml_error_cmd;
    } StResult;
    StResult *mst_result_ = nullptr;

    //* debug
    QWidget *m_message_box_ = nullptr;

    //* data storage
    ISaveFile* mi_save_file_ = nullptr;
    QMap<QString, QVariant> mm_result_data; // = nullptr;

    void varibleInit();
    void resultInit(StResult* result_);

    //****************************** task lists *****************
    virtual void timerEvent(QTimerEvent *event) override;
    bool portlistUpdate();

    //*
    QMap<QString, QByteArray>* getCmdList(const int16_t &proj_index);
    bool writeCmdList(const int16_t &proj_index, const QMap<QString, QByteArray> &bytes);

signals:
    void stepStatusSignal(const int16_t &step, const int16_t &status);

    void subThreadSignal(bool is_exc);

    void portUpdateSignal(QStringList *port_list_, bool port_flag);

    void readySignal(bool is_open);

    void cmdListShowSignal();
    void sendedCmdShowSignal(QByteArray cmd);
    void ackedCmdShowSignal(QByteArray cmd);

    void cmdPrasedSignal();

    void resultShowSignal();

public slots:
    void cmdModifyUpdate_slot(const uint8_t index, QByteArray cmd);
    void dataReceive(ITestBoard::ECommStep step, ECommStatus status, QByteArray bytes);
};



#endif
