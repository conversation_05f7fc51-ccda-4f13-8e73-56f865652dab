#ifndef _SPMS_SERIAL_H_
#define _SPMS_SERIAL_H_

#include <QString>
#include <QMessageBox>
#include <QDebug>
#include <QTime>
#include <QTimer>

#include "IPms.h"

class CSpmsSerial: public QObject
{
    Q_OBJECT
public:
    explicit CSpmsSerial(QObject *parent = nullptr, IPms *pms_ = nullptr);
    ~CSpmsSerial();

    int8_t     m_task_id;

    void device_change_interface(IPms* pms_); //设备接口调整
    void taskIdChange(const uint8_t &id);
public slots:
    //void portInit(bool isOpen);
    void loop(int task_id); //接收串口数据API

private:
    IPms *mi_pms_ = nullptr;
};

#endif
