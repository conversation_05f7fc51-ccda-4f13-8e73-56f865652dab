#!/usr/bin/env python
# 脚本路径: scripts/requirements/add_requirement.py

import os
import re
import argparse

def add_requirement(matrix_path, req_id, description, status, parent_req=None, test_standard=None):
    """向需求矩阵添加新需求"""
    with open(matrix_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取表头以确定矩阵类型
    headers = re.search(r'\|(.*)\|', content).group(1)
    
    # 根据不同类型的矩阵构建新行
    if '上层需求' not in headers and '测试标准' not in headers:
        # 顶层矩阵
        new_row = f"| {req_id} | {description} | {status} | |"
    elif '测试标准' in headers:
        # 客户层矩阵
        parent_link = f"[{parent_req}](#)" if parent_req else "-"
        new_row = f"| {req_id} | {parent_link} | {description} | {status} | {test_standard or '-'} |"
    else:
        # 中间层或产品层矩阵
        parent_link = f"[{parent_req}](#)" if parent_req else "-"
        new_row = f"| {req_id} | {parent_link} | {description} | {status} | |"
    
    # 找到表格末尾并添加新行
    table_end = content.rfind('|')
    table_end = content.rfind('\n', 0, table_end) + 1
    
    updated_content = content[:table_end] + new_row + "\n" + content[table_end:]
    
    # 写回文件
    with open(matrix_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"已添加需求 {req_id} 到矩阵: {matrix_path}")

def main():
    parser = argparse.ArgumentParser(description='添加需求到矩阵')
    parser.add_argument('--matrix', required=True, help='需求矩阵文件路径')
    parser.add_argument('--id', required=True, help='需求ID')
    parser.add_argument('--desc', required=True, help='需求描述')
    parser.add_argument('--status', default='计划中', help='需求状态')
    parser.add_argument('--parent', help='上层需求ID')
    parser.add_argument('--test', help='测试标准（仅客户层）')
    args = parser.parse_args()
    
    add_requirement(args.matrix, args.id, args.desc, args.status, args.parent, args.test)

if __name__ == "__main__":
    main() 