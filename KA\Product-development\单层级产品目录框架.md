# 单层级产品目录框架

## 1. 框架概述

本文档提出了一种基于单层目录的产品开发框架，通过ID表格记录和管理组件间关系，避免多层目录结构带来的文件夹过多问题，同时保持组件间的关联性和可追溯性。

### 1.1 核心原则

1. **单层目录结构**：所有组件文档放在同一层目录下，避免过深的目录嵌套
2. **ID表格管理**：每个组件维护一个ID表格，记录内部文档及其与其他组件的关联
3. **组件关系配置**：在一体化流程中配置组件间的关联关系
4. **统一命名规范**：采用规范化的文件命名方式，确保文件可识别性
5. **自动化关联维护**：通过脚本自动更新和维护组件间的关联关系

### 1.2 优势

- **简化目录结构**：减少文件夹层级，提高文件查找效率
- **灵活的组件关联**：通过ID表格而非目录结构建立组件间关系
- **降低维护成本**：集中管理文件，减少跨文件夹操作
- **提高可视化效率**：便于生成统一的关系图谱和追溯报告

## 2. 目录结构设计

### 2.1 目录结构

```
product_development/
├── product.canvas                         # 整个产品关联文档
├── product_info/                          # 产品基本信息
│   ├── product_brief.md                   # 产品简介
│   ├── market_analysis.md                 # 市场分析
│   ├── competitor_products/               # 竞争产品资料目录
│   │   ├── competitor_A/                  # 竞争对手A的产品资料
│   │   └── competitor_B/                  # 竞争对手B的产品资料
│   │
│   └── business_model.md                  # 商业模式
├── requirements/                           # 需求相关文档
│   ├── main_requirements/                  # 产品主需求文档
│   ├── custom_requirements/                # 客户需求文档 
│   ├── market_requirements/                # 市场需求
│   └── requirement_traceability.md        # 需求追踪矩阵
├── design/                                 # 设计方案文档
│   ├── project_solution/                   # 项目方案
│   │   ├── design.excalidraw.md            # 整体方案(手写, excalidraw格式)
|   |   └── system_architecture.md          # 整体方案和系统架构
│   ├── components/                         # 器件组件等资料
│   ├── principle_Information/              # 原理资料
│   ├── specification_document/             # 规格文档
│   ├── interface_specifications/               # 接口规范
│   ├── project_customer_code_nameMapping.md   # 项目名称-代号映射文档 
│   └── design_decisions.md                     # 设计决策记录
├── development/                            # 开发实施文档
│   ├── hardware/                 # 硬件开发工程
│   │   ├── hw_project_1/                  # 硬件子项目1(例如:主控PCB)
│   │   └── hw_project_2/                  # 硬件子项目2(例如:传感器模块)
│   │
│   ├── firmware/                 # 固件开发工程
│   │   ├── fw_project_1/                  # 固件子项目1(例如:主控固件)
│   │   └── fw_project_2/                  # 固件子项目2(例如:传感器固件)
│   │
│   ├── software/                 # 软件开发工程
│   │   ├── app_project/                   # 应用软件项目(例如:Qt上位机)
│   │   └── cloud_project/                 # 云端服务项目
│   │
│   └── tool/                     # 工具开发工程
│       ├── production_tools/              # 生产工具
│       └── debug_tools/                   # 调试工具
├── quality/                                # 测试验证文档
│   ├── test_plans/                        # 测试计划
│   ├── test_cases/                        # 测试用例
│   ├── test_reports/                      # 测试报告
│   │   ├── internal_tests/                # 内部产品测试报告
│   │   └── competitive_tests/             # 竞争产品测试数据和报告
│   └── issues/                            # 问题追踪
├── production/                             # 生产相关文档
│   ├── bom/                               # 物料清单
│   ├── manufacturing_process/             # 生产工艺
│   └── quality_control/                   # 质量控制
├── deliverables/                           # 交付物文档
│   ├── documents/                         # 
│   ├── firmware/                          # 
│   ├── hardware/                          # 
│   ├── software/                          #
│   ├── deliverables_manifest.md           # 
│   └── tools/                             # 
├── project_management/                     # 项目管理文档
│   ├── schedules/                         # 项目进度
│   ├── resources/                         # 资源分配
│   ├── risks/                             # 风险管理
│   └── meeting_notes/                     # 会议记录
├── scripts/                                # 脚本工具
├── config/                                 # 配置文件
└── reports/                                # 报告输出
```

### 2.2 组件命名规范

文件命名采用以下格式：`[组件代号]_[文档类型]_[文档名称].[扩展名]`

例如：

- `REQ_MATRIX_产品需求矩阵.md` - 需求组件中的需求矩阵文档
- `DEV_SPEC_固件开发规格.md` - 开发组件中的固件开发规格文档
