#!/usr/bin/env python3
"""
开发配置生成器
负责生成项目的开发管理相关配置文件
"""

import os
import json
from pathlib import Path
import argparse


def generate_development_config(project_path=".", project_type="single_layer"):
    """生成开发配置文件"""
    
    # 创建配置目录
    config_dir = Path(project_path) / 'config'
    config_dir.mkdir(exist_ok=True)
    
    # 开发配置
    development_config = {
        "development_settings": {
            "code_review_enabled": True,
            "unit_test_required": True,
            "documentation_required": True,
            "ci_cd_enabled": True
        },
        "environments": [
            "development",
            "testing",
            "staging"
        ],
        "monitoring": {
            "commit_tracking": True,
            "build_status": True,
            "test_coverage": True
        }
    }
    
    # 写入配置文件
    config_file = config_dir / 'development_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(development_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 开发配置文件已生成: {config_file}")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成开发配置文件')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'], help='项目类型')
    
    args = parser.parse_args()
    
    success = generate_development_config(args.project_path, args.project_type)
    
    if success:
        print("[+] 开发配置生成完成")
    else:
        print("[X] 开发配置生成失败")
        exit(1)


if __name__ == "__main__":
    main() 