# 文档关联系统

## 概述

文档关联系统负责自动扫描、注册和管理项目文档，支持Git风格的文件过滤规则和组件级配置。

### 核心功能

- **文档扫描**: 自动发现项目中的文档文件
- **INDEX注册**: 将文档信息注册到组件INDEX文件
- **关联管理**: 建立和维护文档间的关联关系
- **配置化过滤**: 支持Git风格的文件包含/排除规则

### 设计原则

- **单一职责**: 专注于文档关联和注册功能
- **单一来源**: 配置统一管理，避免重复定义
- **双链引用**: 支持[[document_links_config]]等双链语法
- **关键信息**：只保留必要关键信息

## 配置管理

### 配置文件位置

| 配置类型 | 路径 | 状态 | 说明 |
|---------|------|------|------|
| **项目配置** | `config/document_links_config.json` | ✅ 推荐 | 项目级配置，优先级最高 |
| **默认配置** | 内置默认值 | ✅ 备用 | 配置文件缺失时使用 |

## 功能与脚本对应表

| 功能 | 脚本 | 主要参数 | 使用场景 | 效果 |
|------|------|----------|----------|------|
| **文档扫描注册（全部）** | `auto_link_documents.py` | `--register --all --mode update` | 日常维护 | 增量更新所有INDEX文件 |
| **文档扫描注册（初始化）** | `auto_link_documents.py` | `--register --all --mode init` | 项目初始化 | 重新生成所有INDEX文件 |
| **单组件更新** | `auto_link_documents.py` | `--register --component <组件> --mode update` | 特定组件维护 | 更新指定组件INDEX |
| **发现文档关联** | `auto_link_documents.py` | `--discover-associations` | 关联分析 | 生成关联建议报告 |
| **生成关联报告** | `auto_link_documents.py` | `--report` | 分析文档关系 | 生成详细关联分析 |
| **从Canvas同步关联** | `auto_link_documents.py` | `--sync-from-canvas` | Canvas→INDEX | 将Canvas连线同步为INDEX关联 |

### 功能详细说明

#### 文档扫描注册
- **目的**: 自动发现并注册项目文档到INDEX文件
- **触发**: 项目初始化或手动执行
- **效果**: 完全重新生成INDEX内容

#### 增量更新
- **目的**: 维护现有INDEX文件的文档信息
- **触发**: 文档变更后的日常维护
- **效果**:
  - ✅ 新增文档自动添加
  - ✅ 删除不存在的文档
  - ✅ 更新文档路径变更
  - ❌ 不处理手动关联关系

#### Canvas关联同步
- **目的**: 将Canvas中的连线关系同步到INDEX文件
- **触发**: Canvas文件编辑后的关联同步
- **效果**:
  - ✅ 提取Canvas中的edges（连线）
  - ✅ 转换为INDEX文件中的关联关系
  - ✅ 标记为"Canvas关联"来源
  - ✅ 按组件更新对应的INDEX文件

## 使用流程

### 新项目初始化

```bash
# 1. 初始化项目（自动生成配置）
python scripts/init_product_project.py --name my_project --structure single_layer --path ./my_project

# 2. 进入项目目录
cd my_project

# 3. 根据需要修改配置
# 编辑 config/document_links_config.json

# 4. 运行文档注册
python ../../scripts/links/auto_link_documents.py --register --all
```

### 现有项目添加配置

```bash
# 1. 在现有项目中生成配置
python scripts/config/generate_document_links_config.py --project-path . --project-type single_layer

# 2. 自定义配置
# 编辑 config/document_links_config.json

# 3. 验证配置
python scripts/links/auto_link_documents.py --register --component REQ
```

## 故障排除

| 问题 | 现象 | 解决方案 |
|------|------|----------|
| **配置文件不存在** | `⚠ 配置文件不存在，使用默认配置` | 运行配置生成脚本或手动创建 |
| **配置格式错误** | `⚠ 配置文件加载失败，使用默认配置` | 检查JSON格式，修复语法错误 |
| **组件扫描失败** | 扫描结果为空或错误 | 检查文件扩展名、排除模式、目录存在性 |

## 最佳实践

- **逐步配置**: 先使用默认配置，再根据需要调整
- **测试验证**: 修改配置后运行扫描验证结果
- **版本控制**: 将配置文件纳入版本控制
- **文档化**: 为特殊配置添加注释说明原因

## 相关文档

- [[document_links_config]] - 配置文件详细说明
- [[Canvas可视化模块]] - Canvas同步功能
- [[产品体系构建工具关联表]] - 工具关联关系
