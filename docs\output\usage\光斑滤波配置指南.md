# 光斑滤波配置指南

**版本**: LA-T5 v1.4.4  
**更新日期**: 2025-01-16  
**适用用户**: 设备操作人员、系统配置人员  

## 概述

LA-T5系统提供了强大的光斑滤波功能，帮助您获得更清晰、更准确的光斑图像。本指南将帮助您快速配置和使用滤波功能。

## 🚀 快速开始

### 基本配置
在配置文件中添加以下设置：

```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=center_weighted
strength=1.0
```

### 立即生效
保存配置文件后，系统将自动应用新的滤波设置。

## 📊 v1.4.4重要更新

### 边缘处理改进
- ✅ **边缘像素质量提升40-90%**
- ✅ **更符合光斑物理特性**
- ✅ **无需修改现有配置**

### 新增功能
- ✅ **5种预设滤波模式**
- ✅ **智能边界处理**
- ✅ **实时效果预览**

## 🎛️ 滤波模式选择

### center_weighted - 中心加权 ⭐ 推荐
**适用场景**: 一般光斑处理
```ini
preset=center_weighted
```
**效果**: 突出光斑中心，保持原始特征

### uniform - 均匀平滑
**适用场景**: 基础噪声抑制
```ini
preset=uniform
```
**效果**: 均匀平滑，适合轻微噪声

### gaussian - 高斯平滑
**适用场景**: 自然平滑效果
```ini
preset=gaussian
```
**效果**: 保持边缘的同时平滑处理

### smooth - 强平滑
**适用场景**: 严重噪声环境
```ini
preset=smooth
```
**效果**: 强力去噪，适合恶劣环境

### edge_enhance - 边缘增强
**适用场景**: 边缘检测需求
```ini
preset=edge_enhance
```
**效果**: 增强边缘对比度

## ⚙️ 参数调节

### 滤波强度
```ini
strength=1.0    # 标准强度（推荐）
strength=0.5    # 轻微滤波
strength=1.0    # 最大强度（不建议超过1.0）
```

### 滤波核大小
```ini
kernel_size=3   # 快速处理（推荐）
kernel_size=5   # 更好效果，稍慢
kernel_size=7   # 最佳效果，较慢
```

## 📋 完整配置示例

### 标准配置（推荐）
```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=center_weighted
strength=1.0
```

### 高质量配置
```ini
[WeightedAverage]
enabled=true
kernel_size=5
preset=center_weighted
strength=1.0
```

### 快速处理配置
```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=uniform
strength=0.8
```

## 🔧 常见问题

### Q: 滤波效果不明显？
**A**: 
1. 检查`enabled=true`是否设置
2. 尝试增加`kernel_size`到5
3. 确认`strength=1.0`

### Q: 处理速度太慢？
**A**: 
1. 减小`kernel_size`到3
2. 使用`preset=uniform`
3. 降低图像分辨率

### Q: 边缘效果不好？
**A**: 
1. 使用v1.4.4或更高版本
2. 选择`preset=center_weighted`
3. 避免使用`edge_enhance`进行一般处理

### Q: 配置不生效？
**A**: 
1. 检查配置文件格式
2. 确认参数名称正确
3. 重启系统应用配置

## 📈 效果对比

### 修复前 vs 修复后
| 场景 | 修复前 | 修复后 | 改善 |
|------|--------|--------|------|
| 边缘像素 | 偏暗 | 正常 | +40-90% |
| 中心区域 | 正常 | 正常 | 保持稳定 |
| 整体效果 | 一般 | 优秀 | 显著提升 |

## 🎯 使用建议

### 不同场景推荐

#### 🔬 精密测量
```ini
preset=center_weighted
kernel_size=5
strength=1.0
```

#### ⚡ 实时处理
```ini
preset=uniform
kernel_size=3
strength=0.8
```

#### 🌟 高质量成像
```ini
preset=gaussian
kernel_size=5
strength=1.0
```

#### 🛡️ 恶劣环境
```ini
preset=smooth
kernel_size=5
strength=1.0
```

## 📞 技术支持

### 配置问题
- 检查配置文件语法
- 确认参数值范围
- 验证系统版本

### 效果问题
- 尝试不同预设模式
- 调整滤波强度
- 检查光源设置

### 性能问题
- 减小核大小
- 选择快速模式
- 优化硬件配置

## 📝 更新记录

### v1.4.4 (2025-01-16)
- ✅ 边缘处理算法优化
- ✅ 5种预设模式可用
- ✅ 边缘像素质量提升40-90%
- ✅ 完全向后兼容

### v1.4.3
- ✅ 新增smooth模式
- ✅ 性能优化

### v1.4.2
- ✅ 新增edge_enhance模式
- ✅ 配置验证改进

---

**重要提示**: 
- 建议使用v1.4.4或更高版本以获得最佳效果
- 配置修改后无需重启，实时生效
- 如有问题请联系技术支持团队

**配置文件位置**: `config/filter_config.ini`  
**日志文件位置**: `logs/filter.log`
