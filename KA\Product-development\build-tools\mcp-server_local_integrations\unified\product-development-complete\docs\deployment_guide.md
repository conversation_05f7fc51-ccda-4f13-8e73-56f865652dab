# 产品开发完整功能集成MCP服务器 - 部署指南

## 概述

本指南详细说明如何部署和配置产品开发完整功能集成MCP服务器 v2.0。

## 系统要求

### 基础环境
- **操作系统**：Windows 10/11, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Python版本**：Python 3.8 或更高版本
- **内存**：至少 4GB RAM
- **存储空间**：至少 1GB 可用空间

### 软件依赖
- **Cursor IDE** 或 **VSCode** (支持MCP协议)
- **Git** (用于版本控制)
- **Python包管理器** (pip, conda等)

## 安装步骤

### 1. 环境准备

#### 检查Python版本
```bash
python --version
# 或
python3 --version
```

#### 创建虚拟环境（推荐）
```bash
# 使用venv
python -m venv mcp-env

# 激活虚拟环境
# Windows:
mcp-env\Scripts\activate
# Linux/macOS:
source mcp-env/bin/activate
```

### 2. 安装依赖

```bash
# 进入服务器目录
cd KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete

# 安装依赖
pip install -r requirements.txt
```

### 3. 验证安装

```bash
# 运行测试脚本
python test_server.py
```

## 配置部署

### 1. Cursor配置

在项目根目录创建或编辑 `.cursor/mcp.json`：

```json
{
  "mcpServers": {
    "product-development-complete": {
      "command": "python",
      "args": [
        "G:/10_note-files/Obsidian-Vault/KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete/server.py"
      ],
      "description": "产品开发完整功能集成MCP服务器 v2.0",
      "env": {
        "PYTHONPATH": "G:/10_note-files/Obsidian-Vault/KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete",
        "PRODUCT_DEV_ROOT": "G:/10_note-files/Obsidian-Vault/KA/Product-development",
        "DEBUG_MODE": "false"
      }
    }
  }
}
```

**重要提示**：
- 将路径替换为您的实际项目路径
- 使用绝对路径确保服务器正确启动
- 路径中不能包含空格

### 2. 环境变量配置

#### Windows
```cmd
set PYTHONPATH=G:\10_note-files\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
set PRODUCT_DEV_ROOT=G:\10_note-files\Obsidian-Vault\KA\Product-development
set DEBUG_MODE=false
```

#### Linux/macOS
```bash
export PYTHONPATH="/path/to/product-development-complete"
export PRODUCT_DEV_ROOT="/path/to/Product-development"
export DEBUG_MODE="false"
```

### 3. 权限设置

确保Python脚本有执行权限：

```bash
# Linux/macOS
chmod +x server.py

# Windows (通常不需要额外设置)
```

## 启动和验证

### 1. 启动服务器

重启Cursor IDE，MCP服务器会自动启动。

### 2. 验证连接

在Cursor中与AI助手对话：

```
用户：请测试MCP服务器连接状态
AI助手：MCP服务器连接正常，所有工具可用。
```

### 3. 功能测试

```
用户：请列出所有可用的MCP工具
AI助手：可用工具包括：
- 项目管理：init_project_tool, create_project_structure_tool...
- 配置管理：load_config_tool, save_config_tool...
- 需求管理：import_requirements_tool, analyze_requirements_tool...
...
```

## 故障排除

### 常见问题

#### 1. 服务器启动失败

**症状**：Cursor显示MCP服务器连接失败

**解决方案**：
1. 检查Python路径是否正确
2. 验证依赖是否完全安装
3. 查看Cursor的开发者工具中的错误日志
4. 确认路径中没有空格或特殊字符

#### 2. 工具调用失败

**症状**：AI助手无法调用MCP工具

**解决方案**：
1. 检查服务器是否正常启动
2. 验证工具函数的类型注解
3. 确认参数格式正确
4. 查看服务器日志

#### 3. 路径问题

**症状**：文件或目录找不到

**解决方案**：
1. 使用绝对路径
2. 检查文件权限
3. 确认文件确实存在
4. 验证环境变量设置

#### 4. 依赖包问题

**症状**：导入模块失败

**解决方案**：
```bash
# 重新安装依赖
pip install --upgrade -r requirements.txt

# 检查特定包
pip show mcp

# 安装缺失的包
pip install package_name
```

### 调试技巧

#### 1. 启用调试模式

在配置文件中设置：
```json
"env": {
  "DEBUG_MODE": "true"
}
```

#### 2. 查看日志

```bash
# 查看服务器日志
tail -f mcp_server.log

# Windows
type mcp_server.log
```

#### 3. 手动测试

```bash
# 直接运行服务器
python server.py

# 运行测试脚本
python test_server.py
```

## 性能优化

### 1. 内存优化

- 使用虚拟环境隔离依赖
- 定期清理临时文件
- 监控内存使用情况

### 2. 响应速度优化

- 启用异步处理
- 使用缓存机制
- 优化文件I/O操作

### 3. 并发处理

- 限制同时处理的请求数
- 使用连接池
- 实现请求队列

## 安全考虑

### 1. 文件权限

- 限制服务器访问的目录范围
- 设置适当的文件权限
- 避免在敏感目录运行

### 2. 网络安全

- 仅在本地运行MCP服务器
- 不要暴露到公网
- 使用防火墙保护

### 3. 代码安全

- 定期更新依赖包
- 扫描安全漏洞
- 验证输入参数

## 维护和更新

### 1. 定期维护

- 检查依赖包更新
- 清理日志文件
- 备份配置文件

### 2. 版本更新

```bash
# 备份当前配置
cp mcp.json mcp.json.backup

# 更新代码
git pull origin main

# 更新依赖
pip install --upgrade -r requirements.txt

# 测试新版本
python test_server.py
```

### 3. 监控

- 监控服务器状态
- 跟踪性能指标
- 记录错误日志

## 最佳实践

### 1. 配置管理

- 使用版本控制管理配置
- 分离环境特定配置
- 定期备份重要配置

### 2. 开发流程

- 在测试环境验证更改
- 使用渐进式部署
- 保持回滚能力

### 3. 文档维护

- 保持文档同步更新
- 记录配置变更
- 提供故障排除指南

## 支持和反馈

如果遇到问题或需要帮助：

1. 查看本部署指南
2. 检查故障排除部分
3. 查看项目文档
4. 提交问题报告

## 附录

### A. 环境变量列表

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| PYTHONPATH | Python模块搜索路径 | 无 |
| PRODUCT_DEV_ROOT | 产品开发框架根目录 | 无 |
| DEBUG_MODE | 调试模式开关 | false |
