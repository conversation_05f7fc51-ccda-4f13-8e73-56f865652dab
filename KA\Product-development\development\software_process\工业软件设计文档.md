# 工业软件设计文档

## 功能概述

工业软件是基于Qt/C++开发的测试执行核心应用，负责具体的硬件交互、测试逻辑实现和数据采集处理。软件通过标准化的HTTP接口与外部系统集成，支持远程调用和实时状态反馈。

## Qt/C++应用架构设计

### 整体软件架构

```
┌─────────────────────────────────────────────────┐
│                Qt主应用程序                      │
│  ┌─────────────────┐  ┌─────────────────────────┐│
│  │    用户界面层   │  │      网络接口层         ││
│  │  - QML界面      │  │  - QHttpServer         ││
│  │  - 测试状态显示 │  │  - QWebSocket          ││
│  │  - 配置管理     │  │  - SSL支持             ││
│  └─────────────────┘  └─────────────────────────┘│
│  ┌─────────────────────────────────────────────┐ │
│  │              业务逻辑层                      │ │
│  │  ┌─────────────┐  ┌─────────────────────────┐│ │
│  │  │ 测试控制器  │  │      测试引擎           ││ │
│  │  │ - 流程管理  │  │  - 测试计划解析         ││ │
│  │  │ - 状态跟踪  │  │  - 步骤执行器           ││ │
│  │  │ - 报告生成  │  │  - 结果验证器           ││ │
│  │  └─────────────┘  └─────────────────────────┘│ │
│  └─────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────┐ │
│  │              硬件抽象层                      │ │
│  │  ┌─────────────┐  ┌─────────────────────────┐│ │
│  │  │ 设备管理器  │  │      通信接口           ││ │
│  │  │ - 设备发现  │  │  - 串口通信             ││ │
│  │  │ - 连接管理  │  │  - 网络通信             ││ │
│  │  │ - 状态监控  │  │  - USB通信              ││ │
│  │  └─────────────┘  └─────────────────────────┘│ │
│  └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. 用户界面层 (UI Layer)

**设计原则**：
- 采用QML/Qt Quick构建现代化界面
- 支持响应式布局和主题切换
- 实时显示测试进度和结果
- 提供直观的配置管理界面

**主要组件**：
```cpp
class MainWindow : public QQuickView {
    Q_OBJECT
    
public:
    explicit MainWindow(QWindow *parent = nullptr);
    
public slots:
    void updateTestStatus(const TestStatus &status);
    void showTestReport(const TestReport &report);
    void displayError(const QString &message);
    
private:
    void setupQmlContext();
    void loadMainQml();
    
private:
    QQmlEngine *m_qmlEngine;
    TestController *m_testController;
};
```

#### 2. 网络接口层 (Network Interface Layer)

**设计目标**：
- 提供标准化的HTTP/WebSocket接口
- 支持SSL/TLS安全传输
- 实现RESTful API规范
- 支持实时数据推送

**核心实现**：
```cpp
class NetworkInterface : public QObject {
    Q_OBJECT
    
public:
    explicit NetworkInterface(QObject *parent = nullptr);
    void startServer(quint16 port);
    void stopServer();
    
private slots:
    void handleTestStartRequest(const QHttpServerRequest &request);
    void handleStatusRequest(const QString &taskId);
    void handleReportRequest(const QString &taskId);
    
signals:
    void testStartRequested(const TestConfig &config);
    void statusRequested(const QString &taskId);
    void reportRequested(const QString &taskId);
    
private:
    QHttpServer *m_httpServer;
    QWebSocketServer *m_wsServer;
    QSslServer *m_sslServer;
    
    void setupHttpRoutes();
    void setupWebSocketHandlers();
    void configureSsl();
};
```

#### 3. 业务逻辑层 (Business Logic Layer)

**测试控制器设计**：
```cpp
class TestController : public QObject {
    Q_OBJECT
    
public:
    explicit TestController(QObject *parent = nullptr);
    
    // 核心接口
    QString startTest(const TestConfig &config);
    TestStatus getTestStatus(const QString &taskId);
    TestReport getTestReport(const QString &taskId);
    void cancelTest(const QString &taskId);
    
signals:
    void testStarted(const QString &taskId);
    void testProgressUpdated(const QString &taskId, int progress);
    void testCompleted(const QString &taskId, const TestReport &report);
    void testFailed(const QString &taskId, const QString &error);
    
private slots:
    void onStepCompleted(const QString &stepId, const StepResult &result);
    void onDeviceError(const QString &deviceId, const QString &error);
    
private:
    TestEngine *m_testEngine;
    QMap<QString, TestSession*> m_activeSessions;
    
    QString generateTaskId();
    void updateSessionStatus(const QString &taskId, TestStatus::State state);
};
```

**测试引擎设计**：
```cpp
class TestEngine : public QObject {
    Q_OBJECT
    
public:
    explicit TestEngine(QObject *parent = nullptr);
    
    void loadTestPlan(const QString &planPath);
    void executeTest(const TestConfig &config);
    void pauseTest();
    void resumeTest();
    void stopTest();
    
signals:
    void stepStarted(const QString &stepId);
    void stepCompleted(const QString &stepId, const StepResult &result);
    void testProgress(int percentage);
    
private:
    TestPlan m_currentPlan;
    QList<TestStep*> m_testSteps;
    StepExecutor *m_executor;
    ResultValidator *m_validator;
    
    void executeNextStep();
    bool validateStepResult(const StepResult &result);
};
```

#### 4. 硬件抽象层 (Hardware Abstraction Layer)

**设计目标**：
- 提供统一的硬件访问接口
- 支持多种通信协议
- 实现设备热插拔检测
- 提供设备状态监控

**设备管理器设计**：
```cpp
class DeviceManager : public QObject {
    Q_OBJECT
    
public:
    explicit DeviceManager(QObject *parent = nullptr);
    
    void scanDevices();
    bool connectDevice(const QString &deviceId);
    void disconnectDevice(const QString &deviceId);
    QStringList getAvailableDevices() const;
    DeviceInfo getDeviceInfo(const QString &deviceId) const;
    
signals:
    void deviceConnected(const QString &deviceId);
    void deviceDisconnected(const QString &deviceId);
    void deviceError(const QString &deviceId, const QString &error);
    
private:
    QMap<QString, Device*> m_devices;
    QTimer *m_scanTimer;
    
    void detectSerialDevices();
    void detectNetworkDevices();
    void detectUsbDevices();
};
```

**通信接口设计**：
```cpp
class CommunicationInterface : public QObject {
    Q_OBJECT
    
public:
    explicit CommunicationInterface(QObject *parent = nullptr);
    
    virtual bool connect(const ConnectionConfig &config) = 0;
    virtual void disconnect() = 0;
    virtual bool sendCommand(const QByteArray &command) = 0;
    virtual QByteArray readResponse(int timeout = 5000) = 0;
    
signals:
    void dataReceived(const QByteArray &data);
    void connectionStateChanged(bool connected);
    void errorOccurred(const QString &error);
    
protected:
    bool m_connected;
    QString m_lastError;
};

// 串口通信实现
class SerialInterface : public CommunicationInterface {
    Q_OBJECT
    
public:
    bool connect(const ConnectionConfig &config) override;
    void disconnect() override;
    bool sendCommand(const QByteArray &command) override;
    QByteArray readResponse(int timeout = 5000) override;
    
private:
    QSerialPort *m_serialPort;
};
```

## 数据模型设计

### 核心数据结构

#### 测试配置 (TestConfig)
```cpp
struct TestConfig {
    QString projectName;
    QString planId;
    QString planPath;
    QVariantMap parameters;
    int timeout;
    int retries;
    TestPriority priority;
    
    bool isValid() const;
    QJsonObject toJson() const;
    static TestConfig fromJson(const QJsonObject &json);
};
```

#### 测试状态 (TestStatus)
```cpp
class TestStatus {
public:
    enum State {
        Pending,
        Running,
        Paused,
        Completed,
        Failed,
        Cancelled
    };
    
    TestStatus(const QString &taskId);
    
    QString taskId() const;
    State state() const;
    int progress() const;
    QString currentStep() const;
    QDateTime startTime() const;
    QDateTime endTime() const;
    QString errorMessage() const;
    
    void setState(State state);
    void setProgress(int progress);
    void setCurrentStep(const QString &step);
    void setErrorMessage(const QString &error);
    
private:
    QString m_taskId;
    State m_state;
    int m_progress;
    QString m_currentStep;
    QDateTime m_startTime;
    QDateTime m_endTime;
    QString m_errorMessage;
};
```

#### 测试报告 (TestReport)
```cpp
struct StepResult {
    QString stepId;
    QString description;
    bool passed;
    QVariant measuredValue;
    QVariant expectedValue;
    QString errorMessage;
    QDateTime timestamp;
    qint64 duration; // 毫秒
};

class TestReport {
public:
    TestReport(const QString &taskId);
    
    void addStepResult(const StepResult &result);
    QList<StepResult> getStepResults() const;
    QString generateSummary() const;
    bool exportToFile(const QString &filePath, const QString &format) const;
    
private:
    QString m_taskId;
    QString m_projectName;
    QString m_planId;
    QDateTime m_startTime;
    QDateTime m_endTime;
    QList<StepResult> m_stepResults;
    
    QString generateHtmlReport() const;
    QString generateJsonReport() const;
    QString generateXmlReport() const;
};
```

## HTTP接口设计

### RESTful API规范

#### 测试控制接口

**启动测试**
```
POST /api/test/start
Content-Type: application/json

{
    "project": "ProjectX",
    "planId": "Plan1",
    "parameters": {...},
    "timeout": 3600
}

Response:
{
    "taskId": "uuid-xxxx-xxxx",
    "status": "started",
    "estimatedDuration": 1800
}
```

**获取测试状态**
```
GET /api/test/status/{taskId}

Response:
{
    "taskId": "uuid-xxxx-xxxx",
    "state": "running",
    "progress": 45,
    "currentStep": "T2",
    "startTime": "2024-01-01T10:00:00Z"
}
```

**获取测试报告**
```
GET /api/test/report/{taskId}?format=json

Response:
{
    "taskId": "uuid-xxxx-xxxx",
    "summary": {
        "totalSteps": 10,
        "passed": 8,
        "failed": 2,
        "duration": 1650
    },
    "steps": [...]
}
```

### WebSocket实时通信

#### 状态推送设计
```cpp
class WebSocketHandler : public QObject {
    Q_OBJECT
    
public:
    explicit WebSocketHandler(QWebSocketServer *server, QObject *parent = nullptr);
    
    void broadcastTestStatus(const TestStatus &status);
    void broadcastStepResult(const QString &taskId, const StepResult &result);
    
private slots:
    void onNewConnection();
    void onTextMessageReceived(const QString &message);
    void onSocketDisconnected();
    
private:
    QWebSocketServer *m_server;
    QList<QWebSocket*> m_clients;
    
    void sendToClient(QWebSocket *client, const QJsonObject &message);
    void handleSubscription(QWebSocket *client, const QJsonObject &request);
};
```

## 配置管理

### 配置文件系统

#### 应用配置
```cpp
class ConfigManager : public QObject {
    Q_OBJECT
    
public:
    static ConfigManager* instance();
    
    // 配置加载和保存
    bool loadConfig(const QString &configPath);
    bool saveConfig(const QString &configPath = QString());
    
    // 网络配置
    QString serverHost() const;
    quint16 serverPort() const;
    bool sslEnabled() const;
    
    // 设备配置
    QStringList availableDevices() const;
    DeviceConfig getDeviceConfig(const QString &deviceId) const;
    void setDeviceConfig(const QString &deviceId, const DeviceConfig &config);
    
    // 测试配置
    QString defaultTestPlanPath() const;
    int defaultTimeout() const;
    int maxRetries() const;
    
signals:
    void configChanged();
    
private:
    explicit ConfigManager(QObject *parent = nullptr);
    static ConfigManager* s_instance;
    
    QSettings *m_settings;
    QJsonObject m_config;
    
    void setupDefaultConfig();
};
```

#### 测试计划配置
```json
{
    "project": {
        "name": "ProjectX",
        "version": "1.0.0",
        "description": "硬件性能测试项目"
    },
    "plans": [
        {
            "id": "Plan1",
            "name": "基本功能测试",
            "description": "验证基本硬件功能",
            "timeout": 3600,
            "retries": 3,
            "steps": [
                {
                    "id": "T1",
                    "name": "电压检查",
                    "description": "检查输出电压范围",
                    "device": "multimeter",
                    "command": "measure_voltage",
                    "expected": {"min": 4.5, "max": 5.5},
                    "unit": "V"
                },
                {
                    "id": "T2",
                    "name": "温度测试",
                    "description": "测量工作温度",
                    "device": "thermometer",
                    "command": "read_temperature",
                    "expected": {"target": 30, "tolerance": 2},
                    "unit": "°C"
                }
            ]
        }
    ]
}
```

## 日志和错误处理

### 日志系统设计

#### 结构化日志
```cpp
class Logger : public QObject {
    Q_OBJECT
    
public:
    enum LogLevel {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    };
    
    static Logger* instance();
    
    void log(LogLevel level, const QString &category, const QString &message, const QVariantMap &context = QVariantMap());
    void setLogLevel(LogLevel level);
    void setLogOutput(const QString &filePath);
    
signals:
    void logMessage(LogLevel level, const QString &message);
    
private:
    explicit Logger(QObject *parent = nullptr);
    static Logger* s_instance;
    
    LogLevel m_logLevel;
    QFile *m_logFile;
    QTextStream *m_logStream;
    
    QString formatLogMessage(LogLevel level, const QString &category, const QString &message, const QVariantMap &context);
};

// 使用示例
#define LOG_DEBUG(category, message, ...) Logger::instance()->log(Logger::Debug, category, message, ##__VA_ARGS__)
#define LOG_INFO(category, message, ...) Logger::instance()->log(Logger::Info, category, message, ##__VA_ARGS__)
#define LOG_ERROR(category, message, ...) Logger::instance()->log(Logger::Error, category, message, ##__VA_ARGS__)
```

### 错误处理机制

#### 异常处理策略
```cpp
class TestException : public std::exception {
public:
    TestException(const QString &message, const QString &code = QString())
        : m_message(message), m_errorCode(code) {}
    
    const char* what() const noexcept override {
        return m_message.toLocal8Bit().constData();
    }
    
    QString message() const { return m_message; }
    QString errorCode() const { return m_errorCode; }
    
private:
    QString m_message;
    QString m_errorCode;
};

class DeviceException : public TestException {
public:
    DeviceException(const QString &deviceId, const QString &message)
        : TestException(QString("Device %1: %2").arg(deviceId, message), "DEVICE_ERROR") {}
};
```

## 性能优化

### 多线程设计

#### 测试执行线程
```cpp
class TestWorker : public QObject {
    Q_OBJECT
    
public:
    explicit TestWorker(QObject *parent = nullptr);
    
public slots:
    void executeTest(const TestConfig &config);
    void pauseTest();
    void resumeTest();
    void cancelTest();
    
signals:
    void testProgress(int percentage);
    void stepCompleted(const StepResult &result);
    void testFinished(const TestReport &report);
    void errorOccurred(const QString &error);
    
private:
    QThread *m_workerThread;
    QMutex m_stateMutex;
    QWaitCondition m_pauseCondition;
    bool m_paused;
    bool m_cancelled;
    
    void runTestSteps(const QList<TestStep> &steps);
};
```

### 内存管理

#### 资源池管理
```cpp
class ResourcePool : public QObject {
    Q_OBJECT
    
public:
    explicit ResourcePool(QObject *parent = nullptr);
    
    template<typename T>
    T* acquire();
    
    template<typename T>
    void release(T* resource);
    
    void clear();
    
private:
    QMap<QString, QQueue<QObject*>> m_pools;
    QMutex m_poolMutex;
    
    QString getTypeName(QObject* obj);
};
```

## 部署和维护

### 编译配置

#### CMake配置示例
```cmake
cmake_minimum_required(VERSION 3.16)
project(IndustrialTestSoftware)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Qt组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network WebSockets SerialPort)

# 源文件
set(SOURCES
    src/main.cpp
    src/mainwindow.cpp
    src/testcontroller.cpp
    src/testengine.cpp
    src/devicemanager.cpp
    src/networkinterface.cpp
    src/configmanager.cpp
    src/logger.cpp
)

# 可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# 链接Qt库
target_link_libraries(${PROJECT_NAME} 
    Qt6::Core 
    Qt6::Widgets 
    Qt6::Network 
    Qt6::WebSockets 
    Qt6::SerialPort
)

# 安装配置
install(TARGETS ${PROJECT_NAME} DESTINATION bin)
install(DIRECTORY configs/ DESTINATION share/configs)
```

### 系统集成

#### 与外部系统的集成接口
- **MCP服务器集成**：[[MCP服务器集成与AI调用技术方案]] - VSCode/Cursor AI调用的详细技术路径
- **测试框架集成**：[[功能测试一体化框架]] - 完整测试体系的框架设计
- **产品体系集成**：[[产品体系构建框架]] - 与整个产品开发流程的集成

## 总结

本工业软件采用Qt/C++技术栈，提供了完整的测试执行、硬件交互和数据管理功能。软件架构遵循分层设计原则，具备良好的可扩展性和维护性。通过标准化的HTTP接口，可以与各种外部系统进行集成，支持远程调用和实时状态监控。

**核心特性**：
1. **模块化架构**：清晰的分层设计，便于功能扩展
2. **标准化接口**：RESTful API和WebSocket支持
3. **硬件抽象**：统一的设备访问接口
4. **配置管理**：灵活的配置文件系统
5. **性能优化**：多线程和资源池管理
6. **错误处理**：完善的异常处理机制

