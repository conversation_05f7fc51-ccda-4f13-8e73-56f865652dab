#!/usr/bin/env pwsh
# 文件：test_windows_powershell.ps1
# 功能：测试Windows系统下PowerShell Core的Canvas Auto Sync命令

Write-Host "=== Windows PowerShell Core Canvas测试 ===" -ForegroundColor Green

# 检查PowerShell版本
Write-Host "`n📋 环境检查:" -ForegroundColor Yellow
Write-Host "PowerShell版本: $($PSVersionTable.PSVersion)" -ForegroundColor Cyan
Write-Host "平台: $($PSVersionTable.Platform)" -ForegroundColor Cyan
Write-Host "操作系统: $($PSVersionTable.OS)" -ForegroundColor Cyan

# 设置环境变量（临时）
$env:PRODUCT_DEVELOP_DIR = $PWD
Write-Host "`n🔧 环境变量设置:" -ForegroundColor Yellow
Write-Host "PRODUCT_DEVELOP_DIR = $env:PRODUCT_DEVELOP_DIR" -ForegroundColor Cyan

# 检查必要文件
Write-Host "`n📂 文件检查:" -ForegroundColor Yellow
$ScriptPath = "$env:PRODUCT_DEVELOP_DIR/scripts/canvas/on_canvas_save.ps1"
$TestCanvas = "$env:PRODUCT_DEVELOP_DIR/example/test_single_layer/product.canvas"

if (Test-Path $ScriptPath) {
    Write-Host "✓ 脚本文件存在: $ScriptPath" -ForegroundColor Green
} else {
    Write-Host "✗ 脚本文件不存在: $ScriptPath" -ForegroundColor Red
}

if (Test-Path $TestCanvas) {
    Write-Host "✓ 测试Canvas文件存在: $TestCanvas" -ForegroundColor Green
} else {
    Write-Host "✗ 测试Canvas文件不存在: $TestCanvas" -ForegroundColor Red
    Write-Host "创建示例Canvas文件..." -ForegroundColor Yellow
    
    # 创建目录
    $TestDir = Split-Path $TestCanvas -Parent
    if (-not (Test-Path $TestDir)) {
        New-Item -ItemType Directory -Path $TestDir -Force | Out-Null
    }
    
    # 创建简单的Canvas文件
    $SampleCanvas = @{
        nodes = @()
        edges = @()
    }
    $SampleCanvas | ConvertTo-Json -Depth 10 | Set-Content -Path $TestCanvas -Encoding UTF8
    Write-Host "✓ 已创建示例Canvas文件" -ForegroundColor Green
}

# 测试命令构建
Write-Host "`n🧪 命令测试:" -ForegroundColor Yellow

# 模拟Obsidian Shell Commands插件中的完整命令
$ObsidianCommand = "pwsh -NoProfile -ExecutionPolicy Bypass -Command `"& { & \`"$env:PRODUCT_DEVELOP_DIR/scripts/canvas/on_canvas_save.ps1\`" \`"$TestCanvas\`" }`""

Write-Host "构建的命令:" -ForegroundColor Cyan
Write-Host $ObsidianCommand -ForegroundColor White

# 测试命令执行
Write-Host "`n🚀 执行测试:" -ForegroundColor Yellow

try {
    Write-Host "开始执行Canvas同步命令..." -ForegroundColor Cyan
    
    # 直接调用脚本（简化版本）
    & $ScriptPath $TestCanvas
    
    Write-Host "`n✓ 命令执行完成" -ForegroundColor Green
    
} catch {
    Write-Host "`n✗ 命令执行失败" -ForegroundColor Red
    Write-Host "错误详情: $($_.Exception.Message)" -ForegroundColor Red
}

# 在Windows终端中的测试说明
Write-Host "`n📖 在Windows终端中的测试方法:" -ForegroundColor Yellow
Write-Host "1. 打开PowerShell Core (pwsh)" -ForegroundColor White
Write-Host "2. 设置环境变量:" -ForegroundColor White
Write-Host "   `$env:PRODUCT_DEVELOP_DIR = `"项目路径`"" -ForegroundColor Gray
Write-Host "3. 执行完整命令:" -ForegroundColor White
Write-Host "   $ObsidianCommand" -ForegroundColor Gray

Write-Host "`n🔧 故障排除提示:" -ForegroundColor Yellow
Write-Host "• 如果遇到执行策略错误，运行:" -ForegroundColor White
Write-Host "  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Gray
Write-Host "• 如果路径包含空格，确保使用双引号" -ForegroundColor White
Write-Host "• 确保Python已正确安装并在PATH中" -ForegroundColor White

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green 