#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
信息追溯可视化模块

单一职责：专门处理信息追溯相关的数据提取和可视化逻辑
"""

import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import DataAdapter, VisualizationData, VisualizationMode, Node, Edge
from core.component_utils import component_manager

class TraceabilityDataExtractor(DataAdapter):
    """信息追溯数据提取器 - 单一职责：信息追溯可视化数据提取"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
    
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """提取信息追溯可视化数据"""
        self.project_path = Path(project_path)
        return self._extract_traceability_data()
    
    def _extract_traceability_data(self) -> VisualizationData:
        """提取追溯链数据"""
        nodes = []
        edges = []
        
        # 扫描所有文档中的REF语法
        md_files = list(self.project_path.glob("**/*.md"))
        ref_pattern = re.compile(r'\[\[REF:([A-Z]+\d+):([^]]+)\]\]')
        
        doc_nodes = {}
        
        for md_file in md_files:
            try:
                content = self._read_file_with_fallback(md_file)
                if content is None:
                    continue
                
                # 提取文档ID
                doc_id = self._extract_doc_id(md_file.name, content)
                if doc_id and doc_id not in doc_nodes:
                    doc_nodes[doc_id] = {
                        "id": doc_id,
                        "name": md_file.stem,
                        "type": "document",
                        "component": component_manager.extract_component_from_id(doc_id),
                        "properties": {
                            "file_path": str(md_file),
                            "file_size": md_file.stat().st_size,
                            "last_modified": datetime.fromtimestamp(md_file.stat().st_mtime).isoformat()
                        }
                    }
                
                # 扫描REF引用
                ref_matches = ref_pattern.findall(content)
                for ref_id, ref_desc in ref_matches:
                    # 添加引用的目标文档
                    if ref_id not in doc_nodes:
                        doc_nodes[ref_id] = {
                            "id": ref_id,
                            "name": ref_desc,
                            "type": "document",
                            "component": component_manager.extract_component_from_id(ref_id),
                            "properties": {
                                "referenced_only": True,
                                "description": ref_desc
                            }
                        }
                    
                    # 添加追溯边
                    if doc_id:
                        edge_data = {
                            "source": doc_id,
                            "target": ref_id,
                            "type": "追溯",
                            "properties": {
                                "context": ref_desc,
                                "source_file": str(md_file)
                            }
                        }
                        edges.append(edge_data)
                        
            except Exception as e:
                print(f"处理追溯文件失败 {md_file}: {e}")
        
        # 转换为Node和Edge对象
        nodes = [Node(**node_data) for node_data in doc_nodes.values()]
        edges = [Edge(**edge_data) for edge_data in edges]
        
        return VisualizationData(
            title="信息追溯链",
            mode=VisualizationMode.TRACEABILITY,
            nodes=nodes,
            edges=edges,
            metadata={
                "trace_nodes": len(nodes),
                "trace_links": len(edges),
                "ref_patterns_found": sum(len(ref_pattern.findall(self._read_file_with_fallback(f) or "")) for f in md_files),
                "coverage_analysis": self._analyze_traceability_coverage(nodes),
                "scan_time": datetime.now().isoformat()
            }
        )
    
    def _read_file_with_fallback(self, file_path: Path) -> str:
        """读取文件内容，支持编码回退"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文件失败 {file_path}: {e}")
                break
        
        return None
    
    def _extract_doc_id(self, filename: str, content: str) -> str:
        """从文件名或内容中提取文档ID"""
        # 首先尝试从文件名提取
        id_match = re.match(r'([A-Z]+\d+)', filename)
        if id_match:
            return id_match.group(1)
        
        # 然后尝试从内容中提取
        id_patterns = [
            r'(?:文档ID|ID)[：:\s]*([A-Z]+\d+)',
            r'(?:编号|Number)[：:\s]*([A-Z]+\d+)',
            r'^([A-Z]+\d+)',  # 开头的ID
        ]
        
        for pattern in id_patterns:
            match = re.search(pattern, content, re.MULTILINE | re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    def _analyze_traceability_coverage(self, nodes: List[Node]) -> Dict[str, Any]:
        """分析追溯覆盖度"""
        component_coverage = {}
        total_refs = 0
        
        for node in nodes:
            component = node.component or "未知"
            if component not in component_coverage:
                component_coverage[component] = {
                    "documents": 0,
                    "references": 0,
                    "referenced_only": 0
                }
            
            component_coverage[component]["documents"] += 1
            
            if node.properties.get("referenced_only", False):
                component_coverage[component]["referenced_only"] += 1
            
            total_refs += len([prop for prop in node.properties.values() if "REF:" in str(prop)])
        
        return {
            "component_coverage": component_coverage,
            "total_references": total_refs,
            "coverage_percentage": len([c for c in component_coverage.values() if c["documents"] > 0]) / len(component_manager.components) * 100
        }
    
    def build_traceability_matrix(self) -> Dict[str, Dict[str, List[str]]]:
        """构建追溯矩阵"""
        data = self._extract_traceability_data()
        matrix = {}
        
        # 按组件分组
        for node in data.nodes:
            component = node.component or "未知"
            if component not in matrix:
                matrix[component] = {"documents": [], "traces_to": [], "traced_from": []}
            matrix[component]["documents"].append(node.id)
        
        # 分析追溯关系
        for edge in data.edges:
            source_comp = None
            target_comp = None
            
            # 找到源和目标的组件
            for node in data.nodes:
                if node.id == edge.source:
                    source_comp = node.component
                if node.id == edge.target:
                    target_comp = node.component
            
            if source_comp and target_comp and source_comp != target_comp:
                if target_comp not in matrix[source_comp]["traces_to"]:
                    matrix[source_comp]["traces_to"].append(target_comp)
                if source_comp not in matrix[target_comp]["traced_from"]:
                    matrix[target_comp]["traced_from"].append(source_comp)
        
        return matrix
    
    def find_missing_traces(self) -> Dict[str, List[str]]:
        """查找缺失的追溯链接"""
        matrix = self.build_traceability_matrix()
        missing_traces = {}
        
        # 标准的追溯关系（基于开发流程）
        expected_traces = {
            "REQ": ["DES", "QA"],  # 需求应该被设计和测试追溯
            "DES": ["DEV", "QA"],  # 设计应该被开发和测试追溯
            "DEV": ["QA", "PROD"], # 开发应该被测试和生产追溯
            "QA": ["PROD"],        # 测试应该被生产追溯
        }
        
        for source_comp, expected_targets in expected_traces.items():
            if source_comp in matrix:
                actual_targets = matrix[source_comp]["traces_to"]
                missing = [target for target in expected_targets if target not in actual_targets]
                if missing:
                    missing_traces[source_comp] = missing
        
        return missing_traces
    
    def generate_trace_report(self) -> str:
        """生成追溯报告"""
        data = self._extract_traceability_data()
        matrix = self.build_traceability_matrix()
        missing = self.find_missing_traces()
        
        report = f"""# 信息追溯分析报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 追溯概览

- **文档总数**: {len(data.nodes)}
- **追溯链接**: {len(data.edges)}
- **覆盖组件**: {len(matrix)}

## 组件追溯矩阵

"""
        
        for component, info in matrix.items():
            report += f"### {component}\n"
            report += f"- 文档数量: {len(info['documents'])}\n"
            report += f"- 追溯到: {', '.join(info['traces_to']) if info['traces_to'] else '无'}\n"
            report += f"- 被追溯: {', '.join(info['traced_from']) if info['traced_from'] else '无'}\n\n"
        
        if missing:
            report += "## 缺失的追溯关系\n\n"
            for source, targets in missing.items():
                report += f"- **{source}** → {', '.join(targets)}\n"
        
        return report 