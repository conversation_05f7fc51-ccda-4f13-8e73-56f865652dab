#ifndef IMAGEPROCESSING_IIMAGEFILTER_H
#define IMAGEPROCESSING_IIMAGEFILTER_H

#include "../common/ImageData.h"
#include "../common/ProcessingException.h"
#include <QString>
#include <QVector>
#include <memory>

namespace ImageProcessing {

/**
 * @brief 滤波参数基类
 */
struct FilterParams {
    float strength = 1.0f;  ///< 滤波强度 (0.0 - 2.0)
    bool  enabled  = true;  ///< 是否启用滤波

    virtual ~FilterParams() = default;

    /**
     * @brief 验证参数有效性
     * @throws InvalidParameterException 如果参数无效
     */
    virtual void validate() const;

    /**
     * @brief 重置为默认值
     */
    virtual void reset();

    /**
     * @brief 克隆参数对象
     */
    virtual std::unique_ptr<FilterParams> clone() const;

    /**
     * @brief 调试输出
     */
    virtual QString toString() const;
};

/**
 * @brief 卷积滤波参数
 */
struct ConvolutionParams : public FilterParams {
    QVector<QVector<float>> kernel;             ///< 卷积核
    int                     kernelSize = 3;     ///< 核大小
    bool                    normalize  = true;  ///< 是否归一化

    void                          validate() const override;
    void                          reset() override;
    std::unique_ptr<FilterParams> clone() const override;
    QString                       toString() const override;
};

/**
 * @brief 高斯滤波参数
 */
struct GaussianParams : public FilterParams {
    float sigma      = 1.0f;  ///< 标准差
    int   kernelSize = 5;     ///< 核大小

    void                          validate() const override;
    void                          reset() override;
    std::unique_ptr<FilterParams> clone() const override;
    QString                       toString() const override;
};

/**
 * @brief 中值滤波参数
 */
struct MedianParams : public FilterParams {
    int kernelSize = 3;  ///< 核大小

    void                          validate() const override;
    void                          reset() override;
    std::unique_ptr<FilterParams> clone() const override;
    QString                       toString() const override;
};

/**
 * @brief 卡尔曼滤波参数
 */
struct KalmanParams : public FilterParams {
    float processNoise     = 0.1f;  ///< 过程噪声
    float measurementNoise = 0.1f;  ///< 测量噪声
    float initialEstimate  = 0.0f;  ///< 初始估计值

    void                          validate() const override;
    void                          reset() override;
    std::unique_ptr<FilterParams> clone() const override;
    QString                       toString() const override;
};

/**
 * @brief 双边滤波参数
 */
struct BilateralParams : public FilterParams {
    float sigmaColor = 75.0f;  ///< 颜色空间标准差
    float sigmaSpace = 75.0f;  ///< 坐标空间标准差
    int   kernelSize = 5;      ///< 核大小

    void                          validate() const override;
    void                          reset() override;
    std::unique_ptr<FilterParams> clone() const override;
    QString                       toString() const override;
};

/**
 * @brief 加权均值滤波参数
 */
struct WeightedAverageParams : public FilterParams {
    QVector<QVector<float>> weights;            ///< 权重矩阵
    int                     kernelSize = 3;     ///< 核大小
    bool                    normalize  = true;  ///< 是否归一化权重

    void                          validate() const override;
    void                          reset() override;
    std::unique_ptr<FilterParams> clone() const override;
    QString                       toString() const override;
};

/**
 * @brief 图像滤波器抽象接口
 *
 * 定义了所有滤波算法必须实现的接口，遵循接口隔离原则
 */
class IImageFilter {
  public:
    virtual ~IImageFilter() = default;

    /**
     * @brief 应用滤波器到图像数据
     * @param data 图像数据（会被就地修改）
     * @return true if successful, false otherwise
     * @throws ProcessingException 如果滤波失败
     */
    virtual bool apply(ImageDataU32 &data) = 0;

    /**
     * @brief 应用滤波器到图像数据（不修改原数据）
     * @param src 源图像数据
     * @param dst 目标图像数据
     * @return true if successful, false otherwise
     * @throws ProcessingException 如果滤波失败
     */
    virtual bool apply(const ImageDataU32 &src, ImageDataU32 &dst) = 0;

    /**
     * @brief 设置滤波参数
     * @param params 滤波参数
     * @throws InvalidParameterException 如果参数无效
     */
    virtual void setParameters(const FilterParams &params) = 0;

    /**
     * @brief 获取当前滤波参数
     * @return 当前参数的智能指针
     */
    virtual std::unique_ptr<FilterParams> getParameters() const = 0;

    /**
     * @brief 获取算法名称
     * @return 算法名称
     */
    virtual QString getAlgorithmName() const = 0;

    /**
     * @brief 获取算法描述
     * @return 算法描述
     */
    virtual QString getDescription() const = 0;

    /**
     * @brief 检查是否支持指定尺寸的图像
     * @param width 图像宽度
     * @param height 图像高度
     * @return true if supported, false otherwise
     */
    virtual bool isSupported(uint32_t width, uint32_t height) const = 0;

    /**
     * @brief 预估处理时间（毫秒）
     * @param width 图像宽度
     * @param height 图像高度
     * @return 预估处理时间（毫秒）
     */
    virtual uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const = 0;

    /**
     * @brief 重置滤波器状态
     */
    virtual void reset() = 0;

    /**
     * @brief 获取算法版本
     * @return 版本字符串
     */
    virtual QString getVersion() const {
        return "1.0.0";
    }

    /**
     * @brief 是否为线程安全的实现
     * @return true if thread-safe, false otherwise
     */
    virtual bool isThreadSafe() const {
        return false;
    }

    /**
     * @brief 是否支持就地处理
     * @return true if supports in-place processing, false otherwise
     */
    virtual bool supportsInPlace() const {
        return true;
    }

  protected:
    /**
     * @brief 验证输入数据的通用方法
     * @param data 图像数据
     * @throws InvalidImageDataException 如果数据无效
     */
    void validateInput(const ImageDataU32 &data) const;

    /**
     * @brief 限制数值范围
     * @param value 输入值
     * @param minVal 最小值
     * @param maxVal 最大值
     * @return 限制后的值
     */
    uint32_t clampValue(uint32_t value, uint32_t minVal, uint32_t maxVal) const;

    /**
     * @brief 安全的浮点数到整数转换
     * @param value 浮点数值
     * @return 转换后的整数值
     */
    uint32_t safeFloatToUint32(float value) const;
};

/**
 * @brief 滤波器类型枚举
 */
enum class FilterType {
    None,             ///< 无滤波
    Kalman,           ///< 卡尔曼滤波
    Convolution,      ///< 卷积滤波
    Median,           ///< 中值滤波
    Gaussian,         ///< 高斯滤波
    Bilateral,        ///< 双边滤波
    WeightedAverage,  ///< 加权均值滤波
    FilterTypeLast,
};

/**
 * @brief 滤波器工厂接口
 */
class IFilterFactory {
  public:
    virtual ~IFilterFactory() = default;

    /**
     * @brief 创建滤波器实例
     * @param type 滤波器类型
     * @return 滤波器实例的智能指针
     * @throws UnsupportedOperationException 如果不支持指定类型
     */
    virtual std::unique_ptr<IImageFilter> createFilter(FilterType type) = 0;

    /**
     * @brief 获取支持的滤波器类型列表
     * @return 支持的类型列表
     */
    virtual QVector<FilterType> getSupportedTypes() const = 0;

    /**
     * @brief 获取指定类型的描述
     * @param type 滤波器类型
     * @return 类型描述
     */
    virtual QString getTypeDescription(FilterType type) const = 0;
};

}  // namespace ImageProcessing

#endif  // IMAGEPROCESSING_IIMAGEFILTER_H
