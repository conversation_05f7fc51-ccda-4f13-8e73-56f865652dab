#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置文件保存器

专门负责保存各种格式的配置文件（JSON、YAML）。
遵循单一职责原则，只负责配置文件的保存功能。
"""

import json
import yaml
import argparse
import sys
from pathlib import Path
from typing import Dict, Any, Optional


def save_config(
    config_path: str, 
    config_data: Dict[str, Any], 
    config_type: Optional[str] = None,
    create_dirs: bool = True
) -> Dict[str, Any]:
    """
    保存配置文件
    
    Args:
        config_path: 配置文件路径
        config_data: 配置数据
        config_type: 配置文件类型 (json, yaml, yml)
        create_dirs: 是否自动创建目录
    
    Returns:
        Dict[str, Any]: 保存结果
    """
    try:
        file_path = Path(config_path)
        
        if create_dirs:
            file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 自动判断文件类型
        if not config_type:
            suffix = file_path.suffix.lower()
            if suffix in ['.yaml', '.yml']:
                config_type = 'yaml'
            else:
                config_type = 'json'
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if config_type.lower() in ['yaml', 'yml']:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)
            else:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        return {
            "success": True,
            "message": f"✅ 配置文件保存成功: {config_path}",
            "path": str(file_path),
            "type": config_type,
            "size": file_path.stat().st_size
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"❌ 保存配置文件失败: {str(e)}"
        }


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='配置文件保存器')
    parser.add_argument('config_path', help='配置文件路径')
    parser.add_argument('--data', help='配置数据（JSON字符串）')
    parser.add_argument('--data-file', help='配置数据文件路径')
    parser.add_argument('--type', choices=['json', 'yaml', 'yml'], 
                       help='配置文件类型（可选，自动判断）')
    parser.add_argument('--create-dirs', action='store_true', default=True,
                       help='自动创建目录（默认：是）')
    
    args = parser.parse_args()
    
    # 获取配置数据
    config_data = None
    
    if args.data:
        try:
            config_data = json.loads(args.data)
        except json.JSONDecodeError as e:
            print(f"❌ 配置数据JSON格式错误: {e}")
            return 1
    elif args.data_file:
        try:
            with open(args.data_file, 'r', encoding='utf-8') as f:
                if args.data_file.endswith(('.yaml', '.yml')):
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)
        except Exception as e:
            print(f"❌ 读取配置数据文件失败: {e}")
            return 1
    else:
        # 从标准输入读取
        try:
            import sys
            data_str = sys.stdin.read()
            config_data = json.loads(data_str)
        except json.JSONDecodeError as e:
            print(f"❌ 标准输入JSON格式错误: {e}")
            return 1
        except Exception as e:
            print(f"❌ 读取标准输入失败: {e}")
            return 1
    
    if config_data is None:
        print("❌ 未提供配置数据")
        return 1
    
    # 保存配置
    result = save_config(args.config_path, config_data, args.type, args.create_dirs)
    
    if result["success"]:
        print(result["message"])
        print(f"   文件路径: {result['path']}")
        print(f"   文件类型: {result['type']}")
        print(f"   文件大小: {result['size']} bytes")
        return 0
    else:
        print(result["error"])
        return 1


if __name__ == "__main__":
    sys.exit(main())
