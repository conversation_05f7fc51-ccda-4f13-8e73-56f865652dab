#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastMCP 智能参数处理工具
专门为FastMCP优化的参数处理系统

核心功能：
- 参数缓存管理
- 智能默认值推断
- 基于ctx.elicit的用户交互
- 项目上下文检测
"""

import json
import logging
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class ParameterCache:
    """参数缓存管理器"""
    
    def __init__(self):
        self.cache_file = Path.home() / ".mcp_fastmcp_params_cache.json"
        self.cache_data = {}
        self.load_cache()
    
    def load_cache(self):
        """加载参数缓存"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.cache_data = json.load(f)
                logger.info(f"✅ 加载参数缓存: {len(self.cache_data)} 项")
        except Exception as e:
            logger.warning(f"⚠️ 加载缓存失败: {e}")
            self.cache_data = {}
    
    def save_cache(self):
        """保存参数缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 保存参数缓存: {len(self.cache_data)} 项")
        except Exception as e:
            logger.error(f"❌ 保存缓存失败: {e}")
    
    def get_cached_value(self, tool_name: str, param_name: str) -> Optional[str]:
        """获取缓存的参数值"""
        cache_key = f"{tool_name}.{param_name}"
        cached_item = self.cache_data.get(cache_key)
        
        if cached_item:
            # 检查缓存是否过期（7天）
            cache_time = datetime.fromisoformat(cached_item.get('timestamp', ''))
            if datetime.now() - cache_time < timedelta(days=7):
                logger.info(f"🔄 找到缓存参数: {cache_key} = {cached_item['value']}")
                return cached_item['value']
            else:
                # 清理过期缓存
                del self.cache_data[cache_key]
                self.save_cache()
        
        return None
    
    def cache_value(self, tool_name: str, param_name: str, value: str):
        """缓存参数值"""
        cache_key = f"{tool_name}.{param_name}"
        self.cache_data[cache_key] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
        self.save_cache()
        logger.info(f"💾 缓存参数: {cache_key} = {value}")


class IntelligentDefaults:
    """智能默认值系统"""
    
    def __init__(self):
        self.project_context = self._detect_project_context()
    
    def _detect_project_context(self) -> Dict[str, Any]:
        """检测项目上下文"""
        context = {
            "current_dir": Path.cwd(),
            "project_root": None,
            "project_name": Path.cwd().name
        }
        
        # 检测项目根目录
        current = Path.cwd()
        while current.parent != current:
            if any((current / marker).exists() for marker in [
                "build-tools", ".git", "package.json", "requirements.txt", 
                "Cargo.toml", "go.mod", "pom.xml"
            ]):
                context["project_root"] = current
                break
            current = current.parent
        
        # 检测是否在Product-development环境中
        if "Product-development" in str(Path.cwd()):
            context["in_product_dev"] = True
            context["example_dir"] = context.get("project_root", Path.cwd()) / "example"
        else:
            context["in_product_dev"] = False
        
        return context
    
    def get_default(self, param_name: str, current_params: Dict[str, Any] = None) -> Optional[str]:
        """获取智能默认值"""
        current_params = current_params or {}
        
        # 基础默认值映射
        base_defaults = {
            "structure_type": "single_layer",
            "output_format": "json",
            "analysis_type": "full",
            "matrix_type": "traceability",
            "task_format": "json",
            "dashboard_type": "full",
            "language": "auto",
            "report_type": "quality",
            "link_type": "auto",
            "sync_direction": "bidirectional",
            "trace_action": "update",
            "chart_type": "workflow",
            "config_type": "auto",
            "config_format": "json",
            "merge_strategy": "deep",
            "template_type": "workflow",
            "version": "1.0.0",
            "description": "AI 辅助创建的项目"
        }
        
        # 上下文相关的默认值
        if param_name == "project_path":
            if "project_name" in current_params:
                project_name = current_params["project_name"]
                if self.project_context["in_product_dev"]:
                    return str(self.project_context["example_dir"] / project_name)
                else:
                    return f"./{project_name}"
            else:
                return "."
        
        elif param_name == "project_name":
            return self.project_context["project_name"]
        
        return base_defaults.get(param_name)
    
    def get_smart_suggestion(self, param_name: str, current_params: Dict[str, Any] = None) -> Optional[str]:
        """获取智能建议（比默认值更智能）"""
        current_params = current_params or {}
        
        if param_name == "project_path" and "project_name" in current_params:
            project_name = current_params["project_name"]
            if self.project_context["in_product_dev"]:
                suggested_path = self.project_context["example_dir"] / project_name
                return str(suggested_path)
        
        return self.get_default(param_name, current_params)


class SmartParamHelper:
    """FastMCP智能参数助手"""
    
    def __init__(self):
        self.cache = ParameterCache()
        self.defaults = IntelligentDefaults()
    
    async def process_params(self, ctx, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能参数处理主函数
        
        Args:
            ctx: FastMCP Context对象
            tool_name: 工具名称
            params: 当前参数字典
            
        Returns:
            处理后的完整参数字典
        """
        processed = {}
        
        for param_name, param_value in params.items():
            if param_value is not None and param_value != "":  # 参数已提供且非空
                processed[param_name] = param_value
                continue
            
            # 处理缺失或空参数
            final_value = await self._handle_missing_param(
                ctx, tool_name, param_name, processed
            )
            
            if final_value:
                processed[param_name] = final_value
                # 缓存用户选择（除了默认值）
                if final_value != self.defaults.get_default(param_name, processed):
                    self.cache.cache_value(tool_name, param_name, final_value)
        
        return processed
    
    async def _handle_missing_param(self, ctx, tool_name: str, param_name: str, 
                                   current_params: Dict[str, Any]) -> Optional[str]:
        """处理缺失的参数"""
        
        # 1. 检查缓存
        cached_value = self.cache.get_cached_value(tool_name, param_name)
        if cached_value:
            try:
                response = await ctx.elicit(
                    f"参数 '{param_name}' 使用缓存值 '{cached_value}' 吗？\n"
                    f"回复 'y' 使用缓存值，或直接输入新值：",
                    str
                )
                if response.data.lower() in ['y', 'yes', '是', '']:
                    return cached_value
                elif response.data.strip():
                    return response.data.strip()
            except Exception as e:
                logger.warning(f"⚠️ 缓存值确认失败: {e}")
        
        # 2. 获取智能建议
        smart_suggestion = self.defaults.get_smart_suggestion(param_name, current_params)
        if smart_suggestion:
            try:
                response = await ctx.elicit(
                    f"参数 '{param_name}' 使用推荐值 '{smart_suggestion}' 吗？\n"
                    f"回复 'y' 使用推荐值，或直接输入新值：",
                    str
                )
                if response.data.lower() in ['y', 'yes', '是', '']:
                    return smart_suggestion
                elif response.data.strip():
                    return response.data.strip()
            except Exception as e:
                logger.warning(f"⚠️ 智能建议确认失败: {e}")
        
        # 3. 直接询问用户
        try:
            response = await ctx.elicit(f"请输入参数 '{param_name}'：", str)
            return response.data.strip() if response.data else None
        except Exception as e:
            logger.error(f"❌ 用户输入获取失败: {e}")
            # 回退到默认值
            return self.defaults.get_default(param_name, current_params)
    
    def cache_successful_params(self, tool_name: str, params: Dict[str, Any]):
        """缓存成功执行的参数"""
        for param_name, param_value in params.items():
            if param_value and param_value != "":
                self.cache.cache_value(tool_name, param_name, str(param_value))
    
    def get_project_context(self) -> Dict[str, Any]:
        """获取项目上下文信息"""
        return self.defaults.project_context


# 全局实例
smart_param_helper = SmartParamHelper()
