#!/usr/bin/env python3
"""
完整的MCP协议测试脚本
模拟Cursor与MCP服务器的完整交互流程
"""

import json
import subprocess
import sys
from pathlib import Path

def test_mcp_interaction():
    """测试完整的MCP交互流程"""
    print("=== 完整MCP交互测试 ===")
    
    script_path = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_fastmcp.py"
    python_path = "D:\\Programs\\Python313\\python.exe"
    
    # 准备消息序列
    messages = [
        # 1. 初始化
        {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "cursor",
                    "version": "0.43.6"
                }
            }
        },
        # 2. 初始化完成通知
        {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        },
        # 3. 列出工具
        {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        },
        # 4. 列出资源
        {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "resources/list"
        }
    ]
    
    # 创建输入数据
    input_data = ""
    for msg in messages:
        input_data += json.dumps(msg) + "\n"
    
    try:
        print(f"执行: {python_path} {script_path}")
        print(f"发送消息:\n{input_data}")
        
        # 启动服务器进程
        process = subprocess.Popen(
            [python_path, str(script_path)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        # 发送消息并获取响应
        stdout, stderr = process.communicate(input=input_data, timeout=30)
        
        print(f"\n--- 服务器响应 ---")
        print(f"返回码: {process.returncode}")
        print(f"标准输出:\n{stdout}")
        print(f"标准错误:\n{stderr}")
        
        if stdout:
            # 解析响应
            lines = stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if line.strip():
                    try:
                        response = json.loads(line)
                        print(f"\n响应 {i+1}: {json.dumps(response, indent=2, ensure_ascii=False)}")
                        
                        # 检查工具列表响应
                        if response.get('id') == 2 and 'result' in response:
                            tools = response['result'].get('tools', [])
                            print(f"\n🎯 发现 {len(tools)} 个工具:")
                            for tool in tools:
                                print(f"  - {tool['name']}: {tool.get('description', 'N/A')}")
                        
                        # 检查资源列表响应
                        if response.get('id') == 3 and 'result' in response:
                            resources = response['result'].get('resources', [])
                            print(f"\n📚 发现 {len(resources)} 个资源:")
                            for resource in resources:
                                print(f"  - {resource['uri']}: {resource.get('name', 'N/A')}")
                                
                    except json.JSONDecodeError as e:
                        print(f"无法解析响应 {i+1}: {e}")
                        print(f"原始内容: {line}")
        
        return process.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 服务器响应超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_mcp_config():
    """检查MCP配置文件"""
    print("\n=== 检查MCP配置 ===")
    
    config_path = Path(__file__).parent.parent.parent / ".cursor" / "mcp.json"
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✓ 配置文件内容:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False

def main():
    """主函数"""
    print("MCP 完整协议测试")
    print("=" * 50)
    
    # 检查配置
    config_ok = check_mcp_config()
    
    # 测试交互
    interaction_ok = test_mcp_interaction()
    
    print(f"\n=== 测试结果 ===")
    print(f"配置检查: {'✅' if config_ok else '❌'}")
    print(f"协议交互: {'✅' if interaction_ok else '❌'}")
    
    if config_ok and interaction_ok:
        print("\n🎉 MCP服务器完全正常！")
        print("\n📋 下一步:")
        print("1. 完全关闭 Cursor 应用程序")
        print("2. 重新启动 Cursor") 
        print("3. 打开这个工作区")
        print("4. 等待几秒钟让MCP服务器初始化")
        print("5. 查看右下角MCP Tools状态")
    else:
        print("\n❌ 发现问题，需要修复")

if __name__ == "__main__":
    main() 