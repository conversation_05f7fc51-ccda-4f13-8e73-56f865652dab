#include "saveExcel.h"
#include <QFile>
#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QDateTime>
#include <QMapIterator>
#include <QDebug>

CSaveExcel::CSaveExcel(){
    m_dtStr = "";

    m_message_box_ = new QWidget;
}

CSaveExcel::~CSaveExcel(){

}

/**
 * @brief create same file name
 * @param loc_name
 * @param fName
 * @param data
 */
void CSaveExcel::createOneFile(const QString &loc_name, const QString &fName, QMap<QString, QVariant> data) {
    m_dtStr = loc_name + fName + ".csv";

    QFileInfo info(m_dtStr);
    bool isExist = info.exists();

    if(!isExist) {
        QFile fileOut;
        fileOut.setFileName(m_dtStr);

        if(!fileOut.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QMessageBox::warning(m_message_box_, QString("保存文件"),
                                 QString("打开保存文件失败：") + fileOut.errorString());
            return;
        }

        //* 写表头
        QTextStream tsOut(&fileOut);
        QVariantMap::iterator it;
        for(it = data.begin(); it != data.end(); ++it) {
            tsOut << QString(it.key()) << ",";
        }
        tsOut << "\n";
    }

}

/**
 * @brief create file with open window
 */
void CSaveExcel::createRandomFile() {
    //QString filename = QFileDialog::getOpenFileName(this, tr("Open Xml"), ".", tr("Xml files (*.xml)"));
}
/**
 * @brief create new file with file name passed in
 * @param loc_name
 * @param fName
 * @param data
 */
void CSaveExcel::createFile(const QString &loc_name, const QString &fName, QMap<QString,QVariant> data) {
    m_dtStr = loc_name + fName + "-DbToExcel-" + QDateTime::currentDateTime().toString("yyyy-MM-dd-HH-mm") + ".csv";

    QFile fileOut;
    fileOut.setFileName(m_dtStr);

    if(!fileOut.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        QMessageBox::warning(m_message_box_, QString("保存文件"),
                             QString("打开保存文件失败：") + fileOut.errorString());
        return;
    }
    //* 写表头
    QTextStream tsOut(&fileOut);
    QVariantMap::iterator it;
    for(it = data.begin(); it != data.end(); ++it) {
        tsOut << QString(it.key()) << ",";
    }

    //  QMapIterator<QString, QVariant> iter(data); //只读迭代器
    //  for(;iter.hasNext();) {
    //        tsOut << QString(iter.key());
    //    }

    //  for(int i = 0; i < data.count(); ++i) {
    //      tsOut << QString(data[i].key());
    //    }
    tsOut << "\n";
}

/**
 * @brief CSaveExcel::writeFile
 * @param data
 */
void CSaveExcel::writeFile(QMap<QString, QVariant> data) {
    //* 创建文件
    QFile fileOut(m_dtStr);
    if(!fileOut.open(QIODevice::Append|QIODevice::ReadWrite))
    {
        QMessageBox::warning(m_message_box_, QString("保存文件"),
                             QString("打开保存文件失败：") + fileOut.errorString());

        return;
    }

    //* 写数据
    QTextStream tsOut(&fileOut);

#if 1
    QVariantMap::iterator it;
    for(it = data.begin(); it != data.end(); ++it) {
        tsOut << it.value().toString() << ",";// << endl;
    }
#else
#endif
    //  QMapIterator<QString, QVariant> iter(data); //只读迭代器

    //  //* 写内容
    //  for(;iter.hasNext();) {
    //      tsOut << iter.value().toString() << "," << endl; //
    //    }
    tsOut << "\n";

    //* 关闭文件
    fileOut.close();
}

/**
 * @brief save new file with file name passed in
 * @param loc_name
 * @param fName
 * @param data
 */
void CSaveExcel::saveFile(const QString &loc_name, const QString &fName, const QMap<QString,QVariant> &data) {
    QString dtStr = loc_name + fName+"DbToExcel" + QDateTime::currentDateTime().toString("yyyyMMddHHmmss") + ".csv";

    //* 创建文件
    QFile fileOut(dtStr);
    if(!fileOut.open(QIODevice::Append|QIODevice::ReadWrite))
    {
        QMessageBox::warning(m_message_box_, QString("保存文件"),
                             QString("打开保存文件失败：") + fileOut.errorString());
        return;
    }

    //* 写数据
    QTextStream tsOut(&fileOut);

    //* 写表头
    QMapIterator<QString,QVariant> iter(data); //只读迭代器
    for(;iter.hasNext();) {
        tsOut << QString(iter.key());
    }
    tsOut << "\n";
    //  for(QMap<QString,QVariant>::iterator iter = data.begin(); iter != data.end(); ++iter) {

    //    }

    //* 写内容
    for(;iter.hasNext();) {
        tsOut << iter.value().toString() << "," << endl; //
    }
    tsOut << "\n";

    //* 关闭文件
    fileOut.close();
}
