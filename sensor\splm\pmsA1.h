#ifndef _PMS_A1_H_
#define _PMS_A1_H_

#include "IPms.h"
#include "processListA.h"

#include "novaP.h"
#include "simpleInteractionP.h"

#include "vi5300.h"
#include "vl53l4cd.h"

class CPmsA1:public IPms {
  Q_OBJECT
public:
//    CPmsA1(IComm *port_);
    CPmsA1(IComm *port_, IComm *motor_port_);
    ~CPmsA1();


    enum EProtocolId {
        eCALIB1         = 0x01, //cross talk
        eCALIB2         = 0x02, //ref
        eCALIB3         = 0x03,
        eCALIB4         = 0x04,

        eMODIFY_PARAM   = 0x06,
        eMODE_CHANGE    = 0x07,

        eDATA_OUTPUT    = 0x08,
        eLOG_OUTPUT     = 0x09,

        eREAD_VERSION   = 0x0B,
        eREAD_PARAM     = 0x0C,
        eCHIP_ID        = 0x0D,

//        eFPS_COUNTS     = 0x20, //
    };
    Q_ENUM(EProtocolId);

    enum EMode {
        eInteraction_mode           = 1, //交互模式
        eAutoTrans_mode             = 2, //自动上送模式
        eIO_mode                    = 3, //工作触发模式
    };

    enum EDataOutput {
        eNo_output          = 0,
        eAll_info           = 1,
        eDist               = 2,
    };
//    QVector<StCalibItems> m_calib_items;
//    QVector<StTestItem> m_test_items;
//    QMap<QString, int> m_xml_param;


//    QVector<TVerify_process::StTask> mv_accuracyVerify_task_list;

    QByteArray portDataRead(void) override;
    void icom_change_interface(IComm *port_) override;

    QMap<QString, QByteArray>* getCmdList() override;
    bool writeCmdList(const QMap<QString, QByteArray> &cmd_list) override;

    bool modeChange(const uint16_t &mode) override;
    bool changeRigster() override;
    bool readInfo(const uint8_t &id, const uint16_t &data) override;

    EExecStatus calibTasksRun() override;
    EExecStatus calibSingleTaskRun(ISpmsSoc::ECalibProcess calib_step) override;

    EExecStatus verifyTasksRun() override;

    EExecStatus functionTestRun() override;

    bool interactionParsing(QByteArray str, int length) override;
    bool dataParsing(QByteArray str, int length) override;

private:
    /*模式与枚举*/
    enum ECalibProcess {
        eROTATE1                = 0,
        eCALIB1_STEP,
        eROTATE2,
        eCALIB2_STEP,
        eROTATE3,
        eCALIB3_STEP,
        eROTATE4,
        eCALIB4_STEP,
    };

    enum EFunctionProcess {
        eTRIG_ROTATE                = 0,
        eTRIGGER_TEST           ,
        eEMPTE_ROTATE           ,
        eEMPTE_TEST             ,
    };

    IComm *mi_port_ = nullptr;
    IComm *mi_motor_port_ = nullptr;
    QByteArray m_str_send; //指令数据

    //* calib
    typedef CProcessListA<CPmsA1, EExecStatus, CPmsA1::ECalibProcess> TCalib_process; //
    TCalib_process* mc_calib_process_ = nullptr;
    TCalib_process::StStepRecord *mst_calib_task_status_ = nullptr;

    typedef EExecStatus (CPmsA1::*pmsA1Fptr_)(void);

    //* verify


    //* trigger
    typedef CProcessListA<CPmsA1, EExecStatus, CPmsA1::EFunctionProcess> TFunction_process; //
    TFunction_process* mc_function_process_ = nullptr;
    TFunction_process::StStepRecord *mst_function_task_status_ = nullptr;


    //* communication status
    QByteArray* m_cache_buff_;

    StCommunicateStatus* mst_comm_status_ = nullptr;
    CSimpleInteraction::StInteractionFrame* mst_data_receive_ = nullptr;
//    CNovaP::StInteractionFrameRec* mst_data_receive_ = nullptr;
    CNovaP::StDataFrame*    mst_dist_ = nullptr;

    void cmd_init();


    //* calib
    EExecStatus boardRotate(void);
    EExecStatus boardRotateAck(void);

    EExecStatus calibTask1() override;
    EExecStatus calibTask1Ack() override;
    EExecStatus calibTask2() override;
    EExecStatus calibTask2Ack() override;
    EExecStatus calibTask3() override;
    EExecStatus calibTask3Ack() override;
    EExecStatus calibTask4() override;
    EExecStatus calibTask4Ack() override;

    //* accuracy
    EExecStatus checkRotate(void);
    EExecStatus checkRotateAck(void);

    EExecStatus distCheck(void);
    EExecStatus distCheckAck(void);

    //* trigger
    EExecStatus trigRotate(void);
    EExecStatus trigRotateAck(void);
    EExecStatus triggerTest(void);
    EExecStatus triggerTestAck(void);
    EExecStatus emptyRotate(void);
    EExecStatus emptyRotateAck(void);
    EExecStatus emptyTest(void);
    EExecStatus emptyTestAck(void);

public:
    QVector<TCalib_process::StTask> mv_calib_task_list;
    QVector<TFunction_process::StTask> mv_function_task_list;

signals:
    void dataOutput(IPms::ECommStep step, ECommStatus status, QByteArray bytes);

    void calibValueAckSignal(const uint8_t &calib_index, const uint16_t &calib_value1, const uint16_t &calib_value2);

    void triggerCntSignal(const uint8_t &cnt, const int16_t &dist, const int16_t &ref_dist);

public slots:
    void cmdUpdate(const uint8_t &cmd_id, const QString &cmd_name, const QByteArray &cmd);
};



#endif
