#ifndef _CHECK_CALCULATE_H_
#define _CHECK_CALCULATE_H_

#include <QByteArray>
#include <QString>

class CCheckCalculate
{
public:
    CCheckCalculate();
    ~CCheckCalculate();

    //------------------------------------ basic check ------------------
    static uint8_t calc_checksum(uint8_t const *p_data, uint8_t data_len) {
        uint16_t sum = 0;
        while (data_len--) {
            sum += *p_data++;
        }
        if(sum > 0xff)
        {
            sum=~sum;
            sum+=1;
        }
        return sum;
    }

    static uint8_t calc_checksum(const QByteArray &data, uint8_t data_len) {
        uint16_t sum = 0;
        while (data_len--) {
            sum += (uchar)data.at(data_len); //
        }
        if(sum > 0xff)
        {
            sum = ~sum;
            sum += 1;
        }
        return sum;
    }

    /**
     * @brief calc_checkXor
     * @param data
     * @param data_len
     * @param xor_index
     * @return
     */
    static uint8_t calc_checkXor(const QByteArray &data, const uint8_t &data_len, const uint8_t &xor_index) {
        uint8_t xor_tmp = 0;
        for(uint8_t for_i = 0; for_i < data_len; for_i++) {
          if(for_i != xor_index) {
            xor_tmp ^= data.at(for_i);
          }
        }
        return xor_tmp;
    }

private:


};



#endif
