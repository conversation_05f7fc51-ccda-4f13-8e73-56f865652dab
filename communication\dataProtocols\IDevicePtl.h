#ifndef _IDEVICE_PTL_H_
#define _IDEVICE_PTL_H_

#include <QByteArray>
#include <QVector>

class IDevicePtl
{
public:
  IDevicePtl(){};
  virtual ~IDevicePtl(){};

//protected:
  typedef struct{
    uint16_t                        device_addr;
    uint16_t                        addr; //
    uint16_t                        reg_num; //
    uint8_t                         bytes_num; //字节数
    QVector<uint16_t>               word_data;
    QVector<uint32_t>               dword_data; //双字寄存器
    uint16_t                        crc16; //
  } StInput;

public:
  virtual QByteArray getControlCmd(const StInput &input_data) = 0; //控制指令
  virtual QByteArray getWriteWordCmd(const StInput &input_data) = 0; //写入单字数据
//  virtual QByteArray getWriteDWordCmd(const StInput &input_data) = 0; //写入双字数据
  virtual QByteArray getReadCmd(const StInput &input_data) = 0; //读指令

};


#endif
