# MCP Server 创建指南

> **单一来源原则**: 本文档是MCP服务器开发的主要指南，所有相关文档通过双链关联。

基于Model Context Protocol (MCP) 标准的服务器创建完整指南。

## 📋 快速导航

- 🚨 **问题排查**: [[local_mcp-servers_issues]] - 常见问题和解决方案
- 🧪 **测试指南**: [[local_mcp-server_test_architecture]] - 测试架构和方法
- 🐛 **调试方法**: [[local_mcp-servers_debug_method]] - 调试技巧和工具
- 🐧 **Linux兼容**: [[linux_compatibility_guide]] - Linux平台适配
- 🔧 **开发指南**: [[local_mcp-servers_development]] - 详细开发流程

## 🎯 核心原则

- **奥卡姆剃刀**: 选择最简单有效的解决方案
- **单一职责**: 每个工具专注单一功能
- **单一来源**: 避免重复文档，统一维护

---

## 🧠 **智能参数识别与处理方案**

### **问题描述**
当AI对话框使用MCP工具时，需要正确识别和映射到本地脚本的具体参数组合。特别是对于支持多种操作模式的脚本（如`link_documents`和`sync_canvas`）。

### **解决方案**

#### **1. 参数映射策略**

**link_documents工具参数映射：**
```python
# 脚本：links/auto_link_documents.py
# 关键参数：force_update 决定模式
mode = "init" if force_update else "update"

# AI参数 -> 脚本参数映射
link_type_mapping = {
    "auto": ["--register", "--all", "--mode", mode],          # 注册所有组件
    "manual": ["--discover-associations"],                     # 手动发现关联
    "semantic": ["--sync-from-canvas"]                         # 从Canvas同步关联到INDEX
}

# 自然语言映射示例：
# "初始化关联文档" → force_update=true → --mode init
# "更新关联文档" → force_update=false → --mode update
# "同步Canvas关联" → link_type="semantic" → --sync-from-canvas
```

**sync_canvas工具参数映射：**
```python
# 脚本：canvas/auto_link_documents.py
# AI参数 -> 脚本参数映射
sync_direction_mapping = {
    "to_canvas": ["--sync-to-canvas"],                         # INDEX→Canvas同步
    "from_canvas": "ERROR: 使用link_documents工具",            # 重定向到links脚本
    "bidirectional": ["--sync-to-canvas"]                      # 只执行INDEX→Canvas
}

# 自然语言映射示例：
# "同步文档关联到canvas" → --sync-to-canvas

# 注意：Canvas→INDEX同步功能已移至link_documents工具的semantic模式
```

#### **2. 默认行为规则**

**当AI对话框无法明确识别参数时，使用以下默认值：**

1. **link_documents默认行为**：
   - `link_type = "auto"`
   - `force_update = false`
   - 对应脚本参数：`--register --all --mode update`

2. **sync_canvas默认行为**：
   - `sync_direction = "bidirectional"`
   - 对应脚本参数：`--register --all --mode update`

#### **3. 实现原则**

1. **更新模式优先**：默认使用`--mode update`而非`init`，避免覆盖现有数据
2. **全量处理**：默认使用`--all`处理所有组件，确保完整性
3. **安全第一**：未知参数时选择最安全的操作模式
4. **向后兼容**：保持与现有脚本接口的兼容性

#### **4. 路径处理统一方案**

**问题背景**：
- MCP配置的脚本目录是公共库目录（build-tools/scripts）
- 实际工作目录需要通过IDE上下文获取
- 不同工具的路径解析行为不一致，导致部分工具无法找到项目路径

**解决方案**：

1. **PROJECT_ROOT统一定义**：
```python
# 在MCP服务器中统一定义项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent  # 项目根目录
# 对应：build-tools/mcp-server_local_integrations/unified/product-development-complete/server.py
# 向上4级：KA/Product-development/
```

2. **路径解析标准化**：
```python
def resolve_project_path(project_path: str) -> str:
    """统一的路径解析函数"""
    if os.path.isabs(project_path):
        # 绝对路径直接使用
        return project_path
    else:
        # 相对路径基于PROJECT_ROOT解析
        return str(PROJECT_ROOT / project_path)
```

3. **subprocess工作目录设置**：
```python
# 所有subprocess调用必须使用PROJECT_ROOT作为工作目录
result = subprocess.run(
    cmd,
    cwd=PROJECT_ROOT,  # 统一工作目录
    timeout=SCRIPT_TIMEOUT,
    capture_output=True,
    text=True
)
```

4. **脚本参数传递**：
```python
# 传递给脚本的路径参数必须是解析后的绝对路径
resolved_path = resolve_project_path(project_path)
cmd = [sys.executable, script_path, resolved_path, "--json"]
```

**实施要求**：
- 所有MCP工具必须使用统一的路径解析函数
- 已修复工具：get_project_info（参考P007解决方案）
- 待修复工具：link_documents, sync_canvas, create_config_template等
- 测试验证：确保相对路径"example/test_single_layer"能被正确解析

#### **5. 错误处理策略**

1. **模拟测试禁止**：所有工具必须调用实际的本地脚本，禁止使用模拟模式
2. **脚本不存在**：直接报错，不使用内置逻辑替代
3. **参数错误**：提供详细的错误信息和建议的正确参数
4. **超时处理**：设置合理的超时时间，避免无限等待
5. **路径错误**：使用统一路径解析，提供清晰的路径错误信息

## 📊 系统要求

+ **系统**: Windows/Linux/macOS
+ **平台**: VSCode / Cursor
+ **语言**: Python 3.8+
+ **协议**: [MCP](https://github.com/modelcontextprotocol)
+ **参考**: [Scrapfly指南](https://scrapfly.io/blog/how-to-build-an-mcp-server-in-python-a-complete-guide/)

## 📖 MCP 核心概念

**Model Context Protocol (MCP)** 是LLM与外部工具通信的开放标准。

### 🏗 **MCP架构组件**
- **MCP客户端**: 维护与服务器的1:1连接的协议客户端
- **MCP服务器**: 通过标准化协议暴露特定功能的轻量级程序
- **本地数据源**: MCP服务器可以安全访问的计算机文件、数据库和服务
- **远程服务**: 通过API等方式连接的外部系统

### 🔗 三大组件

1. **Tools**: 可调用的函数
2. **Resources**: 文件或数据源
3. **Prompts**: 模板化消息

### 🔧 **本地MCP服务器架构模式**

#### 架构模式1: **FastMCP脚本包装模式 (推荐)**

**特点**:
- **主服务器文件**: `server.py`
- **架构特点**: FastMCP原生实现 + 智能参数处理 + 业务流程组织
- **脚本包装模式**: MCP服务器通过subprocess调用本地脚本，底层scripts目录的脚本实现完全不受影响 [[python调用本地脚本方式]]
- **无内置逻辑**: 找不到本地脚本则直接报错，严格遵循脚本调用模式
- **MCP协议遵循**: 严格遵循MCP协议的返回值格式和错误处理
- **智能参数处理**: 三层处理机制：缓存 → 智能默认值 → 用户交互
- **业务流程导向**: 按照产品开发的实际业务流程组织工具
- **超时处理**: 防止卡住死循环情况

**脚本路径映射规则**:
- MCP工具函数名 → `scripts/{function_name}.py` (根目录查找)
- 如果脚本在子目录，需要在工具实现中明确指定完整路径
- 路径必须与[[产品体系构建工具关联表]]中的Scripts链接列保持一致

**实现示例**:
```python
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("My Server")

@mcp.tool()
def my_tool(param: str) -> str:
    """工具描述"""
    # 调用本地脚本
    result = subprocess.run([...])
    return result.stdout
```

#### 架构模式2: **FastMCP内置逻辑模式**

**特点**:
- 所有业务逻辑直接在MCP工具函数中实现
- 不调用外部脚本，完全自包含
- 适合简单功能和快速原型

**实现示例**:
```python
@mcp.tool()
async def simple_tool(param: str) -> str:
    # 直接在函数中实现业务逻辑
    return f"处理结果: {param}"
```

#### 架构模式3: **低级服务器模式 - 完全控制**

**特点**:
- 使用底层MCP Server API
- 完全自定义协议处理
- 适合复杂场景和高级定制

**实现示例**:
```python
from mcp.server import Server

server = Server("example-server")

@server.call_tool()
async def call_tool(name: str, arguments: dict):
    # 自定义协议处理
    return await execute_local_script(arguments)
```

#### 架构模式4: **其他语言SDK**
- **TypeScript SDK**: Node.js环境
- **Java SDK**: Spring AI集成
- **C# SDK**: .NET环境
- **Kotlin SDK**: JetBrains支持
- **Go SDK**: Google合作
- **Rust SDK**: 高性能场景

### ✅ **当前实现方式验证**
我们使用的**"MCP服务器调用本地Python脚本"**方式：
- ✅ **完全符合MCP协议规范**
- ✅ **官方推荐的标准实现模式**
- ✅ **支持工具、资源、提示等所有MCP功能**
- ✅ **适合本地数据源和服务集成**

### 🎯 核心优势

- 直接集成到AI界面
- 无需复杂API工程
- 适合快速原型开发

## 🛠️ 项目结构

### 📁 标准目录

```
mcp-server/
├── server.py              # 主服务器
├── tools/                 # 工具模块
├── requirements.txt       # 依赖
└── README.md             # 说明
```

### 🏗️ 开发规范

#### 1. **智能参数**
详见: [[final_smart_params_implementation_plan]]

#### 2. **模块化设计**
- 工具、资源、提示分离
- 使用装饰器: `@mcp.tool()`, `@mcp.resource()`, `@mcp.prompt()`

#### 3. **错误处理**
```python
@mcp.tool()
def safe_divide(a: float, b: float) -> float:
    if b == 0:
        raise ValueError("Division by zero")
    return a / b
```

#### 4. **关键机制**

**🚨 防止AI对话框卡住 - 强制超时保护**

```python
@mcp.tool()
def safe_tool(param: str) -> Dict[str, Any]:
    try:
        result = subprocess.run(
            cmd, timeout=120,  # 强制2分钟超时
            capture_output=True, text=True, check = True,
        )
        return {"success": True, "result": result.stdout}
    except subprocess.TimeoutExpired:
        return {"success": False, "error": "操作超时"}
    except Exception as e:
        return {"success": False, "error": str(e)}
```

**详细异常处理**: 参见 [[local_mcp-servers_issues]]

## 🚀 快速开始

### 1. **环境设置**

```bash
python -m venv mcp-env && mcp-env\Scripts\activate
pip install mcp fastmcp
```

### 2. **基础服务器**

```python
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Calculator")

@mcp.tool()
def add(a: int, b: int) -> int:
    """数字相加"""
    return a + b

if __name__ == "__main__":
    mcp.run(transport="stdio")
```

### 3. **详细开发**

参见: [[local_mcp-servers_development]]

## 🧪 测试与调试

- **测试**: [[local_mcp-server_test_architecture]]
- **调试**: [[local_mcp-servers_debug_method]]

## ⚠️ Windows配置要点

### 🚫 限制
- cwd参数无效
- 环境变量不展开
- 路径不能含空格

### ✅ 正确配置

```json
{
  "mcpServers": {
    "my-server": {
      "command": "C:/projects/mcp/.venv/Scripts/python.exe",
      "args": ["C:/projects/mcp/server.py"]
    }
  }
}
```

## 🔗 集成配置

### Cursor/VSCode配置

```json
{
  "mcpServers": {
    "my-server": {
      "command": "C:/projects/mcp/.venv/Scripts/python.exe",
      "args": ["C:/projects/mcp/server.py"]
    }
  }
}
```

##  参考资源

- [MCP官方文档](https://github.com/modelcontextprotocol)
- [Python SDK](https://github.com/modelcontextprotocol/python-sdk)

## 🔍 故障排除

详见: [[local_mcp-servers_issues]]

### 常见问题
1. **服务器启动失败** - 检查Python路径和依赖
2. **工具无法调用** - 确认函数签名和装饰器
3. **配置无效** - 使用绝对路径，避免空格

---

**📝 文档精简完成** - 遵循奥卡姆剃刀原则，保留核心内容，详细信息通过双链关联到专门文档。