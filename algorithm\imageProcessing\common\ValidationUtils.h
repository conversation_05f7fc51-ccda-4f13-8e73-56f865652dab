#ifndef IMAGEPROCESSING_VALIDATIONUTILS_H
#define IMAGEPROCESSING_VALIDATIONUTILS_H

#include "ImageData.h"
#include "ProcessingException.h"
#include <QString>
#include <QDebug>

namespace ImageProcessing {

/**
 * @brief 参数验证工具类
 */
class ValidationUtils {
public:
    /**
     * @brief 验证图像数据是否有效
     * @param data 图像数据
     * @param paramName 参数名称（用于错误信息）
     * @throws InvalidImageDataException 如果数据无效
     */
    template<typename T>
    static void validateImageData(const ImageData<T>& data, const QString& paramName = "image") {
        if (data.empty()) {
            throw InvalidImageDataException(QString("%1 is empty").arg(paramName));
        }
        
        if (!data.isValid()) {
            throw InvalidImageDataException(QString("%1 has invalid structure").arg(paramName));
        }
    }

    /**
     * @brief 验证两个图像数据的尺寸是否兼容
     * @param src 源图像
     * @param dst 目标图像
     * @param allowResize 是否允许自动调整目标图像大小
     * @throws InvalidImageDataException 如果尺寸不兼容
     */
    template<typename T1, typename T2>
    static void validateImageSizeCompatibility(const ImageData<T1>& src, 
                                             const ImageData<T2>& dst,
                                             bool allowResize = false) {
        validateImageData(src, "source image");
        
        if (!allowResize) {
            validateImageData(dst, "destination image");
            if (src.width() != dst.width() || src.height() != dst.height()) {
                throw InvalidImageDataException(
                    QString("Image size mismatch: src(%1x%2) vs dst(%3x%4)")
                    .arg(src.width()).arg(src.height())
                    .arg(dst.width()).arg(dst.height())
                );
            }
        }
    }

    /**
     * @brief 验证数值范围
     * @param value 要验证的值
     * @param min 最小值
     * @param max 最大值
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果值超出范围
     */
    template<typename T>
    static void validateRange(const T& value, const T& min, const T& max, const QString& paramName) {
        if (value < min || value > max) {
            throw InvalidParameterException(paramName, 
                QString("value %1 is out of range [%2, %3]")
                .arg(value).arg(min).arg(max));
        }
    }

    /**
     * @brief 验证正数
     * @param value 要验证的值
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果值不是正数
     */
    template<typename T>
    static void validatePositive(const T& value, const QString& paramName) {
        if (value <= 0) {
            throw InvalidParameterException(paramName, 
                QString("value %1 must be positive").arg(value));
        }
    }

    /**
     * @brief 验证非负数
     * @param value 要验证的值
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果值是负数
     */
    template<typename T>
    static void validateNonNegative(const T& value, const QString& paramName) {
        if (value < 0) {
            throw InvalidParameterException(paramName, 
                QString("value %1 must be non-negative").arg(value));
        }
    }

    /**
     * @brief 验证奇数（通常用于卷积核大小）
     * @param value 要验证的值
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果值不是奇数
     */
    static void validateOddNumber(int value, const QString& paramName) {
        if (value % 2 == 0) {
            throw InvalidParameterException(paramName, 
                QString("value %1 must be odd").arg(value));
        }
    }

    /**
     * @brief 验证插值参数
     * @param srcWidth 源图像宽度
     * @param srcHeight 源图像高度
     * @param dstWidth 目标图像宽度
     * @param dstHeight 目标图像高度
     * @throws InvalidParameterException 如果参数无效
     */
    static void validateInterpolationParams(uint32_t srcWidth, uint32_t srcHeight,
                                          uint32_t dstWidth, uint32_t dstHeight) {
        validatePositive(srcWidth, "source width");
        validatePositive(srcHeight, "source height");
        validatePositive(dstWidth, "destination width");
        validatePositive(dstHeight, "destination height");

        // 检查是否超出合理范围
        const uint32_t MAX_SIZE = 10000; // 最大尺寸限制
        validateRange(srcWidth, 1u, MAX_SIZE, "source width");
        validateRange(srcHeight, 1u, MAX_SIZE, "source height");
        validateRange(dstWidth, 1u, MAX_SIZE, "destination width");
        validateRange(dstHeight, 1u, MAX_SIZE, "destination height");
    }

    /**
     * @brief 验证滤波器核大小
     * @param kernelSize 核大小
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果核大小无效
     */
    static void validateKernelSize(int kernelSize, const QString& paramName = "kernel size") {
        validatePositive(kernelSize, paramName);
        validateOddNumber(kernelSize, paramName);
        
        const int MAX_KERNEL_SIZE = 15; // 最大核大小限制
        validateRange(kernelSize, 1, MAX_KERNEL_SIZE, paramName);
    }

    /**
     * @brief 验证概率值（0-1之间）
     * @param probability 概率值
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果概率值无效
     */
    static void validateProbability(float probability, const QString& paramName) {
        validateRange(probability, 0.0f, 1.0f, paramName);
    }

    /**
     * @brief 验证百分比值（0-100之间）
     * @param percentage 百分比值
     * @param paramName 参数名称
     * @throws InvalidParameterException 如果百分比值无效
     */
    static void validatePercentage(float percentage, const QString& paramName) {
        validateRange(percentage, 0.0f, 100.0f, paramName);
    }

    /**
     * @brief 记录验证信息（调试用）
     * @param message 验证信息
     */
    static void logValidation(const QString& message) {
        qDebug() << "[Validation]" << message;
    }

    /**
     * @brief 记录验证警告
     * @param message 警告信息
     */
    static void logWarning(const QString& message) {
        qWarning() << "[Validation Warning]" << message;
    }
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_VALIDATIONUTILS_H
