# LA-T5 文档结构规划 v2.0

**规划日期**: 2025-01-16  
**版本**: v2.0  
**基于用户反馈优化**  
**目标**: 建立清晰的文档分类体系和关联关系管理  

## 🎯 优化要点

### 用户反馈要点
1. **delivery**: delivery更明确表示交付文档
2. **manual**: manual更符合用户手册的定位
3. **新增support目录**: 支持信息文档很重要
4. **文档关联关系**: 需要明确体现文档间的关联

### 核心改进
- ✅ 使用 `delivery/` 替代 `output/`
- ✅ 使用 `manual/` 替代 `usage/`
- ✅ 新增 `support/` 支持信息目录
- ✅ 建立文档关联矩阵系统
- ✅ 规范化文档引用格式

## 📁 优化后的目录结构

```
docs/
├── demands/                    # 客户需求和反馈
│   ├── 非规则光斑处理.excalidraw.md
│   ├── 光斑滤波效果优化需求.md
│   ├── 性能提升需求.md
│   └── 需求跟踪表.md
│
├── issues/                     # 开发技术问题
│   ├── weighted_average_boundary_fix.md
│   ├── 配置流程重复定义问题.md
│   ├── 性能优化问题.md
│   └── 问题跟踪表.md
│
├── development/                # 开发技术文档
│   ├── 算法分析/
│   │   ├── weighted_average_analysis.md
│   │   ├── 边界处理算法研究.md
│   │   └── performance_optimization.md
│   ├── 代码架构/
│   │   ├── filter_architecture.md
│   │   ├── configuration_design.md
│   │   └── testing_framework.md
│   ├── 技术验证/
│   │   ├── algorithm_validation.md
│   │   ├── performance_benchmarks.md
│   │   └── regression_tests.md
│   └── 开发指南/
│       ├── coding_standards.md
│       ├── testing_guidelines.md
│       └── documentation_guide.md
│
├── delivery/                   # 交付文档 ⭐ 优化
│   ├── manual/                # 用户手册
│   │   ├── readme.md          # 手册总览
│   │   ├── lenAdjust/         # 镜头调节功能手册
│   │   │   └── TOF接收镜片光路耦合软件使用文档.md
│   │   ├── lenMEMD/           # MEMD功能手册
│   │   └── lenRework/         # 返工功能手册
│   ├── release_notes/         # 版本发布说明
│   │   ├── v1.4.4.md         # 当前版本发布说明
│   │   └── changelog.md       # 完整变更日志
│   └── reports/               # 各类报告
│       ├── quality_reports/   # 质量报告
│       ├── test_reports/      # 测试报告
│       └── delivery_reports/  # 交付报告
│
├── support/                    # 支持信息文档 ⭐ 新增
│   ├── contact_info.md        # 技术支持联系方式
│   ├── service_process.md     # 服务流程说明
│   ├── faq.md                 # 常见问题汇总
│   ├── troubleshooting.md     # 故障排除指南
│   └── training_materials/    # 培训材料
│
├── 文档关联矩阵.md            # 文档关联关系 ⭐ 新增
├── 文档引用规范.md            # 引用格式规范 ⭐ 新增
└── 文档结构规划_v2.md         # 本文档
```

## 🔗 文档关联关系设计

### 关联关系类型
1. **需求驱动关系**: demands → issues → development → delivery
2. **版本关联关系**: 同一功能在不同文档中的版本对应
3. **引用关系**: 文档间的相互引用和依赖
4. **更新关联**: 一个文档更新时需要同步更新的其他文档

### 标准化引用格式
```markdown
# 文档内引用格式
[[文档类型/文档名称]] - 描述

# 示例
[[demands/光斑滤波效果优化需求]] - 客户需求原始描述
[[issues/weighted_average_boundary_fix]] - 技术问题解决方案
[[development/算法分析/边界处理算法研究]] - 详细技术分析
[[delivery/manual/lenAdjust/TOF接收镜片光路耦合软件使用文档]] - 用户使用指南
```

### 文档ID系统
每个文档都有唯一标识：
```
格式: [类型]-[模块]-[序号]
示例:
- REQ-FILTER-001: 光斑滤波效果优化需求
- ISS-FILTER-001: 边界处理算法问题
- DEV-ALGO-001: 边界处理算法研究
- MAN-ADJUST-001: TOF接收镜片光路耦合软件使用文档
```

## 📝 各目录详细说明

### 1. demands/ - 客户需求和反馈
**目标用户**: 项目管理、产品经理、客户  
**文档ID前缀**: REQ-  
**关联输出**: issues/, delivery/manual/

### 2. issues/ - 开发技术问题
**目标用户**: 开发人员、技术负责人  
**文档ID前缀**: ISS-  
**关联输入**: demands/  
**关联输出**: development/, delivery/release_notes/

### 3. development/ - 开发技术文档
**目标用户**: 开发人员、技术研究人员  
**文档ID前缀**: DEV-  
**关联输入**: issues/  
**关联输出**: delivery/reports/

### 4. delivery/ - 交付文档 ⭐
**目标用户**: 客户、最终用户  
**子目录说明**:
- **manual/**: 用户手册，文档ID前缀 MAN-
- **release_notes/**: 版本发布说明，文档ID前缀 REL-
- **reports/**: 各类报告，文档ID前缀 RPT-

### 5. support/ - 支持信息文档 ⭐
**目标用户**: 客户、技术支持人员  
**文档ID前缀**: SUP-  
**内容特点**:
- 技术支持联系方式和流程
- 常见问题和解决方案
- 培训材料和操作指南
- 故障排除和诊断工具

## 🔄 文档生命周期管理

### 文档创建流程
1. **需求阶段**: 在demands/创建需求文档
2. **分析阶段**: 在issues/创建问题分析文档
3. **开发阶段**: 在development/创建技术文档
4. **交付阶段**: 在delivery/创建用户文档
5. **支持阶段**: 在support/创建支持文档

### 文档更新流程
1. **变更触发**: 任何文档的重要更新
2. **影响分析**: 检查文档关联矩阵
3. **同步更新**: 更新所有相关文档
4. **版本标记**: 更新版本号和关联记录

### 文档审查机制
- **每周审查**: 检查文档状态和关联关系
- **版本发布前**: 全面审查所有交付文档
- **季度审查**: 评估文档结构和关联关系的有效性

## 🎯 实施计划

### 第一阶段：结构调整（立即执行）
1. ✅ 创建新的目录结构
2. ✅ 移动现有文档到正确位置
3. ✅ 建立文档关联矩阵
4. ✅ 制定引用规范

### 第二阶段：内容优化（本周内）
1. 🔄 为所有文档分配ID
2. 🔄 建立标准化引用链接
3. 🔄 创建support/目录内容
4. 🔄 完善文档关联关系

### 第三阶段：流程建立（持续进行）
1. 📋 建立文档生命周期管理流程
2. 📋 制定文档更新同步机制
3. 📋 建立定期审查制度

---

**文档维护责任**:
- demands/: 产品经理负责
- issues/: 技术负责人负责  
- development/: 开发人员负责
- delivery/: 技术写作人员负责
- support/: 客户服务团队负责

**审查周期**: 每周审查文档状态，每月审查结构优化
