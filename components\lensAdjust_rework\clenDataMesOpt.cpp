#include "clenDataMesOpt.h"
#include <QDebug>
#include <QMessageBox>
#include <QSqlDatabase>
#include <QTimerEvent>
#include <math.h>
#include <qsqlquery.h>


#include "uart.h"
//#include "sensorCoinD.h"
#include "sensorBoardFactory.h"

#include "qLog.h"
#include "typeConvert.h"


#define TIMER_MS         5
#define MAIN_UART_BAUD   230400
#define DEVICE_UART_BAUD 115200


#define MES_SUPER_USER   "admin"

CLenDataMesOpt::CLenDataMesOpt(const NClenMes::StUiConfig &config)
    : mst_config_(&config)  //
      ,
      m_timerId(0),
      m_port_update_cnt(0),
      mc_processList_(new TClen_process),
      mst_task_status_(new TClen_process::StStepRecord)
      //  , mi_icomm_(new CUART("", MAIN_UART_BAUD)) //默认配置
      //  , mi_top_board_(new CSensorCoinD(mi_icomm_)) //设备端口
      ,
      mst_comm_status_(new StCommunicateStatus),
      m_message_box_(new QWidget),
      mc_sql_handle_(new CLenSqlHandle),
      mst_work_order_(new CLenSqlHandle::StWorkOrder),
      mst_xpid_det_(new CLenSqlHandle::StXpiddet),
      mst_mes_xsub2_data_(new CLenSqlHandle::StResultToMes),
      mst_result_(new StResult),
      mst_dds_(new StDds) {
    //********************************** varibles init ******************************
    //* 1.默认外部参数读取
    mst_iniConfig = DATACONFIG;
    qInfo() << "lenDataMesOpt/ config ini: "
            << "\n user id:" << mst_iniConfig.userid << "\n operator position:" << mst_iniConfig.op << "\n sensor version:" << mst_iniConfig.version
            << "\n work order:" << mst_iniConfig.work_number << "\n work domain" << mst_iniConfig.work_domain
            << "\n sensor project: " << mst_iniConfig.sensor_device << "\n sensor project baud: " << mst_iniConfig.sensor_device_buad;


    mi_icomm_ = new CUART("", mst_iniConfig.sensor_device_buad);  //默认配置
                                                                  //    mi_top_board_ = new CSensorCoinD(mi_icomm_); //设备端口
    mi_top_board_ = ISensorBoardFactory::getInstance().sensorBoardCreate((ISensorBoardFactory::ESensorBoard)mst_iniConfig.sensor_device, mi_icomm_);  //设备端口

    //* 2.内部参数初始化
    //**********************************************子线程***********************
    //* 1.初始化就创建子线程
    LOG_INFO(MyLogger::LogType::PROCESS_STATUS, QString("Main thread id:%1").arg(reinterpret_cast<quintptr>(QThread::currentThread())));
    m_sub_thread_ = new QThread;  // 创建线程对象

    /* 创建工作的类对象，千万不要指定给创建的对象指定父对象
     * 如果指定了: QObject::moveToThread: Cannot move objects with a parent
     */
    m_serial_thread_ = new CLensAdjustSerial(nullptr, mi_top_board_, nullptr);
    m_serial_thread_->moveToThread(m_sub_thread_);  //将工作的类对象移动到创建的子线程对象中

    //* 1.3 启动线程
    m_sub_thread_->start();
    //    m_serial_thread_->task_id_change(4);


    //******************************************* connect ***************************
    qRegisterMetaType<CLenDataMesOpt::EProcessStep>("CLenDataMesOpt::EProcessStep");

    connect(this, &CLenDataMesOpt::subThreadSignal, m_serial_thread_, &CLensAdjustSerial::loop);
    connect(m_sub_thread_, &QThread::finished, m_serial_thread_, &QObject::deleteLater);


    connect(dynamic_cast<CSensorCoinD *>(mi_top_board_), &CSensorCoinD::dataOutput, this, &CLenDataMesOpt::sensorDataReceive);  //无法用多态实现


    //************************************* task list init *************************
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::readyStep, &CLenDataMesOpt::readyAck, true, 200, 0, 0, 0));  // readyAck

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::startStep, nullptr, true, 0, 0, 0, 0));
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::calibMode, &CLenDataMesOpt::calibModeAck, true, 0, 0, 100, 3));             //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::chipIdStep, &CLenDataMesOpt::chipIdAck, true, 500, 0, 200, 5));             //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::sensorVersionStep, &CLenDataMesOpt::sensorVersionAck, true, 0, 0, 50, 3));  //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::greyMapMode, &CLenDataMesOpt::greyMapModeAck, true, 0, 0, 100, 3));         //

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenDataMesOpt::compStep, &CLenDataMesOpt::compAck, true, 0, 0, 100, 3));


    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    m_timerId = startTimer(TIMER_MS);

    //******************************** 3.数据库 ******************************
    //    if(mst_iniConfig.userid != MES_SUPER_USER) { //非测试账号，连接MES
    //        mst_mes_xsub2_data_->userid = mst_iniConfig.userid;
    //        mst_mes_xsub2_data_->op = mst_iniConfig.op;
    //        mst_work_order_->wo_lot = mst_iniConfig.work_number;
    mst_work_order_->domain = mst_iniConfig.work_domain;
    //        mst_xpid_det_->work_order = mst_iniConfig.work_number;
    //    }
    //* local
    if (!mc_sql_handle_->connectXSub2DetDB()) {
        QMessageBox::warning(m_message_box_, QString("数据库连接失败"), QString("请检查数据库连接"));
    }

//#define MES_TEST
#ifdef MES_TEST
    //    mc_sql_handle_->connectTestDB();
    uint16_t error_dds = 0;

    mst_work_order_->wo_lot = "6688";
    error_dds               = mc_sql_handle_->checkWorkOrder(mst_work_order_);

    if (error_dds == 0) {
        mst_xpid_det_->mcuid      = QString("30393337000e30353332db9f").toUpper();
        mst_xpid_det_->work_order = mst_work_order_->wo_lot;  // 6688
        mst_xpid_det_->domin      = mst_work_order_->domin;
        error_dds |= mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, true);
    }

    if (error_dds == 0) {
        mesDataHandle(mst_mes_xsub2_data_);
        error_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);
    }


#endif
    //* 4.test
}

CLenDataMesOpt::~CLenDataMesOpt() {
    delete mc_processList_;
    delete mst_task_status_;

    //* 子线程注销
    //    m_serial_thread_->m_task_id = 0;
    m_serial_thread_->task_id_change(0);
    m_sub_thread_->quit();
    m_sub_thread_->wait();
    delete m_sub_thread_;  //会析构 m_serial_thread_

    killTimer(m_timerId);
    QThread::msleep(50);


    delete mi_top_board_;
    if (mi_icomm_ != nullptr)
        delete mi_icomm_;

    delete mst_comm_status_;

    /*释放内存*/
    delete mc_sql_handle_;
    delete mst_work_order_;
    delete mst_xpid_det_;
    delete mst_mes_xsub2_data_;
    delete m_message_box_;

    delete mst_result_;
    delete mst_dds_;
}


void CLenDataMesOpt::timerEvent(QTimerEvent *event) {
    //  Q_UNUSED(event);

    if (m_timerId == event->timerId()) {
        //*******************************非任务task
        //* 1.串口列表刷新
        m_port_update_cnt++;
        if (m_port_update_cnt == 400) {
            m_port_update_cnt = 0;
            portlistUpdate();
        }

        //******************************* 任务循环 ********************
        if (mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_) == eFATAL) {  // tasks exec errror, dont have ack or other reason
            mst_dds_->process_step_dds |= (1 << (uint8_t)mst_task_status_->cur_step);
            emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_STEP_ERROR, mst_dds_->process_step_dds));
            //            mc_processList_->taskInit(&mv_task_list, mst_task_status_);

            mv_task_list[EProcessStep::eCOMPLETE].flag.stop = true;
        }

        //* 状态界面显示更新
        if (mst_task_status_->cur_step != mst_task_status_->last_step) {
            mst_task_status_->last_step = mst_task_status_->cur_step;

            //            if(mst_task_status_->cur_status != mst_task_status_->last_status) { //?
            emit stepStatusSignal((int16_t)mst_task_status_->cur_step, (int16_t)mst_task_status_->cur_ack_status);

            //            }
        }
    }
}

/**
 * @brief
 */
void CLenDataMesOpt::varibleInit() {
    //* 运行状态
    mc_processList_->taskInit(&mv_task_list, mst_task_status_);
    m_serial_thread_->task_id_change(4);

    mst_task_status_->cur_step        = eCOMPLETE;
    mst_task_status_->cur_exec_status = eWAIT;
    mst_task_status_->cur_ack_status  = eWAIT;
    mst_task_status_->last_step       = eCOMPLETE;
    mst_task_status_->last_status     = eWAIT;

    //* 原数据
    m_origin_bytes.clear();

    //* 库数据
    mst_mes_xsub2_data_->nbr      = "";
    mst_mes_xsub2_data_->op       = "";
    mst_mes_xsub2_data_->userid   = "";
    mst_mes_xsub2_data_->date     = "";
    mst_mes_xsub2_data_->time     = 0;
    mst_mes_xsub2_data_->rslt     = false;
    mst_mes_xsub2_data_->rsn_code = "";
    mst_mes_xsub2_data_->param[0] = 0;
    mst_mes_xsub2_data_->param[1] = 0;
    mst_mes_xsub2_data_->param[2] = 0;
    mst_mes_xsub2_data_->param[3] = 0;
    mst_mes_xsub2_data_->param[4] = 0;
    mst_mes_xsub2_data_->param[5] = 0;
    mst_mes_xsub2_data_->param[6] = 0;
    mst_mes_xsub2_data_->param[7] = 0;
    mst_mes_xsub2_data_->param[8] = 0;

    //* result data
    mst_result_->chip_id.clear();
    mst_result_->origin_data = "";
    mst_result_->load_data   = "";
    mst_result_->fatal_reason.clear();

    mst_dds_->process_item_dds.errors = 0;
    mst_dds_->process_step_dds        = 0;
    mst_dds_->mes_dds                 = 0;
}

void CLenDataMesOpt::mesDataShow(const QString &tr) {
    QString mes_data =
        tr + QObject::tr("(xsub2_domain, xsub2_trnbr, xsub2_nbr, xsub2_op, xsub2_userid, xsub2_date, xsub2_time, xsub2_rslt, xsub2_rsn_code, "
                         "xsub2_param1, xsub2_param2, xsub2_param3, xsub2_param4, xsub2_param5, xsub2_param6, xsub2_param7, xsub2_param8, xsub2_param9)"
                         "values('%1', '%2', '%3', '%4', '%5', '%6', '%7', '%8', '%9', '%10', '%11', '%12', '%13', '%14', '%15', '%16', '%17', '%18')")
                 .arg(mst_mes_xsub2_data_->domain)
                 .arg(mst_mes_xsub2_data_->trnbr)
                 .arg(mst_mes_xsub2_data_->nbr)
                 .arg(mst_mes_xsub2_data_->op)
                 .arg(mst_mes_xsub2_data_->userid)
                 .arg(mst_mes_xsub2_data_->date)
                 .arg(mst_mes_xsub2_data_->time)
                 .arg(mst_mes_xsub2_data_->rslt)
                 .arg(mst_mes_xsub2_data_->rsn_code)
                 .arg(mst_mes_xsub2_data_->param[0])
                 .arg(mst_mes_xsub2_data_->param[1])
                 .arg(mst_mes_xsub2_data_->param[2])
                 .arg(mst_mes_xsub2_data_->param[3])
                 .arg(mst_mes_xsub2_data_->param[4])
                 .arg(mst_mes_xsub2_data_->param[5])
                 .arg(mst_mes_xsub2_data_->param[6])
                 .arg(mst_mes_xsub2_data_->param[7])
                 .arg(mst_mes_xsub2_data_->param[8]);

    emit moduleInfoShowSignal(false, mes_data);
}

/**
 * @brief CLenAdjustOpt::portlistUpdate
 * @return
 */
bool CLenDataMesOpt::portlistUpdate(void) {
    QStringList *port_list_ = new QStringList;

    bool port_flag = mi_icomm_->scanPort(port_list_, mst_config_->cur_port_name);  //列表名称变化
    emit portUpdateSignal(port_list_, port_flag);

    return true;
}

/**
 * @brief CLenDataMesOpt::dataRecive
 * @param step
 * @param status
 * @param bytes
 */
void CLenDataMesOpt::sensorDataReceive(ITopBoard::ECommStep step, ECommStatus status, QByteArray bytes) {
    uint16_t num = bytes.length() >> 2;

    mst_comm_status_->comm_status = status;
    if (num != 0) {
#if 0
        if(m_origin_bytes.length() == 0) m_origin_bytes = bytes;
        else qDebug() << "-clen: data receive busy";
#else
        m_origin_bytes = bytes;  //直接覆盖
#endif
    }

    switch (step) {
    case ITopBoard::eMODE_CHANGE:
        switch (mst_task_status_->cur_step) {
        case EProcessStep::eGREY_MODE:
            mv_task_list[eGREY_MODE].flag.stop = true;
            break;
        case EProcessStep::eCALIB_MODE:
            mv_task_list[eCALIB_MODE].flag.stop = true;
            break;
        default:
            break;
        }
        break;
    case ITopBoard::eCHIP_ID:
        mv_task_list[eCHIP_ID].flag.stop = true;
        break;
    case ITopBoard::eVERSION:
        mv_task_list[eVERSION].flag.stop = true;
        break;
        //    case ITopBoard::eMAP_DATA:
        //        switch (mst_task_status_->cur_step) {
        //        case EProcessStep::eMAP_DATA: mv_task_list[eMAP_DATA].flag.stop = true; break;
        //        case EProcessStep::eTEST: mv_task_list[eTEST].flag.stop = true; break;
        //        case EProcessStep::eRETEST: mv_task_list[eRETEST].flag.stop = true; break;
        //        case EProcessStep::eRETEST2: mv_task_list[eRETEST2].flag.stop = true; break;
        //        default: break;
        //        }
        //        break;
    default:
        break;
    }
}

inline void CLenDataMesOpt::sleepMs(uint16_t msec) {
    QTime dieTime = QTime::currentTime().addMSecs(msec);
    while (QTime::currentTime() < dieTime)
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
}

QString CLenDataMesOpt::sensorVersionParse(const QByteArray &version) {
    if (version.size() < 4)
        return "";

    QString tr_version = QString::number(version.at(0), 10) + "." + QString::number(version.at(1), 10) + "." + QString::number(version.at(2), 10) + "." +
                         QString::number(version.at(3), 10);
    return tr_version;
}

bool CLenDataMesOpt::checkVersion(const QString &version) {
    if (version != mst_iniConfig.version)
        return false;
    return true;
}

// bool CLenDataMesOpt::mesDataHandle(CLenSqlHandle::StResultToMes *st_mes_data_) {
//    st_mes_data_->date = QString(QDateTime::currentDateTime().toString("yyyy/MM/dd"));

//    st_mes_data_->time = QDateTime::currentDateTime().time().msecsSinceStartOfDay() / 1000; //s
//    st_mes_data_->rslt = (mst_result_->finalResult.result == 0)?true:false; //(!mst_result_->finalResult.result) == true;
////    st_mes_data_->rslt_pre = ;
//    st_mes_data_->rsn_code = st_mes_data_->rslt == true?"PASS":"NG";

//    if(mst_result_->finalResult.map_matrix.size() > 3 && mst_result_->finalResult.map_matrix.at(0).size() > 3) {
//        st_mes_data_->param[0] = mst_result_->finalResult.map_matrix.at(1).at(1);
//        st_mes_data_->param[1] = mst_result_->finalResult.map_matrix.at(1).at(2);
//        st_mes_data_->param[2] = mst_result_->finalResult.map_matrix.at(1).at(3);
//        st_mes_data_->param[3] = mst_result_->finalResult.map_matrix.at(2).at(1);
//        st_mes_data_->param[4] = mst_result_->finalResult.map_matrix.at(2).at(2);
//        st_mes_data_->param[5] = mst_result_->finalResult.map_matrix.at(2).at(3);
//        st_mes_data_->param[6] = mst_result_->finalResult.map_matrix.at(3).at(1);
//        st_mes_data_->param[7] = mst_result_->finalResult.map_matrix.at(3).at(2);
//        st_mes_data_->param[8] = mst_result_->finalResult.map_matrix.at(3).at(3);
//    }
////    for(uint16_t for_i = 0; for_i < 9; for_i++) {
////        st_mes_data_->param[for_i] = 1000;
////    }

//    return true;
//}

// QString CLenDataMesOpt::errorHandleCallbackFunction(EError_type error_type, const uint16_t &error_code, uint8_t show_type) {
//    switch (error_type) {
//    case EError_type::ePROCESS_STEP_ERROR:
//        break;
//    case EError_type::eADJUST_STEP_ERROR:


//        break;
//    case EError_type::eMES_ERROR:
//        break;
//    default:
//        break;
//    }
//    return QString("process error");
//}

QString CLenDataMesOpt::errorInfoPack(EError_type error_type, const uint32_t &error_code) {
    QString   error_info;
    QMetaEnum stepEnum = QMetaEnum::fromType<CLenDataMesOpt::EProcessStep>();
    switch (error_type) {
    case EError_type::ePROCESS_STEP_ERROR:
        for (uint32_t for_i = 0; for_i <= (uint16_t)eCOMPLETE; for_i++) {
            uint32_t error_index = error_code & (1 << for_i);
            QString  step_str    = stepEnum.valueToKey(for_i);
            if (error_index != 0)
                error_info += "task: " + step_str + "fatal";
        }
        break;

    case EError_type::ePROCESS_ITEM_ERROR:
        //        error_info = "过程：";
        for (uint32_t for_i = 0; for_i < 32; for_i++) {
            uint32_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += QString::number(for_i, 10) + "." + mm_process_items_discribe[error_index];
        }
        break;

    case EError_type::eMES_ERROR:
        for (uint32_t for_i = 0; for_i < 16; for_i++) {
            uint16_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += "MES: " + QString::number(for_i, 10) + "." + CLenSqlHandle::mm_mes_error_discribe[error_index];
        }

        break;
    default:
        break;
    }

    return error_info;
}

EExecStatus CLenDataMesOpt::readyStep(void) {
    //* 端口信息变更，（不包括波特率）
    if (mi_icomm_->getPortName() != mst_config_->port_name) {
        if (mi_icomm_ != nullptr)
            delete mi_icomm_;
        mi_icomm_ = new CUART(mst_config_->port_name, mst_iniConfig.sensor_device_buad);

        mi_top_board_->icom_change_interface(mi_icomm_);
        //        m_serial_thread_->device_change_interface(mi_top_board_, nullptr);
    }

    //    if(mi_icomm_dev_->getPortName() != mst_config_->dev_port_name) {
    //        if(mi_icomm_dev_ != nullptr) delete mi_icomm_dev_;
    //        mi_icomm_dev_ = new CUART(mst_config_->dev_port_name, DEVICE_UART_BAUD);

    //        mi_clen_machine_->icom_change_interface(mi_icomm_dev_);
    //        m_serial_thread_->device_change_interface(nullptr, mi_clen_machine_);
    //    }

    //* 端口检测
    if (!mi_icomm_->openPort()) {
        mst_dds_->process_item_dds.all_error.serial_open = 1;
        emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        return eFATAL;
    }

    //* varibles init
    varibleInit();


    //* 接收线程
    emit subThreadSignal(true);
    emit readySignal(true);

    return eCOMP;
}

EExecStatus CLenDataMesOpt::readyAck(void) {
    return eCOMP;
}

/**
 * @brief 任务开始:抓取镜片
 * @return
 */
EExecStatus CLenDataMesOpt::startStep(void) {
    //* 获取工单号信息
    mst_work_order_->wo_lot = mst_config_->work_number;  // mst_iniConfig.work_number;
    mst_dds_->mes_dds       = mc_sql_handle_->checkWorkOrder(mst_work_order_);

    if (mst_dds_->mes_dds != 0) {
        QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
        emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
        return eFATAL;
    }
    return eCOMP;
}

/**
 * @brief 开始指令反馈
 */
EExecStatus CLenDataMesOpt::startAck(void) {
}


EExecStatus CLenDataMesOpt::calibMode(void) {
    if (mi_top_board_->unlockModule() && mi_top_board_->modeChange(CSensorCoinD::kTofCalibrationMode)) {
        m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}


EExecStatus CLenDataMesOpt::calibModeAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        //      m_serial_thread_->task_id_change(4);
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}


EExecStatus CLenDataMesOpt::chipIdStep(void) {
    //* 校正模式
    if (!mi_top_board_->readInfo(CSensorCoinD::kMcuId, 0))
        return eFATAL;
    else {
        m_serial_thread_->task_id_change(3);
        return eOK;
    }
}


EExecStatus CLenDataMesOpt::chipIdAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        mst_result_->chip_id = m_origin_bytes.toHex().toUpper();
        m_origin_bytes.clear();

        if (mst_result_->chip_id == "")
            mst_dds_->process_item_dds.all_error.chip_id = 1;
        QString info = QString("chip id:" + mst_result_->chip_id + errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        emit    moduleInfoShowSignal(mst_dds_->process_item_dds.all_error.chip_id, info);

        mst_xpid_det_->mcuid = mst_result_->chip_id;
        //        mst_dds_->process_item_dds.all_error.chip_id = 0;

        if (mst_dds_->process_item_dds.all_error.chip_id)
            return eFATAL;
        else {
            //****************************** MES 查询旧数据 *****************
            //* mes 获取旧标签号
            mst_dds_->mes_dds |= mc_sql_handle_->searchTagNumberByMCUID(*mst_xpid_det_, mst_mes_xsub2_data_->nbr_pre);
            if (mst_dds_->mes_dds != 0) {
                emit moduleInfoShowSignal(true, errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds));
                return eFATAL;
            } else {
                mst_mes_xsub2_data_->nbr = mst_mes_xsub2_data_->nbr_pre;

                //* mes 获取旧数据
                mst_dds_->mes_dds |= mc_sql_handle_->loadMesData(mst_mes_xsub2_data_);
                if (mst_dds_->mes_dds != 0) {
                    emit moduleInfoShowSignal(true, errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds));
                    return eFATAL;
                }
                mesDataShow("origin mes data: ");

                return eCOMP;
            }
        }
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

EExecStatus CLenDataMesOpt::sensorVersionStep(void) {
    //* 校正模式
    if (!mi_top_board_->readInfo(CSensorCoinD::kVersionBuad, 0))
        return eFATAL;
    else {
        m_serial_thread_->task_id_change(3);
        return eOK;
    }
}


EExecStatus CLenDataMesOpt::sensorVersionAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        mst_result_->sensor_version = sensorVersionParse(m_origin_bytes);
        m_origin_bytes.clear();

        if (!checkVersion(mst_result_->sensor_version))
            mst_dds_->process_item_dds.all_error.version = 1;  //版本异常如何处理
                                                               //        mst_dds_->process_item_dds.all_error.version = 0;
        QString info =
            QString("version: " + mst_result_->sensor_version + "\n" + errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        emit moduleInfoShowSignal(mst_dds_->process_item_dds.all_error.version, info);

        //* mes connect
        // check work_number status(工单状态)->生成新的工单号
        //        qDebug() << "-i clen data mes/ new nbr flag" << mst_config_->nbr_flag;
        if (mst_config_->nbr_flag) {
            mst_mes_xsub2_data_->nbr  = "";
            mst_xpid_det_->work_order = mst_work_order_->wo_lot;
            mst_xpid_det_->domain     = mst_work_order_->domain;

            mst_dds_->mes_dds |= mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, true);
            if (mst_dds_->mes_dds != 0) {
                emit moduleInfoShowSignal(true, errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds));
                LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("mes dds: %1").arg(QString::number(mst_dds_->mes_dds, 16)));
                return eFATAL;
            }
        } else {
            mst_mes_xsub2_data_->nbr = mst_mes_xsub2_data_->nbr_pre;
        }
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief greyMapMode
 * @return
 */
EExecStatus CLenDataMesOpt::greyMapMode(void) {
    //* 光斑模式
    if (mi_top_board_->unlockModule() && mi_top_board_->modeChange(CSensorCoinD::kTofFaculaMode)) {
        m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}

/**
 * @brief greyMapModeAck
 * @return
 */
EExecStatus CLenDataMesOpt::greyMapModeAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief CLenDataMesOpt::compStep
 */
EExecStatus CLenDataMesOpt::compStep(void) {
    QByteArray array;
    return eCOMP;
}

/**
 * @brief 结束
 * @return
 */
EExecStatus CLenDataMesOpt::compAck(void) {
    QString     facula;
    static uint delay_cnt = 1000;
    if (!mst_config_->is_button_close && mst_dds_->mes_dds == 0 && mst_dds_->process_item_dds.errors == 0 && mst_dds_->process_step_dds == 0) {
        //* 延迟等待新标签生成
        if (mst_config_->nbr_flag) {
            while (delay_cnt--) {
                mv_task_list[CLenDataMesOpt::eCOMPLETE].flag.stop = true;
                return eWAIT;
            }
            delay_cnt = 1000;
            //            mst_mes_xsub2_data_->nbr = "";
        }

        //******************************************* save data *************************
        //* some data update
        mst_mes_xsub2_data_->date = QString(QDateTime::currentDateTime().toString("yyyy/MM/dd"));
        mst_mes_xsub2_data_->time = QDateTime::currentDateTime().time().msecsSinceStartOfDay() / 1000;  // s

        mst_dds_->mes_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);
        mesDataShow("new mes data: ");

        if (mst_dds_->mes_dds != 0) {
            QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
            emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
        } else {
            emit moduleInfoShowSignal(false, "\n数据录入完毕");
        }
        //        qDebug() << "-i clens data mes/ mes save code:" << mst_dds_->mes_dds;
    } else {
        // qDebug() << "-i clens data mes/ item errors:" << mst_dds_->process_item_dds.errors << "process errors:" << mst_dds_->process_step_dds
        //          << mst_dds_->process_item_dds.errors << mst_config_->is_button_close;

        LOG_INFO(MyLogger::LogType::ERROR_LOG,
                 QString("process item errors: %1, process errors: %2, mes errors: %3, button status: %4")
                     .arg(mst_dds_->process_item_dds.errors)
                     .arg(mst_dds_->process_step_dds)
                     .arg(mst_dds_->mes_dds)
                     .arg(mst_config_->is_button_close));
    }

    //* close
    m_serial_thread_->task_id_change(0);
    mi_icomm_->closePort();  // port close

    //    emit subThreadSignal(false); //子线程退出
    emit compAckSignal(true);
    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    return eCOMP;
}
