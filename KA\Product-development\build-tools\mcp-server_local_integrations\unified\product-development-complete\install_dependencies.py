#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCP服务器依赖安装脚本

自动安装产品开发完整功能集成MCP服务器所需的所有依赖包。
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"🔄 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        print(f"✅ {description} - 成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败")
        print(f"错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {description} - 异常: {str(e)}")
        return False


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def install_mcp_package():
    """安装MCP包"""
    print("\n📦 安装MCP协议包...")
    
    # 尝试多种安装方法
    install_methods = [
        ("pip install mcp", "使用pip安装MCP包"),
        ("pip install --upgrade mcp", "升级安装MCP包"),
        ("python -m pip install mcp", "使用python -m pip安装"),
        ("pip install git+https://github.com/modelcontextprotocol/python-sdk.git", "从GitHub安装最新版本")
    ]
    
    for command, description in install_methods:
        if run_command(command, description):
            return True
        print("⚠️ 尝试下一种安装方法...\n")
    
    print("❌ 所有MCP安装方法都失败了")
    return False


def install_basic_dependencies():
    """安装基础依赖"""
    print("\n📦 安装基础依赖包...")
    
    basic_packages = [
        "PyYAML>=6.0",
        "jsonschema>=4.0.0", 
        "pathlib2>=2.3.7",
        "asyncio",
        "typing-extensions"
    ]
    
    success_count = 0
    for package in basic_packages:
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
    
    print(f"✅ 基础依赖安装完成: {success_count}/{len(basic_packages)}")
    return success_count == len(basic_packages)


def install_optional_dependencies():
    """安装可选依赖"""
    print("\n📦 安装可选依赖包...")
    
    optional_packages = [
        "markdown>=3.4.0",
        "python-docx>=0.8.11", 
        "openpyxl>=3.1.0",
        "matplotlib>=3.7.0",
        "pandas>=2.0.0",
        "requests>=2.31.0"
    ]
    
    success_count = 0
    for package in optional_packages:
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
        else:
            print(f"⚠️ 可选依赖 {package} 安装失败，继续...")
    
    print(f"✅ 可选依赖安装完成: {success_count}/{len(optional_packages)}")
    return True


def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    try:
        # 测试导入MCP
        import mcp
        print("✅ MCP包导入成功")
        
        # 测试导入FastMCP
        from mcp.server.fastmcp import FastMCP
        print("✅ FastMCP导入成功")
        
        # 测试其他关键包
        import yaml
        import json
        import asyncio
        print("✅ 基础依赖导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入测试失败: {str(e)}")
        return False


def create_test_server():
    """创建测试服务器"""
    print("\n🧪 创建测试服务器...")
    
    test_code = '''#!/usr/bin/env python3
"""简单的MCP服务器测试"""

import asyncio
from mcp.server.fastmcp import FastMCP

# 创建MCP服务器
mcp = FastMCP("Test Server")

@mcp.tool()
def test_tool() -> str:
    """测试工具函数"""
    return "MCP服务器工作正常！"

async def main():
    print("MCP服务器测试启动...")
    print("如果看到这条消息，说明MCP包安装成功！")
    return True

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    test_file = Path("test_mcp_server.py")
    try:
        test_file.write_text(test_code, encoding='utf-8')
        print(f"✅ 测试服务器创建成功: {test_file}")
        
        # 运行测试
        if run_command("python test_mcp_server.py", "运行MCP服务器测试"):
            print("✅ MCP服务器测试通过")
            return True
        else:
            print("❌ MCP服务器测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试服务器失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 产品开发完整功能集成MCP服务器依赖安装")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装MCP包
    if not install_mcp_package():
        print("\n❌ MCP包安装失败，请手动安装:")
        print("1. pip install mcp")
        print("2. 或访问: https://github.com/modelcontextprotocol/python-sdk")
        sys.exit(1)
    
    # 安装基础依赖
    install_basic_dependencies()
    
    # 安装可选依赖
    install_optional_dependencies()
    
    # 验证安装
    if verify_installation():
        print("\n🎉 所有依赖安装成功！")
        
        # 创建并测试服务器
        if create_test_server():
            print("\n✅ MCP服务器可以正常启动")
        
        print("\n📋 下一步:")
        print("1. 配置Cursor的MCP设置")
        print("2. 重启Cursor IDE")
        print("3. 测试MCP服务器功能")
        
        return 0
    else:
        print("\n❌ 依赖验证失败，请检查安装")
        return 1


if __name__ == "__main__":
    sys.exit(main())
