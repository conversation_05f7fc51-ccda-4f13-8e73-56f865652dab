#!/usr/bin/env python3
"""
Product Development MCP Server

提供产品开发过程中的所有脚本和工具功能，包括：
- 项目初始化
- Canvas自动同步
- 文档关联管理
- 信息追溯系统
- 工作流管理

使用方法：
1. 启动server: python product_development_server.py
2. 在项目中创建mcp.json配置文件
3. 通过MCP客户端调用各种功能
"""

import asyncio
import json
import subprocess
import sys
import os
from pathlib import Path
from typing import Any, Dict, List, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductDevelopmentMCPServer:
    """产品开发MCP服务器"""
    
    def __init__(self, scripts_path: str):
        self.scripts_path = Path(scripts_path)
        self.running = False
        
    async def start_server(self, host: str = "localhost", port: int = 3001):
        """启动MCP服务器"""
        logger.info(f"启动产品开发MCP服务器在 {host}:{port}")
        self.running = True
        
        # 这里实现MCP协议的服务器逻辑
        # 为了演示，我们使用简单的HTTP服务器
        await self._start_http_server(host, port)
    
    async def _start_http_server(self, host: str, port: int):
        """启动HTTP服务器"""
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import threading
        
        class MCPRequestHandler(BaseHTTPRequestHandler):
            def __init__(self, server_instance, *args, **kwargs):
                self.server_instance = server_instance
                super().__init__(*args, **kwargs)
                
            def do_GET(self):
                """处理GET请求"""
                if self.path == '/':
                    self._send_html_response(self._get_main_page())
                elif self.path == '/api/tools':
                    self._send_json_response(self.server_instance.get_available_tools())
                elif self.path == '/api/status':
                    self._send_json_response({"status": "running", "version": "1.0.0"})
                else:
                    self._send_error_response(404, "Not Found")
                    
            def do_POST(self):
                """处理POST请求"""
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length).decode('utf-8')
                
                try:
                    request_data = json.loads(post_data)
                    result = self.server_instance.handle_tool_call(request_data)
                    self._send_json_response(result)
                except Exception as e:
                    logger.error(f"处理请求失败: {e}")
                    self._send_error_response(500, str(e))
                    
            def _send_json_response(self, data):
                """发送JSON响应"""
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))
                
            def _send_html_response(self, html):
                """发送HTML响应"""
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
                
            def _send_error_response(self, code, message):
                """发送错误响应"""
                self.send_response(code)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                error_data = {"error": message, "code": code}
                self.wfile.write(json.dumps(error_data).encode('utf-8'))
                
            def _get_main_page(self):
                """获取主页HTML"""
                return """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>产品开发MCP服务器</title>
                    <meta charset="utf-8">
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        .header { border-bottom: 2px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
                        .tool-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px; }
                        .tool-card { border: 1px solid #ddd; padding: 20px; border-radius: 6px; background: #fafafa; }
                        .tool-card h3 { margin-top: 0; color: #007acc; }
                        .btn { background-color: #007acc; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
                        .btn:hover { background-color: #005a9e; }
                        .status { background: #e8f5e8; padding: 10px; border-radius: 4px; margin-bottom: 20px; border-left: 4px solid #4caf50; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>🚀 产品开发MCP服务器</h1>
                            <p>统一管理产品开发过程中的所有脚本和工具</p>
                        </div>
                        
                        <div class="status">
                            <strong>✅ 服务器状态</strong>: 运行中 | <strong>版本</strong>: 1.0.0 | <strong>端口</strong>: 3001
                        </div>
                        
                        <h2>🛠️ 可用工具</h2>
                        <div class="tool-grid">
                            <div class="tool-card">
                                <h3>📁 项目初始化</h3>
                                <p>创建新的产品项目，包括目录结构、配置文件和基础文档</p>
                                <button class="btn" onclick="callTool('init_project')">初始化项目</button>
                            </div>
                            
                            <div class="tool-card">
                                <h3>🎨 Canvas同步</h3>
                                <p>INDEX文件与Obsidian Canvas的双向同步</p>
                                <button class="btn" onclick="callTool('sync_canvas')">同步Canvas</button>
                            </div>
                            
                            <div class="tool-card">
                                <h3>🔗 文档关联</h3>
                                <p>自动发现和管理文档间的语义关联关系</p>
                                <button class="btn" onclick="callTool('link_documents')">关联文档</button>
                            </div>
                            
                            <div class="tool-card">
                                <h3>📊 信息追溯</h3>
                                <p>建立从需求到交付物的完整追溯关系</p>
                                <button class="btn" onclick="callTool('trace_info')">信息追溯</button>
                            </div>
                            
                            <div class="tool-card">
                                <h3>⚡ 工作流管理</h3>
                                <p>管理产品开发工作流和任务进度</p>
                                <button class="btn" onclick="callTool('manage_workflow')">工作流管理</button>
                            </div>
                            
                            <div class="tool-card">
                                <h3>🔍 项目验证</h3>
                                <p>验证项目结构和配置的完整性</p>
                                <button class="btn" onclick="callTool('validate_project')">验证项目</button>
                            </div>
                        </div>
                        
                        <h2>📋 使用说明</h2>
                        <ol>
                            <li>在项目根目录创建 <code>mcp.json</code> 配置文件</li>
                            <li>配置MCP服务器地址: <code>http://localhost:3001</code></li>
                            <li>在Cursor或其他支持MCP的客户端中使用工具</li>
                        </ol>
                        
                        <h2>🔧 API端点</h2>
                        <ul>
                            <li><code>GET /api/tools</code> - 获取可用工具列表</li>
                            <li><code>GET /api/status</code> - 获取服务器状态</li>
                            <li><code>POST /api/call</code> - 调用工具功能</li>
                        </ul>
                    </div>
                    
                    <script>
                        function callTool(toolName) {
                            // 这里可以实现工具调用的前端逻辑
                            alert('工具调用: ' + toolName + '\\n请通过MCP客户端调用此功能');
                        }
                    </script>
                </body>
                </html>
                """
                
            def log_message(self, format, *args):
                """重写日志方法，减少输出"""
                pass
        
        # 创建服务器
        def create_handler(*args, **kwargs):
            return MCPRequestHandler(self, *args, **kwargs)
        
        httpd = HTTPServer((host, port), create_handler)
        logger.info(f"MCP服务器已启动: http://{host}:{port}")
        
        # 在新线程中运行服务器
        server_thread = threading.Thread(target=httpd.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        # 保持主线程运行
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("正在停止服务器...")
            httpd.shutdown()
            self.running = False

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return [
            {
                "name": "init_project",
                "description": "初始化新的产品项目",
                "parameters": {
                    "project_name": {"type": "string", "description": "项目名称"},
                    "project_path": {"type": "string", "description": "项目路径", "default": "."},
                    "structure_type": {"type": "string", "description": "结构类型", "enum": ["single_layer", "multi_level"], "default": "single_layer"}
                }
            },
            {
                "name": "sync_canvas",
                "description": "同步INDEX文件到Canvas",
                "parameters": {
                    "project_path": {"type": "string", "description": "项目路径", "default": "."},
                    "mode": {"type": "string", "description": "同步模式", "enum": ["incremental", "full"], "default": "incremental"}
                }
            },
            {
                "name": "sync_canvas_to_index",
                "description": "同步Canvas到INDEX文件",
                "parameters": {
                    "project_path": {"type": "string", "description": "项目路径", "default": "."}
                }
            },
            {
                "name": "link_documents",
                "description": "管理文档关联",
                "parameters": {
                    "project_path": {"type": "string", "description": "项目路径", "default": "."},
                    "action": {"type": "string", "description": "操作类型", "enum": ["register", "report", "validate"], "default": "register"}
                }
            },
            {
                "name": "trace_info",
                "description": "信息追溯管理",
                "parameters": {
                    "project_path": {"type": "string", "description": "项目路径", "default": "."},
                    "action": {"type": "string", "description": "操作类型", "enum": ["scan", "report", "validate"], "default": "scan"}
                }
            },
            {
                "name": "validate_project",
                "description": "验证项目完整性",
                "parameters": {
                    "project_path": {"type": "string", "description": "项目路径", "default": "."}
                }
            },
            {
                "name": "start_canvas_watcher",
                "description": "启动Canvas自动监控",
                "parameters": {
                    "project_path": {"type": "string", "description": "项目路径", "default": "."},
                    "daemon": {"type": "boolean", "description": "后台运行", "default": false}
                }
            }
        ]
    
    def handle_tool_call(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具调用请求"""
        tool_name = request_data.get("tool")
        parameters = request_data.get("parameters", {})
        
        logger.info(f"调用工具: {tool_name}, 参数: {parameters}")
        
        try:
            if tool_name == "init_project":
                return self._init_project(parameters)
            elif tool_name == "sync_canvas":
                return self._sync_canvas(parameters)
            elif tool_name == "sync_canvas_to_index":
                return self._sync_canvas_to_index(parameters)
            elif tool_name == "link_documents":
                return self._link_documents(parameters)
            elif tool_name == "trace_info":
                return self._trace_info(parameters)
            elif tool_name == "validate_project":
                return self._validate_project(parameters)
            elif tool_name == "start_canvas_watcher":
                return self._start_canvas_watcher(parameters)
            else:
                return {"success": False, "error": f"未知工具: {tool_name}"}
                
        except Exception as e:
            logger.error(f"工具调用失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _run_script(self, script_path: str, args: List[str]) -> Dict[str, Any]:
        """运行脚本"""
        full_script_path = self.scripts_path / script_path
        cmd = ["python", str(full_script_path)] + args
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                encoding='utf-8',
                cwd=str(self.scripts_path.parent)
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _init_project(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """初始化项目"""
        project_name = params.get("project_name", "new_project")
        project_path = params.get("project_path", ".")
        structure_type = params.get("structure_type", "single_layer")
        
        args = [
            "--project_name", project_name,
            "--project_path", project_path,
            "--structure_type", structure_type
        ]
        
        result = self._run_script("init_product_project.py", args)
        
        if result["success"]:
            result["message"] = f"项目 {project_name} 初始化成功"
        else:
            result["message"] = f"项目初始化失败: {result.get('stderr', '未知错误')}"
            
        return result
    
    def _sync_canvas(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """同步INDEX到Canvas"""
        project_path = params.get("project_path", ".")
        mode = params.get("mode", "incremental")
        
        args = ["--project-path", project_path, "--sync-to-canvas"]
        if mode == "full":
            args.extend(["--mode", "full"])
            
        result = self._run_script("canvas/auto_link_documents.py", args)
        
        if result["success"]:
            result["message"] = "Canvas同步成功"
        else:
            result["message"] = f"Canvas同步失败: {result.get('stderr', '未知错误')}"
            
        return result
    
    def _sync_canvas_to_index(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """同步Canvas到INDEX"""
        project_path = params.get("project_path", ".")
        
        args = ["--project-path", project_path, "--sync-from-canvas"]
        result = self._run_script("canvas/auto_link_documents.py", args)
        
        if result["success"]:
            result["message"] = "Canvas到INDEX同步成功"
        else:
            result["message"] = f"Canvas到INDEX同步失败: {result.get('stderr', '未知错误')}"
            
        return result
    
    def _link_documents(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """文档关联管理"""
        project_path = params.get("project_path", ".")
        action = params.get("action", "register")
        
        args = ["--project-path", project_path]
        
        if action == "register":
            args.extend(["--register", "--all"])
        elif action == "report":
            args.append("--report")
        elif action == "validate":
            args.append("--validate")
            
        result = self._run_script("links/auto_link_documents.py", args)
        
        if result["success"]:
            result["message"] = f"文档关联{action}完成"
        else:
            result["message"] = f"文档关联{action}失败: {result.get('stderr', '未知错误')}"
            
        return result
    
    def _trace_info(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """信息追溯管理"""
        project_path = params.get("project_path", ".")
        action = params.get("action", "scan")
        
        args = ["--project-path", project_path]
        
        if action == "scan":
            args.extend(["--scan", "--all"])
        elif action == "report":
            args.append("--report")
        elif action == "validate":
            args.append("--validate")
            
        result = self._run_script("infoTrace/auto_index_manager.py", args)
        
        if result["success"]:
            result["message"] = f"信息追溯{action}完成"
        else:
            result["message"] = f"信息追溯{action}失败: {result.get('stderr', '未知错误')}"
            
        return result
    
    def _validate_project(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证项目完整性"""
        project_path = params.get("project_path", ".")
        
        # 运行多个验证脚本
        results = []
        
        # 验证文档关联
        result1 = self._run_script("links/auto_link_documents.py", ["--project-path", project_path, "--validate"])
        results.append(("文档关联系统", result1))
        
        # 验证Canvas同步
        result2 = self._run_script("canvas/auto_link_documents.py", ["--project-path", project_path, "--validate-sync"])
        results.append(("Canvas同步系统", result2))
        
        # 验证信息追溯
        result3 = self._run_script("infoTrace/auto_index_manager.py", ["--project-path", project_path, "--validate"])
        results.append(("信息追溯系统", result3))
        
        # 汇总结果
        all_success = all(result[1]["success"] for result in results)
        
        validation_report = []
        for name, result in results:
            status = "✅ 通过" if result["success"] else "❌ 失败"
            validation_report.append(f"{name}: {status}")
            if not result["success"]:
                validation_report.append(f"  错误: {result.get('stderr', '未知错误')}")
        
        return {
            "success": all_success,
            "message": "项目验证完成",
            "validation_report": "\n".join(validation_report),
            "details": results
        }
    
    def _start_canvas_watcher(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """启动Canvas监控"""
        project_path = params.get("project_path", ".")
        daemon = params.get("daemon", False)
        
        args = ["--project-path", project_path]
        if daemon:
            args.append("--daemon")
            
        # 对于监控器，我们不等待完成，而是启动后台进程
        try:
            full_script_path = self.scripts_path / "canvas/canvas_watcher.py"
            cmd = ["python", str(full_script_path)] + args
            
            if daemon:
                # 后台启动
                subprocess.Popen(
                    cmd,
                    cwd=str(self.scripts_path.parent),
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )
                return {
                    "success": True,
                    "message": "Canvas监控器已在后台启动"
                }
            else:
                # 前台启动（返回启动信息）
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=5,  # 5秒超时
                    cwd=str(self.scripts_path.parent)
                )
                return {
                    "success": True,
                    "message": "Canvas监控器已启动",
                    "output": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": True,
                "message": "Canvas监控器已启动（超时但正常）"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"启动Canvas监控器失败: {str(e)}"
            }


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python product_development_server.py <scripts_path> [host] [port]")
        print("示例: python product_development_server.py /path/to/scripts localhost 3001")
        sys.exit(1)
    
    scripts_path = sys.argv[1]
    host = sys.argv[2] if len(sys.argv) > 2 else "localhost"
    port = int(sys.argv[3]) if len(sys.argv) > 3 else 3001
    
    server = ProductDevelopmentMCPServer(scripts_path)
    await server.start_server(host, port)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器已停止") 