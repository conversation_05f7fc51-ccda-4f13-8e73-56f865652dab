#ifndef  _NOVA_P_H_
#define  _NOVA_P_H_

#include <QByteArray>
#include "IProtocol.h"
#include <QVector>


class CNovaP:public IProtocol
{
public:
    CNovaP();
    ~CNovaP();

    //*协议cmd枚举
    enum class EInteractionFrame {
        eHEADER     = 0xA53E,
        eHEADER_LEN = 0x06,
    };

    //* data info frame
    enum class EDataInfoFrame {
        eHEADER     = 0x55A5,
        eHEADER_LEN = 0x05,
    };

    //* dds log frame
    enum class ELogFrame {
        eHEADER     = 0xA55A,
        eHEADER_LEN = 0x06,
    };

    /*协议cmd枚举*/
    enum class ECmd{
        eH2D        = 0<<0, //host->device
        eD2H        = 1<<0, //device->host

        eCMD        = 0<<2 | 0<<1, //cmd
        eACK        = 0<<2 | 1<<1, //ack
//        eINFO       = 1<<2 | 0<<1, //info
//        eDATA       = 1<<2 | 1<<1, //data

        eW          = 0<<3, //write
        eR          = 1<<3, //read
    };

    enum class EFunctionCode {
        eSINGLE_ACK         = 0x06,
        eAUTO_ACK           = 0x10,
    };

#define DATA_CACHE          20
    typedef struct {
        uint16_t            header;
        uint8_t             cmd;
        uint8_t             id;
        uint8_t             num;
        QVector<uint8_t>    data; //LSB
        uint8_t             check_sum;
    } StInteractionFrame;

//    typedef struct {
//        uint16_t            header;
//        uint8_t             cmd;
//        uint8_t             id;
//        uint8_t             checkXor;
//        uint8_t             num;
//        uint8_t             data[DATA_CACHE]; //LSB
//    } StInteractionFrameRec;
#pragma pack(1)
    typedef struct {
      uint16_t              header; //帧头
      uint8_t               addr; //指令id
      uint8_t               num;
      uint8_t               signal;
      uint16_t              distance;
      uint16_t              ref_dist;
      uint8_t               check_sum;
    } StDataFrame;

    typedef struct {
      uint16_t              header; //帧头
      uint8_t               addr; //指令id
      uint8_t               num;
      uint8_t               signal;
      uint16_t              distance;
      QVector<uint32_t>     measure_info;
      uint8_t               check_sum;
    } StDataInfoFrame;

    typedef struct {
      uint16_t              header; //帧头
      uint8_t               addr; //指令id
      uint8_t               num;
      uint16_t              reserve;
      EFunctionCode         function_code;
      uint8_t               error_code;
      uint8_t               check_sum;
    } StDdsFrame;

    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

private:
    StInteractionFrame* mst_interaction_frame_ = nullptr;
    StDataInfoFrame* mst_data_info_frame_ = nullptr;
    StDdsFrame* mst_dds_frame_ = nullptr;
};


#endif
