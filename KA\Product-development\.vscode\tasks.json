{"version": "2.0.0", "tasks": [{"label": "生成项目进度仪表板", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/workflow/generate_progress_dashboard.py", "--workspace=${workspaceFolder}", "--config=${workspaceFolder}/config/phases.json", "--output=${workspaceFolder}/reports"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "生成项目流程图", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/workflow/generate_workflow_diagram.py", "--workspace=${workspaceFolder}", "--output=${workspaceFolder}/reports"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "启动可视化Web服务器", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/visualization/quickviz.py", "${input:projectPath}", "--mode=${input:visualizationMode}", "--port=${input:serverPort}", "--verbose"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "生成静态可视化图片", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/visualization/quickviz.py", "${input:projectPath}", "--mode=${input:visualizationMode}", "--output=${workspaceFolder}/reports/visualization.png", "--format=png"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "API模式可视化数据提取", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/visualization/quickviz.py", "${input:projectPath}", "--mode=${input:visualizationMode}", "--api-only", "--verbose"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "一键生成所有可视化", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/workflow/generate_progress_dashboard.py", "--workspace=${workspaceFolder}", "--config=${workspaceFolder}/config/phases.json", "--output=${workspaceFolder}/reports", "&&", "python", "${workspaceFolder}/scripts/workflow/generate_workflow_diagram.py", "--workspace=${workspaceFolder}", "--output=${workspaceFolder}/reports"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": [], "group": {"kind": "build", "isDefault": true}}, {"label": "生成产品结构图", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/project_initialization/visualize_product_structure.py", "--config=${input:multilevelConfigPath}", "--output=${workspaceFolder}/reports/product_structure.png", "--theme=${input:visualTheme}"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "生成高亮产品结构图", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/project_initialization/visualize_product_structure.py", "--config=${input:multilevelConfigPath}", "--output=${workspaceFolder}/reports/highlighted_product.png", "--theme=${input:visualTheme}", "--highlight=${input:highlightInstance}"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "生成矢量格式产品结构图", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/project_initialization/visualize_product_structure.py", "--config=${input:multilevelConfigPath}", "--output=${workspaceFolder}/reports/product_structure.svg", "--theme=${input:visualTheme}", "--format=svg"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "一键生成产品结构图与需求矩阵", "type": "shell", "command": "python", "args": ["${workspaceFolder}/scripts/project_initialization/visualize_product_structure.py", "--config=${input:multilevelConfigPath}", "--output=${workspaceFolder}/reports/product_structure.png", "--theme=${input:visualTheme}", "&&", "python", "${workspaceFolder}/scripts/requirements/generate_requirements_summary.py", "--config=${input:multilevelConfigPath}", "--output=${workspaceFolder}/reports"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}], "inputs": [{"id": "projectPath", "description": "项目路径", "default": "${workspaceFolder}", "type": "promptString"}, {"id": "visualizationMode", "description": "可视化模式", "default": "all", "options": ["workflow", "documents", "traceability", "progress", "all"], "type": "pickString"}, {"id": "serverPort", "description": "Web服务器端口", "default": "8080", "type": "promptString"}, {"id": "multilevelConfigPath", "description": "多层级配置文件路径", "default": "${workspaceFolder}/__level_config.json", "type": "promptString"}, {"id": "visualTheme", "description": "可视化主题", "default": "default", "options": ["default", "light", "dark", "colorful"], "type": "pickString"}, {"id": "highlightInstance", "description": "要高亮显示的实例名称", "default": "", "type": "promptString"}]}