#ifndef _CLENS_MACHINE_ST_H_
#define _CLENS_MACHINE_ST_H_


#include "IComm.h"
#include "ILoad.h"
#include "IclensMachine.h"
#include "processListA.h"
#include <QMap>
#include <QVector>


class CClensMachineST : public IClensMachine {
    Q_OBJECT
  public:
    CClensMachineST(IComm *port_, const uint8_t &device);  //传入设备地址
    ~CClensMachineST();

    virtual void                          cleanMoveDistance() override;
    virtual C3dHandMachine::St3D<int16_t> getMoveStep(void) override;
    virtual C3dHandMachine::St3D<int16_t> getMoveDist(void) override;
    virtual uint8_t                       deviceSpeedHandle(const C3dHandMachine::St3D<int16_t> &move_step, uint16_t &wait_times) override;

    void       icom_change_interface(IComm *port_) override;
    QByteArray portDataRead() override;
    bool       start(const QVector<uint16_t> &data) override;  //开始
    bool       stop(const QByteArray &data) override;          //停止
    bool       getStatus(void) override;
    bool       getLoc(const uint8_t &dimension) override;
    bool       autoAdjust(const QVector<uint16_t> &data) override;
    bool       getAutoAdjustStatus(void) override;  //获取机台粗调状态
    bool       adjust(const St3D<int16_t> &move_dis) override;
    bool       getMoveStatus(void) override;
    bool       solid(void) override;
    bool       deflate() override;

    bool dataParsing(QByteArray str, const int &length) override;

  signals:
    void dataOutput(IClensMachine::ECommAck step, ECommStatus status, QByteArray bytes);
    void floatDataOutput(IClensMachine::ECommAck step, ECommStatus status, QVector<float> buff);

  private:
    enum EWordRegAddr {
        platformA_time   = 634,    //平台A工作计时
        platformB_time   = 636,    //平台B工作计时
        platformA_status = 638,    //平台A状态
        platformB_status = 639,    //平台B状态
        x_coordinates    = 10000,  //
        z_coordinates    = 10002,  //
        y1_coordinates   = 10004,  //
        y2_coordinates   = 10006,
        NG_num           = 1276,  // R
        OK_num           = 1278,  // R
    };

    enum ECoilRegAddr {
        test_mode_change = 51,
        anto_mode_change = 52,
    };

    enum ECmdDataAddr {
        Hmi_show_add  = 600,  //地址偏移
        Hmi_ack_flag  = 680,  //动作标识 1-
        Hmi_ack_start = 681,  //发送非零值->启动
        Hmi_ack_posX  = 682,  //位移X轴数据
        Hmi_ack_posY  = 684,  //位移Y轴数据
        Hmi_ack_posZ  = 686,  //位移Z轴数据
        Hmi_ack_spd   = 688,  //轴位移
    };

    /**
     * @brief
     * 青盒状态位
     * D680.0 X轴移动标志
     * D680.1 Y轴移动标志
     * D680.2 Z轴移动标志
     * D680.3 固化标志
     * D680.4 关闭真空
     * D680.5 测试NG
     */
    enum EStatusFlag {
        eLEN_CAP      = 0x0001,  //启动，非零值
        eLEN_UNCAPPED = 0x0000,  //未抓取完毕
        eLEN_CAPPED   = 0x8000,  //镜片抓取完毕
        eX_AXIS       = 0x0001,  // x轴
        eY_AXIS       = 0x0002,  // y轴
        eZ_AXIS       = 0x0004,  // z轴
        eTHREE_AXIS   = (eX_AXIS | eY_AXIS | eZ_AXIS),
        eSOLID        = 0x0008,  //
        eUNSOLIDDED   = 0x0008,  //未固化完毕
        eSOLIDDED     = 0x0000,
        eDEFLATE      = 0x0010,  //
        eRESULT_LIGHT = 0x0020,  // qinghe, 结果灯控制，
        eDEVICE_EXIT  = 0x8000,  //退出
    };

    uint8_t            m_device_addr;
    IComm *            im_port_ = nullptr;
    ILoad *            mi_load_ = nullptr;
    QMap<QString, int> m_xml_param;

    //*
    //  const St3D<uint16_t> mst_limit_radius = {1300, 1300, 1000};

    void                 cmd_init(void);
    inline void          sleepMs(uint16_t msec);                                           // inline
    inline St3D<int16_t> move_dis_cal(const int8_t &mp_delta, const int16_t &peak_delta);  //, const uint8_t &move_step
    inline int16_t       distance_limit(int16_t *delta, const uint16_t &limit, const int16_t &dis);
};


#endif
