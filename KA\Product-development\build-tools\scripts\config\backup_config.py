#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置文件备份器

专门负责配置文件的备份功能。
遵循单一职责原则，只负责配置文件的备份操作。
支持时间戳命名、版本控制、压缩等功能。
"""

import json
import yaml
import argparse
import sys
import shutil
import gzip
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional


def backup_config(
    config_path: str,
    backup_dir: Optional[str] = None,
    backup_format: str = "timestamped",
    compress: bool = False
) -> Dict[str, Any]:
    """
    备份配置文件
    
    Args:
        config_path: 要备份的配置文件路径
        backup_dir: 备份目录，如果不指定则在原文件目录创建backup子目录
        backup_format: 备份格式 (timestamped, numbered, simple)
        compress: 是否压缩备份文件
    
    Returns:
        Dict: 包含备份结果的字典
    """
    try:
        config_file = Path(config_path)
        
        if not config_file.exists():
            return {
                "success": False,
                "error": f"配置文件不存在: {config_path}",
                "backup_path": None,
                "backup_size": 0
            }
        
        # 确定备份目录
        if backup_dir:
            backup_directory = Path(backup_dir)
        else:
            backup_directory = config_file.parent / "backup"
        
        backup_directory.mkdir(parents=True, exist_ok=True)
        
        # 生成备份文件名
        backup_filename = generate_backup_filename(
            config_file.name, 
            backup_format, 
            backup_directory
        )
        
        backup_path = backup_directory / backup_filename
        
        # 执行备份
        if compress:
            backup_path = backup_path.with_suffix(backup_path.suffix + '.gz')
            with open(config_file, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
        else:
            shutil.copy2(config_file, backup_path)
        
        # 获取备份文件大小
        backup_size = backup_path.stat().st_size
        
        return {
            "success": True,
            "message": f"配置文件备份成功",
            "original_path": str(config_file),
            "backup_path": str(backup_path),
            "backup_size": backup_size,
            "backup_format": backup_format,
            "compressed": compress,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"备份失败: {str(e)}",
            "backup_path": None,
            "backup_size": 0
        }


def generate_backup_filename(
    original_name: str, 
    backup_format: str, 
    backup_dir: Path
) -> str:
    """
    生成备份文件名
    
    Args:
        original_name: 原始文件名
        backup_format: 备份格式
        backup_dir: 备份目录
    
    Returns:
        str: 备份文件名
    """
    name_parts = Path(original_name).stem, Path(original_name).suffix
    
    if backup_format == "timestamped":
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{name_parts[0]}_backup_{timestamp}{name_parts[1]}"
    
    elif backup_format == "numbered":
        # 查找现有备份文件的最大编号
        existing_backups = list(backup_dir.glob(f"{name_parts[0]}_backup_*{name_parts[1]}"))
        max_num = 0
        for backup in existing_backups:
            try:
                num_str = backup.stem.split('_backup_')[1]
                if num_str.isdigit():
                    max_num = max(max_num, int(num_str))
            except (IndexError, ValueError):
                continue
        
        return f"{name_parts[0]}_backup_{max_num + 1:03d}{name_parts[1]}"
    
    elif backup_format == "simple":
        return f"{name_parts[0]}_backup{name_parts[1]}"
    
    else:
        # 默认使用时间戳格式
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{name_parts[0]}_backup_{timestamp}{name_parts[1]}"


def cleanup_old_backups(
    backup_dir: Path, 
    pattern: str, 
    keep_count: int = 10
) -> Dict[str, Any]:
    """
    清理旧的备份文件，只保留最新的几个
    
    Args:
        backup_dir: 备份目录
        pattern: 文件匹配模式
        keep_count: 保留的备份文件数量
    
    Returns:
        Dict: 清理结果
    """
    try:
        backup_files = sorted(
            backup_dir.glob(pattern),
            key=lambda x: x.stat().st_mtime,
            reverse=True
        )
        
        if len(backup_files) <= keep_count:
            return {
                "success": True,
                "message": f"备份文件数量({len(backup_files)})未超过限制({keep_count})",
                "deleted_count": 0,
                "remaining_count": len(backup_files)
            }
        
        # 删除多余的备份文件
        files_to_delete = backup_files[keep_count:]
        deleted_count = 0
        
        for file_path in files_to_delete:
            try:
                file_path.unlink()
                deleted_count += 1
            except Exception as e:
                print(f"删除备份文件失败 {file_path}: {e}", file=sys.stderr)
        
        return {
            "success": True,
            "message": f"清理完成，删除了{deleted_count}个旧备份文件",
            "deleted_count": deleted_count,
            "remaining_count": len(backup_files) - deleted_count
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"清理备份文件失败: {str(e)}",
            "deleted_count": 0
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置文件备份工具")
    parser.add_argument("--config-file", required=True, help="要备份的配置文件路径")
    parser.add_argument("--backup-dir", help="备份目录路径")
    parser.add_argument("--backup-format", default="timestamped", 
                       choices=["timestamped", "numbered", "simple"],
                       help="备份文件命名格式")
    parser.add_argument("--compress", action="store_true", help="是否压缩备份文件")
    parser.add_argument("--cleanup", action="store_true", help="是否清理旧备份文件")
    parser.add_argument("--keep-count", type=int, default=10, help="保留的备份文件数量")
    parser.add_argument("--project-path", default=".", help="项目路径")
    
    args = parser.parse_args()
    
    # 执行备份
    result = backup_config(
        config_path=args.config_file,
        backup_dir=args.backup_dir,
        backup_format=args.backup_format,
        compress=args.compress
    )
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 如果备份成功且需要清理旧文件
    if result["success"] and args.cleanup:
        backup_path = Path(result["backup_path"])
        backup_dir = backup_path.parent
        
        # 生成清理模式
        original_name = Path(args.config_file).name
        name_parts = Path(original_name).stem, Path(original_name).suffix
        cleanup_pattern = f"{name_parts[0]}_backup_*{name_parts[1]}"
        
        cleanup_result = cleanup_old_backups(backup_dir, cleanup_pattern, args.keep_count)
        print("清理结果:", json.dumps(cleanup_result, ensure_ascii=False, indent=2))
    
    # 设置退出码
    sys.exit(0 if result["success"] else 1)


if __name__ == "__main__":
    main()
