#ifndef IMAGEPROCESSING_MEDIANFILTER_H
#define IMAGEPROCESSING_MEDIANFILTER_H

#include "../interfaces/IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QVector>
#include <QDebug>
#include <algorithm>

namespace ImageProcessing {

/**
 * @brief 中值滤波器实现
 * 
 * 实现中值滤波算法，有效去除椒盐噪声同时保持边缘
 * 适用于去除脉冲噪声和保持图像细节
 */
class MedianFilter : public IImageFilter {
public:
    /**
     * @brief 构造函数
     */
    MedianFilter();

    /**
     * @brief 析构函数
     */
    ~MedianFilter() override = default;

    // IImageFilter接口实现
    bool apply(ImageDataU32& data) override;
    bool apply(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const FilterParams& params) override;
    std::unique_ptr<FilterParams> getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t width, uint32_t height) const override;
    uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;
    bool supportsInPlace() const override;

    /**
     * @brief 设置预定义的滤波模式
     * @param preset 预设模式
     */
    void setPreset(const QString& preset);

    /**
     * @brief 获取支持的预设模式列表
     * @return 预设模式列表
     */
    static QStringList getSupportedPresets();

private:
    MedianParams params_;    ///< 滤波参数

    /**
     * @brief 应用中值滤波到单个像素
     * @param src 源图像
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 中值滤波结果
     */
    uint32_t applyMedianAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const;

    /**
     * @brief 获取安全的像素值（处理边界）
     * @param src 源图像
     * @param x X坐标
     * @param y Y坐标
     * @return 像素值
     */
    uint32_t getSafePixelValue(const ImageDataU32& src, int x, int y) const;

    /**
     * @brief 计算邻域像素的中值
     * @param values 像素值列表
     * @return 中值
     */
    uint32_t calculateMedian(QVector<uint32_t>& values) const;

    /**
     * @brief 验证中值滤波参数
     * @param params 参数对象
     * @throws InvalidParameterException 如果参数无效
     */
    void validateMedianParams(const MedianParams& params) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_MEDIANFILTER_H
