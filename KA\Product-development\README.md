# Product Development - 产品体系构建框架

基于VSCode一体化平台的完整产品开发生命周期管理框架，支持从需求分析到生产交付的全流程自动化管理。

## 🏗️ 总体架构

```
Product development/
├── 📁 scripts/                     # 脚本工具集
│   ├── config/                    # 配置生成和管理
│   ├── directory_initialization/   # 项目初始化
│   ├── requirements/              # 需求管理
│   ├── links/                     # 文档关联系统  
│   ├── infoTrace/                 # 信息追溯系统
│   ├── workflow/                  # 工作流管理
│   ├── task_master/               # 任务管理
│   ├── canvas/                    # Canvas集成
│   ├── visualization/             # 可视化系统
│   ├── production/                # 生产管理
│   └── ...                       # 其他工具脚本
├── 📁 mcp-server_local_integrations/            # MCP服务器集成
│   ├── core/                      # 核心功能MCP服务器
│   ├── development/               # 开发相关MCP服务器
│   ├── integrations/              # 外部系统集成
│   ├── visualization/             # 可视化MCP服务器
│   ├── production/                # 生产相关MCP服务器
│   ├── unified/                   # 统一集成服务器
│   ├── config/                    # MCP配置管理
│   └── interfaces/                # 统一入口和启动
├── 📁 .cursor/                     # Cursor IDE配置
│   └── mcp.json                   # MCP服务器配置
└── 📄 产品体系构建框架.md             # 框架说明文档
```

## 🚀 核心功能模块

### 1. 需求管理系统
- **需求导入**: 市场调研、竞品分析、技术需求收集
- **需求分析**: 需求分解、矩阵维护、优先级排序
- **外部集成**: Firecrawl网页抓取、Atlassian集成、通知服务

**MCP服务器**: [[mcp-server_local_integrations/core/requirements-management.py]]
**脚本依赖**: [[scripts/requirements/]]

### 2. 方案设计系统
- **架构设计**: 系统架构、模块划分、接口定义
- **技术评估**: 可行性评估、技术路线选择
- **图表生成**: UML、AWS、C4架构图自动生成

**MCP服务器**: [[mcp-server_local_integrations/visualization/diagram-generators.py]]

### 3. 开发规划系统
- **项目计划**: 里程碑设定、任务分解、资源分配
- **任务跟踪**: 状态管理、进度报告、资源优化
- **集成管理**: Linear项目管理、GitHub集成

**MCP服务器**: [[mcp-server_local_integrations/core/task-management.py]]
**脚本依赖**: [[scripts/task_master/]]

### 4. 文档关联系统
- **双链网络**: 文档间关联关系建立
- **语义分析**: AI辅助关联发现
- **Canvas集成**: 可视化文档网络

**MCP服务器**: [[mcp-server_local_integrations/core/document-linking.py]]
**脚本依赖**: [[scripts/links/]]

### 5. 信息追溯系统
- **块级追溯**: 从输出内容追溯到原始需求
- **影响分析**: 变更影响范围评估
- **关系维护**: 追溯关系验证和更新

**MCP服务器**: [[mcp-server_local_integrations/core/content-tracing.py]]
**脚本依赖**: [[scripts/infoTrace/]]

### 6. 工作流管理系统
- **组件化设计**: 独立但相互连接的流程组件
- **事件驱动**: 自动化工作流和数据流转
- **双向控制**: 前进推进和影响回溯

**MCP服务器**: [[mcp-server_local_integrations/core/workflow-management.py]]
**脚本依赖**: [[scripts/workflow/]]

### 7. 可视化系统
- **统一平台**: 一体化可视化界面
- **实时交互**: 动态图表和数据展示
- **多元展示**: 架构图、流程图、仪表板

**MCP服务器**: [[mcp-server_local_integrations/visualization/visualization-core.py]]
**脚本依赖**: [[scripts/visualization/]]

### 8. 生产管理系统
- **生产准备**: BOM管理、工艺流程制定
- **质量控制**: 测试流程、数据监控
- **过程优化**: 生产效率分析

**MCP服务器**: [[mcp-server_local_integrations/production/production-management.py]]
**脚本依赖**: [[scripts/production/]]

## 🔧 使用方式

### 通过MCP服务器 (推荐)

**完整集成模式** - 一次性启用所有功能:
```bash
# 通过Cursor MCP集成使用
# 配置在 .cursor/mcp.json 中的 "product-development-complete"
```

**工作流模式** - 按需启用特定工作流:
```bash
# 需求管理工作流
# 配置在 .cursor/mcp.json 中的 "requirements-workflow"

# 开发管理工作流  
# 配置在 .cursor/mcp.json 中的 "development-workflow"

# 生产管理工作流
# 配置在 .cursor/mcp.json 中的 "production-workflow"
```

**模块化模式** - 启用特定功能模块:
```bash
# 核心功能组
# 配置在 .cursor/mcp.json 中的 "core-modules"
```

### 通过统一入口脚本

```bash
# 查看所有可用模块
python mcp-server_local_integrations/interfaces/entrypoint.py --list

# 启动完整集成
python mcp-server_local_integrations/interfaces/entrypoint.py --complete

# 启动特定工作流
python mcp-server_local_integrations/interfaces/entrypoint.py --workflow requirements-workflow

# 启动功能组
python mcp-server_local_integrations/interfaces/entrypoint.py --group core
```

### 直接使用脚本工具

```bash
# 项目初始化
python scripts/init_product_project.py --name="新产品" --type="single_layer"

# 配置生成
python scripts/config/generate_all_configs.py --project_path="./project"

# 文档关联
python scripts/links/link_documents.py --project_path="./project"

# 可视化
python scripts/visualization/quickviz.py ./project
```

## 📊 功能对比

| 使用方式 | 集成度 | 便利性 | 可定制性 | 适用场景 |
|---------|--------|--------|----------|----------|
| MCP服务器 | 🚀 高 | 🚀 高 | 📈 中 | 日常开发，AI辅助 |
| 统一入口 | 📈 中 | 📈 中 | 🚀 高 | 自动化脚本，批处理 |
| 直接脚本 | 💡 低 | 💡 低 | 🚀 高 | 调试，定制开发 |

## 🚨 配置注意事项

### Windows MCP配置限制

- ❌ `cwd` 参数不生效
- ❌ 环境变量不解析（`%VAR%`、`${workspaceFolder}`等）
- ❌ 路径空格问题无法通过配置解决
- ✅ **必须使用完整绝对路径**

### 推荐配置方案

当前 `.cursor/mcp.json` 已优化为模块化配置，支持：

1. **product-development-complete**: 完整功能集成 ⭐ 推荐日常使用
2. **requirements-workflow**: 需求管理专用工作流
3. **development-workflow**: 开发管理专用工作流
4. **production-workflow**: 生产管理专用工作流
5. **core-modules**: 核心功能模块组
6. **legacy-fastmcp**: 向后兼容的遗留版本

## 📚 详细文档

- **框架总述**: [[产品体系构建框架.md]] - 完整的框架设计和原则
- **MCP集成**: [[mcp-server_local_integrations/README.md]] - MCP服务器详细说明
- **脚本工具**: [[scripts/README.md]] - 脚本工具集使用指南
- **配置指南**: [[mcp-server_local_integrations/docs/mcp-server_create_guide.md]] - MCP配置详解

## 🔄 开发路线图

### 第一阶段 - 基础功能 (当前)
- ✅ 项目初始化和配置管理
- ✅ 基础MCP服务器框架
- ✅ 统一入口和配置系统

### 第二阶段 - 核心功能
- 🚧 需求管理系统完善
- 🚧 文档关联系统开发
- 🚧 信息追溯系统实现

### 第三阶段 - 集成优化
- 🚧 工作流自动化
- 🚧 可视化系统完善
- 🚧 外部系统深度集成

### 第四阶段 - 生产化
- 🚧 生产管理系统
- 🚧 质量控制流程
- 🚧 性能优化和稳定性

## 🤝 贡献指南

1. **脚本开发**: 在 `scripts/` 目录下开发功能脚本
2. **MCP服务器**: 在 `mcp-server_local_integrations/` 下创建对应的MCP包装
3. **配置更新**: 更新 `mcp-server_local_integrations/config/mcp_config.yaml`
4. **文档维护**: 同步更新相关文档

---

*基于[[产品体系构建框架]]的一体化、可追溯、AI辅助的产品开发解决方案*



