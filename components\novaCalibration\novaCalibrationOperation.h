#ifndef _NOVA_CALIBRATION_OPERATION_H_
#define _NOVA_CALIBRATION_OPERATION_H_

#include <QObject>
#include <QVector>
#include <QThread>
#include <QTimerEvent>
#include <QApplication>

#include "processListA.h"

#include "IPms.h"
#include "spmsSerial.h"
#include "ISimpleModule.h"
#include "simpleModuleSerial.h"

#include "ILoad.h"
#include "ISaveFile.h"
#include "qLog.h"

namespace NNovaCalibOpt {
typedef struct {
    QString                     cur_port_name; //实时显示端口
    QString                     port_name; //使用端口
    QString                     cur_codeScan_port_name;
    QString                     codeScan_port_name;
    QString                     cur_stepMotor_port_name;
    QString                     stepMotor_port_name;
    QString                     cur_current_port_name;
    QString                     current_port_name;

    IPms::EProjectName          project;
} StUiConfig;
}

//class QApplication;

class CNovaCalibOpt:public QObject{
    Q_OBJECT

public:
  CNovaCalibOpt(const NNovaCalibOpt::StUiConfig &st_config);
  ~CNovaCalibOpt();

  //*************************************** 运行步骤 *******************
//  enum EProcessStep {
//      eOPEN_SERIAL        = 0,
//      eCHIP_ID              ,
//      eVERSION              ,
//      eCODE_SCANNER         ,
//      eROTATE1              ,
//      eXTALK_CALIB          ,
//      eROTATE2              ,
//      eREF_CALIB            ,
//      eREAD_PARAM           ,

//      eINFO_MODE            ,
//      eCHECK_ROTATE         ,
//      eDIST_CHECK           ,

//      eWORK_MODE            ,
//      eCURRENT              ,
//      eTRIG_ROTATE          ,
//      eTRIG_CHECK           ,
//  };
  enum EProcessStep {
//      eOPEN_SERIAL        = 0,
      eREADY                = 0,
      eCHIP_ID              ,
      eVERSION              ,
      eCODE_SCANNER         ,

      eCALIB                ,

      eINFO_MODE            ,

      eACCURACY_VERIFY      ,

//      eCHECK_ROTATE         ,
//      eDIST_CHECK           ,

      eWORK_MODE            ,
      eFUNCTION_TEST        ,
      eCURRENT              ,
//      eTRIG_CHECK           ,
      eCOMPLETE             ,
  };
  Q_ENUM(EProcessStep);

private:

  //************************************* basic ***************
  //* config
  ILoad* mi_load_ = nullptr;
  QMap<QString, int> m_xml_param;
  const NNovaCalibOpt::StUiConfig *mst_config_ = nullptr;
  IPms::EProjectName m_cur_project_name;

  //* process/status
  int m_timerId;
  uint16_t m_port_update_cnt;

  typedef CProcessListA<CNovaCalibOpt, EExecStatus, EProcessStep> TNova_process;
  TNova_process* mc_processList_ = nullptr;
  TNova_process::StStepRecord *mst_task_status_ = nullptr;

  typedef CProcessListA<CNovaCalibOpt, EExecStatus, EProcessStep> TVerify_process;
  TVerify_process* mc_verify_process_ = nullptr;
  TVerify_process::StStepRecord *mst_verify_task_status_ = nullptr;

  //* cmd view status
  typedef struct {
     bool       isCirculation;
     QString    cmd;
     uint8_t    priority; //small num, high priority
     uint16_t   interval; //ms
  } StCmdInfo;

  //* sub thread
  QThread* m_sub_thread_ = nullptr;
  CSpmsSerial *m_spms_thread_ = nullptr;
  CSimpleModuleSerial *m_simple_module_thread_ = nullptr;


  //* port
  IComm* mi_icomm_              = nullptr; //main port
  IComm *mi_icomm_curr_         = nullptr;
  IComm *mi_icomm_motor_        = nullptr;
  IComm *mi_icomm_code_scan_    = nullptr;
  IComm* mi_icomm_reserve_      = nullptr; //sub port

  //* devices
  IPms *mi_spms_ = nullptr;
  ISimpleModule *mi_current_module_ = nullptr;
  ISimpleModule *mi_stepper_motor_ = nullptr;
  ISimpleModule *mi_code_scanner_ = nullptr;

  //* communication status
  StCommunicateStatus* mst_comm_status_ = nullptr;

  //* data
  typedef struct {
      QByteArray         send_cmd;
      QByteArray         ack_cmd;
  } StCommCmd;
  StCommCmd *mst_cur_cmd_ = nullptr;

  QByteArray m_origin_bytes;
  float      m_origin_float;


  //* result
  typedef struct {
      QByteArray            chip_id;
      QByteArray            bar_code;
      QByteArray            firm_version;
      float                 current;

      bool                  xtalk_calib;
      bool                  ref_calib;

  } StResult;
  StResult *mst_result_ = nullptr;

  //* debug
  QWidget *m_message_box_ = nullptr;

  //* data storage
  ISaveFile* mi_save_file_ = nullptr;
  QMap<QString, QVariant> mm_result_data; // = nullptr;


  void varibleInit();
//  void calibTasksUpdate();
//  void accuracyVerifyTasksUpdate();
  void resultInit(StResult* result_);

  //****************************** task lists *****************
  virtual void timerEvent(QTimerEvent *event) override;
  bool portlistUpdate();

  EExecStatus readyStep(void);
  EExecStatus readyStepAck(void);

  EExecStatus codeScanner(void);
  EExecStatus codeScannerAck(void);

  EExecStatus version(void);
  EExecStatus versionAck(void);

  EExecStatus chipId(void); //
  EExecStatus chipIdAck(void);

  EExecStatus calib(void);
  EExecStatus calibAck(void);

  EExecStatus infoMode(void);
  EExecStatus infoModeAck(void);

  EExecStatus accuracyVerify(void);
  EExecStatus accuracyVerifyAck(void);

  EExecStatus workMode(void); //*trigger or distance output
  EExecStatus workModeAck(void);

  EExecStatus functionTest(void);
  EExecStatus functionTestAck(void);

  EExecStatus current(void);
  EExecStatus currentAck(void);

  EExecStatus compStep(void);
  EExecStatus compAck(void);

public:
    QVector<TNova_process::StTask>  mv_task_list;

signals:
    void subThreadSignal(int task_id);

    void subModuleThreadSignal(int module_id);

    void portUpdateSignal(QStringList *port_list_);

    void calibTasksViewSignal(const uint8_t &calib_index, const uint16_t &calib_value1, const uint16_t &calib_value2);

    void functionTestTmpSignal(const uint8_t &cnt, const int16_t &dist, const int16_t &ref_dist);

    void accuracyVerifyTasksViewSignal();

    void otherTestTasksViewSignal();

    void serialShowSignal(bool is_open);

    void stepStatusSignal(const int16_t &step, const int16_t &status);

    void chipIdAckSignal(QByteArray chip_id);

    void currentAckSignal(float current);
public slots:
    bool openSerial_slot(void); //串口开关
    bool closeSerial_slot(void);

    void calib1SingleTmp_slot(void);
    void calib2SingleTmp_slot(void);
    void emptyTriggerTmp_slot(void);

    void calibView_slot(const uint8_t &calib_index, const uint16_t &calib_value1, const uint16_t &calib_value2);
    void functionTestView_slot(const uint8_t &cnt, const int16_t &dist, const int16_t &ref_dist);

private slots:
    void dataReceive(IPms::ECommStep step, ECommStatus status, QByteArray bytes);
    void moduleDataReceive(ISimpleModule::ECommStep step, ECommStatus status, QByteArray bytes);
};



#endif
