#include "faculaFactory.h"
#include <QDebug>


IFaculaFactory::IFaculaFactory() {
    //* register class name
//    CMyPowerCtrlB::registerName();
//    CStepperMotor::registerName();

    //*
}

IFaculaFactory::~IFaculaFactory() {

}

IFaculaAdjust* IFaculaFactory::faculaAdjustCreate(const ERxFaculaType &rx_facula_type, const IFaculaAdjust::StMapInfo &map_info) {
    IFaculaAdjust* facula_ = nullptr;

    switch (rx_facula_type) {
    case ERxFaculaType::circle:
        facula_ = new CFaculaCircle(map_info);
        break;

//    case ERxFaculaType::vertical:
//        facula_ = new CFaculaVertical(map_info);
//        break;

//    case ERxFaculaType::horizontal:
//        facula_ = new CFaculaHorizontal(map_info);
//        break;

//    case ERxFaculaType::double_disc:
//        facula_ = new CFaculaDouble(map_info);
//        break;

    default:
        facula_ = new CFaculaCircle(map_info);
        break;
    }
    return facula_;
}
