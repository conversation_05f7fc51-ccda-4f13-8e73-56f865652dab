#include "loadXml.h"
#include <QDebug>
#include <QFile>
#include <QMap>
#include <QMessageBox>
#include <QStringList>
#include <QXmlStreamReader>

#include "qLog.h"
#include "typeConvert.h"

CLoadXml::CLoadXml() {
}

CLoadXml::~CLoadXml() {
}

bool CLoadXml::loadFile(const QString &filename) {
    QFile file(filename);
    if (!file.open(QFile::ReadOnly | QFile::Text)) {
        // qDebug() << QObject::tr("error::ParserXML->OpenXmlFile->file.open->%s\n") << filename;
        QMessageBox::critical(0, QString("Error"), QString("Cannot OpenXmlFile %1").arg(filename));
        return false;
    }

    if (!m_doc.setContent(&file)) {
        // qDebug() << QObject::tr("error::ParserXML->OpenXmlFile->doc.setContent\n") << filename;
        QMessageBox::critical(0, QString("Error"), QString("Cannot OpenXmlFile %1").arg(filename));
        file.close();
        return false;
    }

    file.close();
    return true;
}

//写入XML
void CLoadXml::writeXML(const QString &filename) {
    QFile file(filename);                                        //打开或新建xml文件
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate))  // Truncate表示清空原来的内容
    {
        // QMessageBox::warning(this, "错误", "文件打开失败");
        return;
    }

    //    QDomDocument doc;
    //    //写入xml头部
    //    QDomProcessingInstruction instruction;//添加处理指令
    //    instruction = doc.createProcessingInstruction("xml","version=\"1.0\" encoding=\"UTF-8\"");
    //    doc.appendChild(instruction);
    //    //添加根节点
    //    QDomElement root = doc.createElement("library");
    //    doc.appendChild(root);
    //    //添加第一个子节点，及其子元素
    //    QDomElement book = doc.createElement("book");
    //    book.setAttribute("id",1);//方法1，创建属性，键值对可以是各种类型
    //    QDomAttr time = doc.createAttribute("time");//方法2，创建属性，值必须是字符串
    //    time.setValue("2020/6/3");
    //    book.setAttributeNode(time);

    //    QDomElement title = doc.createElement("title");//创建子元素
    //    QDomText text = doc.createTextNode("C++ primer");//设置括号标签中间的值
    //    book.appendChild(title);
    //    title.appendChild(text);
    //    QDomElement author = doc.createElement("author");//创建子元素
    //    text = doc.createTextNode("Stanley B.Lippman");
    //    author.appendChild(text);
    //    book.appendChild(author);
    //    root.appendChild(book);

    //    //添加第二个子节点，部分变量只需重新赋值
    //    book=doc.createElement("book");
    //    book.setAttribute("id",2);
    //    time = doc.createAttribute("time");
    //    time.setValue("2007/5/25");
    //    book.setAttributeNode(time);
    //    title = doc.createElement("title");
    //    text = doc.createTextNode("Thinking in Java");
    //    book.appendChild(title);
    //    title.appendChild(text);
    //    author = doc.createElement("author");
    //    text = doc.createTextNode("Bruce Eckel");
    //    author.appendChild(text);
    //    book.appendChild(author);
    //    root.appendChild(book);

    //输出文件
    //    QTextStream out_stream(&file);
    //    doc.save(out_stream,4);//缩进4格
    file.close();
}

void CLoadXml::addXML(const QString &filename) {
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        // QMessageBox::warning(this,"错误","增加XML，文件打开失败1");
        return;
    }
    QDomDocument doc;
    if (!doc.setContent(&file))  //从字节数组中解析XML文档，并将其设置为文档的内容
    {
        file.close();
        return;
    }
    file.close();

    QDomElement root = doc.documentElement();
    QDomElement book = doc.createElement("book");
    book.setAttribute("id", 3);
    book.setAttribute("time", "1813/1/27");
    QDomElement title = doc.createElement("title");
    QDomText    text;
    text = doc.createTextNode("Pride and Prejudice");
    title.appendChild(text);
    book.appendChild(title);
    QDomElement author = doc.createElement("author");
    text               = doc.createTextNode("Jane Austen");
    author.appendChild(text);
    book.appendChild(author);
    root.appendChild(book);

    if (!file.open(QFile::WriteOnly | QFile::Truncate))  //重写文件，如果不用truncate就是在后面追加内容，就无效了
    {
        // QMessageBox::warning(this,"错误","增加XML，文件打开失败2");
        return;
    }
    QTextStream out_stream(&file);
    doc.save(out_stream, 4);
    file.close();
}

//删减XML内容
void CLoadXml::removeXML(const QString &filename) {
    QFile file(filename);
    //    if(!file.open(QIODevice::ReadOnly))
    //    {
    //        QMessageBox::warning(this,"错误","增加XML，文件打开失败1");
    //        return;
    //    }
    //    QDomDocument doc;
    //    if(!doc.setContent(&file))//从字节数组中解析XML文档，并将其设置为文档的内容
    //    {
    //        file.close();
    //        return;
    //    }
    //    file.close();
    //    QDomElement root=doc.documentElement();
    //    QDomNodeList list = doc.elementsByTagName("book");//指定名称的节点列表
    //    for(int i=0;i<list.count();i++)
    //    {
    //        QDomElement e = list.at(i).toElement();
    //        if(e.attribute("time")=="2007/5/25")
    //            root.removeChild(list.at(i));
    //    }
    //    if(!file.open(QFile::WriteOnly|QFile::Truncate))//重写文件，如果不用truncate就是在后面追加内容，就无效了
    //    {
    //        QMessageBox::warning(this,"错误","删减XML内容，文件打开失败");
    //        return;
    //    }
    //    QTextStream out_stream(&file);
    //    doc.save(out_stream,4);
    file.close();
}

void CLoadXml::updateXML(const QString &filename) {
    QFile file(filename);
    //    if(!file.open(QIODevice::ReadOnly))
    //    {
    //        QMessageBox::warning(this,"错误","更新XML，文件打开失败1");
    //        return;
    //    }
    //    QDomDocument doc;
    //    if(!doc.setContent(&file))//从字节数组中解析XML文档，并将其设置为文档的内容
    //    {
    //        file.close();
    //        return;
    //    }
    //    file.close();

    //    QDomElement root = doc.documentElement();//获得根节点
    //    QDomNodeList list = root.elementsByTagName("book");//指定名称的节点列表
    //    QDomNode node = list.at(list.count()-1).firstChild();//定位到第三个一级子节点的子元素
    //    QDomNode oldNode = node.firstChild();//标签之间的内容作为节点的子节点出现,当前是Pride and Projudice
    //    oldNode.setNodeValue("dalao");//修改元素内容
    ////    node.firstChild().setNodeValue("diannao");
    ////    QDomNode newNode = node.firstChild();
    ////    node.replaceChild(newNode,oldNode);

    //    if(!file.open(QFile::WriteOnly|QFile::Truncate))//重写文件，如果不用truncate就是在后面追加内容，就无效了
    //    {
    //        QMessageBox::warning(this,"错误","更新XML内容，文件打开失败2");
    //        return;
    //    }
    //    QTextStream out_stream(&file);
    //    doc.save(out_stream,4);
    file.close();
}

/**
 * @brief 读参数。使用静态函数？如何实现多态
 * @param filename
 * @param xml_read
 */
void CLoadXml::readParam(const QString &filename, QMap<QString, int> *xml_read) {
    QFile file(filename);
    if (!file.open(QFile::ReadOnly | QFile::Text)) {
        QMessageBox::critical(0, QString("Error"), QString("Cannot read file %1").arg(filename));
        return;
    }
    QXmlStreamReader xmlReader(&file);
    xmlReader.readNext();
    while (!xmlReader.atEnd()) {
        if (xmlReader.isStartElement()) {
            if (xmlReader.name() == "Parameters") {
                xmlReader.readNext();
            } else {
                int                          para1 = xmlReader.readElementText().toInt();
                QString                      name  = xmlReader.name().toString();
                QMap<QString, int>::iterator iter  = xml_read->find(name);

                if (iter != xml_read->end())
                    iter.value() = para1;
                else {
                    LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("param %1 dont find").arg(name));
                }
                xmlReader.readNext();
                //                for(QMap<QString, int>::iterator it = m_xml_param.begin(); it != m_xml_param.end(); it++)
                //                {
                //                    if(it.key() == "" )
                //                }
            }
        } else {
            xmlReader.readNext();
        }
        file.close();
        if (xmlReader.hasError()) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("failed to parse file %1: %2").arg(filename).arg(xmlReader.errorString()));
        } else if (file.error() != QFile::NoError) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("Cannot read file, %1: %2").arg(filename).arg(file.errorString()));
        }
    }
}

/**
 * @brief 读参数。使用静态函数？如何实现多态
 * @param filename
 * @param xml_read
 */
void CLoadXml::readParam(const QString &filename, QMap<QString, QVariant> *xml_read) {
    QFile file(filename);
    if (!file.open(QFile::ReadOnly | QFile::Text)) {
        QMessageBox::critical(0, QString("Error"), QString("Cannot read file %1").arg(filename));
        return;
    }
    QXmlStreamReader xmlReader(&file);
    xmlReader.readNext();
    while (!xmlReader.atEnd()) {
        if (xmlReader.isStartElement()) {
            if (xmlReader.name() == "Parameters") {
                xmlReader.readNext();
            } else {
                int                               para1 = xmlReader.readElementText().toInt();
                QString                           name  = xmlReader.name().toString();
                QMap<QString, QVariant>::iterator iter  = xml_read->find(name);

                if (iter != xml_read->end())
                    iter.value() = para1;
                else {
                    LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("param %1 dont find").arg(name));
                }
                xmlReader.readNext();
            }
        } else {
            xmlReader.readNext();
        }
        file.close();
        if (xmlReader.hasError()) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("failed to parse file %1: %2").arg(filename).arg(xmlReader.errorString()));
        } else if (file.error() != QFile::NoError) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("Cannot read file, %1: %2").arg(filename).arg(file.errorString()));
        }
    }
}

/**
 * @brief load all params from xml file
 * @param filename
 * @param xml_read
 */
void CLoadXml::readParam(const QString &filename, QMap<QString, QByteArray> *xml_read) {
    QFile file(filename);
    if (!file.open(QFile::ReadOnly | QFile::Text)) {
        QMessageBox::critical(0, QString("Error"), QString("Cannot read file %1").arg(filename));
        return;
    }
    QXmlStreamReader xmlReader(&file);
    xmlReader.readNext();
    while (!xmlReader.atEnd()) {
        if (xmlReader.isStartElement()) {
            if (xmlReader.name() == "Parameters") {
                xmlReader.readNext();
            } else {
                QString     read_text   = xmlReader.readElementText();
                QStringList string_list = read_text.split(" ");
                QByteArray  cmd_tmp;
                for (QStringList::iterator iter = string_list.begin(); iter != string_list.end(); iter++) {
                    //                    iter->toInt(nullptr, 16);
                    uint8_t tmp = static_cast<uint8_t>(iter->toUInt(nullptr, 16));

                    cmd_tmp.append(tmp);
                }
                //                QByteArray para1 = xmlReader.readElementText();

                QString                             name = xmlReader.name().toString();
                QMap<QString, QByteArray>::iterator iter = xml_read->find(name);

                if (iter != xml_read->end())
                    iter.value() = cmd_tmp;
                else {
                    LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("param %1 dont find").arg(name));
                }
                xmlReader.readNext();
                //                for(QMap<QString, int>::iterator it = m_xml_param.begin(); it != m_xml_param.end(); it++)
                //                {
                //                    if(it.key() == "" )
                //                }
            }
            /*else if (xmlReader.name() == "z_radius_limit") {
                int para2 = xmlReader.readElementText().toInt();
                xmlReader.readNext();
            } else {
                xmlReader.raiseError(QObject::tr("Not a options file"));
            }*/
        } else {
            xmlReader.readNext();
        }
        file.close();
        if (xmlReader.hasError()) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("failed to parse file %1: %2").arg(filename).arg(xmlReader.errorString()));
        } else if (file.error() != QFile::NoError) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("Cannot read file, %1: %2").arg(filename).arg(file.errorString()));
        }
    }
}


void CLoadXml::readParam(const QString &filename, QMap<QString, QString> *xml_read) {
    QFile file(filename);
    if (!file.open(QFile::ReadOnly | QFile::Text)) {
        QMessageBox::critical(0, QString("Error"), QString("Cannot read file %1").arg(filename));
        return;
    }
    QXmlStreamReader xmlReader(&file);
    xmlReader.readNext();
    while (!xmlReader.atEnd()) {
        if (xmlReader.isStartElement()) {
            if (xmlReader.name() == "Parameters") {
                xmlReader.readNext();
            } else {
                QByteArray para1 = xmlReader.readElementText().toUtf8();
                QString    name  = xmlReader.name().toString();

                xml_read->insert(name, para1);
                //                QMap<QString, QString>::iterator iter = xml_read->find(name);
                //                if(iter != xml_read->end())
                //                    iter.value() = para1;
                //                else
                //                    qDebug() << "-xml -e: param s% dont find " << name;
                xmlReader.readNext();
            }
        } else {
            xmlReader.readNext();
        }
        file.close();
        if (xmlReader.hasError()) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("failed to parse file %1: %2").arg(filename).arg(xmlReader.errorString()));
        } else if (file.error() != QFile::NoError) {
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("Cannot read file, %1: %2").arg(filename).arg(file.errorString()));
        }
    }
}

bool CLoadXml::readParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_read) {
    if (!loadFile(filename)) {
        return false;
    }

    //获取根结点元素
    QDomElement root = m_doc.documentElement();
    if (root.tagName().compare(rootname) != 0) {
        qDebug() << "rootName does not match";
        return false;
    }

    //获取子节点
    QDomNode node = root.firstChild();

    while (!node.isNull()) {
        //获取第1层子结点元素
        QDomElement nodeElement = node.toElement();
        if (!nodeElement.isNull()) {
            if (nodeElement.nodeName().compare(nodename) == 0) {  //判断第1层子节点的名字
                QDomNodeList list = nodeElement.childNodes();     //获取第1层子节点列表, 并遍历
                for (int i = 0; i < list.count(); i++) {
                    QDomNode node = list.at(i);  //获取第2层第1个/2个子节点
                    if (node.isElement()) {
                        //                        if(node.nodeName().compare(node2Name1) == 0) { //获取第3层第1个字结点,并修改值
                        //                            QDomNode oldnode = node.firstChild();
                        //                            node.firstChild().setNodeValue("");
                        //                            QDomNode newnode = node.firstChild();
                        //                            node.replaceChild(newnode, oldnode);
                        xml_read->insert(node.nodeName(), node.firstChild().nodeValue());
                    }
                }
            }
        }

        //第1层子结点的下一结点
        node = node.nextSibling();
    }

    //重写入文件
    QFile filexml(filename);
    if (!filexml.open(QFile::WriteOnly | QFile::Truncate)) {
        qWarning("error::ParserXML->writeOperateXml->file.open\n");
        return false;
    }

    QTextStream ts(&filexml);
    ts.reset();
    ts.setCodec("utf-8");
    m_doc.save(ts, 4, QDomNode::EncodingFromTextStream);
    filexml.close();

    return true;
}


/**
 * @brief add new params to xml file
 * @param filename
 * @param xml_write
 */
bool CLoadXml::writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QByteArray> *xml_write) {
    //* 1.open file
    if (!loadFile(filename)) {
        return false;
    }

    //获取根结点元素
    QDomElement root = m_doc.documentElement();
    if (root.tagName().compare(rootname) != 0) {
        qDebug() << "rootName does not match";
        return false;
    }

    //获取子节点
    QDomNode node = root.firstChild();
    while (!node.isNull()) {
        //获取第1层子结点元素
        QDomElement nodeElement = node.toElement();
        if (!nodeElement.isNull()) {
            if (nodeElement.nodeName().compare(nodename) == 0) {  //判断第1层子节点的名字
                QDomNodeList list = nodeElement.childNodes();     //获取第1层子节点列表, 并遍历

                for (QMap<QString, QByteArray>::iterator it = xml_write->begin(); it != xml_write->end(); it++) {
                    bool is_exist = false;
                    for (int i = 0; i < list.count(); i++) {
                        QDomNode node = list.at(i);  //获取第2层第1个/2个子节点
                        if (node.isElement()) {
                            if (node.nodeName() == it.key())
                                is_exist = true;
                        }
                    }

                    if (is_exist) {
                        QDomNode oldnode = node.firstChild();
                        node.firstChild().setNodeValue(it.value());
                        QDomNode newnode = node.firstChild();
                        node.replaceChild(newnode, oldnode);
                    } else {
                        QDomElement new_node = m_doc.createElement(it.key());
                        QDomText    text;
                        text = m_doc.createTextNode(NsTypeConvert::byteArrayToString(it.value()));
                        new_node.appendChild(text);
                        nodeElement.appendChild(new_node);
                    }
                }
            }
        }

        //第1层子结点的下一结点
        node = node.nextSibling();
    }

    //重写入文件
    QFile filexml(filename);
    if (!filexml.open(QFile::WriteOnly | QFile::Truncate)) {
        qWarning("error::ParserXML->writeOperateXml->file.open\n");
        return false;
    }

    QTextStream ts(&filexml);
    ts.reset();
    ts.setCodec("utf-8");
    m_doc.save(ts, 4, QDomNode::EncodingFromTextStream);
    filexml.close();

    return true;
}

/**
 * @brief 修改已有内容，增加新内容
 * @param filename
 * @param rootname
 * @param nodename
 * @param xml_write
 * @return
 */
bool CLoadXml::writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_write) {
    //* 1.open file
    if (!loadFile(filename)) {
        return false;
    }

    //获取根结点元素
    QDomElement root = m_doc.documentElement();
    if (root.tagName().compare(rootname) != 0) {
        qDebug() << "rootName does not match";
        return false;
    }

    //获取子节点
    QDomNode node = root.firstChild();
    while (!node.isNull()) {
        //获取第1层子结点元素
        QDomElement nodeElement = node.toElement();
        if (!nodeElement.isNull()) {
            if (nodeElement.nodeName().compare(nodename) == 0) {  //判断第1层子节点的名字
                QDomNodeList list = nodeElement.childNodes();     //获取第1层子节点列表, 并遍历
                                                                  //                for(int i=0; i<list.count(); i++) {
                                                                  //                    QDomNode node = list.at(i);  //获取第2层第1个/2个子节点
                                                                  //                    if(node.isElement()) {
                ////                        if(node.nodeName().compare(node2Name1) == 0) { //获取第3层第1个字结点,并修改值
                ////                            QDomNode oldnode = node.firstChild();
                ////                            node.firstChild().setNodeValue("");
                ////                            QDomNode newnode = node.firstChild();
                ////                            node.replaceChild(newnode, oldnode);
                ////                        }
                //                        QMap<QString, QString>::iterator iter = xml_write->find(node.nodeName());

                //                        if(iter != xml_write->end()) {
                //                            QDomNode oldnode = node.firstChild();
                //                            node.firstChild().setNodeValue(iter.value());
                //                            QDomNode newnode = node.firstChild();
                //                            node.replaceChild(newnode,oldnode);
                //                        }
                //                        else {
                //                            QDomElement new_node = m_doc.createElement();
                //                            QDomText text;
                //                            text = m_doc.createTextNode();
                //                            nodeElement.appendChild(text);
                //                        }
                //                    }
                //                }
                for (QMap<QString, QString>::iterator it = xml_write->begin(); it != xml_write->end(); it++) {
                    bool     is_exist = false;
                    QDomNode node;
                    for (int i = 0; i < list.count(); i++) {
                        node = list.at(i);  //获取第2层第1个/2个子节点
                        if (node.isElement()) {
                            if (node.nodeName() == it.key()) {
                                is_exist = true;
                                break;
                            }
                        }
                    }

                    if (is_exist) {
                        QDomNode oldnode = node.firstChild();
                        node.firstChild().setNodeValue(it.value());
                        QDomNode newnode = node.firstChild();
                        node.replaceChild(newnode, oldnode);
                    } else {
                        QDomElement new_node = m_doc.createElement(it.key());
                        QDomText    text;
                        text = m_doc.createTextNode(it.value());
                        nodeElement.appendChild(text);
                    }
                }
            }
        }

        //第1层子结点的下一结点
        node = node.nextSibling();
    }

    //重写入文件
    QFile filexml(filename);
    if (!filexml.open(QFile::WriteOnly | QFile::Truncate)) {
        qWarning("error::ParserXML->writeOperateXml->file.open\n");
        return false;
    }

    QTextStream ts(&filexml);
    ts.reset();
    ts.setCodec("utf-8");
    m_doc.save(ts, 4, QDomNode::EncodingFromTextStream);
    filexml.close();

    return true;
}
