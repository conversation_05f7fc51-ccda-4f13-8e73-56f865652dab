#!/usr/bin/env python3
"""
需求矩阵摘要生成工具 - 生成多层级产品的需求矩阵摘要报告
用法: python generate_requirements_summary.py --config="__level_config.json" --output="reports"
"""

import os
import sys
import json
import argparse
import re
import glob
from pathlib import Path
import datetime
import shutil

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="生成多层级产品的需求矩阵摘要报告")
    parser.add_argument('--config', required=True, help="层级配置文件路径")
    parser.add_argument('--output', default="reports", help="输出报告目录")
    parser.add_argument('--format', default="markdown", choices=["markdown", "html"], help="输出格式")
    parser.add_argument('--show-links', action='store_true', help="是否显示需求间的链接关系")
    return parser.parse_args()

def load_config(config_path):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 简单验证配置文件
        if "product_name" not in config or "levels" not in config:
            print("错误: 配置文件格式不正确，缺少必要字段")
            sys.exit(1)
            
        return config
    except FileNotFoundError:
        print(f"错误: 找不到配置文件 {config_path}")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"错误: 配置文件 {config_path} 不是有效的JSON格式")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 加载配置文件时出错: {str(e)}")
        sys.exit(1)

def find_requirement_files(product_path, config):
    """查找所有层级的需求矩阵文件"""
    requirement_files = {}
    
    for level in config["levels"]:
        level_id = level["level_id"]
        level_files = []
        
        for instance in level["instances"]:
            instance_path = os.path.join(product_path, f"level_{level_id}_{instance}")
            req_file = os.path.join(instance_path, "requirements", "requirements_matrix.md")
            
            if os.path.exists(req_file):
                level_files.append((instance, req_file))
        
        if level_files:
            requirement_files[level_id] = level_files
    
    return requirement_files

def parse_requirement_file(file_path):
    """解析需求矩阵文件，提取需求项"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式提取表格内容
        table_pattern = r'\|.*?\|.*?\|.*?\|.*?\|'
        table_rows = re.findall(table_pattern, content, re.MULTILINE)
        
        # 提取需求项
        requirements = []
        
        for row in table_rows:
            # 跳过表头和分隔行
            if '---|---' in row or '需求ID' in row:
                continue
                
            # 分割单元格
            cells = [cell.strip() for cell in row.split('|') if cell.strip()]
            
            if len(cells) >= 3:
                # 提取需求ID、描述和状态
                req_id = cells[0]
                description = cells[1]
                status = cells[2]
                
                # 提取下层关联（如果有）
                child_links = []
                if len(cells) > 3:
                    # 使用正则表达式提取链接的需求ID
                    link_pattern = r'\[(.*?)\]'
                    matches = re.findall(link_pattern, cells[3])
                    child_links = [m for m in matches if m.strip()]
                
                requirements.append({
                    'id': req_id,
                    'description': description,
                    'status': status,
                    'child_links': child_links
                })
        
        return requirements
    except Exception as e:
        print(f"警告: 解析需求文件 {file_path} 时出错: {str(e)}")
        return []

def calculate_statistics(requirement_files):
    """计算需求统计信息"""
    stats = {
        'total_requirements': 0,
        'by_level': {},
        'by_status': {
            '已实现': 0,
            '进行中': 0,
            '未开始': 0,
            '已取消': 0,
            '其他': 0
        }
    }
    
    # 按层级统计需求
    for level_id, level_files in requirement_files.items():
        level_total = 0
        level_stats = {
            'instances': {},
            'status': {
                '已实现': 0,
                '进行中': 0,
                '未开始': 0,
                '已取消': 0,
                '其他': 0
            }
        }
        
        for instance, file_path in level_files:
            requirements = parse_requirement_file(file_path)
            instance_count = len(requirements)
            level_total += instance_count
            
            # 按实例统计
            level_stats['instances'][instance] = instance_count
            
            # 按状态统计
            for req in requirements:
                status = req['status']
                if status == '已实现':
                    level_stats['status']['已实现'] += 1
                    stats['by_status']['已实现'] += 1
                elif status == '进行中':
                    level_stats['status']['进行中'] += 1
                    stats['by_status']['进行中'] += 1
                elif status == '未开始':
                    level_stats['status']['未开始'] += 1
                    stats['by_status']['未开始'] += 1
                elif status == '已取消':
                    level_stats['status']['已取消'] += 1
                    stats['by_status']['已取消'] += 1
                else:
                    level_stats['status']['其他'] += 1
                    stats['by_status']['其他'] += 1
        
        stats['by_level'][level_id] = level_stats
        stats['total_requirements'] += level_total
    
    return stats

def generate_markdown_report(output_path, config, requirement_files, stats, show_links=False):
    """生成Markdown格式的摘要报告"""
    product_name = config.get("product_name", "未命名产品")
    
    # 构建报告内容
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = [
        f"# {product_name} - 需求矩阵摘要报告",
        f"*生成时间: {now}*",
        "",
        "## 需求概览",
        "",
        f"- **总需求数**: {stats['total_requirements']}",
        "- **需求状态分布**:",
        f"  - 已实现: {stats['by_status']['已实现']} ({calculate_percentage(stats['by_status']['已实现'], stats['total_requirements'])}%)",
        f"  - 进行中: {stats['by_status']['进行中']} ({calculate_percentage(stats['by_status']['进行中'], stats['total_requirements'])}%)",
        f"  - 未开始: {stats['by_status']['未开始']} ({calculate_percentage(stats['by_status']['未开始'], stats['total_requirements'])}%)",
        f"  - 已取消: {stats['by_status']['已取消']} ({calculate_percentage(stats['by_status']['已取消'], stats['total_requirements'])}%)",
        f"  - 其他状态: {stats['by_status']['其他']} ({calculate_percentage(stats['by_status']['其他'], stats['total_requirements'])}%)",
        "",
        "## 各层级需求分布",
        ""
    ]
    
    # 按层级添加详细信息
    for level in sorted(config["levels"], key=lambda x: x["level_id"]):
        level_id = level["level_id"]
        level_name = level["level_name"]
        
        if level_id not in stats['by_level']:
            continue
            
        level_stats = stats['by_level'][level_id]
        level_total = sum(level_stats['instances'].values())
        
        report.append(f"### {level_id}. {level_name}")
        report.append("")
        report.append(f"- **层级需求总数**: {level_total}")
        report.append("- **需求状态分布**:")
        report.append(f"  - 已实现: {level_stats['status']['已实现']} ({calculate_percentage(level_stats['status']['已实现'], level_total)}%)")
        report.append(f"  - 进行中: {level_stats['status']['进行中']} ({calculate_percentage(level_stats['status']['进行中'], level_total)}%)")
        report.append(f"  - 未开始: {level_stats['status']['未开始']} ({calculate_percentage(level_stats['status']['未开始'], level_total)}%)")
        report.append(f"  - 已取消: {level_stats['status']['已取消']} ({calculate_percentage(level_stats['status']['已取消'], level_total)}%)")
        report.append(f"  - 其他状态: {level_stats['status']['其他']} ({calculate_percentage(level_stats['status']['其他'], level_total)}%)")
        report.append("")
        
        # 按实例添加详细信息
        report.append("| 实例名称 | 需求数量 | 已实现 | 进行中 | 未开始 |")
        report.append("|---------|----------|--------|--------|--------|")
        
        for instance, file_path in requirement_files[level_id]:
            requirements = parse_requirement_file(file_path)
            instance_count = len(requirements)
            
            # 计算各状态需求数
            completed = sum(1 for req in requirements if req['status'] == '已实现')
            in_progress = sum(1 for req in requirements if req['status'] == '进行中')
            not_started = sum(1 for req in requirements if req['status'] == '未开始')
            
            report.append(f"| {instance} | {instance_count} | {completed} | {in_progress} | {not_started} |")
        
        report.append("")
        
        # 添加需求链接关系
        if show_links:
            report.append("#### 需求追溯关系")
            report.append("")
            
            for instance, file_path in requirement_files[level_id]:
                requirements = parse_requirement_file(file_path)
                
                # 筛选有下层链接的需求
                linked_reqs = [req for req in requirements if req['child_links']]
                
                if linked_reqs:
                    report.append(f"**{instance}**:")
                    report.append("")
                    
                    for req in linked_reqs:
                        report.append(f"- {req['id']} → {', '.join(req['child_links'])}")
                    
                    report.append("")
    
    # 生成输出文件
    output_file = os.path.join(output_path, "requirements_summary.md")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"成功: 已生成需求矩阵摘要报告 {output_file}")
    return output_file

def calculate_percentage(part, total):
    """计算百分比"""
    if total == 0:
        return 0
    return round((part / total) * 100, 1)

def generate_html_report(markdown_file, output_path):
    """将Markdown报告转换为HTML格式"""
    try:
        # 检查是否安装了markdown库
        import markdown
        from markdown.extensions.tables import TableExtension
    except ImportError:
        print("警告: 未安装markdown库，无法生成HTML报告")
        print("      可以通过 'pip install markdown' 安装")
        return
    
    try:
        # 读取Markdown内容
        with open(markdown_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换为HTML
        html_content = markdown.markdown(
            md_content,
            extensions=['tables', 'fenced_code']
        )
        
        # 添加基本样式
        styled_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求矩阵摘要报告</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #2980b9;
            margin-top: 30px;
        }}
        h3 {{
            color: #3498db;
        }}
        h4 {{
            color: #2c3e50;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        code {{
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 3px;
        }}
    </style>
</head>
<body>
    {html_content}
</body>
</html>
"""
        
        # 生成输出文件
        output_file = os.path.join(output_path, "requirements_summary.html")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(styled_html)
        
        print(f"成功: 已生成HTML格式需求矩阵摘要报告 {output_file}")
    except Exception as e:
        print(f"警告: 生成HTML报告时出错: {str(e)}")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 加载配置文件
    config = load_config(args.config)
    
    # 确定产品路径
    config_path = Path(args.config)
    product_path = config_path.parent
    
    # 查找需求矩阵文件
    requirement_files = find_requirement_files(product_path, config)
    
    if not requirement_files:
        print("警告: 未找到任何需求矩阵文件")
        sys.exit(0)
    
    # 计算统计信息
    stats = calculate_statistics(requirement_files)
    
    # 确保输出目录存在
    output_path = Path(args.output)
    if not output_path.exists():
        output_path.mkdir(parents=True, exist_ok=True)
    
    # 生成报告
    md_file = generate_markdown_report(
        str(output_path),
        config,
        requirement_files,
        stats,
        show_links=args.show_links
    )
    
    # 如果需要HTML格式，转换Markdown报告
    if args.format == "html":
        generate_html_report(md_file, str(output_path))

if __name__ == "__main__":
    main() 