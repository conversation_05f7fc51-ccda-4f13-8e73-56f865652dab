#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Canvas工具模块

提供Canvas相关的工具函数，包括路径处理、布局计算等功能。
"""

from pathlib import Path
from typing import Dict, List, Optional
import sys

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir.parent
sys.path.insert(0, str(scripts_dir))

from common.config import get_canvas_layout_config, get_component_info


class CanvasPathUtils:
    """Canvas路径工具类"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
        self.project_name = self.project_path.name
    
    def format_canvas_path(self, doc_path: str) -> str:
        """
        格式化Canvas文件路径

        Args:
            doc_path: 文档相对路径

        Returns:
            str: Canvas格式的路径（相对路径，不以/开头）
        """
        # 标准化路径
        doc_path = self.normalize_document_path(doc_path)

        # Canvas中的路径应该是相对路径，不需要项目名前缀，也不以/开头
        # 移除可能的项目名前缀
        if doc_path.startswith(f"{self.project_name}/"):
            doc_path = doc_path[len(f"{self.project_name}/"):]

        # 确保路径不以/开头
        if doc_path.startswith('/'):
            doc_path = doc_path[1:]

        return doc_path
    
    def normalize_document_path(self, doc_path: str) -> str:
        """
        标准化文档路径
        
        Args:
            doc_path: 原始文档路径
            
        Returns:
            str: 标准化后的路径
        """
        # 移除前导的 ./
        if doc_path.startswith('./'):
            doc_path = doc_path[2:]
        
        # 转换为正斜杠
        doc_path = doc_path.replace('\\', '/')
        
        return doc_path


class CanvasLayoutUtils:
    """Canvas布局工具类"""
    
    def __init__(self):
        self.layout_config = get_canvas_layout_config()
        self.node_width = 400
        self.node_height = 300
        self.node_spacing_y = 350
        self.margin = 25
    
    def calculate_node_position(self, component: str, index_in_component: int) -> Dict[str, int]:
        """
        计算节点在Canvas中的位置
        
        Args:
            component: 组件名称
            index_in_component: 节点在该组件中的索引（从0开始）
            
        Returns:
            Dict[str, int]: 包含x和y坐标的字典
        """
        if component not in self.layout_config:
            # 使用默认组件DEV
            component = "DEV"
            print(f"未知组件，使用默认组件DEV")
            
        area = self.layout_config[component]
        
        # 计算X坐标：在组件区域内居中，确保不为负数
        area_width = area["x_end"] - area["x_start"]
        x = area["x_start"] + max(0, (area_width - self.node_width) // 2)
        
        # 计算Y坐标：垂直排列，每列最多15个节点
        column = index_in_component // 15
        row = index_in_component % 15
        
        y = 50 + row * self.node_spacing_y  # 顶部留50像素空白
        x += column * (self.node_width + 50)  # 多列时向右偏移
        
        # 确保坐标不为负数
        x = max(0, x)
        y = max(0, y)
        
        return {"x": x, "y": y}
    
    def get_component_color(self, component: str) -> str:
        """
        获取组件对应的颜色
        
        Args:
            component: 组件名称
            
        Returns:
            str: 十六进制颜色值
        """
        if component in self.layout_config:
            return self.layout_config[component]["color"]
        
        # 使用shared_config中的颜色
        component_info = get_component_info(component)
        return component_info.color if component_info else "#CCCCCC"
    
    def validate_layout(self, nodes: List[Dict]) -> List[str]:
        """
        验证布局的合理性
        
        Args:
            nodes: 节点列表
            
        Returns:
            List[str]: 问题描述列表，空列表表示无问题
        """
        issues = []
        
        # 计算最大X坐标
        max_x = max(area["x_end"] for area in self.layout_config.values()) + 500
        
        for node in nodes:
            node_id = node.get("id", "unknown")
            x, y = node.get("x", 0), node.get("y", 0)
            
            # 检查坐标是否在合理范围内
            if x < 0:
                issues.append(f"节点 {node_id} 的X坐标为负数: {x}")
            if x > max_x:
                issues.append(f"节点 {node_id} 的X坐标过大: {x}")
            if y < 0:
                issues.append(f"节点 {node_id} 的Y坐标为负数: {y}")
            
            # 检查节点是否重叠（简单检查）
            for other_node in nodes:
                if other_node.get("id") != node_id:
                    other_x, other_y = other_node.get("x", 0), other_node.get("y", 0)
                    if abs(x - other_x) < 50 and abs(y - other_y) < 50:
                        issues.append(f"节点 {node_id} 可能与其他节点重叠")
                        break
        
        return issues
    
    def get_layout_stats(self) -> Dict:
        """获取布局统计信息"""
        return {
            "total_areas": len(self.layout_config),
            "area_width": self.node_width,
            "area_height": self.node_height,
            "vertical_spacing": self.node_spacing_y,
            "max_nodes_per_column": 15,
            "components": list(self.layout_config.keys())
        } 