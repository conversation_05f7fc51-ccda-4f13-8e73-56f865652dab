# 产品开发完整功能集成MCP服务器依赖

# MCP核心依赖
mcp>=1.0.0
fastmcp>=1.0.0

# 文件系统操作
pathlib2>=2.3.7

# JSON/YAML处理
jsonschema>=4.0.0
PyYAML>=6.0

# Excel文件处理（需求导入）
openpyxl>=3.1.0
pandas>=2.0.0

# 图表生成
matplotlib>=3.7.0
plotly>=5.15.0
graphviz>=0.20.0

# 文档处理
python-docx>=0.8.11
markdown>=3.4.0

# 日志记录
loguru>=0.7.0

# HTTP客户端（API集成）
httpx>=0.24.0
requests>=2.31.0

# 数据验证
pydantic>=2.0.0

# 类型检查
typing-extensions>=4.0.0

# 异步支持
asyncio-mqtt>=0.13.0

# 缓存支持
cachetools>=5.3.0

# 配置管理
python-dotenv>=1.0.0
