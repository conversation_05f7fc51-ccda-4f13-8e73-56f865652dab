#ifndef MOTORMONITORSERIAL_H
#define MOTORMONITORSERIAL_H

#include <QString>
#include <QMessageBox>
#include <QDebug>
#include <QTime>
#include <QTimer>
#include "ITestBoard.h"


class CTestBoardSerial:public QObject
{
  Q_OBJECT
public:
  explicit CTestBoardSerial(QObject *parent = nullptr, ITestBoard* test_board_ = nullptr);
  ~CTestBoardSerial();

  enum protocol_type{
    motor           = 1,
    bottom          = 2,
    sensor_cloud    = 3,
    transBoard      = 4,
  };

  bool            m_single_receive;

  void device_change_interface(ITestBoard* test_board_); //设备接口调整
  void task_id_change(const uint8_t &id);
private:
  int8_t          m_task_id;
  ITestBoard      *mc_test_board_ = nullptr;

public slots:
  void loop(const bool &is_exc); //接收串口数据API
};

#endif
