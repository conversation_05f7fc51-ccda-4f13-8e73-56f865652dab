#include "curveCustomPlot.h"

CCurveCustomPlot::CCurveCustomPlot(QCustomPlot &customPlot, const StFigureInfo &st_figure_info) :
    m_length(250)
{
    m_customPlot_ = &customPlot;
    mst_figure_info = st_figure_info;

    createFigure();
    QObject::connect(m_customPlot_, SIGNAL(mouseDoubleClick(QMouseEvent*)), this, SLOT(mouseDouClick(QMouseEvent *)));
}

CCurveCustomPlot::~CCurveCustomPlot()
{
    delete wavePacketText;
    delete mainRectRight;
    delete subRectLeft;
    delete subRectRight;
    delete m_curve_graph_;
}

void CCurveCustomPlot::createFigure() {
//    m_customPlot_->plotLayout()->clear();   // 清空默认的轴矩形 可以清空轴的所有内容
//    m_customPlot_->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    //@1主视图的轴
    QCPLayoutGrid *mainGrid = new QCPLayoutGrid;

    QCPAxis *main_key_Axis = m_customPlot_->xAxis; //(mainRectRight, QCPAxis::atLeft);
    QCPAxis *main_value_Axis = m_customPlot_->yAxis;

    mainRectRight = new QCPAxisRect(m_customPlot_, true); //可缩放
    subRectLeft = new QCPAxisRect(m_customPlot_, true);   // 不配置轴

    mainGrid->addElement(0, 0, mainRectRight); //layoutGrid 中添加 AxisRect

    //@3分配视图
    m_customPlot_->plotLayout()->addElement(mst_figure_info.local.start, mst_figure_info.local.end, mainGrid);     // 在第一行添加轴矩形

    /*2.1 主图*/
    main_key_Axis = mainRectRight->axis(QCPAxis::atBottom);
    main_value_Axis = mainRectRight->axis(QCPAxis::atLeft);

    main_key_Axis->setTickLabelRotation(60);     // 轴刻度文字旋转60度
    main_key_Axis->setVisible(true);
    main_key_Axis->setTickLabels(true);
    main_key_Axis->setLabel(mst_figure_info.key_label);
    main_key_Axis->setSubTicks(false);           // 不显示子刻度
    main_key_Axis->setTickLength(0, 4);          // 轴内外刻度的长度分别是0,4,也就是轴内的刻度线不显示
    main_key_Axis->setRange(mst_figure_info.key_range.start, mst_figure_info.key_range.end);               // 设置范围
    main_key_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    main_value_Axis->setRange(mst_figure_info.value_range.start, mst_figure_info.value_range.end);
    main_value_Axis->setPadding(35);             // 轴的内边距，可以到QCustomPlot之开始（一）看图解
    main_value_Axis->setLabel(mst_figure_info.value_label);
    main_value_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    m_curve_graph_ = new QCPGraph(main_key_Axis, main_value_Axis); //

    m_curve_graph_->setAntialiased(false); // 为了更好的边框效果，关闭抗齿锯
    m_curve_graph_->setName(mst_figure_info.title); // 设置柱状图的名字，可在图例中显示
    m_curve_graph_->setPen(QPen(QColor(0, 168, 140).lighter(130))); // 设置柱状图的边框颜色
    m_curve_graph_->setBrush(QColor(0, 168, 140));  // 设置柱状图的画刷颜色

    m_customPlot_->show();

    //customPlots = &customPlot;
}

void CCurveCustomPlot::mouseDouClick(QMouseEvent *event)
{
    Qt::MouseButton buttonType =  event->button();
    if(buttonType == Qt::MouseButton::LeftButton)
    {
        mainRectRight->axis(QCPAxis::atBottom)->setRange(0,m_length);
        mainRectRight->axis(QCPAxis::atLeft)->setRange(0,500);
    }
    else if(buttonType == Qt::MouseButton::RightButton)
    {
        subRectLeft->axis(QCPAxis::atBottom)->setRange(0,m_length);
        subRectLeft->axis(QCPAxis::atLeft)->setRange(0,100);
    }
    m_customPlot_->replot(QCustomPlot::rpQueuedReplot);
}

void CCurveCustomPlot::plotSampleCurve(QCPGraph* sub_graph, QVector<St2DiPoint> vec)
{
    if(m_length == 0) {
        QMessageBox::information(this, tr("curve 点数过少"), tr("重新获取数据"));
    }
    int length = vec.length() - 5;//2
    QVector<QCPGraphData> data(length);

    for(int i=0; i<length; i++) {
       data[i].key = vec.at(i).index;
       data[i].value = vec.at(i).value;
    }

    sub_graph->data()->set(data);
    //subGraphRando->data()->set(data);
    //customPlots->replot(QCustomPlot::rpQueuedReplot);
}

void CCurveCustomPlot::plotFitCurve(QVector<St2DiPoint> vec)
{
    int length = vec.length();
    QVector<QCPGraphData> data(length);

    for(int i=0; i<length; i++)
    {
       data[i].key = vec.at(i).index;
       data[i].value = vec.at(i).value;
    }
    m_customPlot_->replot(QCustomPlot::rpQueuedReplot);
}

/* @brief: 质心计算
 * @param_in: 光斑数据；质心计算模式；光斑处理模式
 * @out:质心
 */
#if 0
bool CCurveCustomPlot::dataHandle(const QByteArray &fg_data_original, const QByteArray &cycle_data_original, CentroidDistData* centroid_dist)
{
#else
QSharedPointer<QCPBarsDataContainer> CCurveCustomPlot::dataHandle(const QVector<uint16_t> &data)
{
#endif
    QString str;
    int maxIndex=0, length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;

    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);
//    CentroidDistData* centroid_dist = new CentroidDistData;
//    centroid_dist->cycleHandleData = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

//    if(data1.length() < 4)
//    {
//        return ;
//    }
    length = data.length();
    //1.0
    barData.resize(length);
    for(int i=0; i<length; i++)
    {
//        centroid_dist->fgHandled[i].key = i;
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

//    if(facula_data_original.length() == (int)m_length)
//    {
//        m_centroid = centroidPos;
//    }

    return handled_data;
}

QSharedPointer<QCPBarsDataContainer> CCurveCustomPlot::dataHandle1(uint16_t start_index, const QVector<uint16_t> &data)
{
    QString str;
    int  length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;

    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

    length = data.length();

    barData.resize(length);
    for(int i=start_index; i<length; i++)
    {
//        uint key_tmp = round((float)1000/i*100);
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

    return handled_data;
}

/* @brief: 光斑显示；文本框；质心显示
 * @param_in:
 * @out:
 */
void CCurveCustomPlot::customPlotShow(QVector<double> x, QVector<double> y)
{
    QString str;

    m_curve_graph_->setData(x, y);//绘制fg曲线图

    m_customPlot_->replot(QCustomPlot::rpQueuedRefresh); //更新图表
}

/**
 * @brief: 余晖模式下文本内容显示
 * @param: wavePointText 文本框
 * @param: centroid_arr 位置index 质心数据
 * @param: dist_arr 位置index 距离数据
 */
//void CCurveCustomPlot::wavePointTextShow(const uint8_t &index, QCPItemText **wavePointText, QPen *pen, QVector<double> centroid_arr, QVector<uint16_t> dist_arr, float position_offset, CentroidDistData* centroid_dist)
//{
////    uint8_t centroid_arr_num = sizeof (centroid_arr)/sizeof(double);
////    uint8_t dist_arr_num = sizeof (dist_arr)/sizeof(uint16_t);
//    uint8_t centroid_arr_num =centroid_arr.length();
//    uint8_t dist_arr_num = dist_arr.length();
//    if(centroid_arr_num != dist_arr_num)
//    {
//        //报错
//        QMessageBox::warning(this,tr("质心数量:" + centroid_arr_num),tr("距离数量：" + dist_arr_num));
//        return;
//    }
//    float centroid_sigma = StandardDeviation(centroid_arr, centroid_arr_num);
//    float dist_sigma = StandardDeviation(dist_arr, dist_arr_num); //QString::number(centroid_sigma,'g', -1)
//    float centr_sum = 0, centr_mean, dist_sum = 0, dist_mean;
//    for(uint8_t i =0; i< centroid_arr_num; ++i)
//    {
//        centr_sum += centroid_arr[i];
//        dist_sum += dist_arr[i];
//    }
//    centr_mean = centr_sum/centroid_arr_num;
//    dist_mean = dist_sum/dist_arr_num;

//    if(*wavePointText == NULL)
//    {
//        *wavePointText = new QCPItemText(m_customPlot_);
//        (*wavePointText)->setPositionAlignment(Qt::AlignTop|Qt::AlignLeft); //文字布局：顶、左对齐
//        (*wavePointText)->position->setType(QCPItemPosition::ptAxisRectRatio); //位置类型（当前轴范围的比例为单位/实际坐标为单位）
//        (*wavePointText)->position->setCoords(0.85, 0.02 + position_offset); //把文字框放在X轴的中间，Y轴的最顶部
//        (*wavePointText)->setFont(QFont("仿宋",6,QFont::Bold)); //字体大小
//        (*wavePointText)->setColor(pen->color()); //
//        (*wavePointText)->setPen(*pen); //
//        (*wavePointText)->setPadding(QMargins(2,2,2,2)); //文字距离边框几个像素
//    }
//    (*wavePointText)->setText("centr_mean:" + QString::number(centr_mean,'f',2) + " centr_σ:" + QString::number(centroid_sigma,'f',2) +
//            "\ndist_max:" + QString::number(centroid_dist->dist_max[1],10) + " dist_min:" + QString::number(centroid_dist->dist_min[1],10) +
//            "\ndist_mean:" + QString::number(dist_mean,'f', 2) + " dist_σ:" + QString::number(dist_sigma,'f', 2));
//}

void CCurveCustomPlot::subCentroidShow(QCPGraph** subCentroidGraph, QPen *pen, QVector<QCPGraphData> dataCentroidSca)
{
    if(*subCentroidGraph == NULL)
    {
        *subCentroidGraph = m_customPlot_->addGraph(subRectLeft->axis(QCPAxis::atBottom), subRectLeft->axis(QCPAxis::atLeft));
        (*subCentroidGraph)->setPen(*pen);
        (*subCentroidGraph)->setLineStyle(QCPGraph::lsNone); //曲线样式
        (*subCentroidGraph)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle,2)); //点空心
        (*subCentroidGraph)->setName(QString::fromLocal8Bit("实时质心"));
        (*subCentroidGraph)->rescaleKeyAxis();
    }
    (*subCentroidGraph)->data()->set(dataCentroidSca); //绘制散点图 余晖
}
