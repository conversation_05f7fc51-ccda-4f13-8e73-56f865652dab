# Linux 兼容性指南

## 概述

本 MCP 服务器已经过跨平台兼容性优化，支持在 Linux 系统上运行。本指南提供了在 Linux 环境中部署和使用的详细说明。

## 🔧 系统要求

### 基础要求
- **Python**: 3.8+ (推荐 3.10+)
- **操作系统**: Ubuntu 18.04+, CentOS 7+, 或其他主流 Linux 发行版
- **内存**: 最少 512MB，推荐 1GB+
- **磁盘空间**: 最少 100MB

### 系统依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv graphviz

# CentOS/RHEL/Fedora
sudo yum install python3 python3-pip graphviz
# 或者 (较新版本)
sudo dnf install python3 python3-pip graphviz

# Arch Linux
sudo pacman -S python python-pip graphviz
```

## 📦 安装步骤

### 1. 创建虚拟环境
```bash
# 创建项目目录
mkdir -p ~/Projects/mcp-server
cd ~/Projects/mcp-server

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate
```

### 2. 安装依赖
```bash
# 升级 pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境变量
```bash
# 创建环境配置文件
cat > .env << EOF
# 项目配置
PROJECT_ROOT=/home/<USER>/Projects/mcp-server
PYTHONPATH=/home/<USER>/Projects/mcp-server

# 编码配置
LANG=en_US.UTF-8
LC_ALL=en_US.UTF-8
PYTHONIOENCODING=utf-8
EOF

# 加载环境变量
source .env
```

## 🔄 跨平台兼容性特性

### 1. 智能路径检测
- ✅ 自动检测 Linux 用户目录结构
- ✅ 支持 `~/Documents`, `~/Projects` 等常见位置
- ✅ 使用 `pathlib.Path` 确保路径兼容性

### 2. 编码处理
- ✅ UTF-8 编码支持
- ✅ 平台特定的编码设置
- ✅ 文件系统编码自动检测

### 3. 依赖库兼容性
- ✅ 所有核心依赖都支持 Linux
- ✅ 可选依赖的优雅降级
- ✅ 系统级依赖的自动检测

## 🚀 启动服务器

### 方法 1: 直接启动
```bash
cd /path/to/mcp-server
source venv/bin/activate
python server_silent.py
```

### 方法 2: 使用 systemd 服务
```bash
# 创建服务文件
sudo tee /etc/systemd/system/mcp-server.service > /dev/null << EOF
[Unit]
Description=MCP Server for Product Development
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/home/<USER>/Projects/mcp-server
Environment=PATH=/home/<USER>/Projects/mcp-server/venv/bin
ExecStart=/home/<USER>/Projects/mcp-server/venv/bin/python server_silent.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable mcp-server
sudo systemctl start mcp-server
```

## 🔍 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 确保文件权限正确
chmod +x server_silent.py
chmod -R 755 tools/

# 检查目录权限
ls -la ~/Projects/mcp-server
```

#### 2. 依赖安装失败
```bash
# 更新系统包管理器
sudo apt update  # Ubuntu/Debian
sudo yum update   # CentOS/RHEL

# 安装编译工具
sudo apt install build-essential python3-dev  # Ubuntu/Debian
sudo yum groupinstall "Development Tools"     # CentOS/RHEL
```

#### 3. Graphviz 问题
```bash
# 验证 graphviz 安装
which dot
dot -V

# 如果缺失，重新安装
sudo apt install graphviz graphviz-dev  # Ubuntu/Debian
sudo yum install graphviz graphviz-devel # CentOS/RHEL
```

#### 4. 路径检测问题
```bash
# 手动设置项目根目录
export PROJECT_ROOT=/home/<USER>/Projects/mcp-server

# 验证路径检测
python3 test_cross_platform.py
```

### 日志检查
```bash
# 查看服务日志
journalctl -u mcp-server -f

# 查看应用日志
tail -f mcp_server_debug.log
```

## 🧪 测试验证

### 运行兼容性测试
```bash
# 跨平台兼容性测试
python3 test_cross_platform.py

# 智能参数管理测试
python3 test_smart_params.py
```

### 验证功能
```bash
# 测试项目初始化
python3 -c "
import asyncio
from tools.project_tools import init_project
asyncio.run(init_project('test_linux', '', 'single_layer'))
"
```

## 📋 性能优化

### 1. 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
```

### 2. Python 优化
```bash
# 使用 Python 优化模式
export PYTHONOPTIMIZE=1

# 禁用调试模式（生产环境）
export PYTHONDEBUG=0
```

## 🔐 安全建议

1. **文件权限**: 确保敏感文件权限设置正确
2. **用户隔离**: 使用专用用户运行服务
3. **网络安全**: 配置防火墙规则
4. **日志管理**: 定期清理和轮转日志文件

## 📞 支持

如果在 Linux 环境中遇到问题：

1. 首先运行 `test_cross_platform.py` 诊断
2. 检查系统日志和应用日志
3. 确认所有系统依赖已正确安装
4. 验证环境变量配置

---

**注意**: 本服务器已经过 Windows 和 Linux 环境测试，具有良好的跨平台兼容性。
