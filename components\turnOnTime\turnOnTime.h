#ifndef TURNONTIME_H
#define TURNONTIME_H

#include <QDockWidget>
#include <QTimer>
#include <QMessageBox>
#include "motorMonitorSerial.h"
#include <qstatusbar.h>

#include "motorMonitorCustPlot.h"
#include "turnOnTimeOperation.h"

namespace Ui {
class turnOnTime;
}

class turnOnTime : public QDockWidget
{
    Q_OBJECT

public:
    explicit turnOnTime(QWidget *parent = nullptr);
    ~turnOnTime();

    signals:
    void turnOnTimeCloseSignal(bool);
    void subSerialLoop(int task_id);

private slots:

    void on_startButton_clicked();

    void on_portBox_currentIndexChanged(int index);

    void on_transPortBox_currentIndexChanged(int index);

    void on_id_textEdited(const QString &arg1);

private:
    /*1. 任务管理*/
    typedef struct{
        uint16_t                            cur_step;
        uint16_t                            cur_status;
        uint                                step_time_limit;
        uint                                step_time_cnt;
        QElapsedTimer                       step_timer_cnt;
        uint16_t                            cur_step_time;
        QElapsedTimer                       process_timer_cnt;
        uint16_t                            process_time;
        uint8_t                             cur_task_id;
    } ST_STATUS_REC; //阻塞任务

    typedef union{
        uint16_t flag;
        struct{
            uint16_t                        data_rec            :1; //数据接收
            uint16_t                        deviec_start        :1; //获取设备状态
            uint16_t                        device_moved        :1; //移动结束
            uint16_t                        data_handled        :1; //数据处理完毕
            uint16_t                        comm_cnt            :1;
        } status;
    } U_STATUS_FLAG;

    /*2. 通信状态*/
    typedef struct{
        uint                                check_error_cnt;
        uint                                timeout_cnt;
    } ST_COMM_STATUS;

    /*3. 功能*/
    enum function_mode{
        motor_monitor, //电机转速监控
        lidar_monitor, //雷达转速监控
    };

    enum speed_state{
        start_state             = 0x0001,
        stable_state            = 0x0002,
        change_state            = 0x0004,
        down_state              = 0x0008,
    };

    /*4. 结果*/

    Ui::turnOnTime *ui;
    motorMonitorSerial* m_speedMonitorSerial_;
    U_STATUS_FLAG *m_status_flag_;
    ST_TASK_CNT *m_task_cnt;
    QThread* speedMonitorSub_ = nullptr;
    motorMonitorCustPlot *motorMonitorPlot = nullptr;
    int m_timerId;
    QByteArray m_start_cmd;
    QString m_port_name;
    QString m_transBoard_name;

    static const unsigned char m_transform_board_off[5]; //转接板断电
    static const unsigned char m_transform_board_on[5]; //转接板上电
    static const unsigned char m_turn_on[4];
    static const unsigned char m_turn_off[4];
    static const unsigned char m_read_cmdBuffer[4];
    static const QString m_red_SheetStyle;
    static const QString m_green_SheetStyle;
    static const QString m_grey_SheetStyle;
    static const QString m_yellow_SheetStyle;

    uint8_t m_function_select;
    QMap<QString, int> m_xml_param;
    ST_STATUS_REC *m_stStatus_rec_ = nullptr;
    ST_MOTOR_CONFIG *mst_config_ = nullptr; //
    ST_RESULT_DATA *mst_start_timer_data_ = nullptr;
    ST_RESULT_SHOW *mst_result_ = nullptr;
    ST_COMM_STATUS *m_bottom_comm_status = nullptr;

    bool on_portSwitch_clicked();
    void varibleInit(); //变量初始化
    void errorHandle(); //异常情况处理
    void deleteNode(ST_LIST_NODE* head, int data);
    void insertNode(ST_LIST_NODE* head, int data);
    void transB_turn_on(void);
    void bottomB_turn_on(void);
    void cloud_receive(void);
    void bottomB_turn_off(void);
    void transB_turn_off(void);
    void process_register(const function_mode &mode, const uint16_t &stage);
    void process_loop(uint16_t &step, uint16_t &status);
    void statusBarUpdate(monitor_process_two::PROCESS_STEP step, monitor_process_two::STEP_STATUS status);
    void dataHandle(speed_state state, const uint16_t &time, ST_RESULT_DATA *timer_data); //数据处理
    void lidar_speed_dataHandle();
    bool resultCal(ST_RESULT_DATA *motor_data);
    void resultShow(speed_state state, const ST_RESULT_DATA &timer_data); //结果显示
    void startCmdSend();
    inline void threadDelayMs(const uint16_t &ms);
    inline void comm_start(const uint16_t &time_limit_10ms);

    virtual void timerEvent(QTimerEvent *event) override;
    virtual void closeEvent( QCloseEvent * event) override;
};

#endif // MOTORMONITORWIDGET_H
