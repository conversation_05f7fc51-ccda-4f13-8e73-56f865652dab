#include "novaCalibration.h"
#include "ui_novacalibration.h"
#include <QMenu>
#include <QThread>


#define CMD_TABLE_LINE_NUM      20

CNovaCalibration::CNovaCalibration(QWidget *parent) :
    Q<PERSON><PERSON><PERSON><PERSON>ow(parent)
  , ui(new Ui::CNovaCalibration)
  , mst_config_(new NNovaCalibOpt::StUiConfig)
  , mc_operation_(new CNovaCalibOpt(*mst_config_))
{
    //-------------------------------------- ui init -------------------------------
    ui->setupUi(this);
    ui->projectSelect->setCurrentIndex(0); //Nova-A1
    mst_config_->project = (IPms::EProjectName)ui->projectSelect->currentIndex();

    //* ui init
    //  QMenu *fileMenu = new QMenu(tr("&File"), this);
    //  QAction *newAction = fileMenu->addAction(tr("&New..."));
    //  newAction->setShortcuts(QKeySequence::New);

    const QString cmd_line_sheetStyle = "QLineEdit{"
                                        "min-width: 500px;"
                                        "min-height: 25px;"
                                        "border-radius: 6px;"
                                        "border:2px solid black;"
                                        "background: rgb(255,255,255)"
                                        "color: rgb(25, 25, 25);}"; /*浅黑*/
    //                                        "text-align: right;}";

    const QString cmd_pushbnt_sheetStyle = "QPushButton{"
                                           "min-width: 40px;"
                                           "min-height: 25px;"
                                           "max-width: 40px; "
            //                                           "max-height: 16px;"
            "background-color: rgb(255,255,255);"
            "color: rgb(25, 25, 25);"
            "border: 2px solid black;"
            "border-radius: 6px;}"

            "QPushButton:hover{"
            "background-color: rgb(25, 25, 25);"
            "color: rgb(255,255,255);"
            "border: 2px solid white;}";

    const QString priority_line_sheetStyle = "QLineEdit{"
                                             "min-width: 30px;"
                                             "min-height: 50px;"
                                             "max-width: 30px;"
                                             "border-radius: 6px;"
                                             "border: 2px solid black;"
                                             "background: rgb(25,25,25)"
                                             "color: rgb(25, 25, 25);}"; /*浅黑*/
    //                                             "text-align: right;}";


    //***************************************** test items list init ***************************
    //setCentralWidget(ui->finalTestView);
    m_table_model_ = new QStandardItemModel(this);//数据模型
    m_select_model_ = new QItemSelectionModel(m_table_model_);//选择模型
    ui->finalTestView->setModel(m_table_model_);//数据模型
    ui->finalTestView->setSelectionModel(m_select_model_);//选择模型

    //* 外部excel获取数据
    //    int rowcount = filedata.count();
    //    m_table_model_->setRowCount(rowcount-1);
    //    QString header = filedata.at(0);
    //    QStringList headerList=header.split(QRegExp("\\s+"),QString::SkipEmptyParts);

    //* add header
    m_header_list.append("测试");
    m_header_list.append("测试项");
    m_header_list.append("合格条件");
    m_header_list.append("标准差");
    m_header_list.append("测试值");
    m_header_list.append("误差");
    m_table_model_->setHorizontalHeaderLabels(m_header_list);

    //* TEST ITEMS
    QStandardItem   *item_;

    //* STANDARD VALUES


    //    int i,j;
    //    for (i=1;i<rowcount;i++) {
    //        oneRowTextlist = filedata.at(i); //从filedata中获取一行的内容
    //        contentList=oneRowTextlist.split(QRegExp("\\s+"), QString::SkipEmptyParts);//一个或多个空格、TAB等分隔符隔开的字符串分解为多个字符串
    //        for (j = 0; j < 6; j++)
    //        {
    //            mitem = new QStandardItem(contentList.at(j));//创建item
    //            m_table_model_->setItem(i - 1, j, mitem); //为模型的i行j列位置设置Item
    //        }
    //    }

    //***************************** connect ***********************
    connect(this, &CNovaCalibration::openSerial,
            mc_operation_, &CNovaCalibOpt::openSerial_slot);
    connect(this, &CNovaCalibration::closeSerial,
            mc_operation_, &CNovaCalibOpt::closeSerial_slot);
    connect(mc_operation_, &CNovaCalibOpt::serialShowSignal,
            this, &CNovaCalibration::serialShow_slot);

    //* tmp connect for calibration
    connect(this, &CNovaCalibration::calib1SingleTmpSignal,
            mc_operation_, &CNovaCalibOpt::calib1SingleTmp_slot);
    connect(this, &CNovaCalibration::calib2SingleTmpSingnal,
            mc_operation_, &CNovaCalibOpt::calib2SingleTmp_slot);
    connect(this, &CNovaCalibration::triggerTestTmpSignal,
            mc_operation_, &CNovaCalibOpt::emptyTriggerTmp_slot);


    connect(mc_operation_, &CNovaCalibOpt::portUpdateSignal,
            this, &CNovaCalibration::portListShow_slot);
    //    connect(mc_operation_, &CNovaCalibOpt::serialShowSignal,
    //            this, &CNovaCalibration::serialShow_slot);
    //    connect(mc_operation_, &CNovaCalibOpt::serialShowSignal,
    //            this, &CNovaCalibration::serialShow_slot);
    //    connect(mc_operation_, &CNovaCalibOpt::serialShowSignal,
    //            this, &CNovaCalibration::serialShow_slot);

    //*
    connect(mc_operation_, &CNovaCalibOpt::calibTasksViewSignal,
            this, &CNovaCalibration::calibTableShow_slot);
    connect(mc_operation_, &CNovaCalibOpt::accuracyVerifyTasksViewSignal,
            this, &CNovaCalibration::verifyTableShow_slot);
    connect(mc_operation_, &CNovaCalibOpt::functionTestTmpSignal,
            this, &CNovaCalibration::functionTestShow_slot);

    //*


}

CNovaCalibration::~CNovaCalibration()
{
    delete ui;

    delete m_table_model_;
    delete mst_config_;
    delete mc_operation_;

    //    for (uint8_t i = 0; i < mv_cmd_view.size(); ++i) {
    //        delete mv_cmd_view[i]->is_hex_check_;
    //        delete mv_cmd_view[i]->line_cmd_;
    //        delete mv_cmd_view[i]->send_btn_;
    //        delete mv_cmd_view[i]->priority_;
    //        delete mv_cmd_view[i]->interval_time_;
    //        delete mv_cmd_view[i];
    //    }
    //    mv_cmd_view.clear();
    //    mv_cmd_view.shrink_to_fit();
}

void CNovaCalibration::closeEvent( QCloseEvent * event)
{
    Q_UNUSED(event);

    this->setAttribute(Qt::WA_DeleteOnClose); //释放窗口资源
    QThread::msleep(50);
    emit windowCloseSiganl(true);
}


void CNovaCalibration::on_DeviceSelect_currentIndexChanged(int index)
{
    switch (index) {
    case 0:

        break;
    case 1:
        break;

    }
}

void CNovaCalibration::processStatusShow_slot(const int16_t &step, const int16_t &status) {

}

void CNovaCalibration::serialShow_slot(bool is_open) {
    if(is_open) {
        ui->serialOpen->setText("close"); //
    }
    else {
        ui->serialOpen->setText("open"); //
    }
}

void CNovaCalibration::portListUpdate(QComboBox* port_box_, QStringList *port_list_, QString cur_port_name) {
    bool port_flag = false;
    /*列表刷新*/
    for(QList<QString>::Iterator it = port_list_->begin(); it != port_list_->end(); it++) {
        if((*it) == cur_port_name) port_flag = true;
    }


    port_box_->blockSignals(true); //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    port_box_->clear(); //会触发currentIndexChanged回调函数
    port_box_->addItems(*port_list_); //会触发消息事件

    if(!port_flag) port_box_->setCurrentIndex(0);
    else port_box_->setCurrentText(cur_port_name);

    port_box_->blockSignals(false);
}

void CNovaCalibration::portListShow_slot(QStringList *port_list_) {
    portListUpdate(ui->novaPort, port_list_, mst_config_->cur_port_name);
    portListUpdate(ui->codeScannerPort, port_list_, mst_config_->cur_codeScan_port_name);
    portListUpdate(ui->boardPort, port_list_, mst_config_->cur_stepMotor_port_name);
    portListUpdate(ui->currentPort, port_list_, mst_config_->cur_current_port_name);

}

//void CNovaCalibration::portListShow_slot(QStringList *port_list_, bool port_flag) {
//    ui->novaPort->blockSignals(true); //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

//    ui->novaPort->clear(); //会触发currentIndexChanged回调函数
//    ui->novaPort->addItems(*port_list_); //会触发消息事件

//    if(!port_flag) ui->novaPort->setCurrentIndex(0);
//    else ui->novaPort->setCurrentText(mst_config_->cur_port_name);

//    ui->novaPort->blockSignals(false);
//}

//void CNovaCalibration::codeScanPortListShow_slot(QStringList *port_list_, bool port_flag) {
//    ui->codeScannerPort->blockSignals(true); //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

//    ui->codeScannerPort->clear(); //会触发currentIndexChanged回调函数
//    ui->codeScannerPort->addItems(*port_list_); //会触发消息事件

//    if(!port_flag) ui->codeScannerPort->setCurrentIndex(0);
//    else ui->codeScannerPort->setCurrentText(mst_config_->cur_codeScan_port_name);

//    ui->codeScannerPort->blockSignals(false);
//}


//void CNovaCalibration::stepMotorPortShow_slot(QStringList *port_list_, bool port_flag) {
//    ui->boardPort->blockSignals(true); //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

//    ui->boardPort->clear(); //会触发currentIndexChanged回调函数
//    ui->boardPort->addItems(*port_list_); //会触发消息事件

//    if(!port_flag) ui->boardPort->setCurrentIndex(0);
//    else ui->boardPort->setCurrentText(mst_config_->cur_stepMotor_port_name);

//    ui->boardPort->blockSignals(false);
//}

//void CNovaCalibration::currentPortShow_slot(QStringList *port_list_, bool port_flag) {
//    ui->currentPort->blockSignals(true); //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

//    ui->currentPort->clear(); //会触发currentIndexChanged回调函数
//    ui->currentPort->addItems(*port_list_); //会触发消息事件

//    if(!port_flag) ui->currentPort->setCurrentIndex(0);
//    else ui->currentPort->setCurrentText(mst_config_->cur_current_port_name);

//    ui->currentPort->blockSignals(false);
//}

void CNovaCalibration::calibTableShow_slot(const uint8_t &calib_index, const uint16_t &calib_value1, const uint16_t &calib_value2) {
    switch (calib_index) {
    case 1:
        ui->processView->setText("xtalk/ peak:" + QString::number(calib_value1, 10) + " tof: " + QString::number(calib_value2, 10));
        break;
    case 2:
        ui->processView->setText("ref/ peak:" + QString::number(calib_value1, 10) + " tof: " + QString::number(calib_value2, 10));
        break;
    default:
        break;
    }
}

void CNovaCalibration::verifyTableShow_slot() {

}

void CNovaCalibration::functionTestShow_slot(const uint8_t &cnt, const int16_t &dist, const int16_t &ref_dist) {
    ui->processView->setText("trigger test/ cnt:" + QString::number(cnt, 10) + "dist: " + QString::number(dist, 10) + " ref_dist: " + QString::number(ref_dist, 10));
}

void CNovaCalibration::chipIdShow_slot(const QByteArray &id) {

}

void CNovaCalibration::resultShow_slot() {

}

/**
 * @brief 配置更新
 */
void CNovaCalibration::updateConfig(NNovaCalibOpt::StUiConfig *config_) {
    config_->port_name = ui->novaPort->currentText();
    config_->cur_port_name = ui->novaPort->currentText();
    config_->codeScan_port_name = ui->codeScannerPort->currentText();
    config_->cur_codeScan_port_name = ui->codeScannerPort->currentText();
    config_->stepMotor_port_name = ui->boardPort->currentText();
    config_->cur_stepMotor_port_name = ui->boardPort->currentText();
    config_->current_port_name = ui->currentPort->currentText();
    config_->cur_current_port_name = ui->currentPort->currentText();
}

void CNovaCalibration::on_serialOpen_clicked() {
    if(ui->serialOpen->text() == QString("open")) {//
        updateConfig(mst_config_); //配置更新
        if(mst_config_->port_name == mst_config_->codeScan_port_name ||\
                mst_config_->port_name == mst_config_->stepMotor_port_name ||\
                mst_config_->port_name == mst_config_->current_port_name ||\
                mst_config_->codeScan_port_name == mst_config_->stepMotor_port_name ||\
                mst_config_->codeScan_port_name == mst_config_->current_port_name ||\
                mst_config_->stepMotor_port_name == mst_config_->current_port_name) {
            QMessageBox::information(this, "error", "port can't be the same");

            //* tmp use
            emit openSerial(); //

            return;
        }
        else{
            emit openSerial(); //
        }
    }
    else {//关闭->退出
        emit closeSerial();
    }
}

void CNovaCalibration::on_novaPort_currentIndexChanged(int index)
{
    Q_UNUSED(index);
    mst_config_->cur_port_name = ui->novaPort->currentText();
}

void CNovaCalibration::on_currentPort_currentIndexChanged(int index)
{
    Q_UNUSED(index);
    mst_config_->cur_current_port_name = ui->currentPort->currentText();

}

void CNovaCalibration::on_boardPort_currentIndexChanged(int index)
{
    Q_UNUSED(index);
    mst_config_->cur_stepMotor_port_name = ui->boardPort->currentText();

}

void CNovaCalibration::on_codeScannerPort_currentIndexChanged(int index)
{
    Q_UNUSED(index);
    mst_config_->cur_codeScan_port_name = ui->codeScannerPort->currentText();

}

void CNovaCalibration::on_reservePort_currentIndexChanged(int index)
{

}

void CNovaCalibration::on_start_clicked()
{
    if(ui->serialOpen->text() != QString("open")) {

        if(ui->start->text() == QString("start")) {//
            ui->start->setText("running");

            mst_config_->project = (IPms::EProjectName)ui->projectSelect->currentIndex();
            mc_operation_->mv_task_list[CNovaCalibOpt::eREADY].flag.exec = true; //开始任务
        }
        else {//exit
            mc_operation_->mv_task_list[CNovaCalibOpt::eCOMPLETE].flag.stop = true; //结束任务
            emit closeSerial();
        }
    }
    else {
        QMessageBox::information(this, "error", "pls open port first");
        return;
    }
}

void CNovaCalibration::on_calib1_tmp_clicked()
{
    if(ui->serialOpen->text() != QString("open")) {
//        mc_operation_->mv_task_list[CNovaCalibOpt::eREADY].flag.exec = true; //开始任务
        emit calib1SingleTmpSignal();
    }
}

void CNovaCalibration::on_calib2_tmp_clicked()
{
    emit calib2SingleTmpSingnal();
}

void CNovaCalibration::on_trigger_tmp_clicked()
{
    emit triggerTestTmpSignal();
}
