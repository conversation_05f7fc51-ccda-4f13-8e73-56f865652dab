#include "tableViewModule.h"
#include "qLog.h"
#include <QMessageBox>
#include <QMetaEnum>
#include <QHeaderView>


CTableViewModule::CTableViewModule(QTableWidget* table_, const StTableInfo &table_info) {
    /*1.ui init*/
    m_table_ = table_;
    mst_table_info = table_info;

    tableInit();

    mv_table_item.resize(0);
    //* 3.信号与槽
    //连接信号与槽，showSlot()是自定义的槽函数!
}

CTableViewModule::~CTableViewModule() {
    uint8_t item_lens = mv_table_item.length();
    if(item_lens != 0) {
        for(uint i = 0; i < item_lens; i++) {
            if(mv_table_item.at(i) != nullptr)
                delete mv_table_item.at(i);
        }
    }
}

void CTableViewModule::tableInit()
{
    m_table_->setRowCount(mst_table_info.y);
    m_table_->setColumnCount(mst_table_info.x);
    m_table_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch); //列自适应cell大小
    m_table_->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch); //行自适应cell大小
    m_table_->horizontalHeader()->setVisible(false); //隐藏水平表头
    m_table_->verticalHeader()->setVisible(false); //隐藏垂直表头
    m_table_->setEditTriggers(QAbstractItemView::NoEditTriggers); //表格不可编辑
    m_table_->setFont(QFont("黑体", 15));
}

void CTableViewModule::targetFaculaAreaShow() { //const uint8_t &tf_x, const uint8_t &tf_y, const bool &is_left_2_right
    /*4. 目标区域凸显 (0,0),(1,0),(0,1),(-1,0),(0,-1)*/
    volatile const uint8_t xlen = mst_table_info.x, ylen = mst_table_info.y;
    volatile const uint8_t x_tf = mst_table_info.target_mp_x, y_tf = mst_table_info.target_mp_y;
    int8_t coordinate_x = 0, coordinate_y = 0;
    uint8_t tip_num = 0;

    /*释放内存*/
    uint8_t item_lens = mv_table_item.length();
    if(item_lens != 0) {
        for(uint i = 0; i < item_lens; i++) {
            if(mv_table_item.at(i) != nullptr)
                delete mv_table_item.at(i);
        }
    }
    mv_table_item.clear();

    for (uint y = 0; y < ylen; y++) {
//            if(y > y_tf)
//                coordinate_y = y - y_tf;
//            else
            coordinate_y = y_tf - y;
            coordinate_y = coordinate_y>0? coordinate_y:(-coordinate_y);
        for (uint x = 0; x < xlen; x++) {
//                if(x > x_tf)
//                    coordinate_x = x - x_tf;
//                else
            coordinate_x = x_tf - x;
            coordinate_x = coordinate_x>0? coordinate_x:(-coordinate_x);

            QTableWidgetItem *item = new QTableWidgetItem();
            item->setForeground(Qt::yellow); //darkred
            item->setFont(QFont("Times", 20, QFont::Black)); //加粗
            if((coordinate_x <= 1) && (coordinate_y <= 1) && ((coordinate_x + coordinate_y) <= 1)) {
                item->setForeground(Qt::green);
                item->setFont(QFont("Times", 26, QFont::Black)); //加粗
            }
            item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);

            /*2. MP提示值*/
            if(mst_table_info.mp_order == left_2_right) //左往右
                tip_num = y*xlen + x + 1;
            else
                tip_num = x*ylen + y + 1;
            item->setToolTip(QString::number(tip_num)); //提示 VI4300
            mv_table_item.push_back(item);
            m_table_->setItem(y, x, item);
        }
    }
}

/*
 * @BRIEF: 数据显示
 */
void CTableViewModule::greyMapShow(const uint &max, const QVector<QVector<uint32_t>> &matrix)
{
    uint8_t alpha_tmp = 180, rgb_tmp;
    uint32_t data_tmp = 0;

    uint8_t xlens = mst_table_info.x;
    uint8_t ylens = mst_table_info.y;
    uint32_t data_max = max > 10? max:10; //peak 最小值

    for (int y = 0; y < ylens; ++y) {
        for (int x = 0; x < xlens; ++x) {
            /*1. 数值 update*/
            data_tmp = matrix.at(y).at(x);
            mv_table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

            /*3. 背景颜色*/
            QColor color;
            rgb_tmp = 255 - (data_tmp * 255 / data_max);
            alpha_tmp = data_tmp * 255 / data_max;
            color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
            m_table_->item(y,x)->setBackground(QBrush(color));
        }
    }
}
