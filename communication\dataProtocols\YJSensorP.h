#ifndef _YJSENSOR_P_H_
#define _YJSENSOR_P_H_

#include <QString>
#include <QByteArray>
#include "IProtocol.h"

using namespace std;

class YJSensorP:public IProtocol
{
public:
    YJSensorP();
    ~YJSensorP();


    QByteArray getControlCmd(const char &id) override; //控制指令
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override; //写入数据
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override; //读指令

//    bool parseCloudProtocolSamSung(QByteArray str, int length);

    QByteArray m_strPre;

private:
    float m_angleLast;
    int m_pointNumPerCol;
    int m_protocolType;

};

#endif // YJProtocol_H
