#include "lensAdjust.h"
#include "qLog.h"

lensAdjust::lensAdjust(QCustomPlot *customPlot)
{
    m_customPlots = customPlot;

    customPlot->plotLayout()->clear();   // 清空默认的轴矩形 可以清空轴的所有内容
//    customPlot->setInteractions(QCP::iRangeDrag|QCP::iRangeZoom); // 这也将允许通过拖拽/缩放尺度改变颜色范围

    /*2.0 轴距形->网格->layout->*/
    QCPLayoutGrid *mainGrid = new QCPLayoutGrid;

    m_mainAxisRect = new QCPAxisRect(customPlot, true);
    mainGrid->addElement(0, 0, m_mainAxisRect); //layoutGrid 中添加 AxisRect
//    customPlot->axisRect()->setupFullAxesBox(true); //

    /*2.1 */
    customPlot->plotLayout()->addElement(0, 0, mainGrid);
    /*2.2 轴*/
    QCPAxis *main_key_Axis = customPlot->xAxis; //(m_mainAxisRect, QCPAxis::atLeft);
    QCPAxis *main_value_Axis = customPlot->yAxis;
    main_key_Axis2 = customPlot->xAxis2; //(m_mainAxisRect, QCPAxis::atLeft);
    QCPAxis *main_value_Axis2 = customPlot->yAxis2;
    main_key_Axis = m_mainAxisRect->axis(QCPAxis::atBottom);
    main_value_Axis = m_mainAxisRect->axis(QCPAxis::atLeft);
    main_key_Axis2 = m_mainAxisRect->axis(QCPAxis::atTop);
    main_value_Axis2 = m_mainAxisRect->axis(QCPAxis::atRight);

    main_key_Axis->setVisible(false);
    main_value_Axis->setVisible(true);
    main_key_Axis2->setVisible(true);
    main_value_Axis2->setVisible(false);

//    main_value_Axis->setTickLabelRotation(60);     // 轴刻度文字旋转60度
    main_value_Axis->setSubTicks(false);           // 不显示子刻度
    main_value_Axis->setRange(0, 100);               // 设置范围
    main_value_Axis->setTickLabels(false);          //不显示
    /*2.3 坐标轴刻度*/
//    main_value_Axis->setTickStep(1);                //
//    main_value_Axis->setAutoTickStep(false);        //设置轴刻度的一跳为1

//    main_key_Axis2->setTickLabelRotation(60);     // 轴刻度文字旋转60度
    main_key_Axis2->setSubTicks(false);           // 不显示子刻度
    main_key_Axis2->setRange(0, 100);               // 设置范围
    main_key_Axis2->grid()->setSubGridVisible(true);
    main_key_Axis2->setSubTicks(true);

    /*2.4 */
    mainMap = new QCPColorMap(main_key_Axis2, main_value_Axis);
    mainMap->setTightBoundary(true);

    customPlot->show();

    QObject::connect(customPlot, SIGNAL(mouseDoubleClick(QMouseEvent*)), this, SLOT(mouseDouClick(QMouseEvent *)));
}

lensAdjust::~lensAdjust()
{
    if(m_mainAxisRect != nullptr)
        delete m_mainAxisRect;
    delete mainMap;
    delete main_key_Axis2;
}

void lensAdjust::mouseDouClick(QMouseEvent *event)
{
    Qt::MouseButton buttonType =  event->button();
    if(buttonType == Qt::MouseButton::LeftButton)
    {
        m_mainAxisRect->axis(QCPAxis::atBottom)->setRange(0, 250);
        m_mainAxisRect->axis(QCPAxis::atLeft)->setRange(0,255);
    }
    m_customPlots->replot(QCustomPlot::rpQueuedReplot);
}

float lensAdjust::StandardDeviation(QVector<uint16_t> array, uint8_t n)
{
//    int n = sizeof(*array)/sizeof (uint16_t);
    double sigma = 0, pSigma = 0;
    for (int i = 0; i < n; ++i) {
        sigma += array[i];        // sum
        pSigma += array[i]*array[i];     // 平方和
    }
    sigma /= n;          // 获得平均值
    return qSqrt( (pSigma/n - sigma*sigma));
}

float lensAdjust::StandardDeviation(QVector<double> array,uint8_t n)
{
//    int n = sizeof (*array)/sizeof(double);
    double sigma = 0, pSigma = 0;
    for (int i = 0; i < n; ++i) {
        sigma += array[i];        // sum
        pSigma += (array[i])*(array[i]);     // 平方和
    }
    sigma /= n;          // 获得平均值
    return qSqrt((pSigma/n - sigma*sigma)) ;
}

/**
 * @brief: 质心计算
 * @param_in: 光斑数据；质心计算模式；光斑处理模式
 * @out:质心
 */
QSharedPointer<QCPBarsDataContainer> lensAdjust::dataHandle(const QVector<uint16_t> &data)
{
    QString str;
    int length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;
    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

    length = data.length();
    //1.0
    barData.resize(length);
    for(int i=0; i<length; i++)
    {
//        centroid_dist->fgHandled[i].key = i;
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

    return handled_data;
}

/* @brief: 光斑显示；文本框；质心显示
 * @param_in:
 * @out:
 */
void lensAdjust::customPlotShow(QVector<uint32_t> map_data)
{
    QString str;
    uint16_t xlens = 0, ylens = 0;
    uint8_t cell_num = map_data.length();
    QVector<double> label_pos;
    QVector<QString> label;
    if(cell_num == 8) //YC
    {
        xlens = 4;
        ylens = 2;
    }
    else if(cell_num == 18) //VI4300 3*6
    {
        xlens = 6;
        ylens = 3;
    }
    else if(cell_num == 25) //VI4302 5*5
    {
        xlens = 8;
        ylens = 4;
    }
    else
    {
        // qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-clens map num:" << map_data.length();
    }
    mainMap->data()->setSize(xlens, ylens);
    mainMap->data()->setRange(QCPRange(0, xlens), QCPRange(0, ylens));
//    mainMap->setDataRange(QCPRange(0,10));
    label_pos.resize(xlens);
    label.resize(xlens);
    QSharedPointer<QCPAxisTickerText> xTicker(new QCPAxisTickerText);
    for(uint8_t i = 0; i < xlens; ++i)
    {
        label_pos[i] = i+0.5;
        label[i] = QString::number(i + 1);
    }
    xTicker->setTicks(label_pos, label); //
    xTicker->setSubTickCount(1);
    main_key_Axis2->setTicker(xTicker);
//        main_key_Axis2->setRange(0, xlens);

    for (int x=0; x<xlens; ++x)
        for (int y=0; y<ylens; ++y)
            mainMap->data()->setCell(x, y, map_data.at((y<<2) + x));
    mainMap->setGradient(QCPColorGradient::gpGrayscale);
    mainMap->setInterpolate(false);         // 为了显示小方块，我们禁用插值
    mainMap->setTightBoundary(false);
    mainMap->rescaleDataRange(true);        // 重新缩放数据维度（颜色），以使所有数据点都位于颜色渐变显示的范围内
    m_customPlots->rescaleAxes();           //自适应大小
    m_customPlots->replot(QCustomPlot::rpQueuedRefresh); //更新图表
}
