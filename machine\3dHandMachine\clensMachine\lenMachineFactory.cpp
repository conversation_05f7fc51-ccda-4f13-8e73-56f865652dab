#include "lenMachineFactory.h"


CLenMachineFactory::CLenMachineFactory() {
    //* register class name
    // CMyPowerCtrlB::registerName();
    // CStepperMotor::registerName();

    //*
}

CLenMachineFactory::~CLenMachineFactory() {
}

// 注册
//    CPortFactory::StPortConfig CLenMachineFactory::getDevicePortConfig(const IPort::EPortType &port_type, const QString &device) {
//        ICommDevice *              device_             = nullptr;
//        CPortFactory::StPortConfig default_port_config = {IPort::eUART, {"", 19200}};

//        QPair<QString, QString> device_type_name = splitDeviceTypeAndName(device);

//        //* input: main devices -> sub device object
//        auto it = m_device_type.find(device);
//        if (it != m_device_type.end()) {
//            device_ = it.value()(nullptr, device_type_name.second);
//            if (device_) {
//                default_port_config = device_->getPortConfig();
//            } else {
//                LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("create %1 device error").arg(device));
//            }
//        } else {
//            LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("unknown device: %1").arg(device));
//        }

//        return default_port_config;
//    }

IClensMachine *CLenMachineFactory::lenMachineCreate(const QString &port_box_name, const QString &model_name) {
    //        IPort *port = CPortNode::getInstance().getPort(port_box_name);

    //        QPair<QString, QString> device_type_name = splitDeviceTypeAndName(model_name);

    //        //* input: main devices -> sub device object
    //        if (!port) {
    //            LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("failed to get port: %1").arg(port_box_name));
    //            return nullptr;
    //        }

    //        auto it = m_device_type.find(model_name);
    //        if (it != m_device_type.end()) {
    //            return it.value()(port, device_type_name.second);
    //            // LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("create %1 device error").arg(model_name));
    //            // device_->icomChangeInterface(CPortNode::getInstance().getPortInfo(port->getPortName()));
    //        } else {
    //            LOG_WARN(MyLogger::LogType::ERROR_LOG, QString("unknown device: %1").arg(model_name));
    //        }

    //        return nullptr;
}

IClensMachine *CLenMachineFactory::lenMachineCreate(const ELenMachine &machine, IComm *port_, const uint8_t &address) {
    IClensMachine *machine_ = nullptr;

    switch (machine) {
    case ELenMachine::eSHUN_TUO:
        machine_ = new CClensMachineST(port_, address);
        break;

    case ELenMachine::eQING_HE:
        machine_ = new CClensMachineQH(port_, address);
        break;

    case ELenMachine::eQING_HE_SHJ:
        machine_ = new CClensMachineQH_SHJ(port_, address);
        break;

    default:
        machine_ = new CClensMachineST(port_, address);
        break;
    }
    return machine_;
}

QStringList CLenMachineFactory::getDeviceModelList(void) const {
#if 0
    //* 根据本地配置

#else
    // 直接反馈全部
    qDebug() << "devicelist: " << m_device_model;

    return m_device_model;
#endif
}
