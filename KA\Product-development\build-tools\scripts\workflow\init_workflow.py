#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工作流组件初始化脚本
负责创建工作流相关的配置文件，支持完整的事件驱动工作流
"""

import os
import json
import argparse
import datetime
import subprocess
import sys
from pathlib import Path

def create_workflow_config(project_path, structure_type, scripts_base):
    """创建完整的工作流配置文件，包含组件和连接关系"""
    config_dir = os.path.join(project_path, 'config')
    os.makedirs(config_dir, exist_ok=True)
    
    # 生成完整的 workflow_config.json
    workflow_config = {
        "workflow": {
            "name": "产品开发工作流",
            "description": "完整的产品开发流程，支持事件驱动和自动化执行",
            "version": "1.0.0",
            "project_type": structure_type,
            "created_date": datetime.datetime.now().isoformat(),
            
            # 工作流组件定义
            "components": [
                {
                    "name": "需求导入",
                    "type": "input", 
                    "script": f"{scripts_base}/requirements/import_requirements.py",
                    "config": "requirements_import_config.json",
                    "description": "从外部系统导入需求文档"
                },
                {
                    "name": "需求分析",
                    "type": "process",
                    "script": f"{scripts_base}/requirements/analyze_requirements.py", 
                    "config": "requirements_analysis_config.json",
                    "description": "分析和分解需求"
                },
                {
                    "name": "方案设计",
                    "type": "process",
                    "script": f"{scripts_base}/design/generate_design.py",
                    "config": "design_config.json",
                    "description": "生成系统架构和技术方案"
                },
                {
                    "name": "开发实施", 
                    "type": "process",
                    "script": f"{scripts_base}/development/manage_development.py",
                    "config": "development_config.json",
                    "description": "硬件、固件、软件开发"
                },
                {
                    "name": "测试验证",
                    "type": "process", 
                    "script": f"{scripts_base}/quality/run_verification.py",
                    "config": "quality_config.json",
                    "description": "质量测试和验证"
                },
                {
                    "name": "生产准备",
                    "type": "process",
                    "script": f"{scripts_base}/production/prepare_production.py", 
                    "config": "production_config.json",
                    "description": "生产BOM和流程准备"
                },
                {
                    "name": "项目输出",
                    "type": "output",
                    "script": f"{scripts_base}/deliverables/generate_deliverables.py",
                    "config": "deliverables_config.json",
                    "description": "生成最终交付物"
                }
            ],
            
            # 工作流连接关系和事件触发
            "connections": [
                {
                    "from": "需求导入",
                    "to": "需求分析",
                    "triggers": [
                        {
                            "name": "需求文档导入完成",
                            "type": "event",
                            "handlers": [
                                {
                                    "type": "mcp_server",
                                    "name": "Context7 MCP",
                                    "script": f"{scripts_base}/mcp-server_local_integrations/analyze_requirements.py",
                                    "params": {
                                        "input": "${event.output_path}"
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    "from": "需求分析",
                    "to": "方案设计",
                    "triggers": [
                        {
                            "name": "需求矩阵更新",
                            "type": "event",
                            "handlers": [
                                {
                                    "type": "mcp_server",
                                    "name": "UML-MCP Server",
                                    "script": f"{scripts_base}/mcp-server_local_integrations/generate_architecture_diagrams.py",
                                    "params": {
                                        "requirements_matrix": "${event.file_path}"
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    "from": "方案设计",
                    "to": "开发实施",
                    "triggers": [
                        {
                            "name": "方案文档完成",
                            "type": "event",
                            "handlers": [
                                {
                                    "type": "mcp_server",
                                    "name": "Task Master MCP",
                                    "script": f"{scripts_base}/task_master/generate_tasks.py",
                                    "params": {
                                        "design_doc": "${event.file_path}",
                                        "requirements": "requirements/requirements_matrix.md"
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    "from": "开发实施",
                    "to": "测试验证",
                    "triggers": [
                        {
                            "name": "代码提交",
                            "type": "event",
                            "handlers": [
                                {
                                    "type": "mcp_server",
                                    "name": "Code Review Server MCP",
                                    "script": f"{scripts_base}/mcp-server_local_integrations/code_review.py",
                                    "params": {
                                        "commit_id": "${event.commit_id}"
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    "from": "测试验证",
                    "to": "生产准备",
                    "triggers": [
                        {
                            "name": "测试通过",
                            "type": "event",
                            "handlers": [
                                {
                                    "type": "script",
                                    "name": "生成生产BOM",
                                    "script": f"{scripts_base}/production/generate_bom.py",
                                    "params": {
                                        "hardware_path": "development/hardware/${event.project_name}"
                                    }
                                }
                            ]
                        }
                    ]
                },
                {
                    "from": "生产准备",
                    "to": "项目输出",
                    "triggers": [
                        {
                            "name": "生产准备完成",
                            "type": "event",
                            "handlers": [
                                {
                                    "type": "mcp_server",
                                    "name": "GitHub MCP Server",
                                    "script": f"{scripts_base}/mcp-server_local_integrations/create_release.py",
                                    "params": {
                                        "version": "${event.version}",
                                        "release_notes": "${event.release_notes_path}"
                                    }
                                }
                            ]
                        }
                    ]
                }
            ],
            
            # MCP服务器配置
            "mcp_servers": [
                {
                    "name": "Firecrawl MCP Server",
                    "script": f"{scripts_base}/mcp-server_local_integrations/firecrawl.py",
                    "purpose": "网页需求抓取",
                    "enabled": True
                },
                {
                    "name": "IM Notifier MCP Server", 
                    "script": f"{scripts_base}/mcp-server_local_integrations/im_notifier.py",
                    "purpose": "通知服务",
                    "enabled": True
                },
                {
                    "name": "UML-MCP Server",
                    "script": f"{scripts_base}/mcp-server_local_integrations/uml_mcp.py", 
                    "purpose": "UML图生成",
                    "enabled": True
                },
                {
                    "name": "Linear MCP Server",
                    "script": f"{scripts_base}/mcp-server_local_integrations/linear_mcp.py",
                    "purpose": "任务管理",
                    "enabled": True
                }
            ],
            
            # 执行设置
            "execution_settings": {
                "parallel_execution": False,
                "error_handling": "stop_on_error",
                "logging_level": "INFO",
                "timeout_seconds": 3600,
                "auto_trigger_enabled": True,
                "event_monitoring": True
            }
        }
    }
    
    config_path = os.path.join(config_dir, 'workflow_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(workflow_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建工作流配置文件: {config_path}")
    return config_path

def create_component_configs(project_path, structure_type):
    """创建各组件的详细配置文件"""
    config_dir = os.path.join(project_path, 'config')
    
    # 需求导入配置
    requirements_import_config = {
        "import_settings": {
            "supported_formats": ["xlsx", "csv", "json", "md"],
            "auto_validation": True,
            "backup_enabled": True,
            "event_trigger": True
        },
        "sources": {
            "jira": {"enabled": False, "url": "", "auth": ""},
            "confluence": {"enabled": False, "url": "", "auth": ""},
            "excel": {"enabled": True, "template_path": "templates/requirements_template.xlsx"}
        },
        "output": {
            "format": "markdown",
            "location": "requirements/",
            "event_file": "requirements_import_completed.json"
        }
    }
    
    # 需求分析配置
    requirements_analysis_config = {
        "analysis_settings": {
            "auto_categorization": True,
            "priority_detection": True,
            "dependency_analysis": True,
            "traceability_enabled": True
        },
        "output_formats": ["matrix", "tree", "graph"],
        "triggers": {
            "matrix_updated": "requirements_matrix_updated.json"
        }
    }
    
    # 设计配置
    design_config = {
        "design_settings": {
            "auto_diagram_generation": True,
            "architecture_templates": ["layered", "microservices", "embedded"],
            "review_required": True,
            "version_control": True
        },
        "outputs": {
            "architecture_doc": "design/architecture.md",
            "technical_spec": "design/technical_specification.md",
            "completion_event": "design_completed.json"
        }
    }
    
    # 开发配置
    development_config = {
        "development_settings": {
            "code_review_enabled": True,
            "unit_test_required": True,
            "documentation_required": True,
            "ci_cd_enabled": True
        },
        "environments": ["development", "testing", "staging"],
        "monitoring": {
            "commit_tracking": True,
            "build_status": True,
            "test_coverage": True
        }
    }
    
    # 质量配置
    quality_config = {
        "testing_settings": {
            "unit_test_coverage": 80,
            "integration_test_required": True,
            "performance_test_required": True,
            "security_scan_enabled": True
        },
        "quality_gates": ["code_review", "test_coverage", "security_scan"],
        "reporting": {
            "test_results": "quality/test_results.json",
            "coverage_report": "quality/coverage_report.html"
        }
    }
    
    # 生产配置
    production_config = {
        "production_settings": {
            "bom_validation": True,
            "process_documentation": True,
            "quality_control": True,
            "regulatory_compliance": True
        },
        "outputs": {
            "bom_file": "production/bom.xlsx",
            "process_doc": "production/manufacturing_process.md",
            "quality_plan": "production/quality_control_plan.md"
        }
    }
    
    # 交付配置
    deliverables_config = {
        "deliverables_settings": {
            "documentation_complete": True,
            "version_control": True,
            "package_signing": True,
            "release_notes": True
        },
        "package_types": ["source", "binary", "documentation"],
        "distribution": {
            "internal_release": True,
            "customer_release": False,
            "archive_enabled": True
        }
    }
    
    # 写入配置文件
    configs = {
        "requirements_import_config.json": requirements_import_config,
        "requirements_analysis_config.json": requirements_analysis_config,
        "design_config.json": design_config,
        "development_config.json": development_config,
        "quality_config.json": quality_config,
        "production_config.json": production_config,
        "deliverables_config.json": deliverables_config
    }
    
    for config_file, config_content in configs.items():
        config_path = os.path.join(config_dir, config_file)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_content, f, ensure_ascii=False, indent=2)
        print(f"[+] 创建组件配置文件: {config_file}")

def create_placeholder_scripts(scripts_base):
    """创建MCP服务器和处理器的占位脚本"""
    script_templates = [
        {
            "path": os.path.join(scripts_base, "mcp-server_local_integrations", "analyze_requirements.py"),
            "content": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
需求分析MCP服务器
分析需求文档并提取结构化信息
"""

import argparse
import json
import os
import sys

def analyze_requirements(input_path):
    """分析需求文档并提取结构化信息"""
    print(f"分析需求文档: {input_path}")
    
    # 这里是MCP服务器实现的占位符
    # 实际实现将调用MCP服务器或AI模型分析需求
    
    return {
        "status": "success",
        "message": f"需求文档 {input_path} 分析完成",
        "results": {
            "requirements_count": 10,
            "categories": ["功能需求", "非功能需求", "性能需求"],
            "priority_distribution": {"高": 3, "中": 5, "低": 2}
        }
    }

def main():
    parser = argparse.ArgumentParser(description="需求分析MCP服务器")
    parser.add_argument("--input", required=True, help="输入需求文档路径")
    parser.add_argument("--output", default="requirements/analysis_results.json", 
                        help="输出分析结果路径")
    
    args = parser.parse_args()
    
    results = analyze_requirements(args.input)
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 写入分析结果
    with open(args.output, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"分析结果已写入 {args.output}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
        },
        {
            "path": os.path.join(scripts_base, "mcp-server_local_integrations", "generate_architecture_diagrams.py"),
            "content": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
UML-MCP服务器 - 架构图生成
基于需求矩阵生成UML架构图
"""

import argparse
import json
import os
import sys

def generate_diagrams(requirements_matrix):
    """基于需求矩阵生成架构图"""
    print(f"基于需求矩阵生成架构图: {requirements_matrix}")
    
    # 这里是MCP服务器实现的占位符
    # 实际实现将调用MCP服务器或AI模型生成图表
    
    return {
        "status": "success",
        "message": f"基于 {requirements_matrix} 生成架构图完成",
        "diagrams": [
            {
                "type": "class_diagram",
                "path": "design/class_diagram.png"
            },
            {
                "type": "sequence_diagram",
                "path": "design/sequence_diagram.png"
            },
            {
                "type": "component_diagram",
                "path": "design/component_diagram.png"
            }
        ]
    }

def main():
    parser = argparse.ArgumentParser(description="UML-MCP服务器 - 架构图生成")
    parser.add_argument("--requirements_matrix", required=True, help="需求矩阵文档路径")
    parser.add_argument("--output_dir", default="design/diagrams", help="输出图表目录")
    
    args = parser.parse_args()
    
    results = generate_diagrams(args.requirements_matrix)
    
    # 创建输出目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # 写入生成结果
    output_path = os.path.join(args.output_dir, "diagram_results.json")
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"图表生成结果已写入 {output_path}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
        },
        {
            "path": os.path.join(scripts_base, "mcp-server_local_integrations", "code_review.py"),
            "content": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代码审查MCP服务器
自动化代码审查和质量检查
"""

import argparse
import json
import os
import sys

def review_code(commit_id):
    """执行代码审查"""
    print(f"执行代码审查: {commit_id}")
    
    # 这里是MCP服务器实现的占位符
    # 实际实现将调用代码审查工具和AI模型
    
    return {
        "status": "success",
        "commit_id": commit_id,
        "review_result": {
            "score": 8.5,
            "issues": [
                {"type": "warning", "message": "函数过长，建议拆分"},
                {"type": "info", "message": "建议添加单元测试"}
            ],
            "passed": True
        }
    }

def main():
    parser = argparse.ArgumentParser(description="代码审查MCP服务器")
    parser.add_argument("--commit_id", required=True, help="提交ID")
    parser.add_argument("--output", default="development/code_review_results.json", 
                        help="输出审查结果路径")
    
    args = parser.parse_args()
    
    results = review_code(args.commit_id)
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 写入审查结果
    with open(args.output, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"代码审查结果已写入 {args.output}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
        }
    ]
    
    for template in script_templates:
        script_path = template["path"]
        script_dir = os.path.dirname(script_path)
        
        # 创建目录
        if not os.path.exists(script_dir):
            os.makedirs(script_dir, exist_ok=True)
        
        # 创建脚本文件
        if not os.path.exists(script_path):
            with open(script_path, "w", encoding="utf-8") as f:
                f.write(template["content"])
            
            # 设置可执行权限
            try:
                os.chmod(script_path, 0o755)
            except:
                pass  # Windows系统可能不支持chmod
            
            print(f"[+] 创建MCP脚本: {os.path.basename(script_path)}")

def ensure_required_directories(project_path):
    """确保所需目录存在"""
    required_dirs = [
        "config",
        "reports", 
        "logs",
        "requirements",
        "design",
        "development",
        "quality",
        "production",
        "deliverables"
    ]
    
    for directory in required_dirs:
        dir_path = os.path.join(project_path, directory)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="工作流组件初始化工具")
    parser.add_argument("--project_path", required=True, help="项目路径")
    parser.add_argument("--structure_type", choices=["single_layer", "multi_level"], 
                       default="single_layer", help="目录结构类型")
    parser.add_argument("--scripts_base", required=True, help="公共脚本基础路径")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.project_path):
        print(f"错误: 项目路径不存在 {args.project_path}")
        return False
    
    print(f"初始化工作流组件...")
    print(f"项目路径: {args.project_path}")
    print(f"结构类型: {args.structure_type}")
    print(f"脚本路径: {args.scripts_base}")
    
    try:
        # 确保必需目录存在
        ensure_required_directories(args.project_path)
        
        # 创建MCP占位脚本
        create_placeholder_scripts(args.scripts_base)
        
        # 创建工作流配置
        create_workflow_config(args.project_path, args.structure_type, args.scripts_base)
        
        # 创建组件配置
        create_component_configs(args.project_path, args.structure_type)
        
        print("[OK] 工作流组件初始化完成!")
        print("包含功能:")
        print("  - 7个工作流组件配置")
        print("  - 6个连接关系和事件触发")
        print("  - 4个MCP服务器配置")
        print("  - MCP占位脚本创建")
        print("  - 完整的事件驱动支持")
        return True
        
    except Exception as e:
        print(f"[X] 工作流组件初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 