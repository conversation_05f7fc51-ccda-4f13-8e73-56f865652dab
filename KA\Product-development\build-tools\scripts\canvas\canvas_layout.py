#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Canvas布局管理器

该模块负责Obsidian Canvas中节点的布局管理，包括组件区域划分、
节点位置计算、目录分组和布局验证等功能。
"""

from typing import Dict, List, Tuple, Optional, Set
from pathlib import Path
import json
import hashlib
import math
import sys
import os

# 添加scripts目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared_config import get_subdirectory_abbreviation


class CanvasLayoutManager:
    """Canvas布局管理器"""
    
    def __init__(self, config_path: Optional[Path] = None):
        """
        初始化布局管理器
        
        Args:
            config_path: 布局配置文件路径，如果为None则使用默认配置
        """
        self.component_areas = self._load_component_areas(config_path)

        # 布局基础参数（根据新的布局策略）
        self.node_width = 400
        self.node_height = 300
        self.node_spacing_x = 20   # 文档节点水平间距
        self.node_spacing_y = 20   # 文档节点垂直间距

        # 组件级组参数
        self.component_min_width = 600     # 组件组最小宽度
        self.component_spacing = 300       # 组件组间距（增大间距）
        self.component_margin_top = 50     # 组件组顶部边距

        # 项目级组参数
        self.project_margin = 30           # 项目组边距
        self.project_spacing = 50          # 项目组间距
        self.project_min_height = 400      # 项目组最小高度

        # Canvas整体参数
        self.canvas_padding = 100          # Canvas边缘留白
        self.min_columns = 2               # 最少列数
        self.max_columns = 6               # 最大列数
        self.max_nodes_per_column = 15     # 每列最大节点数（保持兼容）

        # 颜色配置
        self.component_colors = {
            "border": "#2196F3",           # 深蓝色边框
            "background": "rgba(33, 150, 243, 0.05)"
        }
        self.project_colors = {
            "border": "#4CAF50",           # 绿色边框
            "background": "rgba(76, 175, 80, 0.05)"
        }
        
    def _load_component_areas(self, config_path: Optional[Path]) -> Dict:
        """
        加载组件区域配置，支持多列布局
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 组件区域配置字典
        """
        # 默认组件区域配置 - 增大间距，确保最少两列布局
        default_areas = {
            "PROD_INFO": {
                "x_start": 100,
                "x_end": 700,  # 增加区域宽度
                "color": "#FF6B6B",
                "name": "产品信息",
                "icon": "📋",
                "max_columns": 2
            },
            "REQ": {
                "x_start": 1000,
                "x_end": 1800,    # 增加宽度
                "color": "#4ECDC4",
                "name": "需求管理",
                "icon": "📝",
                "max_columns": 2
            },
            "DES": {
                "x_start": 2200,   # 增大间距到400px
                "x_end": 3000,    # 增加宽度
                "color": "#45B7D1",
                "name": "设计文档",
                "icon": "🎨",
                "max_columns": 2
            },
            "DEV": {
                "x_start": 3400,   # 增大间距到400px
                "x_end": 4200,    # 增加宽度
                "color": "#96CEB4",
                "name": "开发实施",
                "icon": "💻",
                "max_columns": 2
            },
            "QA": {
                "x_start": 3700,   # 增大间距
                "x_end": 4300,
                "color": "#FFEAA7",
                "name": "质量保证",
                "icon": "🧪",
                "max_columns": 2
            },
            "PROD": {
                "x_start": 4600,   # 增大间距
                "x_end": 5200,
                "color": "#DDA0DD",
                "name": "生产制造",
                "icon": "🏭",
                "max_columns": 2
            },
            "PM": {
                "x_start": 5500,   # 第一行继续
                "x_end": 6100,
                "color": "#98D8C8",
                "name": "项目管理",
                "icon": "📊",
                "max_columns": 2
            },
            "DEL": {
                "x_start": 6400,   # 第一行继续
                "x_end": 7000,
                "color": "#F7DC6F",
                "name": "交付物",
                "icon": "📦",
                "max_columns": 2
            }
        }
        
        if config_path and config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    custom_config = json.load(f)
                    # 合并自定义配置
                    default_areas.update(custom_config.get('component_areas', {}))
            except Exception as e:
                print(f"加载布局配置失败，使用默认配置: {e}")
        
        return default_areas

    def _calculate_dynamic_layout(self, active_components: List[str], component_sizes: Dict[str, Dict] = None) -> Dict:
        """
        根据活跃组件数量和实际尺寸动态计算布局

        Args:
            active_components: 有文档的组件列表
            component_sizes: 组件实际尺寸信息 {component: {width, height}}

        Returns:
            Dict: 动态计算的组件区域配置
        """
        # 组件样式配置
        component_styles = {
            "PROD_INFO": {"color": "#FF6B6B", "name": "产品信息", "icon": "📋"},
            "REQ": {"color": "#4ECDC4", "name": "需求管理", "icon": "📝"},
            "DES": {"color": "#45B7D1", "name": "设计文档", "icon": "🎨"},
            "DEV": {"color": "#96CEB4", "name": "开发实施", "icon": "💻"},
            "QA": {"color": "#FFEAA7", "name": "质量保证", "icon": "🧪"},
            "PROD": {"color": "#DDA0DD", "name": "生产制造", "icon": "🏭"},
            "PM": {"color": "#98D8C8", "name": "项目管理", "icon": "📊"},
            "DEL": {"color": "#F7DC6F", "name": "交付物", "icon": "📦"}
        }

        # 动态布局参数
        default_component_width = 1000  # 默认组件组宽度
        component_spacing = 300         # 组件组之间的间距
        layout_start_x = 100           # 布局起始X坐标

        # 只为活跃组件计算位置
        dynamic_areas = {}
        current_x = layout_start_x

        for component_id in active_components:
            if component_id in component_styles:
                style = component_styles[component_id]

                # 使用实际尺寸或默认尺寸
                if component_sizes and component_id in component_sizes:
                    component_width = component_sizes[component_id]["width"]
                else:
                    component_width = default_component_width

                dynamic_areas[component_id] = {
                    "x_start": current_x,
                    "x_end": current_x + component_width,
                    "color": style["color"],
                    "name": style["name"],
                    "icon": style["icon"],
                    "max_columns": 2
                }
                current_x += component_width + component_spacing

        return dynamic_areas

    def _calculate_position_in_group(self, group_pos: Dict, node_index: int, total_nodes: int, is_component_level: bool = False) -> Dict:
        """
        计算组内文档的位置，避免重叠

        Args:
            group_pos: 组的位置信息
            node_index: 节点索引
            total_nodes: 总节点数
            is_component_level: 是否为组件级文档（放在组件组顶部）

        Returns:
            Dict: 节点位置信息
        """
        if is_component_level:
            # 组件级文档：放在组件组顶部区域，使用紧凑布局
            available_width = group_pos["width"] - self.project_margin * 2
            component_doc_area_height = 100  # 组件级文档区域高度

            # 计算列数
            columns = max(2, min(4, total_nodes))
            col = node_index % columns
            row = node_index // columns

            # 计算实际位置（顶部区域，确保可读性）
            node_width = min(self.node_width, (available_width - (columns - 1) * self.node_spacing_x) // columns)
            node_height = 280  # 与项目级文档保持一致的高度

            x = group_pos["x"] + self.project_margin + col * (node_width + self.node_spacing_x)
            y = group_pos["y"] + 60 + row * (node_height + 10)  # 紧凑间距

        else:
            # 项目级文档：放在项目组内
            available_width = group_pos["width"] - self.project_margin * 2
            available_height = group_pos["height"] - 60 - self.project_margin * 2

            # 计算列数和行数（允许多列布局）
            columns = max(2, min(self.max_columns, int(math.sqrt(total_nodes))))
            rows = math.ceil(total_nodes / columns)

            # 计算节点在网格中的位置
            row = node_index // columns
            col = node_index % columns

            # 计算实际位置
            node_width = min(self.node_width, (available_width - (columns - 1) * self.node_spacing_x) // columns)
            node_height = min(self.node_height, (available_height - (rows - 1) * self.node_spacing_y) // rows)

            x = group_pos["x"] + self.project_margin + col * (node_width + self.node_spacing_x)
            y = group_pos["y"] + 40 + row * (node_height + self.node_spacing_y)  # 减少项目组内文档的顶部间距

        return {
            "x": x,
            "y": y,
            "width": node_width,
            "height": node_height
        }

    def detect_overlap(self, rect1: Dict, rect2: Dict) -> bool:
        """
        检测两个矩形是否重叠

        Args:
            rect1: 第一个矩形 {x, y, width, height}
            rect2: 第二个矩形 {x, y, width, height}

        Returns:
            bool: 是否重叠
        """
        return not (rect1["x"] + rect1["width"] <= rect2["x"] or
                    rect2["x"] + rect2["width"] <= rect1["x"] or
                    rect1["y"] + rect1["height"] <= rect2["y"] or
                    rect2["y"] + rect2["height"] <= rect1["y"])

    def resolve_overlaps(self, elements: List[Dict]) -> List[Dict]:
        """
        解决元素重叠问题

        Args:
            elements: 元素列表

        Returns:
            List[Dict]: 调整后的元素列表
        """
        for i in range(len(elements)):
            for j in range(i + 1, len(elements)):
                if self.detect_overlap(elements[i], elements[j]):
                    # 向下移动第二个元素
                    elements[j]["y"] = elements[i]["y"] + elements[i]["height"] + self.project_spacing

        return elements
    
    def calculate_position(self, component: str, node_index_in_component: int, 
                          group_offset_y: int = 0) -> Dict[str, int]:
        """
        计算节点在Canvas中的位置，支持多列布局
        
        Args:
            component: 组件名称
            node_index_in_component: 节点在该组件中的索引（从0开始）
            group_offset_y: 组偏移量（用于目录分组）
            
        Returns:
            Dict[str, int]: 包含x和y坐标的字典
        """
        if component not in self.component_areas:
            component = "DEV"  # 默认组件
            print(f"未知组件，使用默认组件DEV: {component}")
            
        area = self.component_areas[component]
        max_columns = area.get("max_columns", 1)
        
        # 计算列和行
        column = (node_index_in_component // self.max_nodes_per_column) % max_columns
        row = node_index_in_component % self.max_nodes_per_column
        
        # 如果超出最大列数，继续在第一列向下排列
        if node_index_in_component >= self.max_nodes_per_column * max_columns:
            overflow_index = node_index_in_component - (self.max_nodes_per_column * max_columns)
            column = 0
            row = self.max_nodes_per_column + overflow_index
        
        # 计算X坐标：基于列数在组件区域内分布
        area_width = area["x_end"] - area["x_start"]
        column_width = (area_width - (max_columns - 1) * self.node_spacing_x) // max_columns
        x = area["x_start"] + column * (column_width + self.node_spacing_x)
        
        # 确保节点不超出组件区域
        if x + self.node_width > area["x_end"]:
            x = area["x_end"] - self.node_width
        
        # 计算Y坐标：垂直排列
        y = 50 + group_offset_y + row * self.node_spacing_y
        
        return {"x": x, "y": y}
    
    def calculate_group_position(self, group_name: str, component: str,
                               group_index: int, total_nodes: int, is_nested: bool = False,
                               component_doc_height: int = 0, project_sizes: Dict = None,
                               all_project_sizes: Dict = None) -> Dict[str, int]:
        """
        计算目录组的位置和尺寸，支持嵌套组和新的布局策略

        Args:
            group_name: 组名称
            component: 所属组件
            group_index: 组索引
            total_nodes: 组内节点总数
            is_nested: 是否为嵌套组（项目级组）

        Returns:
            Dict: 包含组位置和尺寸信息
        """
        if component not in self.component_areas:
            # 错误：不应该强制回退到DEV，这会导致DEV承担所有未知组件的内容
            # 应该抛出错误或使用默认配置
            raise ValueError(f"组件 '{component}' 不在component_areas中，可能是动态布局计算顺序错误")

        area = self.component_areas[component]

        if is_nested:
            # 项目级组：在组件组内部，垂直排列
            x = area["x_start"] + self.project_margin

            # 计算Y位置：根据组件级文档的实际高度动态计算
            if component_doc_height > 0:
                # 有组件级文档：在文档下方留间距
                component_doc_area_height = 60 + component_doc_height + 40  # 标题+文档高度+间距
            else:
                # 无组件级文档：项目组直接从标题下方开始
                component_doc_area_height = 60  # 只留标题空间
            base_y = self.component_margin_top + component_doc_area_height

            # 使用实际项目组高度计算Y位置
            if all_project_sizes and group_index > 0:
                # 累加前面所有项目组的实际高度
                cumulative_height = 0
                project_names = list(all_project_sizes.keys())
                for i in range(group_index):
                    if i < len(project_names):
                        cumulative_height += all_project_sizes[project_names[i]]["height"] + self.project_spacing
                y = base_y + cumulative_height
            else:
                # 第一个项目组或没有尺寸信息时使用原逻辑
                y = base_y + group_index * (self.project_min_height + self.project_spacing)

            # 项目组尺寸计算
            # 计算需要的列数（允许多列布局）
            columns = max(self.min_columns, min(self.max_columns,
                                               int(math.sqrt(total_nodes)) if total_nodes > 0 else 2))
            rows = math.ceil(total_nodes / columns) if total_nodes > 0 else 1

            # 宽度：确保能容纳指定列数的文档
            width = (columns * self.node_width +
                    (columns - 1) * self.node_spacing_x +
                    self.project_margin * 2)
            width = max(width, self.component_min_width - self.project_margin * 2)

            # 高度：根据行数计算
            height = (rows * self.node_height +
                     (rows - 1) * self.node_spacing_y +
                     self.project_margin * 2 + 40)  # 40px标题空间
            height = max(height, self.project_min_height)

        else:
            # 组件级组：水平排列，所有组件在同一行
            y = self.component_margin_top
            x = area["x_start"]

            # 基于实际项目组尺寸计算组件组尺寸
            if project_sizes:
                # 计算所需的最大宽度（最宽的项目组）
                max_project_width = 0
                total_height = 60  # 组件组标题空间
                project_count = len(project_sizes)

                # 添加一层目录文件的空间（需要从外部传入信息，这里先用固定逻辑）
                # TODO: 这里需要重构以获取一层目录文件信息
                if component_doc_height > 0:
                    total_height += component_doc_height + 40  # 一层目录文件高度 + 间距

                for project_name, size_info in project_sizes.items():
                    max_project_width = max(max_project_width, size_info["width"])
                    total_height += size_info["height"]

                # 添加项目组间距（项目数-1个间距）
                if project_count > 1:
                    total_height += (project_count - 1) * self.project_spacing

                # 组件组宽度：最宽项目组 + 边距
                width = max(self.component_min_width,
                           area["x_end"] - area["x_start"],
                           max_project_width + self.project_margin * 2)

                # 组件组高度：所有项目组垂直排列的总高度 + 边距
                height = max(800, total_height + self.project_margin * 2)

                # 添加高度保护机制，防止异常巨大的高度值
                MAX_COMPONENT_HEIGHT = 50000  # 最大组件组高度限制
                if height > MAX_COMPONENT_HEIGHT:
                    print(f"警告：组件 {component} 的计算高度 {height}px 超过限制，已限制为 {MAX_COMPONENT_HEIGHT}px")
                    print(f"  项目组数量: {project_count}")
                    print(f"  总高度计算: {total_height}")
                    height = MAX_COMPONENT_HEIGHT

            else:
                # 回退到估算逻辑（向后兼容）
                estimated_projects = max(1, total_nodes // 3)
                columns = max(self.min_columns, min(self.max_columns,
                                                   int(math.sqrt(total_nodes)) if total_nodes > 0 else 2))
                project_width = (columns * self.node_width +
                               (columns - 1) * self.node_spacing_x +
                               self.project_margin * 2)

                width = max(self.component_min_width,
                           area["x_end"] - area["x_start"],
                           project_width + self.project_margin * 2)

                height = (60 + estimated_projects * (self.project_min_height + self.project_spacing) +
                         self.project_margin * 2 + 300)

            # 设置合理的最小高度
            min_component_height = 800
            height = max(height, min_component_height)

        return {
            "x": x,
            "y": y,
            "width": width,
            "height": height,
            "is_nested": is_nested
        }
    
    def generate_group_id(self, group_name: str, component: str) -> str:
        """
        生成组的唯一ID
        
        Args:
            group_name: 组名称
            component: 组件名称
            
        Returns:
            str: 组ID
        """
        # 使用组名和组件名生成稳定的ID
        content = f"{component}_{group_name}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def calculate_area_positions_with_groups(self, documents_by_component: Dict[str, List], use_hierarchical: bool = True) -> Dict[str, Dict]:
        """
        批量计算各组件下所有文档的位置，支持层级分组和动态布局

        Args:
            documents_by_component: 按组件分组的文档列表
            use_hierarchical: 是否使用层级分组（组件级→项目级）

        Returns:
            Dict: 包含节点和组信息的布局数据
        """
        # 使用动态布局：三阶段计算（修复顺序问题）
        active_components = list(documents_by_component.keys())

        # 第一阶段：预初始化component_areas（使用默认尺寸）
        default_component_sizes = {comp: {"width": 1000, "height": 800} for comp in active_components}
        dynamic_areas = self._calculate_dynamic_layout(active_components, default_component_sizes)
        self.component_areas = dynamic_areas

        # 第二阶段：计算所有组件的实际尺寸
        component_sizes = {}
        if use_hierarchical:
            for component, documents in documents_by_component.items():
                # 预计算项目组尺寸
                hierarchy = self._group_documents_hierarchical(documents, component)
                project_sizes = self._calculate_project_group_sizes(hierarchy["component_group"]["projects"])

                # 检查是否有一层目录文件
                has_component_level_docs = "一层目录文件" in hierarchy["component_group"]["projects"]

                # 计算组件组尺寸
                component_size = self._calculate_component_size(component, len(documents), project_sizes, has_component_level_docs)
                component_sizes[component] = component_size

        # 第三阶段：基于实际尺寸重新计算位置
        if component_sizes:
            dynamic_areas = self._calculate_dynamic_layout(active_components, component_sizes)
            self.component_areas = dynamic_areas

        result = {}

        for component, documents in documents_by_component.items():
            if use_hierarchical:
                # 使用层级分组
                result[component] = self._calculate_hierarchical_layout(component, documents)
            else:
                # 使用简单分组（向后兼容）
                result[component] = self._calculate_simple_layout(component, documents)

        return result

    def _calculate_simple_layout(self, component: str, documents: List[Dict]) -> Dict[str, List]:
        """
        计算简单分组布局（向后兼容）
        """
        groups = self._group_documents_by_directory(documents)

        component_nodes = []
        component_groups = []
        node_index = 0

        for group_index, (group_name, group_docs) in enumerate(groups.items()):
            # 计算组位置
            group_pos = self.calculate_group_position(group_name, component,
                                                    group_index, len(group_docs), is_nested=False)

            # 创建组信息
            group_info = {
                "id": self.generate_group_id(group_name, component),
                "type": "group",
                "label": group_name,
                "x": group_pos["x"],
                "y": group_pos["y"],
                "width": group_pos["width"],
                "height": group_pos["height"]
            }
            component_groups.append(group_info)

            # 计算组内文档位置
            for doc_index, doc in enumerate(group_docs):
                position = self.calculate_position(component, doc_index, group_pos["y_offset"])

                node_info = {
                    "document": doc,
                    "position": position,
                    "color": self.get_component_color(component),
                    "group_id": group_info["id"],
                    "index": node_index
                }
                component_nodes.append(node_info)
                node_index += 1

        return {
            "nodes": component_nodes,
            "groups": component_groups
        }

    def _calculate_project_group_sizes(self, projects: Dict[str, List]) -> Dict[str, Dict]:
        """
        预计算所有项目组的尺寸

        Args:
            projects: 项目组字典 {project_name: [documents...]}

        Returns:
            Dict: 项目组尺寸信息 {project_name: {width, height, columns, rows}}
        """
        project_sizes = {}

        for project_name, project_docs in projects.items():
            if project_name == "一层目录文件":
                # 一层目录文件不需要项目组
                continue

            doc_count = len(project_docs)
            if doc_count == 0:
                continue

            # 计算列数和行数
            columns = max(self.min_columns, min(self.max_columns, int(math.sqrt(doc_count))))
            rows = math.ceil(doc_count / columns)

            # 计算项目组尺寸
            width = (columns * self.node_width +
                    (columns - 1) * self.node_spacing_x +
                    self.project_margin * 2)
            height = (rows * self.node_height +
                     (rows - 1) * self.node_spacing_y +
                     self.project_margin * 2 + 40)  # 40px标题空间
            height = max(height, self.project_min_height)

            project_sizes[project_name] = {
                "width": width,
                "height": height,
                "columns": columns,
                "rows": rows,
                "doc_count": doc_count
            }

        return project_sizes

    def _calculate_component_size(self, component: str, total_docs: int, project_sizes: Dict[str, Dict],
                                  has_component_level_docs: bool = False) -> Dict[str, int]:
        """
        计算组件组的实际尺寸（与Canvas生成逻辑保持一致）

        Args:
            component: 组件名称
            total_docs: 文档总数
            project_sizes: 项目组尺寸信息
            has_component_level_docs: 是否有一层目录文件

        Returns:
            Dict: 组件组尺寸 {width, height}
        """
        if project_sizes:
            # 有项目组：基于项目组尺寸计算
            max_project_width = 0
            total_height = 60  # 组件组标题空间
            project_count = len(project_sizes)

            # 添加一层目录文件的空间
            if has_component_level_docs:
                total_height += 280 + 40  # 一层目录文件高度 + 间距

            for project_name, size_info in project_sizes.items():
                max_project_width = max(max_project_width, size_info["width"])
                total_height += size_info["height"]

            # 添加项目组间距（项目数-1个间距）
            if project_count > 1:
                total_height += (project_count - 1) * self.project_spacing

            # 组件组宽度：最宽项目组 + 边距
            width = max(self.component_min_width, max_project_width + self.project_margin * 2)

            # 组件组高度：所有项目组垂直排列的总高度 + 边距
            height = max(800, total_height + self.project_margin * 2)

            # 添加高度保护机制，防止异常巨大的高度值
            MAX_COMPONENT_HEIGHT = 50000  # 最大组件组高度限制
            if height > MAX_COMPONENT_HEIGHT:
                print(f"警告：组件 {component} 的计算高度 {height}px 超过限制，已限制为 {MAX_COMPONENT_HEIGHT}px")
                print(f"  项目组数量: {project_count}")
                print(f"  总高度计算: {total_height}")
                height = MAX_COMPONENT_HEIGHT
        else:
            # 没有项目组：使用与Canvas生成相同的回退逻辑
            estimated_projects = max(1, total_docs // 3)
            columns = max(self.min_columns, min(self.max_columns,
                                               int(math.sqrt(total_docs)) if total_docs > 0 else 2))
            project_width = (columns * self.node_width +
                           (columns - 1) * self.node_spacing_x +
                           self.project_margin * 2)

            width = max(self.component_min_width,
                       project_width + self.project_margin * 2)

            height = (60 + estimated_projects * (self.project_min_height + self.project_spacing) +
                     self.project_margin * 2 + 300)
            height = max(800, height)  # 最小高度800px

        return {"width": width, "height": height}

    def _calculate_hierarchical_layout(self, component: str, documents: List[Dict]) -> Dict[str, List]:
        """
        计算层级分组布局：创建两层结构（组件级+项目级）
        """
        # 获取层级分组结构
        hierarchy = self._group_documents_hierarchical(documents, component)
        component_group = hierarchy["component_group"]

        component_nodes = []
        component_groups = []
        node_index = 0

        # 预计算所有项目组的尺寸
        project_sizes = self._calculate_project_group_sizes(component_group["projects"])

        # 计算组件级文档的实际高度
        component_level_docs = component_group["projects"].get("一层目录文件", [])
        component_doc_actual_height = 280 if component_level_docs else 0  # 如果有组件级文档，使用280px高度

        # 第一层：创建组件级组（REQ、DES、DEV、REV、DEL、PM）
        component_group_pos = self.calculate_group_position(
            component, component, 0, len(documents), is_nested=False,
            project_sizes=project_sizes, component_doc_height=component_doc_actual_height
        )

        component_group_info = {
            "id": self.generate_group_id(component, "component"),
            "type": "group",
            "label": component,  # 显示组件代号（REQ、DES等）
            "x": component_group_pos["x"],
            "y": component_group_pos["y"],
            "width": component_group_pos["width"],
            "height": component_group_pos["height"],
            "color": self.component_colors["border"]  # 组件级组颜色
        }
        component_groups.append(component_group_info)

        # 第二层：创建项目级组（嵌套在组件级组内）
        project_index = 0
        for project_name, project_docs in component_group["projects"].items():
            # 跳过一层目录文件，它们直接显示在组件级组下
            if project_name == "一层目录文件":
                # 一层目录文件直接放在组件级组顶部，不创建子组
                for doc_index, doc in enumerate(project_docs):
                    position = self._calculate_position_in_group(
                        component_group_pos, doc_index, len(project_docs), is_component_level=True
                    )

                    node_info = {
                        "document": doc,
                        "position": position,
                        "color": self.get_component_color(component),
                        "group_id": component_group_info["id"],  # 直接属于组件级组
                        "index": node_index
                    }
                    component_nodes.append(node_info)
                    node_index += 1
                continue

            # 计算项目组位置（嵌套在组件组内）
            project_group_pos = self.calculate_group_position(
                project_name, component, project_index, len(project_docs), is_nested=True,
                component_doc_height=component_doc_actual_height, all_project_sizes=project_sizes
            )

            # 创建项目组信息
            project_group_info = {
                "id": self.generate_group_id(project_name, component),
                "type": "group",
                "label": project_name,  # 显示项目名称
                "x": project_group_pos["x"],
                "y": project_group_pos["y"],
                "width": project_group_pos["width"],
                "height": project_group_pos["height"],
                "color": self.project_colors["border"]  # 项目级组颜色
            }
            component_groups.append(project_group_info)

            # 计算项目组内文档位置（支持多列布局）
            for doc_index, doc in enumerate(project_docs):
                position = self._calculate_position_in_group(
                    project_group_pos, doc_index, len(project_docs)
                )

                node_info = {
                    "document": doc,
                    "position": position,
                    "color": self.get_component_color(component),
                    "group_id": project_group_info["id"],
                    "index": node_index
                }
                component_nodes.append(node_info)
                node_index += 1

            project_index += 1

        return {
            "nodes": component_nodes,
            "groups": component_groups
        }
    def _group_documents_by_directory(self, documents: List[Dict]) -> Dict[str, List[Dict]]:
        """
        按目录对文档进行分组（简单分组，向后兼容）

        Args:
            documents: 文档列表

        Returns:
            Dict[str, List[Dict]]: 按目录分组的文档
        """
        groups = {}

        for doc in documents:
            doc_path = doc.get('doc_path', '')
            if not doc_path:
                group_name = "未分类"
            else:
                # 提取文档所在的直接父目录
                path_parts = Path(doc_path).parts
                if len(path_parts) > 1:
                    group_name = path_parts[-2]  # 直接父目录名
                else:
                    group_name = "根目录"

            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(doc)

        return groups

    def _group_documents_hierarchical(self, documents: List[Dict], component: str) -> Dict[str, Dict]:
        """
        按层级对文档进行分组：组件级→项目级

        Args:
            documents: 文档列表
            component: 组件名称

        Returns:
            Dict: 层级分组结构
            {
                "component_group": {
                    "name": "组件名称",
                    "projects": {
                        "project_name": [documents...]
                    }
                }
            }
        """
        # 按项目对文档进行分组
        project_groups = {}

        for doc in documents:
            doc_path = doc.get('doc_path', '')
            project_name = self._extract_project_name(doc_path)

            if project_name not in project_groups:
                project_groups[project_name] = []
            project_groups[project_name].append(doc)

        return {
            "component_group": {
                "name": component,
                "projects": project_groups
            }
        }

    def _extract_project_name(self, doc_path: str) -> str:
        """
        从文档路径提取项目名称（使用子目录缩写）

        Args:
            doc_path: 文档路径

        Returns:
            str: 项目名称（子目录缩写）
        """
        if not doc_path:
            return "未分类"

        path_parts = Path(doc_path).parts

        # 检查是否为一层目录文件（直接在组件目录下）
        if len(path_parts) == 2:
            component_dir = path_parts[0]  # requirements, design, etc.
            filename = path_parts[1]       # README.md, requirements_matrix.md, etc.

            # 一层目录文件直接显示在组件级组下，不需要第二层分组
            return "一层目录文件"

        # 对于两层以上目录的组件，第二层使用子目录缩写
        # 例如：requirements/custom_requirements/xxx.md → CUST
        elif len(path_parts) >= 3:
            component_dir = path_parts[0]  # requirements, design, etc.
            subdir = path_parts[1]         # custom_requirements, main_requirements, etc.

            # 对于简单的两层结构，使用子目录缩写
            if component_dir in ['requirements', 'design', 'deliverables', 'project_management', 'quality', 'production']:
                return get_subdirectory_abbreviation(subdir)

            # 对于development目录的特殊处理
            elif component_dir == 'development':
                # development/firmware_projects/fw_project_1/docs/API接口文档.md
                if len(path_parts) >= 4:
                    project_type = path_parts[1]  # firmware_projects
                    project_id = path_parts[2]    # fw_project_1

                    # 生成项目组名：FW-fw_project_1
                    if project_type.endswith('_projects'):
                        # 提取项目类型：firmware_projects -> firmware
                        base_type = project_type.replace('_projects', '')
                        type_abbr = get_subdirectory_abbreviation(base_type)
                        return f"{type_abbr}-{project_id}"
                    else:
                        return project_id
                elif len(path_parts) == 3:
                    # development/firmware_projects/test.md 这种情况
                    project_type = path_parts[1]  # firmware_projects
                    if project_type.endswith('_projects'):
                        # 提取项目类型：firmware_projects -> firmware
                        base_type = project_type.replace('_projects', '')
                        type_abbr = get_subdirectory_abbreviation(base_type)
                        return f"{type_abbr}-misc"  # 杂项文件
                    else:
                        return get_subdirectory_abbreviation(subdir)
                else:
                    # 简单的development子目录
                    return get_subdirectory_abbreviation(subdir)

            # 其他情况，使用子目录缩写
            else:
                return get_subdirectory_abbreviation(subdir)

        elif len(path_parts) >= 1:
            # 只有一层目录，使用目录名
            return path_parts[0].upper()
        else:
            return "根目录"
    
    def get_component_color(self, component: str) -> str:
        """
        获取组件对应的颜色
        
        Args:
            component: 组件名称
            
        Returns:
            str: 十六进制颜色值
        """
        return self.component_areas.get(component, {}).get("color", "#CCCCCC")
    
    def get_component_info(self, component: str) -> Dict:
        """
        获取组件的完整信息
        
        Args:
            component: 组件名称
            
        Returns:
            Dict: 组件信息字典
        """
        return self.component_areas.get(component, {
            "x_start": 0, 
            "x_end": 200, 
            "color": "#CCCCCC",
            "name": "未知组件",
            "icon": "❓"
        })
    
    def get_all_components(self) -> List[str]:
        """
        获取所有可用的组件列表
        
        Returns:
            List[str]: 组件名称列表
        """
        return list(self.component_areas.keys())
    
    def validate_layout(self, nodes: List[Dict]) -> List[str]:
        """
        验证布局的合理性
        
        Args:
            nodes: 节点列表
            
        Returns:
            List[str]: 问题描述列表，空列表表示无问题
        """
        issues = []
        
        for node in nodes:
            node_id = node.get("id", "unknown")
            x, y = node.get("x", 0), node.get("y", 0)
            
            # 检查坐标是否在合理范围内
            if x < 0:
                issues.append(f"节点 {node_id} 的X坐标为负数: {x}")
            if x > 2500:  # 考虑到多列的情况
                issues.append(f"节点 {node_id} 的X坐标过大: {x}")
            if y < 0:
                issues.append(f"节点 {node_id} 的Y坐标为负数: {y}")
            
            # 检查节点是否重叠（简单检查）
            for other_node in nodes:
                if other_node.get("id") != node_id:
                    other_x, other_y = other_node.get("x", 0), other_node.get("y", 0)
                    if abs(x - other_x) < 50 and abs(y - other_y) < 50:
                        issues.append(f"节点 {node_id} 可能与其他节点重叠")
                        break
        
        return issues
    
    def optimize_layout(self, nodes: List[Dict]) -> List[Dict]:
        """
        优化节点布局，解决重叠和位置问题
        
        Args:
            nodes: 原始节点列表
            
        Returns:
            List[Dict]: 优化后的节点列表
        """
        optimized_nodes = []
        
        # 按组件分组
        nodes_by_component = {}
        for node in nodes:
            # 尝试从文件路径推断组件
            component = self._infer_component_from_path(node.get("file", ""))
            if component not in nodes_by_component:
                nodes_by_component[component] = []
            nodes_by_component[component].append(node)
        
        # 重新计算位置
        for component, component_nodes in nodes_by_component.items():
            for index, node in enumerate(component_nodes):
                position = self.calculate_position(component, index)
                node["x"] = position["x"]
                node["y"] = position["y"]
                node["color"] = self.get_component_color(component)
                optimized_nodes.append(node)
        
        return optimized_nodes
    
    def _infer_component_from_path(self, file_path: str) -> str:
        """
        从文件路径推断组件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 推断的组件名称
        """
        path_lower = file_path.lower()
        
        if "product_info" in path_lower:
            return "PROD_INFO"
        elif "requirement" in path_lower or "req_" in path_lower:
            return "REQ"
        elif "design" in path_lower or "des_" in path_lower:
            return "DES"
        elif "development" in path_lower or "dev_" in path_lower:
            return "DEV"
        elif "quality" in path_lower or "qa_" in path_lower or "test" in path_lower:
            return "QA"
        elif "production" in path_lower or "prod_" in path_lower:
            return "PROD"
        elif "project_management" in path_lower or "pm_" in path_lower:
            return "PM"
        elif "deliverable" in path_lower or "del_" in path_lower:
            return "DEL"
        else:
            return "DEV"  # 默认组件
    
    def generate_layout_config(self, output_path: Path) -> bool:
        """
        生成布局配置文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            bool: 生成成功返回True
        """
        config = {
            "component_areas": self.component_areas,
            "node_settings": {
                "default_width": self.node_width,
                "default_height": self.node_height,
                "spacing_y": self.node_spacing_y,
                "spacing_x": self.node_spacing_x,
                "margin": self.margin,
                "group_margin": self.group_margin,
                "max_nodes_per_column": self.max_nodes_per_column
            }
        }
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"布局配置已生成: {output_path}")
            return True
        except Exception as e:
            print(f"生成布局配置失败: {e}")
            return False


def main():
    """测试函数"""
    layout_manager = CanvasLayoutManager()
    
    # 测试位置计算
    test_documents = {
        "REQ": [{"name": "需求1"}, {"name": "需求2"}],
        "DES": [{"name": "设计1"}],
        "DEV": [{"name": "开发1"}, {"name": "开发2"}, {"name": "开发3"}]
    }
    
    positions = layout_manager.calculate_area_positions_with_groups(test_documents)
    
    print("布局测试结果:")
    for component, data in positions.items():
        print(f"\n{component} 组件:")
        for node_info in data["nodes"]:
            print(f"  {node_info['document']['name']}: {node_info['position']}")
    
    # 显示所有组件信息
    print("\n所有组件配置:")
    for component in layout_manager.get_all_components():
        info = layout_manager.get_component_info(component)
        print(f"  {component}: {info['name']} ({info['color']})")


if __name__ == "__main__":
    main() 