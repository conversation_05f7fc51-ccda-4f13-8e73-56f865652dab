# Canvas到INDEX自动同步方法

> 📖 **完整使用指南**: 详细配置和故障排除请参考 [[CANVAS_AUTO_SYNC_GUIDE]]
> 
> 🔧 **技术实现**: 底层技术详情请参考 [[README#Canvas可视化模块]]

Canvas模块提供两种自动同步方法，当Canvas文件中的edges（连线）发生变更时，自动同步关联关系到INDEX文档。

## 🎯 测试验证结果

基于项目 `example/test_single_layer` 的最新测试结果：

### ✅ 核心功能验证
- **INDEX→Canvas同步**: ✅ 正常工作，自动创建目录组
- **Canvas→INDEX同步**: ✅ 手动测试成功，关联关系同步正常
- **目录分组**: ✅ 自动根据文档目录创建组（如：`deliverables`, `project_management`）

### ⚠️ 自动同步方法状态
| 方法 | 状态 | 核心问题 | 推荐使用 |
|------|------|----------|----------|
| **方法1** (Obsidian Plugin) | 部分可用 | 缺少`jq`工具导致edges提取失败 | 个人开发（需解决依赖） |
| **方法2** (Watchdog监控) | 完全可用 | PyYAML已安装，功能正常 | 团队协作 ✅ |

---

## 方法一：Obsidian Plugin + Shell Commands

> 💡 **详细配置**: 完整配置步骤请参考 [[CANVAS_AUTO_SYNC_GUIDE#方法一：Obsidian Plugin集成]]

### 概述
利用Obsidian的Shell Commands插件，监听Canvas文件保存事件，自动触发同步脚本。

---

## 方法二：Python Watchdog监控（✅ 推荐）

> 💡 **详细使用**: 完整使用方法请参考 [[CANVAS_AUTO_SYNC_GUIDE#方法二：Watchdog后台监控]]

### 概述
使用Python的watchdog库实现文件系统级别的监控，作为后台服务持续运行。

### ✅ 测试验证
```bash
# 依赖检查 ✅
pip install PyYAML watchdog  # 已安装成功

# 启动测试 ✅
python scripts/canvas/canvas_watcher.py --project-path "example/test_single_layer"
# 结果：可正常启动，开始监控Canvas文件变化
```

### 快速启动（经验证）
```bash
# 方式1：直接启动
python scripts/canvas/canvas_watcher.py

# 方式2：后台运行
python scripts/canvas/canvas_watcher.py --daemon

# 方式3：使用启动脚本
bash scripts/canvas/start_canvas_watcher.sh start --daemon
```

---

## 📊 同步功能详解

### INDEX→Canvas同步（✅ 验证通过）
```bash
# 测试命令
python scripts/canvas/auto_link_documents.py --sync-to-canvas

# 测试结果
✅ 成功：2 个文档节点, 2 个目录组, 0 条边
✅ 自动创建目录组：deliverables, project_management
✅ 保留手动调整：增量模式保护现有布局
```

### Canvas→INDEX同步（✅ 验证通过）
```bash
# 测试场景：在Canvas中手动添加连线 PM002 → DEL001
# 同步命令
python scripts/canvas/auto_link_documents.py --sync-from-canvas

# 测试结果
✅ 成功：1/1 个文档更新成功
✅ 关联关系正确写入INDEX文件
```

### 目录分组机制（✅ 验证通过）
- **自动创建**: 根据文档父目录自动创建Canvas分组
- **位置计算**: 不同组件类型分配到不同X坐标区域
- **智能布局**: 支持多列布局，自动处理文档溢出

---

## 🚀 推荐使用策略

### 当前环境推荐
基于测试结果，针对不同使用场景的推荐：

| 使用场景 | 推荐方法 | 理由 |
|----------|----------|------|
| **个人开发** | 方法2 (Watchdog) | 方法1需解决jq依赖，方法2开箱即用 |
| **团队协作** | 方法2 (Watchdog) | 可作为后台服务，自动化程度高 |
| **CI/CD集成** | 方法2 (Watchdog) | 适合无界面环境，稳定可靠 |

### 实施建议
1. **立即可用**: 使用方法2进行Canvas自动监控
2. **后续优化**: 安装jq工具后启用方法1作为备选
3. **混合使用**: 开发时用方法1，部署时用方法2

---

## 📋 故障排除

> 🔍 **完整故障排除**: 详细调试方法请参考 [[CANVAS_AUTO_SYNC_GUIDE#故障排除]]

### 方法1常见问题
```bash
# 问题1：edges提取失败
# 现象：[ERROR] 提取edges失败
# 解决：安装jq工具或依赖Python备用方法

# 问题2：环境变量未设置
# 检查：echo $env:PRODUCT_DEVELOP_DIR (PowerShell)
# 设置：$env:PRODUCT_DEVELOP_DIR = "项目路径"
```

### 方法2常见问题
```bash
# 问题1：模块导入失败
# 解决：pip install PyYAML watchdog

# 问题2：权限问题
# 解决：确保Python脚本有执行权限
```

---

## 📚 相关文档

- [[CANVAS_AUTO_SYNC_GUIDE]] - 完整使用指南和配置方法
- [[README#Canvas可视化模块]] - 技术架构和API文档
- [[canvas_watcher_config.yml]] - 监控器配置模板
- [[start_canvas_watcher.sh]] - 监控器启动脚本

---

## 🔄 版本更新记录

### v1.2.0 (当前版本)
- ✅ 验证了两种自动同步方法的可用性
- ✅ 确认INDEX→Canvas同步会自动创建目录组
- ✅ 方法2 (Watchdog) 在当前环境完全可用
- ⚠️ 方法1需要解决jq工具依赖问题
- 📖 完善了文档双链体系和单一来源原则

