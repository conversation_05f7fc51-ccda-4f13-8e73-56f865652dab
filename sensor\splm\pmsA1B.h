#ifndef _PMS_A1B_H_
#define _PMS_A1B_H_

#include "IPms.h"
#include "IComm.h"
#include "processListA.h"

#include "vi5300.h"
#include "vl53l4cd.h"

class CPmsA1B:public IPms {
  Q_OBJECT
public:
    CPmsA1B(IComm *port_);
    ~CPmsA1B();


    enum EProtocolId {
        eCROSS_TALK     = 0x01, //cross talk
        eREF            = 0x02, //ref
        eMODIFY_PARAM   = 0x03,
        eMODE_CHANGE    = 0x04,

        eDATA_OUTPUT    = 0x05,
        eLOG_OUTPUT     = 0x06,

        eREAD_VERSION   = 0x07,
        eREAD_PARAM     = 0x08,
        eCHIP_ID        = 0x09,

        eFPS_COUNTS     = 0x20, //
    };

    /*模式与枚举*/
    enum EMode{

    };


    typedef CProcessListA<ISpmsSoc, EExecStatus, CSpmsVi5300::ECalibProcess> TCalib_process;
    TCalib_process* mc_calib_process_ = nullptr;
    TCalib_process::StStepRecord *mst_calib_task_status_ = nullptr;

    QVector<StCalibItems> m_calib_items;
//    QVector<StTestItem> m_test_items;
//    QMap<QString, int> m_xml_param;

    QVector<TCalib_process::StTask> mv_calib_task_list;
//    QVector<TVerify_process::StTask> mv_accuracyVerify_task_list;

    QByteArray portDataRead(void) override;
    void icom_change_interface(IComm *port_) override;

    bool modeChange(const uint16_t &mode) override;
    bool changeRigster() override;
    bool readInfo(const uint8_t &id, const uint16_t &data) override;

    void calibTasksInit() override;
    void verifyTasksInit() override;

//    EExecStatus calibTasksRun() override;
    EExecStatus verifyTasksRun() override;

    bool interactionParsing(QByteArray str, int length) override;
    bool dataParsing(QByteArray str, int length) override;

private:
    IComm *mi_port_ = nullptr;
    QByteArray m_str_send; //指令数据

//    uint8_t m_version;

    void cmd_init();

    //* calib
//    EExecStatus calib(void);
//    EExecStatus calibAck(void);

//    EExecStatus calibRotate(void);
//    EExecStatus calibRotateAck(void);

//    EExecStatus xtalkCalib(void);
//    EExecStatus xtalkCalibAck(void);

//    EExecStatus calibRotate2(void);
//    EExecStatus calibRotate2Ack(void);

//    EExecStatus refCalib(void);
//    EExecStatus refCalibAck(void);

    //* accuracy
    EExecStatus checkRotate(void);
    EExecStatus checkRotateAck(void);

    EExecStatus distCheck(void);
    EExecStatus distCheckAck(void);

    //* trigger
    EExecStatus trigRotate(void);
    EExecStatus trigRotateAck(void);

signals:
    void dataOutput(EProtocolId step, ECommStatus status, QByteArray bytes);

public slots:
    void cmdUpdate(const uint8_t &cmd_id, const QString &cmd_name, const QByteArray &cmd);
};



#endif
