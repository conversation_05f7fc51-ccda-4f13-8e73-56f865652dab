#include "processList.h"


#if 0
/*
 * @brief: 阻塞方式管理任务集(不行，UI会卡顿)
 * @param: m_stStatus_rec_->cur_step    m_stStatus_rec_->cur_status = status
 */
void cLensAdjust::processLoop(uint16_t &step, uint16_t &status)
{
    /* */
    for (;;) {
        m_status_flag_->status.deviec_start = true;
        m_lens_adjust_serial_->m_task_id = 2;
        step = monitor_process_two::start_step;
        status = monitor_process_two::start;

        /*1. init*/
        m_map_data_->mp_adjust.sub_adjust_step = 1;
        m_map_data_->mp_adjust.cur_adjust_step = 1;
        m_map_data_->mp_adjust.theta = 0;
        m_map_data_->mp_adjust.peak_limit_cnt = 0;

        m_device_info_->move_delta.x = 0;
        m_device_info_->move_delta.y = 0;
        m_device_info_->move_delta.z = 0;

        m_status_flag_->status.device_moved = true;
        statusBarUpdate(monitor_process_two::start_step, monitor_process_two::none);

        /*2.wait device catch up mirror completed*/
        while((monitor_process_two::exit_step != step) && (monitor_process_two::ok != (status & monitor_process_two::ok)));
        m_status_flag_->status.deviec_start = false;
        if(monitor_process_two::exit_step != step)
        {
            statusBarUpdate(monitor_process_two::start_step, monitor_process_two::ok);
            m_lens_adjust_serial_->m_task_id = 1; //
//            step &= ~monitor_process_two::start_step;
            status = monitor_process_two::none; //
        }

        /*3.adjusting */
        while(monitor_process_two::exit_step != step) //device control
        {
            /*sensor数据接收和设备控制互锁？接收与控制一定交替？用步骤互锁*/
            if(((step & monitor_process_two::adjust_step)== monitor_process_two::adjust_step) && (status != monitor_process_two::none))
            {
                if((status & monitor_process_two::ok) == monitor_process_two::ok) //device ack ok
                {
                    m_status_flag_->status.device_moved = true;
                    m_lens_adjust_serial_->m_strPre.clear(); //重新开始点云接收数据
                    m_lens_adjust_serial_->m_task_id = 1;
                    m_mean_cnt = 0;
                    m_map_data_cache.clear();
                    status = monitor_process_two::start;
                    qDebug() << "-clen: device ack ok";
                }
                else if((status & monitor_process_two::error) == monitor_process_two::error) //device ack check error
                {
//                    status &= ~monitor_process_two::error; //
                    ++ m_sensor_comm_status_->check_error_cnt;
                    qDebug() << "-clen: device ack error:" << m_sensor_comm_status_->check_error_cnt;
                }
                else if((status & monitor_process_two::timeout) == monitor_process_two::timeout) //device ack timeout
                {
                    ++ m_sensor_comm_status_->timeout_cnt;
                    qDebug() << "-clen: device ack timeout num:" << m_sensor_comm_status_->timeout_cnt;
                    step = monitor_process_two::exit_step;
                    break;
                }
                statusBarUpdate(monitor_process_two::adjust_step, monitor_process_two::STEP_STATUS(status));
                status = monitor_process_two::none;
                step &= ~monitor_process_two::adjust_step;
            }
            else if(((step & monitor_process_two::data_step) == monitor_process_two::data_step) && (status != monitor_process_two::none))
            {
                if((status & monitor_process_two::ok) == monitor_process_two::ok) //data input
                {
                    if(originDataHanlde())
                    {
                        if(m_config_->mode == e_mode::auto_mode && m_status_flag_->status.device_moved) //data input
                        {
                            m_my_interPolation_->kalman_filter();
                            m_my_interPolation_->bilinear_interpolation(m_map_data_->map_matrix, m_map_interpolation_data_->map_matrix); //
                            m_my_interPolation_->Convolution_filter();
#if 0
                            faculaAdjust(m_map_interpolation_data_); //拓展图->自动校准
#else
                            if(faculaAdjust(m_map_data_)) //原图->自动校准
                            {
                                step = monitor_process_two::complete_step;
                                break;
                            }
#endif
                            deviceSpeedHandle(&m_device_info_->cur_move_distance);

                            greyMapShow(ui->greyMap, *m_map_data_); //正常图
                            greyMapShow(ui->greyMapExpand, *m_map_interpolation_data_); //拓展图
                        }
                        else if(m_config_->mode == e_mode::manual_mode && m_status_flag_->status.manual_move)
                        {
                            m_status_flag_->status.manual_move = false;
                            deviceSpeedHandle(&m_device_info_->cur_move_distance);
                            greyMapShow(ui->handleGreyMap, *m_map_data_); //正常图
                        }
                    }
                    statusBarUpdate(monitor_process_two::data_step, monitor_process_two::ok);
                }
                else if((status & monitor_process_two::error) == monitor_process_two::error) //sensor ack check error
                {
                    ++ m_sensor_comm_status_->check_error_cnt;
                    qDebug() << "-clen: sensor ack error:" << m_sensor_comm_status_->check_error_cnt;
                }
                else if((status & monitor_process_two::timeout) == monitor_process_two::timeout) //退出serial线程
                {
                    ++ m_sensor_comm_status_->timeout_cnt;
                    qDebug() << "-clen: sensor ack timeout num:" << m_sensor_comm_status_->timeout_cnt;
                    step = monitor_process_two::exit_step; //exit
                    break;
                }
                statusBarUpdate(monitor_process_two::data_step, (monitor_process_two::STEP_STATUS)status);
                status = monitor_process_two::none; //
                step &= ~monitor_process_two::data_step;
            }
        }

        /*4. complete*/
        if(step == monitor_process_two::exit_step) //退出
        {
            m_lens_adjust_serial_->machineExit();
            Sleep (50); //等待数据发送完毕
            m_lens_adjust_serial_->m_serial_port->close();
            m_lens_adjust_serial_->m_device_serial_port->close();

            m_lens_adjust_serial_->m_task_id = 0;
            ui->serialOpen->setText("open");
            statusBarUpdate(monitor_process_two::ready_step, monitor_process_two::none);
            break;
        }
        else if(step == monitor_process_two::complete_step)
        {
            resultShow();
            statusBarUpdate(monitor_process_two::complete_step, monitor_process_two::ok);
        }
        step = 0;
        m_lens_adjust_serial_->m_task_id = 4;
    };
}
#endif
