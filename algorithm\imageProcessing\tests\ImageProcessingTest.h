#ifndef IMAGEPROCESSING_TEST_H
#define IMAGEPROCESSING_TEST_H

#include "../common/ImageData.h"
#include "../interfaces/IImageFilter.h"
#include "../interfaces/IInterpolation.h"
#include "../factories/FilterFactory.h"
#include "../factories/InterpolationFactory.h"
#include <QDebug>
#include <QVector>
#include <QString>
#include <memory>

namespace ImageProcessing {

/**
 * @brief 图像处理测试工具类
 * 
 * 提供独立的测试框架，用于验证滤波器和插值算法的正确性
 */
class ImageProcessingTest {
public:
    /**
     * @brief 构造函数
     */
    ImageProcessingTest();

    /**
     * @brief 析构函数
     */
    ~ImageProcessingTest() = default;

    /**
     * @brief 运行所有测试
     * @return 测试是否全部通过
     */
    bool runAllTests();

    /**
     * @brief 测试卷积滤波器
     * @return 测试是否通过
     */
    bool testConvolutionFilter();

    /**
     * @brief 测试加权均值滤波器
     * @return 测试是否通过
     */
    bool testWeightedAverageFilter();

    /**
     * @brief 测试卡尔曼滤波器
     * @return 测试是否通过
     */
    bool testKalmanFilter();

    /**
     * @brief 测试中值滤波器
     * @return 测试是否通过
     */
    bool testMedianFilter();

    /**
     * @brief 测试高斯滤波器
     * @return 测试是否通过
     */
    bool testGaussianFilter();

    /**
     * @brief 测试双边滤波器
     * @return 测试是否通过
     */
    bool testBilateralFilter();

    /**
     * @brief 测试插值算法
     * @return 测试是否通过
     */
    bool testInterpolation();

    /**
     * @brief 创建5x5测试数据
     * @return 测试图像数据
     */
    static ImageDataU32 create5x5TestData();

    /**
     * @brief 创建3x3测试数据
     * @return 测试图像数据
     */
    static ImageDataU32 create3x3TestData();

    /**
     * @brief 验证卷积结果
     * @param input 输入数据
     * @param output 输出数据
     * @param expected 期望结果
     * @param tolerance 容差
     * @return 验证是否通过
     */
    static bool verifyConvolutionResult(const ImageDataU32& input, 
                                       const ImageDataU32& output,
                                       const QVector<QVector<uint32_t>>& expected,
                                       uint32_t tolerance = 1);

    /**
     * @brief 打印图像数据
     * @param data 图像数据
     * @param title 标题
     */
    static void printImageData(const ImageDataU32& data, const QString& title);

    /**
     * @brief 比较两个图像数据
     * @param data1 图像数据1
     * @param data2 图像数据2
     * @param tolerance 容差
     * @return 是否相等
     */
    static bool compareImageData(const ImageDataU32& data1, 
                                const ImageDataU32& data2, 
                                uint32_t tolerance = 0);

    /**
     * @brief 计算图像数据的统计信息
     * @param data 图像数据
     * @return 统计信息字符串
     */
    static QString getImageStatistics(const ImageDataU32& data);

private:
    int m_passedTests;    ///< 通过的测试数量
    int m_totalTests;     ///< 总测试数量

    /**
     * @brief 记录测试结果
     * @param testName 测试名称
     * @param passed 是否通过
     */
    void recordTestResult(const QString& testName, bool passed);

    /**
     * @brief 输出测试日志
     * @param message 日志信息
     */
    void logTest(const QString& message);

    /**
     * @brief 输出错误日志
     * @param message 错误信息
     */
    void logError(const QString& message);
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_TEST_H
