#ifndef _LENS_ADJUST_SERIAL_H_
#define _LENS_ADJUST_SERIAL_H_

#include <QDebug>
#include <QMessageBox>
#include <QMutex>
#include <QString>
#include <QTime>
#include <QTimer>


#include "ITopBoard.h"
#include "IclensMachine.h"

class CLensAdjustSerial : public QObject {
    Q_OBJECT
  public:
    explicit CLensAdjustSerial(QObject *parent = nullptr, ITopBoard *top_board_ = nullptr, IClensMachine *machine_ = nullptr);
    ~CLensAdjustSerial();

    void device_change_interface(ITopBoard *top_board_, IClensMachine *clen_machine_);  //设备接口调整
    void task_id_change(const uint8_t &id);
  public slots:
    // void portInit(bool isOpen);
    void loop(const bool &is_exc);  //接收串口数据API

  private:
    uint16_t m_task_id;

    ITopBoard *    mc_top_board_ = nullptr;
    IClensMachine *mc_machine_   = nullptr;

    QMutex m_mutex;  // 添加互斥锁
};

#endif
