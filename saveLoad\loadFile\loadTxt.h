#ifndef _LOAD_TXT_H_
#define _LOAD_TXT_H_

#include <ILoad.h>
#include <QTextStream>

class CLoadTxt:public ILoad
{
public:
    CLoadTxt();
    ~CLoadTxt();

    bool loadFile(const QString &filename) override;

    void readParam(const QString &filename, QMap<QString, int> *xml_read) override;
    void readParam(const QString &filename, QMap<QString, QByteArray> *xml_read) override;
    void readParam(const QString &filename, QMap<QString, QString> *xml_read) override;
    bool readParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_read) override;
    bool writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QByteArray> *xml_write) override;
    bool writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_write) override;

private:
    //  QFile *m_read_file_ = nullptr;
    QByteArray m_bytes_all;
    QString m_str_all;

    void dataPrasing(QString &str, QMap<QString, QString> *xml_read);
};

#endif // PARAMLOAD_H
