# MCP服务器集成与AI调用技术方案

## 概述

本文档详细描述VSCode/Cursor AI环境通过MCP (Model Context Protocol) 服务器调用工业软件的完整技术路径，包括MCP服务器的实现方式、AI调用机制、数据流转和集成配置。

## 技术架构

### 整体数据流

```
用户自然语言指令 → VSCode/Cursor AI → MCP客户端 → MCP服务器 → 工业软件接口 → 硬件测试 → 结果返回 → AI分析展示
```

### 核心组件关系

```
┌─────────────────────────────────┐
│     VSCode/Cursor AI环境        │
│  ┌─────────────────────────────┐│
│  │      MCP 客户端             ││
│  │  - 自然语言解析             ││
│  │  - 工具调用管理             ││
│  │  - 结果展示分析             ││
│  └─────────────────────────────┘│
└─────────────────────────────────┘
                 ↓ MCP Protocol
┌─────────────────────────────────┐
│        MCP 服务器模块           │
│  ┌─────────────────────────────┐│
│  │     协议适配层              ││
│  │  - tools/list              ││
│  │  - tools/call              ││
│  │  - 参数验证                ││
│  └─────────────────────────────┘│
└─────────────────────────────────┘
                 ↓ API调用
┌─────────────────────────────────┐
│       工业软件(Qt/C++)          │
│  ┌─────────────────────────────┐│
│  │     业务逻辑层              ││
│  │  - 测试流程控制             ││
│  │  - 硬件接口管理             ││
│  │  - 数据采集处理             ││
│  └─────────────────────────────┘│
└─────────────────────────────────┘
```

## MCP服务器实现方案

### 1. 服务器架构选择

#### 方案A：内嵌式MCP服务器
- **实现方式**：在Qt应用内部集成MCP服务器
- **技术栈**：Qt HttpServer + 自定义MCP协议实现
- **优势**：部署简单，延迟低，资源占用少
- **适用场景**：单机测试环境，简单集成需求

#### 方案B：独立MCP服务器进程
- **实现方式**：使用Python/Node.js实现独立的MCP服务器
- **技术栈**：Python MCP SDK + FastAPI 或 Node.js MCP SDK
- **优势**：协议标准，易扩展，支持多客户端
- **适用场景**：复杂测试环境，多用户访问

### 2. 推荐实现架构 (方案B)

#### MCP服务器技术栈
```python
# Python MCP服务器实现
from mcp.server.fastmcp import FastMCP
from mcp.server.models import Tool
import asyncio
import httpx

app = FastMCP("Industrial Test Server")

@app.tool()
async def start_test(project: str, plan_id: str, options: dict = None) -> dict:
    """启动指定的测试计划"""
    # 调用Qt应用的HTTP接口
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8080/api/test/start",
            json={
                "project": project,
                "planId": plan_id,
                "options": options or {}
            }
        )
        return response.json()

@app.tool()
async def get_test_status(task_id: str) -> dict:
    """获取测试状态"""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"http://localhost:8080/api/test/status/{task_id}")
        return response.json()

@app.tool()
async def get_test_report(task_id: str, format: str = "json") -> dict:
    """获取测试报告"""
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"http://localhost:8080/api/test/report/{task_id}",
            params={"format": format}
        )
        return response.json()
```

#### Qt应用HTTP接口
```cpp
// Qt应用中的HTTP服务器接口
class TestApiServer : public QObject {
    Q_OBJECT
    
public:
    TestApiServer(QObject *parent = nullptr);
    void setupRoutes();
    
private slots:
    void handleStartTest(const QHttpServerRequest &request);
    void handleGetStatus(const QHttpServerRequest &request);
    void handleGetReport(const QHttpServerRequest &request);
    
private:
    QHttpServer *m_httpServer;
    TestController *m_testController;
};

void TestApiServer::setupRoutes() {
    // 启动测试接口
    m_httpServer->route("/api/test/start", QHttpServerRequest::Method::Post,
        [this](const QHttpServerRequest &request) {
            return handleStartTest(request);
        });
    
    // 获取状态接口
    m_httpServer->route("/api/test/status/<arg>", QHttpServerRequest::Method::Get,
        [this](const QString &taskId, const QHttpServerRequest &request) {
            return handleGetStatus(taskId, request);
        });
    
    // 获取报告接口
    m_httpServer->route("/api/test/report/<arg>", QHttpServerRequest::Method::Get,
        [this](const QString &taskId, const QHttpServerRequest &request) {
            return handleGetReport(taskId, request);
        });
}
```

## VSCode/Cursor AI集成配置

### 1. MCP客户端配置

#### VSCode配置 (.vscode/mcp.json)
```json
{
  "mcpServers": {
    "industrial-test": {
      "command": "python",
      "args": ["-m", "mcp_server.main"],
      "cwd": "./mcp_server",
      "env": {
        "QT_APP_HOST": "localhost",
        "QT_APP_PORT": "8080",
        "MCP_API_KEY": "${MCP_API_KEY}",
        "LOG_LEVEL": "INFO"
      }
    }
  }
}
```

#### Cursor配置 (.cursor/mcp.json)
```json
{
  "mcpServers": {
    "industrial-test": {
      "command": "uvx",
      "args": ["--from", "mcp-server-industrial", "run-server"],
      "env": {
        "INDUSTRIAL_SOFTWARE_URL": "http://localhost:8080",
        "MCP_SERVER_PORT": "3001"
      }
    }
  }
}
```

### 2. AI助手工具发现机制

#### 工具注册流程
1. **MCP服务器启动**：注册所有可用工具到工具列表
2. **AI助手连接**：通过`tools/list`请求获取工具清单
3. **自然语言解析**：AI分析用户意图并匹配相应工具
4. **工具调用**：通过`tools/call`执行具体功能

#### 工具元数据定义
```python
# 工具元数据示例
tools_metadata = {
    "start_test": {
        "name": "start_test",
        "description": "启动指定的工业软件测试计划",
        "inputSchema": {
            "type": "object",
            "properties": {
                "project": {
                    "type": "string",
                    "description": "项目名称"
                },
                "plan_id": {
                    "type": "string", 
                    "description": "测试计划ID"
                },
                "options": {
                    "type": "object",
                    "description": "可选配置参数"
                }
            },
            "required": ["project", "plan_id"]
        }
    }
}
```

## AI调用流程详解

### 1. 自然语言指令处理

#### 典型对话流程
```
用户输入: "请启动ProjectX的Plan1测试计划"

AI解析步骤:
1. 意图识别: 启动测试
2. 参数提取: project="ProjectX", plan_id="Plan1"
3. 工具匹配: start_test工具
4. 参数验证: 检查必需参数完整性
5. 工具调用: 发起MCP tools/call请求

MCP服务器处理:
1. 接收调用请求
2. 参数验证和预处理
3. 调用Qt应用HTTP接口
4. 等待响应并处理结果
5. 返回结构化响应给AI

AI结果展示:
1. 解析MCP服务器响应
2. 生成用户友好的回复
3. 提取关键信息(如任务ID)
4. 准备后续交互上下文
```

### 2. 错误处理和重试机制

#### 错误类型和处理策略
```python
class MCPCallHandler:
    async def call_with_retry(self, tool_name: str, parameters: dict, max_retries: int = 3):
        for attempt in range(max_retries):
            try:
                result = await self.mcp_client.call_tool(tool_name, parameters)
                return result
            except ConnectionError as e:
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                    continue
                raise MCPConnectionError(f"无法连接到工业软件: {e}")
            except ValidationError as e:
                # 参数验证错误不重试
                raise MCPValidationError(f"参数验证失败: {e}")
            except TimeoutError as e:
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue
                raise MCPTimeoutError(f"工业软件响应超时: {e}")
```

### 3. 实时状态更新

#### WebSocket连接方案
```python
# MCP服务器WebSocket支持
@app.websocket("/ws/test_progress")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        # 订阅Qt应用的状态更新
        async for status_update in qt_app_status_stream():
            await websocket.send_json({
                "type": "status_update",
                "data": status_update
            })
    except WebSocketDisconnect:
        pass

# AI助手状态监控
async def monitor_test_progress(task_id: str):
    """监控测试进度并实时更新"""
    uri = f"ws://localhost:3001/ws/test_progress?task_id={task_id}"
    async with websockets.connect(uri) as websocket:
        async for message in websocket:
            update = json.loads(message)
            yield f"测试进度更新: {update['data']['progress']}%"
```

## 跨设备访问集成

### 多设备部署支持

对于需要在多台设备间进行测试控制和数据共享的场景，本MCP服务器支持跨设备访问。具体的网络架构设计、安全认证机制、数据共享策略等详细方案请参考：

**详细技术方案**：[[跨设备访问与安全方案]]

### 基础配置示例

#### 简单跨设备配置
```json
// 客户端配置示例
{
  "mcpServers": {
    "remote-test": {
      "command": "python",
      "args": ["-m", "mcp_client_remote"],
      "env": {
        "REMOTE_MCP_URL": "https://test-server.local:3001",
        "MCP_API_KEY": "${REMOTE_API_KEY}"
      }
    }
  }
}
```

#### 安全连接配置
```python
# 基础安全配置
SECURITY_CONFIG = {
    "ssl_enabled": True,
    "api_key_required": True,
    "ip_whitelist_enabled": True
}
```

更多跨设备部署的详细配置、安全策略、故障恢复等内容请查看：[[跨设备访问与安全方案]]

## 部署和运维

### 1. 部署脚本

#### 自动化部署脚本
```bash
#!/bin/bash
# deploy_mcp_server.sh

# 环境检查
check_dependencies() {
    echo "检查依赖环境..."
    python3 --version || { echo "Python3未安装"; exit 1; }
    pip3 show mcp || { echo "安装MCP SDK"; pip3 install mcp; }
}

# 配置生成
generate_config() {
    echo "生成配置文件..."
    cat > mcp_server_config.json << EOF
{
    "server": {
        "host": "${MCP_HOST:-localhost}",
        "port": ${MCP_PORT:-3001}
    },
    "qt_app": {
        "host": "${QT_APP_HOST:-localhost}",
        "port": ${QT_APP_PORT:-8080}
    }
}
EOF
}

# 启动服务
start_service() {
    echo "启动MCP服务器..."
    python3 -m mcp_server.main --config mcp_server_config.json &
    echo $! > mcp_server.pid
    echo "MCP服务器已启动，PID: $(cat mcp_server.pid)"
}

# 执行部署
check_dependencies
generate_config
start_service
```

### 2. 监控和日志

#### 日志配置
```python
import logging
import structlog

# 结构化日志配置
logging.basicConfig(
    format="%(message)s",
    stream=sys.stdout,
    level=logging.INFO,
)

structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# 使用示例
@app.tool()
async def start_test(project: str, plan_id: str):
    logger.info("开始测试", project=project, plan_id=plan_id)
    try:
        result = await execute_test(project, plan_id)
        logger.info("测试启动成功", task_id=result.get("taskId"))
        return result
    except Exception as e:
        logger.error("测试启动失败", error=str(e), project=project, plan_id=plan_id)
        raise
```

## 与其他文档的关联

- **主要技术实现**：[[工业软件设计文档]] - Qt/C++工业软件的具体架构设计
- **整体框架**：[[功能测试一体化框架]] - 完整的测试体系架构
- **产品体系集成**：[[产品体系构建框架]] - 与整个产品开发流程的集成

## 总结

本方案通过MCP协议实现了VSCode/Cursor AI环境与工业软件的无缝集成，提供了：

1. **标准化接口**：基于MCP协议的工具调用机制
2. **自然语言交互**：AI助手的智能意图识别和工具匹配
3. **跨设备支持**：安全的网络访问和认证机制
4. **实时反馈**：WebSocket实时状态更新
5. **可扩展架构**：模块化设计支持功能扩展

该方案确保了测试流程的自动化、智能化和安全性，为工业软件的AI辅助操作提供了完整的技术基础。 