{"version": "2.0.0", "tasks": [{"label": "🏗️ 初始化产品项目", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/init_product_project.py", "--project_name=${input:productName}", "--structure_type=${input:structureType}", "--project_path=${input:projectPath}"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": {"kind": "build", "isDefault": true}}, {"label": "🔗 配置文档关联系统", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/links/auto_link_documents.py", "--project-path=${input:projectPath}", "--all", "--register"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "build"}, {"label": "📊 生成文档关联报告", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/links/auto_link_documents.py", "--project-path=${input:projectPath}", "--report"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "🔄 更新文档关联关系", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/links/auto_link_documents.py", "--project-path=${input:projectPath}", "--all", "--register"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "🎨 同步Canvas可视化", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/canvas/auto_link_documents.py", "${input:projectPath}", "--sync-to-canvas"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "build"}, {"label": "✅ 验证Canvas同步状态", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/canvas/auto_link_documents.py", "${input:projectPath}", "--validate-sync"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "📈 查看Canvas统计信息", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/canvas/auto_link_documents.py", "${input:projectPath}", "--stats"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "📊 配置信息追溯系统", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/infoTrace/auto_index_manager.py", "--project-path=${input:projectPath}", "--scan", "--all"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "group": "build"}, {"label": "📋 生成追溯关系报告", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/infoTrace/auto_index_manager.py", "--project-path=${input:projectPath}", "--report"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "🔍 验证信息追溯系统", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/infoTrace/auto_index_manager.py", "--project-path=${input:projectPath}", "--validate"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "⚡ 增量扫描追溯系统", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/infoTrace/auto_index_manager.py", "--project-path=${input:projectPath}", "--scan", "--incremental"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "📝 查看产品命名规则", "type": "shell", "command": "code", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/project_initialization/tasks/naming_conventions.md"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "🎯 产品体系可视化", "type": "shell", "command": "python", "args": ["${env:PRODUCT_DEVELOP_DIR}/scripts/visualization/quickviz.py"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "🔬 验证整体系统集成", "type": "shell", "command": "python", "args": ["-c", "print('执行系统集成验证...'); import subprocess; import sys; scripts_dir='${env:PRODUCT_DEVELOP_DIR}/scripts'; project_path='${input:projectPath}'; subprocess.run([sys.executable, f'{scripts_dir}/links/auto_link_documents.py', '--project-path', project_path, '--validate']); subprocess.run([sys.executable, f'{scripts_dir}/canvas/auto_link_documents.py', project_path, '--validate-sync']); subprocess.run([sys.executable, f'{scripts_dir}/infoTrace/auto_index_manager.py', '--project-path', project_path, '--validate']); print('系统集成验证完成!')"], "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "verify", "type": "shell", "command": "mvn -B verify", "group": "build"}, {"label": "test", "type": "shell", "command": "mvn -B test", "group": "test"}], "inputs": [{"id": "productName", "description": "输入产品名称 (推荐格式: [前缀]-[产品类别]-[功能描述], 例如: KA-IoT-Gateway)", "default": "KA-新产品", "type": "promptString"}, {"id": "projectPath", "description": "项目路径 (如：F:/projects)", "default": "${workspaceFolder}", "type": "promptString"}, {"id": "structureType", "description": "输入目录结构 (single_layer or multi_level)", "default": "single_layer", "type": "promptString"}, {"id": "productFamilyName", "description": "输入产品族名称 (推荐格式: [前缀]-[产品类别]-[功能描述], 例如: KA-IoT-Gateway)", "default": "KA-新产品族", "type": "promptString"}, {"id": "backupBeforeDelete", "description": "删除前是否备份", "default": "true", "type": "pickString", "options": ["true", "false"]}, {"id": "productDescription", "description": "输入产品描述（可选）", "default": "", "type": "promptString"}, {"id": "projectType", "description": "选择项目类型", "default": "full", "type": "pickString", "options": ["hardware", "software", "firmware", "hardware+software", "hardware+firmware", "firmware+software", "full"]}, {"id": "linkConfigPath", "description": "文档关联配置文件路径", "default": "./config/document_links_config.json", "type": "promptString"}, {"id": "visualOutputPath", "description": "可视化输出路径", "default": "./reports/visualizations", "type": "promptString"}, {"id": "graphType", "description": "关系图类型", "default": "network", "type": "pickString", "options": ["network", "tree", "force", "circular"]}]}