#!/usr/bin/env python3
"""
Working Hello MCP Server
经过测试的稳定版本
"""

import json
import sys

def handle_message(message):
    """处理 MCP 消息"""
    method = message.get("method")
    msg_id = message.get("id")
    params = message.get("params", {})
    
    if method == "initialize":
        return {
            "jsonrpc": "2.0",
            "id": msg_id,
            "result": {
                "protocolVersion": "2025-03-26",
                "capabilities": {
                    "tools": {"listChanged": False},
                    "experimental": {}
                },
                "serverInfo": {
                    "name": "working-hello-mcp",
                    "version": "1.0.0"
                }
            }
        }
    
    elif method == "initialized":
        return None  # 不需要响应
    
    elif method == "tools/list":
        return {
            "jsonrpc": "2.0",
            "id": msg_id,
            "result": {
                "tools": [
                    {
                        "name": "say_hello",
                        "description": "Say hello to someone",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "string",
                                    "description": "Name to greet (default: World)"
                                }
                            }
                        }
                    },
                    {
                        "name": "get_status",
                        "description": "Get server status",
                        "inputSchema": {
                            "type": "object",
                            "properties": {}
                        }
                    }
                ]
            }
        }
    
    elif method == "tools/call":
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name == "say_hello":
            name = arguments.get("name", "World")
            text = f"Hello, {name}! 🎉 Working MCP Server is running perfectly!"
        elif tool_name == "get_status":
            import platform
            text = f"✅ MCP Server Status:\n- Name: working-hello-mcp\n- Version: 1.0.0\n- Python: {sys.version.split()[0]}\n- Platform: {platform.system()}"
        else:
            return {
                "jsonrpc": "2.0",
                "id": msg_id,
                "error": {
                    "code": -32601,
                    "message": f"Tool not found: {tool_name}"
                }
            }
        
        return {
            "jsonrpc": "2.0",
            "id": msg_id,
            "result": {
                "content": [{"type": "text", "text": text}],
                "isError": False
            }
        }
    
    elif method == "ping":
        return {
            "jsonrpc": "2.0",
            "id": msg_id,
            "result": {}
        }
    
    else:
        return {
            "jsonrpc": "2.0",
            "id": msg_id,
            "error": {
                "code": -32601,
                "message": f"Method not found: {method}"
            }
        }

def main():
    """主函数 - stdio 协议处理"""
    try:
        while True:
            # 读取消息头
            headers = {}
            while True:
                line = sys.stdin.readline()
                if not line:
                    sys.exit(0)
                line = line.strip()
                if not line:
                    break
                if ': ' in line:
                    key, value = line.split(': ', 1)
                    headers[key] = value
            
            # 读取消息内容
            content_length = int(headers.get('Content-Length', 0))
            if content_length <= 0:
                continue
            
            content = sys.stdin.read(content_length)
            if not content:
                continue
                
            # 解析并处理消息
            try:
                message = json.loads(content)
                response = handle_message(message)
                
                # 发送响应
                if response:
                    response_str = json.dumps(response, ensure_ascii=False)
                    response_bytes = response_str.encode('utf-8')
                    
                    # 写入响应头和内容
                    header = f"Content-Length: {len(response_bytes)}\r\n\r\n"
                    sys.stdout.write(header)
                    sys.stdout.write(response_str)
                    sys.stdout.flush()
                    
            except json.JSONDecodeError as e:
                # JSON 解析错误
                error_response = {
                    "jsonrpc": "2.0",
                    "id": None,
                    "error": {
                        "code": -32700,
                        "message": f"Parse error: {str(e)}"
                    }
                }
                response_str = json.dumps(error_response)
                response_bytes = response_str.encode('utf-8')
                header = f"Content-Length: {len(response_bytes)}\r\n\r\n"
                sys.stdout.write(header)
                sys.stdout.write(response_str)
                sys.stdout.flush()
                
    except KeyboardInterrupt:
        sys.exit(0)
    except Exception as e:
        # 记录错误到 stderr
        print(f"MCP Server Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main() 