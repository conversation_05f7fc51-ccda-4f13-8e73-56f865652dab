#ifndef _UART_H_
#define _UART_H_

#include "IComm.h"
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QMessageBox>
//#include <QWidget>

class CUART:public IComm{
public:
  explicit CUART(const QString &port_name, const uint &baud);
  ~CUART();

  QStringList detectPort() override;
  bool scanPort(QStringList *port_list_, const QString &last_port_name) override;
  QString getPortName() override;

  bool openPort() override;
  void closePort() override;
  bool checkPort() override;

  //* 输出传输
  void setBufferSize(uint16_t bytes) override;
  QByteArray read(uint16_t wait_ms) override;
  bool write(QByteArray data) override;

private:
  QSerialPort *mc_serial_port_;
  uint  m_baud;


  bool openUartPort(QSerialPort *serial_port, const QString &port_name, const uint &baud);
  void closeUartPort(QSerialPort *serial_port);
  bool checkUartPort(QSerialPort *serial_port);

};


#endif
