# 角色权限系统设计

**文档ID**: SYS-ROLE-001  
**版本**: v1.0  
**创建日期**: 2025-01-16  
**最后更新**: 2025-01-16  
**状态**: 通用方案  
**维护人员**: 系统架构团队  
**适用范围**: 软件开发项目文档权限管理  

## 关联文档
- 📖 [[软件系统文档关联系统]] - 主系统架构
- 📝 [[文档元数据标准]] - 元数据规范
- 🔄 [[文档发布工具]] - 发布系统

## 🎯 设计目标

### 核心原则
1. **最小权限原则**: 每个角色只能访问完成工作所需的最少信息
2. **内容分离原则**: 技术内容与用户内容严格分离
3. **动态过滤原则**: 根据角色动态过滤文档内容
4. **可扩展原则**: 支持新角色和权限的灵活扩展

### 解决的问题
- ❌ 客户看到开发技术细节
- ❌ 开发人员看到商业敏感信息
- ❌ 权限管理复杂且容易出错
- ❌ 文档内容无法按角色定制

## 👥 角色体系设计

### 主要角色定义

#### 1. Customer (客户)
```yaml
customer:
  name: 客户
  code: CUS
  description: 最终用户，关心产品如何使用和配置
  
  access_needs:
    - 产品功能说明
    - 使用操作指南
    - 配置参数说明
    - 常见问题解答
    - 版本更新信息
    - 技术支持信息
  
  forbidden_content:
    - 技术实现细节
    - 代码架构设计
    - 内部开发流程
    - 商业敏感信息
    - 未发布功能信息
  
  content_level: basic
  technical_depth: minimal
```

#### 2. Developer (开发人员)
```yaml
developer:
  name: 开发人员
  code: DEV
  description: 技术实现人员，关心技术实现和代码架构
  
  access_needs:
    - 技术需求分析
    - 系统架构设计
    - 代码实现细节
    - API接口文档
    - 测试验证报告
    - 开发工具使用
  
  forbidden_content:
    - 客户商业信息
    - 合同和定价信息
    - 高层战略决策
    - 人事敏感信息
  
  content_level: technical
  technical_depth: detailed
```

#### 3. Manager (项目管理者)
```yaml
manager:
  name: 项目管理者
  code: MGR
  description: 项目管理人员，关心需求跟踪和项目进度
  
  access_needs:
    - 需求文档和变更
    - 项目进度报告
    - 质量评估报告
    - 风险评估信息
    - 资源分配情况
    - 客户沟通记录
  
  forbidden_content:
    - 详细技术实现
    - 底层代码细节
    - 开发工具配置
    - 个人技术笔记
  
  content_level: management
  technical_depth: overview
```

### 扩展角色

#### 4. Support (技术支持)
```yaml
support:
  name: 技术支持
  code: SUP
  description: 客户服务人员，需要了解产品功能和常见问题
  
  access_needs:
    - 产品功能文档
    - 故障排除指南
    - 常见问题解答
    - 客户反馈记录
    - 版本发布信息
  
  content_level: support
  technical_depth: moderate
```

#### 5. Tester (测试人员)
```yaml
tester:
  name: 测试人员
  code: TEST
  description: 质量保证人员，需要了解功能需求和测试方法
  
  access_needs:
    - 功能需求文档
    - 测试用例设计
    - 缺陷报告记录
    - 质量标准文档
  
  content_level: testing
  technical_depth: functional
```

## 🔐 权限矩阵设计

### 文档类型权限表

| 文档类型 | 客户 | 开发 | 管理 | 支持 | 测试 | 说明 |
|---------|------|------|------|------|------|------|
| **需求文档 (REQ-*)** | ✅ | ✅ | ✅ | ⚠️ | ✅ | 支持人员只看功能需求 |
| **问题文档 (ISS-*)** | ❌ | ✅ | ✅ | ⚠️ | ✅ | 客户不看技术问题 |
| **开发文档 (DEV-*)** | ❌ | ✅ | ⚠️ | ❌ | ⚠️ | 管理和测试只看概要 |
| **用户手册 (MAN-*)** | ✅ | ✅ | ✅ | ✅ | ✅ | 所有角色都可能需要 |
| **发布说明 (REL-*)** | ✅ | ❌ | ✅ | ✅ | ❌ | 开发和测试不需要 |
| **支持文档 (SUP-*)** | ✅ | ✅ | ✅ | ✅ | ✅ | 支持信息对所有角色有用 |
| **测试文档 (TEST-*)** | ❌ | ✅ | ✅ | ❌ | ✅ | 内部质量保证文档 |
| **架构文档 (ARCH-*)** | ❌ | ✅ | ⚠️ | ❌ | ❌ | 纯技术文档 |

**图例**:
- ✅ 完全访问
- ⚠️ 受限访问（过滤内容）
- ❌ 禁止访问

### 内容级别权限

#### 章节级别过滤
```yaml
section_filters:
  customer:
    remove_sections:
      - "## 技术实现"
      - "## 代码架构"
      - "## 开发细节"
      - "## 内部流程"
    
    keep_sections:
      - "## 功能说明"
      - "## 使用方法"
      - "## 配置参数"
      - "## 常见问题"
  
  manager:
    summarize_sections:
      - "## 技术实现" -> "## 实现概述"
      - "## 代码架构" -> "## 架构概要"
    
    remove_sections:
      - "## 代码细节"
      - "## 调试信息"
```

#### 标签级别过滤
```yaml
tag_filters:
  customer:
    remove_tags:
      - "[TECH]"          # 技术细节
      - "[DEV-ONLY]"       # 开发专用
      - "[INTERNAL]"       # 内部信息
      - "[DEBUG]"          # 调试信息
  
  manager:
    remove_tags:
      - "[DEV-ONLY]"
      - "[CODE-DETAIL]"
      - "[DEBUG]"
    
    transform_tags:
      - "[TECH]" -> "[TECH-SUMMARY]"
```

#### 代码块过滤
```yaml
code_filters:
  customer:
    remove_code_blocks: true
    keep_config_examples: true
  
  manager:
    remove_code_blocks: false
    simplify_code_blocks: true
    max_code_lines: 10
```

## 🛠️ 技术实现

### 权限检查引擎
```python
class PermissionEngine:
    def __init__(self, config_path):
        self.roles = self.load_roles(config_path)
        self.permissions = self.load_permissions(config_path)
        self.filters = self.load_filters(config_path)
    
    def check_document_access(self, role, doc_type):
        """检查角色是否可以访问文档类型"""
        return doc_type in self.permissions[role]['allowed_types']
    
    def get_access_level(self, role, doc_type):
        """获取角色对文档类型的访问级别"""
        if not self.check_document_access(role, doc_type):
            return 'denied'
        
        permission = self.permissions[role]['document_types'][doc_type]
        return permission.get('access_level', 'full')
    
    def filter_content(self, content, role, doc_type):
        """根据角色过滤文档内容"""
        if self.get_access_level(role, doc_type) == 'denied':
            return None
        
        filters = self.filters[role]
        filtered_content = content
        
        # 章节过滤
        filtered_content = self.apply_section_filters(
            filtered_content, filters.get('sections', {})
        )
        
        # 标签过滤
        filtered_content = self.apply_tag_filters(
            filtered_content, filters.get('tags', {})
        )
        
        # 代码块过滤
        filtered_content = self.apply_code_filters(
            filtered_content, filters.get('code', {})
        )
        
        return filtered_content
```

### 内容过滤器
```python
class ContentFilter:
    def __init__(self, role_config):
        self.role_config = role_config
    
    def remove_sections(self, content, sections_to_remove):
        """移除指定章节"""
        for section in sections_to_remove:
            pattern = rf'^{re.escape(section)}.*?(?=^#|\Z)'
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        return content
    
    def summarize_sections(self, content, section_mappings):
        """将详细章节替换为概要"""
        for original, summary in section_mappings.items():
            # 提取章节内容并生成摘要
            section_content = self.extract_section(content, original)
            if section_content:
                summary_content = self.generate_summary(section_content)
                content = content.replace(section_content, 
                                        f"{summary}\n\n{summary_content}")
        return content
    
    def filter_code_blocks(self, content, code_config):
        """过滤代码块"""
        if code_config.get('remove_code_blocks', False):
            # 移除所有代码块，但保留配置示例
            if code_config.get('keep_config_examples', False):
                content = self.keep_config_only(content)
            else:
                content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)
        
        elif code_config.get('simplify_code_blocks', False):
            # 简化代码块
            max_lines = code_config.get('max_code_lines', 10)
            content = self.simplify_code_blocks(content, max_lines)
        
        return content
```

### 权限配置文件
```yaml
# roles.yml
roles:
  customer:
    name: 客户
    permissions:
      document_types:
        requirement: { access_level: full }
        manual: { access_level: full }
        release_note: { access_level: full }
        support: { access_level: full }
        issue: { access_level: denied }
        development: { access_level: denied }
      
      content_filters:
        sections:
          remove: ["## 技术实现", "## 代码架构"]
        tags:
          remove: ["[TECH]", "[DEV-ONLY]", "[INTERNAL]"]
        code:
          remove_code_blocks: true
          keep_config_examples: true

  developer:
    name: 开发人员
    permissions:
      document_types:
        requirement: { access_level: full }
        issue: { access_level: full }
        development: { access_level: full }
        manual: { access_level: full }
        support: { access_level: full }
        release_note: { access_level: denied }
      
      content_filters:
        tags:
          remove: ["[CUSTOMER-ONLY]", "[BUSINESS-SENSITIVE]"]

  manager:
    name: 项目管理者
    permissions:
      document_types:
        requirement: { access_level: full }
        issue: { access_level: full }
        development: { access_level: summary }
        manual: { access_level: full }
        release_note: { access_level: full }
        support: { access_level: full }
      
      content_filters:
        sections:
          summarize:
            "## 技术实现": "## 实现概述"
            "## 代码架构": "## 架构概要"
        tags:
          remove: ["[DEV-ONLY]", "[CODE-DETAIL]"]
```

## 🔍 权限验证

### 自动化检查
```python
def validate_document_permissions(doc_path):
    """验证文档权限设置"""
    doc = load_document(doc_path)
    
    # 检查角色标签
    roles = doc.metadata.get('roles', [])
    for role in roles:
        if role not in VALID_ROLES:
            raise InvalidRoleError(f"Invalid role: {role}")
    
    # 检查内容标签
    content_tags = extract_content_tags(doc.content)
    for tag in content_tags:
        validate_content_tag(tag, roles)
    
    # 检查敏感内容
    sensitive_patterns = get_sensitive_patterns()
    for pattern in sensitive_patterns:
        if re.search(pattern, doc.content):
            check_role_permission(pattern, roles)

def check_role_separation():
    """检查角色分离是否正确"""
    violations = []
    
    for doc_path in get_all_documents():
        doc = load_document(doc_path)
        roles = doc.metadata.get('roles', [])
        
        # 检查客户文档是否包含技术内容
        if 'customer' in roles:
            tech_content = find_technical_content(doc.content)
            if tech_content:
                violations.append({
                    'doc': doc_path,
                    'issue': 'customer_sees_technical_content',
                    'content': tech_content
                })
    
    return violations
```

## 📊 实施计划

### 阶段1：基础权限系统 (1周)
- [ ] 角色定义和权限矩阵设计
- [ ] 权限配置文件格式设计
- [ ] 基础权限检查引擎开发

### 阶段2：内容过滤系统 (1周)
- [ ] 章节过滤器开发
- [ ] 标签过滤器开发
- [ ] 代码块过滤器开发
- [ ] 内容摘要生成器开发

### 阶段3：验证和测试 (3天)
- [ ] 权限验证脚本开发
- [ ] 角色分离检查脚本
- [ ] 自动化测试用例编写
- [ ] 性能优化和调试

## 💡 应用场景

### 适用项目类型
- 多角色协作的软件项目
- 客户参与度高的项目
- 涉及商业敏感信息的项目
- 需要严格权限控制的项目

### 团队配置
- 有明确角色分工的团队
- 需要与客户频繁沟通的团队
- 涉及多个部门协作的团队
- 对信息安全有要求的团队

---

**设计状态**: ✅ 通用方案  
**适用范围**: 软件开发项目文档权限管理  
**维护团队**: 系统架构团队  
**最后更新**: 2025-01-16
