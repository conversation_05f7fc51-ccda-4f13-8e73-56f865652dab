# Git风格的文档扫描过滤系统

## 概述

本系统实现了类似`.gitignore`的文档扫描过滤机制，允许使用熟悉的git语法来精确控制哪些文件被包含在文档扫描中。

## 功能特性

### ✅ 支持的语法

- **注释**：`# 这是注释`
- **通配符**：
  - `*` - 匹配除路径分隔符外的任意字符
  - `**` - 匹配任意路径（包括子目录）
  - `?` - 匹配单个字符
- **否定规则**：`!pattern` - 包含被排除的文件
- **目录规则**：`directory/` - 只匹配目录
- **绝对路径**：`/path/from/root` - 从项目根开始的路径

### ✅ 规则优先级

规则按文件中的顺序应用，后面的规则会覆盖前面的规则。

## 使用方法

### 1. 创建.docignore文件

在项目根目录创建`.docignore`文件：

```gitignore
# 全局排除规则
__pycache__/
*.tmp
*.log
.git/
.vscode/

# 开发目录特殊处理
development/**/*.c
development/**/*.h
development/**/*.py
development/**/*.js

# 但包含文档目录
!development/**/docs/**
!development/**/*.md
!development/**/*.txt
```

### 2. 系统自动检测

文档扫描器会自动检测`.docignore`文件：
- 如果存在，优先使用git风格规则
- 如果不存在，回退到传统JSON配置

## 实际应用示例

### 开发目录过滤

**需求**：在development目录下只扫描docs子目录的文档，忽略源代码文件

**配置**：
```gitignore
# 排除所有开发目录下的源代码
development/**/*.c
development/**/*.h
development/**/*.cpp
development/**/*.py
development/**/*.js
development/**/*.java

# 包含docs目录下的所有文件
!development/**/docs/**

# 也包含散落的文档文件
!development/**/*.md
!development/**/*.pdf
!development/**/*.docx
```

**效果**：
- ✅ `development/firmware/fw_project_1/docs/设计文档.md` - 包含
- ❌ `development/firmware/fw_project_1/main.c` - 忽略
- ✅ `development/software/app/docs/API文档.md` - 包含
- ❌ `development/software/app/main.py` - 忽略

### 多组件配置

```gitignore
# 全局排除
__pycache__/
*.tmp
.git/

# 开发组件：只要docs和文档
development/**/*.c
development/**/*.py
!development/**/docs/**
!development/**/*.md

# 质量组件：包含测试文件
!quality/**/*.feature
!quality/**/*.spec

# 生产组件：排除中间文件
production/**/intermediate/
production/**/temp/
```

## 技术实现

### 核心组件

1. **DocIgnoreParser** - 解析.docignore文件
2. **DocumentScanner** - 集成ignore规则的文档扫描器

### 关键代码

```python
# 自动检测.docignore文件
docignore_file = self.project_path / ".docignore"
if docignore_file.exists():
    self.docignore_parser = DocIgnoreParser(self.project_path)

# 优先使用.docignore规则
def _should_exclude(self, file_path: Path) -> bool:
    if self.docignore_parser:
        return self.docignore_parser.should_ignore(file_path)
    # 回退到传统配置...
```

## 调试功能

### 查看匹配规则

```python
from common.gitignore_parser import DocIgnoreParser

parser = DocIgnoreParser(".")
parser.debug_path(Path("development/project/main.c"))
```

输出：
```
=== 调试路径: development/project/main.c ===
是否忽略: True
匹配的规则:
  行39: development/**/*.c (排除)
```

## 与传统配置的对比

| 特性 | .docignore | JSON配置 |
|------|------------|----------|
| 语法熟悉度 | ✅ Git用户熟悉 | ❌ 需要学习 |
| 复杂规则 | ✅ 支持否定、通配符 | ❌ 有限支持 |
| 可读性 | ✅ 直观易懂 | ❌ 嵌套复杂 |
| 维护性 | ✅ 易于修改 | ❌ 结构固定 |
| 调试 | ✅ 规则匹配追踪 | ❌ 难以调试 |

## 最佳实践

1. **分组组织**：使用注释将规则分组
2. **先排除后包含**：先写排除规则，再用!包含特定文件
3. **测试验证**：使用调试功能验证规则效果
4. **文档说明**：在文件顶部说明规则用途

## 兼容性

- ✅ 完全向后兼容现有JSON配置
- ✅ 可以与传统配置共存
- ✅ 自动检测和切换
