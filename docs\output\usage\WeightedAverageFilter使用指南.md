# WeightedAverageFilter使用指南

**版本**: LA-T5 v1.4.4  
**更新日期**: 2025-01-16  
**适用范围**: 光斑图像处理、加权均值滤波  

## 概述

WeightedAverageFilter是一个高性能的加权均值滤波器，专门用于光斑图像处理。该滤波器支持多种预定义权重模式，能够根据不同的应用场景提供最佳的滤波效果。

### 主要特性
- ✅ **多种权重模式**：支持uniform、gaussian、center_weighted、edge_enhance、smooth等5种预设
- ✅ **边缘优化处理**：采用边缘复制技术，避免边缘像素偏小问题
- ✅ **配置灵活**：支持配置文件和代码两种配置方式
- ✅ **性能优化**：高效的算法实现，适合实时处理

## 快速开始

### 基本使用
```cpp
#include "WeightedAverageFilter.h"

// 创建滤波器
auto filter = std::make_unique<WeightedAverageFilter>();

// 设置基础参数
WeightedAverageParams params;
params.kernelSize = 3;
params.strength = 1.0f;
params.normalize = true;
params.enabled = true;

filter->setParameters(params);

// 设置权重模式
filter->setPredefinedWeights("center_weighted");

// 应用滤波
ImageDataU32 image = loadImage("input.png");
bool success = filter->apply(image);
```

### 配置文件使用
```ini
[WeightedAverage]
kernel_size=3
preset=center_weighted
strength=1.0
enabled=true
```

## 权重模式详解

### 1. uniform - 均匀权重
**适用场景**：基础平滑处理
```
权重矩阵:
1.0  1.0  1.0
1.0  1.0  1.0
1.0  1.0  1.0
```
**特点**：所有邻域像素权重相等，提供均匀的平滑效果

### 2. gaussian - 高斯权重
**适用场景**：自然平滑，保持边缘
```
权重矩阵:
1.0  2.0  1.0
2.0  4.0  2.0
1.0  2.0  1.0
```
**特点**：中心权重较高，边缘权重较低，模拟高斯分布

### 3. center_weighted - 中心加权 ⭐
**适用场景**：光斑中心增强，保持原始特征
```
权重矩阵:
1.0  2.0  1.0
2.0  8.0  2.0
1.0  2.0  1.0
```
**特点**：突出中心像素重要性，适合光斑处理

### 4. edge_enhance - 边缘增强
**适用场景**：边缘检测，特征增强
```
权重矩阵:
0.0  -1.0  0.0
-1.0  5.0  -1.0
0.0  -1.0  0.0
```
**特点**：类似锐化效果，增强边缘对比度

### 5. smooth - 强平滑
**适用场景**：噪声抑制，强平滑处理
```
权重矩阵:
1.0  4.0  1.0
4.0  12.0  4.0
1.0  4.0  1.0
```
**特点**：中心权重很高，提供强烈的平滑效果

## 配置参数说明

### 基础参数
| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| kernelSize | uint32_t | 3 | 滤波核大小（必须为奇数） |
| strength | float | 1.0 | 滤波强度（0.0-1.0） |
| normalize | bool | true | 是否归一化权重 |
| enabled | bool | true | 是否启用滤波器 |

### 高级参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| weights | QVector<QVector<float>> | 自定义权重矩阵 |
| preset | QString | 预定义权重模式名称 |

## 边界处理说明

### 🔧 v1.4.4重要更新
从v1.4.4版本开始，WeightedAverageFilter采用**边缘复制**边界处理方式，替代之前的零填充方式。

#### 修复前（零填充）
- 边界外像素值为0
- 导致边缘像素被人为拉低40-90%
- 不符合光斑物理特性

#### 修复后（边缘复制）
- 使用最近边缘像素值
- 边缘像素值提升40-90%
- 更符合实际光斑特性

#### 影响评估
- ✅ **向后兼容**：现有配置文件无需修改
- ✅ **性能提升**：边缘处理更准确
- ✅ **物理合理**：符合光斑衰减特性

## 最佳实践

### 1. 权重模式选择
```cpp
// 光斑中心增强
filter->setPredefinedWeights("center_weighted");

// 噪声抑制
filter->setPredefinedWeights("smooth");

// 边缘检测
filter->setPredefinedWeights("edge_enhance");
```

### 2. 滤波强度调节
```cpp
// 轻微滤波
params.strength = 0.3f;

// 标准滤波
params.strength = 1.0f;

// 强滤波（不推荐超过1.0）
params.strength = 1.0f;
```

### 3. 自定义权重
```cpp
// 创建自定义3x3权重矩阵
QVector<QVector<float>> customWeights = {
    {0.1f, 0.2f, 0.1f},
    {0.2f, 0.4f, 0.2f},
    {0.1f, 0.2f, 0.1f}
};

params.weights = customWeights;
filter->setParameters(params);
```

## 性能优化建议

### 1. 核大小选择
- **3x3**：快速处理，适合实时应用
- **5x5**：平衡效果与性能
- **7x7及以上**：高质量处理，计算量大

### 2. 批量处理
```cpp
// 批量处理多个图像
for (auto& image : imageList) {
    filter->apply(image);
}
```

### 3. 内存管理
```cpp
// 重用滤波器实例
static auto filter = std::make_unique<WeightedAverageFilter>();
```

## 故障排除

### 常见问题

#### Q: 边缘像素值偏小
**A**: 确保使用v1.4.4或更高版本，该版本已修复边界处理问题。

#### Q: 配置文件中的预设不生效
**A**: 确保使用v1.4.4或更高版本，该版本已修复配置流程问题。

#### Q: 滤波效果不明显
**A**: 检查strength参数设置，建议使用1.0。检查权重模式是否适合当前场景。

#### Q: 性能问题
**A**: 减小kernelSize，使用3x3核。考虑降低图像分辨率。

### 调试技巧
```cpp
// 启用详细日志
filter->setLogLevel(LogLevel::Debug);

// 检查参数有效性
try {
    filter->setParameters(params);
} catch (const InvalidParameterException& e) {
    qDebug() << "参数错误:" << e.what();
}
```

## 关联文档

- [[图像滤波器配置说明]] - 通用滤波器配置指南
- [[边界处理最佳实践]] - 边界处理方式详解
- [[光斑处理工作流程]] - 完整的光斑处理流程
- [[性能优化指南]] - 图像处理性能优化建议
- [[../issues/weighted_average_boundary_fix.md]] - v1.4.4修复报告

## 版本历史

### v1.4.4 (2025-01-16)
- 🔧 修复边界处理问题（零填充→边缘复制）
- 🔧 修复配置流程重复定义问题
- ✨ 所有预设权重模式现在可通过配置文件使用
- 📈 边缘像素值提升40-90%

### v1.4.3
- ✨ 添加smooth权重模式
- 🔧 优化权重归一化算法

### v1.4.2
- ✨ 添加edge_enhance权重模式
- 📝 完善文档和示例

---

**技术支持**: 如有问题请参考关联文档或联系开发团队  
**文档维护**: 请保持本文档与代码版本同步更新
