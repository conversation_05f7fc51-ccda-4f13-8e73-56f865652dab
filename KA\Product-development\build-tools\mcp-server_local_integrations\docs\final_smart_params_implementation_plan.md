# 智能参数管理最终实施方案

## 方案概述

基于对现有`mcp-server_auto_get_param.md`文档的深度研究，结合之前实现的智能参数管理功能，制定以下最终实施方案：

### 核心设计原则

1. **FastMCP原生优先**：充分利用FastMCP的原生功能（Annotated + Field + ctx.elicit）
2. **智能增强**：保留智能默认值、参数缓存等增强功能
3. **简化架构**：减少复杂的装饰器，更多依赖FastMCP机制
4. **用户体验**：提供流畅的多轮交互体验

## 技术架构

### 1. 参数定义标准化

使用FastMCP推荐的Annotated + Field方式：

```python
from typing import Annotated
from pydantic import Field
from fastmcp import Context

@mcp.tool(
    name="init_project",
    description="初始化项目，创建目录结构和配置文件"
)
async def init_project(
    ctx: Context,
    project_name: Annotated[str, Field(description="项目名称，用于创建项目目录")],
    structure_type: Annotated[str, Field(description="项目结构类型：single_layer（单层）或multi_level（多层）")] = "single_layer",
    project_path: Annotated[str, Field(description="项目创建路径，默认为当前目录")] = ".",
    description: Annotated[str, Field(description="项目描述信息")] = ""
) -> dict:
    """初始化项目"""
    # 智能参数处理逻辑
    pass
```

### 2. 智能参数处理流程

```
1. 参数接收 → 2. 缓存检查 → 3. 智能推断 → 4. 用户交互 → 5. 执行工具
```

### 3. 核心组件重构

#### A. SmartParamHelper（简化版参数助手）
```python
class SmartParamHelper:
    def __init__(self):
        self.cache = ParameterCache()
        self.defaults = IntelligentDefaults()
    
    async def process_params(self, ctx: Context, tool_name: str, params: dict) -> dict:
        """处理参数：缓存 → 推断 → 交互"""
        pass
    
    def get_smart_default(self, param_name: str, context: dict = None) -> str:
        """获取智能默认值"""
        pass
    
    async def elicit_missing_params(self, ctx: Context, missing_params: list) -> dict:
        """使用ctx.elicit获取缺失参数"""
        pass
```

#### B. 参数缓存系统（保留）
```python
class ParameterCache:
    def get_cached_value(self, tool_name: str, param_name: str) -> Optional[str]:
        """获取缓存值"""
        pass
    
    def cache_value(self, tool_name: str, param_name: str, value: str):
        """缓存参数值"""
        pass
```

#### C. 智能默认值系统（保留并增强）
```python
class IntelligentDefaults:
    def get_default(self, param_name: str, context: dict = None) -> str:
        """根据上下文获取智能默认值"""
        pass
    
    def detect_project_context(self) -> dict:
        """检测项目上下文信息"""
        pass
```

## 实施步骤

### 第一阶段：重构核心组件

1. **简化smart_params.py**
   - 移除复杂的装饰器逻辑
   - 保留缓存和智能默认值功能
   - 重构为SmartParamHelper类

2. **移除smart_decorators.py**
   - 不再使用复杂的装饰器
   - 改为在工具函数内部调用SmartParamHelper

3. **创建fastmcp_param_utils.py**
   - 专门为FastMCP优化的参数处理工具
   - 集成ctx.elicit功能

### 第二阶段：更新工具函数

1. **标准化参数定义**
   - 所有工具函数使用Annotated + Field
   - 添加Context参数用于用户交互
   - 详细的参数描述

2. **集成智能参数处理**
   - 在函数开始处调用SmartParamHelper
   - 处理缺失参数和智能推断
   - 缓存用户输入

### 第三阶段：优化用户体验

1. **多轮交互优化**
   - 使用ctx.elicit进行友好的参数收集
   - 提供智能建议和默认值
   - 支持批量参数输入

2. **上下文感知**
   - 项目环境检测
   - 历史操作记忆
   - 智能路径推断

## 具体实现示例

### 工具函数模板

```python
@mcp.tool(
    name="create_project_structure",
    description="根据指定结构类型创建项目目录层次"
)
async def create_project_structure(
    ctx: Context,
    project_path: Annotated[str, Field(description="项目路径，支持相对路径和绝对路径")],
    structure_type: Annotated[str, Field(description="结构类型：single_layer或multi_level")] = "single_layer",
    components: Annotated[list, Field(description="要创建的组件列表，可选")] = None
) -> dict:
    """创建项目结构"""
    
    # 1. 智能参数处理
    helper = SmartParamHelper()
    processed_params = await helper.process_params(ctx, "create_project_structure", {
        "project_path": project_path,
        "structure_type": structure_type,
        "components": components
    })
    
    # 2. 执行实际逻辑
    result = await _execute_create_structure(processed_params)
    
    # 3. 缓存成功的参数
    helper.cache_successful_params("create_project_structure", processed_params)
    
    return result
```

### 参数处理逻辑

```python
async def process_params(self, ctx: Context, tool_name: str, params: dict) -> dict:
    """智能参数处理"""
    processed = {}
    
    for param_name, param_value in params.items():
        if param_value:  # 参数已提供
            processed[param_name] = param_value
            continue
            
        # 1. 检查缓存
        cached = self.cache.get_cached_value(tool_name, param_name)
        if cached:
            # 询问是否使用缓存值
            use_cached = await ctx.elicit(
                f"参数 {param_name} 使用缓存值 '{cached}' 吗？(y/n)", 
                str
            )
            if use_cached.data.lower() in ['y', 'yes', '是']:
                processed[param_name] = cached
                continue
        
        # 2. 智能推断
        smart_default = self.defaults.get_default(param_name, processed)
        if smart_default:
            # 询问是否使用智能默认值
            use_default = await ctx.elicit(
                f"参数 {param_name} 使用推荐值 '{smart_default}' 吗？(y/n 或直接输入新值)", 
                str
            )
            if use_default.data.lower() in ['y', 'yes', '是']:
                processed[param_name] = smart_default
            else:
                processed[param_name] = use_default.data
        else:
            # 3. 直接询问用户
            user_input = await ctx.elicit(f"请输入 {param_name}:", str)
            processed[param_name] = user_input.data
        
        # 缓存用户选择
        self.cache.cache_value(tool_name, param_name, processed[param_name])
    
    return processed
```

## 优势分析

### 相比原方案的改进

1. **更好的FastMCP集成**：充分利用原生功能
2. **简化的架构**：减少复杂性，提高可维护性
3. **更好的用户体验**：流畅的多轮交互
4. **保留智能功能**：缓存、推断、默认值等

### 技术优势

1. **标准化**：遵循FastMCP最佳实践
2. **可扩展**：易于添加新的智能功能
3. **可测试**：清晰的组件分离
4. **高性能**：减少不必要的装饰器开销

## 下一步行动

1. 重构现有代码，实现新的架构
2. 更新所有工具函数，使用新的参数处理方式
3. 创建完整的测试套件
4. 更新文档和使用示例
