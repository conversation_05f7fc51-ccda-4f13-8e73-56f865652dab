# Canvas自动同步完整使用指南

> 📋 **快速参考**: 方法概述请参考 [[auto_sync_from_canvas_to_INDEX]]
> 
> 🔧 **技术实现**: 底层架构请参考 [[README#Canvas可视化模块]]

## 概述

Canvas自动同步系统提供两种可靠的方法，将Obsidian Canvas中的edges（连线）变化自动同步到项目INDEX文档中。基于 `example/test_single_layer` 项目的实际测试验证，本指南将详细介绍配置、使用和故障排除的完整流程。

### 🎯 最新验证结果

**测试环境**: `F:\101. link notebook\Obsidian Vault\KA\Product development\example\test_single_layer`

| 功能测试 | 结果 | 详情 |
|----------|------|------|
| **INDEX→Canvas同步** | ✅ 完全正常 | 2个文档节点, 2个目录组自动创建 |
| **Canvas→INDEX同步** | ✅ 完全正常 | 手动连线成功同步到INDEX文档 |
| **目录分组机制** | ✅ 完全正常 | 自动创建`deliverables`、`project_management`组 |

| 自动同步方法 | 状态 | 主要问题 | 推荐程度 |
|--------------|------|----------|----------|
| **方法1** (Obsidian Plugin) | ⚠️ 部分可用 | 缺少jq工具，edges提取失败 | ⭐⭐⭐ (需解决依赖) |
| **方法2** (Watchdog监控) | ✅ 完全可用 | PyYAML已安装，功能正常 | ⭐⭐⭐⭐⭐ (推荐使用) |

## 🎯 系统架构

```mermaid
graph TD
    A[Obsidian Canvas] --> B{变化检测}
    B --> C[方法一: Obsidian Plugin]
    B --> D[方法二: Watchdog监控]
    
    C --> E[Shell Commands插件]
    E --> F[on_canvas_save.sh]
    F --> G[edges变化检测]
    
    D --> H[canvas_watcher.py]
    H --> I[文件系统监控]
    I --> G
    
    G --> J[Python同步脚本]
    J --> K[INDEX文档更新]
    K --> L[关联关系同步]
```

## 📋 前置要求

### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python**: 3.8+
- **Obsidian**: 最新版本
- **内存**: 最低512MB可用内存
- **存储**: 至少100MB可用空间

### 依赖库安装
```bash
# 安装核心依赖
pip install watchdog>=3.0.0

# 可选依赖（用于高级功能）
pip install PyYAML>=6.0
pip install psutil>=5.8.0
```

### 环境变量设置
```bash
# Windows
setx PRODUCT_DEVELOP_DIR "F:\101. link notebook\Obsidian Vault\KA\Product development"

# macOS/Linux
export PRODUCT_DEVELOP_DIR="/path/to/your/project"
echo 'export PRODUCT_DEVELOP_DIR="/path/to/your/project"' >> ~/.bashrc
```

## 🔧 方法一：Obsidian Plugin集成（推荐新手）

### 步骤1：安装Shell Commands插件

1. 打开Obsidian → 设置 → 第三方插件
2. 禁用安全模式（如果尚未禁用）
3. 点击"浏览"，搜索"Shell Commands"
4. 安装并启用插件

### 步骤2：根据系统选择Shell配置

#### 🪟 Windows系统配置

##### 选项A：PowerShell Core（推荐）
```
Name/Alias: Canvas Auto Sync (PowerShell)
Shell: PowerShell Core (pwsh)
Command: $env:PYTHONIOENCODING="utf-8"; $env:PYTHONLEGACYWINDOWSSTDIO="utf-8"; pwsh -NoProfile -ExecutionPolicy Bypass -Command "& { & `"$env:PRODUCT_DEVELOP_DIR\scripts\canvas\on_canvas_save.ps1`" `"{{file_path:absolute}}`" }"
Description: Canvas edges变更时自动同步到INDEX文档 (Unicode+路径修复版)
```

**⚠️ 重要配置说明**:
- `PYTHONIOENCODING="utf-8"`: 解决Unicode字符显示问题（✓、✗、⚠等）
- `PYTHONLEGACYWINDOWSSTDIO="utf-8"`: 修复Windows GBK编码错误
- 路径用绝对路径避免空格问题
- 适用于包含中文或特殊字符的项目路径

##### 选项B：Git Bash
```
Name/Alias: Canvas Auto Sync (Git Bash)
Shell: Bash (通常是Git Bash)
Command: bash "$PRODUCT_DEVELOP_DIR/scripts/canvas/on_canvas_save.sh" "{{file_path:absolute}}"
Description: Canvas edges变更时自动同步到INDEX文档 (Bash)
```

#### 🐧 Linux系统配置

```
Name/Alias: Canvas Auto Sync (Linux)
Shell: Bash
Command: bash "$PRODUCT_DEVELOP_DIR/scripts/canvas/on_canvas_save.sh" "{{file_path:absolute}}"
Description: Canvas edges变更时自动同步到INDEX文档 (Linux Bash)
```

#### 🍎 macOS系统配置

```
Name/Alias: Canvas Auto Sync (macOS)
Shell: Zsh 或 Bash
Command: bash "$PRODUCT_DEVELOP_DIR/scripts/canvas/on_canvas_save.sh" "{{file_path:absolute}}"
Description: Canvas edges变更时自动同步到INDEX文档 (macOS)
```

### 📋 环境变量设置参考

#### Windows (PowerShell)
```powershell
# 临时设置（当前会话）
$env:PRODUCT_DEVELOP_DIR = "F:\101. link notebook\Obsidian Vault\KA\Product development"

# 永久设置（需要管理员权限）
[Environment]::SetEnvironmentVariable("PRODUCT_DEVELOP_DIR", "F:\101. link notebook\Obsidian Vault\KA\Product development", "User")
```

#### Windows (Git Bash)
```bash
# 在 ~/.bashrc 或 ~/.bash_profile 中添加
export PRODUCT_DEVELOP_DIR="/f/101. link notebook/Obsidian Vault/KA/Product development"
```

#### Linux/macOS
```bash
# 在 ~/.bashrc, ~/.zshrc 或 ~/.bash_profile 中添加
export PRODUCT_DEVELOP_DIR="/path/to/your/product/development"
```

### 步骤3：设置触发事件

1. 展开"Events"选项
2. 启用：**File content modified**
3. （可选）启用：**File content changed**
4. 保存配置

### 步骤4：高级配置（可选）

```
Working directory: ${env:PRODUCT_DEVELOP_DIR}
Timeout: 60 seconds
Run in background: ✓ 启用
Error handling: Show notification
```

### 步骤3：在Windows终端测试PowerShell Core命令

在配置Obsidian之前，建议先在终端测试命令是否正常工作：

#### 🧪 自动化测试脚本
```powershell
# 运行测试脚本
& "scripts/canvas/test_windows_powershell.ps1"
```

#### 🔧 手动测试步骤
```powershell
# 1. 打开PowerShell Core
pwsh

# 2. 设置环境变量
$env:PRODUCT_DEVELOP_DIR = "F:\101. link notebook\Obsidian Vault\KA\Product development"

# 3. 测试完整命令
pwsh -NoProfile -ExecutionPolicy Bypass -Command "& { & `"$env:PRODUCT_DEVELOP_DIR/scripts/canvas/on_canvas_save.ps1`" `"$env:PRODUCT_DEVELOP_DIR/example/test_single_layer/product.canvas`" }"

# 4. 查看输出，应该显示同步过程信息
```

#### ⚠️ 常见问题解决
```powershell
# 如果遇到执行策略错误
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 如果找不到pwsh命令，安装PowerShell Core
# 从 https://github.com/PowerShell/PowerShell/releases 下载安装
```

### 步骤4：验证配置和诊断

#### 🔍 诊断Obsidian Shell Commands是否正常工作

**问题症状**: 在Obsidian中保存Canvas文件后没有任何反应

**诊断步骤**:

1. **检查插件状态**:
   - 打开 Settings → Community plugins
   - 确认 "Shell Commands" 插件已启用
   - 确认命令配置存在且已启用

2. **查看插件日志**:
   - 打开 Settings → Shell Commands → Output
   - 查看是否有执行记录和错误信息
   - 查看 "Recent executions" 部分

3. **手动测试命令**:
   ```powershell
   # 在PowerShell中直接运行命令测试
   $env:PYTHONIOENCODING="utf-8"
   $env:PYTHONLEGACYWINDOWSSTDIO="utf-8"
   $env:PRODUCT_DEVELOP_DIR="F:\101. link notebook\Obsidian Vault\KA\Product development"
   pwsh -NoProfile -ExecutionPolicy Bypass -Command "& { & `"$env:PRODUCT_DEVELOP_DIR\scripts\canvas\on_canvas_save.ps1`" `"$env:PRODUCT_DEVELOP_DIR\example\test_single_layer\product.canvas`" }"
   ```

4. **检查文件事件触发**:
   - 在Shell Commands设置中，确认已勾选 "File content modified"
   - 测试文件类型：确保是 `.canvas` 文件
   - 尝试在Events配置中添加更多触发条件

5. **调试输出检查**:
   - 启用Shell Commands的"Show output"选项
   - 保存Canvas文件后查看输出面板
   - 如果有错误，会在这里显示

#### 📋 正常工作验证清单

1. 打开项目中的`product.canvas`文件
2. 添加或修改一些连线
3. 保存文件（Ctrl+S / Cmd+S）
4. **应该看到的反馈**:
   - ✅ Shell Commands输出面板显示执行记录
   - ✅ 控制台显示同步进度信息（Unicode字符正常显示）
   - ✅ INDEX文档被正确更新
   - ✅ 日志文件记录同步操作：`${PROJECT_DIR}/logs/canvas_sync.log`

```bash
# 查看同步日志 (Linux/macOS)
tail -f "${PROJECT_DIR}/logs/canvas_sync.log"
```

```powershell
# 查看同步日志 (Windows PowerShell)
Get-Content "${env:PROJECT_DIR}/logs/canvas_sync.log" -Tail 20 -Wait
```

## 🔧 方法二：Watchdog后台监控（推荐高级用户）

### 步骤1：生成配置文件

```bash
# 进入项目目录
cd "${PRODUCT_DEVELOP_DIR}"

# 生成配置文件
bash scripts/canvas/start_canvas_watcher.sh config
```

### 步骤2：自定义配置（可选）

编辑 `config/canvas_watcher.yml`：

```yaml
watcher:
  # 基本配置
  watch_delay: 0.5          # 文件变化等待时间
  sync_interval: 5          # 最小同步间隔
  max_retries: 3           # 最大重试次数
  
  # 日志配置
  log_level: INFO          # 日志级别
  max_log_size: 10MB       # 日志文件大小限制
  
  # 批处理优化
  batch_timeout: 30        # 批处理超时时间
```

### 步骤3：启动监控器

```bash
# 前台启动（调试用）
bash scripts/canvas/start_canvas_watcher.sh start

# 后台启动（日常使用）
bash scripts/canvas/start_canvas_watcher.sh start --daemon

# 使用自定义配置
bash scripts/canvas/start_canvas_watcher.sh start --config custom.yml
```

### 步骤4：监控管理

```bash
# 查看运行状态
bash scripts/canvas/start_canvas_watcher.sh status

# 查看实时日志
bash scripts/canvas/start_canvas_watcher.sh logs --follow

# 查看统计信息
bash scripts/canvas/start_canvas_watcher.sh stats

# 停止监控
bash scripts/canvas/start_canvas_watcher.sh stop

# 重启监控
bash scripts/canvas/start_canvas_watcher.sh restart
```

## 📊 监控和统计

### 日志文件位置

- **主日志**: `${PROJECT_DIR}/logs/canvas_sync.log`
- **监控器日志**: `${PROJECT_DIR}/logs/canvas_watcher.log`
- **后台运行日志**: `${PROJECT_DIR}/logs/canvas_watcher_daemon.log`

### 统计信息文件

- **同步统计**: `${PROJECT_DIR}/logs/sync_stats.json`
- **监控统计**: `${PROJECT_DIR}/logs/canvas_watcher_stats.json`

### 查看统计示例

```bash
# 人类可读格式
bash scripts/canvas/start_canvas_watcher.sh stats

# JSON格式（便于程序处理）
bash scripts/canvas/start_canvas_watcher.sh stats --json
```

输出示例：
```
Canvas监控器统计信息:
总同步次数: 45
成功同步: 43
失败同步: 2
平均耗时: 1.24秒
最后同步: 2024-12-19T14:30:15
运行时间: 2:15:30
```

## 🔍 故障排除

### 常见问题及解决方案

#### 问题1：命令不执行（详细诊断指南）

**症状**: Obsidian中保存Canvas文件后没有反应

**🔍 逐步诊断流程**:

1. **第一步：检查插件基本配置**
   ```
   Settings → Community plugins → Shell Commands
   ✅ 插件已启用
   ✅ 至少有一个命令配置
   ✅ 命令已启用（开关打开）
   ```

2. **第二步：检查事件触发配置**
   ```
   Shell Commands → Events tab
   ✅ "File content modified" 已勾选
   ✅ "Supported file extensions" 包含 canvas
   ```

3. **第三步：查看执行历史**
   ```
   Shell Commands → Output tab → Recent executions
   - 如果为空：事件未触发，检查文件类型和路径
   - 如果有记录：查看返回码和错误信息
   ```

4. **第四步：手动测试命令**
   ```powershell
   # Windows PowerShell测试
   $env:PYTHONIOENCODING="utf-8"
   $env:PYTHONLEGACYWINDOWSSTDIO="utf-8"
   $env:PRODUCT_DEVELOP_DIR="F:\101. link notebook\Obsidian Vault\KA\Product development"
   
   # 测试脚本是否存在
   Test-Path "$env:PRODUCT_DEVELOP_DIR\scripts\canvas\on_canvas_save.ps1"
   
   # 手动执行命令
   pwsh -NoProfile -ExecutionPolicy Bypass -Command "& { & `"$env:PRODUCT_DEVELOP_DIR\scripts\canvas\on_canvas_save.ps1`" `"$env:PRODUCT_DEVELOP_DIR\example\test_single_layer\product.canvas`" }"
   ```

5. **第五步：检查权限和路径**
   ```powershell
   # 检查执行策略
   Get-ExecutionPolicy
   
   # 如果受限，设置执行策略
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   
   # 检查路径中的特殊字符
   Write-Host $env:PRODUCT_DEVELOP_DIR
   ```

**📊 常见问题对照表**:

| 症状 | 可能原因 | 解决方案 |
|------|----------|----------|
| 插件输出为空 | 事件未触发 | 检查文件扩展名配置 |
| 权限被拒绝 | 执行策略限制 | 设置PowerShell执行策略 |
| 找不到文件 | 路径错误 | 验证PRODUCT_DEVELOP_DIR |
| 编码错误 | Unicode问题 | 添加UTF-8环境变量 |
| 无输出但执行 | 脚本内部错误 | 查看日志文件 |

#### 问题2：权限错误

**症状**: 脚本执行时显示权限拒绝

**解决方案**:
```bash
# Linux/macOS
chmod +x scripts/canvas/on_canvas_save.sh
chmod +x scripts/canvas/start_canvas_watcher.sh

# Windows（PowerShell）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 问题3：Python环境问题

**症状**: 找不到Python或导入模块失败

**解决方案**:
```bash
# 检查Python版本
python --version

# 检查模块安装
python -c "import watchdog; print('watchdog OK')"
python -c "import sys; print('\n'.join(sys.path))"

# 重新安装依赖
pip install -r requirements.txt
```

#### 问题4：频繁触发同步

**症状**: 短时间内多次同步，影响性能

**解决方案**:
```yaml
# 在canvas_watcher.yml中调整配置
watcher:
  watch_delay: 2.0        # 增加等待时间
  batch_timeout: 60       # 增加批处理超时
  sync_interval: 10       # 增加最小同步间隔
```

#### 问题5：Canvas文件解析错误

**症状**: JSON解析失败或edges提取错误

**解决方案**:
```bash
# 验证Canvas文件格式
python -c "import json; json.load(open('product.canvas'))"

# 检查文件编码
file product.canvas

# 备份并修复Canvas文件
cp product.canvas product.canvas.backup
# 在Obsidian中重新保存Canvas文件
```

### 调试技巧

#### 启用详细日志

```bash
# 方法一：环境变量
export DEBUG_MODE=1
bash scripts/canvas/on_canvas_save.sh product.canvas

# 方法二：配置文件
# 在canvas_watcher.yml中设置
watcher:
  log_level: DEBUG
```

#### 手动测试同步

```bash
# 测试edges检测
python scripts/canvas/canvas_manager.py --extract-edges product.canvas

# 测试完整同步
python scripts/canvas/auto_link_documents.py --sync-from-canvas product.canvas

# 验证INDEX更新
grep -r "canvas_auto" *.md
```

#### 性能分析

```bash
# 监控资源使用
top | grep python
ps aux | grep canvas_watcher

# 检查日志文件大小
du -h logs/

# 清理日志（如果需要）
find logs/ -name "*.log" -mtime +7 -delete
```

## ⚡ 性能优化

### 监控器优化

```yaml
watcher:
  # 减少磁盘I/O
  stats_save_interval: 600   # 10分钟保存一次统计
  
  # 批处理优化
  batch_timeout: 45          # 45秒批处理窗口
  immediate_threshold: 1     # 单个变更立即处理
  
  # 内存管理
  memory_limit_mb: 256       # 限制内存使用
  enable_compression: true   # 启用日志压缩
```

### 系统级优化

```bash
# 调整文件监控限制（Linux）
echo 'fs.inotify.max_user_watches=524288' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 优化Python性能
export PYTHONOPTIMIZE=1
export PYTHONDONTWRITEBYTECODE=1
```

## 🔄 备份和恢复

### 自动备份配置

```yaml
watcher:
  auto_backup: true
  backup_interval_hours: 24
  backup_retention_days: 30
  backup_location: "backups/canvas"
```

### 手动备份

```bash
# 备份Canvas文件
cp product.canvas "backups/product.canvas.$(date +%Y%m%d_%H%M%S)"

# 备份INDEX文件
find . -name "*_INDEX.md" -exec cp {} backups/ \;

# 备份配置和日志
tar -czf "backups/canvas_system_$(date +%Y%m%d).tar.gz" \
    config/ logs/ scripts/canvas/
```

### 恢复操作

```bash
# 恢复Canvas文件
cp backups/product.canvas.20241219_143000 product.canvas

# 重新同步所有关联
python scripts/canvas/auto_link_documents.py --sync-to-canvas --mode full
python scripts/canvas/auto_link_documents.py --sync-from-canvas
```

## 🚀 高级功能

### 自定义钩子脚本

```yaml
watcher:
  advanced:
    hooks:
      before_sync: "scripts/hooks/before_canvas_sync.sh"
      after_sync: "scripts/hooks/after_canvas_sync.sh"
      on_error: "scripts/hooks/canvas_sync_error.sh"
```

### 通知集成

```yaml
watcher:
  notifications:
    enable_webhook: true
    webhook_url: "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"
    webhook_on_error: true
```

### 多项目支持

```bash
# 为每个项目创建独立配置
cp config/canvas_watcher.yml config/canvas_watcher_project1.yml
cp config/canvas_watcher.yml config/canvas_watcher_project2.yml

# 启动多个监控实例
bash scripts/canvas/start_canvas_watcher.sh start --config config/canvas_watcher_project1.yml --daemon
bash scripts/canvas/start_canvas_watcher.sh start --config config/canvas_watcher_project2.yml --daemon
```

## 📋 最佳实践

### 1. 日常使用建议

- **个人开发**: 使用Obsidian Plugin方法，简单易用
- **团队协作**: 使用Watchdog监控，可作为共享服务
- **生产环境**: 配置系统服务，确保高可用性

### 2. 同步策略

- **开发阶段**: 启用实时同步，快速反馈
- **测试阶段**: 使用批处理模式，减少干扰
- **发布前**: 运行完整验证，确保一致性

### 3. 监控告警

```bash
# 设置监控脚本
# /etc/cron.d/canvas-monitor
*/5 * * * * /path/to/check_canvas_watcher.sh

# check_canvas_watcher.sh
#!/bin/bash
if ! pgrep -f canvas_watcher.py > /dev/null; then
    echo "Canvas watcher is down!" | mail -s "Alert" <EMAIL>
    # 自动重启
    bash /path/to/start_canvas_watcher.sh start --daemon
fi
```

### 4. 团队协作规范

- **统一配置**: 使用版本控制管理配置文件
- **变更通知**: 重要Canvas修改需通知团队
- **冲突解决**: 建立Canvas编辑的协作流程
- **定期备份**: 每周备份重要Canvas文件

## 📞 支持和社区

### 获取帮助

1. **查看日志**: 首先检查详细日志信息
2. **搜索文档**: 在项目文档中搜索相关问题
3. **社区讨论**: 在项目GitHub上创建Issue
4. **联系维护者**: 通过项目联系方式寻求帮助

### 贡献改进

- **报告Bug**: 详细描述问题和复现步骤
- **功能建议**: 提出有价值的功能改进建议
- **代码贡献**: 提交Pull Request改进系统
- **文档完善**: 帮助改进和翻译文档

---

## 🎉 结语

Canvas自动同步系统为您的项目文档管理提供了强大而灵活的解决方案。通过正确配置和使用，您可以实现Canvas可视化编辑与INDEX文档管理的无缝集成，大大提高工作效率。

如果您在使用过程中遇到任何问题，请参考本指南的故障排除章节，或通过项目支持渠道寻求帮助。

**祝您使用愉快！** 🚀 