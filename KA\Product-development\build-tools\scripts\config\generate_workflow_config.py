#!/usr/bin/env python3
"""
工作流配置生成器

按照单一原则，专门负责生成工作流系统的配置文件。
"""

import os
import json
import argparse
from pathlib import Path
from datetime import datetime


def generate_workflow_config(project_path, project_type="single_layer", scripts_base="", non_interactive=False):
    """
    生成工作流配置文件
    
    Args:
        project_path: 项目根目录路径
        project_type: 项目类型(single_layer/multi_level)
        scripts_base: 脚本基础路径
    
    Returns:
        bool: 是否成功生成
    """
    try:
        config_dir = Path(project_path) / 'config'
        config_dir.mkdir(exist_ok=True)
        
        config_file = config_dir / 'workflow_config.json'
        
        if config_file.exists():
            if non_interactive:
                print(f"[!] 工作流配置文件已存在，非交互模式下直接覆盖: {config_file}")
            else:
                print(f"[!] 工作流配置文件已存在: {config_file}")
                response = input("是否覆盖现有配置文件？(y/N): ")
                if response.lower() != 'y':
                    print("操作已取消")
                    return True
        
        workflow_config = {
            "workflow": {
                "name": "产品开发工作流",
                "description": "完整的产品开发流程，支持事件驱动和自动化执行",
                "version": "1.0.0",
                "project_type": project_type,
                "created_date": datetime.now().isoformat(),
                "components": [
                    {
                        "name": "需求导入",
                        "type": "input",
                        "script": f"{scripts_base}/requirements/import_requirements.py",
                        "config": "requirements_import_config.json",
                        "description": "从外部系统导入需求文档"
                    },
                    {
                        "name": "需求分析",
                        "type": "process",
                        "script": f"{scripts_base}/requirements/analyze_requirements.py",
                        "config": "requirements_analysis_config.json",
                        "description": "分析和分解需求"
                    },
                    {
                        "name": "方案设计",
                        "type": "process",
                        "script": f"{scripts_base}/design/generate_design.py",
                        "config": "design_config.json",
                        "description": "生成系统架构和技术方案"
                    },
                    {
                        "name": "开发实施",
                        "type": "process",
                        "script": f"{scripts_base}/development/manage_development.py",
                        "config": "development_config.json",
                        "description": "硬件、固件、软件开发"
                    },
                    {
                        "name": "测试验证",
                        "type": "process",
                        "script": f"{scripts_base}/quality/run_verification.py",
                        "config": "quality_config.json",
                        "description": "质量测试和验证"
                    },
                    {
                        "name": "生产准备",
                        "type": "process",
                        "script": f"{scripts_base}/production/prepare_production.py",
                        "config": "production_config.json",
                        "description": "生产BOM和流程准备"
                    },
                    {
                        "name": "项目输出",
                        "type": "output",
                        "script": f"{scripts_base}/deliverables/generate_deliverables.py",
                        "config": "deliverables_config.json",
                        "description": "生成最终交付物"
                    }
                ],
                "connections": [
                    {
                        "from": "需求导入",
                        "to": "需求分析",
                        "triggers": [
                            {
                                "name": "需求文档导入完成",
                                "type": "event",
                                "handlers": [
                                    {
                                        "type": "mcp_server",
                                        "name": "Context7 MCP",
                                        "script": f"{scripts_base}/mcp-server_local_integrations/analyze_requirements.py",
                                        "params": {
                                            "input": "${event.output_path}"
                                        }
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "from": "需求分析",
                        "to": "方案设计",
                        "triggers": [
                            {
                                "name": "需求矩阵更新",
                                "type": "event",
                                "handlers": [
                                    {
                                        "type": "mcp_server",
                                        "name": "UML-MCP Server",
                                        "script": f"{scripts_base}/mcp-server_local_integrations/generate_architecture_diagrams.py",
                                        "params": {
                                            "requirements_matrix": "${event.file_path}"
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ],
                "mcp_servers": [
                    {
                        "name": "Firecrawl MCP Server",
                        "script": f"{scripts_base}/mcp-server_local_integrations/firecrawl.py",
                        "purpose": "网页需求抓取",
                        "enabled": True
                    },
                    {
                        "name": "IM Notifier MCP Server",
                        "script": f"{scripts_base}/mcp-server_local_integrations/im_notifier.py",
                        "purpose": "通知服务",
                        "enabled": True
                    },
                    {
                        "name": "UML-MCP Server",
                        "script": f"{scripts_base}/mcp-server_local_integrations/uml_mcp.py",
                        "purpose": "UML图生成",
                        "enabled": True
                    }
                ]
            }
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(workflow_config, f, ensure_ascii=False, indent=2)
        
        print(f"[+] 工作流配置文件已生成: {config_file}")
        print("  - 事件驱动工作流配置")
        print("  - MCP服务器集成配置")
        print("  - 组件间连接和触发器配置")
        
        return True
        
    except Exception as e:
        print(f"[X] 工作流配置文件生成失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='工作流配置生成器')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'],
                       help='项目类型')
    parser.add_argument('--scripts-base', default='', help='脚本基础路径')
    parser.add_argument('--non-interactive', action='store_true', help='非交互模式，自动覆盖现有文件')

    args = parser.parse_args()
    
    project_path = Path(args.project_path).resolve()
    
    if not project_path.exists():
        print(f"[X] 项目路径不存在: {project_path}")
        return 1
    
    success = generate_workflow_config(project_path, args.project_type, args.scripts_base, args.non_interactive)
    
    if success:
        print("\n[+] 工作流配置生成完成")
        return 0
    else:
        print("\n[X] 工作流配置生成失败")
        return 1


if __name__ == "__main__":
    exit(main()) 