#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文档分析器 - 用于16. line lidar项目的文档状态分析
"""

import os
import re
import json
import yaml
from pathlib import Path
import argparse

def analyze_single_document(doc_path):
    """分析单个文档的质量和完成度"""
    try:
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"无法读取文件 {doc_path}: {e}")
        return {
            "path": doc_path,
            "title": os.path.basename(doc_path),
            "filename": os.path.basename(doc_path),
            "word_count": 0,
            "heading_count": 0,
            "structure_elements": 0,
            "completion": 0,
            "status": "error",
            "error": str(e)
        }
    
    # 基本信息
    filename = os.path.basename(doc_path)
    title_match = re.search(r'^# (.+)$', content, re.MULTILINE)
    title = title_match.group(1) if title_match else Path(doc_path).stem
    
    # 提取YAML元数据
    yaml_match = re.match(r'^---\n(.*?)\n---\n', content, re.DOTALL)
    metadata = {}
    if yaml_match:
        yaml_content = yaml_match.group(1)
        try:
            metadata = yaml.safe_load(yaml_content)
        except:
            pass
            
    # 评估指标
    word_count = len(content.split())
    heading_count = len(re.findall(r'^#{1,6} ', content, re.MULTILINE))
    list_count = len(re.findall(r'^[*-] ', content, re.MULTILINE))
    table_count = len(re.findall(r'\|.*\|.*\|', content))
    code_blocks = len(re.findall(r'```.*?```', content, re.DOTALL))
    
    # 计算简单完成度指标（可扩展为更复杂的算法）
    min_expected_words = 300  # 一个合格文档的最小字数
    max_expected_words = 3000  # 较完善文档的参考字数
    
    # 基于字数的完成度（在最小和最大期望值之间映射到0-1）
    if word_count <= min_expected_words:
        content_score = word_count / min_expected_words * 0.5  # 低于最小期望仅计部分分
    elif word_count >= max_expected_words:
        content_score = 1.0
    else:
        # 在最小到最大期望之间线性映射到0.5-1.0
        content_score = 0.5 + 0.5 * (word_count - min_expected_words) / (max_expected_words - min_expected_words)
    
    # 结构得分 - 基于标题、列表、表格等结构元素
    structure_elements = heading_count + list_count + table_count + code_blocks
    structure_score = min(1.0, structure_elements / 10)  # 最多10个结构元素满分
    
    # 如果元数据中有状态和进度，优先使用这些值
    if metadata.get("status") and metadata.get("progress"):
        status = metadata["status"]
        progress_str = metadata["progress"]
        if isinstance(progress_str, str) and "%" in progress_str:
            completion = float(progress_str.strip("%")) / 100
        else:
            completion = float(progress_str) if isinstance(progress_str, (int, float)) else 0
    else:
        # 综合评分
        completion = content_score * 0.7 + structure_score * 0.3
        if completion > 0.8:
            status = "completed"
        elif completion > 0.3:
            status = "in_progress"
        else:
            status = "initial"
    
    return {
        "path": doc_path,
        "title": title,
        "filename": filename,
        "word_count": word_count,
        "heading_count": heading_count,
        "structure_elements": structure_elements,
        "completion": completion,
        "status": status,
        "metadata": metadata
    }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析Markdown文档状态")
    parser.add_argument("--file", help="要分析的文档路径")
    parser.add_argument("--output", help="输出结果的JSON路径")
    args = parser.parse_args()
    
    if not args.file:
        print("错误: 请指定要分析的文档路径")
        return 1
        
    result = analyze_single_document(args.file)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    return 0

if __name__ == "__main__":
    exit(main()) 