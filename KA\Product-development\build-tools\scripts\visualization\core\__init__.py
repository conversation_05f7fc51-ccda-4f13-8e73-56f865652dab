"""
可视化系统核心模块

提供统一的接口、数据适配器、布局引擎和组件工具
"""

from .interfaces import VisualizationData, VisualizationRenderer, DataAdapter
from .data_adapters import MultiModeDataAdapter
from .layout_engines import ComponentLayoutEngine
from .component_utils import ComponentManager

__all__ = [
    'VisualizationData', 'VisualizationRenderer', 'DataAdapter',
    'MultiModeDataAdapter', 'ComponentLayoutEngine', 'ComponentManager'
] 