#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""调试配置脚本"""

from config import get_component_list, get_component_info, get_canvas_layout_config
from canvas_utils import CanvasLayoutUtils

def main():
    print("=== 组件排序结果 ===")
    components = get_component_list() 
    for comp in components:
        info = get_component_info(comp)
        print(f"{comp}: order={info.order}, name={info.name}")
    
    print("\n=== Canvas布局配置 ===")
    layout_config = get_canvas_layout_config()
    for comp, config in layout_config.items():
        print(f"{comp}: x_start={config['x_start']}, x_end={config['x_end']}, color={config['color']}")
    
    print("\n=== 位置计算测试 ===")
    layout_utils = CanvasLayoutUtils()
    for comp in ["PROD_INFO", "REQ", "DES"]:
        for index in range(3):
            pos = layout_utils.calculate_node_position(comp, index)
            print(f"{comp}[{index}]: x={pos['x']}, y={pos['y']}")

if __name__ == "__main__":
    main() 