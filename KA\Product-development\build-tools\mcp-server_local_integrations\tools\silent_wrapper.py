#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Silent wrapper for MCP tools to prevent stdout interference
"""

import sys
import io
import logging
from contextlib import contextmanager

class NullHandler:
    """Null handler that discards all output"""
    def write(self, text):
        pass
    def flush(self):
        pass
    def close(self):
        pass

@contextmanager
def silence_all_output():
    """Context manager to completely silence all output"""
    # Save original streams
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    # Create null handlers
    null_handler = NullHandler()
    
    try:
        # Redirect all output to null
        sys.stdout = null_handler
        sys.stderr = null_handler
        
        # Disable all logging
        logging.disable(logging.CRITICAL)
        
        yield
        
    finally:
        # Restore original streams
        sys.stdout = original_stdout
        sys.stderr = original_stderr
        
        # Re-enable logging
        logging.disable(logging.NOTSET)

def silent_import(module_name):
    """Import a module silently"""
    with silence_all_output():
        return __import__(module_name)

def silent_call(func, *args, **kwargs):
    """Call a function silently"""
    with silence_all_output():
        return func(*args, **kwargs)

async def silent_async_call(func, *args, **kwargs):
    """Call an async function silently"""
    with silence_all_output():
        return await func(*args, **kwargs)
