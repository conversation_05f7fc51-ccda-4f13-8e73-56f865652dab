#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Matplotlib渲染器

将可视化数据渲染为静态图像（PNG、SVG等）
重构自原有generate_relation_graph.py的功能
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.font_manager import FontProperties
import networkx as nx
from pathlib import Path
import numpy as np
import os

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import VisualizationRenderer, VisualizationData
from core.component_utils import component_manager

class MatplotlibRenderer(VisualizationRenderer):
    """Matplotlib渲染器 - 单一职责：生成静态图像"""
    
    def __init__(self):
        # 设置中文字体
        self.font = self._get_chinese_font()
        
    def render(self, data: VisualizationData, **kwargs) -> str:
        """渲染可视化数据为图像"""
        output_path = kwargs.get('output_path', None)
        format_type = kwargs.get('format', 'png')
        figsize = kwargs.get('figsize', (15, 10))
        dpi = kwargs.get('dpi', 300)
        
        if data.mode.value == 'all':
            return self._render_combined_view(data, output_path, format_type, figsize, dpi)
        else:
            return self._render_single_mode(data, output_path, format_type, figsize, dpi)
    
    def get_supported_formats(self):
        """获取支持的渲染格式"""
        return ["png", "svg", "pdf", "jpg", "eps"]
    
    def _render_single_mode(self, data: VisualizationData, output_path, format_type, figsize, dpi):
        """渲染单一模式"""
        # 创建NetworkX图
        G = self._create_networkx_graph(data)
        
        if not G.nodes():
            print("警告: 没有找到任何节点，无法生成图")
            return None
        
        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)
        
        # 应用组件区域布局
        pos = self._apply_component_layout(G, data.nodes)
        
        # 绘制组件区域背景
        self._draw_component_areas(ax, data.nodes, figsize)
        
        # 绘制节点
        self._draw_nodes(G, pos, ax, data.nodes)
        
        # 绘制边
        self._draw_edges(G, pos, ax, data.edges)
        
        # 设置标题和样式
        ax.set_title(data.title, fontproperties=self.font, fontsize=16, fontweight='bold')
        ax.axis('off')
        
        # 添加图例
        self._add_legend(ax, data.nodes, data.edges)
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight', format=format_type)
            print(f"图像已保存到 {output_path}")
            plt.close()
            return output_path
        else:
            plt.show()
            return None
    
    def _render_combined_view(self, data: VisualizationData, output_path, format_type, figsize, dpi):
        """渲染综合视图 - 支持子图布局"""
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
        axes = [ax1, ax2, ax3, ax4]
        
        # 提取各个模块的数据
        modules = data.metadata.get('modules', {})
        module_names = ['workflow', 'documents', 'traceability', 'progress']
        
        for idx, module_name in enumerate(module_names):
            if idx < len(axes) and module_name in modules:
                # 从综合数据中提取该模块的节点和边
                module_nodes = [node for node in data.nodes 
                              if node.properties.get('module') == module_name]
                module_edges = [edge for edge in data.edges 
                              if edge.properties.get('module') == module_name]
                
                # 为每个子图创建单独的VisualizationData
                module_data = VisualizationData(
                    title=modules[module_name].get('title', module_name),
                    mode=data.mode,
                    nodes=module_nodes,
                    edges=module_edges,
                    metadata=modules[module_name]
                )
                
                self._render_subplot(module_data, axes[idx])
        
        plt.suptitle("产品体系综合视图", fontproperties=self.font, fontsize=20, fontweight='bold')
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight', format=format_type)
            print(f"综合视图已保存到 {output_path}")
            plt.close()
            return output_path
        else:
            plt.show()
            return None
    
    def _render_subplot(self, data: VisualizationData, ax):
        """渲染子图"""
        G = self._create_networkx_graph(data)
        
        if not G.nodes():
            ax.text(0.5, 0.5, '无数据', transform=ax.transAxes, 
                   ha='center', va='center', fontproperties=self.font)
            ax.set_title(data.title, fontproperties=self.font)
            ax.axis('off')
            return
        
        # 计算布局
        pos = nx.spring_layout(G, seed=42, k=0.3)
        
        # 绘制节点
        for node in data.nodes:
            component = node.component
            color = component_manager.get_component_color(component)
            nx.draw_networkx_nodes(G, pos, nodelist=[node.id], 
                                 node_color=color, node_size=800, ax=ax)
        
        # 绘制边
        for edge in data.edges:
            edge_color = component_manager.get_relation_color(edge.type)
            nx.draw_networkx_edges(G, pos, edgelist=[(edge.source, edge.target)],
                                 edge_color=edge_color, arrows=True, ax=ax)
        
        # 绘制标签
        labels = {node.id: node.id for node in data.nodes}
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=8,
                               font_family='SimSun' if self.font else 'sans-serif', ax=ax)
        
        ax.set_title(data.title, fontproperties=self.font, fontsize=12)
        ax.axis('off')
    
    def _create_networkx_graph(self, data: VisualizationData):
        """创建NetworkX图对象"""
        G = nx.DiGraph()
        
        # 添加节点
        for node in data.nodes:
            G.add_node(node.id, 
                      name=node.name, 
                      component=node.component,
                      type=node.type,
                      **node.properties)
        
        # 添加边
        for edge in data.edges:
            G.add_edge(edge.source, edge.target, 
                      type=edge.type,
                      **edge.properties)
        
        return G
    
    def _apply_component_layout(self, G, nodes):
        """应用组件区域布局"""
        # 获取所有组件类型
        components_in_use = list(set(node.component for node in nodes))
        
        # 计算组件区域
        total_width = 10.0
        total_height = 8.0
        areas = component_manager.calculate_component_areas(
            components_in_use, total_width, total_height
        )
        
        # 为每个节点分配位置
        pos = {}
        component_counts = {comp: 0 for comp in components_in_use}
        
        for node in nodes:
            component = node.component
            if component in areas:
                area = areas[component]
                
                # 在组件区域内分布节点
                count = component_counts[component]
                rows = int(np.sqrt(len([n for n in nodes if n.component == component]))) + 1
                
                x_offset = (count % rows) * (area["width"] / rows)
                y_offset = (count // rows) * (area["height"] / rows)
                
                pos[node.id] = (
                    area["x"] + area["width"] * 0.1 + x_offset,
                    area["y"] + area["height"] * 0.1 + y_offset
                )
                
                component_counts[component] += 1
            else:
                # 默认位置
                pos[node.id] = (total_width / 2, total_height / 2)
        
        return pos
    
    def _draw_component_areas(self, ax, nodes, figsize):
        """绘制组件区域背景"""
        components_in_use = list(set(node.component for node in nodes))
        areas = component_manager.calculate_component_areas(
            components_in_use, figsize[0], figsize[1]
        )
        
        for component, area in areas.items():
            # 创建区域矩形
            rect = patches.Rectangle(
                (area["x"], area["y"]), area["width"], area["height"],
                linewidth=2, edgecolor=area["color"], facecolor=area["color"],
                alpha=0.1, linestyle='--'
            )
            ax.add_patch(rect)
            
            # 添加区域标签
            ax.text(area["center_x"], area["y"] + area["height"] * 0.05,
                   f'{component} 组件区域',
                   ha='center', va='bottom', fontproperties=self.font,
                   fontsize=12, fontweight='bold', color=area["color"])
    
    def _draw_nodes(self, G, pos, ax, nodes):
        """绘制节点"""
        # 按组件类型分组绘制
        for component in set(node.component for node in nodes):
            node_list = [node.id for node in nodes if node.component == component]
            if node_list:
                color = component_manager.get_component_color(component)
                nx.draw_networkx_nodes(G, pos, nodelist=node_list,
                                     node_color=color, node_size=1000,
                                     alpha=0.8, ax=ax)
        
        # 绘制节点标签
        labels = {node.id: f"{node.id}\n{node.name[:10]}..." if len(node.name) > 10 
                 else f"{node.id}\n{node.name}" for node in nodes}
        nx.draw_networkx_labels(G, pos, labels=labels, font_size=8,
                               font_family='SimSun' if self.font else 'sans-serif', ax=ax)
    
    def _draw_edges(self, G, pos, ax, edges):
        """绘制边"""
        # 按关系类型分组绘制
        for edge_type in set(edge.type for edge in edges):
            edge_list = [(edge.source, edge.target) for edge in edges if edge.type == edge_type]
            if edge_list:
                color = component_manager.get_relation_color(edge_type)
                nx.draw_networkx_edges(G, pos, edgelist=edge_list,
                                     edge_color=color, arrows=True,
                                     arrowstyle='->', arrowsize=20,
                                     width=2, alpha=0.7, ax=ax)
    
    def _add_legend(self, ax, nodes, edges):
        """添加图例"""
        # 组件图例
        component_legend = []
        for component in set(node.component for node in nodes):
            color = component_manager.get_component_color(component)
            component_info = component_manager.get_component_info(component)
            name = component_info.name if component_info else component
            component_legend.append(patches.Patch(color=color, label=f'{component} - {name}'))
        
        # 关系类型图例
        relation_legend = []
        for edge_type in set(edge.type for edge in edges):
            color = component_manager.get_relation_color(edge_type)
            relation_legend.append(patches.Patch(color=color, label=edge_type))
        
        # 添加图例
        if component_legend:
            legend1 = ax.legend(handles=component_legend, loc='upper left', 
                              title='组件类型', prop=self.font, title_fontproperties=self.font)
            ax.add_artist(legend1)
        
        if relation_legend:
            ax.legend(handles=relation_legend, loc='upper right',
                     title='关系类型', prop=self.font, title_fontproperties=self.font)
    
    def _get_chinese_font(self):
        """获取中文字体"""
        font_paths = [
            r"c:\windows\fonts\simsun.ttc",  # Windows
            r"c:\windows\fonts\simhei.ttf",  # Windows
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    return FontProperties(fname=font_path)
                except:
                    continue
        
        # 如果没有找到字体，返回None
        return None 