#include "clensMachineQH_SJ.h"

#include <QApplication>
#include <QTimer>
#include <qmath.h>


#include "loadXml.h"
#include "modbusRtu.h"
#include "typeConvert.h"

#include "qLog.h"


CClensMachineQH_SHJ::CClensMachineQH_SHJ(IComm *port_, const uint8_t &device)
    : m_device_addr(device), im_port_(port_), mi_load_(new CLoadXml), m_cmd_read_addr(Hmi_ack_start) {
    //* 此处应该可以选择数据接收方式
    im_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

    //* 协议
    m_protocol_ = new CMyModBus(device);  //选择协议

    //* varible init
    cmd_init();

    m_xml_param["xy_radius_limit"] = 900;
    m_xml_param["z_radius_limit"]  = 1000;
    m_xml_param["x_step_dist"]     = 10;  // min move distance / um
    m_xml_param["y_step_dist"]     = 10;  // min move distance / um
    m_xml_param["z_step_dist"]     = 10;  // min move distance / um

    QString filename = QApplication::applicationDirPath() + "/config/clen_config_QH_SHJ.xml";
    mi_load_->readParam(filename, &m_xml_param);

    LOG_INFO(MyLogger::LogType::INIT, QString("xy radius limit %1").arg(m_xml_param["xy_radius_limit"]));

    m_step_dis.x = m_xml_param["x_step_dist"];  // um
    m_step_dis.y = m_xml_param["y_step_dist"];  // um
    m_step_dis.z = m_xml_param["z_step_dist"];  // um

    //* 数据类型注册
    qRegisterMetaType<QVector<float>>("QVector<float>");

    LOG_INFO(MyLogger::LogType::INIT, QString("cmd/ start: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["start"])));
}


CClensMachineQH_SHJ::~CClensMachineQH_SHJ() {
    if (m_protocol_ != nullptr)
        delete m_protocol_;

    delete mi_load_;
}

void CClensMachineQH_SHJ::sleepMs(uint16_t msec) {
    QTime dieTime = QTime::currentTime().addMSecs(msec);
    while (QTime::currentTime() < dieTime)
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
}

/**
 * @brief 固定指令直接初始化，有变化指令用成员变量，只初始化固定部分
 *
 */
void CClensMachineQH_SHJ::cmd_init(void) {
    IDevicePtl::StInput *input_data_ = new IDevicePtl::StInput;
    input_data_->device_addr         = 0;
    input_data_->addr                = 0;
    input_data_->reg_num             = 0;
    input_data_->bytes_num           = 0;
    input_data_->dword_data.clear();
    input_data_->crc16 = 0;

    //*******************固定指令初始化*******************
    //* 开始抓取->写状态位
    input_data_->addr = ECmdDataAddr::Hmi_ack_flag;
    input_data_->word_data.clear();
    input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eMACHINE_START));  //
    m_cmd["start"] = m_protocol_->getControlCmd(*input_data_);

    // 机台开始调节
    input_data_->addr = ECmdDataAddr::Hmi_ack_start;
    input_data_->word_data.clear();
    input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eLEN_CAP));  //
    m_cmd["autoAdjust"] = m_protocol_->getControlCmd(*input_data_);


    //* 退出->写状态位 01 06 02 A8 80 00 68 52
    input_data_->addr = ECmdDataAddr::Hmi_ack_flag;
    input_data_->word_data.clear();
    input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eDEVICE_EXIT));  //
    m_cmd["stop"] = m_protocol_->getControlCmd(*input_data_);

    //* 读状态 01 03 02 A8 00 01 04 52
    input_data_->addr    = ECmdDataAddr::Hmi_ack_flag;
    input_data_->reg_num = 0x01;
    m_cmd["status"]      = m_protocol_->getReadCmd(*input_data_);

    //* 机台粗调状态
    input_data_->addr         = ECmdDataAddr::Hmi_visiual_status;
    input_data_->reg_num      = 0x01;
    m_cmd["autoAdjustStatus"] = m_protocol_->getReadCmd(*input_data_);

    //* read location
    input_data_->addr    = EWordRegAddr::x_coordinates;
    input_data_->reg_num = 0x06;
    m_cmd["loc"]         = m_protocol_->getReadCmd(*input_data_);

    //* 移动坐标轴标识 01 06 02 A8 00 07 48 50(三轴)
    input_data_->addr = ECmdDataAddr::Hmi_ack_flag;
    input_data_->word_data.clear();
    input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eTHREE_AXIS));  //
    m_cmd["axis"] = m_protocol_->getControlCmd(*input_data_);

    //* 机台粗调状态
    input_data_->addr    = ECmdDataAddr::Hmi_move_status;
    input_data_->reg_num = 0x01;
    m_cmd["moveStatus"]  = m_protocol_->getReadCmd(*input_data_);

    //* 固化
    input_data_->addr = ECmdDataAddr::Hmi_ack_flag;
    input_data_->word_data.clear();
    input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eSOLID));  //
    m_cmd["solid"] = m_protocol_->getControlCmd(*input_data_);

    //* 放气
    input_data_->addr = ECmdDataAddr::Hmi_ack_flag;
    input_data_->word_data.clear();
    input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eDEFLATE));  //
    m_cmd["deflate"] = m_protocol_->getControlCmd(*input_data_);

    LOG_INFO(MyLogger::LogType::INIT, QString("cmd/ start: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["start"])));
    //******************变化指令初始化********************
}

QByteArray CClensMachineQH_SHJ::portDataRead() {
    return im_port_->read(5);
}


void CClensMachineQH_SHJ::icom_change_interface(IComm *port_) {
    im_port_ = port_;
    im_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析
}

/**
 * @brief start
 * @return
 */
bool CClensMachineQH_SHJ::start(const QVector<uint16_t> &data) {
    Q_UNUSED(data);
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("start cmd: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["start"])));
#endif
    m_strPre.clear();
    return im_port_->write(m_cmd["start"]);
}

/**
 * @brief CClensMachineQH_SHJ::getStatus
 * @param addr
 * @return
 */
bool CClensMachineQH_SHJ::getStatus(void) {
    m_strPre.clear();
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("get status: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["status"])));
#endif
    m_cmd_read_addr = Hmi_ack_flag;
    return im_port_->write(m_cmd["status"]);
}

/**
 * @brief 退出
 * @param data
 * @return
 */
bool CClensMachineQH_SHJ::stop(const QByteArray &data) {
    Q_UNUSED(data);
    m_strPre.clear();
    return im_port_->write(m_cmd["stop"]);
}

/**
 * @brief getLoc
 * @param dimension
 * @return
 */
bool CClensMachineQH_SHJ::getLoc(const uint8_t &dimension) {
    Q_UNUSED(dimension);
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("loc cmd: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["loc"])));
#endif
    m_strPre.clear();
    return im_port_->write(m_cmd["loc"]);
}

bool CClensMachineQH_SHJ::autoAdjust(const QVector<uint16_t> &data) {
    Q_UNUSED(data);
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("start cmd: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["start"])));
#endif
    m_strPre.clear();
    return im_port_->write(m_cmd["autoAdjust"]);
}

/**
 * @brief CClensMachineQH_SHJ::getAutoAdjustStatus
 * @param addr
 * @return
 */
bool CClensMachineQH_SHJ::getAutoAdjustStatus(void) {
    m_strPre.clear();
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("auto adjust status: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["autoAdjustStatus"])));
#endif
    m_cmd_read_addr = Hmi_visiual_status;

    return im_port_->write(m_cmd["autoAdjustStatus"]);
}

/**
 * @brief 移动控制 01 10 02 AA 00 05 0A 03 E8 00 00 03 E8 00 00 03 E8 17 C2（三轴移动1000）
 * @return
 */
bool CClensMachineQH_SHJ::adjust(const St3D<int16_t> &move_dis) {
    IDevicePtl::StInput *move_input_data_ = new IDevicePtl::StInput;

    move_input_data_->addr      = static_cast<uint16_t>(ECmdDataAddr::Hmi_ack_flag);
    move_input_data_->reg_num   = 0x07;
    move_input_data_->bytes_num = move_input_data_->reg_num << 1;
    move_input_data_->dword_data.clear();
    move_input_data_->word_data.append(static_cast<uint16_t>(EStatusFlag::eTHREE_AXIS));
    move_input_data_->word_data.append(0);
    move_input_data_->word_data.append(move_dis.x);
    move_input_data_->word_data.append(0x0000);
    move_input_data_->word_data.append(move_dis.y);  //
    move_input_data_->word_data.append(0x0000);
    move_input_data_->word_data.append(move_dis.z);  // move_dis.z

    m_cmd["move"] = m_protocol_->getWriteWordCmd(*move_input_data_);

    m_strPre.clear();

#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("move cmd: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["move"])));
//    LOG_INFO(MyLogger::LogType::COMM, QString("move cmd: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["axis"])));
#endif
    //    sleepMs(5);

    //    send_flag &= im_port_->write(m_cmd["axis"]);

    return im_port_->write(m_cmd["move"]);
}

/**
 * @brief CClensMachineQH_SHJ::getStatus
 * @param addr
 * @return
 */
bool CClensMachineQH_SHJ::getMoveStatus(void) {
    m_strPre.clear();
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("move status: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["moveStatus"])));
#endif

    m_cmd_read_addr = Hmi_move_status;

    return im_port_->write(m_cmd["moveStatus"]);
}

bool CClensMachineQH_SHJ::solid(void) {
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("solid cmd: %1").arg(NsTypeConvert::byteArrayToString(m_cmd["solid"])));
#endif
    return im_port_->write(m_cmd["solid"]);
}

bool CClensMachineQH_SHJ::deflate(void) {
#ifdef COMM_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM, QString("deflate cmd: ").arg(NsTypeConvert::byteArrayToString(m_cmd["deflate"])));
#endif
    return im_port_->write(m_cmd["deflate"]);
}

/**
 * @brief dtof grey map data prasing
 * @param str
 * @param length
 * @return
 */
// bool CClensMachineQH_SHJ::dataParsing(QByteArray str, const int &length)
//{
//    Q_UNUSED(length);

//    QByteArray strSum = m_strPre + str;
//    if(strSum.length() < 6) {//固定部分长度6
//        m_strPre = strSum;
//        return false;
//    }
//    else if(strSum.length() > 2000) { //防止异常解析
//        m_strPre.clear();
//        return false;
//    }

//    QVector<uint16_t> reg_v;
//    int i = 0;
//    uint8_t num;
//    uint16_t crc_16_cal;

//#define COMM_ACK
//#ifdef COMM_ACK
//    qDebug() << "-i -shunT/ ack:" << NsTypeConvert::byteArrayToString(strSum);
//#endif
//    /*1.1 parse*/
//    for(i = 0; i < (strSum.length() - 6); ++i) {//留7个数(读寄存器）
//        if((uchar)strSum.at(i) == m_device_addr) {//帧头
//            QByteArray strTmp; //有效完整指令
//            strTmp.clear();

//            CMyModBus::StSingleWriteWord* st_write_word_ = nullptr;
//            uint16_t reg_addr = 0, u16_data = 0;

//            strTmp.push_back((uchar)strSum.at(i));
//            switch ((uchar)strSum.at(i + 1)) {    //
//            case CMyModBus::EFunctionCode::eREAD_RW_REG: //数据反馈-读flag数据
//                strTmp.push_back((uchar)strSum.at(i + 1));

//                num = (uchar)strSum.at(i + 2); //
//                strTmp.push_back(num);

//                crc_16_cal = 0;
//                if((strSum.length() - i) >= (num + 5)) {//数量符合

//                    reg_v.clear();
//                    for(uint8_t j = 0; j < (num>>1); ++j) {
//                        strTmp.push_back((uchar)strSum.at(3 + (j<<1)));
//                        strTmp.push_back((uchar)strSum.at(3 + (j<<1) + 1));
//                        uint16_t data = ((uchar)strSum.at(3 + (j<<1))<<8) | (uchar)strSum.at(3 + (j<<1) + 1); //
//                        reg_v.push_back(data);
//                    }

//                    crc_16_cal = CMyModBus::crc16(strTmp, num + 3);
//                    uint16_t crc_16_tmp = (uchar)strSum.at(i + num + 3)<<8 | (uchar)strSum.at(i + num + 4);
//                    if(crc_16_cal == crc_16_tmp) {
//                        if(num == 0x02) {//device status flag
//                            strTmp.clear();
//                            if(reg_v.at(0) == EStatusFlag::eLEN_UNCAPPED) //
//                                emit dataOutput(ECommAck::eLEN_STATUS_ACK, ECommStatus::eCOMM_OK, strTmp);
//                            else if(reg_v.at(0) == EStatusFlag::eLEN_CAPPED) //抓取状态获取
//                                emit dataOutput(ECommAck::eLEN_STATUS_ACK, ECommStatus::eCOMM_COMP, strTmp);

//                            else if(reg_v.at(0) == EStatusFlag::eUNSOLIDDED)
//                                emit dataOutput(ECommAck::eSOLID_STATUS_ACK, ECommStatus::eCOMM_OK, strTmp);
//                            else if(reg_v.at(0) == EStatusFlag::eSOLIDDED)
//                                emit dataOutput(ECommAck::eSOLID_STATUS_ACK, ECommStatus::eCOMM_COMP, strTmp);
//                            else
//                                ;
//                        }
//                        else if(num == 12) {//device location
//                            QVector<float> loc_tmp;
//                            uint8_t tmp_buff[4];
//                            for(uint8_t n = 0; n < 3; ++n) {
//                                tmp_buff[0] = reg_v.at(n<<1) & 0xff;
//                                tmp_buff[1] = (reg_v.at(n<<1)>>8) & 0xff;
//                                tmp_buff[2] = reg_v.at((n<<1)+1) & 0xff;
//                                tmp_buff[3] = (reg_v.at((n<<1)+1)>>8) & 0xff; //HSB
//                                loc_tmp.push_back(*(float*)(tmp_buff));
//                            }
//                            emit floatDataOutput(ECommAck::eLOC_ACK, ECommStatus::eCOMM_COMP, loc_tmp);
//                        }
//                        i += num + 5;
//                    }
//                    else {
//                        i += 2;
//                        qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-clen mod crc16 cal: " << crc_16_cal;
//                        strTmp.clear();
//                        if(reg_v.at(0) == EStatusFlag::eLEN_CAPPED) //抓取状态获取
//                            emit dataOutput(ECommAck::eLEN_STATUS_ACK, ECommStatus::eCOMM_ERROR, strTmp);
//                        else if(reg_v.at(0) == EStatusFlag::eSOLIDDED)
//                            emit dataOutput(ECommAck::eSOLID_STATUS_ACK, ECommStatus::eCOMM_ERROR, strTmp);
//                        else ;
//                    }
//                }
//                else {
//                    strSum.remove(0, i); //i-1
//                    m_strPre.clear();
//                    m_strPre.push_back(strSum); //存储剩余数据f
//                    return false;
//                }
//                break;
//            case CMyModBus::EFunctionCode::eWRITE_REG_CONT:
//                strSum.clear();
//                i = 0;
//                qDebug() << "-i -shunT/ adjust ack:";
//                //              emit dataOutput(ECommAck::eADJUST_LEN_ACK, ECommStatus::eCOMM_COMP, strTmp); //移动指令ack
//                break;
//            case CMyModBus::EFunctionCode::eWRITE_REG: //调速中的ack会被屏蔽
//                strSum.remove(0, i);
//                //              m_strPre.clear();
//                //              m_strPre.push_back(strSum); //存储剩余数据

//                st_write_word_ = reinterpret_cast<CMyModBus::StSingleWriteWord*>(strSum.data());
//                reg_addr = ((st_write_word_->addr & 0xff) << 8) | ((st_write_word_->addr >> 8) & 0xff);
//                u16_data = ((st_write_word_->data & 0xff) << 8) | ((st_write_word_->data >> 8) & 0xff);

//                if(reg_addr == Hmi_ack_start) {
//                    emit dataOutput(ECommAck::eLEN_CAP_ACK, ECommStatus::eCOMM_COMP, strTmp);
//                }
//                else {
//                    if(u16_data == EStatusFlag::eSOLID) //固化
//                        emit dataOutput(ECommAck::eSOLID_ACK, ECommStatus::eCOMM_COMP, strTmp);
//                    else if(u16_data == EStatusFlag::eTHREE_AXIS)
//                        emit dataOutput(ECommAck::eADJUST_LEN_ACK, ECommStatus::eCOMM_COMP, strTmp); //移动指令ack
//                    else if(u16_data == EStatusFlag::eDEFLATE) //放气
//                        emit dataOutput(ECommAck::eDEFLATE_ACK, ECommStatus::eCOMM_COMP, strTmp);
//                    else if(u16_data == EStatusFlag::eDEVICE_EXIT) //退出
//                        emit dataOutput(ECommAck::eEXIT_ACK, ECommStatus::eCOMM_COMP, strTmp);
//                    else ;
//                }

//                strSum.clear();
//                i = 0;
//                break;
//            default:   //无效指令
//                ++i; //
//                break;
//            } //
//        }
//    }

//    strSum.remove(0, i - 1);
//    if(m_strPre.length() != 0) m_strPre.clear();
//    m_strPre.push_back(strSum); //存储剩余数据
//    return false;
//}

/**
 * @brief CClensMachineQH_SHJ::dataParsing
 * @param str
 * @param length
 * @return
 */
bool CClensMachineQH_SHJ::dataParsing(QByteArray str, const int &length) {
    Q_UNUSED(length);

    QByteArray strSum = m_strPre + str;
#if 0
    uint8_t test_data[8] = {0x92, 0x01, 0x03, 0x02, 0x00, 0x00, 0xb8, 0x44};
    QByteArray strSum((char*)&test_data[0], 8);
#endif

    if (strSum.length() < (uint16_t)CMyModBus::EModbusRtuFrame::eHEADER_LEN) {  //固定部分长度6
        m_strPre = strSum;
        return false;
    } else if (strSum.length() > 2000) {  //防止异常解析
        m_strPre.clear();
        return false;
    }

    QVector<uint16_t> reg_v;
    int               i = 0;
    uint8_t           num;
    uint16_t          crc_16_cal;

#ifdef COMM_ACK_OUTPUT
    LOG_INFO(MyLogger::LogType::COMM_ACK, QString("len machine ack: %1").arg(NsTypeConvert::byteArrayToString(strSum)));
#endif

    /*1.1 parse*/
    for (i = 0; i < (strSum.length() - 6); ++i) {    //留7个数(读寄存器）
        if ((uchar)strSum.at(i) == m_device_addr) {  //帧头
            QByteArray strTmp;                       //有效完整指令
            strTmp.clear();

            CMyModBus::StSingleWriteWord *st_write_word_ = nullptr;
            uint16_t                      reg_addr = 0, u16_data = 0;

            strTmp.push_back((uchar)strSum.at(i));
            switch ((uchar)strSum.at(i + 1)) {            //
            case CMyModBus::EFunctionCode::eREAD_RW_REG:  //数据反馈-读flag数据
                strTmp.push_back((uchar)strSum.at(i + 1));

                num = (uchar)strSum.at(i + 2);  //
                strTmp.push_back(num);

                crc_16_cal = 0;
                if ((strSum.length() - i) >= (num + 5)) {  //数量符合

                    reg_v.clear();
                    for (uint8_t j = 0; j < (num >> 1); ++j) {
                        strTmp.push_back((uchar)strSum.at(i + 3 + (j << 1)));
                        strTmp.push_back((uchar)strSum.at(i + 3 + (j << 1) + 1));
                        uint16_t data = ((uchar)strSum.at(i + 3 + (j << 1)) << 8) | (uchar)strSum.at(i + 3 + (j << 1) + 1);  //
                        reg_v.push_back(data);
                    }

                    crc_16_cal          = CMyModBus::crc16(strTmp, num + 3);
                    uint16_t crc_16_tmp = ((uchar)strSum.at(i + num + 3) << 8) | (uchar)strSum.at(i + num + 4);
                    if (crc_16_cal == crc_16_tmp) {
                        if (num == 0x02) {  // device status flag
                            strTmp.clear();

                            switch (m_cmd_read_addr) {
                            case Hmi_visiual_status:
                                if (reg_v.at(0) == (uint16_t)EVisualStatus::eVISUAL_ADJUSTED) {
                                    emit dataOutput(ECommAck::eAUTO_ADJUST_STATUS_ACK, ECommStatus::eCOMM_COMP, strTmp);
                                } else if (reg_v.at(0) == (uint16_t)EVisualStatus::eVISUAL_ADJUST_ERR) {
                                    emit dataOutput(ECommAck::eAUTO_ADJUST_STATUS_ACK, ECommStatus::eCOMM_FATAL, strTmp);
                                } else {
                                    emit dataOutput(ECommAck::eAUTO_ADJUST_STATUS_ACK, ECommStatus::eCOMM_OK, strTmp);
                                }
                                break;

                            case Hmi_move_status:
                                if (reg_v.at(0) == (uint16_t)EMoveStatus::eMOVED) {
                                    emit dataOutput(ECommAck::eMOVE_STATUS_ACK, ECommStatus::eCOMM_COMP, strTmp);
                                } else if (reg_v.at(0) == (uint16_t)EMoveStatus::eMOVE_TIMEOUT) {
                                    emit dataOutput(ECommAck::eMOVE_STATUS_ACK, ECommStatus::eCOMM_TIMEOUT, strTmp);
                                } else if (reg_v.at(0) == (uint16_t)EMoveStatus::eMOVE_DIST_ERR) {
                                    emit dataOutput(ECommAck::eMOVE_STATUS_ACK, ECommStatus::eCOMM_ERROR, strTmp);
                                } else {
                                    emit dataOutput(ECommAck::eMOVE_STATUS_ACK, ECommStatus::eCOMM_OK, strTmp);
                                }
                                break;

                            case Hmi_ack_flag:
                                if (reg_v.at(0) == EStatusFlag::eCONTROL_WAIT)  //
                                    emit dataOutput(ECommAck::eLEN_STATUS_ACK, ECommStatus::eCOMM_OK, strTmp);
                                else if (reg_v.at(0) == EStatusFlag::eLEN_CAPPED)  //抓取状态获取
                                    emit dataOutput(ECommAck::eLEN_STATUS_ACK, ECommStatus::eCOMM_COMP, strTmp);

                                else if (reg_v.at(0) == EStatusFlag::eSOLID)
                                    emit dataOutput(ECommAck::eSOLID_STATUS_ACK, ECommStatus::eCOMM_OK, strTmp);
                                else if (reg_v.at(0) == EStatusFlag::eSOLIDDED)
                                    emit dataOutput(ECommAck::eSOLID_STATUS_ACK, ECommStatus::eCOMM_COMP, strTmp);
                                else {
                                }
                                break;

                            default:
                                break;
                            }

                        } else if (num == 12) {  // device location
                            QVector<float> loc_tmp;
                            uint8_t        tmp_buff[4];
                            for (uint8_t n = 0; n < 3; ++n) {
                                tmp_buff[0] = reg_v.at(n << 1) & 0xff;
                                tmp_buff[1] = (reg_v.at(n << 1) >> 8) & 0xff;
                                tmp_buff[2] = reg_v.at((n << 1) + 1) & 0xff;
                                tmp_buff[3] = (reg_v.at((n << 1) + 1) >> 8) & 0xff;  // HSB
                                loc_tmp.push_back(*(float *)(tmp_buff));
                            }
                            emit floatDataOutput(ECommAck::eLOC_ACK, ECommStatus::eCOMM_COMP, loc_tmp);
                        }
                        i += num + 5;
                    } else {
                        i += 2;

                        // qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-clen mod crc16 cal: " << crc_16_cal;
                        strTmp.clear();
                        if (reg_v.at(0) == EStatusFlag::eLEN_CAPPED) {
                            emit dataOutput(ECommAck::eLEN_STATUS_ACK, ECommStatus::eCOMM_ERROR, strTmp);
                        } else if (reg_v.at(0) == EStatusFlag::eSOLIDDED) {
                            emit dataOutput(ECommAck::eSOLID_STATUS_ACK, ECommStatus::eCOMM_ERROR, strTmp);
                        } else {
                        }
                    }
                } else {
                    strSum.remove(0, i);  // i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据f
                    return false;
                }
                break;
            case CMyModBus::EFunctionCode::eWRITE_REG_CONT:
                emit dataOutput(ECommAck::eADJUST_LEN_ACK, ECommStatus::eCOMM_COMP, strTmp);  //移动指令ack
                break;
            case CMyModBus::EFunctionCode::eWRITE_REG:  //调速中的ack会被屏蔽
                strSum.remove(0, i);
                //              m_strPre.clear();
                //              m_strPre.push_back(strSum); //存储剩余数据

                st_write_word_ = reinterpret_cast<CMyModBus::StSingleWriteWord *>(strSum.data());
                reg_addr       = ((st_write_word_->addr & 0xff) << 8) | ((st_write_word_->addr >> 8) & 0xff);
                u16_data       = ((st_write_word_->data & 0xff) << 8) | ((st_write_word_->data >> 8) & 0xff);

                if (reg_addr == Hmi_ack_start) {
                    // emit dataOutput(ECommAck::eLEN_CAP_ACK, ECommStatus::eCOMM_COMP, strTmp);

                    emit dataOutput(ECommAck::eAUTO_ADJUST_ACK, ECommStatus::eCOMM_COMP, strTmp);
                } else {
                    if (u16_data == EStatusFlag::eMACHINE_START) {
                        emit dataOutput(ECommAck::eLEN_CAP_ACK, ECommStatus::eCOMM_COMP, strTmp);
                    } else if (u16_data == EStatusFlag::eSOLID)  //固化
                        emit dataOutput(ECommAck::eSOLID_ACK, ECommStatus::eCOMM_COMP, strTmp);
                    //                    else if (u16_data == EStatusFlag::eTHREE_AXIS)
                    //                        emit dataOutput(ECommAck::eADJUST_LEN_ACK, ECommStatus::eCOMM_COMP, strTmp);  //移动指令ack
                    else if (u16_data == EStatusFlag::eDEFLATE)  //放气
                        emit dataOutput(ECommAck::eDEFLATE_ACK, ECommStatus::eCOMM_COMP, strTmp);
                    else if (u16_data == EStatusFlag::eDEVICE_EXIT)  //退出
                        emit dataOutput(ECommAck::eEXIT_ACK, ECommStatus::eCOMM_COMP, strTmp);
                    else
                        ;
                }

                strSum.clear();
                i = 0;
                break;
            default:  //无效指令
                //++i; //
                break;
            }  //
        }
    }

    if (strSum.length() != 0 && i > 0)
        strSum.remove(0, i - 1);
    if (m_strPre.length() != 0)
        m_strPre.clear();
    m_strPre.push_back(strSum);  //存储剩余数据
    return false;
}


void CClensMachineQH_SHJ::cleanMoveDistance(void) {
    mst_move_delta.x = 0;
    mst_move_delta.y = 0;
    mst_move_delta.z = 0;
}

C3dHandMachine::St3D<int16_t> CClensMachineQH_SHJ::getMoveStep(void) {
    C3dHandMachine::St3D<int16_t> move_step;
    move_step.x = mst_move_delta.x / m_step_dis.x;
    move_step.y = mst_move_delta.y / m_step_dis.y;
    move_step.z = mst_move_delta.z / m_step_dis.z;
    return move_step;
}

C3dHandMachine::St3D<int16_t> CClensMachineQH_SHJ::getMoveDist() {
    return mst_move_delta;
}
/**
 * @brief: 计算移动距离
 * @param: 通道差，peak差值
 * @return: 移动距离
 */
inline C3dHandMachine::St3D<int16_t> CClensMachineQH_SHJ::move_dis_cal(const int8_t &mp_delta, const int16_t &peak_delta) {
    St3D<int16_t> move_dis;

    move_dis.x = mp_delta * abs(mp_delta) * peak_delta * m_step_dis.x;
    move_dis.y = mp_delta * abs(mp_delta) * peak_delta * m_step_dis.y;
    move_dis.z = mp_delta * abs(mp_delta) * peak_delta * m_step_dis.z;
    return move_dis;
}


/**
 * @brief CClensMachineQH_SHJ::distance_limit
 * @param delta
 * @param limit
 * @param dis
 * @return
 */
inline int16_t CClensMachineQH_SHJ::distance_limit(int16_t *delta, const uint16_t &limit, const int16_t &dis) {
    //    if((*delta) >= limit) {
    //        if((*delta * dis) > 0) {//不变
    //            qDebug() << "-c dis_delta limit";
    //            return 0;
    //        }
    //    }
    int16_t delta_tmp = *delta + dis;
    if (abs(delta_tmp) > limit) {
        if (dis > 0) {
            *delta = limit;
            return dis - (delta_tmp - limit);
        } else {
            *delta = -limit;
            return dis - (delta_tmp + limit);
        }
    }
    *delta = delta_tmp;
    return dis;
}

/**
 * @brief: 设备控制
 * 移动距离，已移动距离 -> 限位，
 */
// bool CClensMachineQH_SHJ::deviceSpeedHandle(C3dHandMachine::St3D<int16_t> *move_dist_, const C3dHandMachine::St3D<uint16_t> &limit_radius)
//{
////    m_lens_adjust_serial_->m_device_ctr_frame_->data.clear();
//    int16_t xy_tmp, radius;

//    /*1. 限位*/
//    if(move_dist_->x != 0)
//    {
//        xy_tmp = mst_move_delta.x + move_dist_->x;
//        radius = sqrt(pow(xy_tmp, 2) + pow(mst_move_delta.y ,2));
//        if(radius < limit_radius.x) //m_xml_param["xy_radius_limit"]
//        {
//            mst_move_delta.x = xy_tmp;
//        }
//        else
//        {
//            move_dist_->x = 0;
//            qDebug() << "-c x radius limit:" << radius;
//        }
////        move_dist_->x = distance_limit(&m_device_info_->mst_move_delta.x, limit, move_dist_->x);
//    }
//    if(move_dist_->y != 0)
//    {
//        xy_tmp = mst_move_delta.y + move_dist_->y;
//        radius = sqrt(pow(mst_move_delta.x, 2) + pow(xy_tmp, 2));
//        if(radius < limit_radius.y) //m_xml_param["xy_radius_limit"]
//        {
//            mst_move_delta.y = xy_tmp;
//        }
//        else
//        {
//            move_dist_->y = 0;
//            qDebug() << "-c y radius limit:" << radius;
//        }
////        move_dist_->y = distance_limit(&m_device_info_->mst_move_delta.y, limit, move_dist_->y);
//    }
//    if(move_dist_->z != 0)
//    {
//        move_dist_->z = distance_limit(&mst_move_delta.z, limit_radius.z, move_dist_->z); //m_xml_param["z_radius_limit"]
//    }

//    /*2. 限最小速 */
//    if(move_dist_->x != 0 || move_dist_->y != 0 || move_dist_->z != 0)
//    {
//        adjust(*move_dist_);

////        m_lens_adjust_serial_->m_device_ctr_frame_->data.push_back(move_dist->x);
////        m_lens_adjust_serial_->m_device_ctr_frame_->data.push_back(0); //两个字寄存器
////        m_lens_adjust_serial_->m_device_ctr_frame_->data.push_back(move_dist->y);
////        m_lens_adjust_serial_->m_device_ctr_frame_->data.push_back(0); //
////        m_lens_adjust_serial_->m_device_ctr_frame_->data.push_back(move_dist->z);

////        m_lens_adjust_serial_->m_device_flag_frame_->data.clear();
////        m_lens_adjust_serial_->m_device_flag_frame_->data.push_back(0x07);

////        m_lens_adjust_serial_->machineCtrl();
//        qDebug() << "-clen: move distance" << move_dist_->x << move_dist_->y << move_dist_->z;
//        qDebug() << "-clen: move delta:" << mst_move_delta.x << mst_move_delta.y << mst_move_delta.z;
////        if(mst_config_->mode == EMode::auto_mode)
////        {
////            communicate_start(mst_task_status_->cur_status);
////        }
//        return true;
//    }
//    return false;
//}

/**
 * @brief: 设备控制, 传入移动步进，转换成移动距离
 * 移动距离，已移动距离 -> 限位，
 */
uint8_t CClensMachineQH_SHJ::deviceSpeedHandle(const C3dHandMachine::St3D<int16_t> &move_step, uint16_t &wait_times) {
    //    m_lens_adjust_serial_->m_device_ctr_frame_->data.clear();
    uint8_t device_status = 0;

    int16_t                       xy_tmp, radius;
    C3dHandMachine::St3D<int16_t> move_dis;
    move_dis.x = move_step.x * m_step_dis.x;  //
    move_dis.y = move_step.y * m_step_dis.y;
    move_dis.z = move_step.z * m_step_dis.z;

    /*1. 限位*/
    if (move_dis.x != 0) {
        xy_tmp = mst_move_delta.x + move_dis.x;
        radius = sqrt(pow(xy_tmp, 2) + pow(mst_move_delta.y, 2));
        if (radius < m_xml_param["xy_radius_limit"]) {  // m_xml_param["xy_radius_limit"]
            mst_move_delta.x = xy_tmp;
        } else {
            move_dis.x = 0;
            device_status |= (uint8_t)IClensMachine::EDeviceStatus::eXY_AXIS_LMIT;
            qDebug() << "-c x radius limit:" << radius;
        }
        //        move_dist_->x = distance_limit(&m_device_info_->mst_move_delta.x, limit, move_dist_->x);
    }
    if (move_dis.y != 0) {
        xy_tmp = mst_move_delta.y + move_dis.y;
        radius = sqrt(pow(mst_move_delta.x, 2) + pow(xy_tmp, 2));
        if (radius < m_xml_param["xy_radius_limit"]) {  // m_xml_param["xy_radius_limit"]
            mst_move_delta.y = xy_tmp;
        } else {
            move_dis.y = 0;
            device_status |= (uint8_t)IClensMachine::EDeviceStatus::eXY_AXIS_LMIT;
            qDebug() << "-c y radius limit:" << radius;
        }
    }
    if (move_dis.z != 0) {
        if (mst_move_delta.z >= m_xml_param["z_radius_limit"]) {
            if ((mst_move_delta.z * move_dis.z) > 0) {  //不变
                move_dis.z = 0;

                device_status |= (uint8_t)IClensMachine::EDeviceStatus::eZ_AXIS_LMIT;
                qDebug() << "-e dis_delta limit";
            } else
                move_dis.z = distance_limit(&mst_move_delta.z, m_xml_param["z_radius_limit"], move_dis.z);
        } else
            move_dis.z = distance_limit(&mst_move_delta.z, m_xml_param["z_radius_limit"], move_dis.z);  // m_xml_param["z_radius_limit"]
    }

    /*2. 限最小速 */
    if (move_dis.x != 0 || move_dis.y != 0 || move_dis.z != 0) {
        if (adjust(move_dis)) {
            uint16_t x_times = abs(move_step.x);
            uint16_t y_times = abs(move_step.y);
            uint16_t z_times = abs(move_step.z);
            wait_times       = x_times > y_times ? x_times : y_times;
            wait_times       = wait_times > z_times ? wait_times : z_times;
            wait_times       = wait_times > 5 ? 5 : wait_times;
            if (wait_times == 0)
                wait_times = 1;

            device_status |= (uint8_t)IClensMachine::EDeviceStatus::eCMD_STATUS;
#ifdef PROCESS_DATA
            qInfo() << "clenST/ move distance" << move_dis.x << move_dis.y << move_dis.z;
//            qDebug() << "-i clenST/ move delta:" << mst_move_delta.x << mst_move_delta.y << mst_move_delta.z;
#endif
        }
    } else
        device_status |= (uint8_t)IClensMachine::EDeviceStatus::eCMD_SEND;
    return device_status;
}
