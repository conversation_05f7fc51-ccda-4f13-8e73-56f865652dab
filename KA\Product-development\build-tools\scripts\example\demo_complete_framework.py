#!/usr/bin/env python3
"""
产品体系构建框架完整演示脚本
展示从项目初始化到文档追溯的完整流程
"""

import os
import sys
import time
from pathlib import Path

# Add scripts path
current_dir = Path(__file__).parent.absolute()
scripts_base = current_dir.parent / "scripts"
sys.path.insert(0, str(scripts_base))

try:
    from workflow.workflow_manager import WorkflowManager
except ImportError:
    WorkflowManager = None

try:
    from infoTrace.traceability_manager import TraceabilityManager
except ImportError:
    TraceabilityManager = None

def print_section(section_title):
    """打印章节标题"""
    print(f"
{'='*60}")
    print(f"  {section_title}")
    print('='*60)

def print_step(step, description):
    """打印步骤信息"""
    print(f"
[步骤 {step}] {description}")
    print("-" * 40)

def main():
    """主演示函数"""
    print_section("产品体系构建框架完整演示")
    
    print("本演示将展示框架的核心功能：")
    print("1. 工作流管理系统")
    print("2. 信息追溯系统") 
    print("3. 文档关联系统")
    print("4. 报告生成系统")
    
    # 步骤1: 工作流系统演示
    print_step(1, "工作流管理系统演示")
    try:
        if WorkflowManager:
            wm = WorkflowManager("config/workflow_config.json")
            print("[+] 工作流管理器初始化成功")
            
            # 生成工作流报告
            if hasattr(wm, 'generate_workflow_report'):
                wm.generate_workflow_report()
                print("[+] 工作流报告生成完成")
        else:
            print("[!] 工作流管理器模块未找到，跳过演示")
        
    except Exception as e:
        print(f"[X] 工作流系统错误: {e}")
    
    time.sleep(1)
    
    # 步骤2: 信息追溯系统演示
    print_step(2, "信息追溯系统演示")
    try:
        if TraceabilityManager:
            tm = TraceabilityManager("config/traceability_config.json")
            print("[+] 追溯管理器初始化成功")
            
            # 扫描文档
            if hasattr(tm, 'scan_project_documents'):
                documents = tm.scan_project_documents()
                print(f"[+] 扫描到 {len(documents)} 个文档")
            
            # 更新追溯表格
            if hasattr(tm, 'update_tracking_tables'):
                tm.update_tracking_tables()
                print("[+] 追溯表格更新完成")
            
            # 生成追溯报告
            if hasattr(tm, 'generate_reports'):
                tm.generate_reports()
                print("[+] 追溯报告生成完成")
        else:
            print("[!] 追溯管理器模块未找到，跳过演示")
        
    except Exception as e:
        print(f"[X] 追溯系统错误: {e}")
    
    print_section("演示完成")
    print("产品体系构建框架核心功能演示完成！")
    print("
各组件配置文件由对应组件维护：")
    print("- 工作流配置: workflow组件负责")
    print("- 追溯配置: infoTrace组件负责")
    print("- 目录结构: directory_initialization组件负责")

if __name__ == "__main__":
    main()
