#include "clenEnterDataMesOpt.h"
#include "sensorBoardFactory.h"
#include <QCoreApplication>
#include <QDebug>
#include <QSqlDatabase>
#include <QTimerEvent>
#include <math.h>
#include <qsqlquery.h>


#include "qLog.h"
#include "typeConvert.h"
#include "uart.h"


#define TIMER_MS         5
#define MAIN_UART_BAUD   230400
#define DEVICE_UART_BAUD 115200


#define MES_SUPER_USER   "Uadmin"

clenEnterDataMesOpt::clenEnterDataMesOpt(const NClenEnterDataMes::StUiConfig &config)
    : mst_config_(&config)  //
      ,
      m_timerId(0),
      m_port_update_cnt(0),
      mc_processList_(new TClen_process),
      mst_task_status_(new TClen_process::StStepRecord)
      //  , mst_map_info_(new IFaculaAdjust::StMapInfo)
      //  , m_map_data_(new IFaculaAdjust::StMapData)
      //  , mi_icomm_(new CUART("", MAIN_UART_BAUD)) //默认配置
      //  , mi_top_board_(new CSensorCoinD(mi_icomm_)) //设备端口
      ,
      mst_comm_status_(new StCommunicateStatus),
      m_message_box_(new QWidget),
      mi_save_file_(new CSaveExcel),
      mc_sql_handle_(new CLenSqlHandle),
      mst_work_order_(new CLenSqlHandle::StWorkOrder),
      mst_xpid_det_(new CLenSqlHandle::StXpiddet),
      mst_mes_xsub2_data_(new CLenSqlHandle::StResultToMes),
      mst_result_(new StResult),
      mst_dds_(new StDds) {
    //********************************** varibles init ******************************
    //* 1.默认外部参数读取
    mst_iniConfig = DATACONFIG;
    qInfo() << "len/ config ini: "
            << "\n user id:" << mst_iniConfig.userid << "\n operator position:" << mst_iniConfig.op << "\n sensor version:" << mst_iniConfig.version
            << "\n work order:" << mst_iniConfig.work_number << "\n sensor project: " << mst_iniConfig.sensor_device
            << "\n sensor project baud: " << mst_iniConfig.sensor_device_buad;

    mi_icomm_ = new CUART("", mst_iniConfig.sensor_device_buad);  //默认配置

    mst_facula_sensor_info              = ISensorBoardFactory::sst_facula_sensor_info[(ISensorBoardFactory::ESensorBoard)mst_iniConfig.sensor_device];
    mst_facula_sensor_info.target_tf.ax = mst_iniConfig.facula_center_loc_x;
    mst_facula_sensor_info.target_tf.ay = mst_iniConfig.facula_center_loc_y;

    mc_facula_context_ = new CFaculaContext(mst_facula_sensor_info);

    mi_top_board_ = ISensorBoardFactory::getInstance().sensorBoardCreate((ISensorBoardFactory::ESensorBoard)mst_iniConfig.sensor_device, mi_icomm_);  //设备端口


    mi_top_board_ = new CSensorCoinD(mi_icomm_);  //设备端口

    //* 2.内部参数初始化
    //**********************************************子线程***********************
    //* 1.初始化就创建子线程
    LOG_INFO(MyLogger::LogType::PROCESS_STATUS, QString("Main thread id:%1").arg(reinterpret_cast<quintptr>(QThread::currentThread())));

    m_sub_thread_ = new QThread;  // 创建线程对象

    /* 创建工作的类对象，千万不要指定给创建的对象指定父对象
     * 如果指定了: QObject::moveToThread: Cannot move objects with a parent
     */
    m_serial_thread_ = new CLensAdjustSerial(nullptr, mi_top_board_, nullptr);
    m_serial_thread_->moveToThread(m_sub_thread_);  //将工作的类对象移动到创建的子线程对象中

    //* 1.3 启动线程
    m_sub_thread_->start();
    //    m_serial_thread_->task_id_change(4);


    //******************************************* connect ***************************
    qRegisterMetaType<clenEnterDataMesOpt::EProcessStep>("clenEnterDataMesOpt::EProcessStep");

    connect(this, &clenEnterDataMesOpt::subThreadSignal, m_serial_thread_, &CLensAdjustSerial::loop);
    connect(m_sub_thread_, &QThread::finished, m_serial_thread_, &QObject::deleteLater);


    connect(dynamic_cast<CSensorCoinD *>(mi_top_board_), &CSensorCoinD::dataOutput, this, &clenEnterDataMesOpt::sensorDataReceive);  //无法用多态实现


    //************************************* task list init *************************
    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::readyStep, &clenEnterDataMesOpt::readyAck, true, 200, 0, 0, 0));  // readyAck

    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::startStep, nullptr, true, 0, 0, 0, 0));
    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::calibMode, &clenEnterDataMesOpt::calibModeAck, true, 0, 0, 100, 3));  //
    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::chipIdStep, &clenEnterDataMesOpt::chipIdAck, true, 500, 0, 200, 5));  //
    mv_task_list.append(
        mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::sensorVersionStep, &clenEnterDataMesOpt::sensorVersionAck, true, 0, 0, 50, 3));           //
    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::greyMapMode, &clenEnterDataMesOpt::greyMapModeAck, true, 0, 0, 100, 3));  //
    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::faculaTest, &clenEnterDataMesOpt::faculaTestAck, true, 0, 0, 500, 3));    //
    mv_task_list.append(mc_processList_->addCallbackFunction(&clenEnterDataMesOpt::compStep, &clenEnterDataMesOpt::compAck, true, 0, 0, 100, 3));


    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    m_timerId = startTimer(TIMER_MS);

    //******************************** 3.数据库 ******************************


    mm_result_data.clear();
    mm_result_data.insert("时间", "");  //无法排序
    mm_result_data.insert("芯片ID", "");
    mm_result_data.insert("事务号", "");
    mm_result_data.insert("标签号nbr", "");
    mm_result_data.insert("光斑MP", "");
    mm_result_data.insert("放气光斑code", "");
    mm_result_data.insert("结果", "");
    mm_result_data.insert("fatal reason", "");

    QString desk_path = QApplication::applicationDirPath();  // QStandardPaths::writableLocation(QStandardPaths::DesktopLocation);
    mi_save_file_->createOneFile(desk_path, "/镜片数据手动录入MES", mm_result_data);
    if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES
        mst_mes_xsub2_data_->userid = mst_iniConfig.userid;
        mst_mes_xsub2_data_->op     = mst_iniConfig.op;
        mst_work_order_->wo_lot     = mst_iniConfig.work_number;
        mst_work_order_->domain     = mst_iniConfig.work_domain;
        //        mst_xpid_det_->work_order = mst_iniConfig.work_number;
    }
    if (!mc_sql_handle_->connectXSub2DetDB()) {
        QMessageBox::warning(m_message_box_, QString("数据库连接失败"), QString("请检查数据库连接"));
    }
    //* local
    //#define MES_TEST
#ifdef MES_TEST
    //    mc_sql_handle_->connectTestDB();
    uint16_t error_dds = 0;

    mst_work_order_->wo_lot = "6688";
    error_dds               = mc_sql_handle_->checkWorkOrder(mst_work_order_);

    if (error_dds == 0) {
        mst_xpid_det_->mcuid      = QString("30393337000e30353332db9f").toUpper();
        mst_xpid_det_->work_order = mst_work_order_->wo_lot;  // 6688
        mst_xpid_det_->domin      = mst_work_order_->domin;
        error_dds |= mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, true);
    }

    if (error_dds == 0) {
        mesDataHandle(mst_mes_xsub2_data_);
        error_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);
    }


#endif
    //* 4.test
#if 1
    QVector<QVector<uint32_t>>     map_matrix = {//        {5, 54, 155, 201, 44},
                                             //        {26, 34, 182, 319, 503},
                                             //        {36, 62, 218, 491, 940},
                                             //        {17, 25, 191, 392, 817},
                                             //        {4, 104, 200, 309, 69}
                                             {
                                                 {0, 98, 150, 281, 25},
                                                 {42, 60, 283, 531, 336},
                                                 {43, 101, 514, 952, 563},
                                                 {27, 125, 293, 551, 372},
                                                 {0, 26, 148, 210, 9},
                                             }};
    IFaculaAdjust::UFaculaJudgeDds facula_dds_info;
    QVector<uint32_t>              target_map;

    mc_facula_context_->localFaculaTest(map_matrix, facula_dds_info.dds_info, target_map, IFaculaAdjust::eFACULA_ADJUST_TEST);
#endif
}

clenEnterDataMesOpt::~clenEnterDataMesOpt() {
    delete mc_processList_;
    delete mst_task_status_;

    //* 子线程注销
    //    m_serial_thread_->m_task_id = 0;
    m_serial_thread_->task_id_change(0);
    m_sub_thread_->quit();
    m_sub_thread_->wait();
    delete m_sub_thread_;  //会析构 m_serial_thread_

    killTimer(m_timerId);
    QThread::msleep(50);


    delete mi_top_board_;
    if (mi_icomm_ != nullptr)
        delete mi_icomm_;

    delete mst_comm_status_;

    //    uint8_t item_lens = mst_map_info_->table_item.length();
    //    if(item_lens != 0) {
    //        for(uint i = 0; i < item_lens; i++) {
    //            if(mst_map_info_->table_item.at(i) != nullptr)
    //                delete mst_map_info_->table_item.at(i);
    //        }
    //    }
    //    delete mst_map_info_;
    //    delete m_map_data_;

    if (mc_facula_context_ != nullptr)
        delete mc_facula_context_;

    /*释放内存*/
    delete mi_save_file_;

    delete mc_sql_handle_;
    delete mst_work_order_;
    delete mst_xpid_det_;
    delete mst_mes_xsub2_data_;
    delete m_message_box_;

    delete mst_result_;
    delete mst_dds_;
}


void clenEnterDataMesOpt::timerEvent(QTimerEvent *event) {
    //  Q_UNUSED(event);

    if (m_timerId == event->timerId()) {
        //*******************************非任务task
        //* 1.串口列表刷新
        m_port_update_cnt++;
        if (m_port_update_cnt == 400) {
            m_port_update_cnt = 0;
            portlistUpdate();
        }

        //******************************* 任务循环 ********************
        if (mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_) == eFATAL) {  // tasks exec errror, dont have ack or other reason
            mst_dds_->process_step_dds |= (1 << (uint8_t)mst_task_status_->cur_step);
            emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_STEP_ERROR, mst_dds_->process_step_dds));
            //            mc_processList_->taskInit(&mv_task_list, mst_task_status_);

            mv_task_list[EProcessStep::eCOMPLETE].flag.stop = true;
        }

        //* 状态界面显示更新
        if (mst_task_status_->cur_step != mst_task_status_->last_step) {
            mst_task_status_->last_step = mst_task_status_->cur_step;

            //            if(mst_task_status_->cur_status != mst_task_status_->last_status) { //?
            //            emit stepStatusSignal((int16_t)mst_task_status_->cur_step, (int16_t)mst_task_status_->cur_ack_status);

            //            }
        }
    }
}

/**
 * @brief
 */
void clenEnterDataMesOpt::varibleInit() {
    //* 运行状态
    mc_processList_->taskInit(&mv_task_list, mst_task_status_);
    m_serial_thread_->task_id_change(4);

    mst_task_status_->cur_step        = eCOMPLETE;
    mst_task_status_->cur_exec_status = eWAIT;
    mst_task_status_->cur_ack_status  = eWAIT;
    mst_task_status_->last_step       = eCOMPLETE;
    mst_task_status_->last_status     = eWAIT;

    //* 原数据
    m_origin_bytes.clear();

    //* 库数据
    mst_mes_xsub2_data_->nbr = "";
    //    mst_mes_xsub2_data_->op = "";
    //    mst_mes_xsub2_data_->userid = "";
    mst_mes_xsub2_data_->date     = "";
    mst_mes_xsub2_data_->time     = 0;
    mst_mes_xsub2_data_->rslt     = false;
    mst_mes_xsub2_data_->rsn_code = "";
    mst_mes_xsub2_data_->param[0] = 0;
    mst_mes_xsub2_data_->param[1] = 0;
    mst_mes_xsub2_data_->param[2] = 0;
    mst_mes_xsub2_data_->param[3] = 0;
    mst_mes_xsub2_data_->param[4] = 0;
    mst_mes_xsub2_data_->param[5] = 0;
    mst_mes_xsub2_data_->param[6] = 0;
    mst_mes_xsub2_data_->param[7] = 0;
    mst_mes_xsub2_data_->param[8] = 0;

    //* result data
    mst_result_->chip_id.clear();
    mst_result_->origin_data = "";
    //    mst_result_->load_data = "";
    mst_result_->fatal_reason.clear();
    mst_result_->finalResult.result = 0xff;  // true
    mst_result_->finalResult.mp_origin_data.clear();
    mst_result_->finalResult.map_matrix.clear();
    mst_result_->target_map.clear();

    mst_dds_->process_item_dds.errors = 0;
    mst_dds_->process_step_dds        = 0;
    mst_dds_->mes_dds                 = 0;
}

void clenEnterDataMesOpt::mesDataShow(const QString &tr) {
    QString mes_data =
        tr + QObject::tr("(xsub2_domain, xsub2_trnbr, xsub2_nbr, xsub2_op, xsub2_userid, xsub2_date, xsub2_time, xsub2_rslt, xsub2_rsn_code, "
                         "xsub2_param1, xsub2_param2, xsub2_param3, xsub2_param4, xsub2_param5, xsub2_param6, xsub2_param7, xsub2_param8, xsub2_param9)"
                         "values('%1', '%2', '%3', '%4', '%5', '%6', '%7', '%8', '%9', '%10', '%11', '%12', '%13', '%14', '%15', '%16', '%17', '%18')")
                 .arg(mst_mes_xsub2_data_->domain)
                 .arg(mst_mes_xsub2_data_->trnbr)
                 .arg(mst_mes_xsub2_data_->nbr)
                 .arg(mst_mes_xsub2_data_->op)
                 .arg(mst_mes_xsub2_data_->userid)
                 .arg(mst_mes_xsub2_data_->date)
                 .arg(mst_mes_xsub2_data_->time)
                 .arg(mst_mes_xsub2_data_->rslt)
                 .arg(mst_mes_xsub2_data_->rsn_code)
                 .arg(mst_mes_xsub2_data_->param[0])
                 .arg(mst_mes_xsub2_data_->param[1])
                 .arg(mst_mes_xsub2_data_->param[2])
                 .arg(mst_mes_xsub2_data_->param[3])
                 .arg(mst_mes_xsub2_data_->param[4])
                 .arg(mst_mes_xsub2_data_->param[5])
                 .arg(mst_mes_xsub2_data_->param[6])
                 .arg(mst_mes_xsub2_data_->param[7])
                 .arg(mst_mes_xsub2_data_->param[8]);

    emit moduleInfoShowSignal(false, mes_data);
}

/**
 * @brief CLenAdjustOpt::portlistUpdate
 * @return
 */
bool clenEnterDataMesOpt::portlistUpdate(void) {
    QStringList *port_list_ = new QStringList;

    bool port_flag = mi_icomm_->scanPort(port_list_, mst_config_->cur_port_name);  //列表名称变化
    emit portUpdateSignal(port_list_, port_flag);

    return true;
}

/**
 * @brief clenEnterDataMesOpt::dataRecive
 * @param step
 * @param status
 * @param bytes
 */
void clenEnterDataMesOpt::sensorDataReceive(ITopBoard::ECommStep step, ECommStatus status, QByteArray bytes) {
    uint16_t num = bytes.length() >> 2;

    mst_comm_status_->comm_status = status;
    if (num != 0) {
#if 0
        if(m_origin_bytes.length() == 0) m_origin_bytes = bytes;
        else qDebug() << "-clen: data receive busy";
#else
        m_origin_bytes = bytes;  //直接覆盖
#endif
    }

    switch (step) {
    case ITopBoard::eMODE_CHANGE:
        switch (mst_task_status_->cur_step) {
        case EProcessStep::eGREY_MODE:
            mv_task_list[eGREY_MODE].flag.stop = true;
            break;
        case EProcessStep::eCALIB_MODE:
            mv_task_list[eCALIB_MODE].flag.stop = true;
            break;
        default:
            break;
        }
        break;
    case ITopBoard::eCHIP_ID:
        mv_task_list[eCHIP_ID].flag.stop = true;
        break;
    case ITopBoard::eVERSION:
        mv_task_list[eVERSION].flag.stop = true;
        break;
    case ITopBoard::eMAP_DATA:
        mv_task_list[eFACULA_TEST].flag.stop = true;
        break;
        //        switch (mst_task_status_->cur_step) {
        //        case EProcessStep::eMAP_DATA: mv_task_list[eMAP_DATA].flag.stop = true; break;
        //        case EProcessStep::eTEST: mv_task_list[eTEST].flag.stop = true; break;
        //        case EProcessStep::eRETEST: mv_task_list[eRETEST].flag.stop = true; break;
        //        case EProcessStep::eRETEST2: mv_task_list[eRETEST2].flag.stop = true; break;
        //        default: break;
        //        }
        //        break;
    default:
        break;
    }
}

inline void clenEnterDataMesOpt::sleepMs(uint16_t msec) {
    QTime dieTime = QTime::currentTime().addMSecs(msec);
    while (QTime::currentTime() < dieTime)
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
}

QString clenEnterDataMesOpt::sensorVersionParse(const QByteArray &version) {
    if (version.size() < 4)
        return "";
    QString tr_version = QString::number(version.at(0), 10) + "." + QString::number(version.at(1), 10) + "." + QString::number(version.at(2), 10) + "." +
                         QString::number(version.at(3), 10);
    return tr_version;
}

/**
 * @brief CLenAdjustOpt::originDataHanlde
 * @param mp_origin_bytes -> 光斑矩阵数据
 * @return
 */
// bool clenEnterDataMesOpt::originDataHanlde(QByteArray &mp_origin_bytes, IFaculaAdjust::StMapData* map_data_)
//{
//    //    static uint16_t cell_num_last = 0; //传入MP数
//    //    static uint8_t  merge_data_cnt = 0; //传入数据帧个数
//    uint16_t num = mp_origin_bytes.length() >> 2;

//    QVector<uint32_t> map_data; //

//    if((num == 0) || (num != (mst_map_info_->xlens * mst_map_info_->ylens))) {
//        return false;
//    }

//    for(uint16_t n = 0; n < num; ++n) {//
//        uint32_t data_tmp = (uchar)mp_origin_bytes.at((n<<2) + 0) | ((uchar)mp_origin_bytes.at((n<<2) + 1)<<8) | ((uchar)mp_origin_bytes.at((n<<2) + 2)<<16) |
//        ((uchar)mp_origin_bytes.at((n<<2) + 3)<<24); map_data.push_back(data_tmp); //
//    }

//    uint8_t ylen = mst_map_info_->ylens;
//    uint8_t xlen = mst_map_info_->xlens;

//    if(map_data.length() != ylen*xlen) {
//        qDebug() << "-e clen/ map data length error" << map_data.length() << ylen*xlen;
//        return false;
//    }

//    //* 添加数据,
//    if(mst_map_info_->mp_order == IPhotonSensor::EMpOrder::left_2_right) { //光斑顺序 左->右 YC
//        for (int y=0; y<ylen; ++y) {
//            map_data_->map_matrix[y].clear();
//            for (int x=0; x<xlen; ++x) {
//                map_data_->map_matrix[y].push_back(map_data.at((y*xlen) + x));
//            }
//        }
//    }
//    else {
//        for(int i = 0; i<ylen; ++i) {
//            map_data_->map_matrix[i].clear(); //;
//        }
//        for(int x=0; x<xlen; ++x) {
//            for (int y=0; y<ylen; ++y) {
//                map_data_->map_matrix[y].push_back(map_data.at((x*ylen) + y));
//            }
//        }
//    }
//    return true;
//}

bool clenEnterDataMesOpt::checkVersion(const QString &version) {
    if (version != mst_iniConfig.version)
        return false;
    return true;
}

void clenEnterDataMesOpt::mapDataShow() {
    IFaculaAdjust::StMapData map_data = mc_facula_context_->getOriginMapData();

    emit dataAckSignal(map_data.max_peak, map_data.map_matrix);
}

ITable::StTableInfo clenEnterDataMesOpt::getOriginTableInfo() {
    ITable::StTableInfo table_info;
    table_info.x           = mst_facula_sensor_info.sensor_info.xlens;
    table_info.y           = mst_facula_sensor_info.sensor_info.ylens;
    table_info.mp_order    = mst_facula_sensor_info.sensor_info.order;
    table_info.target_mp_x = mst_facula_sensor_info.target_tf.ax;
    table_info.target_mp_y = mst_facula_sensor_info.target_tf.ay;

    return table_info;
}

bool clenEnterDataMesOpt::mesDataHandle(CLenSqlHandle::StResultToMes *st_mes_data_) {
    st_mes_data_->date = QString(QDateTime::currentDateTime().toString("yyyy/MM/dd"));

    st_mes_data_->time = QDateTime::currentDateTime().time().msecsSinceStartOfDay() / 1000;  // s

    if (mst_config_->is_auto_judge_facula) {                                         //自动判定
        st_mes_data_->rslt = (mst_result_->finalResult.result == 0) ? true : false;  //(!mst_result_->finalResult.result) == true;
        if (st_mes_data_->rslt == true)
            st_mes_data_->rsn_code = "";
        else
            st_mes_data_->rsn_code = QString::number(mst_result_->finalResult.result, 16);  // mm_result_data["fatal reason"].toString();
    } else {
        st_mes_data_->rslt     = true;
        st_mes_data_->rsn_code = "";
    }

    uint8_t target_map_cnt = mst_result_->target_map.count();
    if (target_map_cnt == 9) {
        for (uint8_t for_i = 0; for_i < target_map_cnt; for_i++) {
            st_mes_data_->param[for_i] = mst_result_->target_map.at(for_i);
        }
    } else {
        qDebug() << "-e clen adjustOpt/target map cnt error:" << target_map_cnt;
    }
    return true;
}


// QString clenEnterDataMesOpt::errorHandleCallbackFunction(EError_type error_type, const uint16_t &error_code, uint8_t show_type) {
//    switch (error_type) {
//    case EError_type::ePROCESS_STEP_ERROR:
//        break;
//    case EError_type::eADJUST_STEP_ERROR:


//        break;
//    case EError_type::eMES_ERROR:
//        break;
//    default:
//        break;
//    }
//    return QString("process error");
//}

QString clenEnterDataMesOpt::errorInfoPack(EError_type error_type, const uint32_t &error_code) {
    QString   error_info;
    QMetaEnum stepEnum = QMetaEnum::fromType<clenEnterDataMesOpt::EProcessStep>();
    switch (error_type) {
    case EError_type::ePROCESS_STEP_ERROR:
        for (uint32_t for_i = 0; for_i <= (uint16_t)eCOMPLETE; for_i++) {
            uint32_t error_index = error_code & (1 << for_i);
            QString  step_str    = stepEnum.valueToKey(for_i);
            if (error_index != 0)
                error_info += "task: " + step_str + " fatal";
        }
        break;

    case EError_type::ePROCESS_ITEM_ERROR:
        //        error_info = "过程：";
        for (uint32_t for_i = 0; for_i < 32; for_i++) {
            uint32_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += QString::number(for_i, 10) + "." + mm_process_items_discribe[error_index];
        }
        break;

    case EError_type::eFACULA_JUDGE_ERRPR:
        //        error_info = "光斑判定：";
        for (uint32_t for_i = 0; for_i < 16; for_i++) {
            uint16_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += "光斑判定：" + QString::number(for_i, 10) + "." + IFaculaAdjust::mm_facula_judge_discribe[error_index];
        }
        break;

    case EError_type::eMES_ERROR:
        for (uint32_t for_i = 0; for_i < 16; for_i++) {
            uint16_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += "MES：" + QString::number(for_i, 10) + "." + CLenSqlHandle::mm_mes_error_discribe[error_index];
        }

        break;
    default:
        break;
    }

    return error_info;
}

EExecStatus clenEnterDataMesOpt::readyStep(void) {
    //* 端口信息变更，（不包括波特率）
    if (mi_icomm_->getPortName() != mst_config_->port_name) {
        if (mi_icomm_ != nullptr)
            delete mi_icomm_;
        mi_icomm_ = new CUART(mst_config_->port_name, mst_iniConfig.sensor_device_buad);

        mi_top_board_->icom_change_interface(mi_icomm_);
        //        m_serial_thread_->device_change_interface(mi_top_board_, nullptr);
    }

    //    if(mi_icomm_dev_->getPortName() != mst_config_->dev_port_name) {
    //        if(mi_icomm_dev_ != nullptr) delete mi_icomm_dev_;
    //        mi_icomm_dev_ = new CUART(mst_config_->dev_port_name, DEVICE_UART_BAUD);

    //        mi_clen_machine_->icom_change_interface(mi_icomm_dev_);
    //        m_serial_thread_->device_change_interface(nullptr, mi_clen_machine_);
    //    }

    //* 端口检测
    if (!mi_icomm_->openPort()) {
        mst_dds_->process_item_dds.all_error.serial_open = 1;
        emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        return eFATAL;
    }

    //* varibles init
    varibleInit();


    //* 接收线程
    emit subThreadSignal(true);
    emit readySignal(true);

    return eCOMP;
}

EExecStatus clenEnterDataMesOpt::readyAck(void) {
    return eCOMP;
}

/**
 * @brief 任务开始:抓取镜片
 * @return
 */
EExecStatus clenEnterDataMesOpt::startStep(void) {
    //* 获取工单号信息
    //    mst_work_order_->wo_lot = mst_iniConfig.work_number;
    mst_dds_->mes_dds = mc_sql_handle_->checkWorkOrder(mst_work_order_);

    if (mst_dds_->mes_dds != 0) {
        QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
        emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
        return eFATAL;
    }
    return eCOMP;
}

/**
 * @brief 开始指令反馈
 */
EExecStatus clenEnterDataMesOpt::startAck(void) {
}


EExecStatus clenEnterDataMesOpt::calibMode(void) {
    if (mi_top_board_->unlockModule() && mi_top_board_->modeChange(CSensorCoinD::kTofCalibrationMode)) {
        m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}


EExecStatus clenEnterDataMesOpt::calibModeAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        //      m_serial_thread_->task_id_change(4);
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}


EExecStatus clenEnterDataMesOpt::chipIdStep(void) {
    //* 校正模式
    if (!mi_top_board_->readInfo(CSensorCoinD::kMcuId, 0))
        return eFATAL;
    else {
        m_serial_thread_->task_id_change(3);
        return eOK;
    }
}


EExecStatus clenEnterDataMesOpt::chipIdAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        mst_result_->chip_id = m_origin_bytes.toHex().toUpper();
        m_origin_bytes.clear();

        if (mst_result_->chip_id == "")
            mst_dds_->process_item_dds.all_error.chip_id = 1;
        QString info = QString("chip id:" + mst_result_->chip_id + errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        emit    moduleInfoShowSignal(mst_dds_->process_item_dds.all_error.chip_id, info);

        mst_xpid_det_->mcuid = mst_result_->chip_id;
        //        mst_dds_->process_item_dds.all_error.chip_id = 0;

        if (mst_dds_->process_item_dds.all_error.chip_id)
            return eFATAL;
        else
            return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

EExecStatus clenEnterDataMesOpt::sensorVersionStep(void) {
    //* 校正模式
    if (!mi_top_board_->readInfo(CSensorCoinD::kVersionBuad, 0))
        return eFATAL;
    else {
        m_serial_thread_->task_id_change(3);
        return eOK;
    }
}


EExecStatus clenEnterDataMesOpt::sensorVersionAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        mst_result_->sensor_version = sensorVersionParse(m_origin_bytes);
        m_origin_bytes.clear();

        if (!checkVersion(mst_result_->sensor_version))
            mst_dds_->process_item_dds.all_error.version = 1;  //版本异常如何处理
        //        mst_dds_->process_item_dds.all_error.version = 0;
        QString info =
            QString("version: " + mst_result_->sensor_version + "\n" + errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        emit moduleInfoShowSignal(mst_dds_->process_item_dds.all_error.version, info);

        //* mes connect
        // check work_number status(工单状态)->生成新的工单号
        mst_mes_xsub2_data_->nbr  = "";
        mst_xpid_det_->work_order = mst_work_order_->wo_lot;
        mst_xpid_det_->domain     = mst_work_order_->domain;

        mst_dds_->mes_dds |= mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, true);
        if (mst_dds_->mes_dds != 0) {
            emit moduleInfoShowSignal(true, errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds));
            LOG_ERROR(MyLogger::LogType::ERROR_LOG, QString("mes dds: %1").arg(mst_dds_->mes_dds));
            return eFATAL;
        }
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief greyMapMode
 * @return
 */
EExecStatus clenEnterDataMesOpt::greyMapMode(void) {
    //* 光斑模式
    if (mi_top_board_->unlockModule() && mi_top_board_->modeChange(CSensorCoinD::kTofFaculaMode)) {
        m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}

/**
 * @brief greyMapModeAck
 * @return
 */
EExecStatus clenEnterDataMesOpt::greyMapModeAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(1);
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief 放气后复测
 * @return
 */
EExecStatus clenEnterDataMesOpt::faculaTest(void) {
    //    m_serial_thread_->task_id_change(1);
    return eOK;
}


/**
 * @brief 放气后复测
 * @return
 */
EExecStatus clenEnterDataMesOpt::faculaTestAck(void) {
    static uint8_t facula_test_num          = 0;
    QByteArray     origin_bytes_tmp         = m_origin_bytes;
    mst_result_->finalResult.mp_origin_data = m_origin_bytes;
    QVector<uint32_t>              target_map;
    IFaculaAdjust::UFaculaJudgeDds facula_dds_info;

    //    qDebug() << "-i len enter mes data/ facula test ack step";

    if (mc_facula_context_->faculaTest(m_origin_bytes, facula_dds_info.dds_info, target_map, IFaculaAdjust::eFACUAL_DEFLATE_TEST, false)) {
        mapDataShow();

        mst_result_->finalResult.result &= facula_dds_info.dds_info;
        if (mst_result_->finalResult.result != 0) {
            mst_result_->finalResult.mp_origin_data = origin_bytes_tmp;
        }

        facula_test_num++;
        if (facula_test_num >= mst_iniConfig.facula_ok_times) {
            facula_test_num = 0;
            m_serial_thread_->task_id_change(4);

            emit resultSignal(EResult(!mst_result_->finalResult.result), 0);

            mst_result_->finalResult.map_matrix = mc_facula_context_->getMap();  // save last matrix
            mst_result_->target_map             = target_map;
            return eCOMP;
        }
    }

    return eWAIT;
}

/**
 * @brief clenEnterDataMesOpt::compStep
 */
EExecStatus clenEnterDataMesOpt::compStep(void) {
    QByteArray array;
    return eCOMP;
}

/**
 * @brief 结束
 * @return
 */
EExecStatus clenEnterDataMesOpt::compAck(void) {
    QString     facula;
    static uint delay_cnt = 1000;
    if (mst_dds_->mes_dds == 0 && mst_dds_->process_item_dds.errors == 0 && mst_dds_->process_step_dds == 0) {
        //* 延迟等待新标签生成
        //        if(mst_config_->nbr_flag) {
        while (delay_cnt--) {
            mv_task_list[clenEnterDataMesOpt::eCOMPLETE].flag.stop = true;
            return eWAIT;
        }
        delay_cnt = 1000;

        //******************************************* save data *************************
        //* save local data
        mm_result_data["时间"] =
            QString(QDateTime::currentDateTime().toString("yyyy-MM-dd-hh:mm:ss"));  //时间戳，转速均值，最大值，最小值，最大值偏差，最小值偏差，结果
        mm_result_data["芯片ID"] = mst_result_->chip_id;

        if (mst_result_->finalResult.map_matrix.size() > 0) {
            QString facula_matrix = "";
            for (int for_x = 0; for_x < mst_result_->finalResult.map_matrix.size(); for_x++) {
                for (int for_y = 0; for_y < mst_result_->finalResult.map_matrix.at(0).size(); for_y++) {
                    facula_matrix += QString::number(mst_result_->finalResult.map_matrix.at(for_x).at(for_y), 10) + " ";
                }
            }

            mm_result_data["光斑MP"] = facula_matrix;
        }
        mm_result_data["放气光斑code"] = errorInfoPack(EError_type::eFACULA_JUDGE_ERRPR, mst_result_->finalResult.result);

        mm_result_data["结果"]         = QString(mst_result_->finalResult.result == 0 ? "PASS" : "NG");
        mm_result_data["fatal reason"] = mst_result_->fatal_reason + mm_result_data["放气光斑code"].toString();

        //* save mes data
        if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES
            mesDataHandle(mst_mes_xsub2_data_);
            mst_dds_->mes_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);

            mesDataShow("new mes data: ");

            if (mst_dds_->mes_dds != 0) {
                QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
                emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
            } else {
                emit moduleInfoShowSignal(false, "\n数据录入完毕");
            }
        }

        mm_result_data["事务号"]    = mst_mes_xsub2_data_->trnbr;
        mm_result_data["标签号nbr"] = mst_mes_xsub2_data_->nbr;
        mi_save_file_->writeFile(mm_result_data);

        //* some data update
        //        if(mst_dds_->mes_dds != 0) {
        //            QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
        //            emit moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
        //        }
        //        else {
        //            emit moduleInfoShowSignal(false, "\n数据录入完毕");
        //        }
    } else {
        LOG_INFO(MyLogger::LogType::ERROR_LOG,
                 QString("process item errors: %1, process errors: %2, mes errors: %3")
                     .arg(mst_dds_->process_item_dds.errors)
                     .arg(mst_dds_->process_step_dds)
                     .arg(mst_dds_->mes_dds));
    }

    //* close
    m_serial_thread_->task_id_change(0);
    mi_icomm_->closePort();  // port close

    //    emit subThreadSignal(false); //子线程退出
    emit compAckSignal(true);

    return eCOMP;
}
