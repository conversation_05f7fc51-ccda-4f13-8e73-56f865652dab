#ifndef _TYPE_CONVERT_H_
#define _TYPE_CONVERT_H_

//#include "IDataHandle.h"
#include <QByteArray>
#include <QString>

namespace NsTypeConvert {
//------------------------------------ BASIC CONVERT ------------------
inline const char *toCStr(const QString &str) {
    return str.toLocal8Bit().constData();  // 或 str.toUtf8().constData();
}

inline const char *toCStr(const char *str) {
    return str;
}

inline const char *toCstr(const QByteArray &ba) {
    return ba.constData();
}

template <typename T> inline T toCStr(T value) {
    return value;  // 其他非 QString 类型直接返回
}

void FloatToByte(float floatNum, unsigned char *byteArry);

char ConvertCharToHex(char ch);

void stringToHex(QString str, QByteArray &send_data);

//-------------------------------- QbyteArray ------------------------
QByteArray arrayToByteArray(const uint8_t *input_);

QByteArray enumToByteArray(const uint16_t &input_data);


QByteArray uintArrayToByteArray(uint16_t *input_data_);

QString byteArrayToString(const QByteArray &ba);

};  // namespace NsTypeConvert


#endif
