#!/usr/bin/env python3
"""
产品开发MCP服务器 - 官方FastMCP实现

使用官方MCP Python SDK的FastMCP框架实现，确保完全兼容MCP协议规范。
"""

import asyncio
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List

from mcp.server.fastmcp import FastMCP

# 创建MCP服务器实例
mcp = FastMCP("Product Development")

# 设置脚本基础路径
SCRIPTS_BASE = Path(__file__).parent.parent


@mcp.tool()
def init_project(project_name: str, project_path: str = "", structure_type: str = "single_layer") -> str:
    """
    初始化新的产品项目
    
    Args:
        project_name: 项目名称
        project_path: 项目路径（可选）
        structure_type: 项目结构类型，可选: single_layer, multi_level
    """
    script_path = SCRIPTS_BASE / "init_product_project.py"
    
    if not script_path.exists():
        return f"❌ 脚本不存在: {script_path}"
    
    # 构建命令
    cmd = [sys.executable, str(script_path), "--name", project_name]
    
    if project_path:
        cmd.extend(["--path", project_path])
    
    if structure_type:
        cmd.extend(["--structure-type", structure_type])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            return f"✅ 项目初始化成功\n{result.stdout}"
        else:
            return f"❌ 项目初始化失败\n错误: {result.stderr}\n输出: {result.stdout}"
            
    except Exception as e:
        return f"❌ 执行脚本时出错: {str(e)}"


@mcp.tool()
def generate_all_configs(project_path: str, project_type: str = "", config_types: List[str] = None) -> str:
    """
    生成所有项目配置文件
    
    Args:
        project_path: 项目路径
        project_type: 项目类型（可选）
        config_types: 配置类型列表（可选）
    """
    script_path = SCRIPTS_BASE / "config" / "generate_all_configs.py"
    
    if not script_path.exists():
        return f"❌ 脚本不存在: {script_path}"
    
    # 构建命令
    cmd = [sys.executable, str(script_path), "--project-path", project_path]
    
    if project_type:
        cmd.extend(["--project-type", project_type])
    
    if config_types:
        cmd.append("--config-types")
        cmd.extend(config_types)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            return f"✅ 配置生成成功\n{result.stdout}"
        else:
            return f"❌ 配置生成失败\n错误: {result.stderr}\n输出: {result.stdout}"
            
    except Exception as e:
        return f"❌ 执行脚本时出错: {str(e)}"


@mcp.tool()
def link_documents(project_path: str, enable_register: bool = True, all_docs: bool = False) -> str:
    """
    执行文档关联和注册
    
    Args:
        project_path: 项目路径
        enable_register: 是否注册文档
        all_docs: 是否处理所有文档
    """
    script_path = SCRIPTS_BASE / "links" / "auto_link_documents.py"
    
    if not script_path.exists():
        return f"❌ 脚本不存在: {script_path}"
    
    # 构建命令
    cmd = [sys.executable, str(script_path), "--project-path", project_path]
    
    if enable_register:
        cmd.append("--register")
    
    if all_docs:
        cmd.append("--all")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            return f"✅ 文档关联成功\n{result.stdout}"
        else:
            return f"❌ 文档关联失败\n错误: {result.stderr}\n输出: {result.stdout}"
            
    except Exception as e:
        return f"❌ 执行脚本时出错: {str(e)}"


@mcp.tool()
def sync_canvas(project_path: str, sync_to_canvas: bool = True) -> str:
    """
    同步INDEX文件到Canvas
    
    Args:
        project_path: 项目路径
        sync_to_canvas: 是否同步到Canvas
    """
    script_path = SCRIPTS_BASE / "canvas" / "auto_link_documents.py"
    
    if not script_path.exists():
        return f"❌ 脚本不存在: {script_path}"
    
    # 构建命令
    cmd = [sys.executable, str(script_path), "--project-path", project_path]
    
    if sync_to_canvas:
        cmd.append("--sync-to-canvas")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            return f"✅ Canvas同步成功\n{result.stdout}"
        else:
            return f"❌ Canvas同步失败\n错误: {result.stderr}\n输出: {result.stdout}"
            
    except Exception as e:
        return f"❌ 执行脚本时出错: {str(e)}"


@mcp.resource("project://{project_path}/info")
def get_project_info(project_path: str) -> str:
    """获取项目信息"""
    try:
        project_dir = Path(project_path)
        if not project_dir.exists():
            return f"项目路径不存在: {project_path}"
        
        info = []
        info.append(f"项目路径: {project_path}")
        info.append(f"项目存在: {project_dir.exists()}")
        
        # 检查关键文件
        key_files = [
            "README.md",
            "product_brief.md", 
            "requirements.md",
            ".vscode/tasks.json",
            "config/"
        ]
        
        for file_name in key_files:
            file_path = project_dir / file_name
            info.append(f"{file_name}: {'✅' if file_path.exists() else '❌'}")
        
        return "\n".join(info)
        
    except Exception as e:
        return f"获取项目信息失败: {str(e)}"


@mcp.resource("scripts://status")
def get_scripts_status() -> str:
    """获取脚本状态"""
    try:
        info = []
        info.append(f"脚本基础路径: {SCRIPTS_BASE}")
        
        # 检查关键脚本
        key_scripts = [
            "init_product_project.py",
            "config/generate_all_configs.py",
            "links/auto_link_documents.py", 
            "canvas/auto_link_documents.py"
        ]
        
        for script_name in key_scripts:
            script_path = SCRIPTS_BASE / script_name
            info.append(f"{script_name}: {'✅' if script_path.exists() else '❌'}")
        
        return "\n".join(info)
        
    except Exception as e:
        return f"获取脚本状态失败: {str(e)}"


if __name__ == "__main__":
    # 直接运行MCP服务器
    mcp.run() 