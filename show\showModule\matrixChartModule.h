#ifndef _MATRIX_CHART_MODULE_H
#define _MATRIX_CHART_MODULE_H

#include <QDockWidget>
#include <QImage>
#include <QTableWidget>
#include "IPhotonSensor.h"

class CMatrixChartMod
{
public:
    explicit CMatrixChartMod(QTableWidget* table_, IPhotonSensor::StMapInfo *map_info_);
    ~CMatrixChartMod();

    const QString process_normal_sheetStyle = "QTextEdit{"
                                              "min-width: 300px;"
                                              "min-height: 50px;"
                                              "font: 10pt;" //'Agency FB'
            "border-radius: 6px;"
            "border:2px solid black;"
            "background: rgb(255,255,255);"
            "color: rgb(0, 0, 0);}"; /*浅黑*/

    const QString process_unnormal_sheetStyle = "QTextEdit{"
                                                "min-width: 300px;"
                                                "min-height: 50px;"
                                                "font: 10pt;" // 'Agency FB'
            "border-radius: 6px;"
            "border:2px solid black;"
            "background: rgb(255,255,255);"
            "color: rgb(255, 0, 0);"; /*浅黑*/

//    QTableWidget *m_table_ = nullptr;
    IPhotonSensor::StMapInfo *m_map_info_ = nullptr;

    //* 光斑显示
    void greyMapShow(QTableWidget *map, IPhotonSensor::StMapData *map_data_);

    //* 目标区域凸显

    //* 状态栏update
    void resultClean(void);

private:
    void mp_init(QTableWidget* table_, IPhotonSensor::StMapInfo *map_info_);
};

#endif // CMatrixChartMod_H
