#!/usr/bin/env python3
"""
Simple Hello World MCP Server
基于官方标准协议的最简实现
"""

import asyncio
import json
import sys
import logging
from typing import Any, Sequence

# 配置日志
logging.basicConfig(level=logging.ERROR)

class SimpleMCPServer:
    """简单的 MCP 服务器实现"""
    
    def __init__(self, name: str = "simple-hello-server"):
        self.name = name
        self.version = "1.0.0"
    
    async def handle_message(self, message: dict) -> dict | None:
        """处理收到的消息"""
        method = message.get("method")
        message_id = message.get("id")
        params = message.get("params", {})
        
        if method == "initialize":
            return {
                "jsonrpc": "2.0",
                "id": message_id,
                "result": {
                    "protocolVersion": "2025-03-26",
                    "capabilities": {
                        "tools": {
                            "listChanged": False
                        },
                        "experimental": {}
                    },
                    "serverInfo": {
                        "name": self.name,
                        "version": self.version
                    }
                }
            }
        
        elif method == "initialized":
            # 初始化完成通知，无需响应
            return None
        
        elif method == "tools/list":
            return {
                "jsonrpc": "2.0",
                "id": message_id,
                "result": {
                    "tools": [
                        {
                            "name": "say_hello",
                            "description": "Say hello to someone",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "description": "Name to greet"
                                    }
                                }
                            }
                        },
                        {
                            "name": "get_info",
                            "description": "Get server information",
                            "inputSchema": {
                                "type": "object",
                                "properties": {}
                            }
                        }
                    ]
                }
            }
        
        elif method == "tools/call":
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name == "say_hello":
                name = arguments.get("name", "World")
                result_text = f"Hello, {name}! 🌟 Simple MCP Server is running!"
            elif tool_name == "get_info":
                import platform
                result_text = f"Server: {self.name} v{self.version}\nPython: {sys.version}\nSystem: {platform.system()}"
            else:
                return {
                    "jsonrpc": "2.0",
                    "id": message_id,
                    "error": {
                        "code": -32601,
                        "message": f"Tool not found: {tool_name}"
                    }
                }
            
            return {
                "jsonrpc": "2.0",
                "id": message_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": result_text
                        }
                    ],
                    "isError": False
                }
            }
        
        elif method == "ping":
            return {
                "jsonrpc": "2.0",
                "id": message_id,
                "result": {}
            }
        
        else:
            return {
                "jsonrpc": "2.0",
                "id": message_id,
                "error": {
                    "code": -32601,
                    "message": f"Method not found: {method}"
                }
            }

    async def run_stdio(self):
        """运行 stdio 传输"""
        reader = asyncio.StreamReader()
        protocol = asyncio.StreamReaderProtocol(reader)
        transport, _ = await asyncio.get_event_loop().connect_read_pipe(
            lambda: protocol, sys.stdin
        )
        
        writer_transport, writer_protocol = await asyncio.get_event_loop().connect_write_pipe(
            asyncio.streams.FlowControlMixin, sys.stdout
        )
        writer = asyncio.StreamWriter(writer_transport, writer_protocol, reader, asyncio.get_event_loop())
        
        while True:
            try:
                # 读取消息头
                headers = {}
                while True:
                    line = await reader.readline()
                    if not line:
                        return
                    line = line.decode('utf-8').strip()
                    if not line:
                        break
                    key, value = line.split(': ', 1)
                    headers[key] = value
                
                # 读取消息内容
                content_length = int(headers.get('Content-Length', 0))
                if content_length == 0:
                    continue
                
                content = await reader.readexactly(content_length)
                message = json.loads(content.decode('utf-8'))
                
                # 处理消息
                response = await self.handle_message(message)
                
                # 发送响应
                if response:
                    response_json = json.dumps(response, ensure_ascii=False)
                    response_bytes = response_json.encode('utf-8')
                    
                    header = f"Content-Length: {len(response_bytes)}\r\n\r\n"
                    writer.write(header.encode('utf-8'))
                    writer.write(response_bytes)
                    await writer.drain()
                    
            except Exception as e:
                # 错误日志输出到 stderr
                print(f"Error: {e}", file=sys.stderr)
                break

async def main():
    """主函数"""
    server = SimpleMCPServer()
    await server.run_stdio()

if __name__ == "__main__":
    asyncio.run(main()) 