#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Obsidian Canvas集成模块

该模块提供了Obsidian Canvas与产品文档INDEX系统的集成功能。

主要模块：
- canvas_manager: Canvas文件管理器
- canvas_layout: Canvas布局管理器  
- auto_link_documents: 文档Canvas集成脚本

使用示例：
    from canvas import CanvasManager, CanvasLayoutManager
    
    manager = CanvasManager(project_path)
    layout = CanvasLayoutManager()
"""

from .canvas_manager import CanvasManager
from .canvas_layout import CanvasLayoutManager
from .auto_link_documents import DocumentCanvasIntegrator

__all__ = [
    'CanvasManager',
    'CanvasLayoutManager', 
    'DocumentCanvasIntegrator'
]

__version__ = '1.0.0'
__author__ = 'Product Development Team'
__description__ = 'Obsidian Canvas integration for product documentation system' 