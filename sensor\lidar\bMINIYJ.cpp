#include "bMINIYJ.h"
#include "qLog.h"
#include <QDebug>



CBMiniYJ::CBMiniYJ() {
}

CBMiniYJ::~CBMiniYJ() {
}

/****************************
 * {0xA5, 0x5A, 0x5D, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xA5}; //standby ack
 * {0xA5,0x5A,0x5E,0x07, version, 0x00,0x00,0x00,0x00,0x00,0x00,0xA6}; //running ack
 ***************************/

bool CBMiniYJ::start(const uint8_t &id, const QByteArray &data) {
}


bool CBMiniYJ::stop(const QByteArray &data) {
}


bool CBMiniYJ::changeSpeed(const QByteArray &data) {
}

/**
 * @brief 底板数据解析
 * @param str
 * @param length
 * @return
 */
bool CBMiniYJ::interactionParsing(QByteArray str, const int &length) {
    Q_UNUSED(length);

    /*data output*/
    QString ret;
    for (int i = 0; i < str.count(); ++i) {
        ret.append(QString("0x%1,").arg((quint8)str.at(i), 2, 16, QLatin1Char('0')));
    }
    qDebug() << "-yj-bottom -i: strSum" << ret;

    QByteArray strSum = m_strPre + str;

    if (strSum.length() < 6)  //固定部分长度6
    {
        m_strPre = strSum;
        return false;
    }

    QByteArray        strTmp;  //指令数据
    QVector<uint32_t> data_tmp_v;
    int               i = 0;
    uint16_t          num;
    uint8_t           xor_cal;

    /*1.1 parse*/
    for (i = 0; i < (strSum.length() - 11); ++i)  //留12个数
    {
        if (((uchar)strSum.at(i) == 0xA5) && ((uchar)strSum.at(i + 1) == 0x5A))  //帧头
        {
            switch ((uchar)strSum.at(i + 2)) {  //
            case 0x5D:                          // standby ack
                num     = (uchar)strSum.at(i + 3) + uint16_t((uchar)strSum.at(i + 4) << 8);
                xor_cal = 0;
                if ((strSum.length() - i) >= (num + 5))  //数量符合
                {
                    strTmp.clear();
                    for (uint16_t n = i; n < i + num + 4; ++n) {
                        xor_cal ^= (uchar)strSum.at(n);
                        if (n > (i + 3))
                            strTmp.push_back(strSum.at(n));  //
                    }
                    if (xor_cal == (uchar)strSum.at(i + 11)) {
                        strSum.remove(0, (i + num + 5));
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                        // emit dataOutput(MOTOR_MONITOR::start_step, MOTOR_MONITOR::motor_stop, strTmp);
                        return true;
                    } else {
                        i += 3;

                        //   qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-motor xor: " << xor_cal;
                        // emit dataOutput(MOTOR_MONITOR::start_step, MOTOR_MONITOR::ack_error, strTmp);
                        return true;
                    }
                } else {
                    strSum.remove(0, i);  // i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
                break;
            case 0x5E:  // running ack
                num     = (uchar)strSum.at(i + 3);
                xor_cal = 0;
                if ((strSum.length() - i) >= (num + 5))  //数量符合
                {
                    strTmp.clear();
                    for (uint16_t n = i; n < i + num + 4; ++n) {
                        xor_cal ^= (uchar)strSum.at(n);
                        if (n > (i + 3))
                            strTmp.push_back(strSum.at(n));  //
                    }
                    if (xor_cal == (uchar)strSum.at(i + 11)) {
                        strSum.remove(0, (i + num + 5));
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                        // emit dataOutput(MOTOR_MONITOR::start_step, MOTOR_MONITOR::ack_ok, strTmp);
                        qDebug() << "-yj-bottom-i: running ok";
                        return true;
                    } else {
                        i += 3;
                        //   qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-motor xor: " << xor_cal;
                        // emit dataOutput(MOTOR_MONITOR::start_step, MOTOR_MONITOR::ack_error, strTmp);
                        return true;
                    }
                } else {
                    strSum.remove(0, i);  // i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
                break;
            case 0x50:  // turn on ack 0xA5,0x5A,0x50,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xA8
                num     = (uchar)strSum.at(i + 3);
                xor_cal = 0;
                if ((strSum.length() - i) >= (num + 5))  //数量符合
                {
                    strTmp.clear();
                    for (uint16_t n = i; n < i + num + 4; ++n) {
                        xor_cal ^= (uchar)strSum.at(n);
                        if (n > (i + 3))
                            strTmp.push_back(strSum.at(n));  //
                    }
                    if (xor_cal == (uchar)strSum.at(i + 11)) {
                        strSum.remove(0, (i + num + 5));
                        strTmp.clear();
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                        // emit dataOutput(monitor_process_two::start_step, monitor_process_two::start, strTmp);
                        return true;
                    } else {
                        i += 3;
                        strTmp.clear();
                        //   qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-motor xor: " << xor_cal;
                        // emit dataOutput(monitor_process_two::start_step, monitor_process_two::error, strTmp);
                        return true;
                    }
                } else {
                    strSum.remove(0, i);  // i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
                break;
            case 0x55:  // stop ack 0xA5,0x5A,0x55,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xAD //AbnormalAck
                        // 0xA5,0x5A,0x55,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE9
                num     = (uchar)strSum.at(i + 3) + uint16_t((uchar)strSum.at(i + 4) << 8);
                xor_cal = 0;
                if ((strSum.length() - i) >= (num + 5))  //数量符合
                {
                    strTmp.clear();
                    for (uint16_t n = i; n < i + num + 4; ++n) {
                        xor_cal ^= (uchar)strSum.at(n);
                        if (n > (i + 3))
                            strTmp.push_back(strSum.at(n));  //
                    }
                    if (xor_cal == (uchar)strSum.at(i + 11)) {
                        strSum.remove(0, (i + num + 5));
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                        strTmp.clear();
                        // emit dataOutput(monitor_process_two::adjust_step, monitor_process_two::ok, strTmp);
                        return true;
                    } else if (0xE9 == (uchar)strSum.at(i + 11))  //指令接收异常
                    {
                        strSum.remove(0, (i + num + 5));
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                        strTmp.clear();
                        // emit dataOutput(monitor_process_two::adjust_step, monitor_process_two::error, strTmp);
                        return true;
                    } else {
                        i += 3;
                        strTmp.clear();

                        //   qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-motor xor: " << xor_cal;
                        // emit dataOutput(monitor_process_two::adjust_step, monitor_process_two::error, strTmp);
                    }
                } else {
                    strSum.remove(0, i);  // i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
                break;
            default:  //无效指令
                ++i;  //
                break;
            }  //
        }
    }
    strSum.remove(0, i - 1);
    m_strPre.clear();
    m_strPre.push_back(strSum);  //存储剩余数据
    return true;
}
