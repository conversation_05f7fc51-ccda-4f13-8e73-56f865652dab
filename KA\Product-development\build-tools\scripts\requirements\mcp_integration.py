#!/usr/bin/env python
# 脚本路径: scripts/requirements/mcp_integration.py

import requests
import json
import argparse
import os
import re

def generate_requirements_from_mcp(matrix_path, mcp_url, prompt):
    """使用MCP服务器生成需求"""
    try:
        # 调用MCP API
        response = requests.post(
            mcp_url,
            json={
                "prompt": prompt,
                "mode": "requirements_generation"
            }
        )
        
        if response.status_code != 200:
            print(f"错误: MCP服务器返回状态码 {response.status_code}")
            return False
        
        # 解析返回的需求
        requirements = response.json().get('requirements', [])
        
        if not requirements:
            print("警告: MCP服务器未返回任何需求")
            return False
        
        # 读取现有矩阵
        with open(matrix_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确定需求ID的起始编号
        existing_ids = []
        for line in content.split('\n'):
            if '|' in line:
                parts = line.strip().split('|')
                if len(parts) > 1:
                    id_part = parts[1].strip()
                    if id_part.startswith('L') and '_R' in id_part:
                        existing_ids.append(id_part)
        
        # 提取层级前缀
        if existing_ids:
            level_prefix = existing_ids[0].split('_')[0]
        else:
            # 从文件路径推断层级
            level_match = re.search(r'level_(\d+)', matrix_path)
            if level_match:
                level_prefix = f"L{level_match.group(1)}"
            else:
                level_prefix = "L1"  # 默认值
        
        # 确定新需求ID的起始编号
        req_numbers = [int(id.split('_R')[1]) for id in existing_ids if '_R' in id]
        next_num = max(req_numbers) + 1 if req_numbers else 1
        
        # 查找表格末尾
        table_end = content.rfind('|')
        table_end = content.rfind('\n', 0, table_end) + 1
        
        # 添加新需求
        additions = []
        for i, req in enumerate(requirements):
            req_id = f"{level_prefix}_R{next_num + i}"
            
            # 根据矩阵类型构建行
            if "上层需求" not in content and "测试标准" not in content:
                # 顶层矩阵
                new_row = f"| {req_id} | {req['description']} | 计划中 | |"
            elif "测试标准" in content:
                # 客户层矩阵
                new_row = f"| {req_id} | - | {req['description']} | 计划中 | - |"
            else:
                # 中间层或产品层矩阵
                new_row = f"| {req_id} | - | {req['description']} | 计划中 | |"
            
            additions.append(new_row)
        
        # 更新矩阵
        updated_content = content[:table_end] + '\n'.join(additions) + "\n" + content[table_end:]
        
        with open(matrix_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"已从MCP添加 {len(requirements)} 个需求到 {matrix_path}")
        return True
    
    except Exception as e:
        print(f"错误: MCP集成失败 - {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='从MCP生成需求')
    parser.add_argument('--matrix', required=True, help='需求矩阵文件路径')
    parser.add_argument('--mcp-url', required=True, help='MCP服务器URL')
    parser.add_argument('--prompt', required=True, help='需求生成提示')
    args = parser.parse_args()
    
    success = generate_requirements_from_mcp(args.matrix, args.mcp_url, args.prompt)
    return 0 if success else 1

if __name__ == "__main__":
    exit(main()) 