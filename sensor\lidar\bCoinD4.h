#ifndef _BCOIN_D4_H_
#define _BCOIN_D4_H_

#include "IBottom.h"


class CBcoinD4:public IBottom{
public:
  CBcoinD4(IComm *port_);
  ~CBcoinD4();

  QByteArray portDataRead(void) override;

  void icom_change_interface(IComm *port_) override;
  bool readInfo(const uint8_t &id, const uint16_t &data) override;
  bool start(const uint8_t &id, const QByteArray &data) override;
  bool stop(const QByteArray &data) override;
  bool changeSpeed(const QByteArray &data) override;
  bool interactionParsing(QByteArray str, const int &length) override;
private:
  IComm *im_port_ = nullptr;
  QByteArray m_str_send; //指令数据

  void cmd_init();
};

#endif
