# 文档关系可视化实施方案

## 1. 概述

本方案旨在以Markdown文档为基础，构建项目知识图谱并可视化文档间的语义关联。我们聚焦于落地可运行的基础功能：通过文本嵌入和向量检索分析Markdown文件之间的语义关联，自动生成Foam双链 `[[wiki-link]]`，并利用图谱视图展示文档关系网络。所有处理均在本地完成，无需云服务，确保数据私密可控。类似开源工具Basic Memory所倡导的思路，本方案将知识以Markdown文件形式持久化管理。

## 2. 系统架构

### 2.1 三层轻量化架构

[[产品文档关联可视化实施方案]]

## 3. 功能详述

### 3.1 文档扫描与语义索引

```python
from sentence_transformers import SentenceTransformer
import hnswlib
import os
import re
import json
from pathlib import Path

def scan_markdown_files(workspace_path):
    """扫描工作区内所有Markdown文件"""
    md_files = list(Path(workspace_path).glob("**/*.md"))
    return [str(file) for file in md_files]

def extract_document_info(file_path):
    """提取文档标题和内容"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取标题 - 优先使用第一个H1标题，否则使用文件名
    title_match = re.search(r'^# (.+)$', content, re.MULTILINE)
    title = title_match.group(1) if title_match else Path(file_path).stem
    
    return {
        "path": file_path,
        "title": title,
        "content": content
    }

def build_document_embeddings(documents):
    """为文档构建向量表示"""
    # 加载预训练模型
    model = SentenceTransformer('all-MiniLM-L6-v2')
    
    # 提取清洗后的文本内容
    texts = [clean_content(doc["content"]) for doc in documents]
    
    # 计算嵌入向量
    embeddings = model.encode(texts, show_progress_bar=True)
    
    # 保存文档-向量映射
    for i, doc in enumerate(documents):
        doc["embedding"] = embeddings[i]
    
    return documents, embeddings

def build_hnsw_index(embeddings, documents):
    """构建HNSW索引"""
    # 索引维度与向量维度一致
    dim = embeddings.shape[1]
    
    # 创建索引
    index = hnswlib.Index(space='cosine', dim=dim)
    index.init_index(max_elements=len(embeddings), ef_construction=200, M=16)
    index.add_items(embeddings)
    
    # 设置查询参数
    index.set_ef(50)  # 影响查询质量
    
    # 保存索引(可选)
    index.save_index("document_index.bin")
    
    # 保存文档映射
    with open("document_map.json", "w", encoding="utf-8") as f:
        # 移除embedding字段后保存(避免JSON序列化问题)
        save_docs = [{k: v for k, v in doc.items() if k != 'embedding'} 
                     for doc in documents]
        json.dump(save_docs, f, ensure_ascii=False, indent=2)
    
    return index

def clean_content(content):
    """清理文档内容，移除代码块、注释等"""
    # 移除代码块
    content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)
    # 移除HTML注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    # 移除过多空白字符
    content = re.sub(r'\n{3,}', '\n\n', content)
    return content
```

### 3.2 文档关联与双链生成

```python
def find_related_documents(index, query_embedding, documents, top_k=5, threshold=0.6):
    """查找与查询文档相关的文档"""
    # 搜索最相似的文档
    labels, distances = index.knn_query(query_embedding.reshape(1, -1), k=min(top_k+1, len(documents)))
    
    # 筛选结果（排除自身）
    related_docs = []
    for idx, dist in zip(labels[0], distances[0]):
        sim = 1 - dist  # 将cosine距离转换为相似度
        if sim >= threshold and idx < len(documents):
            related_docs.append({
                "document": documents[idx],
                "similarity": sim
            })
    
    # 按相似度排序
    related_docs.sort(key=lambda x: x["similarity"], reverse=True)
    
    return related_docs

def generate_foam_links(document, related_docs):
    """为文档生成Foam双链链接"""
    # 生成链接文本
    links = []
    for rel in related_docs:
        target_doc = rel["document"]
        similarity = rel["similarity"]
        
        # 计算相对路径
        source_dir = os.path.dirname(document["path"])
        target_path = os.path.relpath(target_doc["path"], source_dir)
        # 移除.md扩展名
        target_path = os.path.splitext(target_path)[0]
        
        # 生成链接(Foam格式)
        link_text = f"- [[{target_path}]] - 相似度: {similarity:.2f}"
        links.append(link_text)
    
    return links

def update_document_with_links(doc_path, links):
    """更新文档，添加或更新关联链接"""
    with open(doc_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 关联章节标记
    start_marker = "## 自动关联\n<!-- 自动关联开始 -->"
    end_marker = "<!-- 自动关联结束 -->"
    
    # 构建关联章节内容
    links_section = f"{start_marker}\n\n"
    if links:
        links_section += "\n".join(links)
    else:
        links_section += "暂无相关文档"
    links_section += f"\n\n{end_marker}"
    
    # 检查是否已有关联章节
    if start_marker in content and end_marker in content:
        # 替换现有章节
        pattern = f"{re.escape(start_marker)}.*?{re.escape(end_marker)}"
        content = re.sub(pattern, links_section, content, flags=re.DOTALL)
    else:
        # 添加到文档末尾
        if not content.endswith('\n'):
            content += '\n'
        content += f"\n{links_section}\n"
    
    # 写回文件
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(content)
```

### 3.3 知识图谱构建

```python
import networkx as nx
import matplotlib.pyplot as plt
from pathlib import Path

def build_knowledge_graph(documents, relationships):
    """构建文档关系知识图谱"""
    # 创建图
    G = nx.Graph()
    
    # 添加节点
    for doc in documents:
        G.add_node(doc["title"], path=doc["path"], type="document")
    
    # 添加边
    for rel in relationships:
        source = rel["source"]
        target = rel["target"]
        similarity = rel["similarity"]
        G.add_edge(source, target, weight=similarity)
    
    return G

def export_graph_visualization(graph, output_path="knowledge_graph.png"):
    """导出知识图谱可视化"""
    plt.figure(figsize=(12, 9))
    
    # 计算节点大小(基于度中心性)
    degrees = dict(nx.degree(graph))
    node_size = [v * 100 for v in degrees.values()]
    
    # 计算节点颜色(可基于文档类型)
    node_color = ["skyblue" for _ in graph.nodes()]
    
    # 计算边宽度(基于相似度)
    edge_width = [graph[u][v].get("weight", 0.5) * 2 for u, v in graph.edges()]
    
    # 使用spring布局
    pos = nx.spring_layout(graph, seed=42)
    
    # 绘制图
    nx.draw_networkx(
        graph, pos, 
        node_size=node_size,
        node_color=node_color,
        width=edge_width,
        edge_color="gray",
        alpha=0.8,
        with_labels=True,
        font_size=10
    )
    
    # 保存图片
    plt.tight_layout()
    plt.axis("off")
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    return output_path

def export_graphml(graph, output_path="knowledge_graph.graphml"):
    """导出为GraphML格式，供其他工具使用"""
    nx.write_graphml(graph, output_path)
    return output_path

def generate_mermaid_diagram(graph):
    """生成Mermaid格式的图表，可嵌入Markdown"""
    mermaid = ["```mermaid", "graph TD"]
    
    # 添加节点
    for node in graph.nodes():
        node_id = sanitize_id(node)
        mermaid.append(f'    {node_id}["{node}"]')
    
    # 添加边
    for u, v, data in graph.edges(data=True):
        u_id = sanitize_id(u)
        v_id = sanitize_id(v)
        weight = data.get("weight", 0.5)
        # 根据权重确定线条样式
        style = "==>" if weight > 0.8 else "-->"
        mermaid.append(f"    {u_id} {style} {v_id}")
    
    mermaid.append("```")
    return "\n".join(mermaid)

def sanitize_id(text):
    """清理文本用作Mermaid图表ID"""
    return re.sub(r'[^a-zA-Z0-9]', '_', text)
```

### 3.4 VSCode Foam集成

通过VSCode任务系统集成文档关联和可视化功能：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "更新文档关联关系",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/update_document_links.py",
        "--workspace=${workspaceFolder}",
        "--threshold=0.6"
      ],
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    },
    {
      "label": "生成知识图谱",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/generate_knowledge_graph.py", 
        "--workspace=${workspaceFolder}",
        "--output=${workspaceFolder}/reports/knowledge_graph.png"
      ],
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    }
  ]
}
```

### 3.5 主脚本实现

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文档关联与知识图谱生成工具
"""

import argparse
import os
from pathlib import Path
import json
import time

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文档关联与知识图谱生成工具")
    parser.add_argument("--workspace", required=True, help="工作区路径")
    parser.add_argument("--threshold", type=float, default=0.6, help="相似度阈值")
    parser.add_argument("--top-k", type=int, default=5, help="每个文档的相关文档数量")
    parser.add_argument("--output-dir", default="reports", help="输出目录")
    args = parser.parse_args()
    
    print(f"开始处理工作区: {args.workspace}")
    start_time = time.time()
    
    # 创建输出目录
    output_dir = Path(args.workspace) / args.output_dir
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # 1. 扫描Markdown文件
    print("扫描Markdown文件...")
    md_files = scan_markdown_files(args.workspace)
    print(f"找到 {len(md_files)} 个Markdown文件")
    
    # 2. 提取文档信息
    print("提取文档信息...")
    documents = []
    for file_path in md_files:
        try:
            doc = extract_document_info(file_path)
            documents.append(doc)
        except Exception as e:
            print(f"处理文件时出错 {file_path}: {e}")
    
    # 3. 构建文档嵌入和索引
    print("构建文档嵌入...")
    documents, embeddings = build_document_embeddings(documents)
    
    print("构建向量索引...")
    index = build_hnsw_index(embeddings, documents)
    
    # 4. 查找文档关联并更新链接
    print("生成文档关联...")
    relationships = []
    for i, doc in enumerate(documents):
        print(f"处理文档 {i+1}/{len(documents)}: {doc['title']}")
        # 查找相关文档
        related = find_related_documents(
            index, 
            doc["embedding"], 
            documents, 
            top_k=args.top_k, 
            threshold=args.threshold
        )
        
        # 排除自身
        related = [r for r in related if r["document"]["path"] != doc["path"]]
        
        # 记录关系
        for rel in related:
            relationships.append({
                "source": doc["title"],
                "target": rel["document"]["title"],
                "source_path": doc["path"],
                "target_path": rel["document"]["path"],
                "similarity": rel["similarity"]
            })
        
        # 生成Foam链接
        if related:
            links = generate_foam_links(doc, related)
            # 更新文档
            update_document_with_links(doc["path"], links)
    
    # 5. 构建和导出知识图谱
    print("构建知识图谱...")
    graph = build_knowledge_graph(documents, relationships)
    
    # 导出可视化
    print("导出知识图谱可视化...")
    graph_path = export_graph_visualization(
        graph, 
        output_path=str(output_dir / "knowledge_graph.png")
    )
    
    # 导出GraphML
    graphml_path = export_graphml(
        graph, 
        output_path=str(output_dir / "knowledge_graph.graphml")
    )
    
    # 保存关系数据
    with open(output_dir / "document_relationships.json", "w", encoding="utf-8") as f:
        json.dump(relationships, f, ensure_ascii=False, indent=2)
    
    # 生成Mermaid图并保存
    mermaid = generate_mermaid_diagram(graph)
    with open(output_dir / "knowledge_graph.md", "w", encoding="utf-8") as f:
        f.write("# 项目知识图谱\n\n")
        f.write(mermaid)
    
    elapsed = time.time() - start_time
    print(f"完成! 处理时间: {elapsed:.2f}秒")
    print(f"已处理 {len(documents)} 个文档，找到 {len(relationships)} 个关联关系")
    print(f"知识图谱已保存至: {graph_path}")
    print(f"GraphML已保存至: {graphml_path}")
    
    return 0

if __name__ == "__main__":
    exit(main())
```

## 4. 部署指南

### 4.1 环境准备

1. **基本要求**:
   - 内存: 8GB以上
   - 磁盘: 至少1GB可用空间
   - 操作系统: 跨平台支持(Windows/macOS/Linux)

2. **软件依赖**:
   - Python 3.8+
   - VSCode编辑器
   - Foam VSCode插件

### 4.2 安装步骤

```bash
# 创建项目目录
mkdir -p document-visualization/scripts
cd document-visualization

# 创建Python虚拟环境
python -m venv venv
source venv/bin/activate  # Windows使用: venv\Scripts\activate

# 安装依赖
pip install sentence-transformers hnswlib networkx matplotlib

# 创建初始目录结构
mkdir -p reports
```

### 4.3 VSCode配置

1. 打开VSCode并安装插件：
   - Foam
   - Markdown All in One
   - Mermaid Preview (可选)

2. 配置VSCode设置：
   - 添加Foam配置到settings.json

```json
// .vscode/settings.json
{
  "foam.openDailyNote.directory": "journal",
  "foam.openDailyNote.titleFormat": "yyyy-mm-dd",
  "foam.openDailyNote.filenameFormat": "yyyy-mm-dd",
  "foam.edit.linkReferenceDefinitions": "withoutExtensions",
  "foam.graph.style": {
    "backgroundColor": "#ffffff",
    "fontSize": 12,
    "highlightedForeground": "#f9c74f",
    "node": {
      "note": "#277da1",
      "requirement": "#d62828",
      "design": "#4d908e",
      "development": "#277da1",
      "test": "#43aa8b"
    }
  }
}
```

## 5. 使用指南

### 5.1 启动知识图谱系统

1. 打开VSCode并打开工作区文件夹
2. 打开命令面板(Ctrl+Shift+P)
3. 运行任务"更新文档关联关系"

### 5.2 查看知识图谱

1. 打开命令面板(Ctrl+Shift+P)
2. 输入"Foam: Show Graph"
3. 在图谱视图中查看文档网络

### 5.3 浏览文档关联

- 在文档底部查看"自动关联"部分
- 点击双链跳转到相关文档
- 使用Foam导航查看文档网络

## 6. 拓展规划

### 6.1 功能扩展方向

1. **文档类型识别**：自动识别不同类型文档(需求、设计、开发等)
2. **关系类型推断**：分析文档内容判断关系类型(依赖、引用等)
3. **交互式图谱**：开发Web界面展示可交互知识图谱
4. **语义检索**：实现基于语义的文档检索功能
5. **版本跟踪**：记录文档关系随时间变化情况

### 6.2 结合ChatGPT增强功能

1. **智能总结生成**：对关联文档生成摘要
2. **关系分析增强**：更精准地识别文档间关系类型
3. **查询问答**：基于知识图谱回答关于项目的问题
