@echo off
echo Creating new document structure...

REM Create inputs subdirectories
mkdir "inputs\requirements\functional" 2>nul
mkdir "inputs\requirements\performance" 2>nul
mkdir "inputs\requirements\integration" 2>nul
mkdir "inputs\feedback\functional" 2>nul
mkdir "inputs\feedback\performance" 2>nul
mkdir "inputs\feedback\usability" 2>nul
mkdir "inputs\issues\bugs" 2>nul
mkdir "inputs\issues\errors" 2>nul

REM Create internal subdirectories
mkdir "internal\analysis\requirements" 2>nul
mkdir "internal\analysis\feasibility" 2>nul
mkdir "internal\analysis\impact" 2>nul
mkdir "internal\design\architecture" 2>nul
mkdir "internal\design\algorithms" 2>nul
mkdir "internal\design\interface" 2>nul
mkdir "internal\development\specifications" 2>nul
mkdir "internal\development\implementation" 2>nul
mkdir "internal\development\code_review" 2>nul
mkdir "internal\testing\plans" 2>nul
mkdir "internal\testing\cases" 2>nul
mkdir "internal\testing\reports" 2>nul

REM Create outputs subdirectories
mkdir "outputs\manuals\installation" 2>nul
mkdir "outputs\manuals\operation" 2>nul
mkdir "outputs\manuals\configuration" 2>nul
mkdir "outputs\releases\notes" 2>nul
mkdir "outputs\releases\changelogs" 2>nul
mkdir "outputs\reports\quality" 2>nul
mkdir "outputs\reports\performance" 2>nul

REM Create support subdirectories (support already exists, just add subdirs)
mkdir "support\faq\installation" 2>nul
mkdir "support\faq\operation" 2>nul
mkdir "support\faq\troubleshooting" 2>nul
mkdir "support\knowledge\best_practices" 2>nul
mkdir "support\knowledge\tips" 2>nul
mkdir "support\training\tutorials" 2>nul

echo Directory structure created successfully!
