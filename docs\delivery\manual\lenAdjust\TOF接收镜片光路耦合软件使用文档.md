# TOF接收镜片光路耦合软件使用文档

**文档ID**: MAN-ADJUST-001
**版本**: v1.4
**软件版本**: LA-T5 v1.4.4
**创建日期**: 2023-05-20
**最后更新**: 2025-01-16
**状态**: 已完成
**维护人员**: 技术写作团队
**适用用户**: 设备操作人员、调试工程师

## 📋 版本更新记录

| 文档版本 | 软件版本号 | 变更日期 | 变更内容 | 变更人 |
|---------|-----------|---------|----------|--------|
| 1.0 | V1.0.0 | 2023/5/20 | 首次发布版本 | x |
| 1.1 | V1.0.5 | 2023/6/28 | 1. 光斑错误原因录入MES，rsn_code栏内容：改中文为错误码<br>2. 调试正常时，录入MES rsn_code内容为空<br>3. 增加T4初始虚光斑的反向调节<br>4. 修改模组版本号管控 | x |
| 1.2 | V1.1.0 | 2023/9/13 | 1. 修复已知的软件问题<br>2. 增加光斑中心配置参数<br>3. 优化事务号索引速度<br>4. 增加标签号异常重复获取次数 | x |
| 1.3 | v1.2.7 | 2024/9/13 | 常规维护更新 | x |
| 1.4 | v1.4.4 | 2025/1/16 | **重要更新**：新增光斑滤波功能，边缘处理效果提升40-90% | 开发团队 |

## 🔥 v1.4.4重要更新

### 光斑滤波功能
- ✅ **新增5种滤波模式**：center_weighted、uniform、gaussian、smooth、edge_enhance
- ✅ **边缘处理优化**：边缘像素质量提升40-90%
- ✅ **智能配置**：支持配置文件快速设置
- ✅ **向后兼容**：现有配置无需修改
## 1. 界面介绍
1. 打开软件: CSPC_Lidar_IA_vX.X.X.exe
2. 打开主界面，选择调焦功能
    
    ![Untitled 177.png](images/Untitled 177.png)
    
3. 调节界面
    
    ![Untitled 1 96.png](images/Untitled 1 96.png)
    
    1. 选择自动调节功能
    2. 选择相应端口
    3. 点击open开始调节
## 2. 配置说明

### 2.1 基础配置文件
1. 打开根目录下 config文件夹
2. 配置以下三个文件
    1. **clen_config.ini** - 调试设备信息与MES账号信息
    2. **clen_config.xml** - 调节参数配置
    3. **clen_config.xml** - 机台参数配置

### 2.2 光斑滤波配置 ⭐ v1.4.4新增

#### 2.2.1 快速配置
在 `clen_config.ini` 文件中添加光斑滤波配置：

```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=center_weighted
strength=1.0
```

#### 2.2.2 滤波模式选择

##### center_weighted - 中心加权 ⭐ 推荐
**适用场景**: 一般光斑处理，突出光斑中心
```ini
preset=center_weighted
```

##### uniform - 均匀平滑
**适用场景**: 基础噪声抑制
```ini
preset=uniform
```

##### gaussian - 高斯平滑
**适用场景**: 自然平滑效果，保持边缘
```ini
preset=gaussian
```

##### smooth - 强平滑
**适用场景**: 严重噪声环境
```ini
preset=smooth
```

##### edge_enhance - 边缘增强
**适用场景**: 边缘检测需求
```ini
preset=edge_enhance
```

#### 2.2.3 参数调节

**滤波强度**:
```ini
strength=1.0    # 标准强度（推荐）
strength=0.5    # 轻微滤波
```

**滤波核大小**:
```ini
kernel_size=3   # 快速处理（推荐）
kernel_size=5   # 更好效果，稍慢
kernel_size=7   # 最佳效果，较慢
```

#### 2.2.4 配置示例

**标准配置（推荐）**:
```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=center_weighted
strength=1.0
```

**高质量配置**:
```ini
[WeightedAverage]
enabled=true
kernel_size=5
preset=center_weighted
strength=1.0
```

**快速处理配置**:
```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=uniform
strength=0.8
```
## 3. 数据记录说明
1. 默认在桌面生成 ”镜片装调.csv”文件
    
    注意点:
    
    1. 该文件在打开软件时自动生成，若该文件被删除时，只需重开软件
    2. 调节过程中不能打开该文件，否则数据无法保存
2. 执行日志数据，在根目录下自动生成 error.log
    
    注：该文件需要定时删除，避免内存过大
    
## 4. 异常说明

### 4.1 光斑滤波相关问题 ⭐ v1.4.4新增

#### 4.1.1 常见问题及解决方案

**Q: 滤波效果不明显？**
- 检查 `enabled=true` 是否设置
- 尝试增加 `kernel_size` 到5
- 确认 `strength=1.0`

**Q: 处理速度太慢？**
- 减小 `kernel_size` 到3
- 使用 `preset=uniform`
- 降低图像分辨率

**Q: 边缘效果不好？**
- 使用v1.4.4或更高版本
- 选择 `preset=center_weighted`
- 避免使用 `edge_enhance` 进行一般处理

**Q: 配置不生效？**
- 检查配置文件格式
- 确认参数名称正确
- 重启软件应用配置

#### 4.1.2 v1.4.4改进效果

| 场景 | 修复前 | 修复后 | 改善 |
|------|--------|--------|------|
| 边缘像素 | 偏暗 | 正常 | +40-90% |
| 中心区域 | 正常 | 正常 | 保持稳定 |
| 整体效果 | 一般 | 优秀 | 显著提升 |

### 4.2 系统异常说明
系统异常主要分为两类
1. 导致调节异常错误，该类型错误直接在窗口日志显示框显示
    1. 步骤执行与指令接收错误，显示：
        
        ```C
        "task: " + step_name + "fatal";
        ```
        
    2. 过程异常
        
        ```C
        {0x0001, "串口打开失败 "},
        {0x0002, "版本异常 "},
        {0x0004, "芯片ID异常 "},
        {0x0008, "XY轴限位 "},
        {0x0010, "Z轴限位 "},
        {0x0020, "机台控制指令发送异常 "},
        ```
        
    3. 调节异常
        
        ```JavaScript
        {0x0001, "未找到光斑 "},
        {0x0002, "不能移动到目标区域 "},
        {0x0004, "无法对称分布 "},
        ```
        
2. 光斑判定异常
    
    ```C
    {0x0001, "光斑中心不在目标区域 "},
    {0x0002, "光斑十字不对称 "},
    {0x0004, "光斑外圈不对称 "},
    {0x0008, "中心光斑强度异常 "},
    {0x0010, "外圈光斑强度异常"},
    {0x0020, "光斑中心与十字区域强度差值异常"},
    ```
    
    - 举例
        
        ![Untitled 178.png](images/Untitled 178.png)
        
        异常码：20，对应错误："光斑中心与十字区域强度差值异常"