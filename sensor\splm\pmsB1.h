#ifndef _PMS_B1_H_
#define _PMS_B1_H_

#include "IPms.h"
#include "IComm.h"


class CPmsB1:public IPms{
  Q_OBJECT
public:
    CPmsB1(IComm *port_);
    ~CPmsB1();

    enum EProtocolId {
        eCROSS_TALK     = 0x01, //cross talk
        eREF            = 0x02, //ref

        eFPS_COUNTS     = 0x20, //
    };

    /*模式与枚举*/
    enum EMode{
    };

    QByteArray portDataRead(void) override;
    void icom_change_interface(IComm *port_) override;
//    bool modeChange(const EModeType &mode) override;
    bool modeChange(const uint16_t &mode) override;
    bool changeRigster() override;
    bool readInfo(const uint8_t &id, const uint16_t &data) override;

    EExecStatus calibTasksRun() override;
//    void calibTasksInit() override;
    EExecStatus verifyTasksRun() override;
//    void verifyTasksInit() override;

    bool interactionParsing(QByteArray str, int length) override;
    bool dataParsing(QByteArray str, int length) override;

private:
    IComm *im_port_ = nullptr;
    QByteArray m_str_send; //指令数据

    uint8_t m_version;
//    uint16_t m_healthCode, m_mcuVoltage;

    void cmd_init();

signals:
    void dataOutput(EProtocolId step, ECommStatus status, QByteArray bytes);

};



#endif
