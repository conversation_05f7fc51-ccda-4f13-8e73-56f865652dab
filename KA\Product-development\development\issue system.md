## 一、项目初始化与模板拷贝
1. 在脚本中引入 Issue 模板  
脚本位置：在你的项目脚手架中，增加拷贝命令：
```bash
cp templates/ISSUE_TEMPLATE.md docs/ISSUES.md
```

2. 可选：为每个 Issue 创建单独文件  
主文档格式示例：
```markdown
| ID        | 时间            | 模块     | 状态  | 链接                 |
|-----------|-----------------|----------|-------|----------------------|
| ISSUE-001 | 2025-05-15 09:00| 通信驱动 | 新建  | [详细](issues/ISSUE-001.md) |
```
注：
+ 状态：新建 → 处理中 → 已解决/已关闭
+ 子文档需包含：
  - 错误现象
  - 排查过程  
  - 根本原因
  - 修复方案
  - 测试验证
## 二、通过 Cline + Filesystem MCP Server 进行动态更新
2. AI 生成 MCP 调用：
```xml
<use_mcp_tool>
  <server_name>filesystem-mcp</server_name>
  <tool_name>append_content</tool_name>
  <arguments>
    {
      "path":"docs/ISSUES.md",
      "content":"| ISSUE-002 | 2025-05-15 10:30 | 电源管理 | 新建 | [详细](issues/ISSUE-002.md) |\n"
    }
  </arguments>
</use_mcp_tool>
```

## 三、自动检索并链接外部资源
3. 将链接插入 Issue 文档：
```xml
<use_mcp_tool>
  <server_name>filesystem-mcp</server_name>
  <tool_name>append_content</tool_name>
  <arguments>{
    "path":"docs/issues/ISSUE-002.md",
    "content":"\n**相关讨论**：\n- [Why ARM reset circuit causes instability? (SO)](https://stackoverflow.com/…)\n- …\n"
  }</arguments>
</use_mcp_tool>