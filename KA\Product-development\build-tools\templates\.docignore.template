# 文档扫描忽略规则模板 - 类似 .gitignore 的语法
# 
# 使用说明：
# 1. 复制此文件到项目根目录并重命名为 .docignore
# 2. 根据项目需求修改规则
# 3. 文档扫描器会自动使用这些规则
#
# 语法说明：
# - 每行一个模式
# - # 开头的行是注释
# - 支持通配符 * 和 **
# - 使用 ! 前缀来包含被排除的文件
# - 以 / 结尾表示目录
# - 以 / 开头表示从根目录开始的绝对路径

# ==================== 全局排除规则 ====================

# 系统文件和目录
.git/
.svn/
.vscode/
.idea/
.vs/

# 编译和构建产物
__pycache__/
node_modules/
.venv/
venv/
build/
dist/
target/
bin/
obj/

# 临时文件
*.tmp
*.temp
*.bak
*.swp
*.swo
~$*
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# INDEX文件（由系统自动生成）
*INDEX.md
*_INDEX.md

# ==================== 开发目录特殊规则 ====================

# 排除所有源代码文件
development/**/*.c
development/**/*.h
development/**/*.cpp
development/**/*.hpp
development/**/*.cc
development/**/*.cxx
development/**/*.c++

# Python源码
development/**/*.py
development/**/*.pyc
development/**/*.pyo
development/**/*.pyd

# JavaScript/TypeScript源码
development/**/*.js
development/**/*.ts
development/**/*.jsx
development/**/*.tsx

# Java源码
development/**/*.java
development/**/*.class
development/**/*.jar

# C#源码
development/**/*.cs
development/**/*.dll
development/**/*.exe

# Go源码
development/**/*.go

# Rust源码
development/**/*.rs

# PHP源码
development/**/*.php

# 汇编和固件文件
development/**/*.asm
development/**/*.s
development/**/*.S
development/**/*.hex
development/**/*.bin
development/**/*.elf

# 项目文件
development/**/*.vcproj
development/**/*.vcxproj
development/**/*.sln
development/**/*.pro
development/**/*.pri
development/**/*.cmake
development/**/CMakeLists.txt
development/**/Makefile
development/**/makefile

# 但是包含文档目录下的所有文件
!development/**/docs/**
!development/**/doc/**
!development/**/documentation/**

# 包含散落的文档文件
!development/**/*.md
!development/**/*.txt
!development/**/*.pdf
!development/**/*.doc
!development/**/*.docx
!development/**/*.xls
!development/**/*.xlsx
!development/**/*.ppt
!development/**/*.pptx

# ==================== 其他组件特殊规则 ====================

# 质量组件：包含测试相关文件
!quality/**/*.feature
!quality/**/*.spec
!quality/**/*.test
!quality/**/*.robot

# 生产组件：排除中间文件和临时文件
production/**/intermediate/
production/**/temp/
production/**/cache/

# 需求组件：包含所有文档
# （默认已包含，无需特殊规则）

# 设计组件：包含所有设计文档
# （默认已包含，无需特殊规则）

# ==================== 自定义规则区域 ====================

# 在此处添加项目特定的规则
# 例如：
# my_special_directory/
# !my_special_directory/important.md

# ==================== 调试规则 ====================

# 取消注释以下行来调试特定文件
# 这些规则会强制包含指定文件，用于测试
# !debug_test.txt
# !test/sample.md
