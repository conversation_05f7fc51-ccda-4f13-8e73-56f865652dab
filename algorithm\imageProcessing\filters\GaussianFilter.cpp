#include "GaussianFilter.h"

namespace ImageProcessing {

GaussianFilter::GaussianFilter() {
    // 设置默认参数
    params_.strength = 1.0f;
    params_.enabled = true;
    params_.sigma = 1.0f;
    params_.kernelSize = 5;
    
    // 生成默认高斯核
    generateGaussianKernel();
    
    logDebug("GaussianFilter initialized with sigma=1.0, kernelSize=5");
}

bool GaussianFilter::apply(ImageDataU32& data) {
    try {
        validateInput(data);
        
        if (!params_.enabled) {
            logDebug("Filter disabled, skipping processing");
            return true;
        }
        
        logDebug(QString("Applying Gaussian filter to %1x%2 image with sigma=%3, kernelSize=%4")
                .arg(data.width()).arg(data.height()).arg(params_.sigma).arg(params_.kernelSize));
        
        // 创建临时图像存储结果
        ImageDataU32 temp(data.width(), data.height());
        
        // 对每个像素应用高斯滤波
        for (uint32_t y = 0; y < data.height(); ++y) {
            for (uint32_t x = 0; x < data.width(); ++x) {
                float gaussianResult = applyGaussianAtPixel(data, x, y);
                
                // 应用滤波强度
                float originalValue = static_cast<float>(data.matrix()[y][x]);
                float filteredValue = originalValue + params_.strength * (gaussianResult - originalValue);
                
                temp.matrix()[y][x] = safeFloatToUint32(filteredValue);
            }
        }
        
        // 复制结果回原图像
        data = std::move(temp);
        
        logDebug("Gaussian filter applied successfully");
        return true;
        
    } catch (const ProcessingException& e) {
        qWarning() << "GaussianFilter::apply failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "GaussianFilter::apply failed:" << e.what();
        return false;
    }
}

bool GaussianFilter::apply(const ImageDataU32& src, ImageDataU32& dst) {
    try {
        validateInput(src);
        
        // 确保目标图像尺寸正确
        if (dst.width() != src.width() || dst.height() != src.height()) {
            dst.resize(src.width(), src.height());
        }
        
        // 复制源数据到目标
        dst = src;
        
        // 应用滤波
        return apply(dst);
        
    } catch (const ProcessingException& e) {
        qWarning() << "GaussianFilter::apply (src->dst) failed:" << e.qMessage();
        return false;
    } catch (const std::exception& e) {
        qWarning() << "GaussianFilter::apply (src->dst) failed:" << e.what();
        return false;
    }
}

void GaussianFilter::setParameters(const FilterParams& params) {
    const GaussianParams* gaussianParams = dynamic_cast<const GaussianParams*>(&params);
    if (!gaussianParams) {
        throw InvalidParameterException("params", "must be GaussianParams type");
    }
    
    validateGaussianParams(*gaussianParams);
    params_ = *gaussianParams;
    
    // 重新生成高斯核
    generateGaussianKernel();
    
    logDebug("Parameters updated: " + params_.toString());
}

std::unique_ptr<FilterParams> GaussianFilter::getParameters() const {
    return std::make_unique<GaussianParams>(params_);
}

QString GaussianFilter::getAlgorithmName() const {
    return "GaussianFilter";
}

QString GaussianFilter::getDescription() const {
    return "Gaussian filter for smooth blurring with natural appearance, effective for noise reduction";
}

bool GaussianFilter::isSupported(uint32_t width, uint32_t height) const {
    try {
        ValidationUtils::validatePositive(width, "width");
        ValidationUtils::validatePositive(height, "height");
        
        // 检查图像是否足够大以应用滤波核
        uint32_t minSize = static_cast<uint32_t>(params_.kernelSize);
        return width >= minSize && height >= minSize;
    } catch (const ProcessingException&) {
        return false;
    }
}

uint32_t GaussianFilter::estimateProcessingTime(uint32_t width, uint32_t height) const {
    // 高斯滤波复杂度与核大小相关
    uint64_t totalPixels = static_cast<uint64_t>(width) * height;
    uint64_t kernelOps = static_cast<uint64_t>(params_.kernelSize) * params_.kernelSize;
    // 假设每个高斯操作需要0.001毫秒
    return static_cast<uint32_t>((totalPixels * kernelOps) / 1000);
}

void GaussianFilter::reset() {
    params_.reset();
    generateGaussianKernel();
    logDebug("GaussianFilter reset to default state");
}

QString GaussianFilter::getVersion() const {
    return "1.0.0";
}

bool GaussianFilter::isThreadSafe() const {
    return true; // 无状态操作，线程安全
}

bool GaussianFilter::supportsInPlace() const {
    return false; // 需要临时缓冲区
}

void GaussianFilter::setPreset(const QString& preset) {
    if (preset == "light_blur") {
        params_.sigma = 0.8f;
        params_.kernelSize = 3;
        params_.strength = 1.0f;
    } else if (preset == "medium_blur") {
        params_.sigma = 1.5f;
        params_.kernelSize = 5;
        params_.strength = 1.0f;
    } else if (preset == "heavy_blur") {
        params_.sigma = 2.5f;
        params_.kernelSize = 7;
        params_.strength = 1.0f;
    } else if (preset == "noise_reduction") {
        params_.sigma = 1.0f;
        params_.kernelSize = 5;
        params_.strength = 0.7f;
    } else {
        throw InvalidParameterException("preset", QString("unsupported preset: %1").arg(preset));
    }
    
    generateGaussianKernel();
    
    logDebug(QString("Set preset: %1 (sigma=%2, kernelSize=%3, strength=%4)")
            .arg(preset).arg(params_.sigma).arg(params_.kernelSize).arg(params_.strength));
}

QStringList GaussianFilter::getSupportedPresets() {
    return {"light_blur", "medium_blur", "heavy_blur", "noise_reduction"};
}

float GaussianFilter::applyGaussianAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const {
    float result = 0.0f;
    int halfKernel = params_.kernelSize / 2;
    
    for (int ky = 0; ky < params_.kernelSize; ++ky) {
        for (int kx = 0; kx < params_.kernelSize; ++kx) {
            int srcX = static_cast<int>(x) + kx - halfKernel;
            int srcY = static_cast<int>(y) + ky - halfKernel;
            
            uint32_t pixelValue = getSafePixelValue(src, srcX, srcY);
            result += static_cast<float>(pixelValue) * m_kernel[ky][kx];
        }
    }
    
    return result;
}

uint32_t GaussianFilter::getSafePixelValue(const ImageDataU32& src, int x, int y) const {
    // 边界处理：镜像扩展
    if (x < 0) x = -x;
    if (y < 0) y = -y;
    if (x >= static_cast<int>(src.width())) x = 2 * (src.width() - 1) - x;
    if (y >= static_cast<int>(src.height())) y = 2 * (src.height() - 1) - y;
    
    // 确保在有效范围内
    x = qBound(0, x, static_cast<int>(src.width() - 1));
    y = qBound(0, y, static_cast<int>(src.height() - 1));
    
    return src.matrix()[y][x];
}

void GaussianFilter::generateGaussianKernel() {
    m_kernel.clear();
    m_kernel.resize(params_.kernelSize);
    
    int halfKernel = params_.kernelSize / 2;
    float sum = 0.0f;
    
    // 生成高斯权重
    for (int y = 0; y < params_.kernelSize; ++y) {
        m_kernel[y].resize(params_.kernelSize);
        for (int x = 0; x < params_.kernelSize; ++x) {
            int dx = x - halfKernel;
            int dy = y - halfKernel;
            float weight = calculateGaussianWeight(dx, dy, params_.sigma);
            m_kernel[y][x] = weight;
            sum += weight;
        }
    }
    
    // 归一化
    if (sum > 0.0f) {
        for (int y = 0; y < params_.kernelSize; ++y) {
            for (int x = 0; x < params_.kernelSize; ++x) {
                m_kernel[y][x] /= sum;
            }
        }
    }
    
    logDebug(QString("Generated Gaussian kernel with sum: %1").arg(sum));
}

float GaussianFilter::calculateGaussianWeight(int x, int y, float sigma) const {
    float exponent = -(static_cast<float>(x * x + y * y) / (2.0f * sigma * sigma));
    return qExp(exponent) / (2.0f * M_PI * sigma * sigma);
}

void GaussianFilter::validateGaussianParams(const GaussianParams& params) const {
    params.validate();
}

void GaussianFilter::logDebug(const QString& message) const {
    qDebug() << "[GaussianFilter]" << message;
}

} // namespace ImageProcessing
