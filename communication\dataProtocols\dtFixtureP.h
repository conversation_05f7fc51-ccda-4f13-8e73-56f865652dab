#ifndef _DT_FIXTURE_P_H
#define _DT_FIXTURE_P_H

#include <QByteArray>
#include <QObject>
#include "IProtocol.h"

class dtFixture:public QObject, IProtocol
{
    Q_OBJECT
public:
    dtFixture(QObject *parent = nullptr);
    ~dtFixture();

    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

private:
    /*协议cmd枚举*/
    enum ECMD{
        eH2D        = 0<<0, //host->device
        eD2H        = 1<<0, //device->host

        eCMD        = 0<<2 | 0<<1, //cmd
        eACK        = 0<<2 | 1<<1, //ack
        eINFO       = 1<<2 | 0<<1, //info
        eDATA       = 1<<2 | 1<<1, //data

        eW          = 0<<3, //write
        eR          = 1<<3, //read

        eNA_O_AN    = 0<<5 | 0<<4, //cmd状态->no ack， data/ack状态->normal
        eA_O_AE     = 0<<5 | 1<<4, //cmd状态->ack反馈， data/ack状态->error
        eD_O_AT     = 1<<5 | 0<<4, //cmd状态->data反馈， data/ack状态->timeout
    };
};


#endif
