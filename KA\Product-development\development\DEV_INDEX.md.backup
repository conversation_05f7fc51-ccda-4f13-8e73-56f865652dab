# 开发索引文件

本文件包含开发相关的所有文档索引信息。

**文档统计**: 9 个文档
**更新时间**: 2025-07-09 11:25:36
**更新方式**: 自动扫描生成

## 文档列表

| 文档ID | 文档名称 | 文档路径 | 文档类型 | 组 | 状态 | 最后修改 | 负责人 | 关联需求 | 关联设计 | 备注 |
|--------|----------|----------|----------|-----|------|----------|--------|----------|----------|------|
| DEV001 | issue system | development\issue system.md | Markdown文档 |  | 有效 | 2025-05-15 | | | | |
| DEV002 | MCP服务器集成与AI调用技术方案 | development\software_process\MCP服务器集成与AI调用技术方案.md | Markdown文档 | SW-MCP服务器集成与AI调用技术方案.md | 有效 | 2025-07-01 | | | | |
| DEV003 | README_ENV | development\software_process\README_ENV.md | Markdown文档 | SW-README_ENV.md | 有效 | 2025-06-12 | | | | |
| DEV004 | 工业软件设计文档 | development\software_process\工业软件设计文档.md | Markdown文档 | SW-工业软件设计文档.md | 有效 | 2025-07-01 | | | | |
| DEV005 | 跨设备访问与安全方案 | development\software_process\跨设备访问与安全方案.md | Markdown文档 | SW-跨设备访问与安全方案.md | 有效 | 2025-07-01 | | | | |
| DEV006 | embedded_issue-demo | development\嵌入式软件开发流程\embedded_issue-demo.md | Markdown文档 | 嵌入-embedded_issue-demo.md | 有效 | 2025-05-15 | | | | |
| DEV007 | 嵌入式项目文档结构 | development\嵌入式软件开发流程\嵌入式项目文档结构.md | Markdown文档 | 嵌入-嵌入式项目文档结构.md | 有效 | 2025-06-04 | | | | |
| DEV008 | 嵌入式项目设计文档 | development\嵌入式软件开发流程\嵌入式项目设计文档.md | Markdown文档 | 嵌入-嵌入式项目设计文档.md | 有效 | 2025-06-20 | | | | |
| DEV009 | 开发项到输出闭环追溯框架 | development\嵌入式软件开发流程\开发项到输出闭环追溯框架.md | Markdown文档 | 嵌入-开发项到输出闭环追溯框架.md | 有效 | 2025-05-22 | | | | |


## 使用说明

1. **文档ID**: 系统自动生成的唯一标识符
2. **文档路径**: 相对于项目根目录的路径
3. **状态**: 文档当前状态（有效/已废弃/草稿等）
4. **关联字段**: 可手动填写文档间的关联关系

## 维护说明

- 本文件由文档扫描器自动生成
- 如需手动维护，请修改状态、负责人、关联等字段
- 重新运行扫描脚本会保留手动修改的信息

---
*最后更新: 2025-07-09 11:25:36*
