#ifndef _PROCESSSTATUS_H_
#define _PROCESSSTATUS_H_

#include <QObject>

namespace MOTOR_MONITOR{ //软件流程一
    enum PROCESS_STEP{
        ready_step,
        start_step,
        data_step,
        complete_step
    };
    enum STEP_STATUS
    {
        ready,
        unready,

        start,
        motor_stop,
        ack_ok,
        ack_timeout,
        ack_error,

        data_flow,
        data_xor_error,
        data_timeout,
        stop, //
        data_comp,

        motor_block, //MOTOR_MONITOR_STATUS
        capture_timeout,

        monitor_comp,
        comp,
    };
};

namespace MONITOR_PROCESS_ONE{ //软件流程一
    enum PROCESS_STEP{
        ready_step,
        start_step,
        data_step,
        adjust_step,
        complete_step
    };
    enum STEP_STATUS
    {
        ready,
        unready,

        start,
        stop, //
        ack_ok,
        ack_timeout,
        ack_error,

        data_flow,
        data_xor_error,
        data_timeout,
        data_comp,

        motor_block, //MOTOR_MONITOR_STATUS
        capture_timeout,

        device_check_error,
        adjusting, //LENS_ADJUST_STATUS

        ctr_timeout,
        ctr_error,

        monitor_comp,
        comp,
    };
};

//namespace MONITOR_PROCESS_TWO{
//enum PROCESS_STEP{
//    ready_step              = 0x0001,
//    start_step              = 0x0002,
//    data_step               = 0x0004,
//    adjust_step             = 0x0008,
//    complete_step           = 0x0010,
//    exit_step               = 0x8000,
//};
//enum STEP_STATUS
//{
//    none                    = 0x0000,
//    start                   = 0x0001, //指令发送 | 开始
//    ok                      = 0x0002, //指令ack正常 | 正常
//    error                   = 0x0003, //
//    timeout                 = 0x0004,
//    stop                    = 0x0005,
//};
//}
class monitor_process_two:public QObject{
    Q_OBJECT
public:
    monitor_process_two(QObject *parent){};
    ~monitor_process_two(){};
    enum PROCESS_STEP{
        ready_step              = 0x0001,
        start_step              = 0x0002,
        data_step               = 0x0004,
        adjust_step             = 0x0008,
        complete_step           = 0x0010,
        exit_step               = 0x8000,
    };
    enum STEP_STATUS
    {
        none                    = 0x0000,
        start                   = 0x0001, //指令发送 | 开始
        ok                      = 0x0002, //指令ack正常 | 正常
        error                   = 0x0004, //
        timeout                 = 0x0008,
        stop                    = 0x0010,
        fatal                   = 0x0020,
    };
    Q_ENUM(STEP_STATUS)
    Q_ENUM(PROCESS_STEP)
};
#endif
