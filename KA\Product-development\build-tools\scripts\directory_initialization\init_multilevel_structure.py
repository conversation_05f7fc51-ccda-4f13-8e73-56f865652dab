#!/usr/bin/env python3
"""
多层级产品项目初始化脚本 - 自动创建灵活层级的产品结构
用法: python init_multilevel_structure.py --config="level_config.json"
"""

import os
import sys
import argparse
import shutil
import json
import datetime
import re

# 基础目录结构 - 可根据层级配置进行自定义
BASE_STRUCTURE = {
    "requirements": {
        "_is_dir": True,
        "requirements_matrix.md": None  # None 表示将使用模板生成内容
    },
    "design": {
        "_is_dir": True
    },
    "development": {
        "_is_dir": True
    },
    "project_management": {
        "_is_dir": True,
        "schedules": {"_is_dir": True},
        "resources": {"_is_dir": True},
        "risks": {"_is_dir": True},
        "meeting_notes": {"_is_dir": True},
        "mcp_tasks": {"_is_dir": True}
    },
    "level_management": {
        "_is_dir": True,
        "level_links.md": None
    }
}

# 层级特定组件
LEVEL_SPECIFIC_COMPONENTS = {
    # 顶层特有组件
    "top_level": {
        "development": {
            "shared_components": {"_is_dir": True},
            "hardware": {"_is_dir": True},
            "firmware": {"_is_dir": True},
            "software": {"_is_dir": True}
        }
    },
    # 中间层特有组件
    "middle_level": {
        "development": {
            "hardware": {"_is_dir": True},
            "firmware_configs": {"_is_dir": True},
            "software_plugins": {"_is_dir": True}
        }
    },
    # 底层特有组件
    "bottom_level": {
        "development": {
            "configs": {"_is_dir": True}
        },
        "quality": {
            "_is_dir": True,
            "test_plans": {"_is_dir": True},
            "test_cases": {"_is_dir": True},
            "test_reports": {"_is_dir": True}
        },
        "production": {
            "_is_dir": True,
            "bom": {"_is_dir": True},
            "manufacturing_process": {"_is_dir": True},
            "quality_control": {"_is_dir": True}
        }
    }
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="创建多层级产品项目结构")
    parser.add_argument('--config', required=True, help="层级配置文件路径")
    parser.add_argument('--path', default=".", help="项目路径")
    return parser.parse_args()

def load_config(config_path):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证配置文件
        validate_config(config)
        return config
    except FileNotFoundError:
        print(f"错误: 找不到配置文件 {config_path}")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"错误: 配置文件 {config_path} 不是有效的JSON格式")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 加载配置文件时出错: {str(e)}")
        sys.exit(1)

def validate_config(config):
    """验证配置文件结构"""
    # 检查必要字段
    if "product_name" not in config:
        raise ValueError("配置文件缺少 'product_name' 字段")
    
    if "levels" not in config or not isinstance(config["levels"], list):
        raise ValueError("配置文件缺少 'levels' 字段或格式不正确")
    
    # 验证层级配置
    level_ids = set()
    instances = set()
    
    for level in config["levels"]:
        # 检查必要字段
        if "level_id" not in level:
            raise ValueError("层级配置缺少 'level_id' 字段")
        
        if "level_name" not in level:
            raise ValueError("层级配置缺少 'level_name' 字段")
        
        if "instances" not in level or not isinstance(level["instances"], list):
            raise ValueError(f"层级 {level['level_id']} 缺少 'instances' 字段或格式不正确")
        
        # 检查唯一性
        if level["level_id"] in level_ids:
            raise ValueError(f"层级ID {level['level_id']} 重复")
        level_ids.add(level["level_id"])
        
        # 检查实例名称唯一性
        for instance in level["instances"]:
            if instance in instances:
                raise ValueError(f"实例名称 '{instance}' 在不同层级中重复")
            instances.add(instance)
    
    # 验证父子关系映射
    for level in config["levels"]:
        if "parent_mapping" in level and level["level_id"] > 1:
            parent_level_id = level["level_id"] - 1
            
            # 找到父级层
            parent_level = None
            for l in config["levels"]:
                if l["level_id"] == parent_level_id:
                    parent_level = l
                    break
            
            if not parent_level:
                raise ValueError(f"层级 {level['level_id']} 的父层级 {parent_level_id} 不存在")
            
            # 验证映射中的父实例是否存在
            parent_instances = set(parent_level["instances"])
            for child, parent in level["parent_mapping"].items():
                if parent not in parent_instances:
                    raise ValueError(f"映射关系中的父实例 '{parent}' 不在层级 {parent_level_id} 中")
                
                # 验证子实例是否存在于当前层级
                if child not in level["instances"]:
                    raise ValueError(f"映射关系中的子实例 '{child}' 不在层级 {level['level_id']} 中")

def get_level_type(level_id, total_levels):
    """根据层级ID判断层级类型（顶层、中间层、底层）"""
    if level_id == 1:
        return "top_level"
    elif level_id == total_levels:
        return "bottom_level"
    else:
        return "middle_level"

def create_directory_structure(base_path, product_name, config):
    """创建多层级产品目录结构"""
    # 创建产品根目录
    product_path = os.path.join(base_path, product_name)
    if os.path.exists(product_path):
        print(f"警告: 目录 {product_path} 已存在，内容可能会被覆盖")
    else:
        os.makedirs(product_path)
    
    # 保存配置文件
    config_file_path = os.path.join(product_path, "__level_config.json")
    with open(config_file_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2)
    
    # 创建顶层交付物目录
    deliverables_path = os.path.join(product_path, "deliverables")
    os.makedirs(deliverables_path, exist_ok=True)
    
    # 为每个层级创建交付物子目录
    for level in config["levels"]:
        level_deliverables_path = os.path.join(deliverables_path, f"level_{level['level_id']}")
        os.makedirs(level_deliverables_path, exist_ok=True)
    
    # 创建交付物清单
    create_deliverables_manifest(deliverables_path, product_name, config)
    
    # 创建层级结构
    level_paths = {}  # 存储每个实例的路径信息
    total_levels = len(config["levels"])
    
    for level in config["levels"]:
        level_id = level["level_id"]
        level_name = level["level_name"]
        level_type = get_level_type(level_id, total_levels)
        
        for instance in level["instances"]:
            # 创建实例目录
            instance_path = os.path.join(product_path, f"level_{level_id}_{instance}")
            os.makedirs(instance_path, exist_ok=True)
            level_paths[instance] = instance_path
            
            # 创建基础目录结构
            create_base_structure(instance_path, BASE_STRUCTURE)
            
            # 创建层级特定组件
            if level_type in LEVEL_SPECIFIC_COMPONENTS:
                create_base_structure(instance_path, LEVEL_SPECIFIC_COMPONENTS[level_type])
            
            # 创建层级特定文档
            create_level_documents(instance_path, product_name, level, instance, config)
    
    # 创建层级间的链接关系
    create_level_links(product_path, config, level_paths)
    
    # 创建README文件
    create_readme(product_path, product_name, config)
    
    # 创建VSCode工作区
    create_vscode_workspace(product_path, product_name, config, level_paths)
    
    return product_path

def create_base_structure(base_path, structure, current_path=""):
    """递归创建目录结构"""
    for name, content in structure.items():
        if name == "_is_dir":
            continue
            
        path = os.path.join(base_path, name)
        
        if isinstance(content, dict) and content.get("_is_dir", False):
            # 创建目录
            os.makedirs(path, exist_ok=True)
            
            # 递归创建子目录结构
            create_base_structure(path, content, os.path.join(current_path, name))
        elif content is None:
            # 文件将由特定函数创建，此处只是占位
            pass
        elif isinstance(content, str):
            # 创建文件
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)

def create_level_documents(instance_path, product_name, level_info, instance_name, config):
    """创建层级特定文档"""
    level_id = level_info["level_id"]
    level_name = level_info["level_name"]
    
    # 创建需求追溯矩阵
    create_requirements_matrix(
        instance_path, 
        product_name, 
        level_id, 
        level_name, 
        instance_name, 
        config
    )
    
    # 创建层级链接文档
    create_level_link_doc(
        instance_path, 
        product_name, 
        level_id, 
        level_name, 
        instance_name, 
        config
    )
    
    # 创建层级特有的其他文档
    if level_id == 1:  # 顶层
        create_top_level_docs(instance_path, product_name, instance_name, config)
    elif level_id == len(config["levels"]):  # 底层
        create_bottom_level_docs(instance_path, product_name, instance_name, config)

def create_requirements_matrix(instance_path, product_name, level_id, level_name, instance_name, config):
    """创建需求追溯矩阵"""
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # 查找下一层级
    next_level = None
    for level in config["levels"]:
        if level["level_id"] == level_id + 1:
            next_level = level
            break
    
    # 查找指向当前实例的下层实例
    child_instances = []
    if next_level and "parent_mapping" in next_level:
        for child, parent in next_level["parent_mapping"].items():
            if parent == instance_name:
                child_instances.append(child)
    
    matrix_content = f"""# {product_name} - {instance_name} 需求追溯矩阵

## 基本信息

- **产品名称**: {product_name}
- **层级**: {level_id} ({level_name})
- **实例**: {instance_name}
- **创建日期**: {today}
- **版本**: 0.1.0
- **状态**: 初始化

## 需求概述

[在此描述 {instance_name} 的主要需求和目标]

## 需求列表

| 需求ID | 需求描述 | 优先级 | 状态 | 相关下层需求 |
|-------|---------|------|------|------------|
| L{level_id}_R001 | [需求描述] | 高/中/低 | 规划中 | """
    
    # 添加下层需求链接
    if child_instances:
        matrix_content += "["
        for i, child in enumerate(child_instances):
            if i > 0:
                matrix_content += ", "
            matrix_content += f"L{level_id+1}_R001 ({child})"
        matrix_content += "]"
    else:
        matrix_content += "N/A"
    
    matrix_content += """ |
| L{}_R002 | [需求描述] | 高/中/低 | 规划中 | ... |
| L{}_R003 | [需求描述] | 高/中/低 | 规划中 | ... |

## 需求详情

### L{}_R001: [需求名称]

**描述**:
[详细需求描述]

**验收标准**:
- 标准1
- 标准2
- 标准3

**技术考量**:
[技术实现考虑因素]

### L{}_R002: [需求名称]

...

## 需求依赖关系

[描述需求之间的依赖关系]

## 追溯关系

""".format(level_id, level_id, level_id, level_id)
    
    # 添加与上层需求的关系
    if level_id > 1:
        matrix_content += """### 上层需求追溯

| 本层需求ID | 上层需求ID | 关系类型 |
|-----------|-----------|---------|
| L{}_R001 | L{}_R001 | 细化 |
| L{}_R002 | L{}_R001 | 细化 |
| L{}_R003 | L{}_R002 | 细化 |

""".format(level_id, level_id-1, level_id, level_id-1, level_id, level_id-1)
    
    # 添加与下层需求的关系
    if child_instances:
        matrix_content += """### 下层需求追溯

| 本层需求ID | 下层需求ID | 下层实例 | 关系类型 |
|-----------|-----------|---------|---------|
"""
        for child in child_instances:
            matrix_content += f"| L{level_id}_R001 | L{level_id+1}_R001 | {child} | 展开 |\n"
            matrix_content += f"| L{level_id}_R002 | L{level_id+1}_R002 | {child} | 展开 |\n"
    
    matrix_content += """
## 变更历史

| 日期 | 版本 | 变更内容 | 变更人 |
|------|------|---------|-------|
| {} | 0.1.0 | 初始创建 | [创建人] |
""".format(today)
    
    # 写入文件
    with open(os.path.join(instance_path, "requirements", "requirements_matrix.md"), 'w', encoding='utf-8') as f:
        f.write(matrix_content)

def create_level_link_doc(instance_path, product_name, level_id, level_name, instance_name, config):
    """创建层级链接文档"""
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # 查找下一层级
    next_level = None
    for level in config["levels"]:
        if level["level_id"] == level_id + 1:
            next_level = level
            break
    
    # 查找指向当前实例的下层实例
    child_instances = []
    if next_level and "parent_mapping" in next_level:
        for child, parent in next_level["parent_mapping"].items():
            if parent == instance_name:
                child_instances.append(child)
    
    links_content = f"""# {product_name} - {instance_name} 层级链接

## 基本信息

- **产品名称**: {product_name}
- **层级**: {level_id} ({level_name})
- **实例**: {instance_name}
- **创建日期**: {today}
- **版本**: 0.1.0

## 层级关系概述

本文档记录 {instance_name} 与下层实例的关系和差异。

"""
    
    if child_instances:
        links_content += f"## 下层实例\n\n{instance_name} 包含以下下层实例：\n\n"
        
        for child in child_instances:
            links_content += f"### {child}\n\n"
            links_content += f"- **路径**: `../level_{level_id+1}_{child}/`\n"
            links_content += f"- **需求文档**: [[../level_{level_id+1}_{child}/requirements/requirements_matrix.md]]\n"
            links_content += "- **主要特点**: [描述该实例的主要特点]\n"
            links_content += "- **与其他实例的差异**: [描述该实例与同级其他实例的差异]\n\n"
    else:
        links_content += "本实例没有下层实例。\n\n"
    
    links_content += """## 开发职责划分

| 组件 | 本层开发范围 | 下层定制范围 |
|------|------------|------------|
| 硬件设计 | [本层负责内容] | [下层负责内容] |
| 固件开发 | [本层负责内容] | [下层负责内容] |
| 软件开发 | [本层负责内容] | [下层负责内容] |
| 测试验证 | [本层负责内容] | [下层负责内容] |

## 资源共享规则

1. [共享规则1]
2. [共享规则2]
3. [共享规则3]

## 版本协同策略

[描述如何协调本层与下层实例的版本]
"""
    
    # 写入文件
    with open(os.path.join(instance_path, "level_management", "level_links.md"), 'w', encoding='utf-8') as f:
        f.write(links_content)

def create_top_level_docs(instance_path, product_name, instance_name, config):
    """创建顶层特有文档"""
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # 创建通用架构文档
    arch_content = f"""# {product_name} - {instance_name} 通用架构

## 基本信息

- **产品名称**: {product_name}
- **实例**: {instance_name}
- **创建日期**: {today}
- **版本**: 0.1.0
- **状态**: 初始化

## 架构概述

[描述{instance_name}的整体架构]

## 系统层次

1. **硬件层**
   - 核心硬件平台
   - 可扩展接口

2. **固件层**
   - 引导程序
   - 设备驱动
   - 核心功能库

3. **应用层**
   - 业务逻辑
   - 用户界面
   - 通信协议

## 核心组件

- **组件1**: [描述]
- **组件2**: [描述]
- **组件3**: [描述]

## 技术选型

| 层次 | 技术选择 | 选型理由 |
|------|---------|---------|
| 硬件平台 | [技术] | [理由] |
| 处理器 | [技术] | [理由] |
| 操作系统 | [技术] | [理由] |
| 开发语言 | [技术] | [理由] |

## 接口定义

[描述核心接口规范]

## 扩展策略

[描述如何支持下层实例的扩展需求]

## 版本兼容策略

[描述不同版本间的兼容策略]
"""
    
    os.makedirs(os.path.join(instance_path, "design", "architecture"), exist_ok=True)
    with open(os.path.join(instance_path, "design", "architecture", "common_architecture.md"), 'w', encoding='utf-8') as f:
        f.write(arch_content)
    
    # 创建组件共享指南
    sharing_content = f"""# {product_name} - {instance_name} 组件共享指南

## 基本信息

- **产品名称**: {product_name}
- **实例**: {instance_name}
- **创建日期**: {today}
- **版本**: 0.1.0

## 共享原则

1. **不修改原则**: 下层不应直接修改共享组件，而是通过配置或扩展适配
2. **兼容性原则**: 共享组件更新必须考虑对所有使用方的兼容性
3. **最小化原则**: 共享组件应包含必要的最小功能集

## 共享组件目录

共享组件位于 `development/shared_components/` 目录，包含以下内容:

- `core/`: 核心功能组件
- `utilities/`: 通用工具组件
- `interfaces/`: 标准接口定义

## 使用方法

### 硬件引用

[描述如何在硬件设计中引用共享组件]

### 固件引用

[描述如何在固件中引用共享组件]

### 软件引用

[描述如何在软件中引用共享组件]

## 配置与扩展机制

[描述如何通过配置和扩展机制适应不同需求]

## 版本管理

[描述共享组件的版本管理策略]

## 贡献流程

[描述如何对共享组件进行改进和贡献]
"""
    
    os.makedirs(os.path.join(instance_path, "development", "shared_components"), exist_ok=True)
    with open(os.path.join(instance_path, "development", "shared_components", "sharing_guide.md"), 'w', encoding='utf-8') as f:
        f.write(sharing_content)

def create_bottom_level_docs(instance_path, product_name, instance_name, config):
    """创建底层特有文档"""
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # 创建产品规格文档
    spec_content = f"""# {product_name} - {instance_name} 产品规格

## 基本信息

- **产品名称**: {product_name} {instance_name}
- **创建日期**: {today}
- **版本**: 0.1.0
- **状态**: 规划中

## 产品概述

[{instance_name}产品描述]

## 技术规格

| 项目 | 规格 | 备注 |
|------|------|------|
| 规格1 | [数值] | [说明] |
| 规格2 | [数值] | [说明] |
| 规格3 | [数值] | [说明] |

## 功能特性

- 特性1
- 特性2
- 特性3

## 用户界面

[描述产品用户界面]

## 使用场景

[描述主要使用场景]

## 认证要求

[描述产品认证要求]

## 包装与配件

[描述产品包装和随机配件]
"""
    
    os.makedirs(os.path.join(instance_path, "product_info"), exist_ok=True)
    with open(os.path.join(instance_path, "product_info", "product_specification.md"), 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    # 创建生产指南
    production_content = f"""# {product_name} - {instance_name} 生产指南

## 基本信息

- **产品名称**: {product_name} {instance_name}
- **创建日期**: {today}
- **版本**: 0.1.0
- **状态**: 规划中

## 生产概述

[描述{instance_name}的生产概述]

## 物料清单 (BOM)

[物料清单链接或摘要]

## 生产工艺流程

1. [工艺步骤1]
2. [工艺步骤2]
3. [工艺步骤3]

## 测试要求

[描述生产测试要求]

## 包装要求

[描述包装要求]

## 质量控制点

[描述关键质量控制点]

## 生产工具需求

[描述所需生产工具]
"""
    
    with open(os.path.join(instance_path, "production", "production_guide.md"), 'w', encoding='utf-8') as f:
        f.write(production_content)

def create_level_links(product_path, config, level_paths):
    """处理层级之间的链接关系"""
    # 配置已经在前面验证过，此处直接使用
    for level in config["levels"]:
        level_id = level["level_id"]
        
        # 顶层没有上层关系，跳过
        if level_id == 1:
            continue
        
        # 处理父子映射关系
        if "parent_mapping" in level:
            for child, parent in level["parent_mapping"].items():
                child_path = level_paths[child]
                parent_path = level_paths[parent]
                
                # 创建上层引用文件 (可选)
                parent_ref_path = os.path.join(child_path, "parent_level.md")
                with open(parent_ref_path, 'w', encoding='utf-8') as f:
                    rel_path = os.path.relpath(parent_path, child_path)
                    f.write(f"# 上层引用\n\n本产品的上层为 [{parent}]({rel_path}/README.md)\n")

def create_deliverables_manifest(deliverables_path, product_name, config):
    """创建交付物清单文档"""
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    manifest_content = f"""# {product_name} 交付物清单

## 基本信息

- **产品名称**: {product_name}
- **创建日期**: {today}
- **版本**: 0.1.0
- **状态**: 初始化

## 文档使用说明

本文档记录{product_name}所有交付物的详细信息，按层级和实例组织。

## 交付物管理流程

1. 发布新版本时，从开发目录复制最新资料到交付目录
2. 更新本清单中对应资料的版本号和日期
3. 添加更新说明
4. 必要时归档旧版本

"""
    
    # 为每个层级添加交付物部分
    for level in config["levels"]:
        level_id = level["level_id"]
        level_name = level["level_name"]
        
        manifest_content += f"## 层级 {level_id}: {level_name} 交付物\n\n"
        
        for instance in level["instances"]:
            manifest_content += f"### {instance} 交付物\n\n"
            manifest_content += """| 资料名称 | 版本号 | 更新日期 | 来源路径 | 说明 |
|---------|-------|---------|---------|------|
| [资料名称] | [版本号] | [日期] | [来源路径] | [说明] |

"""
    
    manifest_content += """## 版本更新历史

### [日期] 版本更新

- [更新内容]

## 交付清单检查表

在最终交付前，请确认以下事项：

- [ ] 所有层级的交付资料已验证
- [ ] 所有交付资料已通过质量检查
- [ ] 本清单信息已更新完毕
- [ ] 所有文档均为最新版本
"""
    
    # 写入文件
    with open(os.path.join(deliverables_path, "deliverables_manifest.md"), 'w', encoding='utf-8') as f:
        f.write(manifest_content)

def create_readme(project_path, product_name, config):
    """创建README文件"""
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    readme_content = f"""# {product_name} 产品

## 基本信息

- **产品名称**: {product_name}
- **创建日期**: {today}
- **版本**: 0.1.0
- **状态**: 初始化

## 产品概述

{config.get('description', '多层级产品项目')}

## 层级结构

本项目采用多层级结构组织：

"""
    
    # 添加每个层级的描述
    for level in sorted(config["levels"], key=lambda x: x["level_id"]):
        level_id = level["level_id"]
        level_name = level["level_name"]
        level_desc = level.get("description", f"{level_name}层级")
        
        readme_content += f"### 层级 {level_id}: {level_name}\n\n"
        readme_content += f"{level_desc}\n\n"
        
        readme_content += "**实例**:\n"
        for instance in level["instances"]:
            readme_content += f"- {instance}\n"
        
        readme_content += "\n"
    
    readme_content += """## 目录结构

- `__level_config.json`: 层级配置文件
- `level_X_Y/`: 层级X的Y实例目录
  - `requirements/`: 需求文档
  - `design/`: 设计文档
  - `development/`: 开发项目
  - `level_management/`: 层级管理文档
  - `project_management/`: 项目管理
- `deliverables/`: 交付物目录
  - `level_X/`: 层级X的交付物

## 快速开始

1. 使用VSCode打开项目的 `.code-workspace` 文件查看完整项目结构
2. 从顶层开始，逐层了解各层级的需求和设计
3. 遵循每个层级的文档说明进行开发

## 开发流程

1. 自上而下规划需求
2. 从上层到下层逐步实现
3. 使用配置和扩展机制适应下层特殊需求
4. 保持跨层级的兼容性和一致性

## 资源共享原则

1. 上层组件可被下层使用
2. 避免在下层修改上层组件
3. 使用配置和扩展机制适应差异

## 脚本调用

本项目使用公共脚本目录的脚本，不在项目内维护脚本：
```
F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/
```

## 联系方式

- 产品负责人: [负责人姓名]
- 邮箱: [邮箱地址]
"""
    
    with open(os.path.join(project_path, "README.md"), 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_vscode_workspace(project_path, product_name, config, level_paths):
    """创建VSCode工作区配置"""
    # 创建工作区文件夹映射
    folder_mappings = []
    
    # 添加README和交付物目录映射
    folder_mappings.append({
        "name": "00_README",
        "path": "."
    })
    
    # 按层级顺序添加文件夹映射
    index = 1
    # for level in sorted(config["levels"], key=lambda x: x["level_id"]):
    #     level_id = level["level_id"]
        
    #     for instance in level["instances"]:
    #         folder_mappings.append({
    #             "name": f"{index:02d}_L{level_id}_{instance}",
    #             "path": f"level_{level_id}_{instance}"
    #         })
    #         index += 1
    
    # 添加交付物文件夹
    folder_mappings.append({
        "name": f"{index:02d}_deliverables",
        "path": "deliverables"
    })
    
    # 创建工作区文件配置
    workspace_file = {
        "folders": folder_mappings,
        "settings": {
            "editor.formatOnSave": True,
            "files.autoSave": "afterDelay",
            "files.autoSaveDelay": 1000,
            "workbench.colorCustomizations": {
                "activityBar.background": "#2C2C54",
                "titleBar.activeBackground": "#3B3B72",
                "titleBar.activeForeground": "#F9F9FD"
            },
            "terminal.integrated.env.windows": {
                "SCRIPTS_PATH": "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts"
            }
        },
        "extensions": {
            "recommendations": [
                "ms-vscode.cpptools",
                "platformio.platformio-ide",
                "mhutchie.git-graph",
                "yzhang.markdown-all-in-one",
                "bierner.markdown-mermaid",
                "eamodio.gitlens"
            ]
        }
    }
    
    with open(os.path.join(project_path, f"{product_name}.code-workspace"), 'w', encoding='utf-8') as f:
        json.dump(workspace_file, f, indent=2)

def main():
    """主函数"""
    args = parse_arguments()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建目录结构
    project_path = create_directory_structure(args.path, config["product_name"], config)
    
    print(f"多层级产品 {config['product_name']} 已成功初始化")
    print(f"层级结构: {', '.join([level['level_name'] for level in config['levels']])}")
    print(f"请使用 VSCode 打开 {os.path.join(project_path, config['product_name'] + '.code-workspace')}")

if __name__ == "__main__":
    main() 