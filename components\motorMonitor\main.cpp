#include "motormonitorwidget.h"

#include "qLog.h"
#include <QApplication>
#include <QStringList>


int main(int argc, char *argv[]) {
    QApplication       a(argc, argv);
    motorMonitorWidget w;

    // 初始化日志系统
    MyLogger::QLog::init();

    //#ifndef LOG_TO_CONSOLE
    MyLogger::QLog::installMessageHandler();
    //#endif

    // 使用Qt调试函数
    // qDebug() << "LenAdjust Application started";

    // 使用日志宏
    // LOG_INFO(MyLogger::LogType::INIT, "Application initialized");

    // 使用组件日志
    LOG_INFO(MyLogger::LogType::INIT, "motorMonitor started");

    w.show();
    return a.exec();
}
