#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动文档链接和Canvas同步脚本

该脚本扩展了原有的文档链接功能，增加了Obsidian Canvas的支持：
1. 将INDEX文件中的文档同步到Canvas（支持增量和完全重写模式）
2. 从Canvas同步关联关系回INDEX文件
3. 支持目录分组和多列布局
4. 保留手动调整的节点位置和连线
5. 提供Canvas与INDEX的双向验证和同步
"""

import argparse
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 添加父目录到路径，以便导入其他模块
current_dir = Path(__file__).parent
scripts_dir = current_dir.parent
sys.path.insert(0, str(scripts_dir))

# 导入基础设施层模块
from common.document_scanner import IndexScanner
from common.index_manager import IndexManager
from common.document_id_generator import DocumentIdGenerator
from canvas.canvas_manager import CanvasManager
from canvas.canvas_layout import CanvasLayoutManager
from canvas.canvas_utils import CanvasPathUtils


class DocumentCanvasIntegrator:
    """文档Canvas集成器"""
    
    def __init__(self, project_path: Path):
        """
        初始化集成器
        
        Args:
            project_path: 项目根目录路径
        """
        self.project_path = Path(project_path)
        
        # 使用基础设施层的统一服务
        self.index_scanner = IndexScanner(project_path)
        self.index_manager = IndexManager(project_path)
        self.id_generator = DocumentIdGenerator()
        
        # Canvas特定服务
        self.canvas_manager = CanvasManager(project_path)
        self.layout_manager = CanvasLayoutManager()
        self.path_utils = CanvasPathUtils(self.project_path)
    
    def sync_index_to_canvas(self, mode: str = "incremental") -> bool:
        """
        将INDEX文件中的文档同步到Canvas
        
        Args:
            mode: 同步模式 - "incremental"(增量) 或 "full"(完全重写)
        
        Returns:
            bool: 同步成功返回True
        """
        print(f"开始将INDEX文件同步到Canvas (模式: {mode})...")
        
        # 使用基础设施层统一服务查找和解析INDEX文件
        index_files = self.index_scanner.find_index_files()
        
        # 收集所有文档
        all_documents = {}
        total_docs = 0
        removed_docs = 0
        
        for component, index_path in index_files.items():
            documents = self.index_scanner.parse_index_file(index_path)
            valid_documents = []
            
            # 验证文档是否实际存在
            for doc in documents:
                doc_path = doc.get('doc_path', '')
                if doc_path:
                    full_path = self.project_path / doc_path
                    if full_path.exists():
                        valid_documents.append(doc)
                    else:
                        removed_docs += 1
                        print(f"  跳过不存在的文档: {doc.get('doc_id', 'Unknown')} - {doc_path}")
                else:
                    # 没有路径信息，跳过
                    removed_docs += 1
                    print(f"  跳过无路径的文档: {doc.get('doc_id', 'Unknown')}")
            
            if valid_documents:
                all_documents[component] = valid_documents
                total_docs += len(valid_documents)
                print(f"  {component}: 找到 {len(valid_documents)} 个有效文档")
        
        if removed_docs > 0:
            print(f"  跳过了 {removed_docs} 个无效文档记录")
        
        if total_docs == 0:
            print("未找到任何有效文档，创建空Canvas")
            return self.canvas_manager.create_canvas()
        
        # 生成Canvas节点和组（支持目录分组）
        canvas_nodes, canvas_groups = self._generate_canvas_nodes_with_groups(all_documents)
        canvas_edges = self._generate_canvas_edges(all_documents)
        
        # 合并节点和组
        all_canvas_nodes = canvas_groups + canvas_nodes
        
        # 更新Canvas
        canvas_data = {
            "nodes": all_canvas_nodes,
            "edges": canvas_edges
        }
        
        success = self.canvas_manager.write_canvas(canvas_data, mode=mode)
        if success:
            print(f"Canvas同步完成: {len(canvas_nodes)} 个文档节点, {len(canvas_groups)} 个目录组, {len(canvas_edges)} 条边")
        else:
            print("Canvas同步失败")
            
        return success
    
    def sync_canvas_to_index(self) -> bool:
        """
        从Canvas同步关联关系回INDEX文件
        
        Returns:
            bool: 同步成功返回True
        """
        print("开始从Canvas同步关联关系到INDEX...")
        
        # 提取Canvas中的关联关系
        associations = self.canvas_manager.extract_edges_to_associations()
        
        if not associations:
            print("Canvas中未发现关联关系")
            return True
        
        print(f"发现 {len(associations)} 个关联关系")
        
        # 按文档ID组织关联关系
        doc_associations = {}
        for assoc in associations:
            from_doc_id = assoc["from_doc_id"]
            to_doc_id = assoc["to_doc_id"]
            
            if from_doc_id not in doc_associations:
                doc_associations[from_doc_id] = []
            doc_associations[from_doc_id].append({
                "target_doc_id": to_doc_id,
                "relation_type": assoc["relation_type"],
                "source": "canvas"
            })
        
        # 更新INDEX文件中的关联信息
        success_count = 0
        for doc_id, relations in doc_associations.items():
            # 查找文档所属的组件
            component = self._find_document_component(doc_id)
            if component:
                success = self._update_document_associations(component, doc_id, relations)
                if success:
                    success_count += 1
        
        print(f"Canvas关联同步完成: {success_count}/{len(doc_associations)} 个文档更新成功")
        return success_count > 0
    
    def _generate_canvas_nodes_with_groups(self, all_documents: Dict[str, List[Dict]]) -> Tuple[List[Dict], List[Dict]]:
        """
        根据文档信息生成Canvas节点和目录组
        
        Args:
            all_documents: 按组件分组的文档列表
            
        Returns:
            Tuple[List[Dict], List[Dict]]: (文档节点列表, 目录组列表)
        """
        all_nodes = []
        all_groups = []
        
        # 使用新的布局管理器计算位置（启用层级分组）
        layout_data = self.layout_manager.calculate_area_positions_with_groups(all_documents, use_hierarchical=True)
        
        for component, component_data in layout_data.items():
            # 添加目录组
            for group_info in component_data["groups"]:
                all_groups.append(group_info)
            
            # 添加文档节点
            for node_info in component_data["nodes"]:
                doc = node_info["document"]
                position = node_info["position"]
                color = node_info["color"]
                
                # 生成节点ID
                node_id = self._generate_node_id(doc['doc_id'])
                
                # 构建文件路径（相对于项目根目录）
                file_path = self.path_utils.format_canvas_path(doc['doc_path'])
                
                node = {
                    "id": node_id,
                    "type": "file",
                    "file": file_path,
                    "x": position["x"],
                    "y": position["y"],
                    "width": position.get("width", self.layout_manager.node_width),
                    "height": position.get("height", self.layout_manager.node_height),
                    "color": color
                }
                
                all_nodes.append(node)
                
        return all_nodes, all_groups
    
    def _generate_canvas_edges(self, all_documents: Dict[str, List[Dict]]) -> List[Dict]:
        """
        根据文档关联关系生成Canvas连线
        
        Args:
            all_documents: 按组件分组的文档列表
            
        Returns:
            List[Dict]: Canvas边列表
        """
        edges = []
        
        # 创建文档ID到节点ID的映射
        doc_to_node_map = {}
        for component, documents in all_documents.items():
            for doc in documents:
                doc_id = doc.get('doc_id', '')
                node_id = self._generate_node_id(doc_id)
                doc_to_node_map[doc_id] = node_id
        
        # 分析文档间的关联关系（基于INDEX中的信息）
        for component, documents in all_documents.items():
            for doc in documents:
                doc_id = doc.get('doc_id', '')
                
                # 这里可以扩展：从文档内容或INDEX信息中提取关联关系
                # 目前返回空列表，由用户手动在Canvas中建立连线
                pass
        
        return edges
    
    def _find_document_component(self, doc_id: str) -> Optional[str]:
        """
        根据文档ID查找所属组件
        
        Args:
            doc_id: 文档ID
            
        Returns:
            Optional[str]: 组件名称，未找到返回None
        """
        index_files = self.index_scanner.find_index_files()
        
        for component, index_path in index_files.items():
            documents = self.index_scanner.parse_index_file(index_path)
            for doc in documents:
                if doc.get('doc_id') == doc_id:
                    return component
        
        return None
    
    def _update_document_associations(self, component: str, doc_id: str, relations: List[Dict]) -> bool:
        """
        更新文档的关联关系
        
        Args:
            component: 组件名称
            doc_id: 文档ID
            relations: 关联关系列表
            
        Returns:
            bool: 更新成功返回True
        """
        # 这里可以实现将关联关系写入INDEX文件的逻辑
        # 暂时只记录到日志
        print(f"  更新 {component}.{doc_id} 的关联关系: {len(relations)} 个")
        return True
    
    def add_document_to_system(self, component: str, doc_type: str, 
                              doc_name: str, file_path: str) -> bool:
        """
        将新文档添加到系统（INDEX + Canvas）
        
        Args:
            component: 组件名称
            doc_type: 文档类型
            doc_name: 文档名称
            file_path: 文件路径
            
        Returns:
            bool: 添加成功返回True
        """
        print(f"添加文档到系统: {doc_name}")
        
        # 1. 使用统一ID生成器生成文档ID
        index_files = self.index_scanner.find_index_files()
        existing_docs = []
        if component in index_files:
            existing_docs = self.index_scanner.parse_index_file(index_files[component])
        
        existing_ids = [doc['doc_id'] for doc in existing_docs]
        doc_id = self.id_generator.generate_new_id(component, existing_ids)
        
        # 2. 添加到INDEX文件
        new_doc = {
            'doc_id': doc_id,
            'doc_name': doc_name,
            'doc_path': file_path,
            'doc_type': doc_type,
            'last_update': datetime.now().strftime("%Y-%m-%d")
        }
        
        existing_docs.append(new_doc)
        success = self.index_manager.rebuild_index_file(component, existing_docs, "document")
        
        if not success:
            print(f"添加到INDEX失败: {component}")
            return False
        
        # 3. 更新Canvas（增量模式）
        success = self.sync_index_to_canvas(mode="incremental")
        if success:
            print(f"✓ 文档添加成功: {doc_id} - {doc_name}")
        else:
            print(f"✗ Canvas更新失败")
            
        return success
    
    def _generate_node_id(self, doc_id: str) -> str:
        """
        生成Canvas节点ID
        
        Args:
            doc_id: 文档ID
            
        Returns:
            str: 节点ID
        """
        # 使用文档ID的前缀作为节点ID，确保可识别
        import hashlib
        
        # 生成短哈希保证唯一性
        hash_obj = hashlib.md5(doc_id.encode())
        hash_suffix = hash_obj.hexdigest()[:6].upper()
        
        return f"{doc_id}{hash_suffix}"
    
    def validate_sync(self) -> Dict[str, List[str]]:
        """
        验证INDEX和Canvas的同步状态
        
        Returns:
            Dict[str, List[str]]: 验证结果
        """
        print("验证INDEX和Canvas同步状态...")
        
        issues = {
            "missing_in_canvas": [],
            "extra_in_canvas": [],
            "layout_issues": [],
            "edge_issues": []
        }
        
        # 获取INDEX中的文档
        index_files = self.index_scanner.find_index_files()
        index_docs = set()
        
        for component, index_path in index_files.items():
            documents = self.index_scanner.parse_index_file(index_path)
            for doc in documents:
                doc_path = doc.get('doc_path', '')
                if doc_path:
                    full_path = self.project_path / doc_path
                    if full_path.exists():
                        canvas_path = self.path_utils.format_canvas_path(doc_path)
                        index_docs.add(canvas_path)
        
        # 获取Canvas中的文档节点
        canvas_data = self.canvas_manager.read_canvas()
        canvas_docs = set()
        
        if canvas_data:
            for node in canvas_data.get("nodes", []):
                if node.get("type") == "file" and "file" in node:
                    canvas_docs.add(node["file"])
        
        # 检查缺失和多余的文档
        missing_in_canvas = index_docs - canvas_docs
        extra_in_canvas = canvas_docs - index_docs
        
        issues["missing_in_canvas"] = list(missing_in_canvas)
        issues["extra_in_canvas"] = list(extra_in_canvas)
        
        # 报告结果
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        
        if total_issues == 0:
            print("✓ INDEX和Canvas同步状态正常")
        else:
            print(f"⚠ 发现 {total_issues} 个同步问题:")
            for issue_type, issue_list in issues.items():
                if issue_list:
                    print(f"  {issue_type}: {len(issue_list)} 个")
        
        return issues
    
    def get_canvas_stats(self) -> Dict:
        """
        获取Canvas统计信息
        
        Returns:
            Dict: 统计信息
        """
        canvas_info = self.canvas_manager.get_canvas_info()
        
        if not canvas_info["exists"]:
            return canvas_info
        
        # 获取详细统计
        canvas_data = self.canvas_manager.read_canvas()
        if canvas_data:
            nodes = canvas_data.get("nodes", [])
            
            # 按类型统计节点
            node_types = {}
            file_nodes = 0
            group_nodes = 0
        
            for node in nodes:
                node_type = node.get("type", "unknown")
                node_types[node_type] = node_types.get(node_type, 0) + 1
                
                if node_type == "file":
                    file_nodes += 1
                elif node_type == "group":
                    group_nodes += 1
            
            canvas_info.update({
                "file_nodes": file_nodes,
                "group_nodes": group_nodes,
                "node_types": node_types
            })
        
        return canvas_info


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Canvas文档同步工具')
    parser.add_argument('--project-path', default='.', help='项目路径')
    
    # 同步操作
    parser.add_argument('--sync-to-canvas', action='store_true', help='将INDEX同步到Canvas')
    # 注意：--sync-from-canvas功能已移至links/auto_link_documents.py，避免重复
    parser.add_argument('--mode', choices=['incremental', 'full'], default='incremental',
                       help='同步模式：incremental(增量，默认) 或 full(完全重写)')
    
    # 验证和统计
    parser.add_argument('--validate-sync', action='store_true', help='验证同步状态')
    parser.add_argument('--stats', action='store_true', help='显示Canvas统计信息')
    
    # 文档管理
    parser.add_argument('--add-document', action='store_true', help='添加新文档')
    parser.add_argument('--component', help='组件代码 (REQ/DES/DEV/QA/PROD/DEL/PM)')
    parser.add_argument('--type', help='文档类型')
    parser.add_argument('--name', help='文档名称')
    parser.add_argument('--path', help='文件路径')
    
    args = parser.parse_args()
    
    integrator = DocumentCanvasIntegrator(args.project_path)
    
    if args.sync_to_canvas:
        success = integrator.sync_index_to_canvas(mode=args.mode)
        if success:
            print("✓ INDEX到Canvas同步完成")
        else:
            print("✗ INDEX到Canvas同步失败")
            sys.exit(1)
    
    # 注意：Canvas到INDEX同步功能已移至links/auto_link_documents.py --sync-from-canvas
        
    elif args.validate_sync:
        issues = integrator.validate_sync()
        if any(issues.values()):
            sys.exit(1)
        
    elif args.stats:
        stats = integrator.get_canvas_stats()
        print("Canvas统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
    
    elif args.add_document:
        if not all([args.component, args.type, args.name, args.path]):
            print("错误: 添加文档需要指定 --component, --type, --name, --path 参数")
            sys.exit(1)
        
        success = integrator.add_document_to_system(
            args.component, args.type, args.name, args.path
        )
        if not success:
            sys.exit(1)
        
    else:
        # 显示帮助信息
        print("Canvas文档同步工具")
        print("")
        print("主要功能:")
        print("1. INDEX文件与Canvas的双向同步")
        print("2. 支持增量和完全重写两种模式")
        print("3. 保留手动调整的节点位置和连线")
        print("4. 自动创建目录分组")
        print("5. 多列布局支持")
        print("")
        print("使用示例:")
        print("  # 增量同步INDEX到Canvas（默认模式，保留手动调整）")
        print("  python auto_link_documents.py --sync-to-canvas")
        print("")
        print("  # 完全重写Canvas（重置所有手动调整）")
        print("  python auto_link_documents.py --sync-to-canvas --mode full")
        print("")
        print("  # 从Canvas同步关联关系到INDEX（使用links脚本）")
        print("  python ../links/auto_link_documents.py --sync-from-canvas")
        print("")
        print("  # 验证同步状态")
        print("  python auto_link_documents.py --validate-sync")
        print("")
        print("  # 查看Canvas统计信息")
        print("  python auto_link_documents.py --stats")
        print("")
        print("  # 添加新文档到系统")
        print("  python auto_link_documents.py --add-document --component REQ --type '需求文档' --name '新需求' --path 'requirements/new_req.md'")


if __name__ == "__main__":
    main() 