# MCP 服务器统一配置
# 产品体系构建框架 - MCP集成配置

# 服务器分组配置
server_groups:
  core:
    description: "核心功能MCP服务器"
    servers:
      - project-initialization
      - configuration-management
      - requirements-management
      - document-linking
      - content-tracing
      - workflow-management
      - task-management
      - canvas-integration

  development:
    description: "开发相关MCP服务器"
    servers:
      - code-analysis
      - testing-automation
      - documentation-generation
      - version-control

  integrations:
    description: "外部系统集成MCP服务器"
    servers:
      - github-integration
      - linear-integration
      - atlassian-integration
      - firecrawl-integration
      - im-notifier
      - context7-integration

  visualization:
    description: "可视化相关MCP服务器"
    servers:
      - diagram-generators
      - visualization-core
      - architecture-diagrams
      - reporting

  production:
    description: "生产相关MCP服务器"
    servers:
      - production-management
      - bom-management
      - quality-control

# 工作流组合配置
workflows:
  requirements-workflow:
    description: "需求管理工作流"
    includes:
      - requirements-management
      - document-linking
      - firecrawl-integration
      - im-notifier

  development-workflow:
    description: "开发管理工作流"
    includes:
      - project-initialization
      - configuration-management
      - task-management
      - code-analysis
      - testing-automation
      - github-integration

  production-workflow:
    description: "生产管理工作流"
    includes:
      - production-management
      - bom-management
      - quality-control
      - workflow-management

# 服务器详细配置
servers:
  # Core服务器
  project-initialization:
    path: "core/project-initialization.py"
    scripts_dependency: "scripts/directory_initialization/"
    description: "项目初始化和目录结构创建"

  configuration-management:
    path: "core/configuration-management.py"
    scripts_dependency: "scripts/config/"
    description: "配置文件生成与管理"

  requirements-management:
    path: "core/requirements-management.py"
    scripts_dependency: "scripts/requirements/"
    description: "需求导入、分析、矩阵维护"

  document-linking:
    path: "core/document-linking.py"
    scripts_dependency: "scripts/links/"
    description: "文档关联系统"

  content-tracing:
    path: "core/content-tracing.py"
    scripts_dependency: "scripts/infoTrace/"
    description: "内容追溯系统"

  workflow-management:
    path: "core/workflow-management.py"
    scripts_dependency: "scripts/workflow/"
    description: "工作流管理"

  task-management:
    path: "core/task-management.py"
    scripts_dependency: "scripts/task_master/"
    description: "任务管理和规划"

  canvas-integration:
    path: "core/canvas-integration.py"
    scripts_dependency: "scripts/canvas/"
    description: "Canvas同步集成"

  # Development服务器
  code-analysis:
    path: "development/code-analysis.py"
    description: "代码分析和审查"

  testing-automation:
    path: "development/testing-automation.py"
    description: "单元测试生成、测试自动化"

  documentation-generation:
    path: "development/documentation-generation.py"
    description: "文档自动生成"

  version-control:
    path: "development/version-control.py"
    description: "GitHub、版本控制集成"

  # Integration服务器
  github-integration:
    path: "integrations/github-integration.py"
    description: "GitHub深度集成"

  linear-integration:
    path: "integrations/linear-integration.py"
    description: "Linear项目管理集成"

  atlassian-integration:
    path: "integrations/atlassian-integration.py"
    description: "Jira/Confluence集成"

  firecrawl-integration:
    path: "integrations/firecrawl-integration.py"
    description: "Firecrawl网页抓取"

  im-notifier:
    path: "integrations/im-notifier.py"
    description: "飞书/钉钉通知服务"

  context7-integration:
    path: "integrations/context7-integration.py"
    description: "Context7文档集成"

  # Visualization服务器
  diagram-generators:
    path: "visualization/diagram-generators.py"
    description: "UML、AWS、C4图表生成"

  visualization-core:
    path: "visualization/visualization-core.py"
    scripts_dependency: "scripts/visualization/"
    description: "核心可视化系统"

  architecture-diagrams:
    path: "visualization/architecture-diagrams.py"
    description: "架构图生成"

  reporting:
    path: "visualization/reporting.py"
    description: "报告和仪表板生成"

  # Production服务器
  production-management:
    path: "production/production-management.py"
    scripts_dependency: "scripts/production/"
    description: "生产流程管理"

  bom-management:
    path: "production/bom-management.py"
    description: "BOM清单管理"

  quality-control:
    path: "production/quality-control.py"
    description: "质量控制和测试"

# 统一集成服务器配置
unified_servers:
  product-development-complete:
    path: "unified/product-development-complete.py"
    description: "完整功能集成(推荐)"
    includes_all: true

  requirements-workflow:
    path: "unified/requirements-workflow.py"
    description: "需求管理工作流"

  development-workflow:
    path: "unified/development-workflow.py"
    description: "开发管理工作流"

  production-workflow:
    path: "unified/production-workflow.py"
    description: "生产管理工作流"

# 环境配置
environment:
  base_path: "F:/101_link notebook/Obsidian Vault/KA/Product development/mcp-server_local_integrations"
  scripts_path: "F:/101_link notebook/Obsidian Vault/KA/Product development/scripts"
  python_executable: "python"

# Cursor配置模板
cursor_config_templates:
  simple:
    description: "简单配置 - 只使用完整集成服务器"
    servers:
      - product-development-complete

  workflow:
    description: "工作流配置 - 按工作流分组"
    servers:
      - requirements-workflow
      - development-workflow
      - production-workflow

  modular:
    description: "模块化配置 - 独立功能模块"
    servers:
      - project-initialization
      - requirements-management
      - document-linking
      - visualization-core
