#ifndef _IPROTOCOL_H_
#define _IPROTOCOL_H_

#include <QByteArray>

class IProtocol
{
public:
  IProtocol();
  virtual ~IProtocol();


  virtual QByteArray getControlCmd(const char &id) = 0; //控制指令
  virtual QByteArray getWriteCmd(const char &id, const QByteArray &w_data) = 0; //写入数据
  virtual QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) = 0; //读指令

protected:
  void calXOR(const uint8_t &xor_id, QByteArray* array_); //计算校验和
  uint8_t calXOR(uint8_t *data, const uint16_t &len);
  uint8_t calXOR(uint8_t *data, const uint16_t &len, const uint8_t &xor_index);

  uint8_t checkSum(uint8_t *p_data, uint8_t data_len); //const
};


#endif
