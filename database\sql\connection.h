#ifndef CONNECTION_H
#define CONNECTION_H

//#include <QMessageBox>
//#include <QSqlDatabase>
//#include <QSqlQuery>
//#include <QDebug>
//#include <QSqlError>

//static bool createConnection(const QString &conn, QSqlDatabase &db)
//{
//#if 1
//    qDebug() << "database drive list";
//    QStringList drivers = QSqlDatabase::drivers();
//    foreach(QString driver, drivers)
//       qDebug() << driver;
//#endif
//    /*2. 数据库链接*/
////    QString connection = "conn1";
////    QSqlDatabase db = QSqlDatabase::addDatabase("QODBC", connection);
//    db.setHostName("127.0.0.1");
//    db.setPort(3306);
//    db.setDatabaseName("2");  //ODBC中设置数据库名称
//    db.setUserName("root");
//    db.setPassword("123456");
//    if (!db.open()){
//        QMessageBox::critical(0, qApp->tr("Cannot open database"),
//                   qApp->tr("Unable to establisha database connection."
//                             ), QMessageBox::Cancel);
//        qDebug()<<"error open database because"<<db.lastError().text();
//        return false;
//    }
//#if 1
//    //以下执行相关sql语句
//    QSqlQuery query(db);

//    //查询数据库中所有表的名称
//    QStringList tables = db.tables();
//    foreach(QString table, tables)
//    {
//        qDebug()<<table;
////        query.exec("DROP TABLE " + table);
//    }
//    tables = db.tables();
//#endif
//    return true;
//}

#endif // CONNECTION_H
