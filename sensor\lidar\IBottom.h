#ifndef _IBOTTOM_H_
#define _IBOTTOM_H_

/**
 * @brief: 底板公用指令
 * const unsigned char turnOnTime::m_turn_on[4] = {0xAA, 0x55, 0xf0, 0x0f};
 * const unsigned char turnOnTime::m_turn_off[4] = {0xAA, 0x55, 0xF5, 0x0A};
 * const unsigned char turnOnTime::m_read_cmdBuffer[4] = {0xAA, 0x55, 0xF9, 0x06};
 */

#include <QMap>
#include <QByteArray>

#include "IComm.h"
#include "IProtocol.h"

class IBottom{
public:
    IBottom(){};
    virtual ~IBottom(){};

    enum ECommStep{
      eCHIP_ID,
      eMODE_CHANGE,
      eMAP_DATA,
    };

    QByteArray  m_strPre;

    virtual QByteArray portDataRead(void) = 0;
    virtual void icom_change_interface(IComm* port_) = 0;
    virtual bool readInfo(const uint8_t &id, const uint16_t &data) = 0;

    virtual bool start(const uint8_t& id, const QByteArray &data) = 0; //开始
    virtual bool stop(const QByteArray &data) = 0; //停止
    virtual bool changeSpeed(const QByteArray &data) = 0;
    virtual bool interactionParsing(QByteArray str, const int &length) = 0; //数据解析

protected:
    QMap<QString, QByteArray> m_cmd =
    {
        {"chipId", {0}}, //


        {"running", {0}},
        {"standby", {0}},
        {"turn_on", {0}},
        {"turn_off", {0}},

        {"debug_info", {0}},
        {"log_io",{0}},
        {"clk_io",{0}},
    };
    IProtocol *m_protocol_ = nullptr; //一个设备一种协议

    const static uint8_t s_turnOn_cmd[4];
    const static uint8_t s_turnOff_cmd[4];
    const static uint8_t s_standby_cmd[4];
    const static uint8_t s_running_cmd[4];


};

#endif
