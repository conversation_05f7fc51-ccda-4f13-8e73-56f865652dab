#!/usr/bin/env python3
"""
BOM清单生成脚本 - 实现T17功能
T17: 生产准备 - 生成物料清单(BOM)，包含零件信息、供应商、成本等
"""

import json
import argparse
import sys
import os
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import pandas as pd

def generate_bom_list(project_path: str = "", bom_type: str = "manufacturing", output_format: str = "excel") -> Dict[str, Any]:
    """
    生成BOM清单
    
    Args:
        project_path: 项目路径
        bom_type: BOM类型 (manufacturing, engineering, service)
        output_format: 输出格式 (excel, json, csv)
    
    Returns:
        生成结果字典
    """
    try:
        if not project_path:
            project_path = os.getcwd()
        
        project_dir = Path(project_path)
        if not project_dir.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {project_path}",
                "project_path": project_path
            }
        
        # 扫描项目文件，识别组件和材料
        components = scan_project_components(project_dir)
        
        # 生成BOM数据
        bom_data = create_bom_data(components, bom_type)
        
        # 添加成本和供应商信息
        bom_with_costs = add_cost_supplier_info(bom_data)
        
        # 生成输出文件
        output_dir = project_dir / "production" / "bom"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 根据格式保存文件
        if output_format == "excel":
            output_file = output_dir / f"BOM_{bom_type}_{timestamp}.xlsx"
            save_bom_excel(bom_with_costs, output_file)
        elif output_format == "json":
            output_file = output_dir / f"BOM_{bom_type}_{timestamp}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(bom_with_costs, f, ensure_ascii=False, indent=2)
        elif output_format == "csv":
            output_file = output_dir / f"BOM_{bom_type}_{timestamp}.csv"
            save_bom_csv(bom_with_costs, output_file)
        
        # 生成BOM摘要
        bom_summary = generate_bom_summary(bom_with_costs, bom_type)
        summary_file = output_dir / f"BOM_summary_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(bom_summary, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "project_path": str(project_dir),
            "bom_type": bom_type,
            "timestamp": datetime.now().isoformat(),
            "output_files": {
                "bom_file": str(output_file),
                "summary_file": str(summary_file)
            },
            "bom_statistics": {
                "total_components": len(bom_with_costs),
                "unique_suppliers": len(set(item.get('supplier', 'Unknown') for item in bom_with_costs)),
                "total_estimated_cost": sum(item.get('unit_cost', 0) * item.get('quantity', 1) for item in bom_with_costs),
                "categories": get_category_distribution(bom_with_costs)
            },
            "component_breakdown": get_component_breakdown(bom_with_costs),
            "cost_analysis": analyze_costs(bom_with_costs),
            "supplier_analysis": analyze_suppliers(bom_with_costs)
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"生成BOM清单时出错: {str(e)}",
            "project_path": project_path
        }

def scan_project_components(project_dir: Path) -> List[Dict[str, Any]]:
    """扫描项目组件"""
    components = []
    
    # 扫描硬件设计文件
    hardware_files = list(project_dir.glob("**/*.sch")) + list(project_dir.glob("**/*.pcb"))
    for hw_file in hardware_files:
        components.extend(extract_hardware_components(hw_file))
    
    # 扫描机械设计文件
    mechanical_files = list(project_dir.glob("**/*.step")) + list(project_dir.glob("**/*.dwg"))
    for mech_file in mechanical_files:
        components.extend(extract_mechanical_components(mech_file))
    
    # 扫描配置文件中的组件信息
    config_files = list(project_dir.glob("**/components.json")) + list(project_dir.glob("**/parts.json"))
    for config_file in config_files:
        components.extend(extract_config_components(config_file))
    
    # 如果没有找到组件，生成示例组件
    if not components:
        components = generate_sample_components()
    
    return components

def extract_hardware_components(hw_file: Path) -> List[Dict[str, Any]]:
    """从硬件设计文件提取组件"""
    # 这里是硬件文件解析的占位符
    # 实际实现需要根据具体的EDA工具格式进行解析
    
    return [
        {
            "part_number": "R001",
            "description": "电阻 10kΩ 0805",
            "category": "电子元件",
            "quantity": 10,
            "unit": "pcs",
            "source_file": str(hw_file),
            "component_type": "resistor"
        },
        {
            "part_number": "C001", 
            "description": "电容 100nF 0805",
            "category": "电子元件",
            "quantity": 5,
            "unit": "pcs",
            "source_file": str(hw_file),
            "component_type": "capacitor"
        }
    ]

def extract_mechanical_components(mech_file: Path) -> List[Dict[str, Any]]:
    """从机械设计文件提取组件"""
    # 这里是机械文件解析的占位符
    # 实际实现需要根据具体的CAD工具格式进行解析
    
    return [
        {
            "part_number": "M001",
            "description": "外壳 ABS塑料",
            "category": "机械零件",
            "quantity": 1,
            "unit": "pcs",
            "source_file": str(mech_file),
            "component_type": "enclosure"
        }
    ]

def extract_config_components(config_file: Path) -> List[Dict[str, Any]]:
    """从配置文件提取组件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        components = []
        if 'components' in config_data:
            for comp in config_data['components']:
                components.append({
                    "part_number": comp.get('part_number', 'Unknown'),
                    "description": comp.get('description', ''),
                    "category": comp.get('category', 'Other'),
                    "quantity": comp.get('quantity', 1),
                    "unit": comp.get('unit', 'pcs'),
                    "source_file": str(config_file),
                    "component_type": comp.get('type', 'generic')
                })
        
        return components
        
    except Exception:
        return []

def generate_sample_components() -> List[Dict[str, Any]]:
    """生成示例组件（当没有找到实际组件时）"""
    return [
        {
            "part_number": "MCU001",
            "description": "微控制器 STM32F103",
            "category": "电子元件",
            "quantity": 1,
            "unit": "pcs",
            "source_file": "sample_data",
            "component_type": "microcontroller"
        },
        {
            "part_number": "PCB001",
            "description": "主板 PCB 100x80mm",
            "category": "电路板",
            "quantity": 1,
            "unit": "pcs",
            "source_file": "sample_data",
            "component_type": "pcb"
        },
        {
            "part_number": "CONN001",
            "description": "连接器 USB Type-C",
            "category": "连接器",
            "quantity": 1,
            "unit": "pcs",
            "source_file": "sample_data",
            "component_type": "connector"
        }
    ]

def create_bom_data(components: List[Dict[str, Any]], bom_type: str) -> List[Dict[str, Any]]:
    """创建BOM数据"""
    bom_data = []
    
    for i, comp in enumerate(components, 1):
        bom_item = {
            "item_number": i,
            "part_number": comp.get('part_number', f'PART_{i:03d}'),
            "description": comp.get('description', ''),
            "category": comp.get('category', 'Other'),
            "quantity": comp.get('quantity', 1),
            "unit": comp.get('unit', 'pcs'),
            "reference_designator": comp.get('reference', ''),
            "component_type": comp.get('component_type', 'generic'),
            "source_file": comp.get('source_file', ''),
            "bom_type": bom_type,
            "level": 1,  # BOM层级
            "make_buy": determine_make_buy(comp),
            "critical": is_critical_component(comp)
        }
        
        bom_data.append(bom_item)
    
    return bom_data

def add_cost_supplier_info(bom_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """添加成本和供应商信息"""
    
    # 供应商数据库（示例）
    supplier_db = {
        "电子元件": {"supplier": "立创商城", "lead_time": 7},
        "机械零件": {"supplier": "机械加工厂", "lead_time": 14},
        "电路板": {"supplier": "PCB制造商", "lead_time": 10},
        "连接器": {"supplier": "连接器供应商", "lead_time": 5}
    }
    
    # 成本数据库（示例）
    cost_db = {
        "resistor": 0.05,
        "capacitor": 0.08,
        "microcontroller": 15.00,
        "pcb": 25.00,
        "connector": 3.50,
        "enclosure": 12.00,
        "generic": 1.00
    }
    
    for item in bom_data:
        category = item.get('category', 'Other')
        comp_type = item.get('component_type', 'generic')
        
        # 添加供应商信息
        supplier_info = supplier_db.get(category, {"supplier": "待确定", "lead_time": 30})
        item['supplier'] = supplier_info['supplier']
        item['lead_time_days'] = supplier_info['lead_time']
        
        # 添加成本信息
        item['unit_cost'] = cost_db.get(comp_type, 1.00)
        item['total_cost'] = item['unit_cost'] * item['quantity']
        
        # 添加其他信息
        item['currency'] = 'CNY'
        item['last_updated'] = datetime.now().isoformat()
        item['status'] = 'active'
    
    return bom_data

def determine_make_buy(component: Dict[str, Any]) -> str:
    """确定制造还是采购"""
    comp_type = component.get('component_type', 'generic')
    
    # 简单的制造/采购决策逻辑
    if comp_type in ['pcb', 'enclosure']:
        return 'make'
    else:
        return 'buy'

def is_critical_component(component: Dict[str, Any]) -> bool:
    """判断是否为关键组件"""
    comp_type = component.get('component_type', 'generic')
    return comp_type in ['microcontroller', 'pcb']

def save_bom_excel(bom_data: List[Dict[str, Any]], output_file: Path):
    """保存BOM为Excel格式"""
    df = pd.DataFrame(bom_data)
    
    # 重新排列列顺序
    column_order = [
        'item_number', 'part_number', 'description', 'category',
        'quantity', 'unit', 'unit_cost', 'total_cost', 'currency',
        'supplier', 'lead_time_days', 'make_buy', 'critical', 'status'
    ]
    
    # 只保留存在的列
    available_columns = [col for col in column_order if col in df.columns]
    df = df[available_columns]
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='BOM', index=False)
        
        # 添加格式化
        workbook = writer.book
        worksheet = writer.sheets['BOM']
        
        # 设置列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

def save_bom_csv(bom_data: List[Dict[str, Any]], output_file: Path):
    """保存BOM为CSV格式"""
    df = pd.DataFrame(bom_data)
    df.to_csv(output_file, index=False, encoding='utf-8-sig')

def generate_bom_summary(bom_data: List[Dict[str, Any]], bom_type: str) -> Dict[str, Any]:
    """生成BOM摘要"""
    total_cost = sum(item.get('total_cost', 0) for item in bom_data)
    
    return {
        "bom_overview": {
            "bom_type": bom_type,
            "total_line_items": len(bom_data),
            "total_estimated_cost": round(total_cost, 2),
            "currency": "CNY",
            "generated_date": datetime.now().isoformat()
        },
        "cost_breakdown": get_cost_breakdown(bom_data),
        "supplier_summary": get_supplier_summary(bom_data),
        "category_summary": get_category_summary(bom_data),
        "critical_components": [item for item in bom_data if item.get('critical', False)],
        "procurement_timeline": calculate_procurement_timeline(bom_data)
    }

def get_category_distribution(bom_data: List[Dict[str, Any]]) -> Dict[str, int]:
    """获取类别分布"""
    categories = {}
    for item in bom_data:
        category = item.get('category', 'Other')
        categories[category] = categories.get(category, 0) + 1
    return categories

def get_component_breakdown(bom_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """获取组件分解"""
    make_items = [item for item in bom_data if item.get('make_buy') == 'make']
    buy_items = [item for item in bom_data if item.get('make_buy') == 'buy']
    
    return {
        "make_items": len(make_items),
        "buy_items": len(buy_items),
        "make_cost": sum(item.get('total_cost', 0) for item in make_items),
        "buy_cost": sum(item.get('total_cost', 0) for item in buy_items)
    }

def analyze_costs(bom_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析成本"""
    costs = [item.get('total_cost', 0) for item in bom_data]
    
    return {
        "total_cost": sum(costs),
        "average_cost_per_item": sum(costs) / len(costs) if costs else 0,
        "highest_cost_item": max(costs) if costs else 0,
        "lowest_cost_item": min(costs) if costs else 0,
        "cost_distribution": {
            "high_cost_items": len([c for c in costs if c > 10]),
            "medium_cost_items": len([c for c in costs if 1 <= c <= 10]),
            "low_cost_items": len([c for c in costs if c < 1])
        }
    }

def analyze_suppliers(bom_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析供应商"""
    suppliers = {}
    for item in bom_data:
        supplier = item.get('supplier', 'Unknown')
        if supplier not in suppliers:
            suppliers[supplier] = {"items": 0, "total_cost": 0}
        suppliers[supplier]["items"] += 1
        suppliers[supplier]["total_cost"] += item.get('total_cost', 0)
    
    return suppliers

def get_cost_breakdown(bom_data: List[Dict[str, Any]]) -> Dict[str, float]:
    """获取成本分解"""
    breakdown = {}
    for item in bom_data:
        category = item.get('category', 'Other')
        cost = item.get('total_cost', 0)
        breakdown[category] = breakdown.get(category, 0) + cost
    return breakdown

def get_supplier_summary(bom_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """获取供应商摘要"""
    return analyze_suppliers(bom_data)

def get_category_summary(bom_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """获取类别摘要"""
    summary = {}
    for item in bom_data:
        category = item.get('category', 'Other')
        if category not in summary:
            summary[category] = {"count": 0, "total_cost": 0}
        summary[category]["count"] += 1
        summary[category]["total_cost"] += item.get('total_cost', 0)
    return summary

def calculate_procurement_timeline(bom_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算采购时间线"""
    lead_times = [item.get('lead_time_days', 30) for item in bom_data]
    
    return {
        "shortest_lead_time": min(lead_times) if lead_times else 0,
        "longest_lead_time": max(lead_times) if lead_times else 0,
        "average_lead_time": sum(lead_times) / len(lead_times) if lead_times else 0,
        "recommended_order_date": (datetime.now() - timedelta(days=max(lead_times) if lead_times else 30)).strftime('%Y-%m-%d')
    }

def main():
    parser = argparse.ArgumentParser(description='BOM清单生成工具')
    parser.add_argument('--project-path', '-p', default='', help='项目路径')
    parser.add_argument('--bom-type', '-t', default='manufacturing',
                       choices=['manufacturing', 'engineering', 'service'],
                       help='BOM类型')
    parser.add_argument('--output-format', '-f', default='excel',
                       choices=['excel', 'json', 'csv'],
                       help='输出格式')
    
    args = parser.parse_args()
    
    # 生成BOM
    result = generate_bom_list(args.project_path, args.bom_type, args.output_format)
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 返回退出码
    sys.exit(0 if result.get('success', False) else 1)

if __name__ == "__main__":
    main()
