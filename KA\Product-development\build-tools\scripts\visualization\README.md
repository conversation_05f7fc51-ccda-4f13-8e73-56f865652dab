# 文档关系可视化工具

基于**单一职责原则**和**模块化架构**设计的可视化工具集。

## 🎯 设计原则

- **单一职责原则**: 每个模块只负责特定的可视化功能
- **模块化架构**: 五大核心可视化模块相互独立
- **统一接口**: 所有模块通过标准接口协同工作
- **自动化友好**: 支持命令行调用，便于集成到工作流中
- **可扩展性**: 易于添加新的可视化模块和渲染器

## 🚀 快速使用

### 方式一：超简洁调用（推荐）

```bash
# 一键生成交互式可视化图表，自动打开浏览器
python scripts/visualization/quickviz.py test_single_layer/
```

### 方式二：指定可视化模式

```bash
# 工作流可视化
python scripts/visualization/quickviz.py test_single_layer/ --mode=workflow

# 文档关联可视化
python scripts/visualization/quickviz.py test_single_layer/ --mode=documents

# 信息追溯可视化
python scripts/visualization/quickviz.py test_single_layer/ --mode=traceability

# 进度管理可视化
python scripts/visualization/quickviz.py test_single_layer/ --mode=progress

# 产品结构可视化
python scripts/visualization/quickviz.py test_single_layer/ --mode=structure

# 综合视图（所有模式）
python scripts/visualization/quickviz.py test_single_layer/ --mode=all
```

### 方式三：生成静态图像

```bash
# 生成PNG格式
python scripts/visualization/quickviz.py test_single_layer/ --mode=workflow --output=workflow.png --format=png

# 生成SVG格式
python scripts/visualization/quickviz.py test_single_layer/ --mode=documents --output=documents.svg --format=svg

# 高分辨率PDF
python scripts/visualization/quickviz.py test_single_layer/ --mode=all --output=comprehensive.pdf --format=pdf --dpi=300
```

## 📊 五大核心可视化模块

### 1. 工作流可视化模块 (workflow)

- **功能**：显示产品开发流程组件关系
- **数据源**：workflow.json配置文件、组件依赖关系
- **特色**：事件触发链、组件状态监控、流程配置

### 2. 文档关联可视化模块 (documents)  

- **功能**：显示文档双链关系网络
- **数据源**：*_INDEX.md文件、双链引用语法
- **特色**：文档层次结构、快速导航、关联强度分析

### 3. 信息追溯可视化模块 (traceability)

- **功能**：显示需求到交付物的追溯链
- **数据源**：REF语法、块级引用关系
- **特色**：变更影响分析、覆盖率统计、缺失追溯检测

### 4. 进度管理可视化模块 (progress)

- **功能**：显示项目进度和任务状态
- **数据源**：任务文件、状态标记、时间统计
- **特色**：甘特图、资源分配、里程碑追踪

### 5. 产品结构可视化模块 (structure) 🆕

- **功能**：显示产品层次结构
- **数据源**：目录结构、__level_config.json
- **特色**：多级展示、配置导出、结构推断

## 🏗️ 模块化架构

```
scripts/visualization/
├── core/                     # 核心模块 - 统一接口和公共功能
│   ├── interfaces.py        # 标准接口定义（DataAdapter、VisualizationRenderer）
│   ├── data_adapters.py     # 多模式数据协调器
│   ├── layout_engines.py    # 布局算法引擎
│   └── component_utils.py   # 组件工具（统一的颜色、区域处理）
├── renderers/               # 渲染器模块 - 显示内容生成
│   ├── html_renderer.py     # 交互式HTML渲染器
│   └── matplotlib_renderer.py # 静态图像渲染器
├── servers/                 # 服务器模块 - Web服务
│   └── web_server.py        # HTTP服务器与API
├── contents/                # 可视化模块显示板
│   ├── workflow_module.py      # 工作流可视化数据提取器
│   ├── documents_module.py     # 文档关联可视化数据提取器
│   ├── traceability_module.py  # 信息追溯可视化数据提取器
│   ├── progress_module.py      # 进度管理可视化数据提取器
│   └── product_structure_module.py # 产品结构可视化数据提取器
├── quickviz.py              # 统一入口 - 系统协调
└── [传统脚本保留作参考]      # 向下兼容的传统脚本
```

## 📋 数据接口标准

### 统一数据模型

```python
@dataclass
class VisualizationData:
    title: str                    # 可视化标题
    mode: VisualizationMode      # 可视化模式
    nodes: List[Node]            # 节点列表
    edges: List[Edge]            # 边列表
    metadata: Dict[str, Any]     # 元数据信息
    layout_config: Optional[Dict] # 布局配置

@dataclass
class Node:
    id: str                      # 节点唯一标识
    name: str                    # 节点显示名称
    type: str                    # 节点类型
    component: str               # 所属组件
    properties: Dict[str, Any]   # 扩展属性
    x: Optional[float]           # X坐标
    y: Optional[float]           # Y坐标

@dataclass  
class Edge:
    source: str                  # 源节点ID
    target: str                  # 目标节点ID
    type: str                    # 关系类型
    properties: Dict[str, Any]   # 扩展属性
```

## 🎨 交互式功能

### Web界面特性

- ✅ **模块切换**：实时切换五种可视化模式
- ✅ **拖拽交互**：节点约束在所属组件区域内
- ✅ **缩放平移**：支持鼠标滚轮缩放和拖拽平移
- ✅ **过滤搜索**：按组件类型、关系类型、关键字过滤
- ✅ **详细信息**：鼠标悬停显示节点详细信息
- ✅ **布局算法**：力导向、层次、环形、网格多种布局
- ✅ **数据导出**：SVG、PNG、PDF多格式导出
- ✅ **实时更新**：支持数据变更的实时同步

### 组件区域管理

- **区域划分**：按组件类型自动划分可视化区域
- **组件顺序**：PROD_INFO → REQ → DES → DEV → QA → PROD → PM → DEL
- **约束拖拽**：节点只能在所属组件区域内移动
- **跨区域连接**：边可以跨区域连接不同组件的节点
- **组件标识**：区域底部显示组件代号标识

## 🔧 命令行选项

```bash
python scripts/visualization/quickviz.py <项目路径> [选项]

位置参数:
  project_path          项目路径

模式选项:
  --mode {workflow,documents,traceability,progress,structure,all}
                        可视化模式 (默认: all)

输出选项:
  --output OUTPUT       输出文件路径
  --format {html,png,svg,pdf,jpg}
                        输出格式 (默认: html)
  --figsize WIDTH HEIGHT
                        图像尺寸 (默认: 16 12)
  --dpi DPI            图像分辨率 (默认: 150)

服务器选项:
  --port PORT          本地服务器端口 (默认: 8080)
  --auto-open          自动打开浏览器 (默认: True)
  --no-auto-open       不自动打开浏览器
  
API选项:
  --api-only           仅输出JSON数据，不启动服务器

其他选项:
  -v, --verbose        显示详细信息
  -h, --help          显示帮助信息
```

## 🎛️ VSCode集成

在项目根目录的`.vscode/tasks.json`中已配置快捷任务：

```json
{
    "label": "启动可视化Web服务",
    "type": "shell",
    "command": "python",
    "args": ["scripts/visualization/quickviz.py", ".", "--mode=all"],
    "group": "build"
},
{
    "label": "生成可视化图像",
    "type": "shell", 
    "command": "python",
    "args": ["scripts/visualization/quickviz.py", ".", "--output=output/visualization.png", "--format=png"],
    "group": "build"
},
{
    "label": "API模式数据输出",
    "type": "shell",
    "command": "python", 
    "args": ["scripts/visualization/quickviz.py", ".", "--api-only"],
    "group": "test"
}
```

使用 `Ctrl+Shift+P` → `Tasks: Run Task` 快速执行。

## 📈 性能指标

- **响应时间**：交互操作 < 200ms，数据加载 < 5s
- **数据规模**：支持1000+节点，5000+边的可视化
- **并发能力**：支持10+用户同时访问
- **兼容性**：支持Chrome/Firefox/Edge现代浏览器
- **平台支持**：Windows/macOS/Linux全平台兼容

## 🔄 错误处理与监控

### 自动错误恢复

- **数据解析错误**：自动跳过损坏文件，继续处理其他数据
- **编码问题**：自动尝试多种编码格式（UTF-8、GBK、GB2312）
- **端口冲突**：自动查找可用端口（8080-8130范围）
- **模块加载错误**：单个模块失败不影响其他模块

### 日志与调试

```bash
# 启用详细日志
python scripts/visualization/quickviz.py test_single_layer/ --verbose

# 检查数据提取情况
python scripts/visualization/quickviz.py test_single_layer/ --mode=documents --api-only
```

## 🚀 扩展开发

### 添加新的可视化模块

1. **创建数据提取器**：

```python
# contents/my_module.py
class MyDataExtractor(DataAdapter):
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        # 实现数据提取逻辑
        pass
```

2. **注册新模式**：

```python
# core/interfaces.py
class VisualizationMode(Enum):
    MY_MODE = "my_mode"  # 添加新模式
```

3. **集成到系统**：

```python
# core/data_adapters.py
self.extractors[VisualizationMode.MY_MODE] = MyDataExtractor(project_path)
```

### 添加新的渲染器

```python
# renderers/my_renderer.py
class MyRenderer(VisualizationRenderer):
    def render(self, data: VisualizationData, **kwargs) -> str:
        # 实现渲染逻辑
        pass
    
    def get_supported_formats(self) -> List[str]:
        return ["my_format"]
```

## 💡 最佳实践

### 1. 项目结构建议

```
项目根目录/
├── 01_requirements/
│   └── REQ_INDEX.md      # 需求INDEX文件
├── 02_design/
│   └── DES_INDEX.md      # 设计INDEX文件
├── 03_development/
│   └── DEV_INDEX.md      # 开发INDEX文件
├── 04_testing/
│   └── QA_INDEX.md       # 测试INDEX文件
├── 05_production/
│   └── PROD_INDEX.md     # 生产INDEX文件
├── 06_deliverables/
│   └── DEL_INDEX.md      # 交付物INDEX文件
├── workflow.json         # 工作流配置
├── __level_config.json   # 产品结构配置
└── output/               # 自动创建的输出目录
```

### 2. INDEX文件格式

```markdown
# 组件INDEX

| 文档ID | 文档名称 | 文档类型 | 关联文档ID | 关系类型 | 状态 |
|--------|----------|----------|------------|----------|------|
| REQ001 | 用户需求 | 功能需求 | DES001     | 实现     | 完成 |
| REQ002 | 性能需求 | 非功能需求 | DES002   | 实现     | 进行 |
```

### 3. 信息追溯语法

```markdown
[[REF:DOC_ID:关系类型]] - 块级引用
[[REQ001:实现]] - 引用需求REQ001的实现关系
```

## 🔧 环境要求

### Python依赖

```bash
pip install pandas matplotlib networkx
```

### 系统要求

- Python 3.7+
- 现代浏览器（用于查看交互式HTML）
- 中文字体支持（Windows系统自带）

---

## 总结

重构后的可视化系统实现了：

- ✅ **五大模块**：workflow、documents、traceability、progress、structure
- ✅ **单一职责**：每个模块职责明确，易于维护扩展
- ✅ **统一接口**：标准化的数据和渲染接口
- ✅ **丰富交互**：Web界面支持实时切换和复杂交互
- ✅ **多格式输出**：支持HTML、PNG、SVG、PDF等格式
- ✅ **高性能**：支持大规模数据可视化
- ✅ **易扩展**：新模块和渲染器可轻松添加
