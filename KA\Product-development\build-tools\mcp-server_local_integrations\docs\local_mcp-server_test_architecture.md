# 本地MCP服务器统一测试架构

## 🎯 测试架构概述

本文档定义了本地开发的MCP服务器的统一测试架构，确保测试结果能够真实反映AI对话框中的实际使用情况。

### 核心原则

1. **真实性优先**: 测试必须模拟真实的AI对话框调用环境
2. **输入输出验证**: 每个工具都必须有明确的输入参数和期望输出
3. **环境一致性**: 测试环境必须与实际MCP运行环境保持一致
4. **可重现性**: 测试结果必须可重现，便于问题定位

## 🏗️ 测试架构设计

### 架构总览

本测试架构采用分层设计，支持多种MCP服务器的统一测试：

```
┌─────────────────────────────────────────────────────────────┐
│                    测试控制器                                │
├─────────────────────────────────────────────────────────────┤
│  配置管理层    │  测试执行层    │  结果验证层    │  报告生成层  │
├─────────────────────────────────────────────────────────────┤
│ 服务器配置     │ 环境准备       │ 格式验证       │ 测试报告     │
│ 测试场景配置   │ MCP客户端      │ 功能验证       │ 问题分析     │
│ 期望输出配置   │ 工具调用       │ 副作用验证     │ 改进建议     │
└─────────────────────────────────────────────────────────────┘
                           │
                    ┌─────────────┐
                    │ 测试数据层   │
                    │ 自动生成     │
                    │ 模板管理     │
                    │ 版本控制     │
                    └─────────────┘
```

### 三层测试体系

```
AI对话框调用测试 (最真实) ← 推荐使用
    ↓
MCP服务器集成测试 (模拟MCP环境)
    ↓
直接脚本调用测试 (基础验证)
```

#### 1. AI对话框环境自动化测试 (Level 1 - 推荐)
- **目的**: 自动化模拟AI对话框调用环境，验证工具在真实MCP环境中的行为
- **环境**: 自动化MCP客户端 + 真实MCP服务器 + 输入条件模拟
- **核心特性**:
  - **输入条件模拟**: 自动生成符合工具要求的输入参数
  - **输出结果验证**: 自动验证输出格式、内容完整性和正确性
  - **环境状态检查**: 验证文件创建、目录结构、配置更新等副作用
  - **错误处理验证**: 测试异常输入和边界条件的处理
- **优势**: 自动化、可重复、接近真实环境
- **实现方式**: Python MCP客户端库 + 测试框架

#### 2. MCP协议集成测试 (Level 2 - 辅助)
- **目的**: 验证MCP协议层面的通信和数据传输
- **环境**: MCP协议客户端 + 服务器通信测试
- **优势**: 验证协议兼容性和通信稳定性
- **用途**: 协议层问题诊断

#### 3. 直接脚本功能测试 (Level 3 - 基础)
- **目的**: 验证底层脚本的核心功能逻辑
- **环境**: 直接Python函数调用
- **优势**: 快速定位功能逻辑问题
- **局限**: 无法发现MCP环境特有问题

## 测试执行流程

### 1. AI对话框环境自动化测试

AI对话框模拟输入内容->AI识别tool名称->调用tool->内容输出->检查输出与直接运行脚本是否相同


## 📋 测试环境要求

### 环境配置检查清单

#### 基础环境
- [ ] Python 3.8+ 已安装
- [ ] 所有依赖包已安装 (requirements.txt)
- [ ] 工作目录设置正确
- [ ] PYTHONPATH 环境变量配置

#### MCP特定环境
- [ ] MCP服务器配置文件正确
- [ ] 路径解析逻辑验证
- [ ] 权限设置检查
- [ ] 日志输出配置

#### 外部工具依赖
- [ ] flake8 (代码风格检查)
- [ ] bandit (安全扫描)
- [ ] pylint (代码质量分析)
- [ ] 其他特定工具依赖

### 测试项目准备

#### 通用测试项目结构设计原则

##### 1. 分层架构设计


##### 2. 服务器适配层设计
- **配置适配**: 根据不同MCP服务器的工具集合动态生成测试配置
- **数据适配**: 根据工具类型自动准备相应的测试数据
- **环境适配**: 根据工具依赖自动配置测试环境
- **结果适配**: 根据工具输出格式自动验证结果

##### 3. 扩展性设计
- **插件化工具支持**: 新工具只需添加配置即可集成测试
- **模板化测试场景**: 通过模板快速生成新的测试场景
- **可配置验证规则**: 支持自定义输出验证逻辑
- **多服务器并行测试**: 支持同时测试多个MCP服务器

#### 测试数据分类与生成策略

##### 1. 按工具类型分类
- **文件操作类**: 需要文件系统结构和各种格式的测试文件
- **配置管理类**: 需要各种配置文件模板和环境变量
- **数据处理类**: 需要结构化数据和处理规则定义
- **代码分析类**: 需要源代码文件和质量检查规则
- **文档生成类**: 需要文档模板和内容数据
- **可视化类**: 需要图形数据和渲染配置

##### 2. 测试数据生成原则
- **自动化生成**: 优先使用脚本自动生成测试数据
- **模板化管理**: 使用模板系统管理不同类型的测试数据
- **参数化配置**: 通过配置文件控制测试数据的生成参数
- **版本化管理**: 测试数据随工具版本同步更新

##### 3. 数据完整性要求
- **覆盖所有工具**: 每个工具都有对应的测试数据集
- **场景完整性**: 包含正常、边界、异常等各种场景数据
- **依赖关系**: 确保工具间的数据依赖关系正确
- **环境一致性**: 测试数据在不同环境下保持一致性

## 🧪 测试输入输出规范

### 输入条件模拟

#### 1. 自动化输入参数生成
- **参数模板**: 为每个工具预定义标准输入参数模板
- **动态参数**: 根据测试环境自动生成路径、文件名等动态参数
- **边界测试**: 自动生成边界条件和异常输入用于测试
- **参数组合**: 测试不同参数组合的兼容性

#### 2. 测试环境状态模拟
- **文件系统状态**: 预创建必要的文件和目录结构
- **配置文件准备**: 自动生成测试所需的配置文件
- **权限设置**: 模拟不同的文件权限场景
- **依赖检查**: 确保外部工具依赖已安装

#### 3. 输入参数验证规则
- **必需参数验证**: 所有必需参数必须提供且类型正确
- **可选参数处理**: 验证默认值行为和可选参数效果
- **路径参数处理**:
  - 支持相对路径和绝对路径
  - 验证路径在允许目录范围内
  - 测试路径不存在时的处理策略
- **数据类型验证**: 确保参数类型符合工具要求

#### 4. 输入条件覆盖策略
- **正常场景**: 标准使用场景的输入参数
- **边界场景**: 最大值、最小值、空值等边界条件
- **异常场景**: 错误类型、无效路径、权限不足等异常输入
- **组合场景**: 多个参数的不同组合情况

### 输出结果验证

#### 1. 输出格式验证
- **JSON结构验证**: 确保输出符合预定义的JSON schema
- **字段完整性**: 验证必需字段是否存在
- **数据类型检查**: 确保各字段数据类型正确
- **格式一致性**: 验证输出格式在不同调用间保持一致

#### 2. 输出内容验证
- **功能结果验证**: 验证工具执行的实际功能是否正确完成
- **数据准确性**: 检查输出数据的准确性和完整性
- **业务逻辑验证**: 确保输出符合业务逻辑要求
- **边界值处理**: 验证边界条件下的输出正确性

#### 3. 副作用验证
- **文件系统变更**: 验证文件创建、修改、删除等操作
- **目录结构**: 检查目录创建和结构变更
- **配置文件更新**: 验证配置文件的修改内容
- **INDEX文件维护**: 检查INDEX文件的更新和链接

#### 4. 错误处理验证
- **错误信息准确性**: 验证错误信息是否准确描述问题
- **错误代码规范**: 检查错误代码是否符合规范
- **异常处理完整性**: 确保异常情况得到妥善处理
- **回滚机制**: 验证失败时的状态回滚

#### 5. 性能指标验证
- **执行时间**: 验证工具执行时间是否在合理范围内
- **资源使用**: 检查内存和CPU使用情况
- **并发处理**: 验证并发调用的处理能力
- **稳定性**: 检查长时间运行的稳定性

#### 标准输出格式

##### 成功输出格式
```json
{
    "success": true,
    "message": "操作成功描述",
    "data": {
        // 具体输出数据
        "created_files": ["file1.json", "file2.md"],
        "updated_indexes": ["DEV_INDEX.md"],
        "processed_items": 10
    },
    "metadata": {
        "execution_time": "2025-01-30 10:30:00",
        "duration_seconds": 2.5,
        "tool_version": "1.0.0"
    }
}
```

##### 失败输出格式
```json
{
    "success": false,
    "error": "错误描述",
    "error_code": "INVALID_PATH",
    "details": {
        "attempted_path": "/invalid/path",
        "reason": "路径不存在或无权限访问",
        "suggestions": ["检查路径是否正确", "确认访问权限"]
    },
    "metadata": {
        "execution_time": "2025-01-30 10:30:00",
        "tool_version": "1.0.0"
    }
}
```

#### 输出验证自动化
- **Schema验证**: 使用JSON Schema自动验证输出格式
- **内容断言**: 自动检查关键字段的值和类型
- **文件系统检查**: 自动验证文件和目录的创建/修改
- **回归测试**: 对比历史输出确保一致性

## 📊 测试报告规范

### 测试记录要求

#### 基本信息记录
- 测试时间
- 测试环境信息
- 测试工具版本
- 测试项目信息

#### 测试结果记录

每个工具的测试结果必须包含以下信息：

- **输入参数**: JSON格式的完整输入参数
- **期望输出**: 具体描述期望的输出内容、格式、数量等
- **实际输出**: 具体描述实际得到的输出内容、格式、数量等
- **测试状态**: ✅成功/⚠️部分成功/❌失败
- **问题描述**: 详细描述遇到的问题，无问题填"无"
- **修复状态**: ✅已修复/⏳待修复/❌无法修复/-

#### 测试结果表格格式

所有测试结果必须按照统一的表格格式记录：

| 序号 | 工具名称 | 分类 | 状态 | 测试时间 | 期望输出 | 实际输出 | 主要问题 | 修复状态 | 备注 |
|------|----------|------|------|----------|----------|----------|----------|----------|------|
| [序号] | [工具名称] | [分类] | [状态] | [HH:MM] | [期望输出描述] | [实际输出描述] | [问题描述] | [修复状态] | [备注] |

**表格字段说明**:
- **序号**: 测试序号，从1开始
- **工具名称**: MCP工具的完整名称
- **分类**: 项目管理/配置管理/需求管理/文档管理/代码分析/可视化
- **状态**: ✅成功/⚠️部分成功/❌失败
- **测试时间**: 测试执行时间 (HH:MM格式)
- **期望输出**: 具体描述期望的输出内容、格式、数量等
- **实际输出**: 具体描述实际得到的输出内容、格式、数量等
- **主要问题**: 描述遇到的主要问题，无问题填"无"
- **修复状态**: ✅已修复/⏳待修复/❌无法修复/-
- **备注**: 其他重要信息或说明

### 测试报告模板

参考: `local_mcp-server_test_report_template.md`

## 🚨 常见问题和解决方案

### 路径问题
- **问题**: 相对路径解析错误
- **解决**: 使用绝对路径，检查工作目录设置

### 依赖问题
- **问题**: 外部工具未安装
- **解决**: 检查依赖清单，安装缺失工具

### 权限问题
- **问题**: 文件创建/修改权限不足
- **解决**: 检查目录权限，使用适当的用户权限

### 环境变量问题
- **问题**: PYTHONPATH未正确设置
- **解决**: 在MCP配置中明确设置环境变量

## 📈 测试质量指标

### 覆盖率要求
- **工具覆盖率**: 100% (所有工具都必须测试)
- **功能覆盖率**: 90%+ (主要功能路径)
- **错误处理覆盖率**: 80%+ (常见错误场景)

### 成功率目标
- **整体成功率**: 90%+
- **核心功能成功率**: 95%+
- **回归测试成功率**: 100%

### 性能指标
- **单个工具测试时间**: < 30秒
- **完整测试套件时间**: < 10分钟
- **测试环境准备时间**: < 5分钟

## 🔄 持续改进

### 测试结果分析
- 定期分析测试失败原因
- 识别常见问题模式
- 优化测试用例设计

### 测试架构优化
- 根据实际使用反馈调整测试策略
- 增加新的测试场景
- 改进测试工具和框架

### 文档维护
- 及时更新测试文档
- 记录最佳实践
- 分享经验教训

---

*最后更新: 2025-07-03*
*版本: 1.0.0*