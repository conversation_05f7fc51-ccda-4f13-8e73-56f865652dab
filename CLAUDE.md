# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System

This project uses CMake with Qt5 and is written in C++17. The build system is configured for both Debug and Release modes.

### Build Commands

```bash
# Configure and build (from project root)
mkdir build
cd build
cmake ..
cmake --build .

# For specific build types
cmake -DCMAKE_BUILD_TYPE=Release ..
cmake -DCMAKE_BUILD_TYPE=Debug ..
```

### Build Modes

The project supports different build configurations:
- **Debug**: Full debugging symbols, all logging enabled
- **Release/MinSizeRel**: Optimized build with minimal logging
- Various logging flags can be controlled via CMake options (LOG_TO_CONSOLE, COMM_OUTPUT, etc.)

### Testing

The project includes comprehensive testing for the image processing modules:

```bash
# Run image processing tests
cd build/algorithm/imageProcessing/tests
./ImageProcessingTest

# Run specific filter tests
./verify_filters
./test_config_flow
```

## Project Architecture

### Core Structure

This is a Qt-based desktop application for laser TOF (Time of Flight) measurement and lens adjustment. The architecture follows a modular design:

- **`components/`** - UI modules and main application components
  - `lensAdjust/` - Primary lens adjustment interface
  - `lensAdjust_MEMD/` - Manual MES data entry
  - `lensAdjust_rework/` - Rework interface
  - `motorMonitor/` - Motor speed monitoring
  - `comCheck/` - Communication checking
  - `novaCalibration/` - Nova sensor calibration

- **`algorithm/`** - Core processing algorithms
  - `imageProcessing/` - Advanced image filtering and processing
  - `feedbackControl/` - PID control algorithms
  - `maxFind/` - Peak detection algorithms
  - `sort/` - Sorting algorithms

- **`sensor/`** - Hardware sensor interfaces
  - `photonSensor/` - Photon detection and spot processing
  - `lidar/` - LIDAR sensor communication
  - `laser/` - Laser control interfaces

- **`communication/`** - Hardware communication protocols
  - `commInterface/` - Low-level communication (UART, SPI, CAN, etc.)
  - `dataProtocols/` - Device-specific protocols
  - `network/` - TCP/UDP networking

- **`machine/`** - Machine control abstractions
  - `3dHandMachine/` - 3D positioning machine interfaces
  - `simpleModule/` - Basic module controls
  - `testTool/` - Testing equipment interfaces

### Key Design Patterns

1. **Factory Pattern**: Used extensively for sensor creation (`sensorBoardFactory`, `lenMachineFactory`)
2. **Strategy Pattern**: Implemented in `algorithm/maxFind/` for different peak detection strategies
3. **Interface Segregation**: Abstract interfaces like `IImageFilter`, `IComm`, `IAnalysis`
4. **Adapter Pattern**: Used in `algorithm/imageProcessing/adapters/` for legacy system integration

### Image Processing System

The image processing module is a sophisticated system with:

- **Filter Types**: Convolution, Weighted Average, Median, Gaussian, Bilateral, Kalman
- **Configuration-Driven**: Uses INI files for filter parameters
- **Factory Pattern**: `FilterFactory` and `InterpolationFactory` for object creation
- **Extensive Testing**: Comprehensive test suite with performance benchmarks

Key files:
- `algorithm/imageProcessing/factories/FilterFactory.cpp` - Filter creation
- `algorithm/imageProcessing/filters/` - All filter implementations
- `sensor/photonSensor/faculaProcessingConfig.cpp` - Configuration management

## Important Development Notes

### Version Management

- Version numbers are automatically extracted from `CHANGELOG.md`
- Python scripts in `scriptFile/` handle version management and changelog generation
- Build artifacts are named with version, date, and build type

### Configuration System

The application uses INI-based configuration files located in `build/*/bin/config/`. Key configuration includes:
- Filter parameters and presets
- Machine-specific settings
- Communication parameters

### Logging System

- Conditional compilation flags control logging levels
- Debug builds enable comprehensive logging
- Release builds minimize logging for performance
- Error logs are saved to files in release mode

### Testing Strategy

- Unit tests for image processing algorithms
- Integration tests for hardware communication
- Performance benchmarks for filter operations
- Configuration validation tests

## Development Workflow

1. **Code Changes**: Edit source files in appropriate modules
2. **Build**: Use CMake to build the project
3. **Test**: Run relevant tests from `build/*/tests/`
4. **Version**: Update `CHANGELOG.md` for version tracking
5. **Documentation**: Update component-specific README files when needed

## Hardware Integration

This software interfaces with specialized laser measurement hardware including:
- TOF sensors (VI5300, VL53L4CD)
- Stepper motors for positioning
- Photon sensors for spot detection
- Various test boards and communication interfaces

The `machine/` and `sensor/` directories contain hardware-specific implementations that should be modified carefully and tested thoroughly.