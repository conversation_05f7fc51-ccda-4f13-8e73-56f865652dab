#ifndef _MYFACTORY_H_
#define _MYFACTORY_H_

#include <QVector>
//#include "component.h"
#include "motormonitorwidget.h"
#include "mywidget.h"

class CMyFactory
{
  Q_OBJECT
public:
    CMyFactory();
    ~CMyFactory();

public:
    enum EComponent{
      eNONE               = 0x00,
      eMOTOR_MONITOR      = 0x01, //
      eCLENS_ADJUST,
      eTURN_ON_TIME,
    };

    /**
     * @brief 创建新功能窗
     */
    static QDockWidget* createComponent(EComponent component, myWidget* my_widget_)
    {
      QDockWidget* comp_ = nullptr;
      switch (component) {
        case eMOTOR_MONITOR:
          comp_ = new motorMonitorWidget;
          QObject::connect(comp_, &motorMonitorWidget::motorMonitorCloseSiganl, my_widget_, &myWidget::motorMonitorFB, Qt::AutoConnection);
          break;
    //    case eCLENS_ADJUST:
    //      comp_ = new cLensAdjust;

    //      break;
        case eTURN_ON_TIME:

          break;
        default:

          break;
        }
       return comp_;
    }

    /**
     * @brief 删除已有功能窗
     */
    static void CMyFactory::deleteComponent(QVector<CComponent*> *v_comp_, EComponent component)
    {

      switch (component) {
        case eMOTOR_MONITOR:
          QObject::disconnect(comp_, &motorMonitorWidget::motorMonitorCloseSiganl, this, &myWidget::motorMonitorFB, Qt::AutoConnection);
          break;
    //    case eCLENS_ADJUST:
    //      QObject::disconnect(comp_, &motorMonitorWidget::motorMonitorCloseSiganl, this, &myWidget::motorMonitorFB, Qt::AutoConnection);
    //      break;
    //    case eTURN_ON_TIME:
    //      QObject::disconnect(comp_, &motorMonitorWidget::motorMonitorCloseSiganl, this, &myWidget::motorMonitorFB, Qt::AutoConnection);
    //      break;
        default:

          break;
        }
      delete *v_comp_[component];
      *v_comp_[component] = nullptr;
    }

};
#endif
