#include "../common/ImageData.h"
#include "../factories/FilterFactory.h"
#include <QCoreApplication>
#include <QDebug>

using namespace ImageProcessing;

/**
 * @brief 测试配置流程是否正确（不依赖于FaculaProcessingConfig）
 */
int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    qDebug() << "========================================";
    qDebug() << "测试滤波器配置流程";
    qDebug() << "========================================";

    // 模拟配置参数
    QString  preset     = "center_weighted";  // 配置文件中的设置
    float    strength   = 1.0f;
    uint32_t kernelSize = 3;

    qDebug() << "配置参数:";
    qDebug() << "  preset:" << preset;
    qDebug() << "  strength:" << strength;
    qDebug() << "  kernelSize:" << kernelSize;

    // 1. 创建基础参数（模拟createWeightedAverageParams的功能）
    qDebug() << "\n=== 步骤1: 创建基础参数 ===";
    WeightedAverageParams params;
    params.kernelSize = kernelSize;
    params.normalize  = true;
    params.strength   = strength;
    params.enabled    = true;
    qDebug() << "基础参数创建完成";

    // 2. 创建滤波器并设置参数
    qDebug() << "\n=== 步骤2: 创建滤波器并设置基础参数 ===";
    auto &factory = FilterFactory::getInstance();
    auto  filter  = factory.createFilter(FilterType::WeightedAverage);

    if (!filter) {
        qDebug() << "创建滤波器失败";
        return 1;
    }

    filter->setParameters(params);

    // 3. 设置预定义权重（模拟faculaContext中的流程）
    qDebug() << "\n=== 步骤3: 设置预定义权重 ===";
    WeightedAverageFilter *weightedFilter = dynamic_cast<WeightedAverageFilter *>(filter.get());
    if (weightedFilter) {
        weightedFilter->setPredefinedWeights(preset);
        qDebug() << "成功设置权重:" << preset;
    } else {
        qDebug() << "类型转换失败";
        return 1;
    }

    // 4. 测试滤波效果
    qDebug() << "\n=== 步骤4: 测试滤波效果 ===";
    ImageDataU32               testData(5, 5);
    QVector<QVector<uint32_t>> testMatrix = {
        {271, 882, 826, 748, 58}, {1011, 908, 792, 756, 738}, {1074, 924, 807, 800, 859}, {1021, 877, 777, 776, 855}, {145, 887, 788, 740, 33}};

    for (uint32_t y = 0; y < 5; ++y) {
        for (uint32_t x = 0; x < 5; ++x) {
            testData.matrix()[y][x] = testMatrix[y][x];
        }
    }

    qDebug() << "原始中心点(2,2)值:" << testData.matrix()[2][2];

    bool success = filter->apply(testData);
    if (success) {
        qDebug() << "滤波后中心点(2,2)值:" << testData.matrix()[2][2];
        qDebug() << "✅ 配置流程测试成功！";

        // 验证是否使用了center_weighted权重
        if (testData.matrix()[2][2] == 818) {
            qDebug() << "✅ center_weighted权重配置生效！";
        } else {
            qDebug() << "❌ center_weighted权重配置未生效，可能使用了默认权重";
        }
    } else {
        qDebug() << "❌ 滤波失败";
        return 1;
    }

    qDebug() << "\n========================================";
    qDebug() << "配置流程测试完成";
    qDebug() << "========================================";

    return 0;
}
