# 产品流程与进度可视化轻型架构

## 文档说明

本文档定义了产品流程与进度可视化系统的轻型架构，阐述系统的设计理念、核心组件及其关系、关键算法和技术实现方法。本文档重点回答"是什么"和"如何实现"，为具体实施方案提供技术基础和参考。

## 1. 概述

本系统旨在提供产品开发全流程的可视化与进度跟踪，通过收集项目文档状态、代码仓库活动和任务进度数据，生成直观的流程图表和进度仪表板，帮助团队掌握项目全局，提高协作效率。系统采用轻量化架构设计，可无缝集成到VSCode开发环境中，支持本地化部署。

## 2. 系统架构

### 2.1 职责划分

系统按以下核心职责划分：

1. **数据采集职责**：负责从多种源获取产品开发状态数据
2. **进度分析职责**：负责计算和评估各阶段完成度和整体进度
3. **可视化展示职责**：负责生成直观的图表、仪表板和报告
4. **集成职责**：负责与其他系统（如任务管理系统）集成

### 2.2 系统层次架构

```
┌─────────────────────┐
│  数据采集层         │
│  - 文档状态采集     │
│  - 代码仓库监控     │
│  - 任务进度获取     │
└─────────┬───────────┘
          ↓
┌─────────────────────┐
│  数据处理层         │
│  - 进度计算引擎     │
│  - 阶段映射处理     │
│  - 依赖关系分析     │
└─────────┬───────────┘
          ↓
┌─────────────────────┐
│  可视化展示层       │
│  - 流程图表生成     │
│  - 进度仪表板       │
│  - Markdown报告     │
└─────────────────────┘
```

### 2.3 核心组件

1. **数据采集器** - 负责从文件系统、Git仓库和任务管理工具获取数据
2. **进度分析器** - 计算各阶段完成度和整体项目进度
3. **流程可视化器** - 生成流程图和进度仪表板
4. **VSCode集成** - 通过任务系统和自定义视图提供用户界面

## 3. 职责详述

### 3.1 数据采集职责

#### 3.1.1 文档状态采集

负责分析工作区内文档的状态和完成度，为进度计算提供基础数据。

```python
import os
import re
import json
from pathlib import Path
from datetime import datetime

def analyze_document_status(workspace_path):
    """分析工作区内各类文档的状态和完成度"""
    # 定义阶段目录与权重
    phases = {
        "product_info": {"weight": 0.1, "required_docs": ["overview.md", "market_analysis.md"]},
        "requirements": {"weight": 0.2, "required_docs": ["functional_requirements.md", "non_functional_requirements.md"]},
        "design": {"weight": 0.3, "required_docs": ["architecture.md", "detailed_design.md"]},
        "development": {"weight": 0.3, "required_docs": ["implementation.md", "testing.md"]},
        "quality": {"weight": 0.1, "required_docs": ["test_report.md", "validation.md"]}
    }
    
    results = {}
    
    # 遍历每个阶段目录
    for phase, config in phases.items():
        phase_dir = os.path.join(workspace_path, phase)
        if not os.path.exists(phase_dir):
            results[phase] = {
                "exists": False,
                "completion": 0,
                "documents": []
            }
            continue
            
        # 获取目录内所有文档
        docs = list(Path(phase_dir).glob("**/*.md"))
        all_docs = [os.path.basename(str(doc)) for doc in docs]
        
        # 计算必要文档的存在情况
        required_docs = config["required_docs"]
        required_found = [doc for doc in required_docs if doc in all_docs]
        
        # 分析文档质量和完成度
        doc_status = []
        for doc in docs:
            doc_info = analyze_single_document(str(doc))
            doc_status.append(doc_info)
            
        # 计算阶段完成度
        req_completion = len(required_found) / len(required_docs) if required_docs else 1
        doc_quality = sum([doc["completion"] for doc in doc_status]) / len(doc_status) if doc_status else 0
        
        # 综合评分 (文档存在 * 文档质量)
        completion = req_completion * 0.6 + doc_quality * 0.4
            
        results[phase] = {
            "exists": True,
            "required_docs": required_docs,
            "required_found": required_found,
            "required_completion": req_completion,
            "document_quality": doc_quality,
            "completion": completion,
            "documents": doc_status
        }
    
    # 计算整体完成度
    total_weight = sum([config["weight"] for phase, config in phases.items()])
    weighted_completion = sum([results[phase]["completion"] * phases[phase]["weight"] 
                             for phase in phases if phase in results])
    
    overall_completion = weighted_completion / total_weight if total_weight > 0 else 0
    
    return {
        "phases": results,
        "overall_completion": overall_completion,
        "timestamp": datetime.now().isoformat()
    }

def analyze_single_document(doc_path):
    """分析单个文档的质量和完成度"""
    with open(doc_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 基本信息
    filename = os.path.basename(doc_path)
    title_match = re.search(r'^# (.+)$', content, re.MULTILINE)
    title = title_match.group(1) if title_match else Path(doc_path).stem
    
    # 评估指标
    word_count = len(content.split())
    heading_count = len(re.findall(r'^#{1,6} ', content, re.MULTILINE))
    list_count = len(re.findall(r'^[*-] ', content, re.MULTILINE))
    table_count = len(re.findall(r'\|.*\|.*\|', content))
    
    # 计算简单完成度指标（可扩展为更复杂的算法）
    min_expected_words = 300  # 一个合格文档的最小字数
    max_expected_words = 3000  # 较完善文档的参考字数
    
    # 基于字数的完成度（在最小和最大期望值之间映射到0-1）
    if word_count <= min_expected_words:
        content_score = word_count / min_expected_words * 0.5  # 低于最小期望仅计部分分
    elif word_count >= max_expected_words:
        content_score = 1.0
    else:
        # 在最小到最大期望之间线性映射到0.5-1.0
        content_score = 0.5 + 0.5 * (word_count - min_expected_words) / (max_expected_words - min_expected_words)
    
    # 结构得分 - 基于标题、列表、表格等结构元素
    structure_elements = heading_count + list_count + table_count
    structure_score = min(1.0, structure_elements / 10)  # 最多10个结构元素满分
    
    # 综合评分
    completion = content_score * 0.7 + structure_score * 0.3
    
    return {
        "path": doc_path,
        "title": title,
        "filename": filename,
        "word_count": word_count,
        "heading_count": heading_count,
        "structure_elements": structure_elements,
        "completion": completion,
        "status": "completed" if completion > 0.8 else "in_progress" if completion > 0.3 else "initial"
    }
```

#### 3.1.2 任务数据采集

通过标准API从任务管理系统获取任务数据，支持灵活的数据源配置。

```python
from .base import ApiBasedDataSource

class TaskMasterDataSource(ApiBasedDataSource):
    """Task Master数据源适配器"""
    
    def __init__(self, config=None):
        """初始化Task Master数据源
        
        Args:
            config: 配置字典，包含api_url和api_key等
        """
        super().__init__(config)
        self.refresh_interval = self.config.get("refresh_interval", 300)  # 默认5分钟刷新一次
        self.cache = {}  # 用于缓存数据
        self.cache_time = {}  # 用于记录缓存时间
        
    def fetch_data(self, params=None):
        """获取Task Master数据
        
        Args:
            params: 查询参数
                endpoint: API端点
                project_id: 项目ID
                filters: 过滤条件
                
        Returns:
            获取的数据
        """
        params = params or {}
        endpoint = params.get("endpoint", "tasks")
        
        # 检查缓存
        cache_key = self._get_cache_key(params)
        if cache_key in self.cache:
            cache_time = self.cache_time.get(cache_key)
            if cache_time and (datetime.now() - cache_time).total_seconds() < self.refresh_interval:
                return self.cache[cache_key]
        
        # 构建API URL
        url = f"{self.api_url}/api/{endpoint}"
        
        # 添加查询参数
        query_params = {}
        if "project_id" in params:
            query_params["project_id"] = params["project_id"]
        if "filters" in params:
            for k, v in params["filters"].items():
                query_params[k] = v
                
        try:
            response = requests.get(
                url,
                headers=self._get_headers(),
                params=query_params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            
            # 更新缓存
            self.cache[cache_key] = data
            self.cache_time[cache_key] = datetime.now()
            self.update_last_update_time()
            
            return data
        except Exception as e:
            print(f"从Task Master获取数据失败: {e}")
            # 如果请求失败但缓存存在，则返回缓存数据
            if cache_key in self.cache:
                return self.cache[cache_key]
            return None
```

### 3.2 进度分析职责

#### 3.2.1 进度计算引擎

负责分析原始数据，计算各阶段和整体项目的完成度指标。

```python
def calculate_phase_progress(phase_data):
    """计算单个阶段的进度"""
    # 获取阶段权重
    weight = phase_data.get("weight", 1.0)
    
    # 获取文档完成情况
    doc_completion = phase_data.get("document_completion", 0.0)
    
    # 获取任务完成情况
    tasks = phase_data.get("tasks", [])
    total_tasks = len(tasks)
    completed_tasks = len([t for t in tasks if t.get("status") == "completed"])
    task_completion = completed_tasks / total_tasks if total_tasks > 0 else 0.0
    
    # 综合完成度 (文档50% + 任务50%)
    completion = doc_completion * 0.5 + task_completion * 0.5
    
    return {
        "name": phase_data.get("name", "未命名阶段"),
        "weight": weight,
        "document_completion": doc_completion,
        "task_completion": task_completion,
        "completion": completion
    }

def calculate_overall_progress(phases):
    """计算整体项目进度"""
    total_weight = sum(phase.get("weight", 1.0) for phase in phases)
    weighted_completion = sum(phase.get("completion", 0.0) * phase.get("weight", 1.0) for phase in phases)
    
    overall_completion = weighted_completion / total_weight if total_weight > 0 else 0.0
    
    return overall_completion
```

#### 3.2.2 进度预测模型

基于历史数据和当前进度预测项目的完成时间。

```python
def predict_completion_date(current_progress, start_date, target_date, velocity_history=None):
    """预测项目完成日期
    
    Args:
        current_progress: 当前进度 (0-1)
        start_date: 项目开始日期
        target_date: 目标完成日期
        velocity_history: 历史速度数据列表
    
    Returns:
        预计完成日期
    """
    from datetime import datetime, timedelta
    import numpy as np
    
    # 如果没有历史数据，使用线性预测
    if not velocity_history:
        # 已经过去的时间
        elapsed = (datetime.now() - start_date).days
        
        # 如果进度为0，无法预测
        if current_progress <= 0:
            return target_date
            
        # 根据当前进度线性预测
        total_days = elapsed / current_progress
        remaining_days = total_days - elapsed
        
        return datetime.now() + timedelta(days=remaining_days)
    
    # 如果有历史数据，使用平均速度
    else:
        # 计算平均日进度
        avg_velocity = np.mean(velocity_history)
        
        # 剩余进度
        remaining = 1.0 - current_progress
        
        # 预计剩余天数
        remaining_days = remaining / avg_velocity if avg_velocity > 0 else float('inf')
        
        return datetime.now() + timedelta(days=remaining_days)
```

### 3.3 可视化展示职责

#### 3.3.1 进度仪表板生成

负责生成直观的项目进度仪表板，包含多种图表和数据表格。

```python
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import os

def generate_progress_dashboard(status_data, output_dir="reports"):
    """生成项目进度仪表板"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取数据
    phases = status_data["phases"]
    phase_names = list(phases.keys())
    completions = [phases[p]["completion"] * 100 for p in phase_names]
    
    # 创建进度条图表
    plt.figure(figsize=(10, 6))
    
    # 水平条形图
    y_pos = np.arange(len(phase_names))
    bars = plt.barh(y_pos, completions, align='center', alpha=0.7)
    plt.yticks(y_pos, [p.capitalize() for p in phase_names])
    plt.xlabel('完成度 (%)')
    plt.title('项目各阶段完成度')
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        plt.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, 
                f'{completions[i]:.1f}%', va='center')
    
    # 设置刻度和网格
    plt.xlim(0, 105)
    plt.grid(axis='x', linestyle='--', alpha=0.6)
    
    # 保存图表
    chart_path = os.path.join(output_dir, "phase_progress.png")
    plt.tight_layout()
    plt.savefig(chart_path, dpi=300)
    plt.close()
    
    # 生成总体进度饼图
    plt.figure(figsize=(8, 8))
    overall = status_data["overall_completion"] * 100
    remaining = 100 - overall
    
    # 饼图
    colors = ['#4CAF50', '#F5F5F5']
    plt.pie([overall, remaining], labels=['已完成', '未完成'], colors=colors, 
            autopct='%1.1f%%', startangle=90, wedgeprops={"edgecolor":"white", 'linewidth': 1})
    plt.title(f'项目总体完成度: {overall:.1f}%')
    plt.axis('equal')  # 确保饼图是圆形的
    
    # 保存饼图
    pie_path = os.path.join(output_dir, "overall_progress.png")
    plt.savefig(pie_path, dpi=300)
    plt.close()
    
    # 生成文档状态表格
    doc_status_table = generate_document_status_table(status_data)
    table_path = os.path.join(output_dir, "document_status.md")
    with open(table_path, 'w', encoding='utf-8') as f:
        f.write(doc_status_table)
    
    # 生成HTML仪表板
    dashboard_html = generate_html_dashboard(status_data, chart_path, pie_path)
    html_path = os.path.join(output_dir, "progress_dashboard.html")
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(dashboard_html)
    
    return {
        "chart_path": chart_path,
        "pie_path": pie_path,
        "table_path": table_path,
        "html_path": html_path
    }
```

#### 3.3.2 流程图生成

负责生成项目流程图，直观展示项目各阶段的状态和关系。

```python
def generate_workflow_diagram(status_data, output_dir="reports"):
    """生成项目流程图"""
    # 准备Mermaid图表
    mermaid = ["```mermaid", "graph LR"]
    
    # 添加阶段节点
    phases = status_data["phases"]
    for phase_name, phase_data in phases.items():
        completion = phase_data["completion"] * 100
        color = get_color_by_completion(completion)
        
        # 根据完成度设置样式
        style = "stroke:#333,stroke-width:2px"
        if completion > 80:
            style += ",fill:" + color
        elif completion > 30:
            style += ",fill:" + color + "77"  # 半透明
        else:
            style += ",fill:#f5f5f5"
            
        node_id = phase_name.replace(" ", "_")
        mermaid.append(f'    {node_id}["{phase_name.capitalize()}<br>{completion:.1f}%"]')
        mermaid.append(f'    style {node_id} {style}')
    
    # 添加阶段间连接
    phase_names = list(phases.keys())
    for i in range(len(phase_names) - 1):
        curr = phase_names[i].replace(" ", "_")
        next_phase = phase_names[i + 1].replace(" ", "_")
        mermaid.append(f"    {curr} --> {next_phase}")
    
    # 添加子文档节点（可选，如果流程图不需要太复杂可以注释掉）
    for phase_name, phase_data in phases.items():
        if not phase_data.get("exists", False):
            continue
            
        phase_id = phase_name.replace(" ", "_")
        for doc in phase_data.get("documents", [])[:3]:  # 限制每阶段最多显示3个文档，避免图表过于复杂
            doc_id = (phase_name + "_" + doc["filename"]).replace(" ", "_").replace(".", "_")
            doc_title = doc["title"] if len(doc["title"]) < 20 else doc["title"][:17] + "..."
            completion = doc["completion"] * 100
            
            mermaid.append(f'    {doc_id}["{doc_title}<br>{completion:.1f}%"]')
            mermaid.append(f"    {phase_id} -.-> {doc_id}")
            
            # 设置文档节点样式
            color = get_color_by_completion(completion)
            mermaid.append(f'    style {doc_id} fill:{color},stroke:#333,stroke-width:1px')
    
    mermaid.append("```")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存Mermaid图
    mermaid_content = "\n".join(mermaid)
    mermaid_path = os.path.join(output_dir, "workflow_diagram.md")
    with open(mermaid_path, 'w', encoding='utf-8') as f:
        f.write("# 项目流程图\n\n")
        f.write(mermaid_content)
    
    return mermaid_path
```

### 3.4 集成职责

#### 3.4.1 数据源扩展机制

提供统一的数据源适配器框架，支持扩展不同的数据源。

```python
from abc import ABC, abstractmethod

class DataSourceAdapter(ABC):
    """数据源适配器基类
    
    所有数据源适配器都应继承此类并实现其抽象方法
    """
    
    def __init__(self, config=None):
        """初始化数据源适配器
        
        Args:
            config: 配置字典，包含数据源的配置信息
        """
        self.config = config or {}
        self.name = self.config.get("name", "未命名数据源")
        self.type = self.__class__.__name__
        self.last_update = None
        
    @abstractmethod
    def connect(self):
        """连接到数据源
        
        Returns:
            bool: 连接成功返回True，否则返回False
        """
        raise NotImplementedError("子类必须实现connect方法")
        
    @abstractmethod
    def fetch_data(self, params=None):
        """获取数据
        
        Args:
            params: 查询参数字典
            
        Returns:
            获取的数据
        """
        raise NotImplementedError("子类必须实现fetch_data方法")
```

#### 3.4.2 VSCode集成

通过VSCode任务系统集成流程可视化功能，提供良好的开发体验。

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "生成项目进度仪表板",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/workflow/generate_progress_dashboard.py",
        "--workspace=${workspaceFolder}",
        "--output=${workspaceFolder}/reports"
      ],
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    },
    {
      "label": "生成项目流程图",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/workflow/generate_workflow_diagram.py",
        "--workspace=${workspaceFolder}",
        "--output=${workspaceFolder}/reports"
      ],
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    }
  ]
}
```

#### 3.4.3 与开发规划系统集成

通过统一的数据交换格式与开发规划与管理系统集成，实现任务和文档数据的双向流通。

```python
class DataExchangeFormat:
    """系统间数据交换标准格式"""
    
    @staticmethod
    def task_data_schema() -> Dict:
        """任务数据模式
        
        Returns:
            任务数据JSON模式
        """
        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "type": "object",
            "required": ["tasks", "metadata"],
            "properties": {
                "tasks": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["id", "title", "status"],
                        "properties": {
                            "id": {"type": "string"},
                            "title": {"type": "string"},
                            "description": {"type": "string"},
                            "status": {"type": "string"},
                            "estimate": {"type": ["number", "null"]},
                            "actual": {"type": ["number", "null"]},
                            "assignee": {"type": ["string", "null"]},
                            "created_at": {"type": "string", "format": "date-time"},
                            "updated_at": {"type": "string", "format": "date-time"}
                        }
                    }
                },
                "metadata": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "string"},
                        "timestamp": {"type": "string", "format": "date-time"},
                        "version": {"type": "string"}
                    }
                }
            }
        }
```

## 4. 主程序实现

### 4.1 主脚本

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目进度可视化工具
"""

import argparse
import os
from pathlib import Path
import json
import time

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目进度可视化工具")
    parser.add_argument("--workspace", required=True, help="工作区路径")
    parser.add_argument("--output", default="reports", help="输出目录")
    args = parser.parse_args()
    
    print(f"开始分析项目: {args.workspace}")
    start_time = time.time()
    
    # 确保输出目录存在
    output_dir = args.output
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 分析文档状态
    print("分析文档状态...")
    status_data = analyze_document_status(args.workspace)
    
    # 保存状态数据为JSON
    status_path = os.path.join(output_dir, "project_status.json")
    with open(status_path, 'w', encoding='utf-8') as f:
        json.dump(status_data, f, ensure_ascii=False, indent=2)
    
    # 2. 生成进度仪表板
    print("生成进度仪表板...")
    dashboard_paths = generate_progress_dashboard(status_data, output_dir)
    
    # 3. 生成流程图
    print("生成流程图...")
    diagram_path = generate_workflow_diagram(status_data, output_dir)
    
    # 4. 生成总体报告
    print("生成项目报告...")
    report_path = generate_project_report(status_data, dashboard_paths, diagram_path, output_dir)
    
    elapsed = time.time() - start_time
    print(f"完成! 处理时间: {elapsed:.2f}秒")
    print(f"项目状态数据: {status_path}")
    print(f"进度仪表板: {dashboard_paths['html_path']}")
    print(f"流程图: {diagram_path}")
    print(f"项目报告: {report_path}")
    
    return 0
```

## 5. 技术选型

### 5.1 数据处理技术

- **编程语言**: Python 3.10+
- **数据处理**: NumPy, Pandas
- **文件格式**: JSON, Markdown, HTML

### 5.2 可视化技术

- **图表库**: Matplotlib, ECharts
- **流程图**: Mermaid
- **报告生成**: Jinja2 (HTML模板)

### 5.3 集成技术

- **API框架**: FastAPI (用于数据交换API)
- **集成接口**: RESTful API, Webhooks
- **IDE集成**: VSCode扩展API

## 6. 扩展性考虑

系统设计采用可扩展的架构，支持以下扩展点：

1. **数据源适配器** - 可添加新的数据源类型
2. **可视化组件** - 可增加新的图表和报告类型
3. **导出格式** - 支持多种导出格式
4. **指标计算器** - 可定制项目进度计算规则

## 7. 未来路线图

1. **交互式仪表板** - 基于Web的交互式仪表板
2. **实时进度更新** - 支持实时数据更新和推送通知
3. **智能进度分析** - 使用机器学习模型分析进度趋势
4. **自定义视图** - 支持用户自定义可视化视图

[[产品流程轻型可视化实施方案]]