#include "itemFlowBottom.h"


const QString CItemFlowBottom::m_red_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:red";
const QString CItemFlowBottom::m_green_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:green";
const QString CItemFlowBottom::m_grey_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:grey";
const QString CItemFlowBottom::m_yellow_SheetStyle = "min-width: 16px; min-height: 16px;max-width:16px; max-height: 16px;border-radius: 8px;  border:1px solid black;background:yellow";


CItemFlowBottom::CItemFlowBottom()
{

}

CItemFlowBottom::~CItemFlowBottom(){

}

