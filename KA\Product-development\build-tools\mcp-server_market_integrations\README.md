# 市场MCP服务器使用指南

本目录管理产品体系构建框架中使用的所有第三方市场MCP服务器的配置和使用说明。

## 📋 市场MCP服务器清单

| 服务器名称 | 功能类别 | 官方仓库 | 使用说明 | 状态 | 推荐场景 |
|-----------|----------|----------|----------|------|----------|
| **文件系统操作** | | | | | |
| [@modelcontextprotocol/server-filesystem](https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem) | 文件操作 | [GitHub](https://github.com/modelcontextprotocol/servers) |  | ✅ 推荐 | 文件读写、目录操作 |
| **版本控制** | | | | | |
| [@modelcontextprotocol/server-github](https://github.com/modelcontextprotocol/servers/tree/main/src/github) | GitHub集成 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[github-server.md]] | ✅ 推荐 | 仓库管理、PR操作 |
| [@modelcontextprotocol/server-git](https://github.com/modelcontextprotocol/servers/tree/main/src/git) | Git操作 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[git-server.md]] | ✅ 推荐 | 本地Git操作 |
| **数据库操作** | | | | | |
| [@modelcontextprotocol/server-sqlite](https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite) | SQLite数据库 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[sqlite-server.md]] | ✅ 推荐 | 数据存储查询 |
| [@modelcontextprotocol/server-postgres](https://github.com/modelcontextprotocol/servers/tree/main/src/postgres) | PostgreSQL数据库 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[postgres-server.md]] | ⚠️ 测试中 | 企业级数据库 |
| **网络和API** | | | | | |
| [@modelcontextprotocol/server-fetch](https://github.com/modelcontextprotocol/servers/tree/main/src/fetch) | HTTP请求 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[fetch-server.md]] | ✅ 推荐 | API调用、网页抓取 |
| [@modelcontextprotocol/server-brave-search](https://github.com/modelcontextprotocol/servers/tree/main/src/brave-search) | 网络搜索 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[brave-search-server.md]] | ✅ 推荐 | 网络信息搜索 |
| **开发工具** | | | | | |
| [@modelcontextprotocol/server-puppeteer](https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer) | 浏览器自动化 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[puppeteer-server.md]] | ⚠️ 测试中 | 网页自动化测试 |
| [@modelcontextprotocol/server-everart](https://github.com/modelcontextprotocol/servers/tree/main/src/everart) | 图像生成 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[everart-server.md]] | 🚧 规划中 | AI图像生成 |
| **云服务集成** | | | | | |
| [@modelcontextprotocol/server-gdrive](https://github.com/modelcontextprotocol/servers/tree/main/src/gdrive) | Google Drive | [GitHub](https://github.com/modelcontextprotocol/servers) | [[gdrive-server.md]] | ⚠️ 测试中 | 云端文件管理 |
| [@modelcontextprotocol/server-slack](https://github.com/modelcontextprotocol/servers/tree/main/src/slack) | Slack集成 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[slack-server.md]] | 🚧 规划中 | 团队协作通知 |
| **内容处理** | | | | | |
| [@modelcontextprotocol/server-memory](https://github.com/modelcontextprotocol/servers/tree/main/src/memory) | 记忆管理 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[memory-server.md]] | ✅ 推荐 | 上下文记忆 |
| [@modelcontextprotocol/server-sequential-thinking](https://github.com/modelcontextprotocol/servers/tree/main/src/sequential-thinking) | 序列思考 | [GitHub](https://github.com/modelcontextprotocol/servers) | [[sequential-thinking-server.md]] | ✅ 推荐 | 复杂问题分析 |

## 📊 状态说明

- ✅ **推荐**：已测试，稳定可用，推荐在产品开发中使用
- ⚠️ **测试中**：功能可用但需要进一步测试验证
- 🚧 **规划中**：计划集成，尚未开始配置

## 🔧 配置模板

### 基础配置模板

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/allowed/path"]
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_token_here"
      }
    },
    "sqlite": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/database.db"]
    }
  }
}
```

### 完整集成配置

```json
{
  "mcpServers": {
    // 自建服务器
    "product-development": {
      "command": "python",
      "args": ["./mcp-server_local_integrations/unified/product-development-complete/server.py"]
    },

    // 市场服务器 - 文件操作
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/workspace"]
    },

    // 市场服务器 - 版本控制
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_token"
      }
    },

    // 市场服务器 - 网络搜索
    "brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_API_KEY": "your_api_key"
      }
    },

    // 市场服务器 - 记忆管理
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    }
  }
}
```

## 📚 使用指南

### 1. 选择合适的服务器

根据项目需求选择合适的市场MCP服务器：

- **文件操作**：使用 filesystem 服务器
- **版本控制**：使用 github 或 git 服务器
- **数据存储**：使用 sqlite 或 postgres 服务器
- **网络功能**：使用 fetch 或 brave-search 服务器
- **AI增强**：使用 memory 或 sequential-thinking 服务器

### 2. 配置环境变量

某些服务器需要API密钥或访问令牌：

```bash
# GitHub访问令牌
export GITHUB_PERSONAL_ACCESS_TOKEN="your_github_token"

# Brave搜索API密钥
export BRAVE_API_KEY="your_brave_api_key"

# Google Drive凭据
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/credentials.json"
```

### 3. 测试配置

配置完成后，重启Cursor并测试MCP服务器：

```
# 测试文件系统服务器
"请列出当前目录下的所有文件"

# 测试GitHub服务器
"请查看我的GitHub仓库列表"

# 测试搜索服务器
"请搜索关于MCP协议的最新信息"
```

## 🔄 更新和维护

### 定期更新

市场MCP服务器会定期更新，建议：

1. **每月检查**：检查是否有新版本发布
2. **测试验证**：在测试环境中验证新版本
3. **逐步升级**：先在非关键项目中测试

### 版本管理

```bash
# 查看已安装版本
npx @modelcontextprotocol/server-filesystem --version

# 更新到最新版本
npx -y @modelcontextprotocol/server-filesystem@latest
```

## 📝 贡献指南

如果您发现新的有用的市场MCP服务器，请：

1. 在对应功能类别下添加服务器信息
2. 创建详细的使用说明文档
3. 测试验证功能可用性
4. 更新配置模板

## 🔗 相关资源

- [MCP官方服务器仓库](https://github.com/modelcontextprotocol/servers)
- [MCP协议规范](https://github.com/modelcontextprotocol)
- [产品体系MCP Server](../产品体系MCP%20Server.md)
- [自建MCP服务器](../mcp-server_local_integrations/)
