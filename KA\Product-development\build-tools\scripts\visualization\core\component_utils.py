#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
组件管理工具

统一处理组件相关的工具函数，避免在不同模块中重复实现
现在从共享配置文件导入组件信息
"""

import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

# 导入共享配置
sys.path.append(str(Path(__file__).parent.parent.parent))
from shared_config import (
    ALL_COMPONENTS, 
    get_component_info, 
    get_ordered_components,
    get_component_color,
    get_relation_color,
    infer_component_from_directory,
    extract_component_from_id,
    RELATION_COLORS,
    VISUALIZATION_MODE_COMPONENTS
)

class ComponentManager:
    """组件管理器 - 单一职责：管理所有组件相关逻辑"""
    
    def __init__(self):
        # 使用共享配置中的组件信息
        self.components = {comp_id: comp_info for comp_id, comp_info in ALL_COMPONENTS.items()}
        self.relation_colors = RELATION_COLORS
    
    def get_component_info(self, component_id: str):
        """获取组件信息"""
        return get_component_info(component_id)
    
    def get_component_color(self, component_id: str) -> str:
        """获取组件颜色"""
        return get_component_color(component_id)
    
    def get_ordered_components(self) -> List[str]:
        """获取按序排列的组件列表"""
        return get_ordered_components()
    
    def get_relation_color(self, relation_type: str) -> str:
        """获取关系类型颜色"""
        return get_relation_color(relation_type)
    
    def infer_component_from_directory(self, directory_name: str) -> str:
        """从目录名推断组件类型"""
        return infer_component_from_directory(directory_name)
    
    def extract_component_from_id(self, item_id: str) -> str:
        """从ID中提取组件类型"""
        return extract_component_from_id(item_id)
    
    def get_components_for_mode(self, mode: str) -> List[str]:
        """获取指定模式下应该显示的组件列表"""
        return VISUALIZATION_MODE_COMPONENTS.get(mode, get_ordered_components())
    
    def calculate_component_layout(self, data, layout_type="component_grid"):
        """计算组件布局"""
        if layout_type != "component_grid":
            return None
        
        # 使用共享配置中的有序组件列表
        ordered_components = [comp for comp in self.get_ordered_components()
                            if any(node.component == comp for node in data.nodes)]
        
        if not ordered_components:
            return None
        
        areas = {}
        area_width = 800 / len(ordered_components)
        
        for i, component in enumerate(ordered_components):
            areas[component] = {
                "x": i * area_width,
                "y": 0,
                "width": area_width,
                "height": 600
            }
        
        return areas
    
    def generate_component_statistics(self, nodes, edges):
        """生成组件统计信息"""
        stats = {}
        
        # 统计节点
        for node in nodes:
            component = getattr(node, 'component', None) or self.extract_component_from_id(getattr(node, 'id', ''))
            if component:
                if component not in stats:
                    stats[component] = {"nodes": 0, "edges": 0}
                stats[component]["nodes"] += 1
        
        # 统计边
        for edge in edges:
            source_comp = getattr(edge, 'source_component', None)
            target_comp = getattr(edge, 'target_component', None)
            
            if source_comp and source_comp in stats:
                stats[source_comp]["edges"] += 1
            if target_comp and target_comp in stats:
                stats[target_comp]["edges"] += 1
        
        return stats

# 创建全局组件管理器实例
component_manager = ComponentManager() 