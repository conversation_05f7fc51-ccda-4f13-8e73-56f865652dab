# 项目文档

### 构建文档

```bash
npm run docs
```

## 文档结构

```
docs/
├── demands/                # 需求文档
│   ├── user_stories.md     # 用户故事
│   ├── use_cases.md        # 用例
│   └── functional_specs.md # 功能规格说明书
├── development/            # 开发文档
│   ├── architecture.md     # 架构设计文档
│   └── performance.md      # 性能设计文档
├── support/                # 外部信息支持文档
│   ├── xx.md               # xx功能参考资料
│   └── xx.md               # xx参考资料
└── output/                 # 输出文档
    ├── user_manual.md      # 用户手册
    ├── api                 # 接口文档
    └── release_notes.md    # 发布说明
```

## 贡献指南

1. 遵循[Git提交规范](https://www.conventionalcommits.org/)
2. 更新相关文档
3. 添加单元测试
4. 提交Pull Request

## 许可证

[CSPC License](License.md)
