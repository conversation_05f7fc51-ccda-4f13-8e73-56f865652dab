#include <QSerialPort>
#include "motorTestB.h"
//#include ""

const QMap<QString, QVector<unsigned char>> CMotorTestB::s_motorB_cmd = {
  {"start_cmd", {0xA5, 0x02, 0x03, 0x11}},
  {"stop_cmd", {0xA5, 0x02, 0x03, 0x11}},
  {"test_cmd", {0xA5, 0x02, 0x03, 0x11}},
};


CMotorTestB::CMotorTestB(){


}

CMotorTestB::~CMotorTestB(){

}

/**
 * @brief start
 * @return
 */
bool CMotorTestB::start(QSerialPort* serial_port)
{

  return true;
}

/**
 * @brief stop
 * @return
 */
bool CMotorTestB::stop()
{

  return true;
}

void startCmdSend()
{
    /*指令发送*/
    uint8_t xor_tmp = 0;
    uint16_t fg_timers = 0;
    switch (m_function_select) { //功能模式调整
    case 1: //motor monitor
//        m_motorMoniSerial_->m_task_id = 1;
        m_motorMoniSerial_->task_id_change(motorMonitorSerial::motor);
        m_start_cmd[6] = mst_config_->pwm_radio & 0xff;

        switch (mst_config_->mode_index + 1) {
        case 1: //循环
            m_start_cmd[2] = 0xA1;

            for(uint8_t i = 0; i < m_start_cmd.length(); ++i) //
            {
                if(i != 3)
                {
                    xor_tmp ^= m_start_cmd[i];
                }
            }
            m_start_cmd[3] = xor_tmp;
            m_motorMoniSerial_->m_serial_port->write(m_start_cmd);
            break;
        case 2: //定时模式
            mst_config_->monitor_limit_time = ui->monitorTime->currentText().toInt();
            m_start_cmd[2] = 0xA0;
            m_start_cmd[7] = ui->monitorTime->currentText().toInt() & 0xff;
            m_start_cmd[8] = 0x00;
            mst_config_->monitor_limit_time = m_start_cmd[7];

            for(uint8_t i = 0; i < m_start_cmd.length(); ++i) //
            {
                if(i != 3)
                {
                    xor_tmp ^= m_start_cmd[i];
                }
            }
            m_start_cmd[3] = xor_tmp;
            m_motorMoniSerial_->m_serial_port->write(m_start_cmd);
            break;
        case 3: //定圈
            mst_config_->monitor_limit_cycle_num = ui->monitorCycle->currentText().toInt();
            m_start_cmd[2] = 0xA2;
            fg_timers = ui->monitorCycle->currentText().toInt()*3;
            m_start_cmd[7] = fg_timers & 0xff;
            m_start_cmd[8] = (fg_timers>>8) & 0xff;
            for(uint8_t i = 0; i < m_start_cmd.length(); ++i) //
            {
                if(i != 3)
                {
                    xor_tmp ^= m_start_cmd[i];
                }
            }
            m_start_cmd[3] = xor_tmp;
            m_motorMoniSerial_->m_serial_port->write(m_start_cmd);
    #ifdef  START_BOTTON_BLOCK
            ui->startButton->setDisabled(true);
    #endif
            break;
        default:
            break;
        }
        break;
     case 2: //lidar speed monitor
//        m_motorMoniSerial_->m_task_id = 2;
        m_motorMoniSerial_->task_id_change(motorMonitorSerial::bottom);
        m_motorMoniSerial_->m_serial_port->write(QByteArray((char *)YJProtocol::s_standby_cmd, 4));
        break;
     case 3: //启动时间、高低曝光切换时间记录

        break;
     case 4: //调参与转速监控
        break;
     default:
        break;
    }
}

