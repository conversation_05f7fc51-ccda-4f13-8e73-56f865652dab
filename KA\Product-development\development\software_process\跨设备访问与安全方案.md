# 跨设备访问与安全方案

## 概述

本文档详细描述工业软件在多设备环境下的部署方案、网络架构设计、安全认证机制和数据共享策略。确保在不同电脑之间安全可靠地进行测试数据共享和远程调用。

## 网络架构设计

### 典型部署场景

#### 场景1：局域网部署
```
电脑A (开发环境)          电脑B (测试环境)
┌─────────────────┐      ┌─────────────────┐
│  VSCode/Cursor  │      │   工业软件      │
│       ↓         │ HTTPS│   (Qt/C++)     │
│  MCP客户端      │◄────►│       ↑         │
└─────────────────┘      │  MCP服务器      │
                         │  (Python)       │
                         └─────────────────┘
        网段：192.168.1.0/24
```

#### 场景2：跨网段部署
```
开发网段(192.168.1.0/24)     测试网段(192.168.10.0/24)
┌─────────────────┐      ┌─────────────────┐
│  开发环境A      │ VPN  │   测试环境B     │
│  - VSCode       │◄────►│   - 工业软件    │
│  - MCP客户端    │      │   - MCP服务器   │
└─────────────────┘      └─────────────────┘
        通过VPN隧道或防火墙端口映射
```

#### 场景3：云端-本地混合部署
```
云端管理平台              本地测试环境
┌─────────────────┐      ┌─────────────────┐
│  云端控制台     │      │   工业软件      │
│  - Web界面      │ HTTPS│   (Qt/C++)     │
│  - 任务调度     │◄────►│       ↑         │
│  - 报告存储     │      │  MCP服务器      │
└─────────────────┘      │  (本地运行)     │
                         └─────────────────┘
```

### 网络协议选择

#### HTTPS vs WSS选择矩阵

| 使用场景 | 推荐协议 | 理由 |
|---------|---------|------|
| 短期API调用 | HTTPS | 简单可靠，防火墙友好 |
| 实时状态更新 | WSS | 低延迟，双向通信 |
| 大文件传输 | HTTPS | 更好的错误恢复 |
| 移动设备接入 | HTTPS | 更好的网络兼容性 |

## 配置管理

### 服务端配置

#### 电脑B（测试环境）配置
```python
# mcp_server_config.py
MCP_CONFIG = {
    "server": {
        "host": "0.0.0.0",  # 监听所有网络接口
        "port": 3001,
        "ssl_enabled": True,
        "ssl_cert": "./certs/server.crt",
        "ssl_key": "./certs/server.key",
        "ssl_ca": "./certs/ca.crt"  # 用于客户端证书验证
    },
    "qt_app": {
        "host": "localhost",
        "port": 8080,
        "timeout": 30,
        "retry_count": 3
    },
    "security": {
        "api_key_required": True,
        "client_cert_required": False,  # 可选的客户端证书认证
        "allowed_hosts": ["*************", "*************"],  # 电脑A的IP白名单
        "rate_limit": "100/hour",
        "session_timeout": 3600
    },
    "logging": {
        "level": "INFO",
        "file": "./logs/mcp_server.log",
        "max_size": "100MB",
        "backup_count": 5
    }
}
```

#### 网络配置脚本
```bash
#!/bin/bash
# setup_network.sh - 网络环境配置脚本

# 防火墙端口开放
sudo ufw allow 3001/tcp comment "MCP Server"
sudo ufw allow 8080/tcp comment "Qt App HTTP"

# 检查网络连通性
ping -c 3 *************  # 电脑A的IP

# 生成SSL证书
if [ ! -f "./certs/server.crt" ]; then
    echo "生成SSL证书..."
    openssl req -x509 -newkey rsa:4096 -keyout ./certs/server.key -out ./certs/server.crt -days 365 -nodes \
        -subj "/C=CN/ST=State/L=City/O=Company/CN=*************"
fi

echo "网络配置完成"
```

### 客户端配置

#### 电脑A（开发环境）配置
```json
{
  "mcpServers": {
    "remote-industrial-test": {
      "command": "python",
      "args": ["-m", "mcp_client_remote"],
      "env": {
        "REMOTE_MCP_URL": "https://*************:3001",
        "MCP_API_KEY": "${REMOTE_MCP_API_KEY}",
        "SSL_VERIFY": "true",
        "SSL_CERT_PATH": "./certs/client.crt",
        "SSL_KEY_PATH": "./certs/client.key",
        "CONNECTION_TIMEOUT": "10",
        "READ_TIMEOUT": "30"
      }
    }
  }
}
```

#### 客户端连接脚本
```python
# mcp_client_remote.py
import os
import ssl
import httpx
import asyncio
from mcp.client import Client

class RemoteMCPClient:
    def __init__(self):
        self.base_url = os.getenv("REMOTE_MCP_URL")
        self.api_key = os.getenv("MCP_API_KEY")
        self.ssl_verify = os.getenv("SSL_VERIFY", "true").lower() == "true"
        
        # SSL配置
        self.ssl_context = ssl.create_default_context()
        if not self.ssl_verify:
            self.ssl_context.check_hostname = False
            self.ssl_context.verify_mode = ssl.CERT_NONE
        
        # 客户端证书（可选）
        cert_path = os.getenv("SSL_CERT_PATH")
        key_path = os.getenv("SSL_KEY_PATH")
        if cert_path and key_path:
            self.ssl_context.load_cert_chain(cert_path, key_path)
    
    async def connect(self):
        """建立到远程MCP服务器的连接"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": "MCP-Remote-Client/1.0"
        }
        
        try:
            async with httpx.AsyncClient(
                base_url=self.base_url,
                headers=headers,
                timeout=30.0,
                verify=self.ssl_context if self.ssl_verify else False
            ) as client:
                # 测试连接
                response = await client.get("/health")
                if response.status_code == 200:
                    print(f"成功连接到远程MCP服务器: {self.base_url}")
                    return client
                else:
                    raise Exception(f"连接失败: {response.status_code}")
        except Exception as e:
            print(f"连接错误: {e}")
            raise
```

## 安全认证机制

### 多层安全策略

#### 1. 传输层安全 (TLS)
```python
# TLS配置
TLS_CONFIG = {
    "version": "TLSv1.3",
    "cipher_suites": [
        "TLS_AES_256_GCM_SHA384",
        "TLS_CHACHA20_POLY1305_SHA256",
        "TLS_AES_128_GCM_SHA256"
    ],
    "cert_verification": "strict",
    "session_resumption": True,
    "compression": False  # 避免CRIME攻击
}
```

#### 2. API密钥认证
```python
class APIKeyManager:
    def __init__(self):
        self.keys = {}
        self.key_expiry = {}
        self.usage_count = {}
    
    def generate_api_key(self, user_id: str, expires_in: int = 86400) -> str:
        """生成API密钥"""
        import secrets
        import time
        
        key = f"mk_{secrets.token_urlsafe(32)}"
        self.keys[key] = {
            "user_id": user_id,
            "created_at": time.time(),
            "expires_at": time.time() + expires_in,
            "permissions": ["read", "write", "execute"]
        }
        return key
    
    def validate_key(self, api_key: str) -> dict:
        """验证API密钥"""
        import time
        
        if api_key not in self.keys:
            raise ValueError("Invalid API key")
        
        key_info = self.keys[api_key]
        if time.time() > key_info["expires_at"]:
            del self.keys[api_key]
            raise ValueError("API key expired")
        
        # 记录使用次数
        self.usage_count[api_key] = self.usage_count.get(api_key, 0) + 1
        
        return key_info
    
    def revoke_key(self, api_key: str):
        """撤销API密钥"""
        if api_key in self.keys:
            del self.keys[api_key]
        if api_key in self.usage_count:
            del self.usage_count[api_key]
```

#### 3. IP白名单机制
```python
class IPWhitelistManager:
    def __init__(self, whitelist_file: str = "allowed_ips.txt"):
        self.whitelist = set()
        self.whitelist_file = whitelist_file
        self.load_whitelist()
    
    def load_whitelist(self):
        """从文件加载IP白名单"""
        try:
            with open(self.whitelist_file, 'r') as f:
                for line in f:
                    ip = line.strip()
                    if ip and not ip.startswith('#'):
                        self.whitelist.add(ip)
        except FileNotFoundError:
            print(f"白名单文件 {self.whitelist_file} 不存在，创建默认配置")
            self.create_default_whitelist()
    
    def is_allowed(self, client_ip: str) -> bool:
        """检查IP是否在白名单中"""
        return client_ip in self.whitelist or self.is_in_subnet(client_ip)
    
    def is_in_subnet(self, client_ip: str) -> bool:
        """检查IP是否在允许的子网中"""
        import ipaddress
        allowed_subnets = [
            "***********/16",
            "10.0.0.0/8",
            "127.0.0.1/32"
        ]
        
        try:
            client = ipaddress.ip_address(client_ip)
            for subnet in allowed_subnets:
                if client in ipaddress.ip_network(subnet):
                    return True
        except ValueError:
            pass
        
        return False
    
    def add_ip(self, ip: str):
        """添加IP到白名单"""
        self.whitelist.add(ip)
        self.save_whitelist()
    
    def remove_ip(self, ip: str):
        """从白名单移除IP"""
        self.whitelist.discard(ip)
        self.save_whitelist()
    
    def save_whitelist(self):
        """保存白名单到文件"""
        with open(self.whitelist_file, 'w') as f:
            f.write("# IP白名单配置文件\n")
            f.write("# 每行一个IP地址\n")
            for ip in sorted(self.whitelist):
                f.write(f"{ip}\n")
```

### 请求认证流程

#### 完整认证序列
```python
async def authenticate_request(request):
    """完整的请求认证流程"""
    
    # 1. IP白名单检查
    client_ip = get_client_ip(request)
    if not ip_manager.is_allowed(client_ip):
        raise AuthenticationError(f"IP {client_ip} not in whitelist")
    
    # 2. API密钥验证
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise AuthenticationError("Missing or invalid Authorization header")
    
    api_key = auth_header[7:]  # 移除 "Bearer " 前缀
    key_info = api_key_manager.validate_key(api_key)
    
    # 3. 速率限制检查
    if not rate_limiter.is_allowed(client_ip, api_key):
        raise AuthenticationError("Rate limit exceeded")
    
    # 4. 权限检查
    required_permission = get_required_permission(request.path, request.method)
    if required_permission not in key_info["permissions"]:
        raise AuthenticationError(f"Insufficient permissions: {required_permission}")
    
    return key_info

def get_client_ip(request):
    """获取客户端真实IP"""
    # 处理代理服务器的情况
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(',')[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    return request.client.host
```

## 数据共享策略

### 报告存储方案

#### 方案1：中心化存储
```python
# 中心化报告服务器
class CentralizedReportStorage:
    def __init__(self, storage_path: str, backup_path: str = None):
        self.storage_path = Path(storage_path)
        self.backup_path = Path(backup_path) if backup_path else None
        self.db_connection = self.init_database()
    
    def store_report(self, task_id: str, report_data: dict, metadata: dict = None):
        """存储测试报告"""
        timestamp = datetime.now().isoformat()
        
        # 存储到文件系统
        report_file = self.storage_path / f"{task_id}_{timestamp}.json"
        with report_file.open('w', encoding='utf-8') as f:
            json.dump({
                "task_id": task_id,
                "timestamp": timestamp,
                "metadata": metadata or {},
                "data": report_data
            }, f, indent=2, ensure_ascii=False)
        
        # 存储元数据到数据库
        self.db_connection.execute(
            "INSERT INTO reports (task_id, file_path, timestamp, metadata) VALUES (?, ?, ?, ?)",
            (task_id, str(report_file), timestamp, json.dumps(metadata))
        )
        
        # 备份（可选）
        if self.backup_path:
            backup_file = self.backup_path / f"{task_id}_{timestamp}.json"
            shutil.copy2(report_file, backup_file)
    
    def get_report(self, task_id: str) -> dict:
        """获取测试报告"""
        cursor = self.db_connection.execute(
            "SELECT file_path FROM reports WHERE task_id = ? ORDER BY timestamp DESC LIMIT 1",
            (task_id,)
        )
        result = cursor.fetchone()
        
        if not result:
            raise FileNotFoundError(f"Report for task {task_id} not found")
        
        report_file = Path(result[0])
        with report_file.open('r', encoding='utf-8') as f:
            return json.load(f)
```

#### 方案2：分布式存储
```python
# 分布式报告同步
class DistributedReportSync:
    def __init__(self, nodes: list, local_storage: str):
        self.nodes = nodes  # 其他节点的地址列表
        self.local_storage = Path(local_storage)
        self.sync_interval = 300  # 5分钟同步一次
    
    async def sync_reports(self):
        """同步报告到所有节点"""
        local_reports = self.get_local_reports()
        
        for node_url in self.nodes:
            try:
                await self.sync_to_node(node_url, local_reports)
                await self.sync_from_node(node_url)
            except Exception as e:
                print(f"同步到节点 {node_url} 失败: {e}")
    
    async def sync_to_node(self, node_url: str, reports: list):
        """向节点发送本地报告"""
        async with httpx.AsyncClient() as client:
            for report_info in reports:
                response = await client.post(
                    f"{node_url}/api/reports/sync",
                    json=report_info,
                    timeout=30
                )
                response.raise_for_status()
    
    async def sync_from_node(self, node_url: str):
        """从节点获取远程报告"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{node_url}/api/reports/list")
            remote_reports = response.json()
            
            for report_info in remote_reports:
                if not self.report_exists_locally(report_info["task_id"]):
                    await self.download_report(client, node_url, report_info)
```

### 实时数据流

#### WebSocket集群方案
```python
class WebSocketCluster:
    def __init__(self):
        self.connections = {}  # 本地连接
        self.remote_nodes = []  # 远程节点
        self.message_queue = asyncio.Queue()
    
    async def broadcast_to_cluster(self, message: dict):
        """向整个集群广播消息"""
        # 本地广播
        await self.broadcast_local(message)
        
        # 远程节点广播
        for node in self.remote_nodes:
            await self.send_to_remote_node(node, message)
    
    async def send_to_remote_node(self, node_url: str, message: dict):
        """向远程节点发送消息"""
        try:
            uri = f"wss://{node_url}/ws/cluster"
            async with websockets.connect(uri) as websocket:
                await websocket.send(json.dumps(message))
        except Exception as e:
            print(f"向远程节点 {node_url} 发送消息失败: {e}")
    
    async def handle_cluster_message(self, websocket, path):
        """处理来自其他节点的消息"""
        async for message in websocket:
            try:
                data = json.loads(message)
                await self.process_remote_message(data)
            except Exception as e:
                print(f"处理远程消息失败: {e}")
```

## 故障恢复与容错

### 连接重试机制
```python
class ConnectionManager:
    def __init__(self, max_retries: int = 5, backoff_factor: float = 2.0):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.current_retry = 0
    
    async def connect_with_retry(self, connect_func, *args, **kwargs):
        """带重试的连接"""
        while self.current_retry < self.max_retries:
            try:
                return await connect_func(*args, **kwargs)
            except Exception as e:
                self.current_retry += 1
                if self.current_retry >= self.max_retries:
                    raise e
                
                wait_time = self.backoff_factor ** self.current_retry
                print(f"连接失败，{wait_time}秒后重试 (第{self.current_retry}次)...")
                await asyncio.sleep(wait_time)
        
        raise Exception("达到最大重试次数，连接失败")
    
    def reset_retry_count(self):
        """重置重试计数"""
        self.current_retry = 0
```

### 健康检查机制
```python
class HealthChecker:
    def __init__(self, check_interval: int = 30):
        self.check_interval = check_interval
        self.services = {}
        self.alerts = []
    
    def register_service(self, name: str, check_url: str, timeout: int = 10):
        """注册需要检查的服务"""
        self.services[name] = {
            "url": check_url,
            "timeout": timeout,
            "status": "unknown",
            "last_check": None,
            "failure_count": 0
        }
    
    async def start_monitoring(self):
        """开始健康检查监控"""
        while True:
            for service_name, service_info in self.services.items():
                await self.check_service_health(service_name, service_info)
            
            await asyncio.sleep(self.check_interval)
    
    async def check_service_health(self, name: str, service_info: dict):
        """检查单个服务健康状态"""
        try:
            async with httpx.AsyncClient(timeout=service_info["timeout"]) as client:
                response = await client.get(service_info["url"])
                
                if response.status_code == 200:
                    service_info["status"] = "healthy"
                    service_info["failure_count"] = 0
                else:
                    raise Exception(f"HTTP {response.status_code}")
                    
        except Exception as e:
            service_info["status"] = "unhealthy"
            service_info["failure_count"] += 1
            
            if service_info["failure_count"] >= 3:
                await self.send_alert(name, f"服务连续{service_info['failure_count']}次检查失败: {e}")
        
        service_info["last_check"] = datetime.now().isoformat()
    
    async def send_alert(self, service_name: str, message: str):
        """发送告警"""
        alert = {
            "service": service_name,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "severity": "critical"
        }
        
        self.alerts.append(alert)
        print(f"告警: {alert}")
        
        # 这里可以集成邮件、短信或其他告警系统
```

## 与其他文档的关联

- **技术实现基础**：[[MCP服务器集成与AI调用技术方案]] - MCP协议的具体技术实现
- **软件架构支持**：[[工业软件设计文档]] - Qt/C++应用的网络接口设计
- **整体测试框架**：[[功能测试一体化框架]] - 完整测试体系中的网络集成

## 总结

本跨设备访问与安全方案提供了完整的多设备部署解决方案，包括：

1. **灵活的网络架构**：支持局域网、跨网段和云端混合部署
2. **多层安全防护**：TLS加密、API密钥、IP白名单、速率限制
3. **可靠的数据共享**：中心化和分布式存储方案
4. **强大的容错能力**：连接重试、健康检查、故障恢复
5. **易于运维管理**：自动化配置、监控告警、日志记录

该方案确保了工业软件在多设备环境下的安全性、可靠性和可扩展性，为测试流程的自动化和智能化提供了坚实的网络基础。 