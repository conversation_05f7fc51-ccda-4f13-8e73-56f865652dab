# Canvas布局策略文档

**文档目的**: 定义Obsidian Canvas中文档节点和组的布局策略，确保视觉清晰、空间高效利用

**更新时间**: 2025-07-09
**版本**: v2.0 - 两阶段动态布局架构

---

## 🎯 布局设计原则

### 核心原则

1. **层次清晰**: 第一层（组件级）和第二层（项目级）在视觉上明确区分
2. **空间高效**: 合理利用Canvas空间，避免重叠和浪费
3. **扩展性**: 支持动态添加文档和组，布局自动调整
4. **一致性**: 相同类型的元素使用统一的样式和间距
5. **计算一致性**: 两阶段布局计算确保尺寸与位置的完全一致

### 视觉层次

- **第一层组件组**: 使用深色边框，较大间距，水平排列
- **第二层项目组**: 使用浅色边框，嵌套在组件组内，垂直排列
- **文档节点**: 统一尺寸，在项目组内多列布局

### 两阶段布局架构 🆕

**第一阶段：尺寸预计算**
- 预计算所有项目组的实际尺寸需求
- 基于项目组尺寸计算组件组的准确尺寸
- 确保计算逻辑与Canvas生成逻辑完全一致

**第二阶段：位置动态计算**
- 基于第一阶段的准确尺寸数据计算组件组位置
- 从左到右动态布局，确保300px标准间距
- 避免重叠和位置计算错误

---

## 📐 布局尺寸规范

### 基础尺寸参数

```python
# 文档节点
NODE_WIDTH = 400          # 文档节点宽度
NODE_HEIGHT = 300         # 文档节点高度
NODE_SPACING_X = 20       # 文档节点水平间距
NODE_SPACING_Y = 20       # 文档节点垂直间距

# 组件级组（第一层）
COMPONENT_MIN_WIDTH = 600     # 组件组最小宽度
COMPONENT_SPACING = 200       # 组件组间距
COMPONENT_MARGIN_TOP = 50     # 组件组顶部边距

# 项目级组（第二层）
PROJECT_MARGIN = 30           # 项目组边距
PROJECT_SPACING = 50          # 项目组间距
PROJECT_MIN_HEIGHT = 400      # 项目组最小高度

# Canvas整体
CANVAS_PADDING = 100          # Canvas边缘留白
MIN_COLUMNS = 2               # 最少列数
MAX_COLUMNS = 6               # 最大列数
```

### 动态尺寸计算

#### 组件级组尺寸

```python
def calculate_component_size(documents_count, nested_groups_count):
    # 宽度：确保能容纳项目组
    width = max(COMPONENT_MIN_WIDTH, 
                PROJECT_MARGIN * 2 + NODE_WIDTH + NODE_SPACING_X)
    
    # 高度：根据嵌套组数量计算
    height = (COMPONENT_MARGIN_TOP + 
              nested_groups_count * (PROJECT_MIN_HEIGHT + PROJECT_SPACING) + 
              PROJECT_MARGIN)
    
    return width, height
```

#### 项目级组尺寸

```python
def calculate_project_size(documents_count):
    # 计算需要的列数和行数
    columns = min(MAX_COLUMNS, max(MIN_COLUMNS, 
                                   int(math.sqrt(documents_count))))
    rows = math.ceil(documents_count / columns)
    
    # 计算尺寸
    width = columns * NODE_WIDTH + (columns - 1) * NODE_SPACING_X + PROJECT_MARGIN * 2
    height = rows * NODE_HEIGHT + (rows - 1) * NODE_SPACING_Y + PROJECT_MARGIN * 2 + 40  # 40px标题空间
    
    return max(width, NODE_WIDTH + PROJECT_MARGIN * 2), max(height, PROJECT_MIN_HEIGHT)
```

---

## 🎨 颜色与样式规范

### 颜色方案

```python
# 第一层组件组
COMPONENT_COLORS = {
    "border": "#2196F3",           # 深蓝色边框
    "background": "rgba(33, 150, 243, 0.05)",  # 浅蓝色背景
    "text": "#1976D2"              # 深蓝色文字
}

# 第二层项目组  
PROJECT_COLORS = {
    "border": "#4CAF50",           # 绿色边框
    "background": "rgba(76, 175, 80, 0.05)",   # 浅绿色背景
    "text": "#388E3C"              # 深绿色文字
}

# 文档节点
DOCUMENT_COLORS = {
    "REQ": "#FF9800",              # 橙色 - 需求
    "DES": "#9C27B0",              # 紫色 - 设计
    "DEV": "#607D8B",              # 蓝灰色 - 开发
    "QA": "#F44336",               # 红色 - 质量
    "DEL": "#795548",              # 棕色 - 交付
    "PM": "#3F51B5"                # 靛蓝色 - 项目管理
}
```

### 边框样式

- **组件级组**: 2px实线边框，圆角4px
- **项目级组**: 1px实线边框，圆角2px  
- **文档节点**: 1px实线边框，圆角2px

---

## 📊 两阶段布局计算策略 🆕

### 第一阶段：组件尺寸预计算

```python
def calculate_component_sizes_phase1(documents_by_component):
    """第一阶段：预计算所有组件的实际尺寸"""
    component_sizes = {}

    for component, documents in documents_by_component.items():
        # 1. 预计算项目组尺寸
        hierarchy = group_documents_hierarchical(documents, component)
        project_sizes = calculate_project_group_sizes(hierarchy["component_group"]["projects"])

        # 2. 计算组件组尺寸（与Canvas生成逻辑完全一致）
        if project_sizes:
            # 有项目组：基于项目组尺寸计算
            max_project_width = max(size_info["width"] for size_info in project_sizes.values())
            total_height = 60 + sum(size_info["height"] + PROJECT_SPACING for size_info in project_sizes.values())
            width = max(COMPONENT_MIN_WIDTH, max_project_width + PROJECT_MARGIN * 2)
            height = max(800, total_height + PROJECT_MARGIN * 2)
        else:
            # 无项目组：使用回退逻辑（与Canvas生成完全一致）
            estimated_projects = max(1, len(documents) // 3)
            columns = max(MIN_COLUMNS, min(MAX_COLUMNS, int(math.sqrt(len(documents)))))
            project_width = (columns * NODE_WIDTH + (columns - 1) * NODE_SPACING_X + PROJECT_MARGIN * 2)
            width = max(COMPONENT_MIN_WIDTH, project_width + PROJECT_MARGIN * 2)
            height = max(800, 60 + estimated_projects * (PROJECT_MIN_HEIGHT + PROJECT_SPACING) + PROJECT_MARGIN * 2 + 300)

        component_sizes[component] = {"width": width, "height": height}

    return component_sizes
```

### 第二阶段：位置动态计算

```python
def calculate_component_positions_phase2(active_components, component_sizes):
    """第二阶段：基于实际尺寸动态计算位置"""
    dynamic_areas = {}
    current_x = CANVAS_PADDING

    for component_id in active_components:
        # 使用第一阶段计算的实际尺寸
        if component_sizes and component_id in component_sizes:
            component_width = component_sizes[component_id]["width"]
        else:
            component_width = COMPONENT_MIN_WIDTH  # 回退默认值

        dynamic_areas[component_id] = {
            "x_start": current_x,
            "x_end": current_x + component_width,
            "color": get_component_color(component_id),
            "name": get_component_name(component_id),
            "max_columns": 2
        }

        # 动态累加位置：确保300px标准间距
        current_x += component_width + COMPONENT_SPACING

    return dynamic_areas
```

### 组件级布局（水平排列）- 传统方法

```python
def calculate_component_positions_legacy(components):
    positions = {}
    current_x = CANVAS_PADDING

    for i, component in enumerate(components):
        # 确保最少两列布局
        if i > 0 and i % MIN_COLUMNS == 0:
            current_x = CANVAS_PADDING  # 换行
            current_y += max_component_height + COMPONENT_SPACING

        positions[component] = {
            "x": current_x,
            "y": COMPONENT_MARGIN_TOP,
            "width": component_width,
            "height": component_height
        }

        current_x += component_width + COMPONENT_SPACING

    return positions
```

### 项目级布局（垂直排列）

```python
def calculate_project_positions(component_area, projects):
    positions = {}
    current_y = component_area["y"] + PROJECT_MARGIN
    
    for project in projects:
        positions[project] = {
            "x": component_area["x"] + PROJECT_MARGIN,
            "y": current_y,
            "width": project_width,
            "height": project_height
        }
        
        current_y += project_height + PROJECT_SPACING
    
    return positions
```

### 文档节点布局（多列排列）

```python
def calculate_document_positions(project_area, documents):
    positions = {}
    available_width = project_area["width"] - PROJECT_MARGIN * 2
    columns = min(MAX_COLUMNS, max(MIN_COLUMNS, 
                                   available_width // (NODE_WIDTH + NODE_SPACING_X)))
    
    for i, doc in enumerate(documents):
        column = i % columns
        row = i // columns
        
        positions[doc] = {
            "x": project_area["x"] + PROJECT_MARGIN + column * (NODE_WIDTH + NODE_SPACING_X),
            "y": project_area["y"] + PROJECT_MARGIN + 40 + row * (NODE_HEIGHT + NODE_SPACING_Y),
            "width": NODE_WIDTH,
            "height": NODE_HEIGHT
        }
    
    return positions
```

---

## 🔧 重叠检测与避免

### 重叠检测算法

```python
def detect_overlap(rect1, rect2):
    """检测两个矩形是否重叠"""
    return not (rect1["x"] + rect1["width"] <= rect2["x"] or
                rect2["x"] + rect2["width"] <= rect1["x"] or
                rect1["y"] + rect1["height"] <= rect2["y"] or
                rect2["y"] + rect2["height"] <= rect1["y"])

def resolve_overlaps(elements):
    """解决元素重叠问题"""
    for i in range(len(elements)):
        for j in range(i + 1, len(elements)):
            if detect_overlap(elements[i], elements[j]):
                # 调整位置避免重叠
                adjust_position(elements[j], elements[i])
```

### 自动调整策略

1. **垂直调整优先**: 优先向下移动重叠元素
2. **保持相对关系**: 调整时保持元素间的相对位置关系
3. **最小移动距离**: 使用最小的移动距离解决重叠

---

## 📋 布局验证清单

### 布局质量检查

- [ ] 所有组件组间距均匀，最少两列布局
- [ ] 项目组完全包含在组件组内部
- [ ] 文档节点完全包含在项目组内部
- [ ] 无任何元素重叠
- [ ] 颜色区分明确（组件组vs项目组）
- [ ] 整体布局平衡，无明显空白浪费

### 性能检查

- [ ] 布局计算时间 < 1秒
- [ ] Canvas文件大小 < 1MB
- [ ] 支持100+文档节点无性能问题

---

## 🔄 布局更新策略

### 增量更新

1. **新增文档**: 在对应项目组内添加，必要时扩展组尺寸
2. **删除文档**: 移除节点，收缩组尺寸，检查空组删除
3. **移动文档**: 更新组归属，重新计算相关组布局

### 全量重建

- 当组结构发生重大变化时
- 当布局参数更新时
- 当检测到严重重叠问题时

---

## 🐛 已解决的布局问题

### v2.0 修复记录 (2025-07-09)

#### 问题1：组件组重叠问题
**现象**: DEL和PM组件组出现重叠，PM组件组位置计算错误
**根因**: 两阶段布局计算不一致
- 第一阶段：`_calculate_component_size`使用简化默认尺寸(600px)
- 第二阶段：Canvas生成使用复杂回退逻辑(940px)
- 结果：位置计算基于错误的尺寸数据

**解决方案**: 统一计算逻辑
- 修改`_calculate_component_size`使用与Canvas生成完全相同的计算逻辑
- 确保两阶段计算的一致性
- 实现真正的动态布局

**验证结果**: ✅
- DEL: x=4240, width=940, 结束=5180
- PM: x=5480, width=940, 结束=6420
- 间距: 5480-5180=300px，完美间距

#### 问题2：组件组尺寸计算不准确
**现象**: 计算的组件组尺寸与实际Canvas显示不一致
**根因**: 尺寸计算方法与Canvas生成方法不同步

**解决方案**:
- 在`_calculate_component_size`中实现与Canvas生成完全相同的回退逻辑
- 处理有项目组和无项目组两种情况
- 确保计算参数完全一致

**验证结果**: ✅ 计算尺寸与Canvas显示完全一致

### 布局质量验证

#### 当前布局状态 (test_single_layer项目) - v2.0修复后
- ✅ REQ: x=100, width=940, height=1290, 结束=1340
- ✅ DES: x=1340, width=940, height=800, 结束=2280 (间距300px)
- ✅ DEV: x=2580, width=1360, height=2830, 结束=3940 (间距300px)
- ✅ DEL: x=4240, width=940, 结束=5180 (间距300px)
- ✅ PM: x=5480, width=940, 结束=6420 (间距300px)

#### 项目组边界检查 - 完全修复！
- ✅ MAIN组(y=880, height=400, 结束=1280) 完全在REQ组件组内(结束=1340) **修复成功！**
- ✅ PROJ组(x=1370, width=880, 结束=2250) 完全在DES组件组内(结束=2280)
- ✅ 所有FW/SW项目组完全在DEV组件组内(结束=3940)

#### 项目组名称规范 - 完全修复！
- ✅ `FW-fw_project_1` (原错误：FIRM-fw_project_1)
- ✅ `SW-app_project` (原错误：SOFT-app_project)
- ✅ 不再有违规的单独FIRM组

#### 性能指标
- ✅ 布局计算时间 < 0.5秒
- ✅ Canvas文件大小 < 10KB
- ✅ 支持23个文档节点无性能问题

---

**维护说明**: 本文档专注于Canvas布局策略，与group_create_princeple.md中的分组逻辑分离，遵循单一职责原则。两阶段布局架构确保了计算一致性和布局质量。
