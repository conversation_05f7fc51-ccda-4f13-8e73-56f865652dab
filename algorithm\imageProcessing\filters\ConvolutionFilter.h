#ifndef IMAGEPROCESSING_CONVOLUTIONFILTER_H
#define IMAGEPROCESSING_CONVOLUTIONFILTER_H

#include "../interfaces/IImageFilter.h"
#include "../common/ValidationUtils.h"
#include <QVector>
#include <QDebug>

namespace ImageProcessing {

/**
 * @brief 卷积滤波器实现
 * 
 * 实现通用的卷积滤波算法，支持自定义卷积核
 * 基于原有my_interPolation::Convolution_filter的逻辑重构
 */
class ConvolutionFilter : public IImageFilter {
public:
    /**
     * @brief 构造函数
     */
    ConvolutionFilter();

    /**
     * @brief 析构函数
     */
    ~ConvolutionFilter() override = default;

    // IImageFilter接口实现
    bool apply(ImageDataU32& data) override;
    bool apply(const ImageDataU32& src, ImageDataU32& dst) override;
    void setParameters(const FilterParams& params) override;
    std::unique_ptr<FilterParams> getParameters() const override;
    QString getAlgorithmName() const override;
    QString getDescription() const override;
    bool isSupported(uint32_t width, uint32_t height) const override;
    uint32_t estimateProcessingTime(uint32_t width, uint32_t height) const override;
    void reset() override;
    QString getVersion() const override;
    bool isThreadSafe() const override;
    bool supportsInPlace() const override;

    /**
     * @brief 设置预定义的卷积核
     * @param type 卷积核类型
     */
    void setPredefinedKernel(const QString& type);

    /**
     * @brief 获取支持的预定义卷积核类型
     * @return 支持的类型列表
     */
    static QStringList getSupportedKernelTypes();

private:
    ConvolutionParams params_;    ///< 卷积参数

    /**
     * @brief 应用卷积操作到单个像素
     * @param src 源图像
     * @param x 像素X坐标
     * @param y 像素Y坐标
     * @return 卷积结果
     */
    float applyConvolutionAtPixel(const ImageDataU32& src, uint32_t x, uint32_t y) const;

    /**
     * @brief 获取安全的像素值（处理边界）
     * @param src 源图像
     * @param x X坐标
     * @param y Y坐标
     * @return 像素值
     */
    uint32_t getSafePixelValue(const ImageDataU32& src, int x, int y) const;

    /**
     * @brief 归一化卷积核
     */
    void normalizeKernel();

    /**
     * @brief 创建预定义的卷积核
     * @param type 核类型
     * @return 卷积核矩阵
     */
    QVector<QVector<float>> createPredefinedKernel(const QString& type) const;

    /**
     * @brief 验证卷积参数
     * @param params 参数对象
     * @throws InvalidParameterException 如果参数无效
     */
    void validateConvolutionParams(const ConvolutionParams& params) const;

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString& message) const;
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_CONVOLUTIONFILTER_H
