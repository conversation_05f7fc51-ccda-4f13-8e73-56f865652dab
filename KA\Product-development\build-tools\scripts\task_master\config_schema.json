{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["server", "nlp", "task_decomposition", "integrations"], "properties": {"server": {"type": "object", "required": ["port", "host"], "properties": {"port": {"type": "integer", "default": 3001}, "host": {"type": "string", "default": "0.0.0.0"}}}, "nlp": {"type": "object", "required": ["model"], "properties": {"model": {"type": "string", "default": "claude-3-opus-20240229"}, "temperature": {"type": "number", "default": 0.2}, "max_tokens": {"type": "integer", "default": 4000}}}, "task_decomposition": {"type": "object", "properties": {"max_depth": {"type": "integer", "default": 3}, "min_task_size": {"type": "string", "default": "2h"}, "default_estimate_unit": {"type": "string", "enum": ["minute", "hour", "day", "week"], "default": "hour"}}}, "integrations": {"type": "object", "properties": {"linear": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "sync_interval": {"type": "integer", "default": 300}, "api_key": {"type": "string"}}}, "github": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "repositories": {"type": "array", "items": {"type": "object", "required": ["owner", "repo"], "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "branches": {"type": "array", "items": {"type": "string"}, "default": ["main"]}}}}}}}}, "data_export": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": true}, "formats": {"type": "array", "items": {"type": "string", "enum": ["json", "csv"]}, "default": ["json"]}, "destinations": {"type": "array", "items": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["file", "webhook"]}, "path": {"type": "string"}, "url": {"type": "string"}, "frequency": {"type": "string", "enum": ["realtime", "hourly", "daily"], "default": "hourly"}}}}}}}}