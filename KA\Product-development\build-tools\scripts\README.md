# Scripts工具集

## ⚠️ 重要说明

**脚本统一化完成**: 所有项目目录中的重复脚本已被清理，统一使用公共库脚本。

### 使用原则

1. **项目目录中不应存在脚本文件** - 所有脚本都在公共库中
2. **直接调用公共库脚本** - 使用完整路径调用
3. **配置统一管理** - 所有项目使用相同的组件定义和配置
4. **单一职责原则** - 每个模块只负责一个明确的功能领域
5. **内容唯一原则** - 避免功能重复，确保单一来源

## 概述

Scripts目录采用分层架构，按功能领域组织，遵循单一职责原则：

## 架构层次

```
scripts/
├── shared_config.py              # 🎯 全局配置定义
├── init_product_project.py      # 🎯 项目初始化主入口（调用11个脚本）
├── config/                       # ⚙️ 配置生成层（10个配置生成器）
│   ├── generate_document_links_config.py    # 文档关联系统配置
│   ├── generate_workflow_config.py          # 工作流系统配置
│   ├── generate_traceability_config.py      # 追溯系统配置
│   ├── generate_deliverables_config.py      # 交付物配置
│   ├── generate_production_config.py        # 生产配置
│   ├── generate_quality_config.py           # 质量保证配置
│   ├── generate_development_config.py       # 开发配置
│   ├── generate_design_config.py            # 设计配置
│   ├── generate_requirements_analysis_config.py  # 需求分析配置
│   └── generate_requirements_import_config.py    # 需求导入配置
├── directory_initialization/     # 📁 项目结构层
│   ├── init_project_structure.py           # 单层级目录结构初始化
│   └── init_multilevel_structure.py        # 多层级目录结构初始化
├── links/                        # 🔗 文档关联层
│   └── auto_link_documents.py   # 语义关联和文档注册（项目初始化时自动调用）
├── common/                       # 🏗️ 基础设施层
│   ├── config.py                # 配置管理接口
│   ├── document_scanner.py      # 通用文档扫描器
│   └── index_manager.py         # INDEX文件基础操作
├── canvas/                       # 🎨 Canvas可视化层
│   ├── auto_link_documents.py   # Canvas文档同步主脚本
│   ├── canvas_manager.py        # Canvas文件操作
│   ├── canvas_layout.py         # 布局管理
│   └── canvas_utils.py          # Canvas工具函数
├── infoTrace/                   # 📊 信息追溯层
│   └── auto_index_manager.py    # 块级INDEX管理
├── visualization/               # 📈 数据可视化层
├── documentation/               # 📚 文档生成层
├── workflow/                    # ⚡ 工作流管理层
├── requirements/                # 📋 需求管理层
├── task_master/                 # 🎯 任务管理层
├── production/                  # 🏭 生产管理层
├── type_convert/               # 🔄 类型转换层
└── integration/                # 🔌 系统集成层
```

## 核心模块职责

### 1. 配置生成层 (config/) - 专门负责生成项目配置文件

**统一配置生成器 (generate_all_configs.py)**

- **唯一职责**: 协调调用所有专门的配置生成器
- **功能**: 按需生成工作流、追溯、文档关联等系统配置，支持批量和单独生成
- **使用者**: 项目初始化、MCP Server、VSCode Tasks
- **依赖**: 各专门配置生成器

**系统级配置生成器**

- **工作流配置生成器 (generate_workflow_config.py)**: 生成事件驱动工作流配置、MCP服务器集成配置
- **追溯系统配置生成器 (generate_traceability_config.py)**: 生成组件追溯关系、块级管理、变更管理配置
- **文档关联配置生成器 (generate_document_links_config.py)**: 生成文档注册、语义关联、双链网络配置

**组件级配置生成器**

- **需求配置生成器**: requirements_import_config.py, requirements_analysis_config.py
- **设计配置生成器**: generate_design_config.py
- **开发配置生成器**: generate_development_config.py
- **质量配置生成器**: generate_quality_config.py
- **生产配置生成器**: generate_production_config.py
- **交付配置生成器**: generate_deliverables_config.py

### 2. 基础设施层 (common/) - 被其他层使用的通用功能

**统一配置管理 (config.py)**

- **唯一职责**: 提供所有脚本的统一配置接口
- **功能**: 基于shared_config.py的简化API，组件信息、布局配置、目录映射
- **使用者**: 所有其他模块
- **依赖**: shared_config.py

**文档扫描器 (document_scanner.py)**

- **唯一职责**: 通用文档发现和信息提取
- **功能**: 支持文档级和块级扫描，多种文件格式支持，智能组件推断
- **使用者**: INDEX管理器、Canvas同步、文档关联系统
- **依赖**: config.py

**INDEX管理器 (index_manager.py)**

- **唯一职责**: INDEX文件的底层CRUD操作
- **功能**: INDEX文件的创建、更新、验证，支持多种INDEX格式，备份和恢复
- **使用者**: 信息追溯、文档关联、Canvas同步
- **依赖**: config.py

### 3. Canvas可视化层 (canvas/) - 专门负责Obsidian Canvas集成

**主Canvas管理脚本 (auto_link_documents.py)**

- **唯一职责**: INDEX文件与Canvas的双向同步
- **功能**:
  - INDEX文件解析和Canvas节点生成
  - Canvas布局计算和可视化渲染
  - Canvas与INDEX的同步状态验证
- **使用者**: 项目直接调用
- **依赖**: canvas_manager.py, canvas_utils.py, common/config.py

**Canvas文件操作 (canvas_manager.py)**

- **唯一职责**: Canvas JSON文件的底层操作
- **功能**: Canvas JSON文件的读写，节点和边的CRUD操作，文件格式验证
- **使用者**: auto_link_documents.py
- **依赖**: 无

**Canvas工具函数 (canvas_utils.py)**

- **唯一职责**: Canvas特定的工具函数和布局计算
- **功能**: 路径格式化，布局计算，Canvas格式转换
- **使用者**: auto_link_documents.py
- **依赖**: common/config.py

### 4. 文档关联层 (links/) - 专门负责语义关联和文档注册

**文档关联管理 (auto_link_documents.py)**

- **唯一职责**: 文档级别的语义关联和INDEX注册
- **功能**:
  - 文档自动注册到各组件的INDEX文件
  - 基于AI语义分析的文档关联发现
  - 双链网络构建和维护
  - 为INDEX表格提供关联关系建议
- **使用者**: 项目直接调用
- **依赖**: common/document_scanner.py, common/index_manager.py

### 5. 信息追溯层 (infoTrace/) - 专门负责内容块级追溯

**块级INDEX管理 (auto_index_manager.py)**

- **唯一职责**: 文档内容块级别的信息追溯和管理
- **功能**: 内容块级别的INDEX管理，REF语法支持，追溯关系构建
- **使用者**: 需要内容追溯的项目调用
- **依赖**: common/document_scanner.py, common/index_manager.py

### 6. 其他功能层

各功能层都遵循单一职责原则，只负责自己的专业领域。

## 使用方式

### 主要入口脚本

**项目初始化**:

```bash
# 初始化新项目（唯一入口，自动调用11个脚本）
python init_product_project.py --project_path="example/test_proj1" --project_name="test_proj1" --structure_type="single_layer"

# 脚本执行流程：
# 1. 目录结构初始化（1个脚本）：directory_initialization/init_project_structure.py
# 2. 配置文件生成（10个脚本）：config/generate_*_config.py
# 3. 项目控制脚本创建
# 4. 文档关联处理：links/auto_link_documents.py（新增）

# 多层级结构初始化
python init_product_project.py --project_path="example/test_multi" --project_name="test_multi" --structure_type="multi_level"
```

**配置生成**:

```bash
# 生成所有配置文件（统一入口）
python config/generate_all_configs.py --project-path . --project-type single_layer

# 生成特定配置类型
python config/generate_all_configs.py --project-path . --config-types workflow traceability

# 单独生成工作流配置
python config/generate_workflow_config.py --project-path . --project-type single_layer
```

**需求管理**:

```bash
# 导入需求文档
python requirements/import_requirements.py --source="market_survey.xlsx" --type="market"

# 需求分析
python requirements/analyze_requirements.py --input="requirements/market_requirements/"

# 从需求矩阵生成初始任务
python integration/req_to_tasks.py --matrix="requirements/requirements_matrix.md" --output="project_management/initial_tasks.json"
```

**项目管理**:

```bash
# 生成任务分解和进度计划
python project_management/generate_tasks.py --requirements="requirements/requirements_matrix.md" --output="project_management/schedules/"

# 状态同步
python integration/sync_req_tasks.py --requirements="requirements/" --tasks="project_management/"

# 生成需求-任务追踪报告
python reporting/generate_req_task_report.py --output="reports/req_task_traceability.md"
```

**质量管理**:

```bash
# 硬件设计检查
python quality/hw_design_check.py --design="development/hardware/hw_project_1/schematic.sch"

# 代码质量检查
python quality/code_check.py --source="development/firmware/fw_project_1/src"

# 生成测试用例
python quality/generate_test_cases.py --requirements="requirements/technical_requirements/"

# 执行自动化测试
python quality/run_tests.py --project="development/firmware/fw_project_1"

# UI自动化测试
python quality/ui_test.py --app="development/software/app_project/bin/app.exe"
```

**生产管理**:

```bash
# 生成BOM清单
python production/generate_bom.py --hardware="development/hardware/hw_project_1"

# 生成生产测试流程
python production/generate_test_procedure.py --product="product_info/product_brief.md"
```

**发布管理**:

```bash
# 生成版本发布包
python release/build_release.py --version="1.0.0" --projects="development/*_projects/*"

# 生成发布文档
python doc_generation/generate_release_notes.py --version="1.0.0"
```

**文档关联管理**:

```bash
# 文档关联（唯一入口）
python links/auto_link_documents.py --register --all
```

**Canvas可视化**:

```bash
# Canvas同步（唯一入口）
python canvas/auto_link_documents.py --sync-to-canvas

# 快速可视化生成（基于Web交互式界面）
python visualization/quickviz.py <项目路径>
```

**信息追溯管理**:

```bash
# 信息追溯（唯一入口）
python infoTrace/auto_index_manager.py --scan --all
```

### 依赖关系图

```
项目脚本调用
    ↓
功能层脚本 (canvas/, links/, infoTrace/, etc.)
    ↓
基础设施层 (common/)
    ↓
全局配置 (shared_config.py)
```

## 重构完成的改进

### 1. 消除功能重复

- **文档关联**: 只保留`links/auto_link_documents.py`
- **Canvas同步**: 只保留`canvas/auto_link_documents.py`
- **INDEX管理**: `common/index_manager.py`提供基础操作，各功能层构建专业功能

### 2. 明确单一职责

- **Canvas层**: 只负责Obsidian Canvas相关功能
- **文档关联层**: 只负责语义关联和文档注册
- **信息追溯层**: 只负责块级追溯和内容管理
- **基础设施层**: 只提供被多个模块使用的通用功能

### 3. 清晰的依赖关系

- 功能层脚本通过基础设施层访问通用功能
- 避免功能层脚本之间的直接依赖
- 统一配置管理，避免配置分散

## 扩展开发

### 添加新功能层

1. 在对应功能目录下创建脚本
2. 通过`common/`模块访问基础功能
3. 遵循单一职责原则
4. 更新此README文档

### 修改基础设施

1. 修改`common/`模块时要考虑所有使用者
2. 保持向后兼容性
3. 更新相关功能层脚本

## 最佳实践

### 开发原则

- **单一职责**: 每个模块只负责一个明确的功能领域
- **内容唯一**: 避免功能重复，确保单一来源
- **依赖最小**: 只依赖必要的模块，避免循环依赖
- **接口稳定**: 保持模块接口的稳定性

### 使用原则

- **从顶层调用**: 优先使用功能层的主入口脚本
- **避免跨层调用**: 不要直接调用其他功能层的脚本
- **统一配置**: 所有配置集中在shared_config.py管理

### 维护原则

- **影响分析**: 修改基础设施层前分析对上层的影响
- **测试验证**: 修改后验证所有依赖的功能层
- **文档同步**: 保持README与实际架构的一致性

## 配置系统

### shared_config.py

核心配置文件，定义：

- 组件信息（ID、名称、目录、颜色、排序）
- 项目类型配置
- 关系颜色配置
- 向后兼容性映射

### 组件配置

```python
CORE_COMPONENTS = {
    "REQ": ComponentInfo(
        id="REQ", name="需求", directory="requirements", 
        color="#4CAF50", order=1, keywords=["requirements", "req"]
    ),
    # ... 其他组件
}
```

### Canvas布局配置

- 组件区域：700像素间距
- 节点尺寸：400x300像素
- 垂直间距：350像素
- 自动多列布局

## 故障排除

### 常见问题

**导入错误：**

- 检查Python路径设置
- 确认公共库模块存在
- 验证相对路径正确性

**配置问题：**

- 检查shared_config.py语法
- 验证组件定义完整性
- 确认目录映射正确

**文件操作失败：**

- 检查文件权限
- 确认目录存在
- 验证文件格式正确

### 调试工具

**配置调试：**

```bash
python common/debug_config.py
```

**INDEX验证：**

```bash
# 验证文档关联系统
python links/auto_link_documents.py --project-path . --validate

# 验证信息追溯系统  
python infoTrace/auto_index_manager.py --project-path . --validate
```

**Canvas验证：**

```bash
python canvas/auto_link_documents.py --project-path . --validate-sync --verbose
```

## 性能优化

- 大项目建议分批处理
- 使用增量更新减少处理时间
- 缓存常用配置和计算结果
- 并行处理独立操作

## 版本兼容性

- Python 3.8+
- 支持Windows/macOS/Linux
- Obsidian 1.0+兼容
- 向下兼容旧版本配置
