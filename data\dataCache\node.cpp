#include "node.h"


void CNode::deleteNode(StListNode* head, const int &data){
    StListNode* p = head;
    //首先判断是不是空链表
    if (p == nullptr){
//        return head;
        return;
    }
    else{
        //判断是不是删除头节点
        if (p->val == data){
            head = p->next;
            delete p;
//            return head;
            return;
        }
        else{
            //如果有该结点，遍历到待删除节点的前一节点
            while (p->next != nullptr && p->next->val != data){
                p = p->next;
            }
            //遍历完整个链表都没有待删除节点
            if (p->next == nullptr){
//                return head;
                return;
            }
            else{
                StListNode* deleteNode = p->next;
                p->next = deleteNode->next;
                delete deleteNode;
//                return head;
                return;
            }
        }
    }
}

//ST_LIST_NODE* turnOnTime::insertNode(ST_LIST_NODE* head, int data){ //返回值出错，原因？
void CNode::insertNode(StListNode* head, const int &data){ //
    StListNode* newNode = new StListNode(data);
    StListNode* p = head;
    if (p == nullptr){ //表头
        head = newNode;
    }
    else{
        while (p->next != nullptr){ //
            p = p->next;
        }
        p->next = newNode;
    }
//    return head;
}
