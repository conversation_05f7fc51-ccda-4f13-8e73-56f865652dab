#include "motorMonitorOperation.h"
#include <QSqlDatabase>
#include <qsqlquery.h>
#include <QDebug>
#include <math.h>
#include <QMessageBox>
#include <QTimerEvent>

#include "uart.h"
#include "motorTestB.h"
#include "saveExcel.h"
#include <QStandardPaths>
#include "statisticalFormula.h"


#define MOTOR_SPEED_UP_NUM      30 //启动圈数
#define MOTOR_SPEED_DOWN_NUM    2 //停止圈数
#define MOTOR_ONE_PACK_NUM      256
#define MOTOR_FG_NUM            3 //单圈fg个数
#define TIMER_MS                5

CMotorMonitorOperation::CMotorMonitorOperation(const NMotorMonitor::StUiConfig &config):
    m_timerId(0)
  , m_port_update_cnt(0)
  , mc_processList_(new TClen_process)
  , mst_task_status_(new TClen_process::StStepRecord)
  , mc_icomm_(new CUART("", 115200)) //默认配置
  , mc_itest_board_(new CMotorTestB(mc_icomm_))
  , m_uart_baud(mc_itest_board_->getUartBaud())
  , mst_config_(&config) //
  , mst_comm_status_(new StCommunicateStatus)
  , m_first_data(false)
  , mst_motor_fg_data_(new StMotorData)
  , mst_cycle_data_(new StMotorData)
  , mst_result_(new StResult)
  , mi_save_file_(new CSaveExcel)
  //, mm_result_data_(new QMap<QString,QVariant>)
  , m_message_box_(new QWidget)
{
    m_timerId = startTimer(TIMER_MS);
    //* 1.子线程（初始化就创建子线程？）
    qDebug() << "-i motor/ main thread id: " << QThread::currentThread();
    m_sub_thread_ = new QThread;// 创建线程对象

    /* 1.1 创建工作的类对象，千万不要指定给创建的对象指定父对象
   * 如果指定了: QObject::moveToThread: Cannot move objects with a parent
   */
    m_serial_thread_ = new CTestBoardSerial(nullptr, mc_itest_board_);
    m_serial_thread_->moveToThread(m_sub_thread_); //将工作的类对象移动到创建的子线程对象中

    //* 1.2 connect
    connect(dynamic_cast<CMotorTestB*>(mc_itest_board_), &CMotorTestB::dataOutput, //无法用多态实现
            this, &CMotorMonitorOperation::dataRecive);
    connect(this, &CMotorMonitorOperation::subThreadSignal,
            m_serial_thread_, &CTestBoardSerial::loop);
    connect(m_sub_thread_, &QThread::finished,
            m_serial_thread_, &QObject::deleteLater);

    //* 1.3 启动线程
    m_sub_thread_->start();

    //* 2.task list init
    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::readyStep, &CMotorMonitorOperation::readyAck, 400, 0));
    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::startStep, &CMotorMonitorOperation::startAck, 0, 0));
    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::dataStep, &CMotorMonitorOperation::dataAck, 0, 0));
    mv_task_list.append(mc_processList_->addCallbackFunction(&CMotorMonitorOperation::compStep, &CMotorMonitorOperation::compAck, 0, 0));

    //* 3.配置
    mst_motor_config.up_filter = MOTOR_SPEED_UP_NUM;
    mst_motor_config.down_filter = MOTOR_SPEED_DOWN_NUM;
    mst_motor_config.cycle_fg_num = MOTOR_FG_NUM;

    //* 3.数据库
    mm_result_data.clear();
    mm_result_data.insert("时间", ""); //无法排序
    mm_result_data.insert("平均转速/hz", "");
    mm_result_data.insert("最大转速/hz", "");
    mm_result_data.insert("最大转速偏差", "");
    mm_result_data.insert("最小转速/hz", "");
    mm_result_data.insert("最小转速偏差", "");
    mm_result_data.insert("标准差", "");
    mm_result_data.insert("结果", "");
    //  mm_result_data["cycle mean/hz"] = "";
    //  mm_result_data["max/hz"] = "";
    //  mm_result_data["bias max"] = "";
    //  mm_result_data["min/hz"] = "";
    //  mm_result_data["bias min"] = "";
    //  mm_result_data["result"] = "";
    //  mm_result_data["time"] = ""; //时间戳，转速均值，最大值，最小值，最大值偏差，最小值偏差，结果

    QString desk_path = QStandardPaths::writableLocation(QStandardPaths::DesktopLocation);
    mi_save_file_->createOneFile(desk_path, "/转速测试结果", mm_result_data);
}

CMotorMonitorOperation::~CMotorMonitorOperation()
{
    //*
    delete mc_processList_;
    delete mst_task_status_;

    //* 子线程注销
    m_serial_thread_->task_id_change(0);
    m_sub_thread_->quit();
    m_sub_thread_->wait();
    delete m_sub_thread_; //会析构 m_serial_thread_

    killTimer(m_timerId);
    QThread::msleep(50);

    //*
    delete mc_itest_board_;
    if(mc_icomm_ != nullptr) delete mc_icomm_;

    delete mst_comm_status_;

    //* data
    delete mst_motor_fg_data_;
    delete mst_cycle_data_;

    delete mst_result_;

    //*
    delete mi_save_file_;

    //* message
    delete m_message_box_;
}

void CMotorMonitorOperation::timerEvent(QTimerEvent *event)
{
    //  Q_UNUSED(event);

    if(m_timerId == event->timerId())
    {
        //* 非任务task
        m_port_update_cnt++;
        if(m_port_update_cnt == 400)
        {
            m_port_update_cnt = 0;
            portlistUpdate();
        }

        //**************************** 业务 tasks
        if(mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_) == eFATAL) ;

        //* 状态界面显示更新

    }
}
/**
 * @brief
 */
void CMotorMonitorOperation::varibleInit()
{
    //* task status init
    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    //* data varibles init
    m_first_data = true;
    m_origin_data.clear();

    mst_motor_fg_data_->data.clear();
    mst_motor_fg_data_->max = 0x00;
    mst_motor_fg_data_->min = 0xffff;
    mst_motor_fg_data_->sum = 0;

    //*
    mst_cycle_data_->data.clear();
    mst_cycle_data_->distribute.clear();
    mst_cycle_data_->max = 0x00;
    mst_cycle_data_->min = 0xffff;
    mst_cycle_data_->sum = 0;
}

bool CMotorMonitorOperation::portlistUpdate(void)
{
    QStringList *port_list_ = new QStringList;
    bool port_flag = mc_icomm_->scanPort(port_list_, mst_config_->cur_port_name); //列表名称变化
    //  m_port_flag = mc_icomm_->scanPort(port_list_, mst_config_->port_name); //使用端口变化
    emit portUpdateSignals(port_list_, port_flag);

    return true;
}

EExecStatus CMotorMonitorOperation::readyStep(void)
{
    //* 打开端口
    if(mc_icomm_->getPortName() != mst_config_->port_name){
        if(mc_icomm_ != nullptr) delete mc_icomm_;
        mc_icomm_ = new CUART(mst_config_->port_name, m_uart_baud);

        mc_itest_board_->change_Icom_interface(mc_icomm_);
        m_serial_thread_->device_change_interface(mc_itest_board_);
    }

    if(!mc_icomm_->checkPort())
        if(!mc_icomm_->openPort()) return eFATAL;

    //* varibles init
    varibleInit();

    //* 接收线程
    m_serial_thread_->task_id_change(1);
    emit subThreadSignal(true);
    emit readySignal(true);

    return eCOMP;
}

EExecStatus CMotorMonitorOperation::readyAck(void)
{
    return eCOMP;
}

/**
 * @brief 任务开始
 * @return
 */
EExecStatus CMotorMonitorOperation::startStep(void)
{
    //* 开始指令
    QByteArray motor_info;
    motor_info.append(mst_config_->pwm_radio);
    motor_info.append(mst_config_->limit_time & 0xff);
    motor_info.append((mst_config_->limit_time >> 8) & 0xff);
    uint16_t fg_num = mst_config_->limit_cycle_num * 3;
    motor_info.append(fg_num & 0xff);
    motor_info.append((fg_num>>8) & 0xff);

    if(mc_itest_board_->start(mst_config_->mode, motor_info)) return eOK; //检测板数据缓存清空
    else return eFATAL;
}

/**
 * @brief 开始指令反馈
 */
EExecStatus CMotorMonitorOperation::startAck(void)
{
    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        return eCOMP;
    }
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) return eERROR;
    else return eWAIT;

}

/**
 * @brief 分布、极值、结果、显示
 * @return
 */
void CMotorMonitorOperation::dataShow(void)
{
    distribute(0, mst_cycle_data_);
    //emit plotSignal(mst_motor_fg_data_->data, mst_cycle_data_->distribute); //

    getResult(mst_result_, mst_config_->result_stand);
    emit resultSignal(mst_cycle_data_->mean); //
}

/**
 * @brief 数据开始
 */
EExecStatus CMotorMonitorOperation::dataStep(void)
{
    //* 循环接收->数据处理
    //dataHandle();

    return eOK;
}

/**
 * @brief 数据返回
 */
EExecStatus CMotorMonitorOperation::dataAck(void)
{
    emit dataAckSignal(mst_task_status_->cur_status);


    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_OK) return eOK;
    else if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        for(uint16_t i = 0; i < m_origin_data.length(); i = i + 2)
        {
            uint16_t fg_time = (uchar)m_origin_data.at(i) + (uint16_t)((uchar)m_origin_data.at(i+1)<<8);
            mst_motor_fg_data_->data.append(fg_time);
        }
    }
    else
    {
        qDebug() << "-e motor/ slot dataReceive process confusion";
        return eFATAL;
    }

    //* 循环接收->数据处理
    if(mst_config_->mode == 0){ //连续监控，每帧数据处理
        if(m_first_data) fgToCycle(mst_motor_config, mst_motor_fg_data_, mst_cycle_data_);
        else fgToCycle(MOTOR_FG_NUM, mst_motor_fg_data_, mst_cycle_data_);


        extremeValue(mst_cycle_data_); //
        //emit resultDataSignal(mean, max_bias, min_bias); //

        dataShow(); //每帧数据显示

        mst_motor_fg_data_->data.clear(); //每帧数据清除
        mst_motor_fg_data_->max = 0x00;
        mst_motor_fg_data_->min = 0xffff;
        mst_motor_fg_data_->sum = 0;

        return eOK;
    }
    return eOK;
}

/**
 * @brief CMotorMonitorOperation::compStep
 */
EExecStatus CMotorMonitorOperation::compStep(void)
{
    QByteArray array_tmp;

    if(mst_config_->mode == 0){ //连续监控
        if(!mc_itest_board_->stop(array_tmp)) qDebug() << "-e motor: port close fail"; //stop cmd
        return eOK;
    }

    return eCOMP;
}

/**
 * @brief 结束
 * @return
 */
EExecStatus CMotorMonitorOperation::compAck(void)
{
    //* close
    m_serial_thread_->task_id_change(0);
    emit subThreadSignal(false); //子线程退出
    mc_icomm_->closePort(); //port close
    emit compAckSignal(mst_task_status_->cur_status);


    if(mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        //* 最小圈数判断
        if(!(mst_motor_fg_data_->data.length() > MOTOR_SPEED_UP_NUM * MOTOR_FG_NUM)) {
            QMessageBox::warning(m_message_box_, tr("cycle num:") + mst_motor_fg_data_->data.length(), tr(" filter cycle num:") + MOTOR_SPEED_UP_NUM * MOTOR_FG_NUM);
            return eFATAL; //
        }
        else {
            if(mst_config_->mode == 0) fgToCycle(MOTOR_FG_NUM, mst_motor_fg_data_, mst_cycle_data_);
            else fgToCycle(mst_motor_config, mst_motor_fg_data_, mst_cycle_data_);

            switch (mst_config_->judge_mode) {
            case NMotorMonitor::EJudgeMode::eEXTREME_MODE:
                extremeValue(mst_cycle_data_); //
                break;
            case NMotorMonitor::EJudgeMode::eCONFIDENT_INTERVAL:
                break;
            case NMotorMonitor::EJudgeMode::eSTANDARD_DEVIATION:
                standardDeviation(mst_cycle_data_);
                break;
            default:
                break;
            }

            //* interface update
            dataShow();

            //* save data
            mm_result_data["时间"] = QString(QDateTime::currentDateTime().toString("yyyy-MM-dd-hh:mm:ss")); //时间戳，转速均值，最大值，最小值，最大值偏差，最小值偏差，结果
            mm_result_data["平均转速/hz"] = QString::number((double)mst_cycle_data_->mean/100, 'g', 3);
            mm_result_data["最大转速/hz"] = QString::number((double)mst_cycle_data_->max/100, 'g', 3);
            mm_result_data["最大转速偏差"] = QString::number(mst_result_->max_bias);
            mm_result_data["最小转速/hz"] = QString::number((double)mst_cycle_data_->min/100, 'g', 3);
            mm_result_data["最小转速偏差"] = QString::number(mst_result_->min_bias);
            mm_result_data["标准差"] = QString::number(mst_result_->sigma);
            mm_result_data["结果"] = QString(mst_result_->result == 1?"PASS":"NG");

            mi_save_file_->writeFile(mm_result_data);
        }
    }
    return eCOMP;
}

void CMotorMonitorOperation::dataRecive(ITestBoard::ECommStep step, ECommStatus status, QByteArray bytes) {
    mst_comm_status_->comm_status = status;
    switch (step) {
    case ITestBoard::ECommStep::eSTART_STEP:
        mv_task_list[eSTART_STEP].flag.stop = true; //
        break;
    case ITestBoard::ECommStep::eDATA_STEP:
        m_origin_data = bytes;
        mv_task_list[eDATA_STEP].flag.stop = true; //data ack
        break;
    case ITestBoard::ECommStep::eCOMPLETE_STEP:
        mv_task_list[eCOMPLETE_STEP].flag.stop = true; //comp ack
        break;
    default:
        break;
    }

}

/**
 * @brief 传入数据当作 完整包 数据处理，包与包之间遗漏fg不做处理（该情况需用 ring_buffer 处理）
 * @param cycle_fg_num 单圈fg个数
 * @param motor_data_
 */
void CMotorMonitorOperation::fgToCycle(const uint8_t &cycle_fg_num, StMotorData* const motor_data_, StMotorData* const cycle_data_)
{
    uint                cycle_time = 0;
    uint16_t            fg_num = 0, cycle_num = 0;

    for(QVector<uint16_t>::Iterator iter = motor_data_->data.begin(); iter != motor_data_->data.end(); ++iter) //出去头尾启停不稳定数据
    {
        //* fg 极值
        if(*iter > motor_data_->max) motor_data_->max = *iter;
        if(*iter < motor_data_->min) motor_data_->min = *iter;
        //motor_data_->sum += fg_timer;

        //* 单圈转速
        cycle_time += *iter;

        if(iter > (motor_data_->data.begin() + cycle_fg_num)){
            cycle_time -= *(iter - cycle_fg_num);
            uint16_t cycle_fre = (uint16_t)round(1000000/cycle_time);  //10000（0.1ms）*100(放大倍数)转Hz
            cycle_data_->data.append(cycle_fre);

            if(cycle_fre > cycle_data_->max) cycle_data_->max = cycle_fre;
            if(cycle_fre < cycle_data_->min) cycle_data_->min = cycle_fre;
            cycle_data_->sum += cycle_fre;
        }
    }
    fg_num = motor_data_->data.length();
    cycle_num = cycle_data_->data.length();

#ifdef PROCESS_DATA
    qDebug() << "-i motor/ fg num:" << fg_num << "cycle num:" << cycle_num;
#endif
}

/**
 * @brief CMotorMonitorOperation::fgToCycle
 * @param cycle_filter_num 首包数据过滤启动段圈数
 * @param cycle_fg_num
 * @param motor_data_
 * @param cycle_data_
 */
void CMotorMonitorOperation::fgToCycle(const StMotorConfig &motor_config, StMotorData* const motor_data_, StMotorData* const cycle_data_)
{
    uint16_t            fg_timer = 0;
    uint                cycle_time = 0;

    QVector<uint16_t>::Iterator iter;

    uint16_t fg_num = motor_data_->data.length();
    uint16_t up_filter_fg_num = motor_config.up_filter * motor_config.cycle_fg_num;
    uint16_t down_filter_fg_num = motor_config.down_filter * motor_config.cycle_fg_num;


    for(iter = motor_data_->data.begin(); iter != motor_data_->data.end(); ++iter) //出去头尾启停不稳定数据
    {
        fg_timer = *iter;

        //* fg 极值
        if(fg_timer > motor_data_->max) motor_data_->max = fg_timer;
        if(fg_timer < motor_data_->min) motor_data_->min = fg_timer;
        motor_data_->sum += fg_timer;


        //* 单圈转速

        size_t motor_i = iter - motor_data_->data.begin() + 1; //

        if(motor_i >= up_filter_fg_num && motor_i <= (fg_num - down_filter_fg_num)) { //过滤启动段
            cycle_time += *iter;

            if((motor_i - up_filter_fg_num) >= motor_config.cycle_fg_num) {
                uint16_t cycle_time_old = *(iter - motor_config.cycle_fg_num);
                cycle_time -= cycle_time_old; //
                uint16_t cycle_fre = (uint16_t)round(1000000/cycle_time);  //10000（0.1ms）*100(放大倍数)转Hz
                cycle_data_->data.append(cycle_fre);

                if(cycle_fre > cycle_data_->max) cycle_data_->max = cycle_fre;
                if(cycle_fre < cycle_data_->min) cycle_data_->min = cycle_fre;
                cycle_data_->sum += cycle_fre;
            }
        }

    }

    uint16_t cycle_num = cycle_data_->data.length();

#ifdef PROCESS_DATA
    qDebug() << "-i motor/ fg num:" << fg_num << "cycle num:" << cycle_num;
#endif
}


/**
 * @brief 数据分布，哈希？
 * @param start_cycle_num 过滤圈数
 * @param cycle_data_ 转速数据, 最大转速
 * @return 转速分布
 */
void CMotorMonitorOperation::distribute(const uint16_t &start_cycle_num, StMotorData* const cycle_data_)
{
    uint16_t num_tmp = (uint16_t)round((float)cycle_data_->max/10) + 1; //转速最大范围

    cycle_data_->distribute.resize(num_tmp); //

    for(QVector<uint16_t>::Iterator iter = (cycle_data_->data.begin() - start_cycle_num);  iter != cycle_data_->data.end(); ++iter)
    {
        uint16_t tmp = round((float)(*iter)/10);
        cycle_data_->distribute[tmp]++; //各个转速个数累计
    }
}

/**
 * @brief 极值法
 * @param motor_data_ 最大、最小转速
 * @return 平均转速，最大最小偏差
 */
void CMotorMonitorOperation::extremeValue(StMotorData *motor_data_)
{
    uint16_t cal_data_num = motor_data_->data.length();
    //  if(cal_data_num == 0 || motor_data_->sum == 0)
    //    {
    //      QMessageBox::warning(m_message_box_,tr("result cal faile"),tr("num = 0"));
    //      qDebug() << "-motor: receive num:" << cal_data_num << "sum:" << motor_data_->sum;
    //      motor_data_->mean = 0;
    //      return false;
    //    }
    motor_data_->mean = motor_data_->sum / cal_data_num; //

    if(motor_data_->mean == 0)
    {
        QMessageBox::warning(m_message_box_, tr("result cal faile"), tr("mean = 0"));
        return;
    }
    mst_result_->max_bias = (motor_data_->max - motor_data_->mean)*1000.0f / motor_data_->mean + 0.5f;
    mst_result_->min_bias = (motor_data_->mean - motor_data_->min)*1000.0f / motor_data_->mean + 0.5f;
}

void CMotorMonitorOperation::standardDeviation(StMotorData *motor_data_) {
    uint16_t cal_data_num = motor_data_->data.length();
    motor_data_->mean = motor_data_->sum / cal_data_num; //

    if(motor_data_->mean == 0) {
        QMessageBox::warning(m_message_box_, tr("result cal faile"), tr("mean = 0"));
        return;
    }
    mst_result_->max_bias = (motor_data_->max - motor_data_->mean)*1000.0f / motor_data_->mean + 0.5f;
    mst_result_->min_bias = (motor_data_->mean - motor_data_->min)*1000.0f / motor_data_->mean + 0.5f;
    mst_result_->sigma = CStatisticalFormula::StandardDeviation(motor_data_->data, cal_data_num);
}

/**
 * @brief 置信区间
 */
void CMotorMonitorOperation::confidentInterval(StMotorData *motor_data_)
{
    motor_data_->mean = motor_data_->sum / motor_data_->data.length(); //
}



bool CMotorMonitorOperation::getResult(StResult *st_result_, const uint16_t &standar)
{
    switch(mst_config_->judge_mode) {
    case NMotorMonitor::eEXTREME_MODE: //
        st_result_->result = (st_result_->max_bias < standar) && (st_result_->min_bias < standar);
        break;
    case NMotorMonitor::eCONFIDENT_INTERVAL:

        break;
    case NMotorMonitor::eSTANDARD_DEVIATION:
        st_result_->result = (st_result_->sigma < standar);
        break;
    default:
        break;
    }

    return st_result_->result;
}
