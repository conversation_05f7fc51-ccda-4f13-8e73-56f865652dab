#include "LegacyInterpolationAdapter.h"
#include <QElapsedTimer>
#include <QtMath>

namespace ImageProcessing {

LegacyInterpolationAdapter::LegacyInterpolationAdapter() : debugEnabled_(false), lastProcessingTime_(0) {

    // 创建默认的双线性插值器
    interpolator_ = InterpolationFactory::getInstance().createInterpolation(InterpolationType::Bilinear);

    logDebug("LegacyInterpolationAdapter initialized");
}

LegacyInterpolationAdapter::~LegacyInterpolationAdapter() {
    logDebug("LegacyInterpolationAdapter destroyed");
}

void LegacyInterpolationAdapter::bilinear_interpolation(const QVector<QVector<uint32_t>> &src_array, QVector<QVector<uint32_t>> &dst_array) {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug(QString("Starting bilinear interpolation: %1x%2 -> %3x%4")
                     .arg(src_array.size() > 0 ? src_array[0].size() : 0)
                     .arg(src_array.size())
                     .arg(dst_array.size() > 0 ? dst_array[0].size() : 0)
                     .arg(dst_array.size()));

        // 转换输入数据
        ImageDataU32 srcImage = convertFromQVector(src_array);
        ImageDataU32 dstImage = convertFromQVector(dst_array);

        // 执行插值
        if (!interpolator_->interpolate(srcImage, dstImage)) {
            qWarning() << "Bilinear interpolation failed";
            return;
        }

        // 转换回输出格式
        QVector<QVector<uint32_t>> result = convertToQVector(dstImage);
        dst_array                         = result;

        logPerformance("bilinear_interpolation", timer.elapsed());

    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::bilinear_interpolation failed:" << e.qMessage();
    } catch (const std::exception &e) {
        qWarning() << "LegacyInterpolationAdapter::bilinear_interpolation failed:" << e.what();
    }
}

void LegacyInterpolationAdapter::biCubic_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num) {
    QElapsedTimer timer;
    timer.start();

    try {
        if (!array || array->isEmpty()) {
            qWarning() << "Invalid input array for bicubic interpolation";
            return;
        }

        logDebug(QString("Starting bicubic interpolation: %1x%2 -> %3x%4")
                     .arg(array->size() > 0 ? (*array)[0].size() : 0)
                     .arg(array->size())
                     .arg(dst_column_num)
                     .arg(dst_row_num));

        // 转换输入数据
        ImageDataU32 srcImage = convertFromQVector(*array);
        ImageDataU32 dstImage(dst_column_num, dst_row_num);

        // 目前使用双线性插值作为替代（TODO: 实现真正的双三次插值）
        if (!interpolator_->interpolate(srcImage, dstImage)) {
            qWarning() << "Bicubic interpolation failed";
            return;
        }

        // 转换回输出格式
        *array = convertToQVector(dstImage);

        logPerformance("biCubic_interpolation", timer.elapsed());

    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::biCubic_interpolation failed:" << e.qMessage();
    } catch (const std::exception &e) {
        qWarning() << "LegacyInterpolationAdapter::biCubic_interpolation failed:" << e.what();
    }
}

void LegacyInterpolationAdapter::nonlinear_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num) {
    QElapsedTimer timer;
    timer.start();

    try {
        if (!array || array->isEmpty()) {
            qWarning() << "Invalid input array for nonlinear interpolation";
            return;
        }

        logDebug(QString("Starting nonlinear interpolation: %1x%2 -> %3x%4")
                     .arg(array->size() > 0 ? (*array)[0].size() : 0)
                     .arg(array->size())
                     .arg(dst_column_num)
                     .arg(dst_row_num));

        // 转换输入数据
        ImageDataU32 srcImage = convertFromQVector(*array);
        ImageDataU32 dstImage(dst_column_num, dst_row_num);

        // 目前使用双线性插值作为替代（TODO: 实现真正的非线性插值）
        if (!interpolator_->interpolate(srcImage, dstImage)) {
            qWarning() << "Nonlinear interpolation failed";
            return;
        }

        // 转换回输出格式
        *array = convertToQVector(dstImage);

        logPerformance("nonlinear_interpolation", timer.elapsed());

    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::nonlinear_interpolation failed:" << e.qMessage();
    } catch (const std::exception &e) {
        qWarning() << "LegacyInterpolationAdapter::nonlinear_interpolation failed:" << e.what();
    }
}

void LegacyInterpolationAdapter::median_filter() {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug("Applying median filter");
        applyFilterToCurrentImage(FilterType::Median);
        logPerformance("median_filter", timer.elapsed());
    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::median_filter failed:" << e.qMessage();
    }
}

void LegacyInterpolationAdapter::gaussian_filter() {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug("Applying gaussian filter");
        applyFilterToCurrentImage(FilterType::Gaussian);
        logPerformance("gaussian_filter", timer.elapsed());
    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::gaussian_filter failed:" << e.qMessage();
    }
}

void LegacyInterpolationAdapter::kalman_filter() {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug("Applying kalman filter");
        applyFilterToCurrentImage(FilterType::Kalman);
        logPerformance("kalman_filter", timer.elapsed());
    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::kalman_filter failed:" << e.qMessage();
    }
}

void LegacyInterpolationAdapter::bilateral_filter() {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug("Applying bilateral filter");
        applyFilterToCurrentImage(FilterType::Bilateral);
        logPerformance("bilateral_filter", timer.elapsed());
    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::bilateral_filter failed:" << e.qMessage();
    }
}

void LegacyInterpolationAdapter::guide_filter() {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug("Applying guide filter (using gaussian as substitute)");
        applyFilterToCurrentImage(FilterType::Gaussian);
        logPerformance("guide_filter", timer.elapsed());
    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::guide_filter failed:" << e.qMessage();
    }
}

void LegacyInterpolationAdapter::Convolution_filter() {
    QElapsedTimer timer;
    timer.start();

    try {
        logDebug("Applying convolution filter");
        applyFilterToCurrentImage(FilterType::Convolution);
        logPerformance("Convolution_filter", timer.elapsed());
    } catch (const ProcessingException &e) {
        qWarning() << "LegacyInterpolationAdapter::Convolution_filter failed:" << e.qMessage();
    }
}

void LegacyInterpolationAdapter::setInterpolationParameters(const InterpolationParams &params) {
    if (interpolator_) {
        interpolator_->setParameters(params);
        logDebug("Interpolation parameters updated");
    }
}

void LegacyInterpolationAdapter::setFilterParameters(FilterType type, const FilterParams &params) {
    // 简化实现：添加滤波器并设置参数
    addFilter(type);
    if (!filters_.empty()) {
        // 为最后添加的滤波器设置参数
        filters_.back()->setParameters(params);
        logDebug(QString("Filter parameters updated for type: %1").arg(static_cast<int>(type)));
    }
}

void LegacyInterpolationAdapter::setDebugEnabled(bool enabled) {
    debugEnabled_ = enabled;
    logDebug(QString("Debug output %1").arg(enabled ? "enabled" : "disabled"));
}

uint32_t LegacyInterpolationAdapter::getLastProcessingTime() const {
    return lastProcessingTime_;
}

QString LegacyInterpolationAdapter::getVersion() const {
    return "2.0.0-legacy-adapter";
}

ImageDataU32 LegacyInterpolationAdapter::convertFromQVector(const QVector<QVector<uint32_t>> &qvector) {
    if (qvector.isEmpty()) {
        return ImageDataU32();
    }

    uint32_t height = static_cast<uint32_t>(qvector.size());
    uint32_t width  = static_cast<uint32_t>(qvector[0].size());

    ImageDataU32 imageData(width, height);

    for (uint32_t y = 0; y < height; ++y) {
        for (uint32_t x = 0; x < width; ++x) {
            imageData.matrix()[y][x] = qvector[y][x];
        }
    }

    return imageData;
}

QVector<QVector<uint32_t>> LegacyInterpolationAdapter::convertToQVector(const ImageDataU32 &imageData) {
    QVector<QVector<uint32_t>> qvector;

    if (imageData.empty()) {
        return qvector;
    }

    qvector.resize(imageData.height());
    for (uint32_t y = 0; y < imageData.height(); ++y) {
        qvector[y].resize(imageData.width());
        for (uint32_t x = 0; x < imageData.width(); ++x) {
            qvector[y][x] = imageData.matrix()[y][x];
        }
    }

    return qvector;
}

void LegacyInterpolationAdapter::addFilter(FilterType type) {
    try {
        auto filter = FilterFactory::getInstance().createFilter(type);
        if (filter) {
            filters_.push_back(std::move(filter));
            logDebug(QString("Created filter for type: %1").arg(static_cast<int>(type)));
        }
    } catch (const UnsupportedOperationException &e) {
        qWarning() << "Filter type not supported:" << e.qMessage();
    }
}

bool LegacyInterpolationAdapter::applyAllFilters() {
    if (currentImage_.empty()) {
        logDebug("No current image to filter, operation skipped");
        return true;
    }

    for (auto &filter : filters_) {
        if (filter && !filter->apply(currentImage_)) {
            qWarning() << "Filter application failed:" << filter->getAlgorithmName();
            return false;
        }
    }
    return true;
}

bool LegacyInterpolationAdapter::applyFilterToCurrentImage(FilterType type) {
    // 添加指定类型的滤波器并应用
    addFilter(type);
    return applyAllFilters();
}

void LegacyInterpolationAdapter::logDebug(const QString &message) const {
    if (debugEnabled_) {
        qDebug() << "[LegacyInterpolationAdapter]" << message;
    }
}

void LegacyInterpolationAdapter::logPerformance(const QString &operation, qint64 elapsedMs) const {
    lastProcessingTime_ = static_cast<uint32_t>(elapsedMs);
    if (debugEnabled_) {
        qDebug() << "[LegacyInterpolationAdapter] Performance:" << operation << "completed in" << elapsedMs << "ms";
    }
}

}  // namespace ImageProcessing
