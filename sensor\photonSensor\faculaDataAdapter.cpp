#include "faculaDataAdapter.h"

ImageProcessing::ImageDataU32 FaculaDataAdapter::qvectorToImageData(const QVector<QVector<uint32_t>>& qvectorMatrix) {
    if (!validateQVectorMatrix(qvectorMatrix)) {
        logError("Invalid QVector matrix provided");
        return ImageProcessing::ImageDataU32(0, 0);
    }
    
    uint32_t height = static_cast<uint32_t>(qvectorMatrix.size());
    uint32_t width = static_cast<uint32_t>(qvectorMatrix[0].size());
    
    logDebug(QString("Converting QVector matrix %1x%2 to ImageData").arg(width).arg(height));
    
    ImageProcessing::ImageDataU32 imageData(width, height);
    
    // 复制数据
    for (uint32_t i = 0; i < height; ++i) {
        for (uint32_t j = 0; j < width; ++j) {
            imageData.matrix()[i][j] = qvectorMatrix[i][j];
        }
    }
    
    return imageData;
}

QVector<QVector<uint32_t>> FaculaDataAdapter::imageDataToQVector(const ImageProcessing::ImageDataU32& imageData) {
    if (!validateImageData(imageData)) {
        logError("Invalid ImageData provided");
        return QVector<QVector<uint32_t>>();
    }
    
    uint32_t width = imageData.width();
    uint32_t height = imageData.height();
    
    logDebug(QString("Converting ImageData %1x%2 to QVector matrix").arg(width).arg(height));
    
    QVector<QVector<uint32_t>> qvectorMatrix(height);
    
    // 初始化并复制数据
    for (uint32_t i = 0; i < height; ++i) {
        qvectorMatrix[i].resize(width);
        for (uint32_t j = 0; j < width; ++j) {
            qvectorMatrix[i][j] = imageData.matrix()[i][j];
        }
    }
    
    return qvectorMatrix;
}

bool FaculaDataAdapter::copyImageDataToQVector(const ImageProcessing::ImageDataU32& imageData, 
                                              QVector<QVector<uint32_t>>& qvectorMatrix) {
    if (!validateImageData(imageData)) {
        logError("Invalid ImageData provided for copying");
        return false;
    }
    
    uint32_t srcWidth = imageData.width();
    uint32_t srcHeight = imageData.height();
    
    // 调整目标矩阵尺寸
    resizeQVectorMatrix(qvectorMatrix, srcWidth, srcHeight);
    
    // 复制数据
    for (uint32_t i = 0; i < srcHeight; ++i) {
        for (uint32_t j = 0; j < srcWidth; ++j) {
            qvectorMatrix[i][j] = imageData.matrix()[i][j];
        }
    }
    
    logDebug(QString("Successfully copied ImageData %1x%2 to QVector matrix").arg(srcWidth).arg(srcHeight));
    return true;
}

bool FaculaDataAdapter::validateQVectorMatrix(const QVector<QVector<uint32_t>>& qvectorMatrix) {
    if (qvectorMatrix.isEmpty()) {
        logError("QVector matrix is empty");
        return false;
    }
    
    uint32_t expectedWidth = static_cast<uint32_t>(qvectorMatrix[0].size());
    if (expectedWidth == 0) {
        logError("QVector matrix has zero width");
        return false;
    }
    
    // 检查所有行是否具有相同的宽度
    for (int i = 0; i < qvectorMatrix.size(); ++i) {
        if (static_cast<uint32_t>(qvectorMatrix[i].size()) != expectedWidth) {
            logError(QString("QVector matrix row %1 has inconsistent width: expected %2, got %3")
                    .arg(i).arg(expectedWidth).arg(qvectorMatrix[i].size()));
            return false;
        }
    }
    
    return true;
}

bool FaculaDataAdapter::validateImageData(const ImageProcessing::ImageDataU32& imageData) {
    if (imageData.width() == 0 || imageData.height() == 0) {
        logError(QString("ImageData has invalid dimensions: %1x%2").arg(imageData.width()).arg(imageData.height()));
        return false;
    }
    
    if (imageData.matrix().empty()) {
        logError("ImageData matrix is empty");
        return false;
    }
    
    return true;
}

bool FaculaDataAdapter::getQVectorDimensions(const QVector<QVector<uint32_t>>& qvectorMatrix, 
                                            uint32_t& width, uint32_t& height) {
    if (!validateQVectorMatrix(qvectorMatrix)) {
        width = 0;
        height = 0;
        return false;
    }
    
    height = static_cast<uint32_t>(qvectorMatrix.size());
    width = static_cast<uint32_t>(qvectorMatrix[0].size());
    
    return true;
}

QVector<QVector<uint32_t>> FaculaDataAdapter::createEmptyQVectorMatrix(uint32_t width, uint32_t height, 
                                                                       uint32_t defaultValue) {
    if (width == 0 || height == 0) {
        logError(QString("Invalid dimensions for creating QVector matrix: %1x%2").arg(width).arg(height));
        return QVector<QVector<uint32_t>>();
    }
    
    QVector<QVector<uint32_t>> matrix(height);
    for (uint32_t i = 0; i < height; ++i) {
        matrix[i].resize(width);
        matrix[i].fill(defaultValue);
    }
    
    logDebug(QString("Created empty QVector matrix %1x%2 with default value %3").arg(width).arg(height).arg(defaultValue));
    return matrix;
}

void FaculaDataAdapter::resizeQVectorMatrix(QVector<QVector<uint32_t>>& qvectorMatrix, 
                                           uint32_t newWidth, uint32_t newHeight, 
                                           uint32_t defaultValue) {
    if (newWidth == 0 || newHeight == 0) {
        logError(QString("Invalid dimensions for resizing QVector matrix: %1x%2").arg(newWidth).arg(newHeight));
        return;
    }
    
    // 调整行数
    qvectorMatrix.resize(newHeight);
    
    // 调整每行的列数
    for (uint32_t i = 0; i < newHeight; ++i) {
        uint32_t oldSize = static_cast<uint32_t>(qvectorMatrix[i].size());
        qvectorMatrix[i].resize(newWidth);
        
        // 如果新尺寸更大，用默认值填充新元素
        if (newWidth > oldSize) {
            for (uint32_t j = oldSize; j < newWidth; ++j) {
                qvectorMatrix[i][j] = defaultValue;
            }
        }
    }
    
    logDebug(QString("Resized QVector matrix to %1x%2").arg(newWidth).arg(newHeight));
}

void FaculaDataAdapter::logDebug(const QString& message) {
    qDebug() << "[FaculaDataAdapter]" << message;
}

void FaculaDataAdapter::logError(const QString& message) {
    qCritical() << "[FaculaDataAdapter]" << message;
}
