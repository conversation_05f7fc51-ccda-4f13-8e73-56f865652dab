# 配置生成器工具集 (config/)

## 概述

config目录包含产品体系构建框架的所有配置生成器，遵循**单一职责原则**，每个脚本专门负责生成特定类型的配置文件。这些配置生成器为整个产品开发流程提供标准化的配置支持。

## 🎯 核心原则

- **单一职责**：每个生成器只负责一种配置类型
- **统一接口**：所有生成器使用相同的命令行接口
- **内容唯一**：配置内容在单一文件中管理，避免重复
- **可追溯性**：配置文件支持变更追踪和版本管理

## 📁 脚本结构

### 主控制器
- `generate_all_configs.py` - 统一配置生成器，协调调用所有子生成器

### 系统级配置生成器
- `generate_workflow_config.py` - 工作流系统配置
- `generate_traceability_config.py` - 追溯系统配置  
- `generate_document_links_config.py` - 文档关联系统配置

### 组件级配置生成器
- `generate_requirements_import_config.py` - 需求导入配置
- `generate_requirements_analysis_config.py` - 需求分析配置
- `generate_design_config.py` - 设计配置
- `generate_development_config.py` - 开发配置
- `generate_quality_config.py` - 质量保证配置
- `generate_production_config.py` - 生产配置
- `generate_deliverables_config.py` - 交付物配置

## 🚀 使用方式

### 快速开始 - 生成所有配置

```bash
# 生成所有配置文件
python generate_all_configs.py --project-path /path/to/project --project-type single_layer

# 指定脚本基础路径
python generate_all_configs.py --project-path /path/to/project --scripts-base "/path/to/scripts"

# 只生成特定类型的配置
python generate_all_configs.py --project-path /path/to/project --config-types workflow traceability
```

### 单独生成配置

```bash
# 生成工作流配置
python generate_workflow_config.py --project-path /path/to/project --project-type single_layer

# 生成追溯系统配置
python generate_traceability_config.py --project-path /path/to/project --project-type multi_level

# 生成文档关联配置
python generate_document_links_config.py --project-path /path/to/project
```

## 📊 配置文件详解

### 工作流配置 (workflow_config.json)

**生成器**: `generate_workflow_config.py`

**功能**:
- 事件驱动工作流组件配置
- MCP服务器集成配置
- 组件间连接和触发器配置
- 自动化流程定义

**主要内容**:
```json
{
  "workflow": {
    "components": [...],           // 工作流组件定义
    "connections": [...],          // 组件间连接关系
    "mcp_servers": [...],         // MCP服务器配置
    "triggers": [...]             // 事件触发器
  }
}
```

### 追溯系统配置 (traceability_config.json)

**生成器**: `generate_traceability_config.py`

**功能**:
- 组件和文档类型配置
- 追溯关系链条配置
- 块级管理配置
- 变更管理配置

**主要内容**:
```json
{
  "traceability": {
    "components": {...},          // 组件定义(REQ, DES, DEV等)
    "relationships": [...],       // 追溯关系链
    "block_management": {...},    // 块级追溯配置
    "change_management": {...}    // 变更管理配置
  }
}
```

### 文档关联配置 (document_links_config.json)

**生成器**: `generate_document_links_config.py`

**功能**:
- 文档注册和发现配置
- 语义关联分析配置
- 双链网络构建配置
- INDEX文件管理配置
- **Git风格文档扫描规则** (新增功能)

**主要内容**:
```json
{
  "document_scanning": {
    "mode": "gitignore_style",
    "gitignore_rules": [
      "# 排除源代码文件",
      "development/**/*.c",
      "development/**/*.py",
      "# 包含文档目录",
      "!development/**/docs/**",
      "!**/*.md"
    ],
    "traditional_config": {
      "included_file_types": [".md", ".txt", ".pdf"],
      "excluded_directories": [".git", "__pycache__"]
    }
  }
}
```

**配置模式**:
- `traditional`: 传统的包含/排除列表模式
- `gitignore_style`: Git风格忽略规则模式（推荐）

**Git风格规则语法**:
- `*.py` - 匹配所有Python文件
- `development/**/*.c` - 匹配development目录下所有.c文件
- `!development/**/docs/**` - 否定规则，包含docs目录
- `# 注释` - 注释行

## 🔧 配置参数说明

### 通用参数

| 参数 | 类型 | 默认值 | 描述 |
|-----|------|--------|------|
| `--project-path` | string | `.` | 项目根目录路径 |
| `--project-type` | choice | `single_layer` | 项目类型(single_layer/multi_level) |
| `--scripts-base` | string | `""` | 脚本基础路径，用于生成正确的脚本引用路径 |

### 专用参数

**generate_all_configs.py**:
- `--config-types`: 指定要生成的配置类型列表

**generate_workflow_config.py**:
- 需要`--scripts-base`参数来生成正确的脚本路径引用

## 📋 生成的配置文件

所有配置文件都会生成在项目的`config/`目录下：

```
project/
├── config/
│   ├── workflow_config.json              # 工作流系统配置
│   ├── traceability_config.json          # 追溯系统配置
│   ├── document_links_config.json        # 文档关联配置
│   ├── requirements_import_config.json   # 需求导入配置
│   ├── requirements_analysis_config.json # 需求分析配置
│   ├── design_config.json               # 设计配置
│   ├── development_config.json          # 开发配置
│   ├── quality_config.json              # 质量配置
│   ├── production_config.json           # 生产配置
│   └── deliverables_config.json         # 交付物配置
```

## 🔄 集成与工作流

### 与项目初始化集成

配置生成器通常在项目初始化时调用：

```bash
# 在项目初始化脚本中调用
python init_product_project.py --project_name="新产品" --generate-configs
```

### 与MCP Server集成

配置生成器可以通过MCP Server提供可视化界面：

```python
# MCP Server调用示例
from config.generate_all_configs import generate_all_configs

# 通过MCP接口调用
result = generate_all_configs(
    project_path="/path/to/project",
    project_type="single_layer",
    config_types=["workflow", "traceability"]
)
```

### 与VSCode Tasks集成

可以配置VSCode任务来快速调用配置生成器：

```json
{
    "label": "生成项目配置",
    "type": "shell",
    "command": "python",
    "args": [
        "${workspaceFolder}/../scripts/config/generate_all_configs.py",
        "--project-path", "${workspaceFolder}",
        "--project-type", "single_layer"
    ]
}
```

## 🛠️ 开发与扩展

### 添加新的配置生成器

1. 创建新的生成器脚本，命名为`generate_[类型]_config.py`
2. 实现统一的接口：
   ```python
   def generate_[类型]_config(project_path, project_type, **kwargs):
       # 配置生成逻辑
       pass
   ```
3. 在`generate_all_configs.py`中添加新生成器到`generators`字典
4. 更新命令行参数选择列表

### 配置文件结构规范

所有配置文件应遵循以下结构：

```json
{
  "[配置类型]": {
    "version": "1.0.0",
    "created_date": "ISO格式时间戳",
    "project_type": "single_layer|multi_level",
    // 配置具体内容
  }
}
```

## ⚠️ 注意事项

1. **覆盖确认**: 如果配置文件已存在，脚本会提示是否覆盖
2. **路径依赖**: `scripts-base`参数对于生成正确的脚本引用路径很重要
3. **项目类型**: 不同项目类型可能生成不同的配置内容
4. **依赖关系**: 某些配置可能依赖其他配置的存在

## 🐛 故障排除

### 常见问题

1. **权限错误**: 确保对目标目录有写权限
2. **路径错误**: 检查project-path和scripts-base参数是否正确
3. **JSON格式错误**: 检查生成的配置文件是否为有效JSON

### 调试模式

```bash
# 启用详细输出
python generate_all_configs.py --project-path . --verbose

# 验证生成的配置文件
python -m json.tool config/workflow_config.json
```

## 📚 相关文档

- [产品体系构建框架](../../产品体系构建框架.md)
- [产品体系工作流程框架](../../产品体系工作流程框架.md)
- [产品体系内容追溯系统框架](../../产品体系内容追溯系统框架.md)
- [主脚本工具集README](../README.md)

## 🔄 版本历史

- **v1.0.0**: 初始版本，支持基础配置生成
- 统一接口设计
- 支持单层和多层项目结构
- 集成MCP Server支持 