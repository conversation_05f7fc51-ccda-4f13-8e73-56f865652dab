#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置文件加载器

专门负责加载各种格式的配置文件（JSON、YAML）。
遵循单一职责原则，只负责配置文件的加载功能。
"""

import json
import yaml
import argparse
import sys
from pathlib import Path
from typing import Dict, Any, Optional


def load_config(
    config_path: str, 
    config_type: Optional[str] = None
) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        config_type: 配置文件类型 (json, yaml, yml)，如果不指定则自动判断
    
    Returns:
        Dict[str, Any]: 配置加载结果
    """
    try:
        file_path = Path(config_path)
        
        if not file_path.exists():
            return {
                "success": False,
                "error": f"配置文件不存在: {config_path}"
            }
        
        # 自动判断文件类型
        if not config_type:
            suffix = file_path.suffix.lower()
            if suffix in ['.yaml', '.yml']:
                config_type = 'yaml'
            elif suffix == '.json':
                config_type = 'json'
            else:
                config_type = 'json'  # 默认为JSON
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if config_type.lower() in ['yaml', 'yml']:
                config_data = yaml.safe_load(f) or {}
            else:
                config_data = json.load(f)
        
        return {
            "success": True,
            "config": config_data,
            "path": str(file_path),
            "type": config_type,
            "size": file_path.stat().st_size
        }
        
    except json.JSONDecodeError as e:
        return {
            "success": False,
            "error": f"JSON格式错误: {str(e)}"
        }
    except yaml.YAMLError as e:
        return {
            "success": False,
            "error": f"YAML格式错误: {str(e)}"
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"加载配置文件失败: {str(e)}"
        }


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='配置文件加载器')
    parser.add_argument('config_path', help='配置文件路径')
    parser.add_argument('--type', choices=['json', 'yaml', 'yml'], 
                       help='配置文件类型（可选，自动判断）')
    parser.add_argument('--output', choices=['json', 'yaml'], default='json',
                       help='输出格式（默认：json）')
    
    args = parser.parse_args()
    
    # 加载配置
    result = load_config(args.config_path, args.type)
    
    if result["success"]:
        print(f"✅ 配置文件加载成功")
        print(f"   文件路径: {result['path']}")
        print(f"   文件类型: {result['type']}")
        print(f"   文件大小: {result['size']} bytes")
        print()
        
        # 输出配置内容
        if args.output == 'yaml':
            print("配置内容 (YAML格式):")
            print(yaml.dump(result['config'], default_flow_style=False, allow_unicode=True, indent=2))
        else:
            print("配置内容 (JSON格式):")
            print(json.dumps(result['config'], indent=2, ensure_ascii=False))
        
        return 0
    else:
        print(f"❌ 配置文件加载失败: {result['error']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
