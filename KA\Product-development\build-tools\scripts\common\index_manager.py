#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一INDEX管理器

提供INDEX文件的创建、更新、验证等统一功能。
支持两种INDEX格式：
1. 文档级INDEX：用于Canvas可视化和文档管理
2. 块级INDEX：用于信息追溯系统
"""

import re
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import sys

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir.parent
sys.path.insert(0, str(scripts_dir))

from common.config import get_component_dirs, get_component_info
from common.document_scanner import DocumentScanner
from shared_config import get_subdirectory_abbreviation


class IndexManager:
    """INDEX文件管理器"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
        self.component_dirs = get_component_dirs()
        self.scanner = DocumentScanner(project_path)
    
    def rebuild_index_file(self, component: str, documents: List[Dict], 
                          index_type: str = "document") -> bool:
        """
        重建INDEX文件
        
        Args:
            component: 组件名称
            documents: 文档列表
            index_type: INDEX类型 ("document" 或 "block")
            
        Returns:
            bool: 重建成功返回True
        """
        # 确定INDEX文件路径
        index_filename = f"{component}_INDEX.md"
        
        if component not in self.component_dirs:
            print(f"未知组件: {component}")
            return False
            
        component_dir = self.component_dirs[component]
        index_path = self.project_path / component_dir / index_filename
        
        if not index_path.parent.exists():
            print(f"组件目录不存在: {index_path.parent}")
            return False
        
        # 生成INDEX文件内容
        if index_type == "document":
            content = self._generate_document_index_content(component, documents)
        elif index_type == "block":
            content = self._generate_block_index_content(component, documents)
        else:
            print(f"未知INDEX类型: {index_type}")
            return False
        
        # 备份现有文件
        if index_path.exists():
            backup_path = index_path.with_suffix('.md.backup')
            try:
                index_path.rename(backup_path)
                print(f"已备份原文件: {backup_path}")
            except Exception as e:
                print(f"备份文件失败: {e}")
        
        # 写入新的INDEX文件
        try:
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已重建INDEX文件: {index_path}")
            return True
            
        except Exception as e:
            print(f"写入INDEX文件失败: {e}")
            return False
    
    def _generate_document_index_content(self, component: str, documents: List[Dict]) -> str:
        """生成文档级INDEX文件内容"""
        # 获取组件信息
        component_info = get_component_info(component)
        component_name = component_info.name if component_info else component
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        content = f"""# {component_name}索引文件

本文件包含{component_name}相关的所有文档索引信息。

**文档统计**: {len(documents)} 个文档
**更新时间**: {current_time}
**更新方式**: 自动扫描生成

## 文档列表

| 文档ID | 文档名称 | 文档路径 | 文档类型 | 组 | 状态 | 最后修改 | 负责人 | 关联需求 | 关联设计 | 备注 |
|--------|----------|----------|----------|-----|------|----------|--------|----------|----------|------|
"""

        # 添加文档行
        for doc in documents:
            modified_time = doc['modified_time'].strftime("%Y-%m-%d")
            group_name = self._extract_group_name(doc['doc_path'])
            content += f"| {doc['doc_id']} | {doc['doc_name']} | {doc['doc_path']} | {doc['doc_type']} | {group_name} | 有效 | {modified_time} | | | | |\n"
        
        content += f"""

## 使用说明

1. **文档ID**: 系统自动生成的唯一标识符
2. **文档路径**: 相对于项目根目录的路径
3. **状态**: 文档当前状态（有效/已废弃/草稿等）
4. **关联字段**: 可手动填写文档间的关联关系

## 维护说明

- 本文件由文档扫描器自动生成
- 如需手动维护，请修改状态、负责人、关联等字段
- 重新运行扫描脚本会保留手动修改的信息

---
*最后更新: {current_time}*
"""
        
        return content

    def _extract_group_name(self, doc_path: str) -> str:
        """
        从文档路径提取组名称（第二层分组）

        Args:
            doc_path: 文档路径

        Returns:
            str: 组名称，如果文档在主目录下则返回空字符串
        """
        if not doc_path:
            return ""

        path_parts = Path(doc_path).parts

        # 如果文档在主目录下（只有一层），则没有第二组
        if len(path_parts) <= 1:
            return ""

        # 检查是否为一层目录文件（直接在组件目录下）
        if len(path_parts) == 2:
            component_dir = path_parts[0]  # requirements, design, etc.
            filename = path_parts[1]       # README.md, requirements_matrix.md, etc.

            # 一层目录文件直接显示在组件级组下，不需要第二层分组
            return ""

        # 对于两层以上目录的组件，第二层使用子目录缩写
        if len(path_parts) >= 3:
            component_dir = path_parts[0]  # requirements, design, etc.
            subdir = path_parts[1]         # custom_requirements, main_requirements, etc.

            # 对于简单的两层结构，使用子目录缩写
            if component_dir in ['requirements', 'design', 'deliverables', 'project_management', 'quality', 'production']:
                return get_subdirectory_abbreviation(subdir)

            # 对于开发项目，使用项目类型缩写
            elif component_dir == 'development':
                project_type = path_parts[1]  # firmware, software
                project_name = path_parts[2]  # fw_project_1, app_project

                # 生成项目类型缩写
                if 'firmware' in project_type:
                    return f"FW-{project_name}"
                elif 'software' in project_type:
                    return f"SW-{project_name}"
                elif 'hardware' in project_type:
                    return f"HW-{project_name}"
                else:
                    return f"{project_type.upper()[:2]}-{project_name}"

            # 对于多层目录，动态提取能做区分的目录作为第二层
            else:
                return get_subdirectory_abbreviation(subdir)

        return ""

    def _generate_block_index_content(self, component: str, documents: List[Dict]) -> str:
        """生成块级INDEX文件内容"""
        # 获取组件信息
        component_info = get_component_info(component)
        component_name = component_info.name if component_info else component
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        content = f"""# {component_name}块级索引文件

本文件包含{component_name}相关的所有文档及内容块的索引信息。

**文档统计**: {len(documents)} 个文档
**更新时间**: {current_time}
**更新方式**: 块级扫描生成

## 块级索引表

| 文档ID | 文档名称 | 文档路径 | 文档类型 | 块ID | 块类型 | 块标题 | 追溯源块ID | 关系类型 | 最后更新 |
|--------|----------|----------|----------|------|--------|--------|------------|----------|----------|
"""
        
        # 这里可以扩展为包含块级信息的格式
        # 目前先使用简化格式
        for doc in documents:
            modified_time = doc['modified_time'].strftime("%Y-%m-%d")
            content += f"| {doc['doc_id']} | {doc['doc_name']} | {doc['doc_path']} | {doc['doc_type']} | | | | | | {modified_time} |\n"
        
        content += f"""

## 使用说明

1. **块ID**: 文档内容块的唯一标识符，格式为 DocID.BlockType.序号
2. **块类型**: H1-H6(标题)、LI(列表)、CODE(代码)、TABLE(表格)等
3. **追溯源**: 该块追溯的源内容块ID
4. **关系类型**: 实现、验证、依赖、参考等关系类型

## 维护说明

- 本文件支持块级内容追溯
- 块ID由系统自动生成和维护
- 追溯关系基于文档关联建立

---
*最后更新: {current_time}*
"""
        
        return content
    
    def rebuild_all_index_files(self, index_type: str = "document") -> bool:
        """
        重建所有组件的INDEX文件
        
        Args:
            index_type: INDEX类型 ("document" 或 "block")
            
        Returns:
            bool: 全部重建成功返回True
        """
        all_documents = self.scanner.scan_all_components()
        
        success_count = 0
        total_count = len(all_documents)
        
        for component, documents in all_documents.items():
            print(f"\n=== 重建 {component} INDEX文件 ===")
            if self.rebuild_index_file(component, documents, index_type):
                success_count += 1
        
        print(f"\n重建完成: {success_count}/{total_count} 个INDEX文件")
        return success_count == total_count
    
    def add_document_to_index(self, component: str, doc_info: Dict) -> bool:
        """
        向INDEX文件添加新文档
        
        Args:
            component: 组件名称
            doc_info: 文档信息字典
            
        Returns:
            bool: 添加成功返回True
        """
        if component not in self.component_dirs:
            print(f"未知组件: {component}")
            return False
            
        component_dir = self.component_dirs[component]
        index_path = self.project_path / component_dir / f"{component}_INDEX.md"
        
        if not index_path.exists():
            print(f"INDEX文件不存在: {index_path}")
            return False
        
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找表格插入位置（支持新的包含group列的格式）
            table_pattern = r'(\| 文档ID \| 文档名称 \| 文档路径 \| 文档类型 \|[^\n]*\n\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^\n]*\n)'
            match = re.search(table_pattern, content)

            if match:
                # 提取组名称
                group_name = self._extract_group_name(doc_info['doc_path'])

                # 在表格后添加新行（包含group列）
                new_row = f"| {doc_info['doc_id']} | {doc_info['doc_name']} | {doc_info['doc_path']} | {doc_info['doc_type']} | {group_name} | 有效 | {datetime.now().strftime('%Y-%m-%d')} | | | | |\n"
                insert_pos = match.end()
                new_content = content[:insert_pos] + new_row + content[insert_pos:]
                
                with open(index_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                    
                print(f"已添加到INDEX文件: {index_path}")
                return True
            else:
                print(f"未找到合适的表格格式: {index_path}")
                return False
                
        except Exception as e:
            print(f"更新INDEX文件失败: {e}")
            return False
    
    def validate_index_file(self, component: str) -> Dict[str, List[str]]:
        """
        验证INDEX文件的完整性和一致性
        
        Args:
            component: 组件名称
            
        Returns:
            Dict[str, List[str]]: 验证结果
        """
        issues = {
            "missing_files": [],
            "extra_entries": [],
            "format_errors": [],
            "inconsistencies": []
        }
        
        # 实际扫描的文档
        actual_docs = self.scanner.scan_component_documents(component)
        actual_paths = {doc['doc_path'] for doc in actual_docs}
        
        # INDEX文件中的文档
        if component not in self.component_dirs:
            issues["format_errors"].append(f"未知组件: {component}")
            return issues
            
        component_dir = self.component_dirs[component]
        index_path = self.project_path / component_dir / f"{component}_INDEX.md"
        
        if not index_path.exists():
            issues["format_errors"].append(f"INDEX文件不存在: {index_path}")
            return issues
        
        # 解析INDEX文件
        try:
            from .document_scanner import IndexScanner
            index_scanner = IndexScanner(self.project_path)
            index_docs = index_scanner.parse_index_file(index_path)
            index_paths = {doc['doc_path'] for doc in index_docs}
            
            # 检查缺失文件
            missing = actual_paths - index_paths
            issues["missing_files"].extend(missing)
            
            # 检查多余条目
            extra = index_paths - actual_paths
            issues["extra_entries"].extend(extra)
            
        except Exception as e:
            issues["format_errors"].append(f"解析INDEX文件失败: {e}")
        
        return issues

    def scan_all_components(self) -> Dict[str, Any]:
        """
        扫描所有组件的文档

        Returns:
            Dict[str, Any]: 扫描结果
        """
        results = {
            "components_processed": [],
            "documents_scanned": 0,
            "blocks_identified": 0,
            "trace_relationships": 0
        }

        for component in self.component_dirs.keys():
            component_result = self.scan_component(component)
            results["components_processed"].append(component)
            results["documents_scanned"] += component_result.get("documents_scanned", 0)
            results["blocks_identified"] += component_result.get("blocks_identified", 0)
            results["trace_relationships"] += component_result.get("trace_relationships", 0)

        return results

    def scan_component(self, component: str) -> Dict[str, Any]:
        """
        扫描指定组件的文档

        Args:
            component: 组件名称

        Returns:
            Dict[str, Any]: 扫描结果
        """
        if component not in self.component_dirs:
            return {
                "documents_scanned": 0,
                "blocks_identified": 0,
                "trace_relationships": 0,
                "error": f"未知组件: {component}"
            }

        # 扫描组件文档
        documents = self.scanner.scan_component_documents(component)

        # 重建INDEX文件
        self.rebuild_index_file(component, documents)

        return {
            "documents_scanned": len(documents),
            "blocks_identified": sum(doc.get("block_count", 0) for doc in documents),
            "trace_relationships": sum(doc.get("reference_count", 0) for doc in documents)
        }

    def scan_project_documents(self) -> Dict[str, Any]:
        """
        扫描项目中的所有文档

        Returns:
            Dict[str, Any]: 扫描结果
        """
        return self.scan_all_components()

    def update_index_files(self) -> Dict[str, Any]:
        """
        更新所有INDEX文件

        Returns:
            Dict[str, Any]: 更新结果
        """
        results = {
            "components_processed": [],
            "documents_scanned": 0,
            "blocks_identified": 0,
            "trace_relationships": 0
        }

        for component in self.component_dirs.keys():
            # 扫描文档
            documents = self.scanner.scan_component_documents(component)

            # 重建INDEX文件
            if self.rebuild_index_file(component, documents):
                results["components_processed"].append(component)
                results["documents_scanned"] += len(documents)

        return results