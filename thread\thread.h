#ifndef SERIALTHREAD_H
#define SERIALTHREAD_H

//#include <QObject>
//#include <QString>
//#include <QMessageBox>
//#include <QSerialPort>
//#include <QSerialPortInfo>
//#include <QThread>
//#include <QDebug>
//#include <QTime>
//#include <QTimer>
//#include <QMutex>
//#include <QMutexLocker>
//#include "dataProtocols/Header Files/dt2.h"

//class serialThread:public QThread
//{
//    Q_OBJECT
//public:
//    serialThread();
//    ~serialThread();
//    void loop();
//    void setQuitThread(bool isQuit)
//    {
//        m_isQuit = isQuit;
//    }
//    void setLoop(bool isLoop)
//    {
//        m_isLoop = isLoop;
//    }
//    void setStartRecFlag(bool isRec)
//    {
//        m_isStartReceiveData = isRec;
//    }
//    bool getRecFinishFlag(){
//        return m_isRecFinish;
//    }
//    bool setRecFinishFlag(bool isFinish)
//    {
//        return m_isRecFinish = isFinish;
//    }
//    QByteArray getRecData(){
//        return recByteArray;
//    }
//    QStringList &detectPort();

//    QSerialPort *m_serialPort = nullptr;

//signals:


//private slots:
//    void setQuitAndLoop(bool isQuit,bool isLoop);
//    void setCmd(const QByteArray &rec);


//protected:
//    virtual void run();

//private:
//    QStringList *m_comboBOXPortLists = nullptr;
////    scanProtocol  *m_scanProt = nullptr;
//    bool m_isQuit;
//    bool m_isLoop;
//    bool m_isStartReceiveData;
//    bool m_isRecFinish;
//    QByteArray recByteArray;
//    bool isSend;
//    QByteArray sendBytes;
//};

#endif // SERIALTHREADPLC_H
