#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enhanced Workflow Diagram Generator
Creates visual representations of the product development workflow with detailed component connections.
"""

import os
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional

import matplotlib.pyplot as plt
import networkx as nx
from networkx.drawing.nx_agraph import graphviz_layout

class WorkflowDiagramGenerator:
    """Generates visual diagrams of the product development workflow."""
    
    def __init__(self, config_path: str = "config/workflow_config.json"):
        """Initialize the diagram generator with the workflow configuration."""
        self.config_path = config_path
        self.root_path = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        self.config = self.load_config()
        self.connections = self.config.get("workflow", {}).get("connections", [])
        
        # Define component colors for visualization
        self.component_colors = {
            "需求导入": "#FF9999",  # Light red
            "需求分析": "#FFCC99",  # Light orange
            "方案设计": "#FFFF99",  # Light yellow
            "开发规划": "#CCFF99",  # Light green
            "开发": "#99FFFF",      # Light cyan
            "测试": "#9999FF",      # Light blue
            "生产": "#FF99FF",      # Light pink
            "项目输出": "#DDDDDD"   # Light gray
        }
        
        # Define handler type icons
        self.handler_icons = {
            "script": "s",      # Square
            "mcp_server": "h"   # Hexagon
        }
    
    def load_config(self) -> Dict:
        """Load the workflow configuration from the JSON file."""
        config_file = self.root_path / self.config_path
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return {"workflow": {"connections": []}}
    
    def generate_main_workflow_diagram(self, output_path: str) -> None:
        """Generate the main workflow diagram showing component connections."""
        G = nx.DiGraph()
        
        # Add nodes for each unique component
        components = set()
        for conn in self.connections:
            components.add(conn.get("from"))
            components.add(conn.get("to"))
        
        for component in components:
            G.add_node(component)
        
        # Add edges for connections
        for conn in self.connections:
            from_comp = conn.get("from")
            to_comp = conn.get("to")
            
            # Count triggers for edge label
            trigger_count = len(conn.get("triggers", []))
            G.add_edge(from_comp, to_comp, weight=trigger_count, 
                      label=f"{trigger_count} 触发器" if trigger_count > 0 else "")
        
        # Create the figure
        plt.figure(figsize=(12, 8))
        pos = graphviz_layout(G, prog="dot", args="-Grankdir=LR")
        
        # Draw nodes with custom colors
        for component in G.nodes():
            color = self.component_colors.get(component, "#CCCCCC")
            nx.draw_networkx_nodes(G, pos, nodelist=[component], node_color=color, 
                                  node_size=2000, alpha=0.8)
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, width=2, arrowsize=20, alpha=0.7)
        
        # Draw edge labels
        edge_labels = {(u, v): d.get('label', '') for u, v, d in G.edges(data=True)}
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)
        
        # Draw node labels
        nx.draw_networkx_labels(G, pos, font_size=12, font_weight='bold')
        
        # Add title
        plt.title("产品体系开发流程框架", fontsize=16, fontweight='bold')
        
        # Save the figure
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Main workflow diagram saved to {output_path}")
    
    def generate_detailed_connection_diagram(self, output_base_path: str) -> None:
        """Generate detailed diagrams for each connection showing triggers and handlers."""
        for i, conn in enumerate(self.connections):
            from_comp = conn.get("from")
            to_comp = conn.get("to")
            triggers = conn.get("triggers", [])
            
            if not triggers:
                continue
            
            # Create a new graph for this connection
            G = nx.DiGraph()
            
            # Add the main component nodes
            from_node = f"{from_comp}\n(源组件)"
            to_node = f"{to_comp}\n(目标组件)"
            G.add_node(from_node, type="component")
            G.add_node(to_node, type="component")
            
            # Add trigger nodes and connect them
            for j, trigger in enumerate(triggers):
                trigger_name = trigger.get("name")
                trigger_node = f"{trigger_name}\n({trigger.get('type', 'event')})"
                G.add_node(trigger_node, type="trigger")
                G.add_edge(from_node, trigger_node)
                
                # Add handler nodes and connect them to the trigger
                handlers = trigger.get("handlers", [])
                for k, handler in enumerate(handlers):
                    handler_name = handler.get("name")
                    handler_type = handler.get("type")
                    script = handler.get("script", "").split("/")[-1]  # Just the filename
                    handler_node = f"{handler_name}\n[{handler_type}]\n{script}"
                    G.add_node(handler_node, type="handler", handler_type=handler_type)
                    G.add_edge(trigger_node, handler_node)
                    G.add_edge(handler_node, to_node)
            
            # Create the figure
            plt.figure(figsize=(14, 10))
            pos = graphviz_layout(G, prog="dot", args="-Grankdir=LR")
            
            # Draw nodes with different shapes and colors
            component_nodes = [n for n, d in G.nodes(data=True) if d.get("type") == "component"]
            trigger_nodes = [n for n, d in G.nodes(data=True) if d.get("type") == "trigger"]
            handler_nodes = [n for n, d in G.nodes(data=True) if d.get("type") == "handler"]
            
            # Draw component nodes
            nx.draw_networkx_nodes(G, pos, nodelist=[from_node], 
                                  node_color=self.component_colors.get(from_comp, "#CCCCCC"),
                                  node_size=3000, alpha=0.8)
            nx.draw_networkx_nodes(G, pos, nodelist=[to_node], 
                                  node_color=self.component_colors.get(to_comp, "#CCCCCC"),
                                  node_size=3000, alpha=0.8)
            
            # Draw trigger nodes
            nx.draw_networkx_nodes(G, pos, nodelist=trigger_nodes, 
                                  node_color="#FFC107", node_shape="d",
                                  node_size=2500, alpha=0.8)
            
            # Draw handler nodes with different shapes based on type
            for handler_node in handler_nodes:
                handler_type = G.nodes[handler_node].get("handler_type", "script")
                shape = self.handler_icons.get(handler_type, "o")
                color = "#4CAF50" if handler_type == "script" else "#2196F3"  # Green for scripts, blue for MCP
                
                nx.draw_networkx_nodes(G, pos, nodelist=[handler_node], 
                                      node_color=color, node_shape=shape,
                                      node_size=2500, alpha=0.8)
            
            # Draw edges
            nx.draw_networkx_edges(G, pos, width=1.5, arrowsize=15, alpha=0.7)
            
            # Draw node labels with adjusted font size
            label_font_sizes = {
                from_node: 12,
                to_node: 12
            }
            for node in G.nodes():
                font_size = label_font_sizes.get(node, 10)
                nx.draw_networkx_labels(G, pos, labels={node: node}, font_size=font_size)
            
            # Add title
            plt.title(f"连接详情: {from_comp} → {to_comp}", fontsize=16, fontweight='bold')
            
            # Add legend
            plt.figtext(0.01, 0.01, "图例: ◆ 触发器  □ 脚本  ⬡ MCP服务器", fontsize=12)
            
            # Save the figure
            output_path = f"{output_base_path.rsplit('.', 1)[0]}_{i+1}.png"
            plt.axis('off')
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"Detailed connection diagram saved to {output_path}")
    
    def generate_complete_workflow_diagram(self, output_path: str) -> None:
        """Generate a complete workflow diagram with all components, triggers, and handlers."""
        G = nx.DiGraph()
        
        # Add nodes for each component, trigger and handler
        component_nodes = []
        trigger_nodes = []
        handler_nodes = []
        
        # Process all connections
        for conn in self.connections:
            from_comp = conn.get("from")
            to_comp = conn.get("to")
            
            # Add component nodes if they don't exist
            if from_comp not in component_nodes:
                component_nodes.append(from_comp)
                G.add_node(from_comp, type="component")
            
            if to_comp not in component_nodes:
                component_nodes.append(to_comp)
                G.add_node(to_comp, type="component")
            
            # Process triggers
            for trigger in conn.get("triggers", []):
                trigger_name = f"{trigger.get('name')}\n({from_comp} → {to_comp})"
                trigger_nodes.append(trigger_name)
                G.add_node(trigger_name, type="trigger")
                G.add_edge(from_comp, trigger_name)
                
                # Process handlers
                for handler in trigger.get("handlers", []):
                    handler_name = f"{handler.get('name')}\n[{handler.get('type')}]"
                    handler_nodes.append(handler_name)
                    G.add_node(handler_name, type="handler", handler_type=handler.get("type"))
                    G.add_edge(trigger_name, handler_name)
                    G.add_edge(handler_name, to_comp)
        
        # Create the figure (larger size for the complete diagram)
        plt.figure(figsize=(20, 14))
        
        # Use graphviz for layout
        pos = graphviz_layout(G, prog="dot", args="-Grankdir=LR")
        
        # Draw component nodes
        for comp in component_nodes:
            nx.draw_networkx_nodes(G, pos, nodelist=[comp], 
                                  node_color=self.component_colors.get(comp, "#CCCCCC"),
                                  node_size=3000, alpha=0.8)
        
        # Draw trigger nodes
        nx.draw_networkx_nodes(G, pos, nodelist=trigger_nodes, 
                              node_color="#FFC107", node_shape="d",
                              node_size=2000, alpha=0.7)
        
        # Draw handler nodes with different shapes based on type
        for handler in handler_nodes:
            handler_type = G.nodes[handler].get("handler_type", "script")
            shape = self.handler_icons.get(handler_type, "o")
            color = "#4CAF50" if handler_type == "script" else "#2196F3"
            
            nx.draw_networkx_nodes(G, pos, nodelist=[handler], 
                                  node_color=color, node_shape=shape,
                                  node_size=2000, alpha=0.7)
        
        # Draw edges
        nx.draw_networkx_edges(G, pos, width=1, arrowsize=15, alpha=0.6)
        
        # Draw labels with adjusted font sizes
        component_labels = {node: node for node in component_nodes}
        trigger_labels = {node: node for node in trigger_nodes}
        handler_labels = {node: node for node in handler_nodes}
        
        nx.draw_networkx_labels(G, pos, labels=component_labels, font_size=12, font_weight='bold')
        nx.draw_networkx_labels(G, pos, labels=trigger_labels, font_size=8)
        nx.draw_networkx_labels(G, pos, labels=handler_labels, font_size=8)
        
        # Add title
        plt.title("产品体系开发完整流程图 (含触发器和处理器)", fontsize=18, fontweight='bold')
        
        # Add legend
        plt.figtext(0.01, 0.01, "图例: ■ 组件  ◆ 触发器  □ 脚本  ⬡ MCP服务器", fontsize=14)
        
        # Save the figure
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Complete workflow diagram saved to {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Workflow Diagram Generator")
    parser.add_argument("--config", default="config/workflow_config.json", help="Path to workflow configuration file")
    parser.add_argument("--output", default="reports/workflow_diagram.png", help="Base output path for diagrams")
    parser.add_argument("--type", choices=["main", "detailed", "complete", "all"], default="all", 
                        help="Type of diagram to generate")
    
    args = parser.parse_args()
    
    generator = WorkflowDiagramGenerator(args.config)
    
    if args.type in ["main", "all"]:
        generator.generate_main_workflow_diagram(args.output)
    
    if args.type in ["detailed", "all"]:
        detailed_base = args.output.rsplit('.', 1)[0] + "_detailed.png"
        generator.generate_detailed_connection_diagram(detailed_base)
    
    if args.type in ["complete", "all"]:
        complete_path = args.output.rsplit('.', 1)[0] + "_complete.png"
        generator.generate_complete_workflow_diagram(complete_path)

if __name__ == "__main__":
    main() 