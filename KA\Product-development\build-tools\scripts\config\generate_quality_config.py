#!/usr/bin/env python3
"""
质量保证配置生成器
负责生成项目的质量保证相关配置文件
"""

import os
import json
from pathlib import Path
import argparse


def generate_quality_config(project_path=".", project_type="single_layer"):
    """生成质量保证配置文件"""
    
    # 创建配置目录
    config_dir = Path(project_path) / 'config'
    config_dir.mkdir(exist_ok=True)
    
    # 质量保证配置
    quality_config = {
        "testing_settings": {
            "unit_test_coverage": 80,
            "integration_test_required": True,
            "performance_test_required": True,
            "security_scan_enabled": True
        },
        "quality_gates": [
            "code_review",
            "test_coverage",
            "security_scan"
        ],
        "reporting": {
            "test_results": "quality/test_results.json",
            "coverage_report": "quality/coverage_report.html"
        }
    }
    
    # 写入配置文件
    config_file = config_dir / 'quality_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(quality_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 质量保证配置文件已生成: {config_file}")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成质量保证配置文件')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'], help='项目类型')
    
    args = parser.parse_args()
    
    success = generate_quality_config(args.project_path, args.project_type)
    
    if success:
        print("[+] 质量保证配置生成完成")
    else:
        print("[X] 质量保证配置生成失败")
        exit(1)


if __name__ == "__main__":
    main() 