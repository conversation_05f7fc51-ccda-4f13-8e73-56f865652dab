{"systems": {"task_master": {"api_url": "http://localhost:3001", "api_key": "${TASK_MASTER_API_KEY}"}, "visualization": {"api_url": "http://localhost:5000", "api_key": "${VISUALIZATION_API_KEY}"}}, "api_gateway": {"host": "0.0.0.0", "port": 3010, "enable_cors": true, "allowed_origins": ["*"]}, "data_exchange": {"mode": "pull", "interval": 300, "mappings": [{"source": {"system": "task_master", "endpoint": "/api/tasks"}, "destination": {"system": "visualization", "endpoint": "/api/data/import/tasks"}, "transform": "identity"}, {"source": {"system": "visualization", "endpoint": "/api/documents"}, "destination": {"system": "task_master", "endpoint": "/api/data/import/documents"}, "transform": "document_to_task"}], "export_paths": {"tasks": "/shared/data/tasks_export.json", "documents": "/shared/data/documents_export.json", "git": "/shared/data/git_export.json"}}, "events": {"task_update": {"source": "task_master", "notify": ["visualization"], "delivery": "webhook", "webhook_url": "http://localhost:3010/api/webhook/taskmaster"}, "document_change": {"source": "visualization", "notify": ["task_master"], "delivery": "webhook", "webhook_url": "http://localhost:3010/api/webhook/visualization"}, "git_commit": {"source": "github", "notify": ["task_master", "visualization"], "delivery": "webhook", "webhook_url": "http://localhost:3010/api/webhook/github"}}, "logging": {"level": "INFO", "file": "integration.log", "max_size": 10485760, "backup_count": 5}, "extensions": {"data_sources": [{"name": "jira", "enabled": false, "config": {"url": "https://your-jira-instance.atlassian.net", "username": "${JIRA_USERNAME}", "api_token": "${JIRA_API_TOKEN}"}}, {"name": "gitlab", "enabled": false, "config": {"url": "https://gitlab.com", "private_token": "${GITLAB_TOKEN}"}}], "transformers": [{"name": "task_to_confluence", "module": "transformers.task_to_confluence", "enabled": false}, {"name": "jira_to_task", "module": "transformers.jira_to_task", "enabled": false}]}}