#ifndef  _GENERAL_STRING_P_H_
#define  _GENERAL_STRING_P_H_

#include <QByteArray>
#include "IStringPtl.h"
#include <QVector>

class CGeneralStringP:public IStringPtl
{
public:
    CGeneralStringP();
    ~CGeneralStringP();

    //*协议cmd枚举
//    enum class EInteractionFrame {
//        eHEADER     = 0xA53E,
//        eHEADER_LEN = 0x06,
//    };

//    //* data info frame
//    enum class EDataInfoFrame {
//        eHEADER     = 0x55A5,
//        eHEADER_LEN = 0x06,
//    };

    typedef struct {
        QString             header;
        QString             cmd;
        QString             data;
        QString             tail;
    } StInteractionFrame;


    typedef struct {
      uint16_t              header; //帧头
      uint8_t               addr; //指令id
      uint8_t               num;
      uint8_t               signal;
      uint16_t              distance;
      QVector<uint32_t>     measure_info;
      uint8_t               check_sum;
    } StDataInfoFrame;

    void headerWrite(const QString &header) override;
    QByteArray getControlCmd(const QString &cmd_id) override;
    QByteArray getWriteCmd(const QString &cmd_id, const QString &data) override;
    QByteArray getReadCmd(const QString &cmd_id) override;

private:
    StInteractionFrame* mst_interaction_frame_ = nullptr;
    StDataInfoFrame* mst_data_info_frame_ = nullptr;
};


#endif
