#!/usr/bin/env python3
"""
开发流程工具 - FastMCP原生实现
包含辅助工具开发、代码质量检查、质量验证报告等功能
对应工具：T15, T16, T22

使用FastMCP原生的Annotated + Field方式定义参数
集成智能参数处理系统
"""

import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Annotated
from pydantic import Field

# 导入智能参数处理工具
try:
    from .fastmcp_param_utils import smart_param_helper
except ImportError:
    try:
        from fastmcp_param_utils import smart_param_helper
    except ImportError:
        # 提供简单的替代实现
        class SimpleParamHelper:
            async def process_params(self, ctx, tool_name, params):
                return params
            def cache_successful_params(self, tool_name, params):
                pass
        smart_param_helper = SimpleParamHelper()

# 设置脚本基础路径
SCRIPTS_BASE = Path(__file__).parent.parent.parent / "scripts"
DEBUG_MODE = True  # 调试模式：True=模拟执行，False=真实执行

def run_script_silently(script_path: Path, args: Optional[List[str]] = None, cwd: Optional[str] = None) -> Dict[str, Any]:
    """
    静默运行脚本并返回结果

    Args:
        script_path: 脚本路径
        args: 脚本参数
        cwd: 工作目录

    Returns:
        Dict: 包含success, stdout, stderr的结果字典
    """
    if DEBUG_MODE:
        # 调试模式：返回模拟结果
        return {
            "success": True,
            "stdout": json.dumps({
                "success": True,
                "message": f"[调试模式] 模拟执行脚本: {script_path.name}",
                "script_path": str(script_path),
                "args": args or [],
                "simulated": True
            }, ensure_ascii=False, indent=2),
            "stderr": "",
            "returncode": 0
        }

    try:
        cmd = [sys.executable, str(script_path)]
        if args:
            cmd.extend(args)

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=cwd or str(script_path.parent),
            timeout=60  # 开发工具可能需要更长时间
        )

        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "脚本执行超时",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"脚本执行异常: {str(e)}",
            "returncode": -1
        }

async def analyze_code_quality(
    ctx,
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    analysis_type: Annotated[str, Field(description="分析类型，可选值：full（全面分析）, basic（基础分析）, security（安全分析）, style（风格分析）")] = "full",
    language: Annotated[str, Field(description="编程语言，可选值：auto（自动检测）, python, javascript, java等")] = "auto",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    代码质量检查 (T16)
    分析代码质量指标，检测代码问题，生成质量评分、问题列表、改进建议报告

    Args:
        ctx: FastMCP Context对象
        project_path: 项目路径
        analysis_type: 分析类型
        language: 编程语言
        kwargs: 其他参数

    Returns:
        Dict: 包含代码质量分析结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "analyze_code_quality", {
            "project_path": project_path,
            "analysis_type": analysis_type,
            "language": language,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 调用脚本 - 路径配置参考关联表T16行Scripts链接列
        script_path = SCRIPTS_BASE / "quality" / "analyze_code_quality.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["project_path"],
                params["analysis_type"],
                params["language"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("analyze_code_quality", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "analyze_code_quality",
                            "execution_time": "< 10s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "analyze_code_quality",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "analyze_code_quality",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"代码质量分析时发生错误: {str(e)}",
            "project_path": project_path,
            "metadata": {
                "tool_name": "analyze_code_quality",
                "execution_time": "< 1s"
            }
        }

async def create_project_dashboard(
    ctx,
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    dashboard_type: Annotated[str, Field(description="仪表板类型，可选值：full（完整仪表板）, summary（摘要仪表板）, metrics（指标仪表板）")] = "full",
    output_format: Annotated[str, Field(description="输出格式，可选值：html, json, pdf")] = "html",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    辅助工具开发 (T15)
    创建项目仪表板，返回仪表板HTML文件和统计数据

    Args:
        ctx: FastMCP Context对象
        project_path: 项目路径
        dashboard_type: 仪表板类型
        output_format: 输出格式
        kwargs: 其他参数

    Returns:
        Dict: 包含仪表板创建结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "create_project_dashboard", {
            "project_path": project_path,
            "dashboard_type": dashboard_type,
            "output_format": output_format,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 调用脚本 - 路径配置参考关联表T15行Scripts链接列
        script_path = SCRIPTS_BASE / "dashboard" / "create_project_dashboard.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["project_path"],
                params["dashboard_type"],
                params["output_format"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("create_project_dashboard", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "create_project_dashboard",
                            "execution_time": "< 10s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "create_project_dashboard",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "create_project_dashboard",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }



    except Exception as e:
        return {
            "success": False,
            "error": f"仪表板创建时发生错误: {str(e)}",
            "project_path": project_path,
            "metadata": {
                "tool_name": "create_project_dashboard",
                "execution_time": "< 1s"
            }
        }

async def generate_document_report(
    ctx,
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    report_type: Annotated[str, Field(description="报告类型，可选值：quality（质量报告）, coverage（覆盖报告）, statistics（统计报告）, links（链接报告）")] = "quality",
    output_format: Annotated[str, Field(description="输出格式，可选值：markdown, html, json, pdf")] = "markdown",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    质量验证报告 (T22)
    生成项目文档统计报告，包含文档数量、类型分布、关联关系、质量评估等详细信息

    Args:
        ctx: FastMCP Context对象
        project_path: 项目路径
        report_type: 报告类型
        output_format: 输出格式
        kwargs: 其他参数

    Returns:
        Dict: 包含文档报告生成结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "generate_document_report", {
            "project_path": project_path,
            "report_type": report_type,
            "output_format": output_format,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 调用脚本 - 路径配置参考关联表T22行Scripts链接列
        script_path = SCRIPTS_BASE / "quality" / "generate_document_report.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["project_path"],
                params["report_type"],
                params["output_format"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("generate_document_report", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "generate_document_report",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "generate_document_report",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "generate_document_report",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"文档报告生成时发生错误: {str(e)}",
            "project_path": project_path,
            "metadata": {
                "tool_name": "generate_document_report",
                "execution_time": "< 1s"
            }
        }
