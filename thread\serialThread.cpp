#include "motorMonitorSerial.h"
#include <QApplication>
#include <QThread>


motorMonitorSerial::motorMonitorSerial(QObject *parent):
        QObject(parent)
      , m_task_id(0)
      , m_single_receive(false)
      , m_serial_port(new QSerialPort())
      , m_transBoard_port(new QSerialPort)
//      , m_comboBOXPortLists(new QStringList)
{

}

motorMonitorSerial::~motorMonitorSerial()
{
  delete m_serial_port;
  delete m_transBoard_port;
}

void motorMonitorSerial::loop()
{
    m_serial_port->setReadBufferSize(2000);//与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析
    QByteArray arr;
    for(;;)
    {
        if(m_task_id == protocol_type::motor) //电机检测治具
        {
            m_serial_port->waitForReadyRead(1000);
//            qApp->processEvents(); //处理密集型耗时的事情
            arr = m_serial_port->readAll();
            m_dt2Protocol->motorDataParsing(arr, 100);
        }
        else if(m_task_id == protocol_type::bottom) //YJ bottom
        {
            m_serial_port->waitForReadyRead(10);
            arr = m_serial_port->readAll();
            if(arr.length() > 0){
                if(m_yjProtocol->parseBottomProtocolNarwal(arr, 100))
                    m_task_id = -1; //底板 parse
            }
        }
        else if(m_task_id == protocol_type::sensor_cloud) //YJ sensor
        {
            m_serial_port->waitForReadyRead(50);
            arr = m_serial_port->readAll();
            if(arr.length() > 0)
                if(m_yjProtocol->parseCloudProtocolNarwal(arr, 100) && m_single_receive)
                    m_task_id = -1; //sensor parse
        }
        else if(m_task_id == protocol_type::transBoard) //YJ transboard
        {
            m_transBoard_port->waitForReadyRead(10);
            arr = m_transBoard_port->readAll();
            if(arr.length() > 0)
                if(m_yjProtocol->parseTransboardProtocolNarwal(arr, 100))
                    m_task_id = -1; //转接板 parse
        }
        else if(m_task_id == -1) //wait
        {
            QThread::msleep(1);
        }
        else //退出线程
        {
           m_dt2Protocol->m_strPre.clear();
           break; //return
        }
    }
}

//void motorMonitorSerial::serialPortReceive()
//{
//    QByteArray temp(g_serial_port->readAll());//读取数据
//    QString tempStr;

//    if(ui->ReceiveCheckHexBox->checkState()==Qt::Unchecked)
//    {
//        tempStr=QString::fromLocal8Bit(temp);
//    }
//    else
//    {
//        tempStr=temp.toHex();
//    }

//    if(!temp.isEmpty())
//    {
//        ui->ReceivedMesEdit->moveCursor(QTextCursor::End);
//        ui->ReceivedMesEdit->insertPlainText(tempStr);
//        if(temp[0] == 'C'||temp[0] == 'c')
//        {
//            timer->stop();
//            disconnect(g_serial_port, &QSerialPort::readyRead, this, &Widget::serialPortReceive);// 连接固件升级的槽函数
//        }
//    }
//    this->receive_number_+=temp.size();
//    ui->UartReceiveNumLable->setText(QString::number(this->receive_number_));
//    temp.clear();
//    tempStr.clear();
//}

//void motorMonitorSerial::run()
//{
//    m_isQuit = false;
//    m_isLoop = false;
//    isSend = false;
//    m_serial_port = new QSerialPort();
//    m_comboBOXPortLists = new QStringList();
//    //m_faculaWaveProtocol = new FaculaWaveProtocol();
////    m_scanProt = new dt2Protocol();

//    //qDebug() << "plc thread" << QThread::currentThreadId();
//    loop();
//    delete m_serial_port;
//    delete m_comboBOXPortLists;
//}

void motorMonitorSerial::task_id_change(const enum protocol_type &id)
{
    m_task_id = id;
    if(id == motor)
        m_dt2Protocol->m_strPre.clear();
    else if(id == bottom || id == sensor_cloud || id == transBoard)
        m_yjProtocol->m_strPre.clear();
}

//void motorMonitorSerial::main_serial_write(const enum protocol_type &type, const char *data, const uint8_t &len)
//{
//    task_id_change(type);
//    m_serial_port->write(data, len);
//}
//void motorMonitorSerial::transB_serial_write(const enum protocol_type &type, const char *data, const uint8_t &len)
//{
//    task_id_change(type);
//    m_transBoard_port->write(data, len);
//}

