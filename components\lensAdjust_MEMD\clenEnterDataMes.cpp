#include "clenEnterDataMes.h"
#include "ui_clenEnterDataMes.h"
#include <QShortcut>
#include <QThread>


clenEnterDataMes::clenEnterDataMes(QWidget *parent)
    : QDockWidget(parent), ui(new Ui::clenEnterDataMes), mst_config_(new NClenEnterDataMes::StUiConfig), mc_operation_(new clenEnterDataMesOpt(*mst_config_)) {
    ui->setupUi(this);
    //    this->setWindowIcon(QIcon("cspc1.jpg")); //与ui配置的会冲突
    this->setWindowTitle(LEN_MEMD_APP_WINDOW_TITLE);

    ITable::StTableInfo origin_table_info = mc_operation_->getOriginTableInfo();
    mc_grey_map_                          = new CTableViewModule(ui->greyMap, origin_table_info);
    mc_grey_map_->targetFaculaAreaShow();


    //* 3.信号与槽
    QShortcut *temp = new QShortcut(this);
    //设置键值，也就是设置快捷键.
    //    temp->setKey(tr("ctrl+space"));
    temp->setKey(Qt::CTRL + Qt::Key_Space);
    //这个成员函数挺关键的，设置是否会自动反复按键.也就是说，当你一直按住键盘ctrl+空格时，会一直不停的调用对应的槽函数.
    temp->setAutoRepeat(false);

    //连接信号与槽，showSlot()是自定义的槽函数!
    connect(temp, SIGNAL(activated()), this, SLOT(on_reload_clicked()));

    //    connect(mc_operation_, &clenEnterDataMesOpt::mainMapSizeSignal,
    //            this, &clenEnterDataMes::mpUpdate);

    connect(mc_operation_, &clenEnterDataMesOpt::moduleInfoShowSignal, this, &clenEnterDataMes::processTaskInfoShow_slot);

    connect(mc_operation_, &clenEnterDataMesOpt::portUpdateSignal, this, &clenEnterDataMes::portListShow);  //列表更新

    connect(mc_operation_, &clenEnterDataMesOpt::readySignal, this, &clenEnterDataMes::readyShow);      // ready
    qRegisterMetaType<QVector<QVector<uint32_t>>>("map_matrix");                                        //注册自定义类型信号槽
    connect(mc_operation_, &clenEnterDataMesOpt::dataAckSignal, this, &clenEnterDataMes::dataAckShow);  // data ack

    connect(mc_operation_, &clenEnterDataMesOpt::compAckSignal, this, &clenEnterDataMes::compAckShow);  // complete ack

    connect(mc_operation_, &clenEnterDataMesOpt::resultSignal, this, &clenEnterDataMes::resultShow);  // result
}

clenEnterDataMes::~clenEnterDataMes() {
    delete ui;
    delete mc_grey_map_;
    delete mst_config_;
    delete mc_operation_;
}


void clenEnterDataMes::closeEvent(QCloseEvent *event) {
    Q_UNUSED(event);

    this->setAttribute(Qt::WA_DeleteOnClose);  //释放窗口资源

    QThread::msleep(50);

    emit windowCloseSiganl(true);
}

/**
 * @brief 配置更新
 */
void clenEnterDataMes::updateConfig(NClenEnterDataMes::StUiConfig *config_) {
    config_->port_name     = ui->portBox->currentText();
    config_->cur_port_name = ui->portBox->currentText();

    //    config_->work_number = ui->workOrder->text();
    //    config_->nbr_flag = ui->nbrFlag->isChecked();
    config_->is_auto_judge_facula = ui->auto_judge_facula->isChecked();
}

// void clenEnterDataMes::greyMapShowInit(QTableWidget *table_, const uint8_t &xlen, const uint8_t &ylen)
//{
//    table_->setRowCount(ylen);
//    table_->setColumnCount(xlen);
//    table_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch); //列自适应cell大小
//    table_->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch); //行自适应cell大小
//    table_->horizontalHeader()->setVisible(false); //隐藏水平表头
//    table_->verticalHeader()->setVisible(false); //隐藏垂直表头
//    table_->setEditTriggers(QAbstractItemView::NoEditTriggers); //表格不可编辑
//    table_->setFont(QFont("黑体", 15));
//}

/**
 * @brief 更新窗口列表
 */
void clenEnterDataMes::portListShow(QStringList *port_list_, bool port_flag) {
    ui->portBox->blockSignals(true);  //必须需要阻塞信号  否则会发出不必要的信号触发回调  导致记录端口名出错

    ui->portBox->clear();                //会触发currentIndexChanged回调函数
    ui->portBox->addItems(*port_list_);  //会触发消息事件

    if (!port_flag)
        ui->portBox->setCurrentIndex(0);
    else
        ui->portBox->setCurrentText(mst_config_->cur_port_name);

    ui->portBox->blockSignals(false);
}


void clenEnterDataMes::processTaskInfoShow_slot(const bool &is_error, const QString &info) {
    if (is_error) {
        ui->processView->setTextColor(Qt::red);
        //        ui->processView->append("<font color=\"#FF0000\"");
        //        ui->processView->setStyleSheet(process_unnormal_sheetStyle);
    } else {
        ui->processView->setTextColor(Qt::black);
        //        ui->processView->setStyleSheet(process_normal_sheetStyle);
    }

    ui->processView->append(info);
}

// void clenEnterDataMes::greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data)
//{
//    uint8_t alpha_tmp = 180, rgb_tmp;
//    uint32_t data_tmp = 0;

//    uint8_t xlens = map_info_->xlens;
//    uint8_t ylens = map_info_->ylens;
//    uint32_t data_max = map_data.max_peak>10?map_data.max_peak:10; //peak 最小值

//    for (int y = 0; y < ylens; ++y)
//    {
//        for (int x = 0; x < xlens; ++x)
//        {
//            /*1. 数值 update*/
//            data_tmp = map_data.map_matrix.at(y).at(x);
//            map_info_->table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

//            /*3. 背景颜色*/
//            QColor color;
//            rgb_tmp = 255 - (data_tmp * 255 / data_max);
//            alpha_tmp = data_tmp * 255 / data_max;
//            color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
//            map->item(y,x)->setBackground(QBrush(color));
//        }
//    }
//}

/**
 * @brief 显示界面清空
 */
void clenEnterDataMes::resultClean(void) {
    //* status bar clean
    ui->processView->setTextColor(Qt::black);
    //    ui->processView->setStyleSheet(process_normal_sheetStyle);
    ui->processView->clear();

    ui->finalResult->setStyleSheet("color: black;");
    ui->finalResult->setText("wait");
}


void clenEnterDataMes::readyShow(bool is_open) {
    if (is_open) {
        ui->reload->setText("downloadding");
        resultClean();
    }
}

void clenEnterDataMes::dataAckShow(const uint &max, const QVector<QVector<uint32_t>> &matrix) {
    mc_grey_map_->greyMapShow(max, matrix);
}

/**
 * @brief
 */
void clenEnterDataMes::compAckShow(bool is_comp) {
    Q_UNUSED(is_comp);
    //* 切换界面
    //  if((mst_config_->mode == IClensMachine::EMode::manual_mode) && (ui->modeBox->currentIndex() == 0)) //切换界面
    //  on_modeBox_currentIndexChanged(0);

    //  on_modeBox_currentIndexChanged(1);

    ui->reload->setText("download");  //
}

/**
 * @brief 光斑结果显示
 * @param result
 * @param result_index
 */
void clenEnterDataMes::resultShow(EResult result, const uint8_t &result_index) {
    switch (result_index) {
    case 0:
        if (result == EResult::ePASS) {
            ui->finalResult->setStyleSheet("color: green;");
            ui->finalResult->setText("PASS");
        } else {
            ui->finalResult->setStyleSheet("color: red;");
            ui->finalResult->setText("NG");
        }
        break;
    default:
        if (result == EResult::ePASS) {
            ui->finalResult->setStyleSheet("color: green;");
            ui->finalResult->setText("PASS");
        } else {
            ui->finalResult->setStyleSheet("color: red;");
            ui->finalResult->setText("NG");
        }
        break;
    }
}

/**
 * @brief: 串口开关
 */
void clenEnterDataMes::on_reload_clicked() {
    if (ui->reload->text() == QString("download")) {                                      //
        updateConfig(mst_config_);                                                        //配置更新
        mc_operation_->mv_task_list[clenEnterDataMesOpt::eOPEN_SERIAL].flag.exec = true;  //开始任务
    } else {                                                                              //关闭->退出
        // if(mst_config_->mode) mc_operation_->mv_task_list[clenEnterDataMesOpt::eCOMPLETE].flag.stop = true; //结束任务
        mc_operation_->mv_task_list[clenEnterDataMesOpt::eCOMPLETE].flag.stop = true;  //结束任务
    }
}

void clenEnterDataMes::on_portBox_currentIndexChanged(int index) {
    Q_UNUSED(index);
    mst_config_->cur_port_name = ui->portBox->currentText();
}


// void clenEnterDataMes::on_mesDataInput_clicked()
//{
//    if(mc_lens_data_mes_ == nullptr) {
//        mc_lens_data_mes_ = new clenEnterDataMes;
//        mc_lens_data_mes_->show();
//        this->showMinimized();
//        QObject::connect(mc_lens_data_mes_, &clenEnterDataMes::windowCloseSiganl,
//                         this, &clenEnterDataMes::lenMesDataFB);
//    }
//}

// void clenEnterDataMes::lenMesDataFB(bool closed) {
//    if(closed && mc_lens_data_mes_ != nullptr) {
//        QObject::disconnect(mc_lens_data_mes_, &clenEnterDataMes::windowCloseSiganl, this, &clenEnterDataMes::novaCalibFB); //断开连接
//        //        timeId_ptr_->id6 = false;
//        delete mc_lens_data_mes_;
//        mc_lens_data_mes_ = nullptr;
//        this->showNormal();
//    }
//}
