#ifndef _MOTOR_TEST_B_H_
#define _MOTOR_TEST_B_H_

#include <QMap>
#include <QVector>
#include "ITestBoard.h"
#include "IComm.h"
#include "processListA.h"

class CMotorTestB:public ITestBoard
{
  Q_OBJECT
public:
  CMotorTestB(IComm *port_); //设备端口与PC端口绑定，构造时直接绑定 PC端口
  ~CMotorTestB();

  //static const QMap<QString, QVector<unsigned char>> s_motorB_cmd;

  uint getUartBaud() override;
  bool start(const uint8_t& id, const QByteArray &data) override; //开始
  bool stop(const QByteArray &data) override; //停止

  QByteArray portDataRead() override;
  void change_Icom_interface(IComm *port_) override;
  bool dataParsing(QByteArray str, const int &length) override;
signals:
  void dataOutput(ITestBoard::ECommStep, ECommStatus, QByteArray);

private:
  enum EProtocolId{
    eCONTINUAL          = 0xa1, //连续
    eCYCLE              = 0xa2, //定圈
    eTIME               = 0xa3, //定时

    eSTOP               = 0xB1, //
  };

  IComm* im_port_ = nullptr;
  QByteArray m_str_send; //指令数据

};


#endif
