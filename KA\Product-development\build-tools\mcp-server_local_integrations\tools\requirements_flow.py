#!/usr/bin/env python3
"""
需求管理流程工具 - FastMCP原生实现
包含需求导入、分析、矩阵维护、分解等功能
对应工具：T01, T03, T04, T05

使用FastMCP原生的Annotated + Field方式定义参数
集成智能参数处理系统
"""

import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Annotated
from pydantic import Field

# 导入智能参数处理工具
try:
    from .fastmcp_param_utils import smart_param_helper
except ImportError:
    try:
        from fastmcp_param_utils import smart_param_helper
    except ImportError:
        # 提供简单的替代实现
        class SimpleParamHelper:
            async def process_params(self, ctx, tool_name, params):
                return params
            def cache_successful_params(self, tool_name, params):
                pass
        smart_param_helper = SimpleParamHelper()

# 设置脚本基础路径
SCRIPTS_BASE = Path(__file__).parent.parent.parent / "scripts"
DEBUG_MODE = True  # 调试模式：True=模拟执行，False=真实执行

# 注释：脚本路径配置遵循单一来源原则，具体路径请参考产品体系构建工具关联表.md

def run_script_silently(script_path: Path, args: List[str] = None, cwd: str = None) -> Dict[str, Any]:
    """
    静默运行脚本并返回结果

    Args:
        script_path: 脚本路径
        args: 脚本参数
        cwd: 工作目录

    Returns:
        Dict: 包含success, stdout, stderr的结果字典
    """
    if DEBUG_MODE:
        # 调试模式：返回模拟结果
        return {
            "success": True,
            "stdout": json.dumps({
                "success": True,
                "message": f"[调试模式] 模拟执行脚本: {script_path.name}",
                "script_path": str(script_path),
                "args": args or [],
                "simulated": True
            }, ensure_ascii=False, indent=2),
            "stderr": "",
            "returncode": 0
        }

    try:
        cmd = [sys.executable, str(script_path)]
        if args:
            cmd.extend(args)
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=cwd or str(script_path.parent),
            timeout=30
        )
        
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "脚本执行超时",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": f"脚本执行异常: {str(e)}",
            "returncode": -1
        }

async def import_requirements(
    ctx,
    source_file: Annotated[str, Field(description="源文件路径，支持JSON/Markdown/Excel格式的需求文档")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    output_format: Annotated[str, Field(description="输出格式，可选值：json, markdown, excel")] = "json",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    市场需求收集 (T01)
    从文档文件导入需求，支持多种格式(JSON/Markdown/Excel)，返回导入数量、输出路径、需求列表
    
    Args:
        ctx: FastMCP Context对象
        source_file: 源文件路径
        project_path: 项目路径
        output_format: 输出格式
        kwargs: 其他参数
    
    Returns:
        Dict: 包含导入结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "import_requirements", {
            "source_file": source_file,
            "project_path": project_path,
            "output_format": output_format,
            "kwargs": kwargs
        })
        
        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}
        
        # 验证源文件存在
        source_path = Path(params["source_file"])
        if not source_path.exists():
            return {
                "success": False,
                "error": f"源文件不存在: {params['source_file']}",
                "source_file": params["source_file"]
            }
        
        # 调用脚本 - 路径配置参考关联表T01行Scripts链接列
        script_path = SCRIPTS_BASE / "requirements" / "import_requirements.py"
        
        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["source_file"],
                params["project_path"],
                params["output_format"],
                json.dumps(kwargs_dict)
            ]
            
            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )
            
            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])
                    
                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("import_requirements", params)
                    
                    return {
                        "success": True,
                        "data": output_data,
                        "source_file": params["source_file"],
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "import_requirements",
                            "execution_time": "< 3s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "import_requirements",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "import_requirements",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }
    
    except Exception as e:
        return {
            "success": False,
            "error": f"需求导入时发生错误: {str(e)}",
            "source_file": source_file,
            "metadata": {
                "tool_name": "import_requirements",
                "execution_time": "< 1s"
            }
        }

async def analyze_requirements(
    ctx,
    requirements_file: Annotated[str, Field(description="需求文件路径，包含要分析的需求数据")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    analysis_type: Annotated[str, Field(description="分析类型，可选值：full（全面分析）, basic（基础分析）, priority（优先级分析）, dependency（依赖关系分析）")] = "full",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    技术需求整理 (T03) / 需求分解 (T04)
    分析需求文档内容，生成需求统计、分类、优先级评估、依赖关系分析报告

    Args:
        ctx: FastMCP Context对象
        requirements_file: 需求文件路径
        project_path: 项目路径
        analysis_type: 分析类型
        kwargs: 其他参数

    Returns:
        Dict: 包含分析结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "analyze_requirements", {
            "requirements_file": requirements_file,
            "project_path": project_path,
            "analysis_type": analysis_type,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证需求文件存在
        req_file_path = Path(params["requirements_file"])
        if not req_file_path.exists():
            return {
                "success": False,
                "error": f"需求文件不存在: {params['requirements_file']}",
                "requirements_file": params["requirements_file"]
            }

        # 调用脚本 - 路径配置参考关联表T03/T04行Scripts链接列
        script_path = SCRIPTS_BASE / "requirements" / "analyze_requirements.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["requirements_file"],
                params["project_path"],
                params["analysis_type"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("analyze_requirements", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "requirements_file": params["requirements_file"],
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "analyze_requirements",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "analyze_requirements",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "analyze_requirements",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"需求分析时发生错误: {str(e)}",
            "requirements_file": requirements_file,
            "metadata": {
                "tool_name": "analyze_requirements",
                "execution_time": "< 1s"
            }
        }

async def create_requirements_matrix(
    ctx,
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    matrix_type: Annotated[str, Field(description="矩阵类型，可选值：traceability（追溯矩阵）, dependency（依赖矩阵）, coverage（覆盖矩阵）")] = "traceability",
    requirements_file: Annotated[Optional[str], Field(description="需求文件路径，可选参数")] = None,
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    需求矩阵维护 (T05)
    创建需求追溯矩阵，建立需求间关联关系，生成矩阵文件和可视化图表

    Args:
        ctx: FastMCP Context对象
        project_path: 项目路径
        matrix_type: 矩阵类型
        requirements_file: 需求文件路径
        kwargs: 其他参数

    Returns:
        Dict: 包含矩阵创建结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "create_requirements_matrix", {
            "project_path": project_path,
            "matrix_type": matrix_type,
            "requirements_file": requirements_file,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证项目路径存在
        project_path_obj = Path(params["project_path"]).resolve()
        if not project_path_obj.exists():
            return {
                "success": False,
                "error": f"项目路径不存在: {params['project_path']}",
                "project_path": params["project_path"]
            }

        # 调用脚本 - 路径配置参考关联表T05行Scripts链接列（使用analyze_requirements.py）
        script_path = SCRIPTS_BASE / "requirements" / "analyze_requirements.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["project_path"],
                params["matrix_type"]
            ]

            if params.get("requirements_file"):
                script_args.extend(["--requirements-file", params["requirements_file"]])

            script_args.append(json.dumps(kwargs_dict))

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("create_requirements_matrix", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "create_requirements_matrix",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "create_requirements_matrix",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 严格遵循架构原则：找不到脚本直接报错，不使用内置逻辑
            return {
                "success": False,
                "error": f"脚本文件不存在: {script_path}",
                "expected_script": str(script_path),
                "architecture_violation": "MCP工具必须调用外部脚本，不允许使用内置逻辑",
                "solution": "请确保脚本文件存在于指定路径，或检查关联表中的脚本路径配置",
                "metadata": {
                    "tool_name": "create_requirements_matrix",
                    "script_path": str(script_path),
                    "architecture_mode": "FastMCP脚本包装模式"
                }
            }



    except Exception as e:
        return {
            "success": False,
            "error": f"需求矩阵创建时发生错误: {str(e)}",
            "project_path": project_path,
            "metadata": {
                "tool_name": "create_requirements_matrix",
                "execution_time": "< 1s"
            }
        }

async def req_to_tasks(
    ctx,
    requirements_matrix: Annotated[str, Field(description="需求矩阵文件路径，包含需求关联关系的矩阵文件")],
    project_path: Annotated[str, Field(description="项目路径，默认为当前目录")] = ".",
    task_format: Annotated[str, Field(description="任务格式，可选值：json, markdown, csv")] = "json",
    kwargs: Annotated[str, Field(description="其他参数，JSON格式字符串")] = "{}"
) -> Dict[str, Any]:
    """
    项目计划制定 (T09)
    将需求转换为具体任务，生成任务列表、时间估算、依赖关系、分配建议

    Args:
        ctx: FastMCP Context对象
        requirements_matrix: 需求矩阵文件路径
        project_path: 项目路径
        task_format: 任务格式
        kwargs: 其他参数

    Returns:
        Dict: 包含任务转换结果的字典
    """
    try:
        # 智能参数处理
        params = await smart_param_helper.process_params(ctx, "req_to_tasks", {
            "requirements_matrix": requirements_matrix,
            "project_path": project_path,
            "task_format": task_format,
            "kwargs": kwargs
        })

        # 解析kwargs
        try:
            kwargs_dict = json.loads(params["kwargs"]) if params["kwargs"] else {}
        except json.JSONDecodeError:
            kwargs_dict = {}

        # 验证矩阵文件存在
        matrix_file_path = Path(params["requirements_matrix"])
        if not matrix_file_path.exists():
            return {
                "success": False,
                "error": f"需求矩阵文件不存在: {params['requirements_matrix']}",
                "requirements_matrix": params["requirements_matrix"]
            }

        # 调用脚本 - 路径配置参考关联表T09行Scripts链接列
        script_path = SCRIPTS_BASE / "integration" / "req_to_tasks.py"

        if script_path.exists():
            # 准备脚本参数
            script_args = [
                params["requirements_matrix"],
                params["project_path"],
                params["task_format"],
                json.dumps(kwargs_dict)
            ]

            result = run_script_silently(
                script_path,
                args=script_args,
                cwd=str(SCRIPTS_BASE)
            )

            if result['success']:
                try:
                    output_data = json.loads(result['stdout'])

                    # 缓存成功的参数
                    smart_param_helper.cache_successful_params("req_to_tasks", params)

                    return {
                        "success": True,
                        "data": output_data,
                        "requirements_matrix": params["requirements_matrix"],
                        "project_path": params["project_path"],
                        "metadata": {
                            "tool_name": "req_to_tasks",
                            "execution_time": "< 5s",
                            "script_path": str(script_path)
                        }
                    }
                except json.JSONDecodeError as e:
                    return {
                        "success": False,
                        "error": f"解析脚本输出失败: {e}",
                        "raw_output": result['stdout']
                    }
            else:
                return {
                    "success": False,
                    "error": f"脚本执行失败: {result.get('stderr', '未知错误')}",
                    "metadata": {
                        "tool_name": "req_to_tasks",
                        "script_path": str(script_path)
                    }
                }
        else:
            # 使用内置逻辑
            try:
                # 读取矩阵文件
                with open(matrix_file_path, 'r', encoding='utf-8') as f:
                    matrix_data = json.load(f)

                # 简单的任务转换逻辑
                tasks = []
                for i, relationship in enumerate(matrix_data.get("matrix", {}).get("relationships", [])):
                    task = {
                        "id": f"TASK-{i+1:03d}",
                        "name": f"实现 {relationship.get('from', 'REQ')} 到 {relationship.get('to', 'IMPL')}",
                        "description": f"根据需求 {relationship.get('from')} 实现 {relationship.get('to')}",
                        "estimated_hours": 8,
                        "priority": "中",
                        "dependencies": [],
                        "assigned_to": "待分配"
                    }
                    tasks.append(task)

                # 保存任务文件
                project_path_obj = Path(params["project_path"]).resolve()
                tasks_dir = project_path_obj / "project_management" / "tasks"
                tasks_dir.mkdir(parents=True, exist_ok=True)

                output_file = tasks_dir / f"tasks.{params['task_format']}"

                if params["task_format"] == "json":
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump({"tasks": tasks, "generation_time": datetime.now().isoformat()}, f, ensure_ascii=False, indent=2)
                elif params["task_format"] == "markdown":
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write("# 项目任务列表\n\n")
                        for task in tasks:
                            f.write(f"## {task['id']}: {task['name']}\n")
                            f.write(f"- **描述**: {task['description']}\n")
                            f.write(f"- **预估工时**: {task['estimated_hours']}小时\n")
                            f.write(f"- **优先级**: {task['priority']}\n")
                            f.write(f"- **分配给**: {task['assigned_to']}\n\n")

                # 缓存成功的参数
                smart_param_helper.cache_successful_params("req_to_tasks", params)

                return {
                    "success": True,
                    "data": {
                        "tasks_file": str(output_file),
                        "tasks_count": len(tasks),
                        "total_estimated_hours": sum(task["estimated_hours"] for task in tasks),
                        "generation_time": datetime.now().isoformat()
                    },
                    "metadata": {
                        "tool_name": "req_to_tasks",
                        "execution_time": "< 1s",
                        "script_path": "内置逻辑"
                    }
                }

            except Exception as e:
                return {
                    "success": False,
                    "error": f"需求转任务失败: {str(e)}",
                    "requirements_matrix": params["requirements_matrix"]
                }

    except Exception as e:
        return {
            "success": False,
            "error": f"需求转任务时发生错误: {str(e)}",
            "requirements_matrix": requirements_matrix,
            "metadata": {
                "tool_name": "req_to_tasks",
                "execution_time": "< 1s"
            }
        }
