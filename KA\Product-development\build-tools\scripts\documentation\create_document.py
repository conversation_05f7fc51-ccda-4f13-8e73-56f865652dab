#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文档创建工具

该脚本用于创建新文档并自动添加到ID表格。
"""

import os
import sys
import json
import argparse
import re
from datetime import datetime
import subprocess
from pathlib import Path

# 导入共享配置
sys.path.append(str(Path(__file__).parent.parent))
from shared_config import (
    get_component_codes,
    get_directory_mapping
)

# 导入统一ID生成器
from common.document_id_generator import DocumentIdGenerator

# 获取配置（替代原来的本地定义）
COMPONENT_CODES = get_component_codes()
DIRECTORY_MAPPING = get_directory_mapping()

# 创建ID生成器实例
id_generator = DocumentIdGenerator()

# 文档类型映射（这是文档特有的配置）
DOC_TYPE_MAPPING = {
    "MATRIX": "矩阵",
    "SPEC": "规格",
    "PLAN": "计划",
    "REPORT": "报告",
    "GUIDE": "指南",
    "CONFIG": "配置",
    "CODE": "代码",
    "DIAGRAM": "图表"
}

# 文档模板
DOCUMENT_TEMPLATES = {
    "MATRIX": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的矩阵文档，用于记录和跟踪{component_name}相关的项目内容。

## 矩阵内容

| ID | 名称 | 描述 | 状态 | 负责人 | 计划日期 | 实际日期 |
|----|------|------|------|--------|----------|----------|
|    |      |      |      |        |          |          |

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "SPEC": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的规格文档，详细描述了{title}的技术规格和要求。

## 规格内容

### 1. 功能规格

- 功能1: 描述
- 功能2: 描述

### 2. 技术规格

- 技术参数1: 值
- 技术参数2: 值

### 3. 接口规格

- 接口1: 描述
- 接口2: 描述

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "PLAN": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的计划文档，详细描述了{title}的计划内容和时间安排。

## 计划内容

### 1. 目标

- 目标1: 描述
- 目标2: 描述

### 2. 里程碑

| 里程碑 | 计划日期 | 负责人 | 交付物 |
|--------|----------|--------|--------|
|        |          |        |        |

### 3. 资源需求

- 资源1: 描述
- 资源2: 描述

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "REPORT": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的报告文档，详细记录了{title}的结果和分析。

## 报告内容

### 1. 执行摘要

（在此处提供报告的主要发现和结论的简要摘要）

### 2. 详细结果

- 结果1: 描述
- 结果2: 描述

### 3. 分析与建议

（在此处提供对结果的分析和建议）

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "GUIDE": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的指南文档，提供了关于{title}的使用和操作指导。

## 指南内容

### 1. 前提条件

- 条件1: 描述
- 条件2: 描述

### 2. 操作步骤

1. 步骤1: 描述
2. 步骤2: 描述

### 3. 常见问题

- 问题1: 解决方案
- 问题2: 解决方案

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "CONFIG": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的配置文档，详细描述了{title}的配置参数和设置。

## 配置内容

### 1. 配置参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
|        |      |        |      |

### 2. 配置示例

```json
{{
  "param1": "value1",
  "param2": "value2"
}}
```

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "CODE": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的代码文档，详细描述了{title}的代码结构和实现。

## 代码内容

### 1. 代码结构

- 文件1: 描述
- 文件2: 描述

### 2. 关键算法

```python
# 示例代码
def example_function():
    pass
```

### 3. 使用示例

```python
# 使用示例
result = example_function()
```

## 相关文档

- [[REF:{related_doc_id}]]

""",
    "DIAGRAM": """# {title}

文档ID: {doc_id}
创建日期: {date}
版本: 1.0

## 概述

本文档是{component_name}组件的图表文档，提供了关于{title}的图形化表示。

## 图表内容

### 1. 系统架构图

```mermaid
graph TD
    A[组件A] --> B[组件B]
    A --> C[组件C]
    B --> D[组件D]
    C --> D
```

### 2. 流程图

```mermaid
flowchart LR
    A[开始] --> B{判断条件}
    B -- 是 --> C[处理1]
    B -- 否 --> D[处理2]
    C --> E[结束]
    D --> E
```

## 相关文档

- [[REF:{related_doc_id}]]

"""
}

def load_component_relations():
    """加载组件关系配置"""
    try:
        with open("config/component_relations.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print("警告: 组件关系配置文件不存在，将使用默认映射")
        # 创建默认配置
        components = []
        for comp_id, comp_name in COMPONENT_CODES.items():
            components.append({
                "id": comp_id,
                "name": comp_name,
                "directory": DIRECTORY_MAPPING.get(comp_id, ""),
                "related_components": []
            })
        return {"components": components}

def get_existing_ids(component_code):
    """获取组件已有的文档ID列表"""
    component_dir = DIRECTORY_MAPPING.get(component_code, "")
    index_path = os.path.join(component_dir, f"{component_code}_INDEX.md")
    
    if not os.path.exists(index_path):
        return []
    
    try:
        with open(index_path, "r", encoding="utf-8") as f:
            content = f.read()
            # 查找所有文档ID
            id_pattern = r"\|\s*([A-Z0-9]+)\s*\|"
            ids = re.findall(id_pattern, content)
            # 过滤掉表头中可能匹配到的"文档ID"
            return [id for id in ids if id != "文档ID" and id.startswith(component_code)]
    except Exception as e:
        print(f"警告: 读取索引文件 {index_path} 时出错: {e}")
        return []

def generate_new_id(component_code, existing_ids):
    """生成新的文档ID"""
    # 使用统一的ID生成器
    return id_generator.generate_new_id(component_code, existing_ids)

def create_document(component_code, doc_type, doc_name, related_doc_id=None):
    """创建新文档"""
    # 检查组件代号是否有效
    if component_code not in COMPONENT_CODES:
        print(f"错误: 无效的组件代号 {component_code}")
        print(f"可用的组件代号: {', '.join(COMPONENT_CODES.keys())}")
        return None
    
    # 检查文档类型是否有效
    if doc_type not in DOC_TYPE_MAPPING:
        print(f"错误: 无效的文档类型 {doc_type}")
        print(f"可用的文档类型: {', '.join(DOC_TYPE_MAPPING.keys())}")
        return None
    
    # 获取组件目录
    component_dir = DIRECTORY_MAPPING.get(component_code, "")
    if not component_dir:
        print(f"错误: 组件 {component_code} 没有对应的目录")
        return None
    
    # 创建组件目录（如果不存在）
    if not os.path.exists(component_dir):
        os.makedirs(component_dir)
    
    # 生成文档ID
    existing_ids = get_existing_ids(component_code)
    doc_id = generate_new_id(component_code, existing_ids)
    
    # 生成文件名
    file_name = f"{component_code}_{doc_type}_{doc_name}.md"
    file_path = os.path.join(component_dir, file_name)
    
    # 检查文件是否已存在
    if os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 已存在")
        return None
    
    # 准备模板参数
    template_params = {
        "title": doc_name,
        "doc_id": doc_id,
        "date": datetime.now().strftime("%Y-%m-%d"),
        "component_name": COMPONENT_CODES.get(component_code, component_code),
        "related_doc_id": related_doc_id or "NONE"
    }
    
    # 获取文档模板
    template = DOCUMENT_TEMPLATES.get(doc_type, "# {title}\n\n文档ID: {doc_id}\n创建日期: {date}\n版本: 1.0\n")
    
    # 填充模板
    content = template.format(**template_params)
    
    # 写入文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"成功创建文档: {file_path}")
    print(f"文档ID: {doc_id}")
    
    # 更新ID表格
    update_id_table(component_code)
    
    return file_path

def update_id_table(component_code):
    """更新ID表格"""
    # 调用update_id_tables.py脚本
    try:
        subprocess.run([sys.executable, "scripts/update_id_tables.py", "--component", component_code], check=True)
    except subprocess.CalledProcessError as e:
        print(f"警告: 更新ID表格时出错: {e}")
    except FileNotFoundError:
        print("警告: 找不到update_id_tables.py脚本，跳过更新ID表格")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文档创建工具")
    parser.add_argument("--component", required=True, help="组件代号，如REQ、DES等")
    parser.add_argument("--type", required=True, help="文档类型，如MATRIX、SPEC等")
    parser.add_argument("--name", required=True, help="文档名称")
    parser.add_argument("--related", help="关联文档ID")
    
    args = parser.parse_args()
    
    file_path = create_document(args.component, args.type, args.name, args.related)
    if not file_path:
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 