<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>turnOnTime</class>
 <widget class="QDockWidget" name="turnOnTime">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>807</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icon/motor-1.png</normaloff>:/icon/motor-1.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QDockWidget
{
	background-color: rgb(255,255,255);
	
	font: 12pt &quot;黑体&quot;;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 6px;
}
QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
QCustomPlot{
	/*min-width: 1200px;
	min-height: 1000px;*/
}
#monitorResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	/*min-width: 230px;
	min-height:150px;*/

	text-align: left top;
    padding-left: 60px;
    padding-top: 2px;
	font: 30pt &quot;黑体&quot;;
}</string>
  </property>
  <property name="windowTitle">
   <string>TimeMonitor_V1.2</string>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="4">
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>process</string>
      </property>
      <widget class="QWidget" name="horizontalLayoutWidget">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>30</y>
         <width>761</width>
         <height>31</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QCheckBox" name="checkBox">
          <property name="text">
           <string>加速时间</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox_2">
          <property name="text">
           <string>稳定转速</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox_4">
          <property name="text">
           <string>切换时间</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBox_3">
          <property name="text">
           <string>减速时间</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item row="5" column="0" colspan="4">
     <widget class="QLineEdit" name="monitorResult">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="maximumSize">
       <size>
        <width>280</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="text">
       <string>result</string>
      </property>
     </widget>
    </item>
    <item row="0" column="0" rowspan="2" colspan="4">
     <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,0">
      <property name="spacing">
       <number>6</number>
      </property>
      <item row="1" column="0">
       <widget class="QLabel" name="label_5">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>baud：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>port：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="transBaudBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>9600</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>115200</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>150000</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>153600</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>230400</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>600000</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_10">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>s_baud：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="baudBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>9600</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>115200</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>150000</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>153600</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>230400</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>600000</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="portBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_18">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>s_port：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="transPortBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="2" column="0">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>5</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="3" column="0" colspan="4">
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="5" column="1">
       <widget class="QLineEdit" name="resultStandard">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>30</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>interval</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="monitorMode_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>极值</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>置信度</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_9">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>mode：</string>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="label_17">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>bias(‰)：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="monitorMode">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>持续监测</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_6">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>standard：</string>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="label_11">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>ID：</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>function：</string>
        </property>
       </widget>
      </item>
      <item row="6" column="1">
       <widget class="QLineEdit" name="id">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>0001</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="resultStandard_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>95</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="funcMode">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>时间监控</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>调参与转速监控</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </item>
    <item row="4" column="0" colspan="4">
     <widget class="QToolBox" name="result">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>200</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QLabel{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}

QLineEdit
{
	min-width: 150px;
	min-height: 28px;
}</string>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="startResult">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>280</width>
         <height>142</height>
        </rect>
       </property>
       <property name="locale">
        <locale language="English" country="UnitedStates"/>
       </property>
       <attribute name="label">
        <string>Page 1</string>
       </attribute>
       <widget class="QWidget" name="layoutWidget">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>10</y>
          <width>261</width>
          <height>131</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="0" column="1">
          <widget class="QLineEdit" name="startTmean">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_12">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Tmean/ms：</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_16">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>BiasTi/‰</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="startTmaxAccuracy">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_15">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>BiasTa/‰</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="startTminAccuracy">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="stableResult">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>280</width>
         <height>142</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>Blackadder ITC</family>
        </font>
       </property>
       <attribute name="label">
        <string>Page 2</string>
       </attribute>
       <widget class="QWidget" name="layoutWidget">
        <property name="geometry">
         <rect>
          <x>10</x>
          <y>0</y>
          <width>252</width>
          <height>131</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_7">
         <item row="1" column="1">
          <widget class="QLineEdit" name="stableTmaxAccuracy">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="stableTmean">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="stableTminAccuracy">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="label_23">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Tmean/ms：</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_21">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>BiasTa/‰</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_19">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>BiasTi/‰</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
     </widget>
    </item>
    <item row="7" column="0" colspan="5">
     <layout class="QHBoxLayout" name="statusLayout">
      <item>
       <widget class="QLabel" name="startLamp">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="startStep">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="dataLamp">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="dataStep">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="adjustLamp">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="adjustStep">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="compLamp">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="compStep">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="4" rowspan="6">
     <widget class="QCustomPlot" name="motorPlot" native="true">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>800</width>
        <height>0</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="cursor">
       <cursorShape>PointingHandCursor</cursorShape>
      </property>
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
     </widget>
    </item>
    <item row="6" column="3">
     <widget class="QPushButton" name="startButton">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>85</width>
        <height>39</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="text">
       <string>open</string>
      </property>
     </widget>
    </item>
    <item row="6" column="0" colspan="3">
     <spacer name="horizontalSpacer_4">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Preferred</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>customPlot/qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
