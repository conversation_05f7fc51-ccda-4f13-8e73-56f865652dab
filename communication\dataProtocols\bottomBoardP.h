#ifndef  _BOTTOM_BOARD_P_H_
#define  _BOTTOM_BOARD_P_H_

#include <QByteArray>
#include "IProtocol.h"

class CBottomBoardP:public IProtocol
{
public:
    CBottomBoardP();
    ~CBottomBoardP();

public:
    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

private:
//    typedef union{
//      QByteArray frame;
//      struct frame{
//        uint8_t     header;
//        uint8_t     cmd;
//        uint8_t     id;
//        uint8_t     check_xor;
//        uint16_t    num;
//        QByteArray  data;
//      };
//    } UFrame;


    /*协议cmd枚举*/
    enum EFrame{
      eHEADER   = 0xAA55,
    };
};


#endif
