#include "lenSqlHandle.h"
#include <QObject>
#include <QSqlRecord>


#define DATABASE_OPEN_SQL2 "DRIVER={Progress OpenEdge 10.2B Driver}; DSN=hyh-prod; DB=mfgprod; UID=mfg; PWD=*******; HOST=**********; PORT=56396;"
#define DATABASE_OPEN_SQL  "DRIVER={Progress OpenEdge 10.2B Driver};DSN=hyh-prod;DB=hlpprod;UID=mfg;PWD=*******;HOST=**********;PORT=56398;"

#define WO_STATUS_OPEN     "R"  //开启
#define WO_STATUS_CLOSE    "c"  //关闭

MyConnSql::StSqlConnInfo CLenSqlHandle::mst_lens_mstr_database = {"len-mes-mstr", "*************", 56396, "mfgprod", "hyh_work_order_new", "mfg", "mfg#2024"};
MyConnSql::StSqlConnInfo CLenSqlHandle::mst_lens_main_database = {"len-mes-main", "*************", 56398, "hlpprod", "hyh_main_new", "mfg", "mfg#2024"};
QMap<int, QString>       CLenSqlHandle::mm_mes_error_discribe  = {
    {0x0000, "ok"},
    {0x0001, "工单表数据库连接异常 "},
    {0x0002, "表2库连接异常 "},
    {0x0004, "表格查询失败 "},
    {0x0008, "执行失败 "},
    {0x0010, "未找到该工单 "},
    {0x0020, "工单未开启 "},
    {0x0040, "标签号丢失 "},
    {0x0080, "事务号查找失败 "},
    {0x0100, "数据录入异常 "},
};

CLenSqlHandle::CLenSqlHandle() {
    ;
}

CLenSqlHandle::~CLenSqlHandle() {
    ;
}

bool CLenSqlHandle::connectTestDB() {
    return MyConnSql::createCloudConnection();
}

bool CLenSqlHandle::connectWorkOrderDB() {
    return (MyConnSql::createCloudConnection(m_db, CLenSqlHandle::mst_lens_mstr_database));  //工单表
}


bool CLenSqlHandle::connectXSub2DetDB() {
    if (!MyConnSql::createCloudConnection(m_db, CLenSqlHandle::mst_lens_main_database)) {  //工单表
        return false;  // show error
    }
    return true;
}

/**
 * @brief CLenSqlHandle::checkWorkOrder 表单是否开启判断: 表单、
 * @param workOrder
 * @return
 */
uint16_t CLenSqlHandle::checkWorkOrder(StWorkOrder *st_work_order_) {
    UMesErrors u_mes_error;
    QString    wo_status = "", str_write, str_read;

    u_mes_error.errors = 0;

    //* check database
    //    if(!connectXSub2DetDB()) {
    if (!m_db.open()) {
        u_mes_error.all_error.conn_wo_db = 1;
        return u_mes_error.errors;
    }

    QSqlQuery myQuery(m_db);

#if 0  // show all data list
    str_read = QObject::tr("select * from pub.xwoid_mstr");
    if(!MyConnSql::originTableShow(m_db, str_read, 6)) {
        u_mes_error.all_error.query_table_err = 1;
        return u_mes_error.errors;
    }
#endif
    //* get work order status
    str_write = QString("select * from pub.xwoid_mstr where xwoid_lot = '%1' and xwoid_domain = '%2'").arg(st_work_order_->wo_lot).arg(st_work_order_->domain);

    QMap<QString, QString> m_col_tmp;
    m_col_tmp.insert("xwoid_statu", "");
    m_col_tmp.insert("xwoid_domain", "");

    if (!MyConnSql::queryNValue(m_db, str_write, m_col_tmp)) {
        u_mes_error.all_error.exec_error = 1;
        return u_mes_error.errors;
    }

    if (m_col_tmp["xwoid_statu"] == WO_STATUS_CLOSE)
        u_mes_error.all_error.wo_status_cls = 1;
    else if (m_col_tmp["xwoid_statu"] == "")
        u_mes_error.all_error.wo_miss = 1;
    else {
        //        st_work_order_->domain = m_col_tmp["xwoid_domain"];
        st_work_order_->wo_status = m_col_tmp["xwoid_statu"];
    }

    if (m_db.isOpen())
        m_db.close();

    return u_mes_error.errors;
}

/**
 * @brief CLenSqlHandle::searchTagNumber
 * @param xpid_det 由MCUID查找标签
 * @param nbr 标签
 * @return
 */
uint16_t CLenSqlHandle::searchTagNumberByMCUID(const StXpiddet &xpid_det, QString &nbr) {
    //获取MCUID对应的标签号
#if 0  //已有案例
    QString str_tmp;
    chip_id = "30393337000E30353332DB96";
    work_order = 6297;
    //mst_mes_xsub2_data_.nbr = "62155156";
#endif
    UMesErrors u_mes_error;
    QString    str_write, str_read;

    u_mes_error.errors = 0;

    //* check database
    //    if(!connectXSub2DetDB()) {
    if (!m_db.open()) {
        u_mes_error.all_error.conn_wo_db = 1;
        return u_mes_error.errors;
    }

    QSqlQuery myQuery(m_db);

#if 0  // show all valid data
    str_read = QObject::tr("select * from pub.xpid_det");
    MyConnSql::originTableShow(m_db, str_read, 3);
#endif

    //* check tag number
    str_read = QObject::tr("select %1 from pub.xpid_det where xpid_uid = '%2'").arg("xpid_nbr").arg(xpid_det.mcuid);

    if (!MyConnSql::queryOneValue(m_db, str_read, "xpid_nbr", nbr)) {  //有对应工单和标签号为空不是一个意思，但也可能有工单没开时写入的数据
        u_mes_error.all_error.exec_error = 1;
        return u_mes_error.errors;
    }
    if (nbr == "")
        u_mes_error.all_error.nbr_tag_miss = 1;
    if (m_db.isOpen())
        m_db.close();
    return u_mes_error.errors;
}

/**
 * @brief CLenSqlHandle::searchTagNumber
 * @param xpid_det 标签生成参数
 * @param nbr 标签
 * @return
 */
uint16_t CLenSqlHandle::searchTagNumber(const StXpiddet &xpid_det, QString &nbr, const bool &rewrite) {
    //获取MCUID对应的标签号
#if 0  //已有案例
    QString str_tmp;
    chip_id = "30393337000E30353332DB96";
    work_order = 6297;
    //mst_mes_xsub2_data_.nbr = "62155156";
#endif
    UMesErrors u_mes_error;
    QString    str_write, str_read;

    u_mes_error.errors = 0;

    //* check database
    //    if(!connectXSub2DetDB()) {
    if (!m_db.open()) {
        u_mes_error.all_error.conn_wo_db = 1;
        return u_mes_error.errors;
    }

    QSqlQuery myQuery(m_db);

#if 0  // show all valid data
    str_read = QObject::tr("select * from pub.xpid_det");
    MyConnSql::originTableShow(m_db, str_read, 0);
#endif

    //* check tag number
    str_read = QObject::tr("select %1 from pub.xpid_det where xpid_uid = '%2' and xpid_lot = '%3' and xpid_domain = '%4'")
                   .arg("xpid_nbr")
                   .arg(xpid_det.mcuid)
                   .arg(xpid_det.work_order)
                   .arg(xpid_det.domain);

    if (!MyConnSql::queryOneValue(m_db, str_read, "xpid_nbr", nbr)) {  //有对应工单和标签号为空不是一个意思，但也可能有工单没开时写入的数据
        u_mes_error.all_error.exec_error = 1;
        return u_mes_error.errors;
    }
    //    if(nbr == "") qDebug() << "-i len sql/ nbr sql: " << str_read;

    if (nbr == "" && rewrite) {  //写入数据生成标签
        str_write = QObject::tr("insert into pub.xpid_det (xpid_uid, xpid_lot, xpid_domain) values('%1', '%2', '%3')")
                        .arg(xpid_det.mcuid)
                        .arg(xpid_det.work_order)
                        .arg(xpid_det.domain);

        try {
            if (!myQuery.exec(str_write)) {
                throw QSqlError(myQuery.lastError());
            }
        } catch (QSqlError &e) {
            qDebug() << "Database Error: When excute \"" << str_write << "\"";
            qDebug() << "Exception caught: " << e.text();

            if (m_db.isOpen())
                m_db.close();
            u_mes_error.all_error.exec_error = 1;
            return u_mes_error.errors;
        }

#if 0  // show all valid data
        str_read = QObject::tr("select * from pub.xpid_det");
        MyConnSql::originTableShow(m_db, str_read, 3);
#endif
    }
    if (m_db.isOpen())
        m_db.close();
    return u_mes_error.errors;
}

uint16_t CLenSqlHandle::printfTagTable(const StXpiddet &xpid_det) {
    //获取MCUID对应的标签号
    UMesErrors u_mes_error;
    QString    str_write, str_read;

    u_mes_error.errors = 0;

    //* check database
    if (!m_db.open()) {
        u_mes_error.all_error.conn_wo_db = 1;
        return u_mes_error.errors;
    }

    QSqlQuery myQuery(m_db);

    //* check tag number
    str_read = QObject::tr("select * from pub.xpid_det where xpid_uid = '%1' and xpid_lot = '%2' and xpid_domain = '%3'")
                   .arg(xpid_det.mcuid)
                   .arg(xpid_det.work_order)
                   .arg(xpid_det.domain);

    if (MyConnSql::originTableShow(m_db, str_read, 0)) {  //有对应工单和标签号为空不是一个意思，但也可能有工单没开时写入的数据
        //                qInfo() << "-i len sql/nbr raw: /" << st_mes_data_->nbr << " search timers: " << search_num << "errors: " << u_mes_error.errors;
    }

    if (m_db.isOpen())
        m_db.close();
    return u_mes_error.errors;
}

/**
 * @brief CLenSqlHandle::loadMesData
 * @param xpid_det
 * @param st_mes_data_
 * @return
 */
uint16_t CLenSqlHandle::loadMesData(StResultToMes *st_mes_data_) {

    //* 获取标签号
    QString    str_read;
    UMesErrors u_mes_error;
    u_mes_error.errors = 0;

    if (st_mes_data_->nbr_pre.toUInt() >= 60000000) {
        st_mes_data_->domain = "001";
    } else {
        st_mes_data_->domain = "003";
    }

    //* check database
    //    if(!connectXSub2DetDB()) {
    if (!m_db.open()) {
        u_mes_error.all_error.conn_wo_db = 1;
        return u_mes_error.errors;
    }

    if ((st_mes_data_->nbr_pre != "00000000")) {  //两次产品编号不一样则可以写入 (st_mes_data_->nbr != st_mes_data_->nbr_pre) &&

#if 0  // show all valid data //数据太多，不全部打印
        str_read = QObject::tr("select * from pub.xsub2_det where xsub2_date = '%1'").arg(st_mes_data_->date); //xsub2_det
        //        str_read = QString("select * from pub.xsub2_det");
        MyConnSql::originTableShow(m_db, str_read, 10);
#endif
        str_read = QString("select * from pub.xsub2_det where xsub2_domain = '%1' and xsub2_nbr = '%2'").arg(st_mes_data_->domain).arg(st_mes_data_->nbr_pre);

        QMap<QString, QString> m_col_tmp;
        m_col_tmp.insert("xsub2_op", "");
        m_col_tmp.insert("xsub2_userid", "");
        m_col_tmp.insert("xsub2_date", "");
        m_col_tmp.insert("xsub2_time", "");
        m_col_tmp.insert("xsub2_rslt", "");
        m_col_tmp.insert("xsub2_rsn_code", "");
        m_col_tmp.insert("xsub2_param1", "");
        m_col_tmp.insert("xsub2_param2", "");
        m_col_tmp.insert("xsub2_param3", "");
        m_col_tmp.insert("xsub2_param4", "");
        m_col_tmp.insert("xsub2_param5", "");
        m_col_tmp.insert("xsub2_param6", "");
        m_col_tmp.insert("xsub2_param7", "");
        m_col_tmp.insert("xsub2_param8", "");
        m_col_tmp.insert("xsub2_param9", "");
        if (!MyConnSql::queryNValue(m_db, str_read, m_col_tmp)) {
            u_mes_error.all_error.exec_error = 1;
            return u_mes_error.errors;
        }
        st_mes_data_->op       = m_col_tmp["xsub2_op"];
        st_mes_data_->userid   = m_col_tmp["xsub2_userid"];
        st_mes_data_->date     = m_col_tmp["xsub2_date"];
        st_mes_data_->time     = m_col_tmp["xsub2_time"].toUInt();
        st_mes_data_->rslt     = m_col_tmp["xsub2_rslt"].toInt();
        st_mes_data_->rsn_code = m_col_tmp["xsub2_rsn_code"];
        st_mes_data_->param[0] = m_col_tmp["xsub2_param1"].toUInt();
        st_mes_data_->param[1] = m_col_tmp["xsub2_param2"].toUInt();
        st_mes_data_->param[2] = m_col_tmp["xsub2_param3"].toUInt();
        st_mes_data_->param[3] = m_col_tmp["xsub2_param4"].toUInt();
        st_mes_data_->param[4] = m_col_tmp["xsub2_param5"].toUInt();
        st_mes_data_->param[5] = m_col_tmp["xsub2_param6"].toUInt();
        st_mes_data_->param[6] = m_col_tmp["xsub2_param7"].toUInt();
        st_mes_data_->param[7] = m_col_tmp["xsub2_param8"].toUInt();
        st_mes_data_->param[8] = m_col_tmp["xsub2_param9"].toUInt();

        //转换合成字符 //'%4d/%4d/%2d'
        //        str_write = QObject::tr("insert into pub.xsub2_det (xsub2_domain, xsub2_trnbr, xsub2_nbr, xsub2_op, xsub2_userid, xsub2_date, xsub2_time,
        //        xsub2_rslt, xsub2_rsn_code, "
        //                                "xsub2_param1, xsub2_param2, xsub2_param3, xsub2_param4, xsub2_param5, xsub2_param6, xsub2_param7, xsub2_param8,
        //                                xsub2_param9)"
        //                                "values('%1', '%2', '%3', '%4', 'U%5', '%6', '%7', '%8', '%9', '%10', '%11', '%12', '%13', '%14', '%15', '%16', '%17',
        //                                '%18')")\
//                .arg(st_mes_data_->domain).arg(st_mes_data_->trnbr).arg(st_mes_data_->nbr).arg(st_mes_data_->op).arg(st_mes_data_->userid).arg(st_mes_data_->date)\
//                .arg(st_mes_data_->time).arg(st_mes_data_->rslt).arg(st_mes_data_->rsn_code).arg(st_mes_data_->param[0]).arg(st_mes_data_->param[1]).\
//                arg(st_mes_data_->param[2]).arg(st_mes_data_->param[3]).arg(st_mes_data_->param[4]).arg(st_mes_data_->param[5]).arg(st_mes_data_->param[6])\
//                .arg(st_mes_data_->param[7]).arg(st_mes_data_->param[8]);
    } else {
        u_mes_error.all_error.nbr_tag_miss = 1;
    }
    if (m_db.isOpen())
        m_db.close();
    return u_mes_error.errors;
}

/**
 * @brief CLenSqlHandle::saveMesData
 * @param xpid_det
 * @param st_mes_data_
 * @return
 */
uint16_t CLenSqlHandle::saveMesData(const StXpiddet &xpid_det, StResultToMes *st_mes_data_) {

    //* 获取标签号
    QString    str_read, str_write;
    UMesErrors u_mes_error;
    u_mes_error.errors = 0;
    uint8_t save_cnt   = 5;
    uint8_t search_num = 30, db_connct_num = 10;
    //* check tag number
    //    while(search_num--) {
    //        u_mes_error.errors = 0;
    //        if(st_mes_data_->nbr == "") {
    //            u_mes_error.errors |= searchTagNumber(xpid_det, st_mes_data_->nbr, false);
    //            qWarning() << "-i len sql/nbr query: " << st_mes_data_->nbr << " search timers: " << search_num << "errors: " << u_mes_error.errors;
    //        }
    //        else break;
    //    }
    do {
        searchTagNumber(xpid_det, st_mes_data_->nbr, false);
        if (st_mes_data_->nbr == "") {
            str_read = QObject::tr("select * from pub.xpid_det where xpid_uid = '%1' and xpid_lot = '%2' and xpid_domain = '%3'")
                           .arg(xpid_det.mcuid)
                           .arg(xpid_det.work_order)
                           .arg(xpid_det.domain);

            qWarning() << "-i len sql/nbr query: "
                       << " search timers: " << search_num;
            if (MyConnSql::originTableShow(m_db, str_read, 0)) {  //有对应工单和标签号为空不是一个意思，但也可能有工单没开时写入的数据
                //                qInfo() << "-i len sql/nbr raw: /" << st_mes_data_->nbr << " search timers: " << search_num << "errors: " <<
                //                u_mes_error.errors;
            }
        } else
            break;
    } while (search_num--);

    if (st_mes_data_->nbr == "") {  //无标签号，直接退出 u_mes_error.errors != 0 ||
        u_mes_error.all_error.nbr_tag_miss = 1;
        return u_mes_error.errors;
    }

    //* check database
    //    if(!connectXSub2DetDB()) {
    do {
        u_mes_error.all_error.conn_wo_db = 0;
        if (m_db.open())
            break;
        else
            u_mes_error.all_error.conn_wo_db = 1;
    } while (db_connct_num--);

    if (u_mes_error.all_error.conn_wo_db)
        return u_mes_error.errors;
        //    if(!m_db.open()) {
        //        u_mes_error.all_error.conn_wo_db = 1;
        //        return u_mes_error.errors;
        //    }

#if 0  //不再更新 标签表域名
    str_write = QObject::tr("update pub.xpid_det set xpid_domain = '%1' where xpid_uid = '%2' and xpid_lot = '%3'").arg(st_mes_data_->domain).arg(xpid_det.mcuid).arg(xpid_det.work_order);
    //MyConnSql::excuteSQL(m_db, str_write); //
    if(!myQuery.exec(str_write)) {
        qDebug() << "Database Error: When excute \"" << str_write << "\"";
        m_db.close();
        u_mes_error.all_error.exec_error = 1;
        return u_mes_error.errors;
    }
#endif
    if (st_mes_data_->nbr != "00000000") {  //两次产品编号不一样则可以写入 (st_mes_data_->nbr != st_mes_data_->nbr_pre) &&

        //* 写入域名
        if (st_mes_data_->nbr.toUInt() >= 60000000) {
            st_mes_data_->domain = "001";
        } else {
            st_mes_data_->domain = "003";
        }

#if 0    //索引最后值 11s
            str_read = QObject::tr("SELECT TOP 1 %1 FROM pub.xsub2_det where xsub2_date = '%2' ORDER BY ROWID DESC").arg("xsub2_trnbr").arg(st_mes_data_->date);

            MyConnSql::queryOneValue(m_db, str_read, "xsub2_trnbr", tr_tmp);
#elif 1  //索引最大值 9s
        //            str_read = QObject::tr("select MAX(xsub2_trnbr) from pub.xsub2_det where xsub2_date = '%1'").arg(st_mes_data_->date);
        str_read = QObject::tr("select MAX(xsub2_trnbr) from pub.xsub2_det where xsub2_date = '%1' and xsub2_domain = '%2'")
                       .arg(st_mes_data_->date)
                       .arg(st_mes_data_->domain);
        ;
#elif 0  //先索引最大的几个值，再索引最大值 12s
         //            str_read = QObject::tr("SELECT MAX(subquery.xsub2_trnbr) "
//                                   "FROM (SELECT xsub2_trnbr "
//                                   "      FROM pub.xsub2_det "
//                                   "      WHERE xsub2_date = '%1' AND xsub2_trnbr > 1162979) AS subquery").arg(st_mes_data_->date);
#elif 0  // 18s
        str_read = QObject::tr("SELECT xsub2_trnbr FROM pub.xsub2_det WHERE xsub2_date = '%1'").arg(st_mes_data_->date);
#endif
#if 0  // show all valid data //数据太多，不全部打印
        str_read = QObject::tr("select * from pub.xsub2_det where xsub2_date = '%1'").arg(st_mes_data_->date); //xsub2_det
//        str_read = QString("select * from pub.xsub2_det");
        MyConnSql::originTableShow(m_db, str_read, 10);
#endif
        do {
            //* 先采用日期搜索，若日期未搜到，代表几台未开始测试，换成原最大搜索方式
            //* 索引事务号
            QString tr_tmp = "";
            MyConnSql::queryMaxValue(m_db, str_read, tr_tmp);
            if ((tr_tmp == "") || (tr_tmp == "0")) {  //判断按日期搜索是否有找到，若没找到则采用最原始方式
                str_read = QObject::tr("select MAX(xsub2_trnbr) from pub.xsub2_det");
                MyConnSql::queryMaxValue(m_db, str_read, tr_tmp);

                if ((tr_tmp == "") || (tr_tmp == "0")) {
                    u_mes_error.all_error.trnbr_miss = 1;
                    continue;
                    //                    return u_mes_error.errors;
                }
            }

            st_mes_data_->trnbr = tr_tmp.toUInt();
            ++st_mes_data_->trnbr;  //每一笔自加1 .需要重数据库中获取最大事务号

            //转换合成字符 //'%4d/%4d/%2d'
            str_write =
                QObject::tr("insert into pub.xsub2_det (xsub2_domain, xsub2_trnbr, xsub2_nbr, xsub2_op, xsub2_userid, xsub2_date, xsub2_time, xsub2_rslt, "
                            "xsub2_rsn_code, "
                            "xsub2_param1, xsub2_param2, xsub2_param3, xsub2_param4, xsub2_param5, xsub2_param6, xsub2_param7, xsub2_param8, xsub2_param9)"
                            "values('%1', '%2', '%3', '%4', '%5', '%6', '%7', '%8', '%9', '%10', '%11', '%12', '%13', '%14', '%15', '%16', '%17', '%18')")
                    .arg(st_mes_data_->domain)
                    .arg(st_mes_data_->trnbr)
                    .arg(st_mes_data_->nbr)
                    .arg(st_mes_data_->op)
                    .arg(st_mes_data_->userid)
                    .arg(st_mes_data_->date)
                    .arg(st_mes_data_->time)
                    .arg(st_mes_data_->rslt)
                    .arg(st_mes_data_->rsn_code)
                    .arg(st_mes_data_->param[0])
                    .arg(st_mes_data_->param[1])
                    .arg(st_mes_data_->param[2])
                    .arg(st_mes_data_->param[3])
                    .arg(st_mes_data_->param[4])
                    .arg(st_mes_data_->param[5])
                    .arg(st_mes_data_->param[6])
                    .arg(st_mes_data_->param[7])
                    .arg(st_mes_data_->param[8]);
            //            qDebug() << "-i lens sql/ save data:" << str_write;
            QSqlQuery myQuery(m_db);
            try {
                if (!myQuery.exec(str_write)) {
                    throw QSqlError(myQuery.lastError());
                } else {
                    //* reload data
                    u_mes_error.all_error.exec_error = 0;
                    qDebug() << "-i len sql/mes save: " << str_write;
                    break;
                }
            } catch (QSqlError &e) {
                qDebug() << "Database Error: When excute \"" << str_write << "\"";
                qDebug() << "Exception caught: " << e.text();

                u_mes_error.all_error.exec_error = 1;
            }
#if 0  // show all valid data
            str_read = QObject::tr("select * from pub.xsub2_det  where xsub2_date = '%1'");
            MyConnSql::originTableShow(m_db, str_read, 10);
#endif
        } while (save_cnt--);

    } else {
        u_mes_error.all_error.nbr_tag_miss = 1;
    }
    if (m_db.isOpen())
        m_db.close();
    return u_mes_error.errors;
}
