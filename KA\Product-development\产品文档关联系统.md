# 产品文档关联系统

## 系统定义与核心目标

### 系统定义

产品文档关联系统基于AI语义分析和知识管理技术，自动识别文档间的语义关系，建立智能双链关联网络，提升文档发现能力和知识管理效率。

### 核心目标

- **智能语义关联**：基于AI模型自动识别文档间的语义相似性和关联关系
- **双链知识网络**：构建文档间的双向链接网络，支持知识导航和发现
- **可视化知识图谱**：提供图形化的文档关系可视化，便于理解文档结构

### 与追溯系统的区别

| 维度 | 文档关联系统 (links) | 追溯系统 (infoTrace) |
|------|---------------------|---------------------|
| **核心目标** | 文档级别的关联管理 | 内容级别的追溯管理 |
| **关联粒度** | 文档整体间的关联关系 | 细化到文档内部的具体内容项 |
| **关联方向** | 双向关联，语义相似性 | 从输出逆向追溯到输入 |
| **管理方式** | 基于双链的知识管理 | 基于ID表格的精确追溯 |
| **技术实现** | AI语义分析 + 双链系统 | INDEX文件 + ID注册机制 |
| **应用场景** | 知识发现、内容导航 | 质量管控、变更影响分析 |

## 概述

1. 在文档中插入双链链接
2. 在每个组件目录下包含一个`[组件代号]_INDEX.md`文件，用于记录该组件内所有文档及其关联关
系。文件名无需按严格格式命名，只需在表格中声明ID即可。

文档关联系统作为**文档注册和基础关联发现**的核心组件：

- **文档注册管理**：自动扫描并注册文档到各组件的`[组件代号]_INDEX.md`文件
- **语义关联发现**：通过AI分析发现文档间的潜在语义关系
- **双链网络构建**：建立`[[文档名]]`格式的双向知识链接
- **关联关系建议**：为INDEX表格提供智能化的关联关系建议

### 5.2 技术实现

**遵循原则2(单一来源)和原则14(公共库调用)**：

```
scripts/
└── links/                              # 文档关联系统脚本(公共库)
    └── auto_link_documents.py          # 文档关联核心脚本
```

**关联方式**：

1. **自动语义关联**：基于AI模型分析文档内容相似性
2. **INDEX表格注册**：在各组件目录下的`[组件代号]_INDEX.md`文件中注册文档基本信息
3. **双链网络**：使用VSCode Foam插件实现`[[文档名]]`格式的双向链接

### 5.3 INDEX文件基础结构

**遵循原则3(内容唯一)和原则12(文档单一职责)**：

| 文档ID | 文档名称 | 文档路径 | 文档类型 | 关联组件 | 关联文档ID | 关系类型 | 最后更新时间 |
|--------|---------|---------|---------|---------|-----------|---------|------------|
| REQ001 | 产品需求规格 | 需求规格.md | SPEC | DES | DES001 | 实现 | 2023-06-15 |

## 主要功能

内容追溯关系建立在文档关联基础上，如果两个文档之间不存在关联，则也必然不会存在块级内容的关联

- **自动语义关联**：利用AI模型和向量检索技术，自动识别文档之间的语义关系，并在文档中插入双链链接。
- **关键词匹配**：通过关键词重合度分析，辅助判断文档关联性。
- **可视化网络**：在VSCode中以图谱形式展示文档之间的关联关系。
- **轻量**：避免过多的tokens
- **手动调整**：支持用户手动修改和调整自动生成的链接，确保数据准确性。

## 技术架构

```
┌─────────────────────┐
│  文档采集层         │
│  - 文件系统扫描     │
│  - Markdown解析     │
│  - 文档元数据提取   │
└─────────┬───────────┘
          ↓
┌─────────────────────┐
│  语义处理层         │
│  - 文本嵌入生成     │
│  - 向量索引构建     │
│  - 关联关系分析     │
└─────────┬───────────┘
          ↓
┌─────────────────────┐
│  双链生成层         │
│  - Foam双链生成     │
│  - 知识图谱构建     │
│  - Markdown链接     │
└─────────────────────┘
```

### 脚本工具

```
scripts/
└── links/                              # 文档关联与知识管理系统
    └── auto_link_documents.py          # 自动文档关联脚本
```

### 使用方式

```bash
# 自动建立文档关联
python ../scripts/links/auto_link_documents.py --project-path . --config ./config/document_links_config.json

# 更新文档关联关系
python ../scripts/links/auto_link_documents.py --project-path . --update

# 生成关联关系报告
python ../scripts/links/auto_link_documents.py --project-path . --report
```

## 技术架构详解

### 2.1 核心组件

1. **文档扫描引擎**：负责扫描项目中的所有文档文件
2. **语义分析模块**：基于SentenceTransformers进行文档语义向量化
3. **关联规则引擎**：根据配置的规则自动建立文档关联
4. **双链生成器**：生成Foam格式的双向链接
5. **可视化模块**：生成文档关联网络图

### 2.2 关键组件选型

1. **文本嵌入** - Sentence-Transformers：轻量级预训练模型，用于生成文本向量
2. **向量索引** - HNSWlib：高效相似度检索库，支持内存索引和文件持久化
3. **知识图谱** - NetworkX：构建图结构，支持导出为各种格式
4. **VSCode集成** - Foam：提供双链和图谱可视化能力

### 系统组件

1. **文档处理引擎**：负责读取、解析和预处理Markdown文档。
2. **向量化模块**：将文档内容转换为语义向量。
3. **检索引擎**：基于向量相似度进行文档关联分析。
4. **链接生成器**：创建并插入双链链接到文档中。
5. **VSCode集成模块**：与Foam和VSCode任务系统集成。
6. **可视化模块**：生成和展示文档关联网络图。

### 数据流程

```
文档集合 → 预处理 → 向量化 → 建立索引 → 相似度检索 → 关联分析 → 生成链接 → 更新文档
```

## 详细实施方案

### 1. 环境准备

#### 1.1 依赖安装

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装核心依赖
pip install sentence-transformers faiss-cpu markdown semantic-markdown-parser numpy scikit-learn 

# VSCode扩展
# 通过VSCode扩展市场安装Foam和必要扩展
```

#### 1.2 工作区配置

```json
// .vscode/settings.json 示例配置
{
  "foam.openDailyNote.directory": "journal",
  "foam.openDailyNote.titleFormat": "yyyy-mm-dd",
  "foam.openDailyNote.filenameFormat": "yyyy-mm-dd",
  "foam.edit.linkReferenceDefinitions": "withoutExtensions",
  "foam.graph.style": {
    "fontSize": 12,
    "highlightedForeground": "#f9c74f",
    "node": {
      "note": "#277da1",
      "requirement": "#d62828",
      "design": "#4d908e",
      "test": "#43aa8b"
    }
  }
}
```

### 2. 文档预处理

#### 2.1 文档扫描与加载

```python
def scan_markdown_files(workspace_path):
    """扫描工作区内所有Markdown文件"""
    markdown_files = []
    for root, dirs, files in os.walk(workspace_path):
        for file in files:
            if file.endswith('.md'):
                file_path = os.path.join(root, file)
                markdown_files.append(file_path)
    return markdown_files

def load_document(file_path):
    """加载并基本解析Markdown文档"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取文档类型（基于路径或frontmatter）
    doc_type = get_document_type(file_path, content)
    
    # 提取标题
    title = get_document_title(content)
    
    return {
        'path': file_path,
        'title': title,
        'content': content,
        'type': doc_type
    }
```

#### 2.2 内容清洗

```python
def clean_markdown(content):
    """清洗Markdown内容，移除代码块、注释等"""
    # 移除代码块
    content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)
    
    # 移除HTML注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 移除图片
    content = re.sub(r'!\[.*?\]\(.*?\)', '', content)
    
    # 保留链接文本，移除URL
    content = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', content)
    
    return content
```

#### 2.3 语义分段

```python
from semantic_markdown_parser import MarkdownDocument

def segment_document(content):
    """将文档分解为语义段落"""
    doc = MarkdownDocument(content)
    segments = []
    
    # 按照标题层级分段
    for section in doc.sections:
        # 标题作为一个段落
        segments.append({
            'type': 'heading',
            'level': section.level,
            'content': section.heading
        })
        
        # 段落文本
        for paragraph in section.paragraphs:
            segments.append({
                'type': 'paragraph',
                'content': paragraph.text
            })
    
    return segments
```

### 3. 向量化与检索

#### 3.1 模型加载与向量化

```python
from sentence_transformers import SentenceTransformer
import numpy as np

class DocumentEmbedder:
    def __init__(self, model_name='all-MiniLM-L6-v2'):
        """初始化文档向量化器"""
        self.model = SentenceTransformer(model_name)
        
    def embed_documents(self, documents):
        """文档集合向量化"""
        texts = [clean_markdown(doc['content']) for doc in documents]
        embeddings = self.model.encode(texts, show_progress_bar=True)
        return embeddings
    
    def embed_segments(self, segments):
        """段落集合向量化"""
        texts = [seg['content'] for seg in segments]
        embeddings = self.model.encode(texts, show_progress_bar=True)
        return embeddings
```

#### 3.2 构建Faiss索引

```python
import faiss

def build_faiss_index(embeddings):
    """构建Faiss向量索引"""
    # 归一化向量
    faiss.normalize_L2(embeddings)
    
    # 创建索引
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatIP(dimension)  # 内积相似度
    index.add(embeddings)
    
    return index

def search_similar_documents(index, query_embeddings, k=5):
    """搜索相似文档"""
    # 确保查询向量已归一化
    faiss.normalize_L2(query_embeddings)
    
    # 搜索
    distances, indices = index.search(query_embeddings, k)
    
    return distances, indices
```

#### 3.3 关键词提取与匹配

```python
from sklearn.feature_extraction.text import TfidfVectorizer

def extract_keywords(documents, top_n=10):
    """提取文档关键词"""
    # 提取文档内容
    texts = [clean_markdown(doc['content']) for doc in documents]
    
    # 创建TF-IDF向量化器
    vectorizer = TfidfVectorizer(
        max_df=0.85,
        min_df=2,
        max_features=200,
        stop_words='english'
    )
    
    # 生成TF-IDF矩阵
    tfidf_matrix = vectorizer.fit_transform(texts)
    
    # 获取特征名称(词语)
    feature_names = vectorizer.get_feature_names_out()
    
    # 提取每个文档的关键词
    doc_keywords = []
    for i in range(len(documents)):
        doc_vector = tfidf_matrix[i]
        word_indices = doc_vector.nonzero()[1]
        tfidf_scores = zip(word_indices, [doc_vector[0, x] for x in word_indices])
        sorted_words = sorted(tfidf_scores, key=lambda x: x[1], reverse=True)
        
        # 获取top_n关键词
        keywords = [(feature_names[i], score) for i, score in sorted_words[:top_n]]
        doc_keywords.append(keywords)
    
    return doc_keywords
```

### 4. 关联判断

#### 4.1 混合相似度计算

```python
def compute_hybrid_similarity(semantic_similarity, keyword_similarity, alpha=0.7):
    """计算混合相似度
    
    Args:
        semantic_similarity: 语义相似度
        keyword_similarity: 关键词相似度
        alpha: 语义相似度权重
        
    Returns:
        混合相似度分数
    """
    return alpha * semantic_similarity + (1 - alpha) * keyword_similarity
```

#### 4.2 文档类型关系配置

```python
# 文档类型关系配置
DOC_TYPE_RELATIONS = {
    'requirement': ['design', 'test'],
    'design': ['requirement', 'development', 'test'],
    'development': ['design', 'test'],
    'test': ['requirement', 'design', 'development']
}

def check_relation_validity(source_type, target_type):
    """检查两种文档类型是否应该建立关联"""
    if source_type in DOC_TYPE_RELATIONS:
        return target_type in DOC_TYPE_RELATIONS[source_type]
    return False
```

#### 4.3 关联决策

```python
def decide_associations(doc_index, similarities, doc_types, threshold=0.6):
    """决定文档关联
    
    Args:
        doc_index: 当前文档索引
        similarities: 相似度矩阵
        doc_types: 所有文档类型列表
        threshold: 相似度阈值
        
    Returns:
        关联文档索引列表
    """
    associations = []
    source_type = doc_types[doc_index]
    
    # 获取相似度最高的文档
    for i, sim in enumerate(similarities[doc_index]):
        if i != doc_index and sim >= threshold:
            target_type = doc_types[i]
            
            # 检查文档类型关系
            if check_relation_validity(source_type, target_type):
                associations.append((i, sim))
    
    # 按相似度排序
    associations.sort(key=lambda x: x[1], reverse=True)
    
    return associations
```

### 5. 链接生成

#### 5.1 生成双链链接

```python
def generate_link_text(doc, similarity_score, relationship=None):
    """生成链接描述文本"""
    description = f"{doc['title']}"
    
    if relationship:
        description += f"（{relationship}）"
    
    if similarity_score:
        similarity_percent = int(similarity_score * 100)
        description += f"，相似度: {similarity_percent}%"
    
    return description

def generate_foam_links(source_doc, target_docs, similarities):
    """生成Foam格式的双链接引用"""
    links = []
    
    for i, (doc, sim) in enumerate(zip(target_docs, similarities)):
        # 获取目标文档的相对路径
        rel_path = os.path.relpath(doc['path'], os.path.dirname(source_doc['path']))
        # 移除扩展名
        link_path = os.path.splitext(rel_path)[0]
        # 生成描述
        description = generate_link_text(doc, sim)
        # 创建链接
        links.append(f"- [[{link_path}]]: {description}")
    
    return links
```

#### 5.2 更新文档

```python
def update_document_links(doc_path, links):
    """在文档中更新关联链接"""
    with open(doc_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已有"自动关联"章节
    auto_link_pattern = r'## 自动关联[\s\S]*?<!-- 自动关联结束 -->'
    if re.search(auto_link_pattern, content):
        # 替换现有自动关联章节
        content = re.sub(
            auto_link_pattern,
            generate_auto_link_section(links),
            content
        )
    else:
        # 在文档末尾添加自动关联章节
        content += "\n\n" + generate_auto_link_section(links)
    
    # 写回文件
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(content)

def generate_auto_link_section(links):
    """生成自动关联章节文本"""
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
    section = f"## 自动关联\n<!-- 自动关联开始 -->\n"
    section += f"<!-- 生成时间: {now} -->\n\n"
    
    if links:
        section += "\n".join(links)
    else:
        section += "暂无自动关联内容"
    
    section += "\n<!-- 自动关联结束 -->"
    return section
```

### 6. VSCode集成

#### 6.1 VSCode任务配置

```json
// .vscode/tasks.json 配置示例
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "自动建立文档关联",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/document_association/build_links.py",
        "--workspace",
        "${workspaceFolder}",
        "--threshold",
        "0.65"
      ],
      "problemMatcher": [],
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    },
    {
      "label": "为当前文档创建关联",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/document_association/build_links.py",
        "--file",
        "${file}",
        "--threshold",
        "0.65"
      ],
      "problemMatcher": [],
      "presentation": {
        "reveal": "always",
        "panel": "new"
      }
    }
  ]
}
```

#### 6.2 主脚本实现

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
# document_association/build_links.py

"""
自动建立文档关联脚本
用于扫描Markdown文档并建立语义关联
"""

import os
import argparse
import re
import datetime
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from semantic_markdown_parser import MarkdownDocument

def main():
    # 参数解析
    parser = argparse.ArgumentParser(description='文档关联建立工具')
    parser.add_argument('--workspace', help='工作区路径')
    parser.add_argument('--file', help='单个文件路径')
    parser.add_argument('--threshold', type=float, default=0.65, help='相似度阈值')
    args = parser.parse_args()
    
    if args.workspace:
        process_workspace(args.workspace, args.threshold)
    elif args.file:
        process_single_file(args.file, args.threshold)
    else:
        print("错误: 必须指定 --workspace 或 --file 参数")
        return 1
    
    return 0

def process_workspace(workspace_path, threshold):
    """处理整个工作区"""
    print(f"正在处理工作区: {workspace_path}")
    
    # 1. 扫描文档
    markdown_files = scan_markdown_files(workspace_path)
    print(f"找到 {len(markdown_files)} 个Markdown文件")
    
    # 2. 加载文档
    documents = []
    for file_path in markdown_files:
        doc = load_document(file_path)
        documents.append(doc)
    
    # 3. 向量化
    embedder = DocumentEmbedder()
    embeddings = embedder.embed_documents(documents)
    
    # 4. 构建索引
    index = build_faiss_index(embeddings)
    
    # 5. 提取关键词
    keywords = extract_keywords(documents)
    
    # 6. 构建关联
    for i, doc in enumerate(documents):
        # 获取相似文档
        distances, indices = search_similar_documents(index, embeddings[i:i+1], k=10)
        
        # 过滤掉自身
        similar_docs = []
        similarities = []
        for j, idx in enumerate(indices[0]):
            if idx != i and distances[0][j] >= threshold:
                similar_docs.append(documents[idx])
                similarities.append(distances[0][j])
        
        if similar_docs:
            # 生成链接
            links = generate_foam_links(doc, similar_docs, similarities)
            
            # 更新文档
            update_document_links(doc['path'], links)
            print(f"已更新文档: {doc['path']}")
    
    print("工作区文档关联处理完成")

def process_single_file(file_path, threshold):
    """处理单个文件"""
    print(f"正在处理文件: {file_path}")
    
    # 获取工作区路径
    workspace_path = os.path.dirname(os.path.dirname(file_path))
    
    # 1. 扫描文档
    markdown_files = scan_markdown_files(workspace_path)
    
    # 2. 加载文档
    documents = []
    target_index = -1
    for i, path in enumerate(markdown_files):
        doc = load_document(path)
        documents.append(doc)
        if path == file_path:
            target_index = i
    
    if target_index == -1:
        print(f"错误: 找不到目标文件 {file_path}")
        return
    
    # 3. 向量化
    embedder = DocumentEmbedder()
    embeddings = embedder.embed_documents(documents)
    
    # 4. 构建索引
    index = build_faiss_index(embeddings)
    
    # 5. 提取关键词
    keywords = extract_keywords(documents)
    
    # 6. 获取相似文档
    distances, indices = search_similar_documents(index, embeddings[target_index:target_index+1], k=10)
    
    # 7. 过滤掉自身
    similar_docs = []
    similarities = []
    for j, idx in enumerate(indices[0]):
        if idx != target_index and distances[0][j] >= threshold:
            similar_docs.append(documents[idx])
            similarities.append(distances[0][j])
    
    if similar_docs:
        # 生成链接
        links = generate_foam_links(documents[target_index], similar_docs, similarities)
        
        # 更新文档
        update_document_links(file_path, links)
        print(f"已更新文档: {file_path}")
    else:
        print(f"没有找到与 {file_path} 相似度高于 {threshold} 的文档")
    
    print("文件关联处理完成")

if __name__ == "__main__":
    exit(main())
```

### 7. 部署流程

#### 7.1 系统部署

1. **克隆脚本库**：

   ```bash
   git clone https://github.com/company/document-association-scripts.git
   cd document-association-scripts
   ```

2. **安装依赖**：

   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境**：
   - 创建配置文件`config.yaml`：

     ```yaml
     workspace_path: "F:/101. link notebook/Obsidian Vault/KA/Product development"
     threshold: 0.65
     model_name: "all-MiniLM-L6-v2"
     document_types:
       - requirement
       - design
       - development
       - test
     ```

4. **复制到工作区**：

   ```bash
   mkdir -p "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/document_association"
   cp -r * "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/document_association/"
   ```

5. **设置VSCode任务**：
   - 在工作区创建`.vscode/tasks.json`，添加上述VSCode任务配置。

#### 7.2 自动化运行

1. **手动触发**：
   - 在VSCode中按`Ctrl+Shift+P`，输入"Tasks: Run Task"
   - 选择"自动建立文档关联"或"为当前文档创建关联"

2. **Git钩子集成**：
   - 创建`pre-commit`钩子：

     ```bash
     #!/bin/bash
     
     # 获取已修改的Markdown文件
     changed_md_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.md$')
     
     if [ -n "$changed_md_files" ]; then
       # 对每个修改的文件运行关联脚本
       for file in $changed_md_files; do
         python "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/document_association/build_links.py" --file "$file" --threshold 0.65
       done
       
       # 添加更新后的文件到暂存区
       git add $changed_md_files
     fi
     ```

### 8. 系统测试与验证

#### 8.1 测试步骤

1. **单元测试**：

   ```bash
   # 运行单元测试
   cd "F:/101. link notebook/Obsidian Vault/KA/Product development/scripts/document_association"
   python -m unittest discover tests
   ```

2. **功能测试**：
   - 创建测试文档集合
   - 运行关联脚本
   - 验证链接正确性

3. **性能测试**：
   - 测量大型文档库的处理时间
   - 监控内存使用情况

#### 8.2 验证指标

1. **准确性**：
   - 相关文档关联率 > 85%
   - 误关联率 < 15%

2. **性能**：
   - 单文档处理时间 < 5秒
   - 1000文档工作区处理时间 < 10分钟

3. **用户体验**：
   - 链接准确性满意度评分
   - 手动修正比例 < 20%

### 9. 可视化

[[产品体系可视化与交互系统集成框架]]

 [产品文档关联系统](产品文档关联系统.md) | VSCode Foam + AI语义分析 | 知识发现、内容导航 |

## 工具与技术

- **Foam**：VSCode插件，用于管理Markdown文档的双链链接。
- **SentenceTransformers**：用于生成文档嵌入的Python库。
- **Faiss**：高效的向量检索库。
- **MCP Server**：用于本地部署的模型上下文协议服务器。

## 未来计划

- **扩展支持**：增加对更多文档格式的支持。
- **优化算法**：提升语义分析和关键词匹配的准确性。
- **用户反馈**：收集用户反馈以改进系统功能。

## 相关文档

- [[产品体系构建框架]]：详细介绍了产品构建的各个阶段和流程。

## 推荐工具和MCP服务

### 实现框架流程

```mermaid
graph TD
    subgraph "1. 文档管理与显示层"
        A1[Foam VSCode插件] --> A2[文档双链显示]
        A1 --> A3[关系图谱可视化]
    end
    
    subgraph "2. 内容处理层"
        B1[文档预处理脚本] --> |扫描MD文件| B2[清洗和分段]
        B2 --> B3[语义解析]
        B3 --> B4[关键词提取]
    end
    
    subgraph "3. 语义分析层"
        C1[SentenceTransformers] --> |文档向量化| C2[向量存储]
        C2 --> |相似度计算| C3[文档关联判断]
        C4[混合相似度计算] --> C3
    end
    
    subgraph "4. MCP服务层"
        D1[GraphRAG MCP] --> |知识图谱构建| D2[文档关系存储]
        D3[RDF Explorer MCP] --> |图谱查询| D4[关系检索]
        D5[UML-MCP Server] --> |可视化生成| D6[关系图输出]
    end
    
    subgraph "5. 自动化与集成层"
        E1[VSCode任务系统] --> E2[手动触发更新]
        E3[Git钩子] --> E4[提交时自动更新]
        E5[IM Notifier MCP] --> E6[更新通知]
    end
    
    A1 --> B1
    B4 --> C1
    C3 --> D1
    D2 --> D3
    D4 --> D6
    D6 --> A3
    D2 --> E1
    D2 --> E3
    D2 --> E5
```

### 每阶段推荐工具详情

#### 1. 文档管理与显示层

- **Foam**：VS Code 插件，开源免费（MIT许可证）。提供Markdown文件的双向链接和笔记图谱可视化。
  - 安装：通过VS Code插件市场直接安装
  - 用途：管理文档链接、提供图谱视图、处理[[双链]]语法

#### 2. 内容处理层

- **Semantic-Markdown-Parser**：开源Python库（MIT许可证），将Markdown解析为语义段落。
  - 安装：`pip install semantic-markdown-parser`
  - 用途：结构化解析Markdown，将文档分解为语义块

- **自定义预处理脚本**：基于Python的文档扫描、清洗和关键词提取工具。
  - 实现：使用Python的`os`、`re`和`sklearn`库
  - 用途：文档清洗、分词和TF-IDF关键词提取

#### 3. 语义分析层

- **SentenceTransformers**：开源Python库，用于生成语义嵌入向量。
  - 安装：`pip install sentence-transformers`
  - 用途：将文本转换为语义向量，支持多种预训练模型

- **Faiss**：Facebook开源的向量索引库（MIT许可证），用于高效相似度检索。
  - 安装：`pip install faiss-cpu` 或 `pip install faiss-gpu`
  - 用途：构建向量索引，快速检索相似文档

#### 4. MCP服务层

- **GraphRAG**：微软开源的基于图的检索增强生成系统。
  - 安装：`pip install graphrag`
  - 用途：从非结构化文本中提取结构化知识图谱，构建文档关联网络
  - MCP配置：将其封装为MCP服务，提供知识抽取API

- **RDF Explorer**：自建MCP服务，用于知识图谱查询。
  - 基础：Python + RDFLib + FastAPI
  - 用途：提供对话式图谱查询，检索文档关系
  - MCP集成：在VS Code中注册为MCP服务器

- **UML-MCP Server**：基于MCP的UML图自动生成工具。
  - 安装：`pip install uml-mcp-server`
  - 用途：生成表示文档关系的UML图和可视化图表
  - 输出：将文档关系图以Mermaid或PlantUML格式输出

#### 5. 自动化与集成层

- **VSCode任务系统**：内置功能，用于定义和执行任务。
  - 配置：通过`.vscode/tasks.json`文件设置
  - 用途：手动触发文档关联更新

- **Git钩子**：版本控制工作流自动化脚本。
  - 实现：在`.git/hooks/pre-commit`中添加自定义脚本
  - 用途：提交前自动更新文档关联

- **IM Notifier MCP**：将更新通知发送到企业即时通讯平台。
  - 安装：`npm install -g im-notifier-mcp`
  - 用途：当文档关系更新时通知团队成员
  - 支持平台：钉钉、飞书、企业微信等

### MCP服务器配置示例

在VS Code中配置MCP服务器，使AI能够调用这些工具进行文档关联分析：

```json
// .vscode/settings.json 或 mcp.json
{
  "mcpServers": {
    "document-association": {
      "command": "python",
      "args": [
        "${workspaceFolder}/scripts/document_association/mcp_server.py"
      ],
      "env": {
        "WORKSPACE_PATH": "${workspaceFolder}",
        "THRESHOLD": "0.65",
        "MODEL_NAME": "all-MiniLM-L6-v2"
      }
    },
    "graphrag": {
      "command": "python",
      "args": ["-m", "graphrag.server", "--port", "3001"],
      "env": {
        "GRAPHRAG_DATA_DIR": "${workspaceFolder}/.graphrag"
      }
    },
    "uml-mcp": {
      "command": "python",
      "args": ["-m", "uml_mcp.server", "--port", "3002"],
      "env": {
        "UML_MCP_OUTPUT_DIR": "${workspaceFolder}/diagrams"
      }
    },
    "im-notifier": {
      "command": "npx",
      "args": ["im-notifier-mcp", "serve", "--port", "3003"],
      "env": {
        "DINGTALK_TOKEN": "your-token",
        "FEISHU_TOKEN": "your-token"
      }
    }
  }
}
```

### 轻量化实现策略

为确保系统轻量化、安全且易于维护：

1. **本地部署**：所有处理在本地完成，无需调用外部API
2. **按需处理**：仅在文档更新时触发处理流程，而非持续运行
3. **增量更新**：只处理变更的文档，避免重新处理整个库
4. **缓存向量**：将文档嵌入向量缓存到本地文件，避免重复计算
5. **分块处理**：大文档分块处理，控制内存使用和模型输入长度
6. **简化输出**：在自动关联章节使用简洁格式，减少生成内容体积
7. **手动审核机制**：使用HTML注释标记自动生成内容，方便人工编辑

通过以上工具组合和实现策略，可以构建一个既能满足语义关联需求，又符合轻量化、安全性和可控性要求的文档关联系统。
