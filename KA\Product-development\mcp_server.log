2025-07-05 23:54:36,181 - __main__ - INFO - 设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-05 23:54:37,122 - fastmcp_param_utils - INFO - ✅ 加载参数缓存: 28 项
2025-07-05 23:54:37,123 - mcp_debug - INFO - === MCP调试日志启动 ===
2025-07-05 23:54:37,124 - mcp_debug - INFO - 日志文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\logs\mcp_debug_20250705_235437.log
2025-07-05 23:54:37,124 - mcp_debug - INFO - 脚本基础路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-05 23:54:37,124 - mcp_debug - INFO - 调试模式: False
2025-07-05 23:54:37,124 - mcp_debug - INFO - 超时设置: 120秒
