//#include "thread/Header Files/serialThread.h"

//extern QMutex                  muteFacula;
//extern QByteArray              g_showFaculaWave;
//extern QMutex                  mute;
//extern SYSTEM_SERIAL_FLAG      g_systemSerialFlag;//解析协议完成

//serialThread::serialThread()
//{

//}

//serialThread::~serialThread()
//{

//}



//void serialThread::loop()
//{
//    m_isStartReceiveData = false;
//    m_isRecFinish = false;
//    m_serialPort->setReadBufferSize(2000);//与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析
//    QByteArray arr;
//    while(m_isLoop == false)
//    {
//        msleep(10);
//        if(m_isQuit == true)
//        {
//            return;
//        }
//    }
//    bool isFinish = false;
//    while(1)
//    {
//        m_serialPort->waitForReadyRead(50);
//        arr = m_serialPort->readAll();

//        if(m_isStartReceiveData == false)
//        {
//            isFinish = m_scanProt->parseFacProtocol(arr,100);
//        }
//        else
//        {
//            m_isStartReceiveData = false;
//            m_serialPort->clear(QSerialPort::Input);
//            m_serialPort->waitForReadyRead(200);
//            m_serialPort->clear(QSerialPort::Input);
//            uchar str[2] = {0xA7,0xFA};
//            m_serialPort->write((char*)str,2);
//            m_serialPort->waitForReadyRead(500);
//            recByteArray.clear();
//            recByteArray = m_serialPort->readAll();
//            m_isRecFinish = true;
//            str[0] = 0xA6;
//            str[1] = 0xFD;
//            m_serialPort->write((char*)str,2);
//        }

//        if(isSend == true)
//        {
//            isSend = false;
//            m_serialPort->write(sendBytes);
//            sendBytes.clear();
//        }


//        if(isFinish == true)
//        {
//            isFinish = false;
//            QMutexLocker lock(&mute);
//            g_systemSerialFlag.isFaculaRecFinish = true;
//            lock.unlock();
//        }

//        //1.2 退出线程
//        if(m_isQuit == true)
//        {
//            return;
//        }

//        //1.3 串口等待
//        while(!m_serialPort->isOpen())
//        {
//            msleep(100);
//            if(m_isQuit == true)
//            {
//                return;
//            }
//        }

//    }
//}

//void serialThread::run()
//{
//    m_isQuit = false;
//    m_isLoop = false;
//    isSend = false;
//    m_serialPort = new QSerialPort();
//    m_comboBOXPortLists = new QStringList();
//    //m_faculaWaveProtocol = new FaculaWaveProtocol();
////    m_scanProt = new dt2Protocol();

//    //qDebug() << "plc thread" << QThread::currentThreadId();
//    loop();
//    delete m_serialPort;
//    delete m_comboBOXPortLists;
//}


//QStringList &serialThread::detectPort()
//{
//    m_comboBOXPortLists->clear();
//    foreach (const QSerialPortInfo &info,  QSerialPortInfo::availablePorts())//遍历端口  并返回信息
//    {
//       m_comboBOXPortLists->push_back(info.portName());
//    }
//    return *m_comboBOXPortLists;
//}

//void serialThread::setQuitAndLoop(bool isQuit,bool isLoop)
//{
//    m_isQuit = isQuit;
//    m_isLoop = isLoop;
//}

//void serialThread::setCmd(const QByteArray &rec)
//{
//    if(m_serialPort->isOpen())
//    {
//        isSend = true;
//        //m_serialPort->write(rec);//需要移动到loop中运行
//        sendBytes = rec;
//    }
//}




