{"import_settings": {"supported_formats": ["xlsx", "csv", "json", "md"], "auto_validation": true, "backup_enabled": true, "event_trigger": true}, "sources": {"jira": {"enabled": false, "url": "", "auth": ""}, "confluence": {"enabled": false, "url": "", "auth": ""}, "excel": {"enabled": true, "template_path": "templates/requirements_template.xlsx"}}, "output": {"format": "markdown", "location": "requirements/", "event_file": "requirements_import_completed.json"}}