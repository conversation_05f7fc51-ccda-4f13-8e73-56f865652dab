<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cLensAdjust</class>
 <widget class="QDockWidget" name="cLensAdjust">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>761</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowIcon">
   <iconset resource="../motorMonitor/sub_resource.qrc">
    <normaloff>:/icon/cspc1.jpg</normaloff>:/icon/cspc1.jpg</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QDockWidget
{
	background-color: rgb(255,255,255);
	
	font: 12pt &quot;黑体&quot;;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 6px;
}
QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

#serialOpen{
	min-width: 200px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#serialOpen:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
#serialOpen:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}

#productionInfo{
	min-width: 100px;
	min-height: 25px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#productionInfo:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
#productionInfo:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}

QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}

#adjustResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	max-width: 200px;
	min-height:20px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 22pt &quot;黑体&quot;;
}

#solidResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	max-width: 200px;
	min-height:30px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 26pt &quot;黑体&quot;;
}

#finalResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	min-height:40px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 30pt &quot;黑体&quot;;
}

#processView{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 300px;
	min-height:50px;

	font: 10pt &quot;黑体&quot;;
}

QCustomPlot{
	/*min-width: 1200px;
	min-height: 1000px;*/
}
</string>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <layout class="QGridLayout" name="gridLayout_3">
    <item row="3" column="1">
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="0" column="2" rowspan="7">
     <widget class="QStackedWidget" name="mapShow">
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="autoMapShow">
       <layout class="QGridLayout" name="gridLayout_6">
        <item row="2" column="0">
         <widget class="QTableWidget" name="greyMapExpand"/>
        </item>
        <item row="1" column="0">
         <widget class="QTableWidget" name="greyMap">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">QTableView::item {
    /*border: 1px solid red;*/
}</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="manualMapShow">
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="0">
         <widget class="QTableWidget" name="handleGreyMap"/>
        </item>
        <item row="1" column="0">
         <widget class="QWidget" name="handleCtrlDevice" native="true">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <layout class="QGridLayout" name="gridLayout_7">
           <item row="1" column="4">
            <widget class="QGroupBox" name="groupBox">
             <property name="styleSheet">
              <string notr="true">QPushButton{
	min-width: 50px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

QPushButton:hover{
	background-color: rgb(25, 25, 25);
	color: ;
	color: rgb(255, 255, 255);
	border: 2px solid white; /**/
	border-radius: 6px;	
}	

#retest
{
	background-color: rgba(0, 85, 0,0.7);
	color:  rgb(255, 255, 255);
	font: 12pt &quot;黑体&quot;;
	border-radius: 6px；	
}
#retest:hover
{
	background-color: rgb(255, 255, 255);
	color: rgba(0, 85, 0,0.7);
	font: 12pt &quot;黑体&quot;;
	border-radius: 6px;
}</string>
             </property>
             <property name="title">
              <string>单功能</string>
             </property>
             <widget class="QPushButton" name="mesDataInput">
              <property name="geometry">
               <rect>
                <x>230</x>
                <y>100</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>校正模式</string>
              </property>
             </widget>
             <widget class="QPushButton" name="manualAdjust">
              <property name="geometry">
               <rect>
                <x>10</x>
                <y>30</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>调节</string>
              </property>
             </widget>
             <widget class="QPushButton" name="manualSolid">
              <property name="geometry">
               <rect>
                <x>80</x>
                <y>30</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>固化</string>
              </property>
             </widget>
             <widget class="QPushButton" name="manualDeflate">
              <property name="geometry">
               <rect>
                <x>160</x>
                <y>30</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>放气</string>
              </property>
             </widget>
             <widget class="QPushButton" name="manualExit">
              <property name="geometry">
               <rect>
                <x>230</x>
                <y>30</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>退出</string>
              </property>
             </widget>
             <widget class="QPushButton" name="retest">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>90</y>
                <width>71</width>
                <height>51</height>
               </rect>
              </property>
              <property name="text">
               <string>复测</string>
              </property>
             </widget>
             <widget class="QPushButton" name="greyMode">
              <property name="geometry">
               <rect>
                <x>160</x>
                <y>100</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>灰度模式</string>
              </property>
             </widget>
             <widget class="QPushButton" name="coordinate">
              <property name="geometry">
               <rect>
                <x>80</x>
                <y>100</y>
                <width>61</width>
                <height>39</height>
               </rect>
              </property>
              <property name="text">
               <string>坐标</string>
              </property>
             </widget>
            </widget>
           </item>
           <item row="0" column="0" rowspan="2">
            <spacer name="horizontalSpacer_6">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Expanding</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>360</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="1" rowspan="2">
            <layout class="QVBoxLayout" name="verticalLayout">
             <item>
              <widget class="QPushButton" name="pushButton_7">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>54</width>
                 <height>39</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	min-width: 50px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}</string>
               </property>
               <property name="text">
                <string>Z--</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_6">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>54</width>
                 <height>39</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	min-width: 50px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}</string>
               </property>
               <property name="text">
                <string>Z-</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>54</width>
                 <height>39</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	min-width: 50px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}</string>
               </property>
               <property name="text">
                <string>Z+</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_2">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>54</width>
                 <height>39</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
	min-width: 50px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}</string>
               </property>
               <property name="text">
                <string>Z++</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="0" column="2">
            <spacer name="horizontalSpacer_7">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Preferred</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="0" column="4">
            <widget class="QGroupBox" name="manualCoordinatesShow">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>300</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QLineEdit
{
	min-width: 50px;
	min-height: 25px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}</string>
             </property>
             <property name="title">
              <string>坐标</string>
             </property>
             <widget class="QWidget" name="gridLayoutWidget">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>20</y>
                <width>291</width>
                <height>131</height>
               </rect>
              </property>
              <layout class="QGridLayout" name="gridLayout_4">
               <item row="3" column="2">
                <widget class="QLineEdit" name="cur_coor_Z"/>
               </item>
               <item row="2" column="1">
                <widget class="QLineEdit" name="origin_coor_Y"/>
               </item>
               <item row="1" column="1">
                <widget class="QLineEdit" name="origin_coor_X"/>
               </item>
               <item row="0" column="2">
                <widget class="QLabel" name="label_15">
                 <property name="text">
                  <string>最终</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="3">
                <widget class="QLineEdit" name="move_y"/>
               </item>
               <item row="1" column="3">
                <widget class="QLineEdit" name="move_x"/>
               </item>
               <item row="2" column="2">
                <widget class="QLineEdit" name="cur_coor_Y"/>
               </item>
               <item row="3" column="3">
                <widget class="QLineEdit" name="move_z"/>
               </item>
               <item row="1" column="2">
                <widget class="QLineEdit" name="cur_coor_X"/>
               </item>
               <item row="0" column="3">
                <widget class="QLabel" name="label_16">
                 <property name="text">
                  <string>移动</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QLineEdit" name="origin_coor_Z"/>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="label_14">
                 <property name="text">
                  <string>初始</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_10">
                 <property name="text">
                  <string>Z：</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_12">
                 <property name="text">
                  <string>X:</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_13">
                 <property name="text">
                  <string>Y：</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item row="6" column="0" colspan="2">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QLineEdit" name="adjustResult">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>302</width>
          <height>24</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>302</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#adjustResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	max-width: 200px;
	min-height:20px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 22pt &quot;黑体&quot;;
}</string>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="solidResult">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>302</width>
          <height>34</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>302</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#solidResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	max-width: 200px;
	min-height:30px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 26pt &quot;黑体&quot;;
}</string>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="finalResult">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>302</width>
          <height>44</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#finalResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	min-width: 200px;
	min-height:40px;

	text-align: left top;
    padding-left: 100px;
    padding-top: 2px;
	font: 30pt &quot;黑体&quot;;
}</string>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QTextEdit" name="processView">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="maximumSize">
         <size>
          <width>160</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="0" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout">
      <item row="3" column="1">
       <widget class="QLineEdit" name="faculaArea">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}</string>
        </property>
        <property name="text">
         <string>1,2,3,4</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="faculaType">
        <item>
         <property name="text">
          <string>圆形光斑</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>横条形光斑</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>竖条形光斑</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>双半光斑</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>不规则光斑</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_17">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>目标区域:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="faculaForm">
        <item>
         <property name="text">
          <string>聚焦光斑</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>虚光斑</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>光斑类型:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>光斑形态:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_9">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模式:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="modeBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <item>
         <property name="text">
          <string>自动调节</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>手动调节</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </item>
    <item row="7" column="0" colspan="3">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>总数：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="productTotalNum"/>
      </item>
      <item>
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>良品数：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="goodProductNum"/>
      </item>
      <item>
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>不良数：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="badProductNum"/>
      </item>
      <item>
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>不良率：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="badProductRate"/>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>单次耗时：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="processTime"/>
      </item>
      <item>
       <widget class="QLabel" name="label_18">
        <property name="text">
         <string>平均耗时：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="averProcessTime"/>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="productionInfoClean">
        <property name="text">
         <string>置零</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="1">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="5" column="1">
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,0">
      <property name="spacing">
       <number>6</number>
      </property>
      <item row="1" column="1">
       <widget class="QComboBox" name="devicePortBox"/>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="portBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>设备端口:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>端口:</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="4" column="1">
     <widget class="QPushButton" name="serialOpen">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>204</width>
        <height>39</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="text">
       <string>open</string>
      </property>
      <property name="shortcut">
       <string>Ctrl+Space</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="../motorMonitor/sub_resource.qrc"/>
 </resources>
 <connections/>
</ui>
