#include "ImageProcessingTest.h"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>

/**
 * @brief ImageProcessing库独立测试程序
 * 
 * 这个程序提供了独立的测试环境，用于验证所有滤波器和插值算法的功能
 * 不依赖于主项目的tmp[100]数组，使用标准的5x5测试数据
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "========================================";
    qDebug() << "ImageProcessing库独立测试程序";
    qDebug() << "版本: 1.0.0";
    qDebug() << "========================================";
    
    // 创建测试实例
    ImageProcessing::ImageProcessingTest tester;
    
    // 运行所有测试
    bool allTestsPassed = tester.runAllTests();
    
    qDebug() << "========================================";
    if (allTestsPassed) {
        qDebug() << "🎉 所有测试通过！ImageProcessing库功能正常";
        std::cout << "\n✅ All tests passed! ImageProcessing library is working correctly.\n" << std::endl;
        return 0;
    } else {
        qDebug() << "❌ 部分测试失败，请检查实现";
        std::cout << "\n❌ Some tests failed. Please check the implementation.\n" << std::endl;
        return 1;
    }
}

/**
 * 测试数据说明：
 * 
 * 5x5测试矩阵：
 * 271, 882, 826, 748, 58
 * 1011, 908, 792, 756, 738
 * 1074, 924, 807, 800, 859
 * 1021, 877, 777, 776, 855
 * 145, 887, 788, 740, 33
 * 
 * 这个矩阵用于测试：
 * 1. 卷积滤波器 - 验证3x3锐化核的边界处理和计算正确性
 * 2. 加权均值滤波器 - 测试不同权重模式的平滑效果
 * 3. 中值滤波器 - 验证噪声去除和边缘保持能力
 * 4. 高斯滤波器 - 测试不同sigma值的模糊效果
 * 5. 双边滤波器 - 验证边缘保持的平滑处理
 * 6. 卡尔曼滤波器 - 测试时序滤波和噪声抑制
 * 
 * 预期行为：
 * - 所有滤波器应该保持输入输出尺寸一致（5x5）
 * - 边界处理应该使用镜像扩展
 * - 滤波强度参数应该正确影响输出结果
 * - 不同预设模式应该产生不同的滤波效果
 */
