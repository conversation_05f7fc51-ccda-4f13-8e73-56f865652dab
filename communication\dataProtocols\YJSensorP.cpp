#include "YJSensorP.h"

#define FRAME_COUT 131//163

YJSensorP::YJSensorP():
  m_angleLast(0.0)
, m_protocolType(0)
{
  m_strPre.clear();
  m_pointNumPerCol = 64;
}

YJSensorP::~YJSensorP() {
    ;
}

QByteArray YJSensorP::getControlCmd(const char &id) {

}

QByteArray YJSensorP::getWriteCmd(const char &id, const QByteArray &w_data) {

}

QByteArray YJSensorP::getReadCmd(const uint8_t &id, const QByteArray &w_data) {

}

#if 0
bool YJSensorP::parseCloudProtocolSamSung(QByteArray str,int length)
{
    Q_UNUSED(length);
    QByteArray stA = strPre + str;
    if(stA.length() < 10)
    {
        strPre = stA;
        if(strPre.size() > 102400)
        {
            strPre.clear();
        }
        return false;
    }
    strPre.clear();
    uint pointNumber = 0;
    //float spd = 0;
    float startAngle = 0;
    float endAngle = 0;

    uint8_t checkSumL = 0,checkSumH = 0;

    StPointCloud3D pointCld2d(0,0,0,0,0);
    uint16_t sum = 0;
    for(int n=0; n<stA.length(); n++)
    {
      if(stA.length() - n >= 60)
      {
        //1.0 接收设备数据        先移位再强制转化
        if((uchar)stA.at(n) == 0xAA && (uchar)stA.at(n+1) == 0x55 && (((uchar)stA.at(n+2))&0x01) == 0x00) //点云包
        {
            checkSumL = 0;
            checkSumH = 0;
            checkSumL ^= (uint8_t)stA.at(0 + n) ^(uint8_t)stA.at(2 + n) ^(uint8_t)stA.at(4 + n) ^(uint8_t)stA.at(6 + n);
            checkSumH ^= (uint8_t)stA.at(1 + n) ^(uint8_t)stA.at(3 + n) ^(uint8_t)stA.at(5 + n) ^(uint8_t)stA.at(7 + n);
            for(int i=0; i<(uchar)stA.at(n+3); i++)
            {
                checkSumL ^= (uint8_t)stA.at(2*i + 10 + n);
                checkSumH ^= (uint8_t)stA.at(2*i + 1 + 10 + n) ;
            }

            if(checkSumL == (uint8_t)stA.at(8 + n) && checkSumH == (uint8_t)(stA.at(9 + n)))//校验通过
            {
                pointNumber = (uint8_t)stA.at(n+3);

                startAngle = (((uint8_t)stA.at(n+4) | (uint16_t(stA.at(n+5)<<8))) >> 1)/64.0;
                endAngle = (((uint8_t)stA.at(n+6) | (uint16_t(stA.at(n+7)<<8))) >> 1)/64.0;
                double incAngle = 0;
                //qDebug() << "tmpAngle: " <<startAngle << " " << endAngle;
                //2.0 计算角度
                if(startAngle > endAngle)
                {
                    incAngle = (360 - startAngle + endAngle)/(pointNumber-1);
                }
                else
                {
                    incAngle = (endAngle - startAngle)/(pointNumber-1);
                }
                int m = n + 10;
                for(uint i=0; i<pointNumber; i++)
                {
                    //2.1 计算距离
                    uint16_t highByte = stA.at(m+2*i+1)<<8;
                    uint8_t lowByte = stA.at(m+2*i);
                    uint16_t depth = highByte | lowByte;


                    float tmpdepth = depth>>3;
                    float tmpAngle = startAngle + i*incAngle;


                    tmpAngle = tmpAngle > 360 ? tmpAngle - 360.0:tmpAngle;
                    //double tmpHeight = 0.0;

                    pointCld2d.m_angle = tmpAngle;


                    pointCld2d.m_distance = tmpdepth;
                    pointCld2d.m_intensity = 0;

                    m_scanVec.push_back(pointCld2d);

//                    if((depth&0x0001) == 0x0001  || (depth&0x0002) == 0x0002  || (depth&0x0004) == 0x0004)
//                    {
//                        uint8_t  val = (depth&0x0007);
//                       // qDebug() << "IR " << val;
//                    }
//                    else
//                    {
//                       // uint8_t  val = (depth&0x07);
//                      // qDebug() << "tmpAngle: " <<tmpAngle << "IR " << val;
//                    }


                    if((uchar)stA.at(n+2) == 0x28)
                    {
                        m_scanPointCloud.m_hardAndSoftwareVersion = 0x28;
                    }

                }
                stA.remove(0,n+pointNumber*2+10);
                n = -1;
            }

        }
        //1.1 接收设备的 信息
        else if((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n+1) == 0x5A && (uchar)stA.at(n+2) == 0x14 && (uchar)stA.at(n+3) == 0x00)
        {
            for(int i=0; i<27; i++)
            {
                if(i == 4 || i == 5)
                {
                    continue;
                }
                sum += (uint8_t)stA.at(i+n);
            }
            if(sum == ((uint8_t)stA.at(n+4) | uint16_t(stA.at(n+5)<<8)))
            {
                m_scanPointCloud.m_version = (uint8_t)stA.at(n+26);
                m_version = (uint8_t)stA.at(n+26);
                stA.remove(0,n+27);
                n = -1;
            }

        }
        //1.2 接收 固定的开始发送设备数据的 信息
        else if((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n+1) == 0x5A && (uchar)stA.at(n+6) == 0x81)
        {

        }
        //1.4 零度起始包
        else if((uchar)stA.at(n) == 0xAA && (uchar)stA.at(n+1) == 0x55 && (((uchar)stA.at(n+2))&0x01) == 0x01)
        {
            //4.0 校验 m_scanPointCloud
            checkSumL = 0;
            checkSumH = 0;
            checkSumL ^= (uint8_t)stA.at(0 + n) ^(uint8_t)stA.at(2 + n) ^(uint8_t)stA.at(4 + n) ^(uint8_t)stA.at(6 + n);
            checkSumH ^= (uint8_t)stA.at(1 + n) ^(uint8_t)stA.at(3 + n) ^(uint8_t)stA.at(5 + n) ^(uint8_t)stA.at(7 + n);
            for(int i=0; i<(uchar)stA.at(n+3); i++)
            {
                checkSumL ^= (uint8_t)stA.at(2*i + 10 + n);
                checkSumH ^= (uint8_t)stA.at(2*i + 1 + 10 + n) ;
            }

            if(checkSumL == (uint8_t)stA.at(8 + n) && checkSumH == (uint8_t)(stA.at(9 + n)))//校验通过
            {
                m_scanPointCloud.pointCloud2d = m_scanVec;
                m_scanPointCloud.m_speed = (((uchar)stA.at(n+2))>>1)/10.0;
                m_scanPointCloud.m_healthCode = 0;
                m_scanPointCloud.m_mcuVoltage = 0;
                m_scanVec.clear();
                stA.remove(0,n+12);
                strPre = stA;
                //n = -1;
            }
        }
      }
      else
      {
        strPre = stA;
        return false;
      }

    }
    return false;
}
#endif
