#ifndef IMAGEPROCESSING_IMAGEDATA_H
#define IMAGEPROCESSING_IMAGEDATA_H

#include <QVector>
#include <QDebug>
#include <memory>
#include <stdexcept>

namespace ImageProcessing {

/**
 * @brief 图像数据模板类
 * @tparam T 数据类型（如uint32_t, float等）
 * 
 * 提供类型安全的图像数据存储和操作接口
 */
template<typename T>
class ImageData {
public:
    using MatrixType = QVector<QVector<T>>;
    using ValueType = T;

    /**
     * @brief 默认构造函数
     */
    ImageData() : width_(0), height_(0) {}

    /**
     * @brief 构造函数
     * @param width 图像宽度
     * @param height 图像高度
     */
    ImageData(uint32_t width, uint32_t height) 
        : width_(width), height_(height) {
        resize(width, height);
    }

    /**
     * @brief 拷贝构造函数
     */
    ImageData(const ImageData& other) 
        : matrix_(other.matrix_), width_(other.width_), height_(other.height_) {}

    /**
     * @brief 移动构造函数
     */
    ImageData(ImageData&& other) noexcept
        : matrix_(std::move(other.matrix_)), width_(other.width_), height_(other.height_) {
        other.width_ = 0;
        other.height_ = 0;
    }

    /**
     * @brief 赋值操作符
     */
    ImageData& operator=(const ImageData& other) {
        if (this != &other) {
            matrix_ = other.matrix_;
            width_ = other.width_;
            height_ = other.height_;
        }
        return *this;
    }

    /**
     * @brief 移动赋值操作符
     */
    ImageData& operator=(ImageData&& other) noexcept {
        if (this != &other) {
            matrix_ = std::move(other.matrix_);
            width_ = other.width_;
            height_ = other.height_;
            other.width_ = 0;
            other.height_ = 0;
        }
        return *this;
    }

    /**
     * @brief 重新调整图像大小
     * @param width 新宽度
     * @param height 新高度
     */
    void resize(uint32_t width, uint32_t height) {
        width_ = width;
        height_ = height;
        matrix_.resize(height);
        for (auto& row : matrix_) {
            row.resize(width);
        }
    }

    /**
     * @brief 清空图像数据
     */
    void clear() {
        matrix_.clear();
        width_ = 0;
        height_ = 0;
    }

    /**
     * @brief 填充图像数据
     * @param value 填充值
     */
    void fill(const T& value) {
        for (auto& row : matrix_) {
            row.fill(value);
        }
    }

    /**
     * @brief 检查图像数据是否有效
     * @return true if valid, false otherwise
     */
    bool isValid() const {
        return width_ > 0 && height_ > 0 && 
               matrix_.size() == static_cast<int>(height_) &&
               (matrix_.empty() || matrix_[0].size() == static_cast<int>(width_));
    }

    /**
     * @brief 获取图像宽度
     */
    uint32_t width() const { return width_; }

    /**
     * @brief 获取图像高度
     */
    uint32_t height() const { return height_; }

    /**
     * @brief 获取图像总像素数
     */
    uint32_t size() const { return width_ * height_; }

    /**
     * @brief 检查是否为空
     */
    bool empty() const { return width_ == 0 || height_ == 0; }

    /**
     * @brief 获取矩阵数据的引用
     */
    MatrixType& matrix() { return matrix_; }

    /**
     * @brief 获取矩阵数据的常量引用
     */
    const MatrixType& matrix() const { return matrix_; }

    /**
     * @brief 访问指定位置的像素值（带边界检查）
     * @param x 列索引
     * @param y 行索引
     * @return 像素值的引用
     */
    T& at(uint32_t x, uint32_t y) {
        if (y >= height_ || x >= width_) {
            throw std::out_of_range("ImageData::at() - index out of range");
        }
        return matrix_[y][x];
    }

    /**
     * @brief 访问指定位置的像素值（常量版本，带边界检查）
     */
    const T& at(uint32_t x, uint32_t y) const {
        if (y >= height_ || x >= width_) {
            throw std::out_of_range("ImageData::at() - index out of range");
        }
        return matrix_[y][x];
    }

    /**
     * @brief 安全访问像素值（超出边界返回默认值）
     * @param x 列索引
     * @param y 行索引
     * @param defaultValue 默认值
     * @return 像素值
     */
    T safeAt(uint32_t x, uint32_t y, const T& defaultValue = T{}) const {
        if (y >= height_ || x >= width_) {
            return defaultValue;
        }
        return matrix_[y][x];
    }

    /**
     * @brief 获取指定行的引用
     * @param y 行索引
     * @return 行数据的引用
     */
    QVector<T>& row(uint32_t y) {
        if (y >= height_) {
            throw std::out_of_range("ImageData::row() - row index out of range");
        }
        return matrix_[y];
    }

    /**
     * @brief 获取指定行的常量引用
     */
    const QVector<T>& row(uint32_t y) const {
        if (y >= height_) {
            throw std::out_of_range("ImageData::row() - row index out of range");
        }
        return matrix_[y];
    }

    /**
     * @brief 比较操作符
     */
    bool operator==(const ImageData& other) const {
        return width_ == other.width_ && 
               height_ == other.height_ && 
               matrix_ == other.matrix_;
    }

    bool operator!=(const ImageData& other) const {
        return !(*this == other);
    }

    /**
     * @brief 调试输出
     */
    void debugPrint() const {
        qDebug() << "ImageData: " << width_ << "x" << height_;
        if (isValid() && width_ <= 10 && height_ <= 10) {
            for (uint32_t y = 0; y < height_; ++y) {
                QString row_str;
                for (uint32_t x = 0; x < width_; ++x) {
                    row_str += QString::number(matrix_[y][x]) + " ";
                }
                qDebug() << "Row" << y << ":" << row_str;
            }
        }
    }

private:
    MatrixType matrix_;     ///< 图像数据矩阵
    uint32_t width_;        ///< 图像宽度
    uint32_t height_;       ///< 图像高度
};

// 常用类型别名
using ImageDataU32 = ImageData<uint32_t>;
using ImageDataF32 = ImageData<float>;
using ImageDataU16 = ImageData<uint16_t>;
using ImageDataU8 = ImageData<uint8_t>;

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_IMAGEDATA_H
