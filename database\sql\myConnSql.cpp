#include "myConnSql.h"
#include "QSqlRecord"

#define DATABASE_OPEN_SQL                                                                                                                                      \
    "DRIVER={Progress OpenEdge 10.2B "                                                                                                                         \
    "Driver};DSN=hyh-prod;DB=hlpprod;UID=mfg;PWD=*******;HOST=**********;PORT="                                                                                \
    "56398;"

// QString MyConnSql::connection = "conn1";
// QSqlDatabase MyConnSql::db = QSqlDatabase::addDatabase("QODBC", connection);
const MyConnSql::ST_DATABASE    MyConnSql::m_motor_speed_db   = {"motor_speed", "conn1"};
const MyConnSql::ST_DATABASE    MyConnSql::m_lidar_speed_db   = {"lidar_speed", "conn2"};
const MyConnSql::ST_DATABASE    MyConnSql::m_turn_on_db       = {"turnOn_time", "conn3"};
const MyConnSql::ST_DATABASE    MyConnSql::m_mirror_adjust_db = {"mirror_adjust", "conn4"};
QVector<MyConnSql::ST_DATABASE> MyConnSql::m_database_v       = {MyConnSql::m_motor_speed_db,
                                                           MyConnSql::m_lidar_speed_db,
                                                           MyConnSql::m_turn_on_db,
                                                           MyConnSql::m_mirror_adjust_db};
// const QString m_database_name = "motor speed monitor";
// const QString m_database_name1 = "facula adjust";

MyConnSql::MyConnSql() {
    ;
}

MyConnSql::~MyConnSql() {
    ;
}

bool MyConnSql::createConnection(const QString &connection, const QString &db_name) {
    /*2. 数据库链接*/
    //    QString connection = "conn1";
    QSqlDatabase db = QSqlDatabase::addDatabase("QODBC", connection);
    db.setHostName("127.0.0.1");
    db.setPort(3306);
    db.setDatabaseName(db_name);  // ODBC中设置数据库名称,已有，则选择该数据库
    db.setUserName("root");
    db.setPassword("123456");
    if (!db.open()) {
        //        QMessageBox::information(0, QString("Cannot open database: ") +
        //        QString(db_name),
        //                   QString("Unable to establisha database connection."));
        QString text = db.lastError().text();
        qDebug() << "error open database because" << db.lastError().text();
        return false;
    }
#if 1
    //    MyConnSql::excuteSQL("create table student (id int primary key, "
    //                                              "name varchar(20))");
    //    MyConnSql::excuteSQL(db, "insert into student values(0, 'first')");
    //    MyConnSql::excuteSQL(db, "select * from student");
    //    MyConnSql::excuteSQL(db, "create table student (id int primary key, "
    //                     "name varchar(20))");
    //    MyConnSql::excuteSQL(db, "insert into student values(0, 'first')");
    //    MyConnSql::excuteSQL(db, "select * from student");
    //以下执行相关sql语句
    QSqlQuery query(db);

    query.exec("create table student (id int primary key, "
               "name varchar(20))");
    //查询数据库中所有表的名称
    QStringList tables = db.tables();
    foreach (QString table, tables) {
        qDebug() << table;
        //        query.exec("DROP TABLE " + table);
    }
    tables = db.tables();
#endif
    db.close();
    return true;
}


bool MyConnSql::createCloudConnection(QSqlDatabase &db, const StSqlConnInfo &sql_connect_info) {
    QString connection = sql_connect_info.conn_name;
    db                 = QSqlDatabase::addDatabase("QODBC", connection);
    //    db.setHostName("**********");
    //    db.setPort(56398); //
    //    db.setDatabaseName("hyh");  //ODBC中设置数据库名称,已有，则选择该数据库
    //    db.setUserName("mfg");
    //    db.setPassword("*******");

    db.setHostName(sql_connect_info.host_name);
    db.setPort(sql_connect_info.port);                    //
    db.setDatabaseName(sql_connect_info.obdc_base_name);  // ODBC中设置数据库名称,已有，则选择该数据库
    db.setUserName(sql_connect_info.user_name);
    db.setPassword(sql_connect_info.password);
    if (!db.open()) {
        QString text = db.lastError().text();
        qDebug() << "error open database because" << db.lastError().text();
        return false;
    }

#if 0
//    MyConnSql::excuteSQL("create table student (id int primary key, "
//                                              "name varchar(20))");
    //以下执行相关sql语句
    QSqlQuery query(db);

    //查询数据库中所有表的名称
    QStringList tables = db.tables();
    foreach(QString table, tables) {
        qDebug()<<table;
//        query.exec("DROP TABLE " + table);
    }


    //* 显示表内容
    query.exec("select * from pub.xuser_det"); // where xlbop_nbr = '%s'
    while (query.next()) {
        // 从当前记录中取出各个字段的值
        qDebug() << query.value(0).toString()
                 << query.value(1).toString();
//                     << query.value("dname").toString()
//                     << query.value("loc").toString();
    }

#endif
    //    db.close();
    return true;
}

bool MyConnSql::createCloudConnection() {
    QString      connection = "cloud_conn";
    QSqlDatabase db         = QSqlDatabase::addDatabase("QODBC", connection);
    db.setHostName("**********");
    db.setPort(56398);          //
    db.setDatabaseName("hyh");  // ODBC中设置数据库名称,已有，则选择该数据库
    db.setUserName("mfg");
    db.setPassword("*******");
    if (!db.open()) {
        QString text = db.lastError().text();
        qDebug() << "error open database because" << db.lastError().text();
        return false;
    }

#if 1
    //    MyConnSql::excuteSQL("create table student (id int primary key, "
    //                                              "name varchar(20))");
    //以下执行相关sql语句
    QSqlQuery query(db);

    //查询数据库中所有表的名称
    QStringList tables = db.tables();
    foreach (QString table, tables) {
        qDebug() << table;
        //        query.exec("DROP TABLE " + table);
    }

    //* 显示表内容
    query.exec("select * from pub.xuser_det");  // where xlbop_nbr = '%s'
    while (query.next()) {
        // 从当前记录中取出各个字段的值
        qDebug() << query.value(0).toString() << query.value(1).toString();
        //                     << query.value("dname").toString()
        //                     << query.value("loc").toString();
    }

#endif
    db.close();
    return true;
}

QSqlQuery MyConnSql::excuteSQL(const QString &sqlcmd) {

    //    if(!db.open()){
    //        qDebug() << "Database Error: " << db.lastError().text();
    //        return QSqlQuery();
    //    }
    QSqlQuery myQuery;
    if (!myQuery.exec(sqlcmd)) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        return QSqlQuery();
    }
    return myQuery;
}

QSqlQuery MyConnSql::excuteSQL(QSqlDatabase &db, const QString &sqlcmd) {

    //    if(!db.open()){
    //        qDebug() << "Database Error: " << db.lastError().text();
    //        return QSqlQuery();
    //    }

    QSqlQuery myQuery(db);
    try {
        if (!myQuery.exec(sqlcmd)) {
            throw QSqlError(myQuery.lastError());
        }
    } catch (QSqlError &e) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        qDebug() << "Exception caught: " << e.text();
    }
    db.close();
    return myQuery;
}

bool MyConnSql::excuteSQL(QSqlQuery &query, const QString &sqlcmd) {
    if (!query.exec(sqlcmd)) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        return false;
    }
    //    db.close();
    return true;
}

/**
 * @brief MyConnSql::originTableShow
 * @param db
 * @param sqlcmd
 * @param print_col_num, show column select
 * @return
 */
bool MyConnSql::originTableShow(QSqlDatabase &db, const QString &sqlcmd, const uint8_t &print_col_num) {
    QSqlQuery myQuery(db);

    try {
        if (!myQuery.exec(sqlcmd)) {
            throw QSqlError(myQuery.lastError());
        }

    } catch (QSqlError &e) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        qDebug() << "Exception caught: " << e.text();

        db.close();
        return false;
    }

    while (myQuery.next()) {
        // 判断当前记录是否有效
        if (myQuery.isValid()) {  //&& myQuery.last()
            int row_num = myQuery.at();
            //获取每条记录中属性（即列）的个数
            int column_num = myQuery.record().count();
            //获取属性所在列的编号，列从左向右编号，最左边的编号为0

            //获取属性的值
            QString one_row      = " ";
            uint    print_column = ((print_col_num > column_num) || (print_col_num == 0)) ? column_num : print_col_num;

            for (uint16_t for_i = 0; for_i < print_column; for_i++) {
                one_row += QString("%1 ").arg(myQuery.value(for_i).toString());
            }
            qInfo() << "lens sql/"
                    << "row: " << row_num << "print columns" << print_column << one_row;
        }
    }
    return true;
}

bool MyConnSql::queryOneValue(QSqlDatabase &db, const QString &sqlcmd, const QString &column, QString &get_data) {
    QSqlQuery myQuery(db);
    try {
        if (!myQuery.exec(sqlcmd)) {
            throw QSqlError(myQuery.lastError());
        }
    } catch (QSqlError &e) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        qDebug() << "Exception caught: " << e.text();

        db.close();
        return false;
    }
    //    while(myQuery.next()) {//日期索引有结果
    // 判断当前记录是否有效
    //            int row_num = myQuery.at();
    //            //获取每条记录中属性（即列）的个数
    //            int column_num = myQuery.record().count();

    //            //获取属性所在列的编号，列从左向右编号，最左边的编号为0
    //            int fieldNo = myQuery.record().indexOf(column);

    //            if(fieldNo < 0) {
    //                qDebug() << "-i sql single search " << column << "/row:" <<
    //                row_num << "column:" << column_num << "index" << fieldNo;
    //            }
    //            else {
    //                //获取属性的值
    //                QString tr_tmp = myQuery.value(fieldNo).toString();
    //                if(tr_tmp != "") get_data = tr_tmp;
    //            }
    //            if(column_num > 3)
    //                qDebug() << "-i sql/ signal search " << column << "row:" <<
    //                row_num << "column:" << column_num
    //                     << myQuery.value(0).toString() <<
    //                     myQuery.value(1).toString()
    //                     << myQuery.value(2).toString() <<
    //                     myQuery.value(3).toString();
    //    }

    if (myQuery.next()) {
        QVariant lastValue = myQuery.value(0);
        get_data           = lastValue.toString();
        if (lastValue.isValid()) {
            //            qDebug() << "-i sql single search: " << column << ":" <<
            //            get_data;
        } else {
            qDebug() << "-i sql single search: " << column << "has no valid values.";
        }
    } else {
        //        qDebug() << "-i sql single search: No result returned." << column;
        //        return false;
    }
    return true;
}

bool MyConnSql::queryMaxValue(QSqlDatabase &db, const QString &sqlcmd, QString &get_data) {
    QSqlQuery myQuery(db);
    try {
        if (!myQuery.exec(sqlcmd)) {
            throw QSqlError(myQuery.lastError());
        }
    } catch (QSqlError &e) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        qDebug() << "Exception caught: " << e.text();

        db.close();
        return false;
    }
    //    while(myQuery.next()) {//日期索引有结果
    //        // 判断当前记录是否有效
    ////            if(myQuery.isValid()) {   // && myQuery.last()) {
    //            int row_num = myQuery.at();
    //            //获取每条记录中属性（即列）的个数
    //            int column_num = myQuery.record().count();

    //            if(column_num <= 0) {
    //                qDebug() << "-i sql/ max search " << "row:" << row_num <<
    //                "column:" << column_num;
    //            }
    //            else {
    //                //获取属性的值
    //                QString tr_tmp = myQuery.value(0).toString();
    //                if(tr_tmp != "") get_data = tr_tmp;

    //            }

    //           qDebug() << "-i sql/ " << "row:" << row_num << "column:" <<
    //           column_num << get_data;

    //    }
    if (myQuery.next()) {  // 只处理第一行数据即可
        QString tr_tmp = myQuery.value(0).toString();
        qDebug() << "-i sql/ max search: " << tr_tmp;

        if (!tr_tmp.isEmpty()) {
            get_data = tr_tmp;
        } else
            return false;
    }
    return true;
}

bool MyConnSql::queryNValue(QSqlDatabase &db, const QString &sqlcmd, QMap<QString, QString> &m_get_values) {
    QSqlQuery myQuery(db);
    try {
        if (!myQuery.exec(sqlcmd)) {
            throw QSqlError(myQuery.lastError());
        }
    } catch (QSqlError &e) {
        qDebug() << "Database Error: When excute \"" << sqlcmd << "\"";
        qDebug() << "Exception caught: " << e.text();

        db.close();
        return false;
    }
    uint16_t query_col_num = m_get_values.count();

    while (myQuery.next()) {  //日期索引有结果
        // 判断当前记录是否有效
        //            if(myQuery.isValid()) {   // && myQuery.last()) {
        int row_num = myQuery.at();
        //获取每条记录中属性（即列）的个数
        int column_num = myQuery.record().count();

        //获取属性所在列的编号，列从左向右编号，最左边的编号为0
        int fieldNo;
        //            QMapIterator<QString, QString> iter(m_get_values);
        //            for(;iter.hasNext();) {
        //                fieldNo = myQuery.record().indexOf(iter.key());
        //                iter.value() = myQuery.value(fieldNo).toString();
        //            }
        for (QMap<QString, QString>::iterator it = m_get_values.begin(); it != m_get_values.end(); it++) {
            fieldNo = myQuery.record().indexOf(it.key());
            if (fieldNo < 0) {
                qDebug() << "-i sql/mutil search：" << it.key() << "row:" << row_num << "column:" << column_num << "index" << fieldNo;
            } else {
                //获取属性的值
                QString tr_tmp = myQuery.value(fieldNo).toString();
                if (tr_tmp != "")
                    it.value() = tr_tmp;
            }
        }

        if (column_num > 3)
            qDebug() << "-i sql/mutil search"
                     << "row:" << row_num << "column:" << column_num << myQuery.value(0).toString() << myQuery.value(1).toString()
                     << myQuery.value(2).toString() << myQuery.value(3).toString();
    }
    return true;
}

void MyConnSql::modelChange(QSqlDatabase *model) {
    //    model->setTable(m_tableView_database_->table_name);
    //    //显示model里的数据
    //    model->select();
    //    //设置model的编辑模式，手动提交修改
    //    mode->setEditStrategy(QSqlTableModel::OnManualSubmit);
}
