#!/usr/bin/env python3
"""
统一配置生成器

按照单一原则，统一协调各个专门的配置生成器。
每个配置类型都有专门的生成器，本脚本负责协调调用。
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def run_config_generator(generator_script, project_path, project_type, scripts_base=""):
    """
    运行指定的配置生成器

    Args:
        generator_script: 配置生成器脚本路径
        project_path: 项目路径
        project_type: 项目类型
        scripts_base: 脚本基础路径

    Returns:
        bool: 是否成功
    """
    try:
        cmd = [
            sys.executable, generator_script,
            "--project-path", str(project_path),
            "--project-type", project_type
        ]

        # 检查是否支持 --non-interactive 参数
        script_name = Path(generator_script).name
        non_interactive_supported = [
            "generate_document_links_config.py",
            "generate_workflow_config.py",
            "generate_traceability_config.py"
        ]

        if script_name in non_interactive_supported:
            cmd.append("--non-interactive")

        # 某些生成器需要scripts_base参数
        if "workflow" in generator_script and scripts_base:
            cmd.extend(["--scripts-base", scripts_base])

        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"[X] 运行配置生成器失败: {generator_script}")
        print(f"    返回码: {e.returncode}")
        print(f"    标准输出: {e.stdout}")
        print(f"    错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"[X] 运行配置生成器异常: {generator_script}")
        print(f"    异常: {e}")
        return False


def generate_all_configs(project_path, project_type="single_layer", scripts_base="", config_types=None):
    """
    生成所有配置文件
    
    Args:
        project_path: 项目路径
        project_type: 项目类型
        scripts_base: 脚本基础路径
        config_types: 要生成的配置类型列表，None表示全部
    
    Returns:
        dict: 各配置生成结果
    """
    # 获取当前脚本所在目录
    config_script_dir = Path(__file__).parent
    
    # 各配置生成器映射
    generators = {
        "document_links": {
            "script": config_script_dir / "generate_document_links_config.py",
            "description": "文档关联系统配置"
        },
        "workflow": {
            "script": config_script_dir / "generate_workflow_config.py", 
            "description": "工作流系统配置"
        },
        "traceability": {
            "script": config_script_dir / "generate_traceability_config.py",
            "description": "追溯系统配置"
        },
        "deliverables": {
            "script": config_script_dir / "generate_deliverables_config.py",
            "description": "交付物配置"
        },
        "production": {
            "script": config_script_dir / "generate_production_config.py",
            "description": "生产配置"
        },
        "quality": {
            "script": config_script_dir / "generate_quality_config.py",
            "description": "质量保证配置"
        },
        "development": {
            "script": config_script_dir / "generate_development_config.py",
            "description": "开发配置"
        },
        "design": {
            "script": config_script_dir / "generate_design_config.py",
            "description": "设计配置"
        },
        "requirements_analysis": {
            "script": config_script_dir / "generate_requirements_analysis_config.py",
            "description": "需求分析配置"
        },
        "requirements_import": {
            "script": config_script_dir / "generate_requirements_import_config.py",
            "description": "需求导入配置"
        }
    }
    
    # 确定要生成的配置类型
    if config_types is None:
        config_types = list(generators.keys())
    
    results = {}
    success_count = 0
    
    print(f"[*] 开始生成项目配置...")
    print(f"   项目路径: {project_path}")
    print(f"   项目类型: {project_type}")
    print(f"   配置类型: {', '.join(config_types)}")
    print("")
    
    for config_type in config_types:
        if config_type not in generators:
            print(f"[!] 未知的配置类型: {config_type}")
            results[config_type] = False
            continue
        
        generator_info = generators[config_type]
        script_path = generator_info["script"]
        description = generator_info["description"]
        
        print(f"[-] 生成 {description}...")
        
        if not script_path.exists():
            print(f"[X] 配置生成器不存在: {script_path}")
            results[config_type] = False
            continue
        
        success = run_config_generator(str(script_path), project_path, project_type, scripts_base)
        results[config_type] = success
        
        if success:
            success_count += 1
            print(f"[+] {description} 生成完成")
        else:
            print(f"[X] {description} 生成失败")
        print("")
    
    # 总结
    total_count = len(config_types)
    print("=" * 50)
    print(f"[#] 配置生成总结:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n[OK] 所有配置文件生成完成！")
        print("\n[i] 后续步骤:")
        print("1. 根据项目需要调整各配置文件")
        print("2. 运行文档关联系统进行初始化")
        print("3. 启动工作流和追溯系统")
    else:
        print(f"\n[!] 有 {total_count - success_count} 个配置生成失败，请检查错误信息")
    
    return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一配置生成器')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'],
                       help='项目类型')
    parser.add_argument('--scripts-base', default='', help='脚本基础路径')
    parser.add_argument('--config-types', nargs='*', 
                       choices=['document_links', 'workflow', 'traceability', 'deliverables', 
                               'production', 'quality', 'development', 'design', 
                               'requirements_analysis', 'requirements_import'],
                       help='要生成的配置类型（默认全部）')
    
    args = parser.parse_args()
    
    project_path = Path(args.project_path).resolve()
    
    if not project_path.exists():
        print(f"[X] 项目路径不存在: {project_path}")
        return 1
    
    results = generate_all_configs(
        project_path, 
        args.project_type, 
        args.scripts_base,
        args.config_types
    )
    
    # 返回成功生成的数量
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    if success_count == total_count:
        return 0
    else:
        return 1


if __name__ == "__main__":
    exit(main()) 