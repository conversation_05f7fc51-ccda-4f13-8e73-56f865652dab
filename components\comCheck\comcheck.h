#ifndef COMCHECK_H
#define COMCHECK_H

#include <QMainWindow>
#include "comCheckOperation.h"
#include <QCheckBox>
#include <QLineEdit>
#include <QPushButton>

#include "ITestBoard.h"
#include "IBottom.h"
#include "ITopBoard.h"
#include "IPms.h"
#include "ILoad.h"


namespace Ui {
class CComCheck;
}

class CComCheck : public QMainWindow
{
    Q_OBJECT

public:
    explicit CComCheck(QWidget *parent = nullptr);
    ~CComCheck();


private:
    typedef struct {
        QCheckBox* is_hex_check_; //
        QLineEdit* line_cmd_;
        QPushButton* send_btn_;
        QLineEdit* priority_;
        QLineEdit* interval_time_;
    } StCmdView;

    Ui::CComCheck *ui;
    NComCheck::StUiConfig *mst_config_ = nullptr;
    CComCheckOpt *mc_operation_ = nullptr;

    ILoad *mi_load_ = nullptr;

    //* get cmd
    QMap<QString, QByteArray> *mst_cmd_list_ = nullptr;

    StCmdView *mst_cmd_view_ = nullptr;
    QVector<StCmdView*> mv_cmd_view;

    QMap<QString, QString> * mst_ack_parse_ = nullptr;


    virtual void closeEvent( QCloseEvent *event) override;

    //* ui config
    void updateConfig(NComCheck::StUiConfig *st_config_);

    //*
    void cmdListShowUpdate();

    //*
    void resultClean(void);

signals:
    void comCheckCloseSiganl(bool);

    void cmdModifySignal(const uint8_t index, QByteArray cmd);

private slots:
    void portListShow_slot(QStringList *port_list_, bool port_flag);

    void processStatusShow_slot(const int16_t &step, const int16_t &status);

    void addCmdView_slot();

    void cmdListShow_slot();

    void sendedCmdShow_slot(QByteArray cmd);

    void ackedCmdShow_slot(QByteArray cmd);

//    void interationCmdShow_slot();

    void resultShow_slot();

    void cmdPrasedShow_slow();

    void dataParsingShow_slot();
    void on_device_select_currentIndexChanged(int index);
    void on_port_select_currentIndexChanged(int index);
    void on_baud_select_currentIndexChanged(int index);
    void on_start_btn_clicked();
    void on_actionImport_triggered();
};

#endif // COMCHECK_H
