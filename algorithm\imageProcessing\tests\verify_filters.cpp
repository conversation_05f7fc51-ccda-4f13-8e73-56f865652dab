#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include "../common/ImageData.h"
#include "../filters/ConvolutionFilter.h"
#include "../filters/WeightedAverageFilter.h"
#include "../filters/MedianFilter.h"
#include "../filters/GaussianFilter.h"
#include "../filters/BilateralFilter.h"
#include "../filters/KalmanFilter.h"

using namespace ImageProcessing;

/**
 * @brief 创建5x5测试数据
 */
ImageDataU32 create5x5TestData() {
    ImageDataU32 data(5, 5);
    
    QVector<QVector<uint32_t>> testMatrix = {
        {271, 882, 826, 748, 58},
        {1011, 908, 792, 756, 738},
        {1074, 924, 807, 800, 859},
        {1021, 877, 777, 776, 855},
        {145, 887, 788, 740, 33}
    };
    
    for (uint32_t y = 0; y < 5; ++y) {
        for (uint32_t x = 0; x < 5; ++x) {
            data.matrix()[y][x] = testMatrix[y][x];
        }
    }
    
    return data;
}

/**
 * @brief 打印图像数据
 */
void printImageData(const ImageDataU32& data, const QString& title) {
    qDebug() << title << ":";
    for (uint32_t y = 0; y < data.height(); ++y) {
        QString row;
        for (uint32_t x = 0; x < data.width(); ++x) {
            row += QString("%1").arg(data.matrix()[y][x], 4);
            if (x < data.width() - 1) row += ", ";
        }
        qDebug() << "  " << row;
    }
}

/**
 * @brief 测试卷积滤波器
 */
void testConvolutionFilter() {
    qDebug() << "\n=== 测试卷积滤波器 ===";
    
    ImageDataU32 testData = create5x5TestData();
    printImageData(testData, "原始数据");
    
    ConvolutionFilter filter;
    filter.setPredefinedKernel("sharpen");
    
    ImageDataU32 result = testData;
    bool success = filter.apply(result);
    
    if (success) {
        printImageData(result, "卷积滤波结果");
        qDebug() << "中心点(2,2)：原始值=" << testData.matrix()[2][2] 
                 << "，滤波后=" << result.matrix()[2][2];
    } else {
        qDebug() << "卷积滤波失败";
    }
}

/**
 * @brief 测试加权均值滤波器
 */
void testWeightedAverageFilter() {
    qDebug() << "\n=== 测试加权均值滤波器 ===";
    
    ImageDataU32 testData = create5x5TestData();
    
    WeightedAverageFilter filter;
    filter.setPredefinedWeights("uniform");
    
    ImageDataU32 result = testData;
    bool success = filter.apply(result);
    
    if (success) {
        printImageData(result, "加权均值滤波结果(uniform)");
    } else {
        qDebug() << "加权均值滤波失败";
    }
}

/**
 * @brief 测试中值滤波器
 */
void testMedianFilter() {
    qDebug() << "\n=== 测试中值滤波器 ===";
    
    ImageDataU32 testData = create5x5TestData();
    
    MedianFilter filter;
    filter.setPreset("noise_reduction");
    
    ImageDataU32 result = testData;
    bool success = filter.apply(result);
    
    if (success) {
        printImageData(result, "中值滤波结果");
    } else {
        qDebug() << "中值滤波失败";
    }
}

/**
 * @brief 测试高斯滤波器
 */
void testGaussianFilter() {
    qDebug() << "\n=== 测试高斯滤波器 ===";
    
    ImageDataU32 testData = create5x5TestData();
    
    GaussianFilter filter;
    filter.setPreset("medium_blur");
    
    ImageDataU32 result = testData;
    bool success = filter.apply(result);
    
    if (success) {
        printImageData(result, "高斯滤波结果");
    } else {
        qDebug() << "高斯滤波失败";
    }
}

/**
 * @brief 测试双边滤波器
 */
void testBilateralFilter() {
    qDebug() << "\n=== 测试双边滤波器 ===";
    
    ImageDataU32 testData = create5x5TestData();
    
    BilateralFilter filter;
    filter.setPreset("smooth");
    
    ImageDataU32 result = testData;
    bool success = filter.apply(result);
    
    if (success) {
        printImageData(result, "双边滤波结果");
    } else {
        qDebug() << "双边滤波失败";
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "========================================";
    qDebug() << "图像处理滤波器实际测试验证";
    qDebug() << "========================================";
    
    try {
        testConvolutionFilter();
        testWeightedAverageFilter();
        testMedianFilter();
        testGaussianFilter();
        testBilateralFilter();
        
        qDebug() << "\n========================================";
        qDebug() << "所有测试完成";
        qDebug() << "========================================";
        
    } catch (const std::exception& e) {
        qDebug() << "测试异常:" << e.what();
        return 1;
    }
    
    return 0;
}
