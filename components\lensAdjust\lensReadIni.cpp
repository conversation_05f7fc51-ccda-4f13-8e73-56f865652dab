#include "lensReadIni.h"


ClensReadIni *ClensReadIni::instance = NULL;


ClensReadIni *ClensReadIni::getInstance() {
    if (instance == NULL) {
        instance = new ClensReadIni();
    }
    return instance;
}


ClensReadIni::ClensReadIni() {
    this->readIni();
}


void ClensReadIni::readIni() {
    QSettings *configIniRead_ = new QSettings("config/clen_config.ini", QSettings::IniFormat);  //初始化读取Ini文件对象

    //    configIniRead_->beginGroup("SENSOR_BOARD");
    //    iniConfig.version = configIniRead_->value("version").toString();
    iniConfig.version             = configIniRead_->value("SENSOR_BOARD/version").toString();
    iniConfig.userid              = "U" + configIniRead_->value("MES/userid").toString();
    iniConfig.op                  = configIniRead_->value("MES/op").toString();
    iniConfig.work_number         = configIniRead_->value("MES/work_number").toString();
    iniConfig.work_domain         = configIniRead_->value("MES/work_domain").toString();
    iniConfig.sensor_device       = configIniRead_->value("DEVICE/sensor_device").toInt();
    iniConfig.sensor_device_buad  = configIniRead_->value("DEVICE/sensor_device_baud").toUInt();
    iniConfig.station_number      = configIniRead_->value("DEVICE/station_number").toUInt();
    iniConfig.clens_machine_brand = configIniRead_->value("DEVICE/clens_machine_brand").toUInt();
    iniConfig.facula_center_loc_x = configIniRead_->value("ADJUST_PARAM/facula_center_loc_x").toUInt();
    iniConfig.facula_center_loc_y = configIniRead_->value("ADJUST_PARAM/facula_center_loc_y").toUInt();
    iniConfig.facula_ok_times     = configIniRead_->value("ADJUST_PARAM/facula_ok_time").toUInt();
    iniConfig.solid_time          = configIniRead_->value("ADJUST_PARAM/solid_time").toUInt();
    iniConfig.facula_ng_handle    = configIniRead_->value("ADJUST_PARAM/facula_ng_handle").toUInt();

    // 读取光斑处理配置，如果不存在则使用默认值
    // iniConfig.interpolation_type      = configIniRead_->value("FACULA_PROCESSING/interpolation_type", 0).toUInt();
    // iniConfig.filter_types            = configIniRead_->value("FACULA_PROCESSING/filter_types", "2").toString();
    // iniConfig.interpolation_offset    = configIniRead_->value("FACULA_PROCESSING/interpolation_offset", 0.5).toFloat();
    // iniConfig.kalman_strength         = configIniRead_->value("FACULA_PROCESSING/kalman_strength", 1.0).toFloat();
    // iniConfig.convolution_kernel_size = configIniRead_->value("FACULA_PROCESSING/convolution_kernel_size", 3).toUInt();

    iniConfig.facula_handle_type = configIniRead_->value("FACULA_HANDLE/facula_handle_type").toUInt();

    delete configIniRead_;
}


const ClensReadIni::IniConfig &ClensReadIni::getIniConfig() {
    return iniConfig;
}
