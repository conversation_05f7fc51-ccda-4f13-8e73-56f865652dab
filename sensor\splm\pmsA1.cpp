#include "pmsA1.h"
#include <QMetaEnum>

#include "simpleInteractionP.h"

#include "checkCalculate.h"
#include "loadXml.h"
#include "qLog.h"
#include "typeConvert.h"


// CPmsA1::CPmsA1(IComm *port_):
//    mi_port_(port_)
//  , mst_data_receive_(new CNovaP::StInteractionFrame)
//{
//    mi_load_ = new CLoadXml;
//    m_protocol_ = new CNovaP;
//    mi_spms_soc_ = new CSpmsVi5300;

//    //* 此处应该可以选择数据接收方式
//    mi_port_->setBufferSize(2000); //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

//    //********************************** varibles init **********************************
//    //* read cmd from loc file
//    QString filename = QApplication::applicationDirPath() + "/cmdList/pmsA1.xml";
//    mi_load_->readParam(filename, &m_interaction_cmd);

//    QString configFileName = QApplication::applicationDirPath() + "/config/novaA1_config.xml";
//    mi_load_->readParam(configFileName, &mm_calib_param);


//    QMapIterator<ISpmsSoc::ECalibProcess, QString> m_iter(mi_spms_soc_->mm_calib_flow);
//    uint calib_task_id = 0;
////    QMapIterator<QString, ISpmsSoc::StTaskInfo> m_iter(mi_spms_soc_->mm_calib_flow);
//    while (m_iter.hasNext()) {
//        m_iter.next();

//        StCalibTask calib_task_tmp;

//        //* 枚举转字符
//        QMetaEnum stepEnum = QMetaEnum::fromType<ISpmsSoc::ECalibProcess>(); //
//        QString step_str = stepEnum.valueToKey((uint8_t)m_iter.key());

//        if(m_interaction_cmd.contains(step_str)) {
//            //m_iter.value().cmd = m_interaction_cmd[""];//(m_interaction_cmd.value(m_iter.key()));

//            //m_calib_cmd.insert(m_iter.key(), ); //添加 cmd
//            calib_task_tmp.cmd = m_interaction_cmd.value(step_str);
//        }
//        else {
//            qDebug() << "/pmsA1 -e: calib step " << step_str << " dont have local cmd";
//        }

//        calib_task_tmp.calib_items = mv_calib_items.at(calib_task_id);

//        mm_calib_flow.insert(m_iter.key(), calib_task_tmp);

//        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction(&CPmsA1::boardRotate, &CPmsA1::boardRotateAck, 0, 0));
//        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction((pmsA1Fptr_)mv_calib_func.at(calib_task_id).ptr_,
//        (pmsA1Fptr_)mv_calib_func.at(calib_task_id).ack_ptr_, 0, 0)); //

//        calib_task_id++;
//    }

//    //QString pms_soc_name = typeid (*mi_spms_soc_).name();


//    //* verify items standard values
//    //* accuracy standard


//    //* test items


////    StTestItem item_tmp;

////    m_test_items.append(item_tmp);


//    //* standart values
//#ifdef INIT_OUTPUT
//    qDebug() << "-i pmsA1/ init: --------------------";
//    qDebug() << "-i sensorC/ start cmd: " << m_interaction_cmd["start"];
//    qDebug() << "-i sensorC/ stop cmd: " << m_interaction_cmd["stop"];
//#endif
//}

CPmsA1::CPmsA1(IComm *port_, IComm *motor_port_)
    : mi_port_(port_),
      mi_motor_port_(motor_port_),
      mc_calib_process_(new TCalib_process),
      mst_calib_task_status_(new TCalib_process::StStepRecord),
      mc_function_process_(new TFunction_process),
      mst_function_task_status_(new TFunction_process::StStepRecord),
      m_cache_buff_(new QByteArray),
      mst_comm_status_(new StCommunicateStatus),
      mst_data_receive_(new CSimpleInteraction::StInteractionFrame),
      mst_dist_(new CNovaP::StDataFrame) {
    mi_load_            = new CLoadXml;
    m_protocol_         = new CNovaP;
    m_general_protocol_ = new CSimpleInteraction;
    mi_spms_soc_        = new CSpmsVi5300;

    //* 此处应该可以选择数据接收方式
    mi_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

    //********************************** varibles init **********************************

    //* read cmd from loc file
    QString filename = QApplication::applicationDirPath() + "/cmdList/nova_proj_cmd.xml";
    mi_load_->readParam(filename, &m_interaction_cmd);
    cmd_init();


    QString configFileName = QApplication::applicationDirPath() + "/config/novaA1_config.xml";
    mi_load_->readParam(configFileName, &mm_calib_param);

    qRegisterMetaType<CPmsA1::EProtocolId>("CPmsA1::EProtocolId");

    //************************************* task init ****************************************

    //***************** calib tasks ********
    QMapIterator<ISpmsSoc::ECalibProcess, QString> m_iter(mi_spms_soc_->mm_calib_flow);
    uint                                           calib_task_id = 0;
    //    QMapIterator<QString, ISpmsSoc::StTaskInfo> m_iter(mi_spms_soc_->mm_calib_flow);
    while (m_iter.hasNext()) {
        m_iter.next();

        StCalibTask calib_task_tmp;

        //* 枚举转字符
        QMetaEnum stepEnum = QMetaEnum::fromType<ISpmsSoc::ECalibProcess>();  //
        QString   step_str = stepEnum.valueToKey((int8_t)m_iter.key());

        if (m_interaction_cmd.contains(step_str)) {
            // m_iter.value().cmd = m_interaction_cmd[""];//(m_interaction_cmd.value(m_iter.key()));

            // m_calib_cmd.insert(m_iter.key(), ); //添加 cmd
            calib_task_tmp.cmd = m_interaction_cmd.value(step_str);
            qDebug() << "-i pmsA1 cmd:/" << calib_task_tmp.cmd;
        } else {
            qDebug() << "/pmsA1 -e: calib step " << step_str << " dont have local cmd";
        }

        calib_task_tmp.calib_items = mv_calib_items.at(calib_task_id);

        mm_calib_flow.insert(m_iter.key(), calib_task_tmp);

        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction(&CPmsA1::boardRotate, &CPmsA1::boardRotateAck, 0, 0));
        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction(
            (pmsA1Fptr_)mv_calib_func.at(calib_task_id).ptr_, (pmsA1Fptr_)mv_calib_func.at(calib_task_id).ack_ptr_, 0, 0));  //

        calib_task_id++;
    }

    mc_calib_process_->taskInit(&mv_calib_task_list, mst_calib_task_status_);


    // QString pms_soc_name = typeid (*mi_spms_soc_).name();

    //* verify items standard values
    //* accuracy standard


    //********************* function test tasks ********
    mv_function_task_list.append(mc_function_process_->addCallbackFunction(&CPmsA1::trigRotate, &CPmsA1::trigRotateAck, 0, 0));
    mv_function_task_list.append(mc_function_process_->addCallbackFunction(&CPmsA1::triggerTest, &CPmsA1::triggerTestAck, 0, 0));  //
    mv_function_task_list.append(mc_function_process_->addCallbackFunction(&CPmsA1::emptyRotate, &CPmsA1::emptyRotateAck, 0, 0));  //
    mv_function_task_list.append(mc_function_process_->addCallbackFunction(&CPmsA1::emptyTest, &CPmsA1::emptyTestAck, 0, 0));      //

    mc_function_process_->taskInit(&mv_function_task_list, mst_function_task_status_);

    //    StTestItem item_tmp;

    //    m_test_items.append(item_tmp);


    //* standart values
#ifdef INIT_OUTPUT
    qDebug() << "-i pmsA1/ init: --------------------";
    qDebug() << "-i pmsA1/ start cmd: " << m_interaction_cmd["start"];
#endif
}

CPmsA1::~CPmsA1() {

    delete mc_calib_process_;
    delete mst_calib_task_status_;
    delete mc_function_process_;
    delete mst_function_task_status_;
    delete m_cache_buff_;
    delete mst_comm_status_;
    delete mst_data_receive_;
    delete mst_dist_;

    if (mi_load_ != nullptr)
        delete mi_load_;
    if (m_protocol_ != nullptr)
        delete m_protocol_;
    if (m_general_protocol_ != nullptr)
        delete m_general_protocol_;
    if (mi_spms_soc_ != nullptr)
        delete mi_spms_soc_;
}

/**
 * @brief 固定指令直接初始化，有变化指令用成员变量，只初始化固定部分
 *
 */
void CPmsA1::cmd_init(void) {
    uint16_t data;

    //********************************* 固定指令初始化 *******************

    //* 1. load cmd from xml, if cmd is empty, init it
    QMetaEnum                 stepEnum = QMetaEnum::fromType<CPmsA1::EProtocolId>();
    QString                   step_str;
    QMap<QString, QByteArray> init_cmd;

    //* control cmd
    //    for(uint8_t for_i = (uint8_t)eCALIB1; for_i < uint8_t(eLOG_OUTPUT); for_i++) {
    //        step_str = stepEnum.valueToKey(for_i);

    //        init_cmd.insert(step_str, m_protocol_->getWriteCmd(for_i, ));
    //    }
    init_cmd.insert(stepEnum.valueToKey((uint8_t)eCALIB1), m_general_protocol_->getControlCmd((uint8_t)eCALIB1));
    init_cmd.insert(stepEnum.valueToKey((uint8_t)eCALIB2), m_general_protocol_->getControlCmd((uint8_t)eCALIB2));
    init_cmd.insert(stepEnum.valueToKey((uint8_t)eCALIB3), m_general_protocol_->getControlCmd((uint8_t)eCALIB3));
    init_cmd.insert(stepEnum.valueToKey((uint8_t)eCALIB4), m_general_protocol_->getControlCmd((uint8_t)eCALIB4));

    //* write cmd
    init_cmd.insert(stepEnum.valueToKey((uint8_t)eMODE_CHANGE),
                    m_general_protocol_->getWriteCmd((uint8_t)eMODE_CHANGE, NsTypeConvert::enumToByteArray(static_cast<uint8_t>(eInteraction_mode))));
    init_cmd.insert(stepEnum.valueToKey((uint8_t)eDATA_OUTPUT),
                    m_general_protocol_->getWriteCmd((uint8_t)eDATA_OUTPUT, NsTypeConvert::enumToByteArray(static_cast<uint8_t>(eAll_info))));

    //* read cmd
    QByteArray r_data;
    for (uint8_t for_i = (uint8_t)eREAD_VERSION; for_i < (uint8_t)eCHIP_ID; for_i++) {
        init_cmd.insert(stepEnum.valueToKey(for_i), m_general_protocol_->getReadCmd(for_i, r_data));
    }

    for (QMap<QString, QByteArray>::iterator iter = init_cmd.begin(); iter != init_cmd.end(); iter++) {
        if (m_interaction_cmd.contains(iter.key())) {  // if xml contain that cmd
            if (m_interaction_cmd.value(iter.key()).size() == 0) {
                m_interaction_cmd.insert(iter.key(), iter.value());
            }
        } else {  // add
            m_interaction_cmd.insert(iter.key(), iter.value());
        }
    }

    //* 2. update cmd to xml
    QString filename = QApplication::applicationDirPath() + "/cmdList/nova_proj_cmd.xml";
    mi_load_->writeParam(filename, "project_cmd", "nova-A1", &m_interaction_cmd);


#ifdef COMM_OUTPUT
    qDebug() << "-i nova-A1 cmd/ eCALIB1: " << m_interaction_cmd["eCALIB1"];
#endif

    //******************变化指令初始化********************
}

QByteArray CPmsA1::portDataRead() {
    return mi_port_->read(5);
}

/**
 * @brief icom_change_interface
 * @param port_
 */
void CPmsA1::icom_change_interface(IComm *port_) {
    mi_port_ = port_;
}

QMap<QString, QByteArray> *CPmsA1::getCmdList() {
    return &m_interaction_cmd;
}

bool CPmsA1::writeCmdList(const QMap<QString, QByteArray> &cmd_list) {
    ;
}

#if 0
/****************
 * 对于同一类型有多个选项，采用直接写入选项的方式
 * 1. 枚举切换方式：增加了枚举变量，而且添加模式会改动现有函数
 * 2. 每个模式对应一个函数：单一性，但函数会过多
 * *******************************/
/**
 * @brief 切换模式
 * @return
 */
bool CPmsJamooz::modeChange(const EModeType &mode)
{
    bool flag = false;
    switch (mode) {
    case eSINGLE_MODE:  break;
    case eCALI_MODE: flag = mi_port_->write(m_interaction_cmd["caliMode"]); break;
    case eGREY_MODE: flag = mi_port_->write(m_interaction_cmd["caliMode"]); break;
    case eCLOUD_MODE: flag = mi_port_->write(m_interaction_cmd["caliMode"]); break;
    default: flag = false; break;

    }
    return flag;
}
#else
/**
 * @brief 直接写入模式
 * @param mode
 * @return
 */
bool CPmsA1::modeChange(const uint16_t &mode) {
    m_strPre.clear();
    //  return mi_port_->write(m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(mode))); //
}
#endif
/**
 * @brief 读信息
 * @return
 */
bool CPmsA1::readInfo(const uint8_t &id, const uint16_t &data) {
    QByteArray cmd = m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data));
    m_strPre.clear();
    return mi_port_->write(m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data)));  //
}

/**
 * @brief CPmsJamooz::changeRigster
 * @return
 */
bool CPmsA1::changeRigster(){

};

EExecStatus CPmsA1::boardRotate() {
    mi_motor_port_->write(0);

    return eOK;
}

EExecStatus CPmsA1::boardRotateAck() {

    return eCOMP;
}

EExecStatus CPmsA1::calibTask1() {
    if (!mi_port_->checkPort())
        mi_port_->openPort();

    if (mi_port_->write(mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).cmd)) {
        // qDebug() << "-i uart/ write data:" << NsTypeConvert::byteArrayToString(mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).cmd);
        // m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
    // return mi_spms_soc_->calibTask1();
}

EExecStatus CPmsA1::calibTask1Ack() {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        uint16_t param1 = mst_data_receive_->data[0] + (mst_data_receive_->data[1] << 8);
        int16_t  param2 = mst_data_receive_->data[2] + (mst_data_receive_->data[3] << 8);
        //      m_serial_thread_->task_id_change(4);
        //      mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param1_value = mst_data_receive_->data;
        //      mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param2_value = ;

        emit calibValueAckSignal(1, param1, (uint16_t)param2);
        qDebug() << "calib xtalk/ peak:" << param1 << "tof:" << param2;

        return eWAIT;
        //        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
    // return mi_spms_soc_->calibTask1Ack();
}

EExecStatus CPmsA1::calibTask2() {
    if (mi_port_->write(mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB2).cmd)) {
        // m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}

EExecStatus CPmsA1::calibTask2Ack() {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        //        mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param1_value = ;
        uint16_t param1 = mst_data_receive_->data[0] + (mst_data_receive_->data[1] << 8);
        int16_t  param2 = mst_data_receive_->data[2] + (mst_data_receive_->data[3] << 8);
        //      m_serial_thread_->task_id_change(4);
        //      mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param1_value = mst_data_receive_->data;
        //      mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param2_value = ;

        emit calibValueAckSignal(2, param1, (uint16_t)param2);

        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

EExecStatus CPmsA1::calibTask3() {
    return mi_spms_soc_->calibTask3();
}

EExecStatus CPmsA1::calibTask3Ack() {
    return mi_spms_soc_->calibTask3Ack();
}

EExecStatus CPmsA1::calibTask4() {
    return mi_spms_soc_->calibTask4();
}

EExecStatus CPmsA1::calibTask4Ack() {
    return mi_spms_soc_->calibTask4Ack();
}

EExecStatus CPmsA1::calibTasksRun() {
    EExecStatus status = mc_calib_process_->tasksRun(this, &mv_calib_task_list, mst_calib_task_status_);
    if (status == eCOMP) {
        if ((uint8_t)mst_calib_task_status_->cur_step + 1 == mv_calib_task_list.size()) {  // last step
            return eCOMP;
        } else {
            return eERROR;
        }
    } else {
        return eERROR;
    }
}

EExecStatus CPmsA1::calibSingleTaskRun(ISpmsSoc::ECalibProcess calib_step) {
    switch (calib_step) {
    case ISpmsSoc::ECalibProcess::eCALIB1:
        mv_calib_task_list[ECalibProcess::eCALIB1_STEP].flag.exec = true;
        break;
    case ISpmsSoc::ECalibProcess::eCALIB2:
        mv_calib_task_list[ECalibProcess::eCALIB2_STEP].flag.exec = true;
        break;
    case ISpmsSoc::ECalibProcess::eCALIB3:
        mv_calib_task_list[ECalibProcess::eCALIB3_STEP].flag.exec = true;
        break;
    case ISpmsSoc::ECalibProcess::eCALIB4:
        mv_calib_task_list[ECalibProcess::eCALIB4_STEP].flag.exec = true;
        break;
    default:
        break;
    }
}

//* accuracyVerify items
EExecStatus CPmsA1::checkRotate() {
}

EExecStatus CPmsA1::checkRotateAck() {
}

EExecStatus CPmsA1::distCheck() {
}

EExecStatus CPmsA1::distCheckAck() {
}

EExecStatus CPmsA1::verifyTasksRun(void) {
    return mc_calib_process_->tasksRun(this, &mv_calib_task_list, mst_calib_task_status_);
}


//* work mode items
EExecStatus CPmsA1::trigRotate(void) {
}

EExecStatus CPmsA1::trigRotateAck(void) {
}

EExecStatus CPmsA1::triggerTest(void) {
}

EExecStatus CPmsA1::triggerTestAck(void) {
}

EExecStatus CPmsA1::emptyRotate(void) {
}

EExecStatus CPmsA1::emptyRotateAck(void) {
}

EExecStatus CPmsA1::emptyTest(void) {
    //* 自动上送，不用指令

    return eOK;
}

EExecStatus CPmsA1::emptyTestAck(void) {
    static uint trigger_cnt = 0;

    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        int16_t dist     = mst_dist_->distance;
        int16_t ref_dist = mst_dist_->ref_dist;
        //      mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param1_value = mst_data_receive_->data;
        //      mm_calib_flow.value(ISpmsSoc::ECalibProcess::eCALIB1).calib_items.param2_value = ;

        if (mst_dist_->signal != 0) {
            trigger_cnt++;
            emit triggerCntSignal(trigger_cnt, dist, ref_dist);
            qDebug() << "-e pmsA1/" << QString(QDateTime::currentDateTime().toString("yyyy-MM-dd-hh:mm:ss")) << "trigger cnt: " << trigger_cnt
                     << "dist: " << dist << "ref_dist: " << ref_dist;
        }
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief CPmsA1::functionTestRun
 * @return
 */
EExecStatus CPmsA1::functionTestRun(void) {
    //*
    EExecStatus status = mc_function_process_->tasksRun(this, &mv_function_task_list, mst_function_task_status_);
    if (status == eCOMP) {
        if ((uint8_t)mst_function_task_status_->cur_step + 1 == mv_function_task_list.size()) {  // last step
            return eCOMP;
        } else {
            return eERROR;  //
        }
    } else {
        return eERROR;  //
    }
}

/**
 * @brief 交互指令解析
 * @param str
 * @param length
 * @return
 */
bool CPmsA1::interactionParsing(QByteArray str, int length) {
    Q_UNUSED(length);
    if (str.length() == 0)
        return false;

    QByteArray strSum = m_strPre + str;
    if (strSum.length() < (uint16_t)CNovaP::EInteractionFrame::eHEADER_LEN) {  //固定部分长度6
        m_strPre = strSum;
        return false;
    }
#ifdef COMM_ACK
    qDebug() << "-i pmsA1/ interaction ack:" << NsTypeConvert::byteArrayToString(strSum);
    int i = 0;
#endif
    /*1.1 parse*/
    for (;;) {  //留6个数
        if (strSum.length() >= (uint16_t)CNovaP::EInteractionFrame::eHEADER_LEN) {
            if ((uchar)strSum.at(0) == ((uint16_t)CNovaP::EInteractionFrame::eHEADER >> 8) &&
                (uchar)strSum.at(1) == ((uint16_t)CNovaP::EInteractionFrame::eHEADER &
                                        0xff)) {  //帧头  && ((uchar)strSum.at(i + 1) == (CNovaP::kD2H | CNovaP::kHRS | CNovaP::kHSS))
                //*
                // CNovaP::StInteractionFrame* data_receive_ = new CNovaP::StInteractionFrame;
                uint8_t data_num = strSum.at(5);

                uint8_t frame_num = (data_num + (uint8_t)CNovaP::EInteractionFrame::eHEADER_LEN);
                if (strSum.length() >= frame_num) {  //数量符合

                    //*
                    //                    mst_data_receive_->data.resize(data_num);
                    //                    memcmp(&array_tmp, &strSum, (uint8_t)CNovaP::EInteractionFrame::eHEADER_LEN);
                    //                    mst_data_receive_ = reinterpret_cast<CNovaP::StInteractionFrame *>(strSum.data());

                    //                    QByteArray array_tmp;
                    //                  data_receive_->data.resize(num);
                    //                  data_receive_ = reinterpret_cast<CNovaP::StFrame*>(array_tmp.data()); //qbytearray 无法 copy


                    //* check sum
                    uint8_t check_xor = CCheckCalculate::calc_checkXor(strSum, frame_num, 4);

                    m_cache_buff_->clear();  //此处可能有数据过快清楚的问题
                    m_cache_buff_->append(strSum, frame_num);
                    //                    memcpy(m_cache_buff_, strSum.data(), frame_num);
                    mst_data_receive_ = (CSimpleInteraction::StInteractionFrame *)m_cache_buff_->data();

                    if (check_xor == mst_data_receive_->checkXor) {
                        mst_comm_status_->comm_status = eCOMM_COMP;
                        switch ((EProtocolId)mst_data_receive_->id) {
                        case EProtocolId::eCALIB1:
                            qDebug() << "calib xtalk/ eCALIB1:";
                            mv_calib_task_list[ECalibProcess::eCALIB1_STEP].flag.stop = true;
                            //                            emit dataOutput(ECommStep::eCHIP_ID, ECommStatus::eCOMM_COMP, strTmp);
                            break;
                        case EProtocolId::eCALIB2:
                            mv_calib_task_list[ECalibProcess::eCALIB2_STEP].flag.stop = true;
                            //                            emit dataOutput(ECommStep::eMODE_CHANGE, ECommStatus::eCOMM_COMP, strTmp);
                            break;
                        case EProtocolId::eCALIB3:
                            break;
                        case EProtocolId::eCALIB4:
                            break;
                        case EProtocolId::eREAD_VERSION:
                            break;
                        case EProtocolId::eCHIP_ID:
                            break;
                        case EProtocolId::eMODE_CHANGE:
                            break;
                        default:
                            break;
                        }

                        strSum.remove(0, frame_num);
                        //                        m_strPre.clear();
                        //                        m_strPre.push_back(strSum); //存储剩余数据
                        //                        return true;
                    }
                } else {
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
            }
            strSum.remove(0, 1);
        } else {
            m_strPre.clear();
            m_strPre.push_back(strSum);  //存储剩余数据
            return false;
        }
    }
}

/**
 * @brief dtof 灰度图解析 迭代
 */
bool CPmsA1::dataParsing(QByteArray str, int length) {
    if (length == 0)
        return false;

    Q_UNUSED(length);
    if (str.length() == 0)
        return false;

    QByteArray strSum = m_strPre + str;
    if (strSum.length() < (uint16_t)CNovaP::EDataInfoFrame::eHEADER_LEN) {  //固定部分长度6
        m_strPre = strSum;
        return false;
    }

    //    qDebug() << "-i pmsA1/ data ack:" << strSum;
    int i = 0;
    /*1.1 parse*/
    for (;;) {  //留6个数
        if (strSum.length() >= (uint16_t)CNovaP::EDataInfoFrame::eHEADER_LEN) {
            if ((uchar)strSum.at(0) == ((uint16_t)CNovaP::EDataInfoFrame::eHEADER >> 8) &&
                (uchar)strSum.at(1) ==
                    ((uint16_t)CNovaP::EDataInfoFrame::eHEADER & 0xff)) {  //帧头  && ((uchar)strSum.at(i + 1) == (CNovaP::kD2H | CNovaP::kHRS | CNovaP::kHSS))
                //*
                // CNovaP::StInteractionFrame* data_receive_ = new CNovaP::StInteractionFrame;
                uint8_t data_num = strSum.at(3);

                uint8_t frame_num = (data_num + (uint8_t)CNovaP::EDataInfoFrame::eHEADER_LEN);
                if (strSum.length() >= frame_num) {  //数量符合
                    //* check sum
                    uint8_t check_sum = CCheckCalculate::calc_checksum(strSum, frame_num - 1);

                    m_cache_buff_->clear();  //此处可能有数据过快 clear 的问题
                    m_cache_buff_->append(strSum, frame_num);
                    //                    memcpy(m_cache_buff_, strSum.data(), frame_num);
                    mst_dist_ = (CNovaP::StDataFrame *)m_cache_buff_->data();

                    if (check_sum == mst_dist_->check_sum) {
                        mst_comm_status_->comm_status = eCOMM_COMP;

                        mv_function_task_list[(uint8_t)EFunctionProcess::eEMPTE_TEST].flag.stop = true;

                        strSum.remove(0, frame_num);
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                    }
                } else {
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
            }
            strSum.remove(0, 1);
        } else {
            m_strPre.clear();
            m_strPre.push_back(strSum);  //存储剩余数据
            return false;
        }
    }
}

void CPmsA1::cmdUpdate(const uint8_t &cmd_id, const QString &cmd_name, const QByteArray &cmd) {
}
