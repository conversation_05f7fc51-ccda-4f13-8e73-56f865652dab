# 本地mcp servers开发

本地mcp server开发文档

## 原则

- **奥卡姆剃刀原则**：选择最简单有效的实现方案
- **单一职责原则**：每个工具专注于单一功能，避免功能重叠
- **单一来源原则**：删除重复内容
- **双链链接原则**：使用[[]]语法建立文档间关联

## 架构

本次重构采用FastMCP原生架构，结合智能参数处理系统：

- **主服务器文件**: `server.py`
- **架构特点**: FastMCP原生实现 + 智能参数处理 + 业务流程组织
- **脚本包装模式**：根据[[mcp-server_create_guide]]中的架构，MCP服务器通过subprocess调用[[build-tools/scripts/]]中的本地脚本，底层scripts目录的脚本实现完全不受影响，详细的包装脚本和实现效果信息请参考 [[产品体系构建工具关联表]]。无内置逻辑，为找到本地脚本则直接报错
- **MCP协议遵循**：严格遵循MCP协议的返回值格式和错误处理
- **智能参数处理**: 三层处理机制：缓存 → 智能默认值 → 用户交互 `tools/fastmcp_param_utils.py`
- **业务流程导向**: 按照产品开发的实际业务流程组织工具
- **超时处理**：不能出现卡住死循环情况

## 工具实现清单

### server 

F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\server.py

### 工具列表

> 本文档仅提供MCP工具函数与关联表的映射关系
> 只包含[[产品体系构建工具关联表]]中选择工具选择本地mcp server的工具

### 项目生命周期流程 (Project Lifecycle)

| MCP工具函数            | 工具名称   | 业务流程模块               | 关联表期望脚本路径                                    | MCP工具实际查找路径                    | 脚本状态 |
| ------------------ | ------ | -------------------- | -------------------------------------------- | ----------------------------- | ---- |
| `get_project_info` | 项目信息管理 | project_lifecycle.py | 调用项目信息管理脚本                                 | `scripts/get_project_info.py` | ❌不存在 |
| `init_project`     | 项目初始化  | project_lifecycle.py | `scripts/init_product_project.py`           | `scripts/init_product_project.py` | ✅存在  |

### 需求管理流程 (Requirements Flow)

| MCP工具函数                      | 工具名称        | 业务流程模块               | 脚本路径引用                                    |
| ---------------------------- | ----------- | -------------------- | ----------------------------------------- |
| `import_requirements`        | 市场需求收集      | requirements_flow.py | 参见[[产品体系构建工具关联表]]中T01行Scripts链接列     |
| `analyze_requirements`       | 技术需求整理/需求分解 | requirements_flow.py | 参见[[产品体系构建工具关联表]]中T03/T04行Scripts链接列 |
| `create_requirements_matrix` | 需求矩阵维护      | requirements_flow.py | 参见[[产品体系构建工具关联表]]中T05行Scripts链接列     |
| `req_to_tasks`               | 项目计划制定      | requirements_flow.py | 参见[[产品体系构建工具关联表]]中T09行Scripts链接列     |

### 开发质量流程 (Development Flow)

| MCP工具函数                    | 工具名称   | 业务流程模块              | 脚本路径引用                                    |
| -------------------------- | ------ | ------------------- | ----------------------------------------- |
| `create_project_dashboard` | 辅助工具开发 | development_flow.py | 参见[[产品体系构建工具关联表]]中T15行Scripts链接列     |
| `analyze_code_quality`     | 代码质量检查 | development_flow.py | 参见[[产品体系构建工具关联表]]中T16行Scripts链接列     |
| `generate_document_report` | 质量验证报告 | development_flow.py | 参见[[产品体系构建工具关联表]]中T22行Scripts链接列     |

### 文档关联流程 (Document Flow)

| MCP工具函数             | 工具名称     | 业务流程模块           | 脚本路径引用                                    |
| ------------------- | -------- | ---------------- | ----------------------------------------- |
| `link_documents`    | 文档关联处理   | document_flow.py | 参见[[产品体系构建工具关联表]]中T23行Scripts链接列     |
| `sync_canvas`       | Canvas同步 | document_flow.py | 参见[[产品体系构建工具关联表]]中T24行Scripts链接列     |
| `manage_trace`      | 信息追溯管理   | document_flow.py | 参见[[产品体系构建工具关联表]]中T25行Scripts链接列     |
| `create_flow_chart` | 快速可视化    | document_flow.py | 参见[[产品体系构建工具关联表]]中T26行Scripts链接列     |

### 配置管理流程 (Config Flow)

| MCP工具函数                  | 工具名称   | 业务流程模块         | 脚本路径引用                                    |
| ------------------------ | ------ | -------------- | ----------------------------------------- |
| `load_config`            | 配置加载   | config_flow.py | 参见[[产品体系构建工具关联表]]中配置管理相关行Scripts链接列 |
| `save_config`            | 配置保存   | config_flow.py | 参见[[产品体系构建工具关联表]]中配置管理相关行Scripts链接列 |
| `validate_config`        | 配置验证   | config_flow.py | 参见[[产品体系构建工具关联表]]中配置管理相关行Scripts链接列 |
| `merge_configs`          | 配置合并   | config_flow.py | 参见[[产品体系构建工具关联表]]中配置管理相关行Scripts链接列 |
| `create_config_template` | 配置模板创建 | config_flow.py | 参见[[产品体系构建工具关联表]]中配置管理相关行Scripts链接列 |
| `backup_config`          | 配置备份   | config_flow.py | 参见[[产品体系构建工具关联表]]中配置管理相关行Scripts链接列 |


## 参数处理架构

### product-development-complete服务器参数处理流程

```mermaid
graph TD
    A[MCP客户端调用] --> B[FastMCP接收请求]
    B --> C[工具函数调用]
    C --> D[参数验证与解析]

    D --> E[路径参数处理]
    E --> F{project_path是绝对路径?}
    F -->|是| G[直接使用绝对路径]
    F -->|否| H[相对于工作区根目录解析]

    H --> I[workspace_root = F:/101_link-notebook/Obsidian-Vault/KA/Product-development]
    I --> J[base_path = workspace_root / project_path]
    G --> K[base_path = Path(project_path)]
    J --> L[路径解析与规范化]
    K --> L

    L --> M[base_path.resolve()]
    M --> N[full_project_path = base_path / project_name]

    N --> O[脚本路径构建]
    O --> P[script_path = SCRIPTS_BASE / script_name]
    P --> Q[SCRIPTS_BASE = F:/101_link-notebook/Obsidian-Vault/KA/Product-development/build-tools/scripts]

    Q --> R[Python解释器选择]
    R --> S{虚拟环境存在?}
    S -->|是| T[使用.venv/Scripts/python.exe]
    S -->|否| U[使用系统Python]

    T --> V[命令构建]
    U --> V
    V --> W[cmd = [python_executable, script_path] + script_args]

    W --> X[脚本参数构建]
    X --> Y[--project_path: base_path<br/>--project_name: project_name<br/>--structure_type: structure_type<br/>--scripts_base: SCRIPTS_BASE<br/>--force: 强制模式]

    Y --> Z[subprocess执行]
    Z --> AA[超时保护: 60秒]
    AA --> BB[结果处理与返回]
```

### 关键目录说明

| 目录类型 | 路径 | 说明 |
|---------|------|------|
| **工作区根目录** | `F:/101_link-notebook/Obsidian-Vault/KA/Product-development` | MCP服务器工作区根目录 |
| **脚本目录** | `{工作区根目录}/build-tools/scripts` | 被包装的本地脚本存放目录 |
| **项目基础路径** | `{工作区根目录}/{project_path}` | 项目创建的基础目录 |
| **项目完整路径** | `{项目基础路径}/{project_name}` | 最终项目目录 |
| **虚拟环境** | `{工作区根目录}/.venv` | Python虚拟环境 |
| **MCP工具目录** | `{工作区根目录}/build-tools/mcp-server_local_integrations/tools` | MCP工具实现目录 |

### 参数传递链路

```
MCP客户端参数 → MCP工具验证 → 路径解析 → 脚本参数构建 → subprocess调用 → 本地脚本执行
```

[[final_smart_params_implementation_plan]]

## 支持文件

+ 开发指南
[[mcp-server_create_guide]]

+ mcp server配置
  [[mcp_config.json]]

[[local_mcp-servers_development]]

