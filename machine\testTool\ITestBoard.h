#ifndef _ITEST_BOARD_H_
#define _ITEST_BOARD_H_

#include <QObject>
#include <QMap>
#include <QByteArray>
//#include <QSerialPort>
#include "IComm.h"
#include "IProtocol.h"


class ITestBoard:public QObject
{
  Q_OBJECT
public:
  ITestBoard();
  virtual ~ITestBoard(){};

  QByteArray  m_strPre;

  //* 交互步骤
  enum ECommStep{
    eSTART_STEP,
    eDATA_STEP,
    eCOMPLETE_STEP,
  };

protected:
  QMap<QString, QByteArray> m_cmd =
  {
    {"start", {0}},
    {"stop", {0}},
    {"test", {0}},
  };

  IProtocol* m_protocol_ = nullptr; //一个设备一种协议

public:
  virtual uint getUartBaud(void) = 0;

  virtual bool start(const uint8_t& id, const QByteArray &data) = 0; //开始
  virtual bool stop(const QByteArray &data) = 0; //停止

  virtual QByteArray portDataRead() = 0; //
  virtual void change_Icom_interface(IComm* port_) = 0;
  virtual bool dataParsing(QByteArray str, const int &length) = 0; //数据解析
//signals:
//  virtual void signalEmit(void) = 0; //无法实现多态
};


#endif
