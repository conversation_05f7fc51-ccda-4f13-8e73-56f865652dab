#include "sensorBoardFactory.h"
#include <QDebug>

QMap<ISensorBoardFactory::ESensorBoard, ISensorBoardFactory::StFaculaSensorInfo> ISensorBoardFactory::sst_facula_sensor_info = {
    {
        ISensorBoardFactory::eTOF_D4,
        {
            IPhotonSensor::eFOCUS,
            IFaculaFactory::circle,
            IPhotonSensor::sm_mpNum_link_dimension[IPhotonSensor::e4302],
            true,
            false,
            IFaculaFactory::eINTER_POLATION,
            {0, 0, 0},
            IFaculaAdjust::eFACULA_FOCUS,
        },
    },
    {
        ISensorBoardFactory::eTOF_T4,
        {
            IPhotonSensor::eDISPERSE,
            IFaculaFactory::circle,
            IPhotonSensor::sm_mpNum_link_dimension[IPhotonSensor::e4302],
            true,
            false,
            IFaculaFactory::eINTER_POLATION,
            {0, 0, 0},
            IFaculaAdjust::eFACULA_FOCUS,
        },
    },
    // {
    //     ISensorBoardFactory::eTOF_D6,
    //     {
    //         IPhotonSensor::eFOCUS,
    //         IFaculaFactory::circle,
    //         IPhotonSensor::sm_mpNum_link_dimension[IPhotonSensor::e4302],
    //         false,
    //         false,
    //         IFaculaFactory::eINTER_POLATION,
    //         {0, 0, 0},
    //         IFaculaAdjust::eDESIGNATE_MP,
    //     },
    // },
    {
        ISensorBoardFactory::eTOF_D6,
        {
            IPhotonSensor::eFOCUS,
            IFaculaFactory::circle,
            IPhotonSensor::sm_mpNum_link_dimension[IPhotonSensor::e4302],
            false,
            false,
            IFaculaFactory::eINTER_POLATION,
            {0, 0, 0},
            IFaculaAdjust::eFACULA_FOCUS,
        },
    },
    {
        ISensorBoardFactory::eTOF_T5,
        {
            IPhotonSensor::eFOCUS,
            IFaculaFactory::circle,
            IPhotonSensor::sm_mpNum_link_dimension[IPhotonSensor::e4302],
            true,
            false,
            IFaculaFactory::eINTER_POLATION,
            {0, 0, 0},
            IFaculaAdjust::eFACULA_FOCUS,
        },
    },
};

ISensorBoardFactory::ISensorBoardFactory() {
    //* register class name
    //    CMyPowerCtrlB::registerName();
    //    CStepperMotor::registerName();

    //*
}

ISensorBoardFactory::~ISensorBoardFactory() {
}

ITopBoard *ISensorBoardFactory::sensorBoardCreate(const ESensorBoard &board, IComm *port_) {
    ITopBoard *board_ = nullptr;

    switch (board) {
    case ESensorBoard::eTOF_D4:
        board_ = new CSensorCoinD(port_);
        break;

    case ESensorBoard::eTOF_T4:
        board_ = new CSensorCoinD(port_);
        break;

    case ESensorBoard::eTOF_D6:
        board_ = new CSensorCoinD(port_);
        break;

    case ESensorBoard::eTOF_T5:
        board_ = new CSensorCoinD(port_);
        break;

    default:
        board_ = new CSensorCoinD(port_);
        break;
    }
    return board_;
}
