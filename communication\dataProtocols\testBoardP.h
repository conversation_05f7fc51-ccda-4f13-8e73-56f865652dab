#ifndef  _TEST_BOARD_P_H_
#define  _TEST_BOARD_P_H_

#include <QByteArray>
#include "IProtocol.h"

class CTestBoardP:public IProtocol
{
public:
    CTestBoardP();
    ~CTestBoardP();

    //QByteArray m_strPre;

    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

private:
//    typedef union{
//      QByteArray frame;
      typedef struct {
        uint8_t     header;
        uint8_t     cmd;
        uint8_t     id;
        uint8_t     check_xor;
        uint16_t    num;
        QByteArray  data; //不定长字节，无法用数据，用vector？
      } StFrame;
//    } UFrame;


    /*协议cmd枚举*/
    enum EFrame{
      eHEADER     = 0xA5,
      eHEADER_LEN = 0x06,
    };

    enum ECMD{
      kH2D = 0x01,	/*host->device*/
      kD2H = 0X02,	/*device->host*/
    };

    StFrame *mst_frame_ = nullptr;

//    void calXOR(QByteArray* array_); //计算校验和
//    uint8_t calXOR(uint8_t *data, const uint16_t &len);
};


#endif
