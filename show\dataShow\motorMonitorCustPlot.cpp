#include "motorMonitorcustPlot.h"

motorMonitorCustPlot::motorMonitorCustPlot(QCustomPlot &customPlot) :
    m_length(250)
{
    customPlot.plotLayout()->clear();   // 清空默认的轴矩形 可以清空轴的所有内容
    customPlot.setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    //@1主视图的轴
    QCPLayoutGrid *mainGrid = new QCPLayoutGrid;
    //@2副视图的轴
    QCPLayoutGrid *subGrid = new QCPLayoutGrid;

    QCPAxis *main_key_Axis = customPlot.xAxis; //(mainRectRight, QCPAxis::atLeft);
    QCPAxis *main_value_Axis = customPlot.yAxis;
    QCPAxis *sub_key_Axis = customPlot.xAxis; //(mainRectRight, QCPAxis::atLeft);
    QCPAxis *sub_value_Axis = customPlot.yAxis;

    mainRectRight = new QCPAxisRect(&customPlot, true); //可缩放
    subRectLeft = new QCPAxisRect(&customPlot, true);   // 不配置轴

    mainGrid->addElement(0, 0, mainRectRight); //layoutGrid 中添加 AxisRect

    //@3分配视图
    customPlot.plotLayout()->addElement(0, 0, mainGrid);     // 在第一行添加轴矩形
    customPlot.plotLayout()->addElement(1, 0, subGrid);        // 在第二行添加一个子布局，后面会添加两个轴矩形在里面

    /*2.1 主图*/
    main_key_Axis = mainRectRight->axis(QCPAxis::atBottom);
    main_value_Axis = mainRectRight->axis(QCPAxis::atLeft);

    main_key_Axis->setTickLabelRotation(60);     // 轴刻度文字旋转60度
    main_key_Axis->setSubTicks(false);           // 不显示子刻度
    main_key_Axis->setTickLength(0, 4);          // 轴内外刻度的长度分别是0,4,也就是轴内的刻度线不显示
    main_key_Axis->setRange(0, 250);               // 设置范围
    main_key_Axis->setLabel("fg周期");
    main_key_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    main_value_Axis->setRange(0, 500);
    main_value_Axis->setPadding(35);             // 轴的内边距，可以到QCustomPlot之开始（一）看图解
    main_value_Axis->setLabel("周期值/0.1ms");
    main_value_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    mainBar = new QCPBars(main_key_Axis, main_value_Axis); //

    mainBar->setAntialiased(false); // 为了更好的边框效果，关闭抗齿锯
    mainBar->setName("fg周期分布图"); // 设置柱状图的名字，可在图例中显示
    mainBar->setPen(QPen(QColor(0, 168, 140).lighter(130))); // 设置柱状图的边框颜色
    mainBar->setBrush(QColor(0, 168, 140));  // 设置柱状图的画刷颜色

    /*2.2 sub*/
    subGrid->addElement(0, 0, subRectLeft); //

    /*2.2.1 为柱状图设置一个文字类型的key轴，ticks决定了轴的范围，而labels决定了轴的刻度文字的显示 */
    sub_key_Axis = subRectLeft->axis(QCPAxis::atBottom);
    sub_value_Axis = subRectLeft->axis(QCPAxis::atLeft);

    sub_key_Axis->setTickLabelRotation(60);     // 轴刻度文字旋转60度
    sub_key_Axis->setSubTicks(false);           // 不显示子刻度
    sub_key_Axis->setTickLength(0, 1);          // 轴内外刻度的长度分别是0,4,也就是轴内的刻度线不显示
    sub_key_Axis->setRange(50, 150);               // 设置范围
    sub_key_Axis->setLabel("(10*圈数)/s (10*Hz)");
    sub_key_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    sub_value_Axis->setRange(0, 100);
    sub_value_Axis->setPadding(35);             // 轴的内边距，可以到QCustomPlot之开始（一）看图解
    sub_value_Axis->setLabel("次数");
    sub_value_Axis->setUpperEnding(QCPLineEnding::esSpikeArrow);

    subBar = new QCPBars(sub_key_Axis, sub_value_Axis); //

    subBar->setAntialiased(false); // 为了更好的边框效果，关闭抗齿锯
    mainBar->setName("每秒转速分布图"); // 设置柱状图的名字，可在图例中显示
    subBar->setPen(QPen(QColor(0, 168, 140).lighter(130))); // 设置柱状图的边框颜色
    subBar->setBrush(QColor(0, 168, 140));  // 设置柱状图的画刷颜色

    customPlot.show();

    customPlots = &customPlot;

    QObject::connect(&customPlot, SIGNAL(mouseDoubleClick(QMouseEvent*)), this, SLOT(mouseDouClick(QMouseEvent *)));
}

motorMonitorCustPlot::~motorMonitorCustPlot()
{
    delete wavePacketText;
    delete mainRectRight;
    delete subRectLeft;
    delete subRectRight;
    delete mainBar;
    delete subBar;
}

void motorMonitorCustPlot::mouseDouClick(QMouseEvent *event)
{
    Qt::MouseButton buttonType =  event->button();
    if(buttonType == Qt::MouseButton::LeftButton)
    {
        mainRectRight->axis(QCPAxis::atBottom)->setRange(0,m_length);
        mainRectRight->axis(QCPAxis::atLeft)->setRange(0,500);
    }
    else if(buttonType == Qt::MouseButton::RightButton)
    {
        subRectLeft->axis(QCPAxis::atBottom)->setRange(0,m_length);
        subRectLeft->axis(QCPAxis::atLeft)->setRange(0,100);
    }
    customPlots->replot(QCustomPlot::rpQueuedReplot);
}

void motorMonitorCustPlot::plotSampleCurve(QCPGraph* sub_graph, QVector<KEY_VALUE> vec)
{
    if(m_length == 0)
    {
        QMessageBox::information(this, tr("设置参数"), tr("请设置像素点数"));
    }
    int length = vec.length() - 5;//2
    QVector<QCPGraphData> data(length);

    for(int i=0; i<length; i++)
    {
       data[i].key = vec.at(i).key;
       data[i].value = vec.at(i).value;
    }

    sub_graph->data()->set(data);
    //subGraphRando->data()->set(data);
    //customPlots->replot(QCustomPlot::rpQueuedReplot);
}

void motorMonitorCustPlot::plotFitCurve(QVector<KEY_VALUE> vec)
{
    int length = vec.length();
    QVector<QCPGraphData> data(length);

    for(int i=0; i<length; i++)
    {
       data[i].key = vec.at(i).key;
       data[i].value = vec.at(i).value;
    }
    customPlots->replot(QCustomPlot::rpQueuedReplot);
}

float motorMonitorCustPlot::StandardDeviation(QVector<uint16_t> array, uint8_t n)
{
//    int n = sizeof(*array)/sizeof (uint16_t);
    double sigma = 0, pSigma = 0;
    for (int i = 0; i < n; ++i) {
        sigma += array[i];        // sum
        pSigma += array[i]*array[i];     // 平方和
    }
    sigma /= n;          // 获得平均值
    return qSqrt( (pSigma/n - sigma*sigma));
}

float motorMonitorCustPlot::StandardDeviation(QVector<double> array,uint8_t n)
{
//    int n = sizeof (*array)/sizeof(double);
    double sigma = 0, pSigma = 0;
    for (int i = 0; i < n; ++i) {
        sigma += array[i];        // sum
        pSigma += (array[i])*(array[i]);     // 平方和
    }
    sigma /= n;          // 获得平均值
    return qSqrt((pSigma/n - sigma*sigma)) ;
}

/* @brief: 质心计算
 * @param_in: 光斑数据；质心计算模式；光斑处理模式
 * @out:质心
 */
#if 0
bool motorMonitorCustPlot::dataHandle(const QByteArray &fg_data_original, const QByteArray &cycle_data_original, CentroidDistData* centroid_dist)
{
#else
QSharedPointer<QCPBarsDataContainer> motorMonitorCustPlot::dataHandle(const QVector<uint16_t> &data)
{
#endif
    QString str;
    int maxIndex=0, length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;

    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);
//    CentroidDistData* centroid_dist = new CentroidDistData;
//    centroid_dist->cycleHandleData = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

//    if(data1.length() < 4)
//    {
//        return ;
//    }
    length = data.length();
    //1.0
    barData.resize(length);
    for(int i=0; i<length; i++)
    {
//        centroid_dist->fgHandled[i].key = i;
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

//    if(facula_data_original.length() == (int)m_length)
//    {
//        m_centroid = centroidPos;
//    }

    return handled_data;
}

QSharedPointer<QCPBarsDataContainer> motorMonitorCustPlot::dataHandle1(uint16_t start_index, const QVector<uint16_t> &data)
{
    QString str;
    int  length=0;
    QVector<QCPBarsData> barData;
    QVector<QCPBarsData> bar_Cycle_Data;

    QSharedPointer<QCPBarsDataContainer> handled_data = QSharedPointer<QCPBarsDataContainer>(new QCPBarsDataContainer);

    length = data.length();

    barData.resize(length);
    for(int i=start_index; i<length; i++)
    {
//        uint key_tmp = round((float)1000/i*100);
        barData[i].key = i;
        barData[i].value = (double)data.at(i);
    }
    handled_data->add(barData);

    return handled_data;
}

/* @brief: 光斑显示；文本框；质心显示
 * @param_in:
 * @out:
 */
void motorMonitorCustPlot::customPlotShow(uint8_t layout_select, const QSharedPointer<QCPBarsDataContainer> &data_show)
{
    QString str;

    //QVector<QCPGraphData> dataCos = centroid_dist->faculaHandled;
    if(layout_select == 1)
        mainBar->setData(data_show);//绘制fg曲线图
    else if(layout_select == 2)
        subBar->setData(data_show);//绘制fg曲线图
    /*主视图 3.数据文本框显示*/
//    QPen *pen = new QPen();
//    pen->setWidthF(1.0);
//    pen->setColor(QColor(255,128,0));
//    wavePointTextShow(0, &m_wavePointText[0], pen, m_centroidArrV[0],m_distArrV[0], 0, m_centroid_dist);
//    delete pen;

//    str = "实时数据 " + QString::number(centroidPos,'f',2)
//            + " | dist：" + QString::number(Dist,10)
//            + " | \n脉宽：" + QString::number(centroid_dist->wd,10)
//            + " | 最大幅值：" + QString::number(centroid_dist->maxValue,10)
//            + " | 基底幅值：" + QString::number(centroid_dist->baseLine,10)
//            + " | 波包差值：" + QString::number(centroid_dist->maxValue - centroid_dist->baseLine,10)
//    wavePacketText->setText(str);
    customPlots->replot(QCustomPlot::rpQueuedRefresh); //更新图表
}

/**
 * @brief: 余晖模式下文本内容显示
 * @param: wavePointText 文本框
 * @param: centroid_arr 位置index 质心数据
 * @param: dist_arr 位置index 距离数据
 */
void motorMonitorCustPlot::wavePointTextShow(const uint8_t &index, QCPItemText **wavePointText, QPen *pen, QVector<double> centroid_arr, QVector<uint16_t> dist_arr, float position_offset, CentroidDistData* centroid_dist)
{
//    uint8_t centroid_arr_num = sizeof (centroid_arr)/sizeof(double);
//    uint8_t dist_arr_num = sizeof (dist_arr)/sizeof(uint16_t);
    uint8_t centroid_arr_num =centroid_arr.length();
    uint8_t dist_arr_num = dist_arr.length();
    if(centroid_arr_num != dist_arr_num)
    {
        //报错
        QMessageBox::warning(this,tr("质心数量:" + centroid_arr_num),tr("距离数量：" + dist_arr_num));
        return;
    }
    float centroid_sigma = StandardDeviation(centroid_arr, centroid_arr_num);
    float dist_sigma = StandardDeviation(dist_arr, dist_arr_num); //QString::number(centroid_sigma,'g', -1)
    float centr_sum = 0, centr_mean, dist_sum = 0, dist_mean;
    for(uint8_t i =0; i< centroid_arr_num; ++i)
    {
        centr_sum += centroid_arr[i];
        dist_sum += dist_arr[i];
    }
    centr_mean = centr_sum/centroid_arr_num;
    dist_mean = dist_sum/dist_arr_num;

    if(*wavePointText == NULL)
    {
        *wavePointText = new QCPItemText(customPlots);
        (*wavePointText)->setPositionAlignment(Qt::AlignTop|Qt::AlignLeft); //文字布局：顶、左对齐
        (*wavePointText)->position->setType(QCPItemPosition::ptAxisRectRatio); //位置类型（当前轴范围的比例为单位/实际坐标为单位）
        (*wavePointText)->position->setCoords(0.85, 0.02 + position_offset); //把文字框放在X轴的中间，Y轴的最顶部
        (*wavePointText)->setFont(QFont("仿宋",6,QFont::Bold)); //字体大小
        (*wavePointText)->setColor(pen->color()); //
        (*wavePointText)->setPen(*pen); //
        (*wavePointText)->setPadding(QMargins(2,2,2,2)); //文字距离边框几个像素
    }
    (*wavePointText)->setText("centr_mean:" + QString::number(centr_mean,'f',2) + " centr_σ:" + QString::number(centroid_sigma,'f',2) +
            "\ndist_max:" + QString::number(centroid_dist->dist_max[1],10) + " dist_min:" + QString::number(centroid_dist->dist_min[1],10) +
            "\ndist_mean:" + QString::number(dist_mean,'f', 2) + " dist_σ:" + QString::number(dist_sigma,'f', 2));
}

void motorMonitorCustPlot::subCentroidShow(QCPGraph** subCentroidGraph, QPen *pen, QVector<QCPGraphData> dataCentroidSca)
{
    if(*subCentroidGraph == NULL)
    {
        *subCentroidGraph = customPlots->addGraph(subRectLeft->axis(QCPAxis::atBottom), subRectLeft->axis(QCPAxis::atLeft));
        (*subCentroidGraph)->setPen(*pen);
        (*subCentroidGraph)->setLineStyle(QCPGraph::lsNone); //曲线样式
        (*subCentroidGraph)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle,2)); //点空心
        (*subCentroidGraph)->setName(QString::fromLocal8Bit("实时质心"));
        (*subCentroidGraph)->rescaleKeyAxis();
    }
    (*subCentroidGraph)->data()->set(dataCentroidSca); //绘制散点图 余晖
}
