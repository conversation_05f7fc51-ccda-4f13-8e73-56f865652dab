#include "statisticalFormula.h"


//float CStatisticalFormula::StandardDeviation(QVector<uint16_t> array, uint8_t n) {
////    int n = sizeof(*array)/sizeof (uint16_t);
//    double sigma = 0, pSigma = 0;
//    for (int i = 0; i < n; ++i) {
//        sigma += array[i];        // sum
//        pSigma += array[i]*array[i];     // 平方和
//    }
//    sigma /= n;          // 获得平均值
//    return qSqrt( (pSigma/n - sigma*sigma));
//}

//float CStatisticalFormula::StandardDeviation(QVector<double> array,uint8_t n) {
////    int n = sizeof (*array)/sizeof(double);
//    double sigma = 0, pSigma = 0;
//    for (int i = 0; i < n; ++i) {
//        sigma += array[i];        // sum
//        pSigma += (array[i])*(array[i]);     // 平方和
//    }
//    sigma /= n;          // 获得平均值
//    return qSqrt((pSigma/n - sigma*sigma)) ;
//}
