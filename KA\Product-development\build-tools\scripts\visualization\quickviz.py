#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构后的可视化系统统一入口

基于单一职责原则的重构版本：
- 核心模块：统一接口、数据适配、组件管理
- 渲染器模块：HTML渲染、Matplotlib渲染
- 服务器模块：Web服务器
- 统一入口：命令行接口和协调各模块
"""

import sys
import argparse
import json
from pathlib import Path
from enum import Enum

# 添加核心模块到路径
sys.path.append(str(Path(__file__).parent))

from core.interfaces import VisualizationMode
from core.data_adapters import MultiModeDataAdapter
from core.component_utils import component_manager
from renderers.html_renderer import HTMLRenderer
from renderers.matplotlib_renderer import MatplotlibRenderer
from servers.web_server import WebVisualizationServer

class EnumEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理枚举类型"""
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)

class VisualizationSystem:
    """重构后的可视化系统 - 单一职责：系统协调和命令行接口"""
    
    def __init__(self):
        self.data_adapter = None
        self.html_renderer = HTMLRenderer()
        self.matplotlib_renderer = MatplotlibRenderer()
        self.web_server = None
    
    def run(self, args):
        """运行可视化系统"""
        try:
            # 初始化数据适配器
            self.data_adapter = MultiModeDataAdapter(Path(args.project_path))
            
            # 提取数据
            mode = VisualizationMode(args.mode)
            data = self.data_adapter.extract_data(Path(args.project_path), mode)
            
            print(f"📊 数据提取完成: {len(data.nodes)} 节点, {len(data.edges)} 边")
            
            if args.api_only:
                # API模式：输出JSON
                self._output_api_data(data)
            elif args.output:
                # 静态图像输出
                self._generate_static_image(data, args)
            else:
                # Web服务器模式
                self._start_web_server(data, args)
                
        except Exception as e:
            print(f"❌ 系统运行失败: {e}")
            return 1
        
        return 0
    
    def _output_api_data(self, data):
        """输出API数据"""
        api_data = {
            "title": data.title,
            "mode": data.mode.value,
            "nodes": [self._node_to_dict(node) for node in data.nodes],
            "edges": [self._edge_to_dict(edge) for edge in data.edges],
            "metadata": data.metadata
        }
        
        print(json.dumps(api_data, ensure_ascii=False, indent=2, cls=EnumEncoder))
    
    def _generate_static_image(self, data, args):
        """生成静态图像或HTML文件"""
        output_path = Path(args.output)
        file_extension = output_path.suffix.lower()
        
        if file_extension == '.html':
            # 生成HTML文件
            html_content = self.html_renderer.render(data)
            
            # 写入HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"图像已保存到 {output_path}")
            print(f"✅ HTML文件已生成: {output_path}")
        else:
            # 生成静态图像
            format_type = args.format or file_extension.lstrip('.') or "png"
            
            if format_type in self.matplotlib_renderer.get_supported_formats():
                # 使用Matplotlib渲染器
                output_path = self.matplotlib_renderer.render(
                    data,
                    output_path=args.output,
                    format=format_type,
                    figsize=args.figsize,
                    dpi=args.dpi
                )
                
                if output_path:
                    print(f"✅ 图像已生成: {output_path}")
                else:
                    print("✅ 图像已显示")
            else:
                print(f"❌ 不支持的图像格式: {format_type}")
                print(f"支持的格式: {', '.join(self.matplotlib_renderer.get_supported_formats())}")
    
    def _start_web_server(self, data, args):
        """启动Web服务器"""
        # 使用HTML渲染器生成页面
        html_content = self.html_renderer.render(data)
        
        # 设置并启动Web服务器
        self.web_server = WebVisualizationServer(args.port)
        self.web_server.setup(html_content, args.project_path)
        self.web_server.start(auto_open=args.auto_open)
    
    def _node_to_dict(self, node):
        """将Node对象转换为字典"""
        return {
            "id": node.id,
            "name": node.name,
            "type": node.type,
            "component": node.component,
            "x": node.x,
            "y": node.y,
            **node.properties
        }
    
    def _edge_to_dict(self, edge):
        """将Edge对象转换为字典"""
        return {
            "source": edge.source,
            "target": edge.target,
            "type": edge.type,
            **edge.properties
        }

def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="重构后的可视化系统 - 基于单一职责原则",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # Web模式 (默认)
  python quickviz_refactored.py /path/to/project --mode=all
  
  # API模式 (输出JSON)
  python quickviz_refactored.py /path/to/project --mode=workflow --api-only
  
  # 静态图像生成
  python quickviz_refactored.py /path/to/project --mode=documents --output=graph.png
  
  # 自定义端口
  python quickviz_refactored.py /path/to/project --port=8080
        """
    )
    
    # 必需参数
    parser.add_argument("project_path", 
                       help="项目路径")
    
    # 可视化模式
    parser.add_argument("--mode", 
                       choices=[mode.value for mode in VisualizationMode],
                       default="all",
                       help="可视化模式选择 (默认: all)")
    
    # 输出选项
    output_group = parser.add_mutually_exclusive_group()
    output_group.add_argument("--api-only", action="store_true",
                             help="仅输出JSON数据，不启动服务器")
    output_group.add_argument("--output", 
                             help="输出静态图像文件路径")
    
    # Web服务器选项
    parser.add_argument("--port", type=int, default=8080,
                       help="Web服务器端口 (默认: 8080)")
    parser.add_argument("--auto-open", action="store_true", default=True,
                       help="自动打开浏览器 (默认: True)")
    parser.add_argument("--no-auto-open", dest="auto_open", action="store_false",
                       help="不自动打开浏览器")
    
    # 图像生成选项
    parser.add_argument("--format", 
                       choices=["png", "svg", "pdf", "jpg", "eps"],
                       help="图像格式 (用于静态图像输出)")
    parser.add_argument("--figsize", nargs=2, type=float, default=[15, 10],
                       metavar=("WIDTH", "HEIGHT"),
                       help="图像尺寸 (默认: 15 10)")
    parser.add_argument("--dpi", type=int, default=300,
                       help="图像DPI (默认: 300)")
    
    # 调试选项
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    
    return parser

def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    if args.verbose:
        print("🚀 启动重构后的可视化系统")
        print(f"📁 项目路径: {args.project_path}")
        print(f"📊 可视化模式: {args.mode}")
        print(f"🎨 输出模式: {'API' if args.api_only else ('静态图像' if args.output else 'Web服务器')}")
        print("-" * 50)
    
    # 创建并运行系统
    system = VisualizationSystem()
    return system.run(args)

if __name__ == "__main__":
    sys.exit(main()) 