#!/usr/bin/env python3
"""
MCP 服务器问题诊断脚本
"""

import json
import subprocess
import sys
from pathlib import Path
import importlib.util

def diagnose_environment():
    """诊断Python环境"""
    print("=== Python环境诊断 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查MCP依赖
    try:
        import mcp
        print(f"✓ MCP SDK已安装: {mcp.__version__ if hasattr(mcp, '__version__') else '版本未知'}")
    except ImportError:
        print("❌ MCP SDK未安装 - 运行: pip install mcp")
        return False
    
    try:
        from mcp.server.fastmcp import FastMCP
        print("✓ FastMCP 可用")
    except ImportError as e:
        print(f"❌ FastMCP 不可用: {e}")
        return False
    
    return True

def diagnose_config():
    """诊断配置文件"""
    print("\n=== 配置文件诊断 ===")
    
    # 检查配置文件路径
    config_path = Path(__file__).parent.parent.parent / ".cursor" / "mcp.json"
    print(f"配置文件路径: {config_path}")
    
    if not config_path.exists():
        print("❌ 配置文件不存在")
        return False
    
    print("✓ 配置文件存在")
    
    # 读取配置
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✓ 配置文件格式正确")
        
        # 检查mcpServers配置
        if "mcpServers" not in config:
            print("❌ 缺少 mcpServers 配置")
            return False
        
        print("✓ mcpServers 配置存在")
        
        servers = config["mcpServers"]
        for name, server_config in servers.items():
            print(f"\n服务器: {name}")
            
            # 检查命令路径
            command = server_config.get("command")
            if command and Path(command).exists():
                print(f"  ✓ Python路径有效: {command}")
            else:
                print(f"  ❌ Python路径无效: {command}")
            
            # 检查脚本路径
            args = server_config.get("args", [])
            if args:
                script_path = Path(args[0])
                if script_path.exists():
                    print(f"  ✓ 脚本路径有效: {script_path}")
                else:
                    print(f"  ❌ 脚本路径无效: {script_path}")
            
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def diagnose_server_script():
    """诊断服务器脚本"""
    print("\n=== 服务器脚本诊断 ===")
    
    script_path = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_fastmcp.py"
    
    if not script_path.exists():
        print(f"❌ 服务器脚本不存在: {script_path}")
        return False
    
    print(f"✓ 服务器脚本存在: {script_path}")
    
    # 尝试导入脚本
    try:
        spec = importlib.util.spec_from_file_location("server", script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print("✓ 服务器脚本可以导入")
        
        # 检查MCP对象
        if hasattr(module, 'mcp'):
            print("✓ MCP服务器对象存在")
            return True
        else:
            print("❌ MCP服务器对象不存在")
            return False
            
    except Exception as e:
        print(f"❌ 服务器脚本导入失败: {e}")
        return False

def test_server_execution():
    """测试服务器执行"""
    print("\n=== 服务器执行测试 ===")
    
    script_path = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_fastmcp.py"
    
    try:
        # 使用配置文件中的Python路径
        config_path = Path(__file__).parent.parent.parent / ".cursor" / "mcp.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        python_path = config["mcpServers"]["product-development-fastmcp"]["command"]
        
        # 测试服务器是否可以启动
        cmd = [python_path, str(script_path)]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 发送初始化消息
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # 注意：这里发送\n结尾的消息
        input_data = json.dumps(init_message) + "\n"
        
        result = subprocess.run(
            cmd,
            input=input_data,
            capture_output=True,
            text=True,
            timeout=10,
            encoding='utf-8'
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出: {result.stdout}")
        print(f"标准错误: {result.stderr}")
        
        if result.returncode == 0 and result.stdout:
            try:
                # 尝试解析响应
                response = json.loads(result.stdout.strip())
                print("✓ 服务器响应格式正确")
                print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
                return True
            except json.JSONDecodeError:
                print("❌ 服务器响应格式错误")
                return False
        else:
            print("❌ 服务器执行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 服务器响应超时")
        return False
    except Exception as e:
        print(f"❌ 执行测试失败: {e}")
        return False

def suggest_solutions():
    """提供解决方案建议"""
    print("\n=== 解决方案建议 ===")
    
    solutions = [
        "1. 确保已安装MCP SDK: pip install mcp",
        "2. 重启 Cursor 应用程序",
        "3. 检查 .cursor/mcp.json 配置文件路径是否正确",
        "4. 确保Python路径在配置文件中是正确的",
        "5. 检查脚本路径是否存在且可访问",
        "6. 确保环境变量设置正确",
        "7. 在Cursor中通过 Ctrl+Shift+P 打开命令面板，搜索 'MCP' 查看状态"
    ]
    
    for solution in solutions:
        print(solution)

def main():
    """主诊断流程"""
    print("MCP 服务器问题诊断")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    if diagnose_environment():
        success_count += 1
    
    if diagnose_config():
        success_count += 1
    
    if diagnose_server_script():
        success_count += 1
    
    if test_server_execution():
        success_count += 1
    
    print(f"\n=== 诊断结果 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✅ 所有测试通过，MCP服务器应该能正常工作")
        print("如果仍然有问题，请尝试重启Cursor")
    else:
        print("❌ 发现问题，请参考解决方案建议")
        suggest_solutions()

if __name__ == "__main__":
    main() 