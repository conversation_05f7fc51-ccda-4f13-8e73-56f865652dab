#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
项目信息获取工具 (T06)
扫描项目目录，返回项目基本信息、文件统计、配置状态、目录结构等详细信息
"""

import os
import sys
import json
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

def get_directory_size(path: Path) -> int:
    """获取目录大小（字节）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
    except (OSError, PermissionError):
        pass
    return total_size

def count_files_by_type(path: Path) -> Dict[str, int]:
    """按文件类型统计文件数量"""
    file_counts = {}
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                ext = Path(filename).suffix.lower()
                if not ext:
                    ext = "无扩展名"
                file_counts[ext] = file_counts.get(ext, 0) + 1
    except (OSError, PermissionError):
        pass
    return file_counts

def get_config_status(project_path: Path) -> Dict[str, Any]:
    """检查配置文件状态"""
    config_dir = project_path / "config"
    config_status = {
        "config_directory_exists": config_dir.exists(),
        "config_files": [],
        "missing_configs": []
    }
    
    expected_configs = [
        "document_links_config.json",
        "workflow_config.json", 
        "traceability_config.json",
        "deliverables_config.json",
        "production_config.json",
        "quality_config.json",
        "development_config.json",
        "design_config.json",
        "requirements_analysis_config.json",
        "requirements_import_config.json"
    ]
    
    if config_dir.exists():
        for config_file in expected_configs:
            config_path = config_dir / config_file
            if config_path.exists():
                config_status["config_files"].append({
                    "name": config_file,
                    "size": config_path.stat().st_size,
                    "modified": datetime.fromtimestamp(config_path.stat().st_mtime).isoformat()
                })
            else:
                config_status["missing_configs"].append(config_file)
    else:
        config_status["missing_configs"] = expected_configs
    
    return config_status

def get_directory_structure(path: Path, max_depth: int = 2) -> Dict[str, Any]:
    """获取目录结构"""
    def scan_directory(dir_path: Path, current_depth: int = 0) -> Dict[str, Any]:
        if current_depth >= max_depth:
            return {"type": "directory", "truncated": True}
        
        try:
            items = {}
            for item in dir_path.iterdir():
                if item.name.startswith('.'):
                    continue
                    
                if item.is_dir():
                    items[item.name] = scan_directory(item, current_depth + 1)
                else:
                    items[item.name] = {
                        "type": "file",
                        "size": item.stat().st_size
                    }
            return {"type": "directory", "items": items}
        except (OSError, PermissionError):
            return {"type": "directory", "error": "Permission denied"}
    
    return scan_directory(path)

def get_project_info(project_path: str) -> Dict[str, Any]:
    """获取项目信息"""
    project_path = Path(project_path).resolve()
    
    if not project_path.exists():
        return {
            "success": False,
            "error": f"项目路径不存在: {project_path}",
            "project_path": str(project_path)
        }
    
    # 基本信息
    basic_info = {
        "project_path": str(project_path),
        "project_name": project_path.name,
        "exists": project_path.exists(),
        "is_directory": project_path.is_dir(),
        "scan_time": datetime.now().isoformat()
    }
    
    if not project_path.is_dir():
        return {
            "success": False,
            "error": "指定路径不是目录",
            "basic_info": basic_info
        }
    
    # 文件统计
    file_stats = {
        "total_size": get_directory_size(project_path),
        "file_counts_by_type": count_files_by_type(project_path),
        "total_files": sum(count_files_by_type(project_path).values())
    }
    
    # 配置状态
    config_status = get_config_status(project_path)
    
    # 目录结构
    directory_structure = get_directory_structure(project_path)
    
    # 检查关键文件
    key_files = {
        "README.md": (project_path / "README.md").exists(),
        "product.canvas": (project_path / "product.canvas").exists(),
        "config_directory": (project_path / "config").exists(),
        "scripts_directory": (project_path / "scripts").exists()
    }
    
    return {
        "success": True,
        "basic_info": basic_info,
        "file_statistics": file_stats,
        "config_status": config_status,
        "key_files": key_files,
        "directory_structure": directory_structure
    }

def main():
    parser = argparse.ArgumentParser(description="获取项目信息")
    parser.add_argument("project_path", nargs="?", default=".", 
                       help="项目路径，默认为当前目录")
    parser.add_argument("--json", action="store_true", 
                       help="以JSON格式输出")
    
    args = parser.parse_args()
    
    try:
        result = get_project_info(args.project_path)
        
        if args.json:
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            if result["success"]:
                print(f"项目信息扫描完成: {result['basic_info']['project_name']}")
                print(f"项目路径: {result['basic_info']['project_path']}")
                print(f"总文件数: {result['file_statistics']['total_files']}")
                print(f"总大小: {result['file_statistics']['total_size']} 字节")
                print(f"配置文件数: {len(result['config_status']['config_files'])}")
                if result['config_status']['missing_configs']:
                    print(f"缺失配置: {len(result['config_status']['missing_configs'])} 个")
            else:
                print(f"错误: {result['error']}")
                sys.exit(1)
                
    except Exception as e:
        error_result = {
            "success": False,
            "error": f"执行失败: {str(e)}",
            "project_path": args.project_path
        }
        
        if args.json:
            print(json.dumps(error_result, ensure_ascii=False, indent=2))
        else:
            print(f"错误: {error_result['error']}")
        sys.exit(1)

if __name__ == "__main__":
    main()
