# 多层级产品框架核心原则

本文档总结了多层级产品框架设计的核心原则和基本要求，作为设计和实施多层级产品结构的指导方针。

## 背景

产品开发过程中通常涉及多个层级的结构和组织，从产品平台、产品族、产品系列到特定客户定制化需求。传统的固定层级结构（如产品族+系列两层结构）灵活性不足，无法满足复杂产品线的管理需求。本指南介绍多层级产品框架的核心概念、设计原则和架构思想。

## 基本原则

1. **单一来源原则**：每个组件和文档只在一个层级定义和维护，避免重复开发和冲突

2. **多实例支持**：每个层级可以包含多个实例（多个文件夹），满足不同分支的需求

3. **细化需求流**：需求从上层向下层流动并逐层细化，最后一层（通常是客户层）存放最详细需求

4. **需求追溯机制**：每个层级都有需求汇总文档（追溯矩阵），实现跨层级需求追踪

5. **层级标识**：每个需求ID中明确标识所属层级，确保需求来源清晰

6. **需求驱动开发**：各层级的设计、开发和项目管理内容基于当前层级的需求进行

## 资源分配原则

7. **软件分层开发**：
   - 核心应用软件在顶层开发（多于一个层级时）
   - 中间层提供特定领域功能和插件
   - 最后一层仅存放客户特定插件和配置

8. **固件分层开发**：
   - 固件核心放在非最后一层的某一层（由开发者决定）
   - 最后一层仅存放配置和客户定制的源码

9. **硬件和结构开发**：
   - 各层均可包含硬件和结构设计
   - 根据当前层级需求决定开发内容
   - 上层开发基础通用设计，下层开发特定变体

## 项目管理原则

10. **闭环开发管理**：
    - 在项目管理(PM)中通过Model Context Protocol(MCP)服务器输出开发细项
    - 实现开发细项->测试->问题->修改的闭环流程

11. **分层测试策略**：
    - 最后一层进行整产品测试(quality)
    - 测试内容基于规格和竞品分析
    - 测试问题链接到issue系统

12. **生产与交付**：
    - 生产相关内容(production)放在最后一层
    - 交付物(deliverables)集中放在顶层统一管理

## 层级管理

13. **层级关系管理**：
    - 每一层都有层级管理文档，链接到下一层级信息
    - 维护层级间的依赖和映射关系

14. **脚本支持**：
    - 顶层放置脚本文件夹，提供层级管理工具
    - 初始化时先创建层级目录，再建立层级间链接关系

15. **配置优先**：
    - 尽量通过配置而非修改代码实现定制需求
    - 保持核心功能的一致性和可维护性

## 核心概念

### 层级结构

多层级框架不限定层级数量和名称，可根据实际需求定义任意层级。常见的层级组织包括但不限于：

- **顶层**：技术平台、产品族或产品线
- **中间层**：产品系列、应用场景或区域市场
- **产品层**：具体产品型号、特定场景实现
- **客户层**：客户定制或特定客户实现

### 层级职责

每个层级都有明确的职责划分：

1. **顶层**：定义共享技术、核心架构和基础组件
2. **中间层**：定义特定领域需求、中间组件和场景适配
3. **产品层**：定义具体实现、产品化功能和外形设计
4. **客户层**：定义客户定制、特殊接口和品牌化资产

### 需求流和文档追溯

- 需求从上层向下层流动，逐层细化
- 每层拥有自己的需求汇总文档（追溯矩阵）
- 上层需求链接到下层需求，形成完整追溯链
- 最底层（通常是客户层）包含最终执行级别的详细需求

## 多层级框架设计原则

1. **单一来源**：每个组件和文档只在一个层级定义和维护
2. **层级识别**：每个需求和组件都标识其所属层级
3. **向下兼容**：上层变更必须考虑对下层的影响
4. **分层共享**：上层开发的组件可被所有下层使用
5. **配置替代修改**：尽量通过配置而非修改适应下层特殊需求

## 多层级目录结构概览

多层级产品结构通常包括：

- 层级配置文件：定义整体层级结构和参数
- 各层级目录：按功能和职责划分的子目录结构
- 层级管理文件：维护层级间关联的专用文档
- 交付物目录：集中存放各层级交付成果

每个层级目录通常包含：

- 需求管理
- 设计文档
- 开发资源
- 测试规范（按需）
- 生产资料（按需）
- 项目管理

## 资源共享机制

多层级框架的资源共享遵循以下规则：

1. **上层组件对下层可见**：上层开发的组件可以被所有下层使用
2. **同层组件不共享**：同一层级的不同实例之间默认不共享资源
3. **软件部署策略**：
   - 核心软件在顶层开发
   - 中间层提供插件和扩展
   - 产品层提供默认配置
   - 客户层仅包含定制配置文件
4. **固件部署策略**：
   - 固件核心在非底层开发
   - 产品层提供标准功能集
   - 客户层仅包含参数配置和定制
5. **硬件设计策略**：
   - 核心硬件设计在顶层
   - 产品层定义标准外形和接口
   - 客户层主要定制外形和连接方式

## 客户层级特性

客户层作为多层级产品中的最后一层，具有一些特殊性质：

1. **客户需求管理**：
   - 明确区分客户需求和产品标准功能
   - 追踪客户需求的变更和历史
   - 评估客户需求对上层产品的影响

2. **客户版本控制**：
   - 维护客户定制版本与产品标准版本的对应关系
   - 跟踪客户定制内容的迭代历史
   - 管理不同客户间的共性需求提升

3. **客户资产管理**：
   - 管理客户品牌资产和认证文件
   - 维护客户特定的包装和标识要求
   - 保存客户专用的测试报告和合规声明

4. **客户交付流程**：
   - 维护客户特定的交付清单
   - 管理客户专用的用户手册和技术文档
   - 记录客户验收测试结果和反馈

## 开发流程建议

1. **自上而下规划**：先定义顶层架构，再细化到底层实现
2. **需求分层管理**：确保每层需求合理分配，避免层级间职责重叠
3. **组件开发优先级**：先开发上层共享组件，再开发下层特有功能
4. **测试递进策略**：
   - 上层组件单元测试
   - 中间层集成测试
   - 产品层系统测试
   - 客户层验收测试

## 与传统两层结构的对比

| 特性 | 传统两层结构 | 多层级结构 |
|------|------------|-----------|
| 灵活性 | 固定为产品族和系列 | 支持任意层级数量和名称 |
| 需求管理 | 两级需求结构 | 多级需求追溯矩阵 |
| 组件共享 | 产品族到系列单向共享 | 任意层级间的有向共享 |
| 客户定制 | 在系列层混合处理 | 专门的客户层处理 |
| 适用场景 | 简单产品线管理 | 复杂多变的产品生态系统 |
| 配置复杂度 | 简单 | 较高，需精心设计层级结构 |
| 适应能力 | 有限 | 高度可扩展 |

## 总结

多层级产品框架提供了一种灵活的方式来组织复杂的产品结构，通过层级划分、需求追踪和资源共享机制，实现了产品开发过程中的高效协作和资源复用。特别是通过客户层的引入，使得产品能够同时保持核心组件的一致性和满足不同客户的个性化需求。通过合理设计层级结构，可以适应从简单的两层产品到复杂的多层级产品生态系统的各种场景。

## 总结

多层级产品框架通过明确的层级职责划分、需求追溯机制和资源共享策略，实现了灵活的产品结构管理。该框架适用于复杂产品系统，特别是需要支持多种应用场景和客户定制的产品线。通过遵循上述原则，可以有效平衡通用性与定制化的需求，提高开发效率和维护质量。
