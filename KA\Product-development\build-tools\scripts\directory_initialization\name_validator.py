#!/usr/bin/env python3
"""
产品命名验证工具 - 验证产品名称和系列名称是否符合命名规范
用法: python name_validator.py --product-name="KA-IoT-Gateway" --series-names="基础版,标准版,专业版"
"""

import argparse
import sys
import re
import os
import json
from pathlib import Path

# 产品名称格式：[前缀]-[产品类别]-[功能描述]
PRODUCT_NAME_PATTERN = r'^[A-Za-z0-9]+-[A-Za-z0-9]+-[A-Za-z0-9]+$'

# 标准系列名称列表
STANDARD_SERIES = [
    "基础版", "标准版", "专业版", "企业版", "高性能版", "紧凑版", 
    "增强版", "行业版", "入门版", "旗舰版", "精简版", "定制版",
    "Basic", "Standard", "Professional", "Enterprise", "Performance", 
    "Compact", "Enhanced", "Industry", "Lite", "Pro", "Custom"
]

# 前缀列表，从配置文件加载
def load_prefixes():
    try:
        script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        config_path = script_dir / 'tasks' / 'naming_config.json'
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('valid_prefixes', ['KA', 'RS', 'XT'])
        return ['KA', 'RS', 'XT']
    except Exception as e:
        print(f"警告: 加载前缀配置失败，使用默认前缀列表。错误: {e}")
        return ['KA', 'RS', 'XT']

# 产品类别列表，从配置文件加载
def load_categories():
    try:
        script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        config_path = script_dir / 'tasks' / 'naming_config.json'
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('valid_categories', ['IoT', 'AI', 'Cloud', 'Edge', 'ICS', 'CE', 'Med', 'Sec'])
        return ['IoT', 'AI', 'Cloud', 'Edge', 'ICS', 'CE', 'Med', 'Sec']
    except Exception as e:
        print(f"警告: 加载类别配置失败，使用默认类别列表。错误: {e}")
        return ['IoT', 'AI', 'Cloud', 'Edge', 'ICS', 'CE', 'Med', 'Sec']

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="验证产品名称和系列名称是否符合命名规范")
    parser.add_argument('--product-name', help="产品名称，例如KA-IoT-Gateway")
    parser.add_argument('--series-names', help="系列名称，用逗号分隔，例如基础版,标准版,专业版")
    return parser.parse_args()

def validate_product_name(name):
    """验证产品名称是否符合规范"""
    if not name:
        return False, "产品名称不能为空"
    
    # 检查格式（使用正则表达式）
    if not re.match(PRODUCT_NAME_PATTERN, name):
        return False, "产品名称格式不符合规范，应为[前缀]-[产品类别]-[功能描述]"
    
    # 检查各部分
    parts = name.split('-')
    
    # 检查前缀
    prefix = parts[0]
    valid_prefixes = load_prefixes()
    if prefix not in valid_prefixes:
        return False, f"产品前缀'{prefix}'不在有效前缀列表中。有效前缀: {', '.join(valid_prefixes)}"
    
    # 检查产品类别
    if len(parts) > 1:
        category = parts[1]
        valid_categories = load_categories()
        if category not in valid_categories:
            return False, f"产品类别'{category}'不在有效类别列表中。有效类别: {', '.join(valid_categories)}"
    
    # 避免特殊字符
    invalid_chars = r'/\:*?"<>|'
    for char in invalid_chars:
        if char in name:
            return False, f"产品名称包含非法字符: '{char}'。请避免使用: {invalid_chars}"
    
    return True, "产品名称符合规范"

def validate_series_names(series_str):
    """验证系列名称是否符合规范"""
    if not series_str:
        return False, "系列名称不能为空"
    
    series_list = [s.strip() for s in series_str.split(',')]
    
    # 检查是否为空列表
    if not series_list:
        return False, "系列名称列表不能为空"
    
    # 检查是否有重复
    if len(series_list) != len(set(series_list)):
        return False, "系列名称列表中存在重复项"
    
    # 检查是否使用标准系列名称
    non_standard = [s for s in series_list if s not in STANDARD_SERIES]
    if non_standard:
        warning = f"警告：以下系列名称不是标准系列名称: {', '.join(non_standard)}。推荐使用标准系列名称。"
        print(warning)
    
    # 避免特殊字符
    invalid_chars = r'/\:*?"<>|'
    for series in series_list:
        for char in invalid_chars:
            if char in series:
                return False, f"系列名称'{series}'包含非法字符: '{char}'。请避免使用: {invalid_chars}"
    
    return True, "系列名称符合规范"

def validate_script_integration(product_name, series_names):
    """验证整合到初始化脚本的检查函数"""
    # 验证产品名称
    product_valid, product_message = validate_product_name(product_name)
    if not product_valid:
        return False, product_message
    
    # 验证系列名称（如果提供）
    if series_names:
        series_valid, series_message = validate_series_names(series_names)
        if not series_valid:
            return False, series_message
    
    return True, "所有命名符合规范"

def main():
    """主函数"""
    args = parse_arguments()
    
    if args.product_name:
        valid, message = validate_product_name(args.product_name)
        prefix = "✓" if valid else "✗"
        print(f"{prefix} 产品名称: {message}")
    
    if args.series_names:
        valid, message = validate_series_names(args.series_names)
        prefix = "✓" if valid else "✗"
        print(f"{prefix} 系列名称: {message}")
    
    if not (args.product_name or args.series_names):
        print("请提供至少一个参数：--product-name 或 --series-names")
        parser.print_help()
        sys.exit(1)
    
    # 如果任何验证失败，则返回非零退出码
    if (args.product_name and not validate_product_name(args.product_name)[0]) or \
       (args.series_names and not validate_series_names(args.series_names)[0]):
        print("\n命名不符合规范，请参考命名规则文档进行修正。")
        sys.exit(1)
    
    print("\n所有命名符合规范。")
    sys.exit(0)

if __name__ == "__main__":
    main() 