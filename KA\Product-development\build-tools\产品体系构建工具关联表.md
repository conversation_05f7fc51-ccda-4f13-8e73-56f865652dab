# 产品体系构建工具关联表

本表格列出了产品体系构建框架中各个环节对应的工具选择，遵循市场MCP服务器优先原则。

## 📋 三表关联关系说明

本文档是**核心关联表**，通过以下方式与其他两个文档建立关联：

| 文档 | 关联方式 | 说明 |
|------|---------|------|
| **[[mcp-server_local_integrations/README.md\|工具实现清单]]** | 通过`MCP工具函数`列关联 | 本地MCP服务器的具体实现，每个工具函数对应一个环节 |
| **[[mcp-server_local_integrations/mcp-servers_test_report.md\|测试结果表]]** | 通过`环节索引(T01-T28)`关联 | AI对话框调用测试记录，验证工具实际效果 |

**关联链路**: `环节(T01-T28)` → `本地脚本` → `MCP工具函数` → `测试验证` → `流程模块`

## 工具选择原则(只能手动修改)

1. **市场MCP服务器优先**：优先使用成熟的市场化MCP服务器
2. **本地MCP服务器**：用于产品开发特定功能，**重要：本地MCP服务器都是通过MCP协议调用本地scripts脚本实现，不使用内置逻辑**
3. **Scripts脚本**：用于复杂的自动化操作，是本地MCP服务器的实际执行层
4. **VSCode Tasks**：提供统一的IDE集成界面

## 文档管理原则

- **奥卡姆剃刀原则**：选择最简单有效的实现方案，减少无效信息
- **单一职责原则**：避免功能重叠，每个文档专注于特定职责
- **单一来源原则**：避免多处重复信息，本表格是工具关联的唯一来源

## 总体入口

| 工具类型 | 入口名称 | 链接 | 说明 |
|---------|---------|------|------|
| MCP Server | 产品开发完整功能集成 | [[build-tools/mcp-server_local_integrations/unified/product-development-complete/README.md]] | 推荐的统一MCP服务器入口 |
| Scripts | 项目初始化主入口 | [[build-tools/scripts/init_product_project.py]] | 项目开发流程起点 |
| VSCode Tasks | 产品开发总控制台 | [[build-tools/vscode-tasks/README.md]] | VSCode集成界面入口 |

## 环节工具关联表

| 索引     | 环节               | 环节细项     | Scripts链接                                                            | VSCode Tasks链接                                  | 市场MCP工具                            | 本地MCP工具函数                                                               | 选择工具名称                                    | 实现效果(详细输出)                                             |
| ------ | ---------------- | -------- | -------------------------------------------------------------------- | ----------------------------------------------- | ---------------------------------- | ----------------------------------------------------------------------- | ----------------------------------------- | ------------------------------------------------------ |
|        | **1. 需求导入**      |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T01    |                  | 市场需求收集   | [[build-tools/scripts/requirements/import_requirements.py]]          | [[build-tools/vscode-tasks/README.md#导入需求文档]]   | fetch                              | [[mcp-server_local_integrations/README.md\|import_requirements]]        | 市场MCP: fetch + 本地MCP: import_requirements | 从文档文件导入需求，支持多种格式(JSON/Markdown/Excel)，返回导入数量、输出路径、需求列表 |
| T02    |                  | 竞品资料收集   | [[build-tools/scripts/requirements/import_requirements.py]]          | [[build-tools/vscode-tasks/README.md#导入需求文档]]   | brave-search + fetch               | -                                                                       | 市场MCP: brave-search + fetch               | 搜索竞品信息，获取网页内容，生成竞品分析报告                                 |
| T03    |                  | 技术需求整理   | [[build-tools/scripts/requirements/analyze_requirements.py]]         | [[build-tools/vscode-tasks/README.md#需求分析]]     | -                                  | [[mcp-server_local_integrations/README.md\|analyze_requirements]]       | 本地MCP: analyze_requirements               | 分析需求文档内容，生成需求统计、分类、优先级评估、依赖关系分析报告                      |
|        | **2. 需求分析与方案设计** |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T04    |                  | 需求分解     | [[build-tools/scripts/requirements/analyze_requirements.py]]         | [[build-tools/vscode-tasks/README.md#需求分析]]     | -                                  | [[mcp-server_local_integrations/README.md\|analyze_requirements]]       | 本地MCP: analyze_requirements               | 分析需求文档内容，生成需求统计、分类、优先级评估、依赖关系分析报告                      |
| T05    |                  | 需求矩阵维护   | [[build-tools/scripts/requirements/analyze_requirements.py]]         | [[build-tools/vscode-tasks/README.md#需求分析]]     | -                                  | [[mcp-server_local_integrations/README.md\|create_requirements_matrix]] | 本地MCP: create_requirements_matrix         | 创建需求追溯矩阵，建立需求间关联关系，生成矩阵文件和可视化图表                        |
| T06    |                  | 可行性评估    |                                                                      |                                                 | brave-search + memory              |                                                                         | 市场MCP: brave-search + memory              | 技术可行性分析和评估                                             |
| T07    |                  | 系统架构设计   | [[build-tools/scripts/visualization/]]                               | [[build-tools/vscode-tasks/README.md#快速可视化]]    | -                                  | [[mcp-server_local_integrations/README.md\|create_flow_chart]]          | 本地MCP: create_flow_chart                  | 分析项目结构生成系统架构图，支持多种格式(SVG/PNG/PDF)，包含组件关系和依赖图           |
| TDES01 |                  | 论文资料获取   |                                                                      |                                                 | 市场MCP                              | -                                                                       | 市场MCP                                     |                                                        |
| T08    |                  | 技术路线选择   |                                                                      |                                                 | brave-search + sequential-thinking | -                                                                       | 市场MCP: brave-search + sequential-thinking | 技术路线分析和选择建议                                            |
|        | **3. 开发规划与管理**   |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T09    |                  | 项目计划制定   | [[build-tools/scripts/integration/req_to_tasks.py]]                  | [[build-tools/vscode-tasks/README.md#需求转任务]]    | task-master                        | [[mcp-server_local_integrations/README.md\|req_to_tasks]]               | 市场MCP: task-master + 本地MCP: req_to_tasks  | 将需求转换为具体任务，生成任务列表、时间估算、依赖关系、分配建议                       |
| T10    |                  | 任务跟踪系统   | [[build-tools/scripts/integration/sync_req_tasks.py]]                | [[build-tools/vscode-tasks/README.md#同步需求任务状态]] | github + memory                    | -                                                                       | 市场MCP: github + memory                    | 任务状态跟踪和同步                                              |
| T11    |                  | 风险管理     |                                                                      |                                                 | memory + sequential-thinking       | -                                                                       | 市场MCP: memory + sequential-thinking       | 风险识别、评估和管理建议                                           |
|        | **4. 开发**        |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T12    |                  | 硬件开发     | [[build-tools/scripts/README.md#硬件开发工具]]                             | [[build-tools/vscode-tasks/README.md#初始化硬件项目]]  | -                                  | -                                                                       | Scripts: 硬件特定脚本                           | 硬件开发相关功能                                               |
| T13    |                  | 项目软件开发   | [[build-tools/scripts/README.md#软件开发工具]]                             | [[build-tools/vscode-tasks/README.md#初始化软件项目]]  | github + git + filesystem          | -                                                                       | 市场MCP: github + git + filesystem          | 软件开发项目管理                                               |
| T14    |                  | 生产工具开发   | [[build-tools/scripts/production/]]                                  | [[build-tools/vscode-tasks/README.md#生产管理任务]]   | -                                  | -                                                                       | Scripts: 生产特定脚本                           | 生产工具开发功能                                               |
| T15    |                  | 辅助工具开发   | [[build-tools/scripts/README.md]]                                    | [[build-tools/vscode-tasks/README.md]]          | -                                  | [[mcp-server_local_integrations/README.md\|create_project_dashboard]]   | 本地MCP: create_project_dashboard           | 生成HTML/PDF项目仪表板，包含项目概览、进度统计、质量指标、团队信息等可视化面板            |
| T16    |                  | 代码质量检查   | [[build-tools/scripts/README.md#质量管理]]                               | [[build-tools/vscode-tasks/README.md#代码质量检查]]   | -                                  | [[mcp-server_local_integrations/README.md\|analyze_code_quality]]       | 本地MCP: analyze_code_quality               | 分析代码文件质量，检测复杂度、重复代码、潜在问题，返回质量评分和详细分析报告                 |
|        | **5. 生产**        |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T17    |                  | 生产准备     | [[build-tools/scripts/production/generate_bom.py]]                   | [[build-tools/vscode-tasks/README.md#生成BOM清单]]  | sqlite                             | -                                                                       | Scripts + 市场MCP: sqlite                   | 生产准备和BOM管理                                             |
| T18    |                  | 小批量试产    | [[build-tools/scripts/production/]]                                  | [[build-tools/vscode-tasks/README.md#生产管理任务]]   | -                                  | -                                                                       | Scripts: 生产管理脚本                           | 小批量试产管理                                                |
| T19    |                  | 批量生产     | [[build-tools/scripts/production/]]                                  | [[build-tools/vscode-tasks/README.md#生产管理任务]]   | sqlite                             | -                                                                       | Scripts + 市场MCP: sqlite                   | 批量生产管理                                                 |
|        | **6. 项目输出**      |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T20    |                  | 软件版本发布   | [[build-tools/scripts/README.md#发布管理]]                               | [[build-tools/vscode-tasks/README.md#构建发布版本]]   | github + filesystem                | -                                                                       | 市场MCP: github + filesystem                | 软件版本发布管理                                               |
| T21    |                  | 文档输出     | [[build-tools/scripts/documentation/]]                               | [[build-tools/vscode-tasks/README.md#生成发布文档]]   | filesystem                         | -                                                                       | 市场MCP: filesystem                         | 文档生成和输出                                                |
| T22    |                  | 质量验证报告   | [[build-tools/scripts/README.md#质量管理]]                               | [[build-tools/vscode-tasks/README.md#执行自动化测试]]  | -                                  | [[mcp-server_local_integrations/README.md\|generate_document_report]]   | 本地MCP: generate_document_report           | 生成项目文档统计报告，包含文档数量、类型分布、关联关系、质量评估等详细信息                  |
|        | **7. 文档关联与追溯**   |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T23    |                  | 文档关联处理   | [[build-tools/scripts/links/auto_link_documents.py]]                 | [[build-tools/vscode-tasks/README.md#文档关联处理]]   | -                                  | [[mcp-server_local_integrations/README.md\|link_documents]]             | 本地MCP: link_documents                     | 扫描项目文档，自动建立文档间链接关系，更新INDEX文件，返回处理的文档数量和关联统计            |
| T24    |                  | Canvas同步 | [[build-tools/scripts/canvas/canvas_manager.py]]                     | [[build-tools/vscode-tasks/README.md#Canvas同步]] | -                                  | [[mcp-server_local_integrations/README.md\|sync_canvas]]                | 本地MCP: sync_canvas                        | 同步项目信息到Obsidian Canvas文件，创建可视化项目结构图，返回Canvas文件路径和节点统计  |
| T25    |                  | 信息追溯管理   | [[build-tools/scripts/infoTrace/auto_index_manager.py]]              | [[build-tools/vscode-tasks/README.md#信息追溯管理]]   | -                                  | [[mcp-server_local_integrations/README.md\|manage_trace]]               | 本地MCP: manage_trace                       | 管理信息追溯索引，扫描组件文档，更新INDEX文件，返回追溯关系和索引统计                  |
| T26    |                  | 快速可视化    | [[build-tools/scripts/visualization/quickviz.py]]                    | [[build-tools/vscode-tasks/README.md#快速可视化]]    | -                                  | [[mcp-server_local_integrations/README.md\|create_flow_chart]]          | 本地MCP: create_flow_chart                  | 根据流程数据创建可视化流程图，支持多种主题样式，返回图表文件和流程节点统计                  |
|        | **8. 配置管理**      |          |                                                                      |                                                 |                                    |                                                                         |                                           |                                                        |
| T27    |                  | 项目初始化    | [[build-tools/scripts/init_product_project.py]]                      | [[build-tools/vscode-tasks/README.md#初始化产品项目]]  | -                                  | [[mcp-server_local_integrations/README.md\|init_project]]               | 本地MCP: init_project                       | 创建项目目录结构，生成配置文件，返回项目路径、结构类型、创建的组件列表                    |
| T28    |                  | 配置文件管理   | [[build-tools/scripts/config/]]目录下6个脚本                               | [[build-tools/vscode-tasks/README.md#配置管理]]     | -                                  | 6个配置工具函数                                                                | 本地MCP: 6个配置管理工具                           | 配置文件完整生命周期管理：加载、保存、验证、合并、模板生成、备份，支持JSON/YAML格式         |
| T29    |                  | 项目信息获取   | [[build-tools/scripts/directory_initialization/get_project_info.py]] | [[build-tools/vscode-tasks/README.md#项目信息获取]]   | -                                  | [[mcp-server_local_integrations/README.md\|get_project_info]]           | 本地MCP: get_project_info                   | 扫描项目目录，返回项目基本信息、文件统计、配置状态、目录结构等详细信息                    |

## 工具选择说明

### 市场MCP服务器使用场景
- **文件操作**: filesystem - 通用文件读写操作
- **版本控制**: github, git - 代码仓库管理
- **网络功能**: fetch, brave-search - 信息获取和搜索
- **数据存储**: sqlite - 结构化数据管理
- **AI增强**: memory, sequential-thinking - 智能分析和记忆

### 本地MCP服务器使用场景
- **产品开发特定功能**: requirements-management, project-initialization
- **专业工具集成**: code-analysis, diagram-generators
- **统一功能集成**: product-development-complete (推荐)

### Scripts脚本使用场景
- **硬件相关操作**: 需要特定硬件工具集成
- **生产流程管理**: 复杂的生产工艺流程
- **批量自动化处理**: 大量文件或数据处理

### VSCode Tasks使用场景
- **IDE集成界面**: 提供统一的开发环境操作入口
- **参数化执行**: 需要用户输入参数的操作
- **可视化操作**: 提供友好的用户界面

## 使用建议

1. **优先使用统一MCP服务器**: `product-development-complete` 包含了大部分核心功能
2. **市场MCP服务器补充**: 使用成熟的市场服务器处理通用功能
3. **Scripts处理特殊需求**: 对于硬件、生产等特定领域使用专门脚本
4. **VSCode Tasks提供界面**: 为所有工具提供统一的IDE操作界面