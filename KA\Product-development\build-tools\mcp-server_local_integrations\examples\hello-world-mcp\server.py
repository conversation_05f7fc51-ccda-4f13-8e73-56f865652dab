#!/usr/bin/env python3
"""
Hello World MCP服务器示例
演示MCP服务器的基本结构和功能
"""

from mcp.server.fastmcp import FastMCP

# 创建MCP服务器实例
mcp = FastMCP("Hello World Example")


@mcp.tool()
def say_hello(name: str = "World") -> str:
    """
    向指定的人问好
    
    Args:
        name: 要问候的人的名字，默认为"World"
    
    Returns:
        str: 问候消息
    """
    return f"Hello, {name}! 欢迎使用MCP服务器！"


@mcp.tool()
def add_numbers(a: int, b: int) -> int:
    """
    计算两个数字的和
    
    Args:
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        int: 两个数字的和
    """
    return a + b


@mcp.tool()
def get_server_info() -> dict:
    """
    获取服务器信息
    
    Returns:
        dict: 服务器信息字典
    """
    return {
        "name": "Hello World MCP Server",
        "version": "1.0.0",
        "description": "一个简单的MCP服务器示例",
        "author": "Product Development Team",
        "capabilities": [
            "基本问候功能",
            "数学计算",
            "服务器信息查询"
        ]
    }


@mcp.resource("hello://greeting")
def get_greeting_template() -> str:
    """获取问候模板"""
    return """
    # 问候模板
    
    ## 基本问候
    - Hello, {name}!
    - 你好，{name}！
    - Hi there, {name}!
    
    ## 正式问候
    - Good morning, {name}
    - Good afternoon, {name}
    - Good evening, {name}
    
    ## 友好问候
    - Hey {name}, how are you?
    - Nice to meet you, {name}!
    - Welcome, {name}!
    """


@mcp.prompt()
def greeting_prompt(name: str, style: str = "friendly") -> str:
    """
    生成个性化问候提示
    
    Args:
        name: 要问候的人的名字
        style: 问候风格 (formal, friendly, casual)
    
    Returns:
        str: 个性化问候提示
    """
    styles = {
        "formal": f"请用正式的方式向{name}问好，表现出专业和尊重。",
        "friendly": f"请用友好的方式向{name}问好，营造温暖的氛围。",
        "casual": f"请用轻松随意的方式向{name}问好，保持自然和亲切。"
    }
    
    base_prompt = styles.get(style, styles["friendly"])
    
    return f"""
    {base_prompt}
    
    要求：
    1. 语言要自然流畅
    2. 体现出真诚的欢迎态度
    3. 可以包含一些积极的情感表达
    4. 适合在专业环境中使用
    """


if __name__ == "__main__":
    mcp.run(transport="stdio")
