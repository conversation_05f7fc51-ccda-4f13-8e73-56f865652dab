#include "bottomBoardP.h"
#include <qdebug.h>
#include "qLog.h"



CBottomBoardP::CBottomBoardP(){

}

CBottomBoardP::~CBottomBoardP(){

}

/**
 * @brief 获取控制指令
 * @return
 */
QByteArray CBottomBoardP::getControlCmd(const char &id)
{
  QByteArray cmd;

  cmd.append(0xa5);
  cmd.append(0x55);
  cmd.append(id);
  cmd.append(0x01); //

  calXOR(3, &cmd);
  return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CBottomBoardP::getWriteCmd(const char &id, const QByteArray &w_data)
{
  QByteArray w_cmd;
  w_cmd.append(0xa5);
  w_cmd.append(0x55);
  w_cmd.append(id);
  w_cmd.append(0x01); //xor

  uint16_t num = w_data.length();
  w_cmd.append(num & 0xff);
  w_cmd.append((num>>8) & 0xff);

  w_cmd.append(w_data);

  calXOR(3, &w_cmd);
  return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CBottomBoardP::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{


}
