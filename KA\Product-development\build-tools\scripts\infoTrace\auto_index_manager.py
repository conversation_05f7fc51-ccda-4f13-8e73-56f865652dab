#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
追溯系统 - 自动INDEX块级管理器

根据新的系统协作模式，该脚本专注于块级管理和精确追溯：
1. 块级内容识别和ID生成
2. 块级引用关系提取和验证
3. 基于已建立文档关联的块级追溯关系建立
4. INDEX文件的块级信息维护
5. 块级变更影响分析

重要约束: 内容追溯关系建立在文档关联基础上，如果两个文档之间不存在关联，
则也必然不会存在块级内容的关联。

注意: 此脚本位于公共库中，新项目可直接调用或拷贝使用
"""

import os
import sys
import json
import argparse
import re
from datetime import datetime
from pathlib import Path
import hashlib

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir.parent
sys.path.insert(0, str(scripts_dir))

# 导入基础设施层模块
from common.document_scanner import IndexScanner, DocumentScanner
from common.index_manager import IndexManager
from common.document_id_generator import DocumentIdGenerator
from common.config import get_component_dirs


class BlockManager:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        
        # 使用基础设施层的统一服务
        self.index_scanner = IndexScanner(project_path)
        self.document_scanner = DocumentScanner(project_path)
        self.index_manager = IndexManager(project_path)
        self.id_generator = DocumentIdGenerator()
        
        # 块类型定义
        self.block_types = {
            'H1': '一级标题', 'H2': '二级标题', 'H3': '三级标题',
            'H4': '四级标题', 'H5': '五级标题', 'H6': '六级标题',
            'LI': '列表项', 'OL': '编号列表', 'CODE': '代码块',
            'TABLE': '表格', 'QUOTE': '引用块', 'PARA': '段落',
            'FUNC': '功能块', 'API': 'API接口', 'ARCH': '架构块',
            'CONFIG': '配置块', 'TEST': '测试块', 'DOC': '文档块'
        }
    
    def scan_document_blocks(self, file_path, doc_id):
        """扫描文档中的所有内容块"""
        if not file_path.exists():
            return []
            
        blocks = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 自动识别标题块
            blocks.extend(self.extract_heading_blocks(content, doc_id))
            
            # 自动识别列表块  
            blocks.extend(self.extract_list_blocks(content, doc_id))
            
            # 识别手动标记块
            blocks.extend(self.extract_manual_blocks(content, doc_id))
            
            # 识别代码块
            blocks.extend(self.extract_code_blocks(content, doc_id))
            
            # 识别表格块
            blocks.extend(self.extract_table_blocks(content, doc_id))
            
        except Exception as e:
            print(f"警告: 扫描文档块失败 {file_path}: {e}")
            
        return blocks
    
    def extract_heading_blocks(self, content, doc_id):
        """提取标题块"""
        blocks = []
        lines = content.split('\n')
        
        h1_count = h2_count = h3_count = h4_count = h5_count = h6_count = 0
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                if level <= 6:
                    title = line.lstrip('#').strip()
                    
                    if level == 1:
                        h1_count += 1
                        block_id = f"{doc_id}.H1.{h1_count:03d}"
                    elif level == 2:
                        h2_count += 1
                        block_id = f"{doc_id}.H2.{h2_count:03d}"
                    elif level == 3:
                        h3_count += 1
                        block_id = f"{doc_id}.H3.{h3_count:03d}"
                    elif level == 4:
                        h4_count += 1
                        block_id = f"{doc_id}.H4.{h4_count:03d}"
                    elif level == 5:
                        h5_count += 1
                        block_id = f"{doc_id}.H5.{h5_count:03d}"
                    else:  # level == 6
                        h6_count += 1
                        block_id = f"{doc_id}.H6.{h6_count:03d}"
                    
                    blocks.append({
                        'block_id': block_id,
                        'block_type': f'H{level}',
                        'block_title': title[:100],  # 限制长度
                        'line_number': line_num,
                        'auto_generated': True
                    })
                    
        return blocks
    
    def extract_list_blocks(self, content, doc_id):
        """提取列表块"""
        blocks = []
        lines = content.split('\n')
        
        li_count = ol_count = 0
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('- ') or line.startswith('* '):
                li_count += 1
                block_id = f"{doc_id}.LI.{li_count:03d}"
                title = line[2:].strip()[:50]
                
                blocks.append({
                    'block_id': block_id,
                    'block_type': 'LI',
                    'block_title': title,
                    'line_number': line_num,
                    'auto_generated': True
                })
            elif re.match(r'^\d+\.\s', line):
                ol_count += 1
                block_id = f"{doc_id}.OL.{ol_count:03d}"
                title = re.sub(r'^\d+\.\s', '', line).strip()[:50]
                
                blocks.append({
                    'block_id': block_id,
                    'block_type': 'OL',
                    'block_title': title,
                    'line_number': line_num,
                    'auto_generated': True
                })
                
        return blocks
    
    def extract_manual_blocks(self, content, doc_id):
        """提取手动标记的块"""
        blocks = []
        
        # 查找手动块标记 <!-- BLOCK_ID: DOC001.FUNC.001 -->
        pattern = r'<!--\s*BLOCK_ID:\s*([A-Z]+\d+\.[A-Z]+\.\d+)\s*-->'
        matches = re.finditer(pattern, content)
        
        for match in matches:
            block_id = match.group(1)
            # 提取块类型
            parts = block_id.split('.')
            if len(parts) >= 3:
                block_type = parts[1]
                
                # 尝试提取块标题（从下一行或附近内容）
                start_pos = match.end()
                remaining_content = content[start_pos:start_pos+200]
                
                # 查找标题或内容
                title_match = re.search(r'(?:##?\s*)?([^\n]+)', remaining_content)
                title = title_match.group(1).strip()[:100] if title_match else '手动标记块'
                
                blocks.append({
                    'block_id': block_id,
                    'block_type': block_type,
                    'block_title': title,
                    'line_number': content[:match.start()].count('\n') + 1,
                    'auto_generated': False
                })
                
        return blocks
    
    def extract_code_blocks(self, content, doc_id):
        """提取代码块"""
        blocks = []
        
        # 查找代码块 ```language
        pattern = r'```(\w+)?'
        matches = list(re.finditer(pattern, content))
        
        code_count = 0
        for i in range(0, len(matches), 2):  # 成对出现
            if i + 1 < len(matches):
                code_count += 1
                block_id = f"{doc_id}.CODE.{code_count:03d}"
                
                # 提取语言类型
                lang = matches[i].group(1) or 'text'
                title = f"{lang}代码块"
                
                blocks.append({
                    'block_id': block_id,
                    'block_type': 'CODE',
                    'block_title': title,
                    'line_number': content[:matches[i].start()].count('\n') + 1,
                    'auto_generated': True
                })
                
        return blocks
    
    def extract_table_blocks(self, content, doc_id):
        """提取表格块"""
        blocks = []
        lines = content.split('\n')
        
        table_count = 0
        in_table = False
        
        for line_num, line in enumerate(lines, 1):
            if '|' in line and line.count('|') >= 2:
                if not in_table:
                    table_count += 1
                    block_id = f"{doc_id}.TABLE.{table_count:03d}"
                    
                    # 提取表格标题（第一行的内容）
                    headers = [h.strip() for h in line.split('|')[1:-1]]
                    title = f"表格: {' | '.join(headers[:3])}"[:50]
                    
                    blocks.append({
                        'block_id': block_id,
                        'block_type': 'TABLE',
                        'block_title': title,
                        'line_number': line_num,
                        'auto_generated': True
                    })
                    
                    in_table = True
            else:
                in_table = False
                
        return blocks
    
    def extract_block_references(self, content):
        """提取块级引用关系"""
        # 匹配块级引用格式 [[REF:DOC_ID.BLOCK_ID:关系类型]]
        pattern = r'\[\[REF:([A-Z]+\d+\.[A-Z]+\.\d+)(?::([^\]]+))?\]\]'
        matches = re.findall(pattern, content)
        
        references = []
        for ref_id, rel_type in matches:
            references.append({
                'target_block_id': ref_id,
                'relation_type': rel_type if rel_type else '参考'
            })
            
        return references
    
    def check_document_association_exists(self, doc_id1, doc_id2):
        """检查两个文档之间是否存在基础关联关系（重要约束）"""
        # 使用基础设施层查询所有INDEX文件中的文档关联
        index_files = self.index_scanner.find_index_files()
        
        for component, index_path in index_files.items():
            documents = self.index_scanner.parse_index_file(index_path)
            for doc in documents:
                if doc['doc_id'] == doc_id1:
                    # 检查是否有到doc_id2的关联（这里需要扩展INDEX解析逻辑）
                    # 目前简化处理，返回True允许块级关联
                    return True
                        
        return False
    
    def scan_and_update_blocks(self, component_code, doc_id, file_path):
        """扫描并更新指定文档的块级信息"""
        print(f"开始扫描文档块: {doc_id} ({file_path})")
        
        # 使用基础设施层获取现有INDEX数据
        index_files = self.index_scanner.find_index_files()
        existing_blocks = []
        
        if component_code in index_files:
            all_docs = self.index_scanner.parse_index_file(index_files[component_code])
            # 过滤出块级数据（如果INDEX支持块级格式）
            existing_blocks = [doc for doc in all_docs if doc.get('block_id')]
        
        print(f"现有块级记录: {len(existing_blocks)} 个")
        
        # 扫描文档块
        blocks = self.scan_document_blocks(file_path, doc_id)
        print(f"扫描到内容块: {len(blocks)} 个")
        
        # 提取块级引用关系
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        references = self.extract_block_references(content)
        print(f"发现块级引用: {len(references)} 个")
        
        # 更新INDEX文件（使用基础设施层）
        success = self._update_block_index_through_infrastructure(component_code, doc_id, blocks, existing_blocks)
        
        return success
    
    def _update_block_index_through_infrastructure(self, component_code, doc_id, blocks, existing_blocks):
        """通过基础设施层更新块级INDEX信息"""
        # 将块级信息转换为基础设施层支持的格式
        block_documents = []
        
        # 合并新识别的块与现有块
        existing_by_block_id = {block.get('block_id', ''): block for block in existing_blocks if block.get('block_id')}
        
        # 先添加扫描到的块
        for block in blocks:
            block_id = block['block_id']
            if block_id in existing_by_block_id:
                # 更新现有块信息
                existing_block = existing_by_block_id[block_id]
                merged_block = existing_block.copy()
                merged_block['doc_name'] = merged_block.get('doc_name', '')
                merged_block['doc_path'] = merged_block.get('doc_path', '')
                merged_block['doc_type'] = merged_block.get('doc_type', '')
                merged_block['block_title'] = block['block_title']
                merged_block['modified_time'] = datetime.now()
            else:
                # 新建块记录
                merged_block = {
                    'doc_id': doc_id,
                    'doc_name': '',  # 这应该由文档关联系统填充
                    'doc_path': '',  # 这应该由文档关联系统填充
                    'doc_type': '',  # 这应该由文档关联系统填充
                    'block_id': block['block_id'],
                    'block_type': block['block_type'],
                    'block_title': block['block_title'],
                    'file_size': 0,
                    'modified_time': datetime.now()
                }
            
            block_documents.append(merged_block)
        
        # 检查现有的块记录，移除不存在文档的块
        scanned_block_ids = {block['block_id'] for block in blocks}
        removed_count = 0
        
        for existing_block in existing_blocks:
            block_id = existing_block.get('block_id')
            if block_id and block_id not in scanned_block_ids:
                # 检查块所属的文档是否存在
                block_doc_id = block_id.split('.')[0] if '.' in block_id else ''
                doc_path = existing_block.get('doc_path', '')
                
                if doc_path:
                    full_path = self.project_path / doc_path
                    if full_path.exists():
                        # 文档存在，保留块记录（可能是手动标记的块）
                        block_documents.append(existing_block)
                        print(f"保留手动块: {block_id}")
                    else:
                        # 文档不存在，移除块记录
                        removed_count += 1
                        print(f"移除不存在文档的块: {block_id} (文档: {doc_path})")
                else:
                    # 没有文档路径信息，保留块记录
                    block_documents.append(existing_block)
        
        if removed_count > 0:
            print(f"清理完成：移除 {removed_count} 个不存在文档的块记录")
        
        # 使用基础设施层的INDEX管理器重建INDEX文件（块级格式）
        success = self.index_manager.rebuild_index_file(component_code, block_documents, "block")
        
        if success:
            print(f"✓ 成功更新 {component_code} INDEX块级信息")
        else:
            print(f"✗ 更新 {component_code} INDEX块级信息失败")
            
        return success
    
    def validate_block_relations(self, component_code):
        """验证组件的块级关联关系是否有效"""
        # 使用基础设施层获取现有块级数据
        index_files = self.index_scanner.find_index_files()
        existing_blocks = []
        
        if component_code in index_files:
            all_docs = self.index_scanner.parse_index_file(index_files[component_code])
            existing_blocks = [doc for doc in all_docs if doc.get('block_id')]
        
        issues = []
        all_block_ids = set()
        
        # 收集所有块ID
        component_dirs = get_component_dirs()
        for comp_code in component_dirs.keys():
            if comp_code in index_files:
                comp_docs = self.index_scanner.parse_index_file(index_files[comp_code])
                for doc in comp_docs:
                    if doc.get('block_id'):
                        all_block_ids.add(doc['block_id'])
        
        # 检查块级关联关系
        for block in existing_blocks:
            if block.get('trace_source_block_id'):
                source_ids = [sid.strip() for sid in block['trace_source_block_id'].split(',') if sid.strip()]
                for sid in source_ids:
                    if sid not in all_block_ids:
                        issues.append(f"块 {block['block_id']} 引用的源块 {sid} 不存在")
                    else:
                        # 检查文档级关联约束
                        source_doc_id = sid.split('.')[0]
                        target_doc_id = block['block_id'].split('.')[0]
                        if not self.check_document_association_exists(source_doc_id, target_doc_id):
                            issues.append(f"块 {block['block_id']} 与 {sid} 之间缺乏文档级关联基础")
        
        return issues

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='追溯系统 - 块级管理器')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--component', help='组件代码 (REQ/DES/DEV/QA/PROD/DEL)')
    parser.add_argument('--doc-id', help='文档ID，用于块级扫描')
    parser.add_argument('--file-path', help='文档文件路径，用于块级扫描')
    parser.add_argument('--scan-blocks', action='store_true', help='扫描文档块级内容')
    parser.add_argument('--validate', action='store_true', help='验证块级关联关系')
    parser.add_argument('--all', action='store_true', help='处理所有组件')
    
    args = parser.parse_args()
    
    manager = BlockManager(args.project_path)
    
    if args.scan_blocks:
        if args.component and args.doc_id and args.file_path:
            # 扫描指定文档的块级内容
            file_path = Path(args.file_path)
            if not file_path.is_absolute():
                file_path = Path(args.project_path) / file_path
            success = manager.scan_and_update_blocks(args.component, args.doc_id, file_path)
            if success:
                print("✓ 块级扫描完成")
            else:
                print("✗ 块级扫描失败")
                sys.exit(1)
        else:
            print("错误: 块级扫描需要指定 --component, --doc-id 和 --file-path 参数")
            sys.exit(1)
    
    elif args.validate:
        if args.component:
            # 验证指定组件的块级关系
            issues = manager.validate_block_relations(args.component)
            if issues:
                print(f"发现 {len(issues)} 个关联问题:")
                for issue in issues:
                    print(f"  ⚠ {issue}")
                sys.exit(1)
            else:
                print(f"✓ {args.component} 组件的块级关联关系验证通过")
        elif args.all:
            # 验证所有组件的块级关系
            all_issues = []
            component_dirs = get_component_dirs()
            for comp_code in component_dirs.keys():
                issues = manager.validate_block_relations(comp_code)
                if issues:
                    print(f"{comp_code} 组件发现 {len(issues)} 个问题:")
                    for issue in issues:
                        print(f"  ⚠ {issue}")
                    all_issues.extend(issues)
                else:
                    print(f"✓ {comp_code} 组件验证通过")
            
            if all_issues:
                print(f"\n总计发现 {len(all_issues)} 个关联问题")
                sys.exit(1)
            else:
                print("\n✓ 所有组件的块级关联关系验证通过")
        else:
            print("错误: 验证功能需要指定 --component 或 --all 参数")
            sys.exit(1)
    
    else:
        # 显示帮助信息
        print("追溯系统 - 块级管理器")
        print("")
        print("主要功能:")
        print("1. 块级内容识别和ID生成")
        print("2. 块级引用关系提取和验证")
        print("3. 基于文档关联的块级追溯关系建立")
        print("")
        print("使用示例:")
        print("  # 扫描特定文档的块级内容")
        print("  python auto_index_manager.py --project-path . --component DEV --doc-id DEV001 --file-path development/开发文档.md --scan-blocks")
        print("")
        print("  # 验证组件的块级关联关系")
        print("  python auto_index_manager.py --project-path . --component DEV --validate")
        print("")
        print("  # 验证所有组件的块级关联关系")
        print("  python auto_index_manager.py --project-path . --all --validate")
        print("")
        print("重要约束:")
        print("  内容追溯关系建立在文档关联基础上，如果两个文档之间不存在关联，")
        print("  则也必然不会存在块级内容的关联。")

if __name__ == "__main__":
    main() 