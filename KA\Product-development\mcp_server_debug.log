2025-07-03 21:15:16,230 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 21:15:16,230 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 21:15:16,230 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development
2025-07-03 21:15:16,230 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv\\Lib\\site-packages', 'D:\\Pro Packages\\50_mcp_src\\mcp-feedback-enhanced\\src']
2025-07-03 21:15:17,292 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 21:15:17,345 - mcp.server.fastmcp.tools.tool_manager - WARNING - Tool already exists: test_create_directory
2025-07-03 21:15:17,345 - __main__ - INFO - 🚀 Starting MCP Server...
2025-07-03 21:15:17,351 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 21:21:00,743 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 21:21:00,743 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 21:21:00,743 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development
2025-07-03 21:21:00,744 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv\\Lib\\site-packages', 'D:\\Pro Packages\\50_mcp_src\\mcp-feedback-enhanced\\src']
2025-07-03 21:21:01,368 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 21:21:01,412 - mcp.server.fastmcp.tools.tool_manager - WARNING - Tool already exists: test_create_directory
2025-07-03 21:21:01,412 - __main__ - INFO - 🚀 Starting MCP Server...
2025-07-03 21:21:01,418 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 22:24:43,629 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 22:24:43,629 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 22:24:43,629 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development
2025-07-03 22:24:43,630 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv\\Lib\\site-packages', 'D:\\Pro Packages\\50_mcp_src\\mcp-feedback-enhanced\\src']
2025-07-03 22:24:44,463 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 22:24:44,509 - mcp.server.fastmcp.tools.tool_manager - WARNING - Tool already exists: test_create_directory
2025-07-03 22:24:44,509 - __main__ - INFO - 🚀 Starting MCP Server...
2025-07-03 22:24:44,515 - asyncio - DEBUG - Using proactor: IocpProactor
