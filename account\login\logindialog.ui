<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>loginDialog</class>
 <widget class="QDialog" name="loginDialog">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>340</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>340</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>493</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>CSPC</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../resource.qrc">
    <normaloff>:/icon/cspc1.jpg</normaloff>
    <normalon>:/icon/cspc1.jpg</normalon>:/icon/cspc1.jpg</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">#loginDialog
{
	border-radius: 6px;
	border-image: url(:/icon/pillars_of_creation.jpg);/**/
	border:2px solid gray;
}
QLineEdit{
	background-color: rgba(255, 255, 255, 0.2);
	color:rgb(255,255,255);
	border:2px solid gray;
	border-radius:6px; /*边框棱角值越大，越圆润*/
	/*border-style:none; /*无边框*/	
	font: 10pt &quot;黑体&quot;;
}
QLineEdit:hover
{
	background-color: rgba(85, 85, 127,0.6);
	border:2px solid gray;
	border-radius: 10px;
}
QLabel
{
	font: 10pt &quot;黑体&quot;;
	color: rgb(255, 255, 255);
}
#pButtonLogin
{
	background-color: rgba(0, 85, 0,0.7);
	color:  rgb(255, 255, 255);
	font: 20pt &quot;黑体&quot;;
	border-radius: 10px；	
}
#pButtonLogin:hover
{
	background-color: rgba(85, 85, 127,0.5);
	color: yellow;
	border-radius: 10px;
}</string>
  </property>
  <widget class="QWidget" name="gridLayoutWidget_2">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>90</y>
     <width>231</width>
     <height>97</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="1" column="0">
     <widget class="QLabel" name="label_2">
      <property name="text">
       <string>Password:</string>
      </property>
      <property name="buddy">
       <cstring>lEditPword</cstring>
      </property>
     </widget>
    </item>
    <item row="0" column="2">
     <widget class="QLineEdit" name="lEditAccound">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
     </widget>
    </item>
    <item row="0" column="0">
     <widget class="QLabel" name="label">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="text">
       <string>Username:</string>
      </property>
      <property name="buddy">
       <cstring>lEditAccound</cstring>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QLineEdit" name="lEditPword"/>
    </item>
   </layout>
  </widget>
  <widget class="QPushButton" name="pButtonLogin">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>240</y>
     <width>231</width>
     <height>41</height>
    </rect>
   </property>
   <property name="text">
    <string>login</string>
   </property>
  </widget>
  <widget class="QWidget" name="horizontalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>130</x>
     <y>180</y>
     <width>231</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QPushButton" name="pButtonRegister">
      <property name="styleSheet">
       <string notr="true">QPushButton
{
	background-color: rbga(255,255,255,0);
	color: rgb(255,255,255);
	border-style:none;
	font: 9pt &quot;黑体&quot;;
}
QPushButton:hover
{
	/*background-color: rgba(85, 85, 127,0.7);*/
	color: rgb(255, 255, 0);
	border-radius: 9px &quot;黑体&quot;;
}</string>
      </property>
      <property name="text">
       <string>register?</string>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>40</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <widget class="QPushButton" name="pButtonPasswordForget">
      <property name="styleSheet">
       <string notr="true">QPushButton
{
	background-color: rbga(255,255,255,0);
	color: rgb(255,255,255);
	border-style:none;
	font: 9pt &quot;黑体&quot;;
}
QPushButton:hover
{
	color: yellow;
	border-radius: 9px &quot;黑体&quot;;
}</string>
      </property>
      <property name="text">
       <string>forget?</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="verticalLayoutWidget">
   <property name="geometry">
    <rect>
     <x>379</x>
     <y>310</y>
     <width>121</width>
     <height>31</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="statusLayout">
    <item>
     <widget class="QLabel" name="versionLabel">
      <property name="text">
       <string/>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="../../resource.qrc"/>
 </resources>
 <connections/>
</ui>
