#ifndef _BAR_CUSTOMPLOT_H_
#define _BAR_CUSTOMPLOT_H_

#include <QMessageBox>
#include <QDebug>
#include <qcustomplot.h>

#include "IPlot.h"

typedef struct{
    QSharedPointer<QCPBarsDataContainer> fgHandleData;
    QSharedPointer<QCPBarsDataContainer> cycleHandleData;
}CentroidDistData;

class CBarCustomPlot : public IPlot
{
    Q_OBJECT
public:
    CBarCustomPlot(QCustomPlot &customPlot, StFigureInfo* st_figure_info_);
    ~CBarCustomPlot();

    void createFigure(QCustomPlot &customPlot, StFigureInfo* st_figure_info_);

    QSharedPointer<QCPBarsDataContainer> dataHandle(const QVector<uint16_t> &data);
    QSharedPointer<QCPBarsDataContainer> dataHandle1(uint16_t start_index, const QVector<uint16_t> &data);
    void customPlotShow(const QSharedPointer<QCPBarsDataContainer> &data_show);
    void afterGlowGraphNumChange(QVector<QCPGraph*> &mainGraphCosPoint, QPen *pen, uint8_t graph_num, uint8_t &graph_num_cur);
    void wavePointTextShow(const uint8_t &index, QCPItemText** wavePointText, QPen *pen, QVector<double> centroid_arr, QVector<uint16_t> dist_arr, float position_offset, CentroidDistData* centroid_dist);
    void subCentroidShow(QCPGraph** subCentroidGraph, QPen *pen, QVector<QCPGraphData> dataCentroidSca);

    void createFigure() override;

    uint m_length;
private:
    QCustomPlot *customPlots = nullptr;

    QCPBars *m_bar_ = nullptr;

//    QCPGraph *m_mainGraphCos = nullptr; //绘图元素 折线图(QCPGraph) 实时

//    QCPGraph *m_mainGraphCosPoint = nullptr;
//    QCPGraph *subGraphRandoPoint = nullptr;
    QCPItemText *wavePacketText = nullptr;
    QCPAxisRect *mainRectRight = nullptr;
    QCPAxisRect *subRectLeft = nullptr;
    QCPAxisRect *subRectRight = nullptr;

signals:

private slots:
    void mouseDouClick(QMouseEvent* event);
};

#endif // CUSTOMPLOTWAVE_H
