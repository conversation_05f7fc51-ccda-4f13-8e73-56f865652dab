# 多层级产品结构可视化指南

本文档提供多层级产品结构可视化工具的使用方法和最佳实践，帮助您生成清晰直观的产品结构图表。

## 功能概述

多层级产品结构可视化工具能够：

1. 将配置文件中定义的多层级产品结构转换为图形表示
2. 支持多种输出格式，包括PNG、SVG和PDF
3. 提供多种主题样式，适应不同场合的展示需求
4. 支持高亮显示特定实例，便于关注点分析
5. 根据层级关系自动生成连接，展示产品结构的层次关系

## 安装前提

使用本工具需要安装以下软件：

1. **Python 3.6+**：用于运行可视化脚本
2. **GraphViz**：用于生成图形，安装方法见[官方网站](https://graphviz.org/download/)

### 安装GraphViz

#### Windows系统

1. 从[GraphViz官方下载页面](https://graphviz.org/download/)下载安装包
2. 运行安装程序，按照向导完成安装
3. 确保将GraphViz的bin目录添加到PATH环境变量中

#### macOS系统

使用Homebrew安装：

```bash
brew install graphviz
```

#### Linux系统

Ubuntu/Debian:

```bash
sudo apt-get install graphviz
```

CentOS/RHEL:

```bash
sudo yum install graphviz
```

## 基本使用

### 命令格式

```bash
python scripts/project_initialization/visualize_product_structure.py --config="__level_config.json" --output="product_structure.png"
```

### 参数说明

| 参数 | 说明 | 默认值 | 可选值 |
|------|------|--------|--------|
| `--config` | 层级配置文件路径 | 必填 | - |
| `--output` | 输出图片路径 | `product_structure.png` | 任意有效文件路径 |
| `--format` | 输出格式 | `png` | `png`, `svg`, `pdf` |
| `--title` | 图表标题 | 产品名称 | 任意文本 |
| `--highlight` | 高亮显示的实例名称 | 无 | 配置中的任意实例名称 |
| `--theme` | 图表主题 | `default` | `default`, `light`, `dark`, `colorful` |
| `--show-descriptions` | 是否显示描述文本 | `False` | - |

## 常见使用场景

### 生成项目概览图

生成简单的项目结构图，使用默认主题：

```bash
python scripts/project_initialization/visualize_product_structure.py --config="项目路径/__level_config.json" --output="项目路径/product_overview.png"
```

### 为演示文档生成高质量矢量图

生成SVG格式的结构图，使用colorful主题，适合在演示文档中使用：

```bash
python scripts/project_initialization/visualize_product_structure.py --config="项目路径/__level_config.json" --output="项目路径/product_structure.svg" --format="svg" --theme="colorful"
```

### 高亮特定产品线

生成结构图并高亮显示特定产品实例，便于关注点分析：

```bash
python scripts/project_initialization/visualize_product_structure.py --config="项目路径/__level_config.json" --output="项目路径/highlighted_product.png" --highlight="马桶脚感"
```

### 生成暗色主题图表（适合夜间展示）

使用暗色主题生成结构图，适合在暗色背景的演示环境中使用：

```bash
python scripts/project_initialization/visualize_product_structure.py --config="项目路径/__level_config.json" --output="项目路径/dark_theme.png" --theme="dark"
```

## VSCode任务集成

您可以将产品结构可视化功能集成到VSCode任务中，方便快速生成图表。以下是添加到`.vscode/tasks.json`的任务配置示例：

```json
{
    "label": "生成产品结构图",
    "type": "shell",
    "command": "python",
    "args": [
        "${workspaceFolder}/scripts/project_initialization/visualize_product_structure.py",
        "--config=${input:multilevelConfigPath}",
        "--output=${workspaceFolder}/reports/product_structure.png",
        "--theme=${input:visualTheme}"
    ],
    "problemMatcher": [],
    "presentation": {
        "reveal": "always",
        "panel": "new"
    }
}
```

## 最佳实践

1. **结构清晰**：确保您的层级配置合理，避免过多的交叉连接，保持结构清晰
2. **输出格式选择**：
   - 使用PNG格式用于一般展示和文档嵌入
   - 使用SVG格式用于高质量打印和矢量图需求
   - 使用PDF格式用于正式文档附件
3. **主题选择**：
   - `default`主题：适合一般用途
   - `light`主题：适合白色背景的文档
   - `dark`主题：适合暗色背景的演示
   - `colorful`主题：适合强调层级差异
4. **高亮使用**：聚焦讨论特定产品线时使用高亮功能，使关注点更加明确

## 结合需求追溯矩阵

多层级产品结构图可以与需求追溯矩阵配合使用，提供更全面的产品开发视图：

1. 使用结构图展示产品的组织架构和层级关系
2. 使用需求追溯矩阵展示需求的流转和实现情况
3. 在演示或文档中同时展示两者，帮助团队理解产品结构与需求的映射关系

## 常见问题

### Q: 执行脚本时提示"未安装GraphViz"

A: 请确保正确安装GraphViz，并将其bin目录添加到系统PATH环境变量中。

### Q: 生成的图形太大或太小

A: 调整输出格式，考虑使用SVG格式，这样可以任意缩放而不损失质量。

### Q: 节点名称显示不完整

A: 考虑使用更简短的实例名称，或调整图形大小。

### Q: 连接线交叉过多，图形混乱

A: 重新规划您的产品结构，减少跨层级的复杂关系，或考虑分解为多个单独的图形。
