#ifndef FACULA_DATA_ADAPTER_H
#define FACULA_DATA_ADAPTER_H

#include "common/ImageData.h"
#include <QDebug>
#include <QVector>

/**
 * @brief 光斑数据适配器
 *
 * 负责在QVector<QVector<uint32_t>>和ImageProcessing::ImageDataU32之间进行数据转换
 */
class FaculaDataAdapter {
  public:
    /**
     * @brief 将QVector矩阵转换为ImageDataU32
     * @param qvectorMatrix QVector格式的矩阵数据
     * @return ImageProcessing::ImageDataU32
     */
    static ImageProcessing::ImageDataU32 qvectorToImageData(const QVector<QVector<uint32_t>> &qvectorMatrix);

    /**
     * @brief 将ImageDataU32转换为QVector矩阵
     * @param imageData ImageDataU32格式的数据
     * @return QVector<QVector<uint32_t>>
     */
    static QVector<QVector<uint32_t>> imageDataToQVector(const ImageProcessing::ImageDataU32 &imageData);

    /**
     * @brief 将ImageDataU32的数据复制到已存在的QVector矩阵中
     * @param imageData 源ImageDataU32数据
     * @param qvectorMatrix 目标QVector矩阵（会被修改）
     * @return true if successful, false otherwise
     */
    static bool copyImageDataToQVector(const ImageProcessing::ImageDataU32 &imageData, QVector<QVector<uint32_t>> &qvectorMatrix);

    /**
     * @brief 验证QVector矩阵的有效性
     * @param qvectorMatrix QVector格式的矩阵
     * @return true if valid, false otherwise
     */
    static bool validateQVectorMatrix(const QVector<QVector<uint32_t>> &qvectorMatrix);

    /**
     * @brief 验证ImageDataU32的有效性
     * @param imageData ImageDataU32数据
     * @return true if valid, false otherwise
     */
    static bool validateImageData(const ImageProcessing::ImageDataU32 &imageData);

    /**
     * @brief 获取QVector矩阵的尺寸信息
     * @param qvectorMatrix QVector格式的矩阵
     * @param width 输出宽度
     * @param height 输出高度
     * @return true if successful, false otherwise
     */
    static bool getQVectorDimensions(const QVector<QVector<uint32_t>> &qvectorMatrix, uint32_t &width, uint32_t &height);

    /**
     * @brief 创建指定尺寸的空QVector矩阵
     * @param width 宽度
     * @param height 高度
     * @param defaultValue 默认填充值
     * @return QVector<QVector<uint32_t>>
     */
    static QVector<QVector<uint32_t>> createEmptyQVectorMatrix(uint32_t width, uint32_t height, uint32_t defaultValue = 0);

    /**
     * @brief 调整QVector矩阵的尺寸
     * @param qvectorMatrix 要调整的矩阵
     * @param newWidth 新宽度
     * @param newHeight 新高度
     * @param defaultValue 新元素的默认值
     */
    static void resizeQVectorMatrix(QVector<QVector<uint32_t>> &qvectorMatrix, uint32_t newWidth, uint32_t newHeight, uint32_t defaultValue = 0);

  private:
    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    static void logDebug(const QString &message);

    /**
     * @brief 记录错误信息
     * @param message 错误信息
     */
    static void logError(const QString &message);
};

#endif  // FACULA_DATA_ADAPTER_H
