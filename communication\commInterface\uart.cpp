#include "uart.h"
#include <QDebug>
#include <QApplication>
#include "qLog.h"

CUART::CUART(const QString &port_name, const uint &baud):
    mc_serial_port_(new QSerialPort)
  , m_baud(baud)
{
    m_port_name = port_name; //

}

CUART::~CUART()
{
    delete mc_serial_port_;
}

bool CUART::openUartPort(QSerialPort *serial_port, const QString &port_name, const uint &baud)
{
    serial_port->setPortName(port_name);
    serial_port->setBaudRate(baud);

    serial_port->setDataBits(QSerialPort::Data8);
    serial_port->setParity(QSerialPort::NoParity);
    serial_port->setStopBits(QSerialPort::OneStop);
    serial_port->setFlowControl(QSerialPort::NoFlowControl);


    if(!serial_port->open(QIODevice::ReadWrite))
    {
        qDebug() << "-e uart/ port open fail";
        //QMessageBox::information(this, "error", m_port_name + " open failed");
        return false;
    }
    return true;
}

/**
 * @brief 检测串口是否打开
 * @return
 */

void CUART::closeUartPort(QSerialPort *serial_port)
{
    serial_port->close();
}


bool CUART::checkUartPort(QSerialPort *serial_port)
{
    if(serial_port->isOpen()) return true;
    else return false;
}

/**
 * @brief 扫描端口列表
 * @return
 */
QStringList CUART::detectPort()
{
    QStringList serial_port_list;
    serial_port_list.clear();
    foreach (const QSerialPortInfo &info,  QSerialPortInfo::availablePorts())//遍历端口  并返回信息
    {
        serial_port_list.push_back(info.portName());
    }
    return serial_port_list;
}

//void CUART::serialPortDetect()
//{
//    QList<QSerialPortInfo> serialPortList(QSerialPortInfo::availablePorts());
//    QSerialPort tempSerial;
//    for(int i=0;i<serialPortList.count();i++)
//    {
//        tempSerial.setPort(serialPortList.at(i));
//        port_series = serialPortList.at(i).serialNumber();
////        ui->ReceivedMesEdit->zoomIn(serialPortList.at(i).productIdentifier());
////        ui->ReceivedMesEdit->zoomIn(serialPortList.at(i).vendorIdentifier());
//        tempSerial.close();//close the device?
//    }
//}
/**
 * @brief 判断端口丢失：原端口，现有端口。原端口存在，显示原端口
 * @param port_list_
 * @param last_port_name
 * @return
 */
bool CUART::scanPort(QStringList *port_list_, const QString &last_port_name) {
    *port_list_ = detectPort();

    /*列表刷新*/
    for(QList<QString>::Iterator it = port_list_->begin(); it != port_list_->end(); it++) {
        if((*it) == last_port_name) return true;
    }

    /*串口丢失*/
    if(this->checkPort()) {
        this->closePort();
    }
    return false;
}

QString CUART::getPortName(void) {
    return m_port_name;
}
/**
 * @brief 打开端口
 * @return
 */
bool CUART::openPort(void) {
    if(checkPort()) {
        qDebug() << "-i uart/ serial_port opened";
        return false;
    }
    else
        return openUartPort(mc_serial_port_, m_port_name, m_baud);
}

/**
 * @brief CUART::关闭端口
 */
void CUART::closePort(void) {
    if(!checkPort()) {
        qDebug() << "-i uart: serial_port closed";
        //QMessageBox::information(this, "fatal", "serial not definition");
        return;
    }
    else
        closeUartPort(mc_serial_port_);
}

/**
 * @brief 检测端口是否打开
 * @return
 */
bool CUART::checkPort(void) {
    return checkUartPort(mc_serial_port_);
}

#if 1
void CUART::setBufferSize(uint16_t bytes)
{
    mc_serial_port_->setReadBufferSize(bytes);
}

/**
 * @brief 阻塞方式读取
 * @return
 */
QByteArray CUART::read(uint16_t wait_ms) {
    //  mc_serial_port_->waitForReadyRead(wait_ms);
    qApp->processEvents(); //优先处理消息队列，防止队列信息一直增加导致内存溢出
    return mc_serial_port_->readAll();
}
#else
/**
 * @brief 中断方式读取
 * QObject::connect(mc_serial_port_,&CUART::readyRead,this,&CClass::serialPortReceive);
 * @param wait_ms
 * @return
 */
QByteArray CUART::read(uint16_t wait_ms)
{
    qApp->processEvents(); //优先处理消息队列，防止队列信息一直增加导致内存溢出
    return mc_serial_port_->readAll();//读取数据
}
#endif

/**
 * @brief CUART::write
 * @param data
 * @return
 */
bool CUART::write(QByteArray data) {
    return mc_serial_port_->write(data);
}
