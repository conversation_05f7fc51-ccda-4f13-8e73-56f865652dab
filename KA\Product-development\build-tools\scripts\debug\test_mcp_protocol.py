#!/usr/bin/env python3
"""
MCP 协议调试脚本
用于测试MCP服务器的协议兼容性
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path

def test_current_server():
    """测试当前的MCP服务器实现"""
    print("=== 测试当前MCP服务器实现 ===")
    
    # 服务器脚本路径
    server_script = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_stdio.py"
    
    if not server_script.exists():
        print(f"❌ 服务器脚本不存在: {server_script}")
        return False
    
    print(f"✓ 服务器脚本存在: {server_script}")
    
    try:
        # 启动服务器进程
        print("\n--- 启动服务器进程 ---")
        process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 发送初始化消息
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print(f"发送初始化消息: {json.dumps(init_message, indent=2)}")
        
        # 发送消息并获取响应
        stdout, stderr = process.communicate(json.dumps(init_message) + "\n", timeout=10)
        
        print(f"\n--- 服务器响应 ---")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        print(f"返回码: {process.returncode}")
        
        if stdout:
            try:
                response = json.loads(stdout.strip())
                print(f"✓ 成功解析JSON响应: {json.dumps(response, indent=2)}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        else:
            print("❌ 没有收到响应")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 服务器响应超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def check_mcp_sdk():
    """检查是否安装了官方MCP SDK"""
    print("\n=== 检查官方MCP SDK ===")
    
    try:
        import mcp
        print(f"✓ 官方MCP SDK已安装，版本: {mcp.__version__}")
        return True
    except ImportError:
        print("❌ 官方MCP SDK未安装")
        print("请运行: pip install mcp")
        return False

def check_cursor_config():
    """检查Cursor MCP配置"""
    print("\n=== 检查Cursor MCP配置 ===")
    
    config_files = [
        ".cursor/mcp.json",
        ".cursor/mcp_env.json", 
        ".cursor/mcp_absolute.json",
        ".cursor/mcp_simple.json"
    ]
    
    for config_file in config_files:
        config_path = Path(config_file)
        if config_path.exists():
            print(f"✓ 配置文件存在: {config_file}")
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"  配置内容: {json.dumps(config, indent=2)}")
            except Exception as e:
                print(f"  ❌ 读取配置失败: {e}")
        else:
            print(f"❌ 配置文件不存在: {config_file}")

def test_server_startup():
    """测试服务器启动"""
    print("\n=== 测试服务器启动 ===")
    
    server_script = Path(__file__).parent.parent / "mcp-server_local_integrations" / "product_development_mcp_stdio.py"
    
    try:
        # 测试服务器是否可以启动
        process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一小段时间看是否有错误
        try:
            stdout, stderr = process.communicate(timeout=3)
            print(f"服务器退出，返回码: {process.returncode}")
            if stderr:
                print(f"错误输出: {stderr}")
        except subprocess.TimeoutExpired:
            print("✓ 服务器启动成功，正在运行")
            process.terminate()
            process.wait()
        
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")

if __name__ == "__main__":
    print("MCP 协议调试脚本")
    print("==================")
    
    # 运行所有测试
    test_current_server()
    check_mcp_sdk()
    check_cursor_config()
    test_server_startup()
    
    print("\n=== 建议的解决方案 ===")
    print("1. 安装官方MCP SDK: pip install mcp")
    print("2. 使用官方SDK重新实现服务器")
    print("3. 确保Cursor配置文件正确")
    print("4. 重启Cursor以加载新配置") 