#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
HTML渲染器

将可视化数据渲染为交互式HTML页面
"""

import json
from datetime import datetime
from typing import Dict, Any
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import VisualizationRenderer, VisualizationData
from core.component_utils import component_manager

class EnumEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理枚举类型"""
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)

class HTMLRenderer(VisualizationRenderer):
    """HTML渲染器 - 单一职责：生成交互式HTML页面"""
    
    def render(self, data: VisualizationData, **kwargs) -> str:
        """渲染可视化数据为HTML"""
        return self._generate_html(data, **kwargs)
    
    def get_supported_formats(self):
        """获取支持的渲染格式"""
        return ["html", "web"]
    
    def _generate_html(self, data: VisualizationData, **kwargs) -> str:
        """生成完整的HTML页面"""
        
        # 将数据转换为JSON格式，处理枚举类型
        def serialize_data(obj):
            if hasattr(obj, '__dict__'):
                result = {}
                for key, value in obj.__dict__.items():
                    if hasattr(value, 'value'):  # 处理枚举类型
                        result[key] = value.value
                    else:
                        result[key] = value
                return result
            elif hasattr(obj, 'value'):  # 处理枚举类型
                return obj.value
            return obj
        
        # 序列化节点数据
        nodes_data = []
        for node in data.nodes:
            node_dict = serialize_data(node)
            nodes_data.append(node_dict)
        
        # 序列化边数据
        edges_data = []
        for edge in data.edges:
            edge_dict = serialize_data(edge)
            edges_data.append(edge_dict)
        
        # 构建可视化数据
        viz_data = {
            "title": data.title,
            "mode": data.mode.value if hasattr(data.mode, 'value') else str(data.mode),
            "nodes": nodes_data,
            "edges": edges_data,
            "metadata": data.metadata
        }
        
        # 转换数据格式为JavaScript兼容格式
        js_data = {
            "title": viz_data["title"],
            "mode": viz_data["mode"],
            "nodes": [
                {
                    "id": node.get("id", ""),
                    "name": node.get("name", ""),
                    "type": node.get("type", "default"),
                    "component": node.get("component", ""),
                    **node.get("properties", {})
                }
                for node in viz_data["nodes"]
            ],
            "edges": [
                {
                    "source": edge.get("source", ""),
                    "target": edge.get("target", ""),
                    "type": edge.get("type", "default"),
                    **edge.get("properties", {})
                }
                for edge in viz_data["edges"]
            ],
            "metadata": viz_data["metadata"]
        }
        
        html_template = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{data.title}</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        {self._generate_css()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>{data.title}</h1>
            <div class="mode-selector">
                {self._generate_mode_buttons(data.mode.value)}
            </div>
        </header>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="controls">
                    {self._generate_controls(data.mode.value)}
                </div>
                <div class="metadata">
                    {self._generate_metadata(data)}
                </div>
            </div>
            
            <div class="visualization-area">
                <svg id="main-graph" width="900" height="650">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>
        
        <div class="status-bar">
            {self._generate_status_bar(data)}
        </div>
    </div>
    
    <script>
        {self._generate_javascript(js_data, data.mode.value)}
    </script>
</body>
</html>'''
        
        return html_template
    
    def _convert_to_js_format(self, data: VisualizationData) -> Dict[str, Any]:
        """将数据转换为JavaScript兼容格式"""
        js_nodes = []
        for node in data.nodes:
            js_node = {
                "id": node.id,
                "name": node.name,
                "type": node.type,
                "component": node.component,
                **node.properties
            }
            js_nodes.append(js_node)
        
        js_edges = []
        for edge in data.edges:
            js_edge = {
                "source": edge.source,
                "target": edge.target,
                "type": edge.type,
                **edge.properties
            }
            js_edges.append(js_edge)
        
        return {
            "title": data.title,
            "mode": data.mode.value,
            "nodes": js_nodes,
            "edges": js_edges,
            "metadata": data.metadata
        }
    
    def _generate_css(self) -> str:
        """生成CSS样式 - 使用统一的组件颜色"""
        return f'''
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }}
        
        .container {{
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }}
        
        .header {{
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            height: auto;
        }}
        
        .header h1 {{
            margin: 0;
            color: #333;
            font-size: 24px;
        }}
        
        .mode-selector {{
            display: flex;
            gap: 10px;
        }}
        
        .mode-btn {{
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }}
        
        .mode-btn:hover {{
            background: #f0f0f0;
        }}
        
        .mode-btn.active {{
            background: #007bff;
            color: white;
            border-color: #007bff;
        }}
        
        .main-content {{
            display: flex;
            flex: 1;
            overflow: hidden;
            height: calc(100vh - 120px);
        }}
        
        .sidebar {{
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            padding: 20px;
            overflow-y: auto;
            flex-shrink: 0;
            height: 100%;
        }}
        
        .controls h3, .metadata h3 {{
            margin-top: 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }}
        
        .control-group {{
            margin-bottom: 20px;
        }}
        
        .control-group label {{
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }}
        
        .control-group select, .control-group input {{
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
        
        .control-group button {{
            width: 100%;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
            font-size: 12px;
            transition: background-color 0.3s ease;
        }}
        
        .control-group button:hover {{
            background: #0056b3;
        }}
        
        .control-group button:active {{
            background: #004085;
            transform: translateY(1px);
        }}
        
        .control-group button:disabled {{
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }}
        
        .control-group button.secondary {{
            background: #6c757d;
            font-size: 11px;
            padding: 6px;
        }}
        
        .control-group button.secondary:hover {{
            background: #545b62;
        }}
        
        .visualization-area {{
            flex: 1;
            background: white;
            position: relative;
            width: calc(100% - 300px);
            height: 100%;
            overflow: hidden;
        }}
        
        #main-graph {{
            width: 100%;
            height: 100%;
            display: block;
        }}
        
        .node {{
            cursor: move;
            stroke: #fff;
            stroke-width: 2px;
        }}
        
        .node:hover {{
            stroke-width: 3px;
        }}
        
        .node.highlighted {{
            stroke: #FFD700;
            stroke-width: 4px;
        }}
        
        .link {{
            fill: none;
            stroke-width: 2px;
            marker-end: url(#arrowhead);
        }}
        
        .link:hover {{
            stroke-width: 4px;
        }}
        
        .node-text {{
            font-size: 12px;
            text-anchor: middle;
            pointer-events: none;
            fill: white;
            font-weight: bold;
        }}
        
        .link-label {{
            font-size: 10px;
            text-anchor: middle;
            fill: #333;
            pointer-events: none;
        }}
        
        .component-area {{
            pointer-events: none;
        }}
        
        .area-label {{
            pointer-events: none;
            user-select: none;
        }}
        
        .status-bar {{
            background: #f8f9fa;
            padding: 10px 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
            flex-shrink: 0;
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .tooltip {{
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }}
        
        .tooltip-title {{
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
            color: #FFD700;
        }}
        
        .tooltip-item {{
            margin-bottom: 4px;
        }}
        
        .tooltip-label {{
            font-weight: bold;
            color: #87CEEB;
        }}
        
        .metadata-item {{
            margin-bottom: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 13px;
        }}
        
        .metadata-label {{
            font-weight: bold;
            color: #555;
        }}
        
        .metadata-value {{
            color: #777;
        }}
        '''
    
    def _generate_mode_buttons(self, current_mode: str) -> str:
        """生成模块切换按钮"""
        modes = {
            "all": {"name": "综合视图", "icon": "🌐"},
            "workflow": {"name": "工作流", "icon": "🔄"},
            "documents": {"name": "文档关联", "icon": "📋"},
            "traceability": {"name": "追溯链", "icon": "🔗"},
            "progress": {"name": "进度", "icon": "📊"},
            "structure": {"name": "产品结构", "icon": "🏗️"}
        }
        
        buttons_html = ""
        for mode_key, mode_info in modes.items():
            active_class = "active" if mode_key == current_mode else ""
            buttons_html += f'''
                <button class="mode-btn {active_class}" 
                        onclick="switchMode('{mode_key}')"
                        data-mode="{mode_key}">
                    <span class="mode-icon">{mode_info['icon']}</span>
                    <span class="mode-name">{mode_info['name']}</span>
                </button>
            '''
        
        return buttons_html
    
    def _generate_controls(self, mode: str) -> str:
        """生成控制面板"""
        return f'''
        <div class="controls">
            <h3>🎛️ 控制面板</h3>
            
            <div class="control-group">
                <label>布局算法</label>
                <select id="layoutSelect" onchange="changeLayout()">
                    <option value="force">力导向布局</option>
                    <option value="hierarchical">层次布局</option>
                    <option value="circular">环形布局</option>
                    <option value="grid">网格布局</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>节点过滤</label>
                <select id="nodeFilter" onchange="filterNodes()">
                    <option value="">全部节点</option>
                    <option value="REQ">需求 (REQ)</option>
                    <option value="DES">设计 (DES)</option>
                    <option value="DEV">开发 (DEV)</option>
                    <option value="QA">质量 (QA)</option>
                    <option value="PROD">生产 (PROD)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>搜索节点</label>
                <input type="text" id="searchInput" placeholder="输入节点ID或名称..." oninput="searchNodes()">
            </div>
            
            <div class="control-group">
                <button onclick="resetLayout()">重置布局</button>
                <button onclick="fitToView()">适应窗口</button>
                <button onclick="exportGraph()">导出图表</button>
            </div>
            
            <div class="control-group">
                <label>视图控制</label>
                <button class="secondary" onclick="resetZoom()">重置缩放</button>
                <button class="secondary" onclick="zoomIn()">放大 (+)</button>
                <button class="secondary" onclick="zoomOut()">缩小 (-)</button>
                <button class="secondary" onclick="centerView()">居中视图</button>
            </div>
            
            <div class="control-group">
                <label>快捷键说明</label>
                <div style="font-size: 11px; color: #666; line-height: 1.4;">
                    • 滚轮：缩放<br>
                    • 拖拽：平移视图<br>
                    • + / -：缩放<br>
                    • 0：重置缩放<br>
                    • Space：居中视图
                </div>
            </div>
        </div>
        '''
    
    def _generate_metadata(self, data: VisualizationData) -> str:
        """生成元数据显示"""
        items_html = ""
        
        for key, value in data.metadata.items():
            display_key = key.replace('_', ' ').title()
            items_html += f'''
            <div class="metadata-item">
                <span class="metadata-label">{display_key}:</span>
                <span class="metadata-value">{value}</span>
            </div>
            '''
        
        return f'''
        <div class="metadata">
            <h3>📊 数据统计</h3>
            {items_html}
        </div>
        '''
    
    def _generate_status_bar(self, data: VisualizationData) -> str:
        """生成状态栏"""
        node_count = len(data.nodes)
        edge_count = len(data.edges)
        mode = data.mode.value
        
        return f'''
        <div class="status-left">
            节点: <strong>{node_count}</strong> | 
            边: <strong>{edge_count}</strong> | 
            模式: <strong>{mode}</strong>
        </div>
        <div class="status-right">
            生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
        '''
    
    def _generate_javascript(self, graph_data: Dict[str, Any], mode: str) -> str:
        """生成JavaScript代码 - 使用统一的组件管理器"""
        
        # 获取组件颜色映射
        component_colors = {}
        relation_colors = {}
        
        for comp_id in component_manager.components.keys():
            component_colors[comp_id] = component_manager.get_component_color(comp_id)
        
        for rel_type in component_manager.relation_colors.keys():
            relation_colors[rel_type] = component_manager.get_relation_color(rel_type)
        
        return f'''
        // 全局状态管理
        const appState = {{
            currentMode: '{mode}',
            graphData: {json.dumps(graph_data, ensure_ascii=False, cls=EnumEncoder)},
            originalData: {json.dumps(graph_data, ensure_ascii=False, cls=EnumEncoder)},
            selectedNodes: new Set(),
            simulation: null,
            svg: null,
            width: 900,
            height: 650,
            componentColors: {json.dumps(component_colors, cls=EnumEncoder)},
            relationColors: {json.dumps(relation_colors, cls=EnumEncoder)},
            zoom: null,
            zoomContainer: null
        }};
        
        // 组件区域配置
        const componentAreas = {{}};
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {{
            initializeVisualization();
        }});
        
        function initializeVisualization() {{
            appState.svg = d3.select("#main-graph");
            
            // 动态获取容器尺寸
            const container = appState.svg.node().parentElement;
            appState.width = container.clientWidth;
            appState.height = container.clientHeight;
            
            // 设置SVG尺寸
            appState.svg
                .attr("width", appState.width)
                .attr("height", appState.height);
            
            // 初始化缩放和拖拽功能
            setupZoomAndPan();
            
            updateVisualization();
            
            // 窗口大小变化时重新调整
            window.addEventListener('resize', function() {{
                const newWidth = container.clientWidth;
                const newHeight = container.clientHeight;
                
                if (newWidth !== appState.width || newHeight !== appState.height) {{
                    appState.width = newWidth;
                    appState.height = newHeight;
                    
                    appState.svg
                        .attr("width", appState.width)
                        .attr("height", appState.height);
                    
                    updateVisualization();
                }}
            }});
        }}
        
        function setupZoomAndPan() {{
            // 创建缩放行为
            appState.zoom = d3.zoom()
                .scaleExtent([0.1, 10])  // 缩放范围：10%到1000%
                .on("zoom", function(event) {{
                    // 应用变换到缩放容器
                    appState.zoomContainer.attr("transform", event.transform);
                }});
            
            // 将缩放行为应用到SVG
            appState.svg.call(appState.zoom);
            
            // 创建缩放容器组
            appState.zoomContainer = appState.svg.append("g")
                .attr("class", "zoom-container");
            
            // 禁用默认的双击缩放，避免与节点点击冲突
            appState.svg.on("dblclick.zoom", null);
        }}
        
        function updateVisualization() {{
            // 清除现有内容（但保留缩放容器）
            appState.zoomContainer.selectAll("*").remove();
            
            // 重新添加marker定义到主SVG（不在缩放容器内）
            let defs = appState.svg.select("defs");
            if (defs.empty()) {{
                defs = appState.svg.append("defs");
            }}
            defs.selectAll("*").remove();
            defs.append("marker")
                .attr("id", "arrowhead")
                .attr("markerWidth", 10)
                .attr("markerHeight", 7)
                .attr("refX", 9)
                .attr("refY", 3.5)
                .attr("orient", "auto")
                .append("polygon")
                .attr("points", "0 0, 10 3.5, 0 7")
                .attr("fill", "#666");
            
            const nodes = appState.graphData.nodes || [];
            const links = appState.graphData.edges || [];
            
            // 如果是进度模式，使用Dashboard布局
            if (appState.currentMode === 'progress') {{
                drawProgressDashboard(nodes, links);
                return;
            }}
            
            // 绘制组件区域背景（在缩放容器内）
            drawComponentAreas(nodes);
            
            // 计算节点位置，使其位于对应组件区域内
            assignNodePositionsToAreas(nodes);
            
            // 创建力导向模拟
            appState.simulation = d3.forceSimulation(nodes)
                .force("link", d3.forceLink(links).id(d => d.id).distance(120))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(appState.width / 2, appState.height / 2))
                .force("collision", d3.forceCollide().radius(35))
                .force("boundary", boundaryForce);
            
            // 绘制链接（在缩放容器内）
            const link = appState.zoomContainer.selectAll(".link")
                .data(links)
                .enter().append("line")
                .attr("class", "link")
                .attr("stroke", d => getRelationColor(d.type))
                .attr("stroke-width", 2)
                .attr("marker-end", "url(#arrowhead)");
            
            // 绘制链接标签（在缩放容器内）
            const linkLabel = appState.zoomContainer.selectAll(".link-label")
                .data(links)
                .enter().append("text")
                .attr("class", "link-label")
                .text(d => d.type);
            
            // 绘制节点（在缩放容器内）
            const node = appState.zoomContainer.selectAll(".node")
                .data(nodes)
                .enter().append("circle")
                .attr("class", "node")
                .attr("r", 25)
                .attr("fill", d => getNodeColor(d.component))
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("click", highlightConnections)
                .on("mouseover", showTooltip)
                .on("mouseout", hideTooltip);
            
            // 绘制节点文本（在缩放容器内）
            const nodeText = appState.zoomContainer.selectAll(".node-text")
                .data(nodes)
                .enter().append("text")
                .attr("class", "node-text")
                .text(d => d.id || d.name);
            
            // 更新模拟
            appState.simulation.on("tick", () => {{
                // 应用边界约束
                nodes.forEach(d => {{
                    const area = getComponentArea(d.component);
                    if (area) {{
                        d.x = Math.max(area.x + 30, Math.min(area.x + area.width - 30, d.x));
                        d.y = Math.max(area.y + 30, Math.min(area.y + area.height - 30, d.y));
                    }}
                }});
                
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                linkLabel
                    .attr("x", d => (d.source.x + d.target.x) / 2)
                    .attr("y", d => (d.source.y + d.target.y) / 2 - 5);
                
                node
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                
                nodeText
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + 4);
            }});
        }}
        
        function drawProgressDashboard(nodes, links) {{
            // Dashboard布局参数
            const cardWidth = 250;
            const cardHeight = 150;
            const cardSpacing = 20;
            const cardsPerRow = Math.floor((appState.width - 40) / (cardWidth + cardSpacing));
            
            // 按状态分组节点
            const statusGroups = {{}};
            nodes.forEach(node => {{
                const status = node.status || '未知状态';
                if (!statusGroups[status]) {{
                    statusGroups[status] = [];
                }}
                statusGroups[status].push(node);
            }});
            
            let cardIndex = 0;
            
            // 为每个状态组创建卡片（在缩放容器内）
            Object.entries(statusGroups).forEach(([status, groupNodes]) => {{
                const row = Math.floor(cardIndex / cardsPerRow);
                const col = cardIndex % cardsPerRow;
                
                const cardX = 20 + col * (cardWidth + cardSpacing);
                const cardY = 40 + row * (cardHeight + cardSpacing);
                
                // 绘制卡片背景
                const card = appState.zoomContainer.append("g")
                    .attr("class", "progress-card")
                    .attr("transform", `translate(${{cardX}}, ${{cardY}})`);
                
                // 卡片背景
                card.append("rect")
                    .attr("width", cardWidth)
                    .attr("height", cardHeight)
                    .attr("rx", 8)
                    .attr("ry", 8)
                    .attr("fill", "white")
                    .attr("stroke", getStatusColor(status))
                    .attr("stroke-width", 2)
                    .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))");
                
                // 状态标题
                card.append("text")
                    .attr("x", cardWidth / 2)
                    .attr("y", 25)
                    .attr("text-anchor", "middle")
                    .attr("font-size", "16px")
                    .attr("font-weight", "bold")
                    .attr("fill", getStatusColor(status))
                    .text(status);
                
                // 统计信息
                card.append("text")
                    .attr("x", cardWidth / 2)
                    .attr("y", 50)
                    .attr("text-anchor", "middle")
                    .attr("font-size", "24px")
                    .attr("font-weight", "bold")
                    .attr("fill", "#333")
                    .text(groupNodes.length);
                
                card.append("text")
                    .attr("x", cardWidth / 2)
                    .attr("y", 70)
                    .attr("text-anchor", "middle")
                    .attr("font-size", "12px")
                    .attr("fill", "#666")
                    .text("个任务");
                
                // 进度条
                const progressValue = calculateProgress(groupNodes);
                const progressBarWidth = cardWidth - 40;
                
                card.append("rect")
                    .attr("x", 20)
                    .attr("y", 90)
                    .attr("width", progressBarWidth)
                    .attr("height", 8)
                    .attr("rx", 4)
                    .attr("fill", "#e0e0e0");
                
                card.append("rect")
                    .attr("x", 20)
                    .attr("y", 90)
                    .attr("width", progressBarWidth * progressValue / 100)
                    .attr("height", 8)
                    .attr("rx", 4)
                    .attr("fill", getStatusColor(status));
                
                card.append("text")
                    .attr("x", cardWidth / 2)
                    .attr("y", 115)
                    .attr("text-anchor", "middle")
                    .attr("font-size", "12px")
                    .attr("fill", "#666")
                    .text(`进度: ${{progressValue}}%`);
                
                // 任务列表
                const taskList = groupNodes.slice(0, 3); // 只显示前3个
                taskList.forEach((task, index) => {{
                    card.append("text")
                        .attr("x", 10)
                        .attr("y", 135 + index * 12)
                        .attr("font-size", "10px")
                        .attr("fill", "#666")
                        .text(`• ${{task.name || task.id}}`);
                }});
                
                if (groupNodes.length > 3) {{
                    card.append("text")
                        .attr("x", 10)
                        .attr("y", 135 + 3 * 12)
                        .attr("font-size", "10px")
                        .attr("fill", "#999")
                        .text(`... 还有 ${{groupNodes.length - 3}} 个任务`);
                }}
                
                // 添加点击事件
                card.style("cursor", "pointer")
                    .on("click", () => {{
                        showTaskDetails(status, groupNodes);
                    }});
                
                cardIndex++;
            }});
            
            // 如果没有卡片，显示空状态
            if (cardIndex === 0) {{
                appState.zoomContainer.append("text")
                    .attr("x", appState.width / 2)
                    .attr("y", appState.height / 2)
                    .attr("text-anchor", "middle")
                    .attr("font-size", "18px")
                    .attr("fill", "#999")
                    .text("暂无进度数据");
            }}
        }}
        
        function getStatusColor(status) {{
            const statusColors = {{
                '已完成': '#4CAF50',
                '进行中': '#FF9800', 
                '计划中': '#2196F3',
                '暂停': '#9E9E9E',
                '延期': '#F44336',
                '未开始': '#757575',
                '待评审': '#9C27B0',
                '已取消': '#795548'
            }};
            return statusColors[status] || '#666';
        }}
        
        function calculateProgress(nodes) {{
            if (!nodes || nodes.length === 0) return 0;
            
            let totalProgress = 0;
            nodes.forEach(node => {{
                const progress = node.progress || 0;
                totalProgress += typeof progress === 'number' ? progress : 0;
            }});
            
            return Math.round(totalProgress / nodes.length);
        }}
        
        function showTaskDetails(status, tasks) {{
            // 创建任务详情弹窗
            const modal = d3.select("body").append("div")
                .attr("class", "task-modal")
                .style("position", "fixed")
                .style("top", "50%")
                .style("left", "50%")
                .style("transform", "translate(-50%, -50%)")
                .style("background", "white")
                .style("padding", "20px")
                .style("border-radius", "8px")
                .style("box-shadow", "0 4px 20px rgba(0,0,0,0.3)")
                .style("z-index", "1000")
                .style("max-width", "600px")
                .style("max-height", "80vh")
                .style("overflow-y", "auto");
            
            // 标题
            modal.append("h3")
                .text(`${{status}} - ${{tasks.length}} 个任务`)
                .style("margin-top", "0")
                .style("color", getStatusColor(status));
            
            // 任务列表
            const taskTable = modal.append("table")
                .style("width", "100%")
                .style("border-collapse", "collapse");
            
            const header = taskTable.append("thead").append("tr");
            header.append("th").text("任务ID").style("border", "1px solid #ddd").style("padding", "8px");
            header.append("th").text("任务名称").style("border", "1px solid #ddd").style("padding", "8px");
            header.append("th").text("进度").style("border", "1px solid #ddd").style("padding", "8px");
            header.append("th").text("组件").style("border", "1px solid #ddd").style("padding", "8px");
            
            const tbody = taskTable.append("tbody");
            tasks.forEach(task => {{
                const row = tbody.append("tr");
                row.append("td").text(task.id).style("border", "1px solid #ddd").style("padding", "8px");
                row.append("td").text(task.name || '-').style("border", "1px solid #ddd").style("padding", "8px");
                row.append("td").text(`${{task.progress || 0}}%`).style("border", "1px solid #ddd").style("padding", "8px");
                row.append("td").text(task.component || '-').style("border", "1px solid #ddd").style("padding", "8px");
            }});
            
            // 关闭按钮
            modal.append("button")
                .text("关闭")
                .style("margin-top", "20px")
                .style("padding", "8px 16px")
                .style("background", "#007bff")
                .style("color", "white")
                .style("border", "none")
                .style("border-radius", "4px")
                .style("cursor", "pointer")
                .on("click", () => {{
                    modal.remove();
                }});
            
            // 背景遮罩
            const overlay = d3.select("body").append("div")
                .attr("class", "modal-overlay")
                .style("position", "fixed")
                .style("top", "0")
                .style("left", "0")
                .style("width", "100%")
                .style("height", "100%")
                .style("background", "rgba(0,0,0,0.5)")
                .style("z-index", "999")
                .on("click", () => {{
                    modal.remove();
                    overlay.remove();
                }});
        }}
        
        function drawComponentAreas(nodes) {{
            // 获取所有预定义的组件类型
            const orderedComponents = {json.dumps(component_manager.get_ordered_components(), cls=EnumEncoder)};
            
            // 如果是综合视图(all模式)或产品结构模式，不绘制组件区域
            if (appState.currentMode === 'all' || appState.currentMode === 'structure') {{
                return;
            }}
            
            let componentTypesToShow = [];
            
            // 获取当前节点中存在的组件类型
            const existingComponentTypes = [...new Set(nodes.map(d => d.component).filter(c => c))];
            
            // 确保显示所有预定义的主要组件类型，即使当前没有节点
            componentTypesToShow = orderedComponents.filter(comp => 
                ['REQ', 'DES', 'DEV', 'PM', 'QA'].includes(comp)
            );
            
            // 添加当前存在但不在主要组件中的类型
            existingComponentTypes.forEach(comp => {{
                if (!componentTypesToShow.includes(comp)) {{
                    componentTypesToShow.push(comp);
                }}
            }});
            
            // 按照预定义顺序排序
            componentTypesToShow.sort((a, b) => {{
                const indexA = orderedComponents.indexOf(a);
                const indexB = orderedComponents.indexOf(b);
                if (indexA === -1 && indexB === -1) return a.localeCompare(b);
                if (indexA === -1) return 1;
                if (indexB === -1) return -1;
                return indexA - indexB;
            }});
            
            // 如果没有要显示的组件，就不绘制区域
            if (componentTypesToShow.length === 0) {{
                return;
            }}
            
            // 计算每个组件区域的位置和大小
            const areaWidth = appState.width / componentTypesToShow.length;
            const areaHeight = appState.height;
            
            componentTypesToShow.forEach((component, index) => {{
                const area = {{
                    x: index * areaWidth,
                    y: 0,
                    width: areaWidth,
                    height: areaHeight
                }};
                componentAreas[component] = area;
                
                // 绘制区域背景（在缩放容器内）
                appState.zoomContainer.append("rect")
                    .attr("class", "component-area")
                    .attr("x", area.x)
                    .attr("y", area.y)
                    .attr("width", area.width)
                    .attr("height", area.height)
                    .attr("fill", getNodeColor(component))
                    .attr("fill-opacity", 0.05)
                    .attr("stroke", getNodeColor(component))
                    .attr("stroke-width", 2)
                    .attr("stroke-opacity", 0.3);
                
                // 绘制区域标签（在缩放容器内）
                appState.zoomContainer.append("text")
                    .attr("class", "area-label")
                    .attr("x", area.x + area.width / 2)
                    .attr("y", appState.height - 10)  // 放在下方位置
                    .attr("text-anchor", "middle")
                    .style("font-size", "14px")
                    .style("font-weight", "bold")
                    .style("fill", getNodeColor(component))
                    .text(component);  // 只显示组件代号
            }});
        }}
        
        function assignNodePositionsToAreas(nodes) {{
            nodes.forEach(node => {{
                const area = componentAreas[node.component];
                if (area) {{
                    if (!node.x) {{
                        node.x = area.x + area.width * (0.2 + Math.random() * 0.6);
                    }} else {{
                        node.x = Math.max(area.x + 30, Math.min(area.x + area.width - 30, node.x));
                    }}
                    
                    if (!node.y) {{
                        node.y = area.y + area.height * (0.2 + Math.random() * 0.6);
                    }} else {{
                        node.y = Math.max(area.y + 50, Math.min(area.y + area.height - 30, node.y));
                    }}
                }}
            }});
        }}
        
        function getComponentArea(component) {{
            return componentAreas[component];
        }}
        
        function boundaryForce(alpha) {{
            return function(d) {{
                const area = getComponentArea(d.component);
                if (area) {{
                    const strength = 0.1 * alpha;
                    if (d.x < area.x + 30) d.vx += (area.x + 30 - d.x) * strength;
                    if (d.x > area.x + area.width - 30) d.vx += (area.x + area.width - 30 - d.x) * strength;
                    if (d.y < area.y + 50) d.vy += (area.y + 50 - d.y) * strength;
                    if (d.y > area.y + area.height - 30) d.vy += (area.y + area.height - 30 - d.y) * strength;
                }}
            }};
        }}
        
        function getNodeColor(component) {{
            return appState.componentColors[component] || "#666";
        }}
        
        function getRelationColor(type) {{
            return appState.relationColors[type] || "#666";
        }}
        
        function switchMode(newMode) {{
            if (newMode === appState.currentMode) return;
            
            console.log('Switching to mode:', newMode);
            
            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => {{
                btn.classList.remove('active');
                if (btn.dataset.mode === newMode) {{
                    btn.classList.add('active');
                }}
            }});
            
            // 显示加载状态
            showLoadingSpinner();
            
            // 调用API获取新模式的数据
            fetch(`/api/visualization?mode=${{newMode}}`)
                .then(response => {{
                    if (!response.ok) {{
                        throw new Error(`HTTP error! status: ${{response.status}}`);
                    }}
                    return response.json();
                }})
                .then(data => {{
                    console.log('Received data for mode:', newMode, data);
                    
                    // 更新应用状态
                    appState.graphData = data;
                    appState.currentMode = newMode;
                    
                    // 重新渲染可视化
                    updateVisualization();
                    
                    // 更新元数据显示
                    updateMetadata();
                    
                    // 隐藏加载状态
                    hideLoadingSpinner();
                }})
                .catch(error => {{
                    console.error('Error switching mode:', error);
                    
                    // 显示错误信息
                    alert(`切换模式失败: ${{error.message}}`);
                    
                    // 恢复之前的按钮状态
                    document.querySelectorAll('.mode-btn').forEach(btn => {{
                        btn.classList.remove('active');
                        if (btn.dataset.mode === appState.currentMode) {{
                            btn.classList.add('active');
                        }}
                    }});
                    
                    hideLoadingSpinner();
                }});
        }}
        
        function showLoadingSpinner() {{
            // 显示加载动画
            const svg = appState.svg;
            svg.selectAll('.loading').remove();
            svg.append('text')
                .attr('class', 'loading')
                .attr('x', appState.width / 2)
                .attr('y', appState.height / 2)
                .attr('text-anchor', 'middle')
                .style('font-size', '18px')
                .style('fill', '#666')
                .text('🔄 加载中...');
        }}
        
        function hideLoadingSpinner() {{
            appState.svg.selectAll('.loading').remove();
        }}
        
        function updateMetadata() {{
            const metadata = appState.graphData.metadata || {{}};
            const metadataDiv = document.querySelector('.metadata');
            if (metadataDiv) {{
                let itemsHtml = '';
                for (const [key, value] of Object.entries(metadata)) {{
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    itemsHtml += `
                    <div class="metadata-item">
                        <span class="metadata-label">${{displayKey}}:</span>
                        <span class="metadata-value">${{value}}</span>
                    </div>
                    `;
                }}
                metadataDiv.innerHTML = `
                <h3>📊 数据统计</h3>
                ${{itemsHtml}}
                `;
            }}
            
            // 更新状态栏
            const nodeCount = appState.graphData.nodes ? appState.graphData.nodes.length : 0;
            const edgeCount = appState.graphData.edges ? appState.graphData.edges.length : 0;
            document.querySelector('.status-bar').innerHTML = `
                <div class="status-left">
                    节点: <strong>${{nodeCount}}</strong> | 
                    边: <strong>${{edgeCount}}</strong> | 
                    模式: <strong>${{appState.currentMode}}</strong>
                </div>
                <div class="status-right">
                    更新时间: ${{new Date().toLocaleString()}}
                </div>
            `;
        }}
        
        function dragstarted(event, d) {{
            if (!event.active) appState.simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }}
        
        function dragged(event, d) {{
            const area = getComponentArea(d.component);
            if (area) {{
                d.fx = Math.max(area.x + 30, Math.min(area.x + area.width - 30, event.x));
                d.fy = Math.max(area.y + 50, Math.min(area.y + area.height - 30, event.y));
            }} else {{
                d.fx = event.x;
                d.fy = event.y;
            }}
        }}
        
        function dragended(event, d) {{
            if (!event.active) appState.simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }}
        
        function highlightConnections(event, d) {{
            // 实现高亮功能
            console.log('Highlighting connections for:', d.id);
        }}
        
        function showTooltip(event, d) {{
            // 创建tooltip元素
            const tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);
            
            // 构建tooltip内容
            let content = `<div class="tooltip-title">${{d.id || d.name}}</div>`;
            
            // 添加基本信息
            if (d.component) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">组件:</span> ${{d.component}}</div>`;
            }}
            
            if (d.type) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">类型:</span> ${{d.type}}</div>`;
            }}
            
            if (d.status) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">状态:</span> ${{d.status}}</div>`;
            }}
            
            if (d.description) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">描述:</span> ${{d.description}}</div>`;
            }}
            
            if (d.file_path) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">文件:</span> ${{d.file_path}}</div>`;
            }}
            
            if (d.created_date) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">创建日期:</span> ${{d.created_date}}</div>`;
            }}
            
            if (d.modified_date) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">修改日期:</span> ${{d.modified_date}}</div>`;
            }}
            
            if (d.author) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">作者:</span> ${{d.author}}</div>`;
            }}
            
            if (d.priority) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">优先级:</span> ${{d.priority}}</div>`;
            }}
            
            if (d.tags && d.tags.length > 0) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">标签:</span> ${{d.tags.join(', ')}}</div>`;
            }}
            
            // 显示连接信息
            const connections = (appState.graphData.edges || []).filter(edge => 
                edge.source === d.id || edge.target === d.id || 
                (edge.source.id && edge.source.id === d.id) || 
                (edge.target.id && edge.target.id === d.id)
            );
            if (connections.length > 0) {{
                content += `<div class="tooltip-item"><span class="tooltip-label">连接数:</span> ${{connections.length}}</div>`;
            }}
            
            tooltip.html(content)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px")
                .transition()
                .duration(200)
                .style("opacity", 1);
            
            // 存储tooltip引用以便后续移除
            d3.select(event.target).attr("data-tooltip", "active");
        }}
        
        function hideTooltip(event, d) {{
            // 移除所有tooltip
            d3.selectAll(".tooltip")
                .transition()
                .duration(200)
                .style("opacity", 0)
                .remove();
            
            // 清除tooltip标记
            if (event && event.target) {{
                d3.select(event.target).attr("data-tooltip", null);
            }}
        }}
        
        function resetLayout() {{
            if (appState.simulation) {{
                appState.simulation.alpha(1).restart();
            }}
        }}
        
        function exportGraph() {{
            const svgElement = document.getElementById("main-graph");
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const blob = new Blob([svgData], {{type: "image/svg+xml"}});
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement("a");
            a.href = url;
            a.download = "visualization_" + new Date().toISOString().slice(0,10) + ".svg";
            a.click();
            
            URL.revokeObjectURL(url);
        }}
        
        function changeLayout() {{
            console.log('Change layout');
        }}
        
        function filterNodes() {{
            console.log('Filter nodes');
        }}
        
        function searchNodes() {{
            console.log('Search nodes');
        }}
        
        // 视图控制函数
        function resetZoom() {{
            if (appState.zoom && appState.svg) {{
                appState.svg.transition()
                    .duration(750)
                    .call(appState.zoom.transform, d3.zoomIdentity);
            }}
        }}
        
        function zoomIn() {{
            if (appState.zoom && appState.svg) {{
                appState.svg.transition()
                    .duration(200)
                    .call(appState.zoom.scaleBy, 1.5);
            }}
        }}
        
        function zoomOut() {{
            if (appState.zoom && appState.svg) {{
                appState.svg.transition()
                    .duration(200)
                    .call(appState.zoom.scaleBy, 1 / 1.5);
            }}
        }}
        
        function centerView() {{
            if (appState.zoom && appState.svg) {{
                const centerX = appState.width / 2;
                const centerY = appState.height / 2;
                
                appState.svg.transition()
                    .duration(750)
                    .call(appState.zoom.transform, 
                          d3.zoomIdentity.translate(centerX, centerY).scale(1));
            }}
        }}
        
        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {{
            // 避免在输入框中触发快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {{
                return;
            }}
            
            switch(event.key) {{
                case '+':
                case '=':
                    event.preventDefault();
                    zoomIn();
                    break;
                case '-':
                    event.preventDefault();
                    zoomOut();
                    break;
                case '0':
                    event.preventDefault();
                    resetZoom();
                    break;
                case ' ':
                    event.preventDefault();
                    centerView();
                    break;
                case 'r':
                case 'R':
                    if (event.ctrlKey || event.metaKey) {{
                        event.preventDefault();
                        resetLayout();
                    }}
                    break;
            }}
        }});
        
        // 改进的fitToView函数
        function fitToView() {{
            if (!appState.zoomContainer || !appState.zoom || !appState.svg) return;
            
            try {{
                // 获取所有内容的边界框
                const bbox = appState.zoomContainer.node().getBBox();
                
                if (bbox.width === 0 || bbox.height === 0) return;
                
                // 计算缩放比例和平移量
                const padding = 50;
                const scaleX = (appState.width - padding * 2) / bbox.width;
                const scaleY = (appState.height - padding * 2) / bbox.height;
                const scale = Math.min(scaleX, scaleY, 2); // 最大缩放2倍
                
                // 计算居中的平移量
                const translateX = (appState.width - bbox.width * scale) / 2 - bbox.x * scale;
                const translateY = (appState.height - bbox.height * scale) / 2 - bbox.y * scale;
                
                // 应用变换
                appState.svg.transition()
                    .duration(750)
                    .call(appState.zoom.transform, 
                          d3.zoomIdentity.translate(translateX, translateY).scale(scale));
            }} catch (error) {{
                console.warn('FitToView failed:', error);
                // 回退到居中视图
                centerView();
            }}
        }}
        ''' 