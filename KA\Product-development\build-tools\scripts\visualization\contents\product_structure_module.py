#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
产品结构可视化模块

单一职责：专门处理产品结构层次相关的数据提取和可视化逻辑
整合自visualize_product_structure.py
"""

import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import DataAdapter, VisualizationData, VisualizationMode, Node, Edge
from core.component_utils import component_manager

class ProductStructureDataExtractor(DataAdapter):
    """产品结构数据提取器 - 单一职责：产品结构可视化数据提取"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
    
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """提取产品结构可视化数据"""
        self.project_path = Path(project_path)
        return self._extract_structure_data()
    
    def _extract_structure_data(self) -> VisualizationData:
        """提取产品结构数据"""
        nodes = []
        edges = []
        
        # 尝试加载层级配置文件
        structure_config = self._load_structure_config()
        
        if structure_config:
            # 从配置文件生成结构
            structure_data = self._convert_config_to_graph(structure_config)
            nodes.extend([Node(**node_data) for node_data in structure_data['nodes']])
            edges.extend([Edge(**edge_data) for edge_data in structure_data['edges']])
        else:
            # 从项目结构推断产品层次
            inferred = self._infer_structure_from_directory()
            nodes.extend([Node(**node_data) for node_data in inferred['nodes']])
            edges.extend([Edge(**edge_data) for edge_data in inferred['edges']])
        
        return VisualizationData(
            title="产品结构层次图",
            mode=VisualizationMode.STRUCTURE,  # 新增模式
            nodes=nodes,
            edges=edges,
            metadata={
                "structure_levels": self._count_levels(nodes),
                "total_components": len(nodes),
                "hierarchy_depth": self._calculate_depth(nodes, edges),
                "config_source": "file" if structure_config else "inferred",
                "last_updated": datetime.now().isoformat()
            }
        )
    
    def _load_structure_config(self) -> Dict[str, Any]:
        """加载产品结构配置文件"""
        config_files = [
            self.project_path / "__level_config.json",
            self.project_path / "config" / "product_structure.json",
            self.project_path / "structure.json"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception as e:
                    print(f"加载结构配置失败 {config_file}: {e}")
        
        return None
    
    def _convert_config_to_graph(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """将配置文件转换为图数据"""
        nodes = []
        edges = []
        
        # 处理产品层次结构
        levels = config.get("levels", [])
        node_id_counter = 1
        
        for level_idx, level in enumerate(levels):
            level_name = level.get("name", f"Level {level_idx + 1}")
            
            # 处理该层级的实例
            instances = level.get("instances", [])
            for instance_idx, instance in enumerate(instances):
                node_id = f"STRUCT_{node_id_counter:03d}"
                node_id_counter += 1
                
                node_data = {
                    "id": node_id,
                    "name": instance.get("name", f"{level_name}_{instance_idx + 1}"),
                    "type": "structure",
                    "component": self._map_to_component(level_name),
                    "properties": {
                        "level": level_idx,
                        "level_name": level_name,
                        "description": instance.get("description", ""),
                        "instance_type": instance.get("type", "default"),
                        "parent_level": level_idx - 1 if level_idx > 0 else None
                    }
                }
                nodes.append(node_data)
                
                # 如果有父级，创建层次关系
                if level_idx > 0 and instances:
                    # 简化的父子关系：连接到上一层的第一个实例
                    parent_nodes = [n for n in nodes if n["properties"]["level"] == level_idx - 1]
                    if parent_nodes:
                        parent_id = parent_nodes[0]["id"]  # 简化：连接到上一层第一个节点
                        edge_data = {
                            "source": parent_id,
                            "target": node_id,
                            "type": "包含",
                            "properties": {"hierarchy": True, "level_transition": f"{level_idx-1}->{level_idx}"}
                        }
                        edges.append(edge_data)
        
        return {"nodes": nodes, "edges": edges}
    
    def _infer_structure_from_directory(self) -> Dict[str, List[Dict[str, Any]]]:
        """从目录结构推断产品层次"""
        nodes = []
        edges = []
        
        # 分析目录层次结构
        root_node = {
            "id": "ROOT_001",
            "name": self.project_path.name,
            "type": "structure",
            "component": "PROD_INFO",
            "properties": {
                "level": 0,
                "level_name": "产品根目录",
                "path": str(self.project_path),
                "is_root": True
            }
        }
        nodes.append(root_node)
        
        # 扫描第一级子目录
        subdirs = [d for d in self.project_path.iterdir() if d.is_dir() and not d.name.startswith('.')]
        
        for i, subdir in enumerate(subdirs):
            # 推断组件类型
            component = component_manager.infer_component_from_directory(subdir.name)
            
            node_data = {
                "id": f"LEVEL1_{i+1:03d}",
                "name": subdir.name,
                "type": "structure",
                "component": component,
                "properties": {
                    "level": 1,
                    "level_name": "组件目录",
                    "path": str(subdir),
                    "file_count": len(list(subdir.glob("*"))),
                    "has_subdirs": any(p.is_dir() for p in subdir.iterdir()) if subdir.exists() else False
                }
            }
            nodes.append(node_data)
            
            # 创建与根节点的连接
            edge_data = {
                "source": "ROOT_001",
                "target": node_data["id"],
                "type": "包含",
                "properties": {"hierarchy": True, "level_transition": "0->1"}
            }
            edges.append(edge_data)
            
            # 扫描第二级子目录
            if subdir.exists():
                sub_subdirs = [d for d in subdir.iterdir() if d.is_dir()]
                for j, sub_subdir in enumerate(sub_subdirs[:5]):  # 限制数量避免过于复杂
                    sub_node_data = {
                        "id": f"LEVEL2_{i+1}_{j+1:02d}",
                        "name": sub_subdir.name,
                        "type": "structure",
                        "component": component,
                        "properties": {
                            "level": 2,
                            "level_name": "子模块",
                            "path": str(sub_subdir),
                            "parent_component": component
                        }
                    }
                    nodes.append(sub_node_data)
                    
                    # 创建与父目录的连接
                    sub_edge_data = {
                        "source": node_data["id"],
                        "target": sub_node_data["id"],
                        "type": "包含",
                        "properties": {"hierarchy": True, "level_transition": "1->2"}
                    }
                    edges.append(sub_edge_data)
        
        return {"nodes": nodes, "edges": edges}
    
    def _map_to_component(self, level_name: str) -> str:
        """将层级名称映射到组件"""
        # 使用共享配置中的组件信息来建立映射
        from core.component_utils import component_manager
        
        # 直接从共享配置中获取组件名称映射
        for comp_id, comp_info in component_manager.components.items():
            if comp_info.name in level_name or any(keyword in level_name for keyword in comp_info.keywords):
                return comp_id
        
        return "PROD_INFO"  # 默认为产品信息
    
    def _count_levels(self, nodes: List[Node]) -> int:
        """统计层级数量"""
        levels = set()
        for node in nodes:
            level = node.properties.get("level", 0)
            levels.add(level)
        return len(levels)
    
    def _calculate_depth(self, nodes: List[Node], edges: List[Edge]) -> int:
        """计算层次深度"""
        max_level = 0
        for node in nodes:
            level = node.properties.get("level", 0)
            max_level = max(max_level, level)
        return max_level + 1
    
    def generate_structure_diagram(self, output_path: str = None, format: str = "png") -> str:
        """生成产品结构图表"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            from matplotlib.patches import FancyBboxPatch
            
            data = self._extract_structure_data()
            
            fig, ax = plt.subplots(1, 1, figsize=(14, 10))
            
            # 按层级分组节点
            levels = {}
            for node in data.nodes:
                level = node.properties.get("level", 0)
                if level not in levels:
                    levels[level] = []
                levels[level].append(node)
            
            # 绘制层级结构
            y_positions = {}
            level_height = 150
            
            for level_num, level_nodes in levels.items():
                y_pos = (max(levels.keys()) - level_num) * level_height
                
                for i, node in enumerate(level_nodes):
                    x_pos = i * 200 + 100
                    y_positions[node.id] = (x_pos, y_pos)
                    
                    # 绘制节点
                    component = node.component or "PROD_INFO"
                    color = component_manager.get_component_color(component)
                    
                    # 创建圆角矩形
                    bbox = FancyBboxPatch(
                        (x_pos - 80, y_pos - 30), 160, 60,
                        boxstyle="round,pad=5",
                        facecolor=color, alpha=0.7,
                        edgecolor='black', linewidth=1
                    )
                    ax.add_patch(bbox)
                    
                    # 添加文本
                    ax.text(x_pos, y_pos, node.name, ha='center', va='center',
                           fontsize=10, fontweight='bold', wrap=True)
                    
                    # 添加组件标识
                    ax.text(x_pos, y_pos - 45, f"[{component}]", ha='center', va='center',
                           fontsize=8, style='italic', color='gray')
            
            # 绘制连接线
            for edge in data.edges:
                if edge.source in y_positions and edge.target in y_positions:
                    start_pos = y_positions[edge.source]
                    end_pos = y_positions[edge.target]
                    
                    ax.annotate('', xy=end_pos, xytext=start_pos,
                               arrowprops=dict(arrowstyle='->', lw=1.5, color='gray'))
            
            # 设置图表属性
            ax.set_xlim(-50, max(200 * len(level_nodes) for level_nodes in levels.values()) + 50)
            ax.set_ylim(-50, (max(levels.keys()) + 1) * level_height + 50)
            ax.set_aspect('equal')
            ax.axis('off')
            
            plt.title(data.title, fontsize=16, fontweight='bold', pad=20)
            plt.tight_layout()
            
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight', format=format)
                plt.close()
                return f"产品结构图已保存到 {output_path}"
            else:
                import tempfile
                temp_file = tempfile.NamedTemporaryFile(suffix=f'.{format}', delete=False)
                plt.savefig(temp_file.name, dpi=300, bbox_inches='tight', format=format)
                plt.close()
                return temp_file.name
                
        except ImportError:
            return "需要安装 matplotlib: pip install matplotlib"
        except Exception as e:
            return f"生成产品结构图失败: {e}"
    
    def export_structure_config(self, output_path: str = None) -> str:
        """导出产品结构配置文件"""
        data = self._extract_structure_data()
        
        # 按层级重新组织数据
        levels = {}
        for node in data.nodes:
            level = node.properties.get("level", 0)
            if level not in levels:
                levels[level] = []
            levels[level].append({
                "name": node.name,
                "type": node.type,
                "component": node.component,
                "description": node.properties.get("description", ""),
                "properties": node.properties
            })
        
        config = {
            "product_name": self.project_path.name,
            "structure_type": "hierarchical",
            "levels": [
                {
                    "level": level_num,
                    "name": f"Level {level_num}",
                    "instances": instances
                }
                for level_num, instances in sorted(levels.items())
            ],
            "metadata": data.metadata,
            "generated_at": datetime.now().isoformat()
        }
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return f"结构配置已导出到 {output_path}"
        else:
            return json.dumps(config, indent=2, ensure_ascii=False) 