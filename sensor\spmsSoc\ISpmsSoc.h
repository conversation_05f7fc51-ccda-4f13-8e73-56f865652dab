#ifndef _ISPMS_SOC_H_
#define _ISPMS_SOC_H_

#include <QByteArray>
#include <QMap>
#include <QObject>

#include "processListA.h"

class ISpmsSoc:public QObject{
    Q_OBJECT
public:
    ISpmsSoc();
    virtual ~ISpmsSoc(){};

    enum class ESpmsSoc {
        eVI5300,
        eST,
    };

    enum class ECalibProcess {
        eCALIB1             = 0,
        eCALIB2,
        eCALIB3,
        eCALIB4,
    };
    Q_ENUM(ECalibProcess);

    typedef struct {
        bool        is_calib;
        QString     calib_item_name;
        QString     param1_name;
        uint16_t    param1;
        uint16_t    param1_value;
        QString     param2_name;
        int16_t     param2;
        int16_t     param2_value;
//        int16_t     cal;
    } StCalibItems;

//    typedef bool (ISpmsSoc::*typeFptr_)(void);
//    typedef struct {
//        QByteArray                      cmd;
//        typeFptr_                       ptr_;
//        typeFptr_                       ack_ptr_;
//    } StTaskInfo;
//    QMap<QString, QByteArray> mm_calib_flow;
    QMap<ECalibProcess, QString> mm_calib_flow;
//    QMap<QString, StTaskInfo> mm_calib_flow;


    virtual EExecStatus calibTask1() = 0;
    virtual EExecStatus calibTask1Ack() = 0;
    virtual EExecStatus calibTask2() = 0;
    virtual EExecStatus calibTask2Ack() = 0;
    virtual EExecStatus calibTask3() = 0;
    virtual EExecStatus calibTask3Ack() = 0;
    virtual EExecStatus calibTask4() = 0;
    virtual EExecStatus calibTask4Ack() = 0;

protected:
    virtual QString get_class_name(void) = 0;
};

#endif
