#include "BilinearInterpolation.h"

namespace ImageProcessing {

BilinearInterpolation::BilinearInterpolation() {
    // 设置默认参数
    params_.offset        = 0.5f;
    params_.preserveEdges = true;
    params_.clampValues   = true;
    params_.minValue      = 0;
    params_.maxValue      = UINT32_MAX;

    logDebug("BilinearInterpolation initialized");
}

bool BilinearInterpolation::interpolate(const ImageDataU32 &src, ImageDataU32 &dst) {
    try {
        // 验证输入参数
        validateInterpolationInputs(src, dst);

        logDebug(QString("Starting bilinear interpolation: %1x%2 -> %3x%4").arg(src.width()).arg(src.height()).arg(dst.width()).arg(dst.height()));

        // 处理边界点（四个顶点）
        processCornerPoints(src, dst);

        // 处理边界线
        processBoundaryLines(src, dst);

        // 处理内部区域
        processInteriorRegion(src, dst);

        logDebug("Bilinear interpolation completed successfully");
        return true;

    } catch (const ProcessingException &e) {
        qWarning() << "BilinearInterpolation::interpolate failed:" << e.qMessage();
        return false;
    } catch (const std::exception &e) {
        qWarning() << "BilinearInterpolation::interpolate failed:" << e.what();
        return false;
    }
}

void BilinearInterpolation::setParameters(const InterpolationParams &params) {
    params.validate();
    params_ = params;
    logDebug("Parameters updated: " + params_.toString());
}

InterpolationParams BilinearInterpolation::getParameters() const {
    return params_;
}

QString BilinearInterpolation::getAlgorithmName() const {
    return "BilinearInterpolation";
}

QString BilinearInterpolation::getDescription() const {
    return "High-quality bilinear interpolation algorithm for image resizing";
}

bool BilinearInterpolation::isSupported(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const {
    try {
        ValidationUtils::validateInterpolationParams(srcWidth, srcHeight, dstWidth, dstHeight);
        return true;
    } catch (const ProcessingException &) {
        return false;
    }
}

uint32_t BilinearInterpolation::estimateProcessingTime(uint32_t srcWidth, uint32_t srcHeight, uint32_t dstWidth, uint32_t dstHeight) const {
    // 简单的时间估算：基于目标图像像素数量
    uint64_t totalPixels = static_cast<uint64_t>(dstWidth) * dstHeight;
    // 假设每个像素需要约0.001毫秒处理时间
    return static_cast<uint32_t>(totalPixels / 1000);
}

void BilinearInterpolation::reset() {
    // 重置为默认参数
    params_.reset();
    logDebug("BilinearInterpolation reset to default parameters");
}

QString BilinearInterpolation::getVersion() const {
    return "1.0.0";
}

bool BilinearInterpolation::isThreadSafe() const {
    return true;  // 无状态操作，线程安全
}

float BilinearInterpolation::calculateSourceIndex(uint32_t dstIndex, uint32_t srcBoardLen, uint32_t dstBoardLen, float offset) const {
    return static_cast<float>(dstIndex) * srcBoardLen / dstBoardLen + offset * (static_cast<float>(srcBoardLen) / dstBoardLen - 1.0f);
}

float BilinearInterpolation::calculateSourceIndexExpand(uint32_t dstIndex, uint32_t srcBoardLen, uint32_t dstBoardLen) const {
    return (dstIndex + 1.0f) * (static_cast<float>(srcBoardLen) / dstBoardLen) - 1.0f;
}

uint32_t BilinearInterpolation::linearInterpolation(float rate, uint32_t pointF, uint32_t pointB) const {
    int32_t pB     = static_cast<int32_t>(pointB);
    int32_t fB     = static_cast<int32_t>(pointF);
    float   result = pointF + rate * (pB - fB) + 0.5f;

    uint32_t finalResult = safeFloatToUint32(result);

    if (params_.clampValues) {
        finalResult = clampValue(finalResult, params_.minValue, params_.maxValue);
    }

    return finalResult;
}

uint32_t BilinearInterpolation::bilinearCalculation(float rateX, float rateY, uint32_t f1, uint32_t f2, uint32_t f3, uint32_t f4) const {
    float result = (1.0f - rateY) * (1.0f - rateX) * f1 + (1.0f - rateY) * rateX * f4 + rateY * (1.0f - rateX) * f2 + rateY * rateX * f3;

    uint32_t finalResult = safeFloatToUint32(result);

    if (params_.clampValues) {
        finalResult = clampValue(finalResult, params_.minValue, params_.maxValue);
    }

    return finalResult;
}

void BilinearInterpolation::processCornerPoints(const ImageDataU32 &src, ImageDataU32 &dst) const {
    uint32_t srcWidth  = src.width();
    uint32_t srcHeight = src.height();
    uint32_t dstWidth  = dst.width();
    uint32_t dstHeight = dst.height();

    // 四个顶点直接复制
    dst.matrix()[0][0]                        = src.matrix()[0][0];                         // 左上
    dst.matrix()[0][dstWidth - 1]             = src.matrix()[0][srcWidth - 1];              // 右上
    dst.matrix()[dstHeight - 1][0]            = src.matrix()[srcHeight - 1][0];             // 左下
    dst.matrix()[dstHeight - 1][dstWidth - 1] = src.matrix()[srcHeight - 1][srcWidth - 1];  // 右下
}

void BilinearInterpolation::processBoundaryLines(const ImageDataU32 &src, ImageDataU32 &dst) const {
    uint32_t srcWidth  = src.width();
    uint32_t srcHeight = src.height();
    uint32_t dstWidth  = dst.width();
    uint32_t dstHeight = dst.height();

    // 处理左右边界
    for (uint32_t i = 1; i < dstHeight - 1; ++i) {
        float    rateY     = calculateSourceIndex(i, srcHeight, dstHeight, params_.offset);
        uint32_t srcFIndex = static_cast<uint32_t>(rateY);
        uint32_t srcBIndex = (rateY + 1 < srcHeight) ? (srcFIndex + 1) : srcHeight - 1;
        rateY -= qFloor(rateY);  // 取小数部分

        // 左边界 (x=0)
        uint32_t leftValue = linearInterpolation(rateY, src.matrix()[srcFIndex][0], src.matrix()[srcBIndex][0]);
        dst.matrix()[i][0] = leftValue;

        // 右边界 (x=dstWidth-1)
        uint32_t rightValue           = linearInterpolation(rateY, src.matrix()[srcFIndex][srcWidth - 1], src.matrix()[srcBIndex][srcWidth - 1]);
        dst.matrix()[i][dstWidth - 1] = rightValue;
    }

    // 处理上下边界
    for (uint32_t j = 1; j < dstWidth - 1; ++j) {
        float    rateX     = calculateSourceIndex(j, srcWidth, dstWidth, params_.offset);
        uint32_t srcFIndex = static_cast<uint32_t>(rateX);
        uint32_t srcBIndex = (rateX + 1 < srcWidth) ? (srcFIndex + 1) : srcWidth - 1;
        rateX -= qFloor(rateX);  // 取小数部分

        // 上边界 (y=0)
        uint32_t topValue  = linearInterpolation(rateX, src.matrix()[0][srcFIndex], src.matrix()[0][srcBIndex]);
        dst.matrix()[0][j] = topValue;

        // 下边界 (y=dstHeight-1)
        uint32_t bottomValue           = linearInterpolation(rateX, src.matrix()[srcHeight - 1][srcFIndex], src.matrix()[srcHeight - 1][srcBIndex]);
        dst.matrix()[dstHeight - 1][j] = bottomValue;
    }
}

void BilinearInterpolation::processInteriorRegion(const ImageDataU32 &src, ImageDataU32 &dst) const {
    uint32_t srcWidth  = src.width();
    uint32_t srcHeight = src.height();
    uint32_t dstWidth  = dst.width();
    uint32_t dstHeight = dst.height();

    // 处理内部区域
    for (uint32_t i = 1; i < dstHeight - 1; ++i) {
        float    rateY         = calculateSourceIndex(i, srcHeight, dstHeight, params_.offset);
        uint32_t srcYIndex     = static_cast<uint32_t>(rateY);
        uint32_t srcYIndexNext = (rateY + 1 < srcHeight) ? (srcYIndex + 1) : srcHeight - 1;
        rateY -= qFloor(rateY);  // 取小数部分

        for (uint32_t j = 1; j < dstWidth - 1; ++j) {
            float    rateX         = calculateSourceIndex(j, srcWidth, dstWidth, params_.offset);
            uint32_t srcXIndex     = static_cast<uint32_t>(rateX);
            uint32_t srcXIndexNext = (rateX + 1 < srcWidth) ? (srcXIndex + 1) : srcWidth - 1;
            rateX -= qFloor(rateX);  // 取小数部分

            // 获取四个邻近点的值
            uint32_t f1 = src.matrix()[srcYIndex][srcXIndex];          // 左上
            uint32_t f2 = src.matrix()[srcYIndexNext][srcXIndex];      // 左下
            uint32_t f3 = src.matrix()[srcYIndexNext][srcXIndexNext];  // 右下
            uint32_t f4 = src.matrix()[srcYIndex][srcXIndexNext];      // 右上

            // 双线性插值计算
            uint32_t interpolatedValue = bilinearCalculation(rateX, rateY, f1, f2, f3, f4);
            dst.matrix()[i][j]         = interpolatedValue;
        }
    }
}

uint32_t BilinearInterpolation::getSafePixelValue(const ImageDataU32 &src, int x, int y) const {
    if (x < 0)
        x = 0;
    if (y < 0)
        y = 0;
    if (x >= static_cast<int>(src.width()))
        x = src.width() - 1;
    if (y >= static_cast<int>(src.height()))
        y = src.height() - 1;

    return src.matrix()[y][x];
}

void BilinearInterpolation::validateInterpolationInputs(const ImageDataU32 &src, const ImageDataU32 &dst) const {
    ValidationUtils::validateImageData(src, "source image");
    ValidationUtils::validateImageData(dst, "destination image");

    // 检查最小尺寸要求
    if (src.width() < 2 || src.height() < 2) {
        throw InvalidImageDataException("Source image too small for bilinear interpolation (minimum 2x2)");
    }

    if (dst.width() < 1 || dst.height() < 1) {
        throw InvalidImageDataException("Destination image size invalid");
    }

    // 检查尺寸合理性
    ValidationUtils::validateInterpolationParams(src.width(), src.height(), dst.width(), dst.height());
}

void BilinearInterpolation::logDebug(const QString &message) const {
    qDebug() << "[BilinearInterpolation]" << message;
}

}  // namespace ImageProcessing
