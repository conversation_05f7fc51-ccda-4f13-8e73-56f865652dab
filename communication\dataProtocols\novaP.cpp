#include "novaP.h"
#include <qdebug.h>
#include "qLog.h"

CNovaP::CNovaP():
    mst_interaction_frame_(new StInteractionFrame)
  , mst_data_info_frame_(new StDataInfoFrame)
  , mst_dds_frame_(new StDdsFrame)
{
    mst_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    mst_data_info_frame_->header = (uint16_t)EDataInfoFrame::eHEADER;
    mst_dds_frame_->header = (uint16_t)ELogFrame::eHEADER;
}

CNovaP::~CNovaP(){
    delete mst_interaction_frame_;
    delete mst_data_info_frame_;
    delete mst_dds_frame_;
}

QByteArray CNovaP::getControlCmd(const char &id)
{
    QByteArray cmd;
    StInteractionFrame* st_interaction_frame_ = new StInteractionFrame;

    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    st_interaction_frame_->cmd = (uint8_t)ECmd::eW | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
    st_interaction_frame_->id = id;
    st_interaction_frame_->num = 0;

    st_interaction_frame_->data.resize(0);

    st_interaction_frame_->check_sum = checkSum((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN - 1);

    cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CNovaP::getWriteCmd(const char &id, const QByteArray &w_data)
{
    QByteArray w_cmd;
    StInteractionFrame* st_interaction_frame_ = new StInteractionFrame;

    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    st_interaction_frame_->cmd = (uint8_t)ECmd::eW | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
    st_interaction_frame_->id = id;
    st_interaction_frame_->num = w_data.length();

    //mst_interaction_frame_->data.resize(mst_interaction_frame_->num);
    for(uint8_t for_i = 0; for_i < w_data.size(); for_i++) {
        st_interaction_frame_->data.append(w_data.at(for_i));
    }
    //  w_cmd.append((char*)mst_receive_frame_, (EFrame::eHEADER_LEN )); //+ w_data.length())); //直接拷贝整个 qbytearray数据错乱
    st_interaction_frame_->check_sum = checkSum((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN - 1);

    w_cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CNovaP::getReadCmd(const uint8_t &id, const QByteArray &w_data)
{
    QByteArray r_cmd;
    StInteractionFrame* st_interaction_frame_ = new StInteractionFrame;

    st_interaction_frame_->header = (uint16_t)EInteractionFrame::eHEADER;
    st_interaction_frame_->cmd = (uint8_t)ECmd::eR | (uint8_t)ECmd::eCMD | (uint8_t)ECmd::eH2D;
    st_interaction_frame_->id = id;
    st_interaction_frame_->num = 0;
    st_interaction_frame_->data.resize(0);

    st_interaction_frame_->check_sum = checkSum((uint8_t*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN - 1);

    r_cmd.append((char*)st_interaction_frame_, (uint8_t)EInteractionFrame::eHEADER_LEN);

    return r_cmd;
}


