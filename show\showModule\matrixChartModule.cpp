#include "matrixChartModule.h"
#include "qLog.h"
#include <QMessageBox>
#include <QMetaEnum>
#include <QHeaderView>
#include "IPhotonSensor.h"


CMatrixChartMod::CMatrixChartMod(QTableWidget* table_, IPhotonSensor::StMapInfo *map_info_):
    m_map_info_(map_info_)
{
    /*1.ui init*/
    table_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch); //列自适应cell大小
    table_->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch); //行自适应cell大小
    table_->horizontalHeader()->setVisible(false); //隐藏水平表头
    table_->verticalHeader()->setVisible(false); //隐藏垂直表头
    table_->setEditTriggers(QAbstractItemView::NoEditTriggers); //表格不可编辑
    table_->setFont(QFont("黑体", 15));

    mp_init(table_, m_map_info_);

    //* 3.信号与槽
    //连接信号与槽，showSlot()是自定义的槽函数!
//    connect(mc_operation_, &CLenAdjustOpt::mainMapSizeSignal,
//            this, &CMatrixChartMod::mpUpdate);
}

CMatrixChartMod::~CMatrixChartMod() {
}


void CMatrixChartMod::mp_init(QTableWidget *table_, CTableViewModule::StMapInfo *map_info_) {
    uint8_t xlen = map_info_->sensor_info.x;
    uint8_t ylen = map_info_->sensor_info.y;
    uint8_t tip_num = 0;

    //*
    table_->setRowCount(ylen);
    table_->setColumnCount(xlen);

    //*
//    map_info_->table_item.resize(0);

    /*释放内存*/
    uint8_t item_lens = map_info_->table_item.length();
    if(item_lens != 0) {
        for(uint i = 0; i < item_lens; i++) {
            if(map_info_->table_item.at(i) != nullptr)
                delete map_info_->table_item.at(i);
        }
    }
    map_info_->table_item.clear();

    for (uint y = 0; y < ylen; y++) {
        for (uint x = 0; x < xlen; x++) {
            QTableWidgetItem *item = new QTableWidgetItem();
            item->setForeground(Qt::yellow); //darkred
            item->setFont(QFont("Times", 20, QFont::Black)); //加粗

            for(QVector<IPhotonSensor::St2Dimension>::iterator iter = map_info_->target_map.begin(); iter != map_info_->target_map.end(); iter++) {
                if(y == iter->ay && x == iter->ax) {
                    item->setForeground(Qt::green);
                    item->setFont(QFont("Times", 26, QFont::Black)); //加粗
                }
            }
            item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);

            /*2. MP提示值*/
            if(map_info_->sensor_info.order == CTableViewModule::EMpOrder::left_2_right) //左往右
                tip_num = y*xlen + x + 1;
            else
                tip_num = x*ylen + y + 1;
            item->setToolTip(QString::number(tip_num)); //提示
            map_info_->table_item.push_back(item);
            table_->setItem(y, x, item);
        }
    }
}

/*
 * @BRIEF: 数据显示
 */
void CMatrixChartMod::greyMapShow(QTableWidget *map, IPhotonSensor::StMapData *map_data_)
{
    uint8_t alpha_tmp = 180, rgb_tmp;
    uint32_t data_tmp = 0;

    uint8_t xlens = m_map_info_->sensor_info.x;
    uint8_t ylens = m_map_info_->sensor_info.y;
    uint32_t data_max = map_data_->max_zone.var1->toUInt() > 10? map_data_->max_zone.var1->toUInt():10; //peak 最小值

    for (int y = 0; y < ylens; ++y) {
        for (int x = 0; x < xlens; ++x) {
            /*1. 数值 update*/
            data_tmp = map_data_->map_matrix.at(y).at(x).var1->toUInt();
            m_map_info_->table_item.at(y*xlens + x)->setText(QString::number(data_tmp));

            /*3. 背景颜色*/
            QColor color;
            rgb_tmp = 255 - (data_tmp * 255 / data_max);
            alpha_tmp = data_tmp * 255 / data_max;
            color.setRgb(rgb_tmp, rgb_tmp, rgb_tmp, alpha_tmp);
            map->item(y,x)->setBackground(QBrush(color));
        }
    }
}

/**
 * @brief 显示界面清空
 */
void CMatrixChartMod::resultClean(void)
{

}

//void CMatrixChartMod::mpUpdate(QTableWidget* table_, const uint8_t &xlen, const uint8_t &ylen)
//{
//    table_->setRowCount(ylen);
//    table_->setColumnCount(xlen);
////    table_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch); //列自适应cell大小
////    table_->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch); //行自适应cell大小
//}

/**
 * @brief 数据接收正常与异常 status update
 */
//void CMatrixChartMod::dataAckShow()
//{
//    greyMapShow(table_, mc_operation_->mst_map_info_, *mc_operation_->m_map_data_); //正常图
//}
