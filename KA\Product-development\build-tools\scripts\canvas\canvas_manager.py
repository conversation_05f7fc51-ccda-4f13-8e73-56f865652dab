#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Canvas管理器

该模块负责Obsidian Canvas文件的读写操作，包括JSON格式解析、
节点和边的管理、以及增量同步等功能。
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Set
from datetime import datetime


class CanvasManager:
    """Canvas文件管理器"""
    
    def __init__(self, project_path: Path):
        """
        初始化Canvas管理器
        
        Args:
            project_path: 项目根目录路径
        """
        self.project_path = Path(project_path)
        self.canvas_file = self.project_path / "product.canvas"
        self.backup_dir = self.project_path / "config" / "canvas_backups"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def read_canvas(self) -> Optional[Dict]:
        """
        读取Canvas文件
        
        Returns:
            Optional[Dict]: Canvas数据，读取失败返回None
        """
        if not self.canvas_file.exists():
            return None
            
        try:
            with open(self.canvas_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取Canvas文件失败: {e}")
            return None
    
    def write_canvas(self, canvas_data: Dict, mode: str = "incremental") -> bool:
        """
        写入Canvas文件，支持增量模式和完全重写模式
        
        Args:
            canvas_data: Canvas数据
            mode: 写入模式 - "incremental"(增量) 或 "full"(完全重写)
            
        Returns:
            bool: 写入成功返回True
        """
        try:
            if mode == "incremental":
                # 增量模式：保留手动调整
                merged_data = self._merge_canvas_data(canvas_data)
            else:
                # 完全重写模式：直接使用新数据
                merged_data = canvas_data
            
            # 备份现有文件
            if self.canvas_file.exists():
                self._backup_canvas()
            
            # 写入新数据（格式化JSON，每个节点和边单独一行）
            with open(self.canvas_file, 'w', encoding='utf-8') as f:
                self._write_formatted_canvas(f, merged_data)
            
            return True
            
        except Exception as e:
            print(f"写入Canvas文件失败: {e}")
            return False

    def _write_formatted_canvas(self, file_handle, canvas_data: Dict):
        """
        写入格式化的Canvas JSON，每个节点和边单独一行

        Args:
            file_handle: 文件句柄
            canvas_data: Canvas数据
        """
        file_handle.write('{\n')
        file_handle.write('\t"nodes":[\n')

        # 写入节点，每个节点一行
        nodes = canvas_data.get("nodes", [])
        for i, node in enumerate(nodes):
            node_json = json.dumps(node, ensure_ascii=False, separators=(',', ':'))
            if i < len(nodes) - 1:
                file_handle.write(f'\t\t{node_json},\n')
            else:
                file_handle.write(f'\t\t{node_json}\n')

        file_handle.write('\t],\n')
        file_handle.write('\t"edges":[\n')

        # 写入边，每个边一行
        edges = canvas_data.get("edges", [])
        for i, edge in enumerate(edges):
            edge_json = json.dumps(edge, ensure_ascii=False, separators=(',', ':'))
            if i < len(edges) - 1:
                file_handle.write(f'\t\t{edge_json},\n')
            else:
                file_handle.write(f'\t\t{edge_json}\n')

        file_handle.write('\t]\n')
        file_handle.write('}')
    
    def _merge_canvas_data(self, new_data: Dict) -> Dict:
        """
        合并Canvas数据，保留手动调整的节点位置和边
        
        Args:
            new_data: 新的Canvas数据
            
        Returns:
            Dict: 合并后的Canvas数据
        """
        existing_data = self.read_canvas()
        if not existing_data:
            return new_data
        
        # 合并节点
        merged_nodes = self._merge_nodes(
            existing_data.get("nodes", []), 
            new_data.get("nodes", [])
        )
        
        # 合并边
        merged_edges = self._merge_edges(
            existing_data.get("edges", []), 
            new_data.get("edges", [])
        )
        
        return {
            "nodes": merged_nodes,
            "edges": merged_edges
        }
    
    def _merge_nodes(self, existing_nodes: List[Dict], new_nodes: List[Dict]) -> List[Dict]:
        """
        合并节点，保留手动调整的位置
        
        Args:
            existing_nodes: 现有节点列表
            new_nodes: 新节点列表
            
        Returns:
            List[Dict]: 合并后的节点列表
        """
        # 创建现有节点的映射（基于文件路径或ID）
        existing_by_file = {}
        existing_by_id = {}
        manual_nodes = []  # 手动创建的节点
        
        for node in existing_nodes:
            if node.get("type") == "file" and "file" in node:
                existing_by_file[node["file"]] = node
            elif node.get("type") == "group":
                existing_by_id[node.get("id", "")] = node
            elif node.get("id") and not self._is_auto_generated_id(node["id"]):
                # 可能是手动创建的节点
                manual_nodes.append(node)
        
        merged_nodes = []
        processed_files = set()
        processed_ids = set()
        
        # 处理新节点
        for new_node in new_nodes:
            if new_node.get("type") == "file" and "file" in new_node:
                file_path = new_node["file"]
                processed_files.add(file_path)
                
                if file_path in existing_by_file:
                    # 保留现有节点的位置，更新其他属性
                    existing_node = existing_by_file[file_path]
                    merged_node = new_node.copy()
                    merged_node["x"] = existing_node.get("x", new_node["x"])
                    merged_node["y"] = existing_node.get("y", new_node["y"])
                    merged_node["width"] = existing_node.get("width", new_node.get("width", 400))
                    merged_node["height"] = existing_node.get("height", new_node.get("height", 300))
                    merged_nodes.append(merged_node)
                else:
                    # 新文档，使用计算位置
                    merged_nodes.append(new_node)
                    
            elif new_node.get("type") == "group":
                group_id = new_node.get("id", "")
                processed_ids.add(group_id)
                
                if group_id in existing_by_id:
                    # 保留现有组的位置和尺寸
                    existing_group = existing_by_id[group_id]
                    merged_group = new_node.copy()
                    merged_group["x"] = existing_group.get("x", new_node["x"])
                    merged_group["y"] = existing_group.get("y", new_node["y"])
                    merged_group["width"] = existing_group.get("width", new_node["width"])
                    merged_group["height"] = existing_group.get("height", new_node["height"])
                    merged_nodes.append(merged_group)
                else:
                    # 新组，使用计算位置
                    merged_nodes.append(new_node)
        
        # 添加保留的手动节点（不在新数据中但仍需保留）
        for manual_node in manual_nodes:
            if manual_node.get("type") == "file":
                file_path = manual_node.get("file", "")
                if file_path and file_path not in processed_files:
                    # 检查文件是否仍存在
                    if self._file_exists(file_path):
                        merged_nodes.append(manual_node)
            else:
                # 非文件节点（如文本、链接等），保留
                merged_nodes.append(manual_node)

        # 清理不再需要的旧组（不在新数据中的组）
        # 注意：只清理自动生成的组，保留手动创建的组
        for existing_node in existing_nodes:
            if (existing_node.get("type") == "group" and
                existing_node.get("id") not in processed_ids and
                self._is_auto_generated_group_id(existing_node.get("id", ""))):
                # 这是一个不再需要的自动生成组，不添加到merged_nodes中
                print(f"  清理无效组: {existing_node.get('label', 'Unknown')} (ID: {existing_node.get('id', 'Unknown')})")

        return merged_nodes
    
    def _merge_edges(self, existing_edges: List[Dict], new_edges: List[Dict]) -> List[Dict]:
        """
        合并边，保留手动创建的连接
        
        Args:
            existing_edges: 现有边列表
            new_edges: 新边列表
            
        Returns:
            List[Dict]: 合并后的边列表
        """
        # 创建边的唯一标识符
        def edge_key(edge):
            return f"{edge.get('fromNode', '')}_{edge.get('toNode', '')}"
        
        # 现有边的映射
        existing_edge_keys = {edge_key(edge): edge for edge in existing_edges}
        new_edge_keys = {edge_key(edge) for edge in new_edges}
        
        merged_edges = []
        
        # 添加新边
        for new_edge in new_edges:
            merged_edges.append(new_edge)
        
        # 保留手动创建的边（不在新边列表中的）
        for existing_edge in existing_edges:
            edge_k = edge_key(existing_edge)
            if edge_k not in new_edge_keys:
                # 验证边的节点是否仍存在
                if self._validate_edge_nodes(existing_edge):
                    merged_edges.append(existing_edge)
        
        return merged_edges
    
    def _is_auto_generated_id(self, node_id: str) -> bool:
        """
        判断是否是自动生成的节点ID
        
        Args:
            node_id: 节点ID
            
        Returns:
            bool: 是自动生成ID返回True
        """
        # 检查是否符合自动生成ID的模式
        # 例如：COMP_XXXX、REQ001、DES002等
        import re
        patterns = [
            r'^[A-Z_]+[A-F0-9]{6}$',  # 组件_哈希格式
            r'^[A-Z_]+\d{3}[A-F0-9]{0,6}$',  # 文档ID格式
        ]
        
        for pattern in patterns:
            if re.match(pattern, node_id):
                return True
        return False

    def _is_auto_generated_group_id(self, group_id: str) -> bool:
        """
        检查组ID是否为自动生成

        Args:
            group_id: 组ID

        Returns:
            bool: 是否为自动生成组ID
        """
        # 自动生成的组ID通常是短的随机字符串，如a57e6d93aec1
        import re
        # 12位十六进制字符串，或者是组件代码（REQ、DES等）
        return bool(re.match(r'^[a-f0-9]{12}$', group_id) or
                   group_id in ['REQ', 'DES', 'DEV', 'QA', 'DEL', 'PM', 'PROD', 'READ', 'REQU', 'CUST', 'MAIN', 'FW', 'SW'])

    def _file_exists(self, file_path: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件存在返回True
        """
        try:
            # Canvas中的路径是相对于项目根目录的
            full_path = self.project_path / file_path
            return full_path.exists()
        except Exception:
            return False
    
    def _validate_edge_nodes(self, edge: Dict) -> bool:
        """
        验证边的节点是否仍然存在
        
        Args:
            edge: 边数据
            
        Returns:
            bool: 节点都存在返回True
        """
        # 简化验证：假设边是有效的
        # 实际实现中可以检查fromNode和toNode是否在节点列表中
        return bool(edge.get("fromNode") and edge.get("toNode"))
    
    def _backup_canvas(self) -> bool:
        """
        备份Canvas文件
        
        Returns:
            bool: 备份成功返回True
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"product_canvas_backup_{timestamp}.json"
            shutil.copy2(self.canvas_file, backup_file)
            
            # 只保留最近5个备份
            self._cleanup_old_backups()
            
            return True
        except Exception as e:
            print(f"备份Canvas文件失败: {e}")
            return False
    
    def _cleanup_old_backups(self, keep_count: int = 5):
        """
        清理旧的备份文件
        
        Args:
            keep_count: 保留的备份数量
        """
        try:
            backup_files = list(self.backup_dir.glob("product_canvas_backup_*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for backup_file in backup_files[keep_count:]:
                backup_file.unlink()
                
        except Exception as e:
            print(f"清理备份文件失败: {e}")
    
    def extract_edges_to_associations(self) -> List[Dict]:
        """
        从Canvas边提取文档关联关系
        
        Returns:
            List[Dict]: 关联关系列表
        """
        canvas_data = self.read_canvas()
        if not canvas_data:
            return []
        
        associations = []
        edges = canvas_data.get("edges", [])
        nodes = canvas_data.get("nodes", [])
        
        # 创建节点ID到文档信息的映射
        node_map = {}
        for node in nodes:
            if node.get("type") == "file" and "file" in node:
                node_map[node["id"]] = {
                    "file_path": node["file"],
                    "doc_id": self._extract_doc_id_from_node_id(node["id"])
                }
        
        # 处理边
        for edge in edges:
            from_node_id = edge.get("fromNode")
            to_node_id = edge.get("toNode")
            
            if from_node_id in node_map and to_node_id in node_map:
                from_doc = node_map[from_node_id]
                to_doc = node_map[to_node_id]
                
                association = {
                    "from_doc_id": from_doc["doc_id"],
                    "to_doc_id": to_doc["doc_id"],
                    "from_file_path": from_doc["file_path"],
                    "to_file_path": to_doc["file_path"],
                    "relation_type": "Canvas关联",
                    "created_time": datetime.now().isoformat(),
                    "source": "canvas_manual"
                }
                associations.append(association)
        
        return associations
    
    def _extract_doc_id_from_node_id(self, node_id: str) -> str:
        """
        从节点ID提取文档ID
        
        Args:
            node_id: 节点ID
            
        Returns:
            str: 文档ID
        """
        # 简化实现：假设节点ID包含文档ID
        # 实际实现中需要根据具体的ID生成规则来解析
        import re
        
        # 尝试提取文档ID模式
        match = re.search(r'([A-Z_]+\d{3})', node_id)
        if match:
            return match.group(1)
        
        return node_id  # 回退到使用完整节点ID
    
    def create_canvas(self) -> bool:
        """
        创建空的Canvas文件
        
        Returns:
            bool: 创建成功返回True
        """
        empty_canvas = {
            "nodes": [],
            "edges": []
        }
        
        return self.write_canvas(empty_canvas, mode="full")
    
    def get_canvas_info(self) -> Dict:
        """
        获取Canvas文件信息
        
        Returns:
            Dict: Canvas信息
        """
        canvas_data = self.read_canvas()
        if not canvas_data:
            return {
                "exists": False,
                "node_count": 0,
                "edge_count": 0,
                "file_size": 0
            }
        
        file_size = self.canvas_file.stat().st_size if self.canvas_file.exists() else 0
        
        return {
            "exists": True,
            "node_count": len(canvas_data.get("nodes", [])),
            "edge_count": len(canvas_data.get("edges", [])),
            "file_size": file_size,
            "last_modified": datetime.fromtimestamp(
                self.canvas_file.stat().st_mtime
            ).isoformat() if self.canvas_file.exists() else None
        }


def main():
    """测试函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python canvas_manager.py <project_path>")
        return
    
    project_path = Path(sys.argv[1])
    manager = CanvasManager(project_path)
    
    # 创建Canvas文件
    if not manager.canvas_file.exists():
        manager.create_canvas()
    
    # 显示统计信息
    stats = manager.get_canvas_info()
    print("Canvas统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main() 