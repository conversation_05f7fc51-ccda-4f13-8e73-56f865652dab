{"nodes": [{"id": "f92f149a687af81b", "type": "text", "text": "新项目", "x": -340, "y": -260, "width": 250, "height": 60}, {"id": "c9b869e3cf8db34d", "type": "text", "text": "\t1. 项目建立", "x": -340, "y": -40, "width": 250, "height": 60}, {"id": "12ae4b4db8ed8571", "type": "text", "text": "demo：项目工程", "x": 360, "y": -130, "width": 250, "height": 60}, {"id": "15b9e07e5f7cc426", "type": "text", "text": "脚本： 项目文件夹创建\n+ 选择创建方式：\n\t+ 新建\n    + 拷贝", "x": 360, "y": -10, "width": 250, "height": 150}, {"id": "935bbab294318264", "type": "text", "text": "1. 新建立\n2. 拷贝demo项目", "x": -40, "y": -40, "width": 250, "height": 60}, {"id": "ebf5dbcd5f7c50bf", "type": "text", "text": "2. 文件添加", "x": -340, "y": 400, "width": 250, "height": 60}, {"id": "fa246debfa767b53", "type": "text", "text": "MCP server：\n官方文档和demo导入", "x": -14, "y": 347, "width": 250, "height": 60}, {"id": "11b01597bc5323a6", "type": "text", "text": "文档结构", "x": 360, "y": -230, "width": 250, "height": 60}, {"id": "b59c445432272696", "type": "text", "text": "3. 软件开发需求同步", "x": -340, "y": 740, "width": 250, "height": 60}, {"id": "b34fd4aa6547c1c9", "type": "text", "text": "4. 开发", "x": -340, "y": 1060, "width": 250, "height": 60}, {"id": "1e1ca5f87e2ab890", "type": "text", "text": "5. 配置", "x": -340, "y": 1380, "width": 250, "height": 60}, {"id": "10dce9ff00f50ebb", "type": "text", "text": "target - mirco define 可视化配置", "x": 33, "y": 1371, "width": 250, "height": 60}, {"id": "dcf2538fffde2744", "type": "text", "text": "6. 文档", "x": -340, "y": 1700, "width": 250, "height": 60}, {"id": "526232ffc9cbf304", "type": "file", "file": "KA/Product development/嵌入式项目开发流程/embedded_issue-demo.md", "x": 33, "y": 1780, "width": 367, "height": 260}], "edges": []}