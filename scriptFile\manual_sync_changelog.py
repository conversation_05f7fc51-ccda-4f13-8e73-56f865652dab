#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
Description: Manually sync CHANGELOG.md updates to the manual files
This script can be run manually to synchronize all CHANGELOG.md files to their
corresponding manual files, regardless of the last sync date.

Usage:
  python manual_sync_changelog.py             # Test all components
  python manual_sync_changelog.py lensAdjust  # Test only lensAdjust component
'''

import os
import sys
import subprocess

def main():
    """Run the sync_changelog_to_manual.py script manually"""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Get the path to the sync_changelog_to_manual.py script
    sync_script = os.path.join(script_dir, "sync_changelog_to_manual.py")
    
    if not os.path.exists(sync_script):
        print(f"Error: Sync script not found at {sync_script}")
        sys.exit(1)
    
    # Force update by setting a very old last sync date
    os.environ["FORCE_SYNC"] = "1"
    
    # Check if a specific component was specified
    target_component = None
    if len(sys.argv) > 1:
        target_component = sys.argv[1]
        print(f"Testing only component: {target_component}")
        os.environ["TARGET_COMPONENT"] = target_component
    
    # Make backups of manual files before testing
    if target_component:
        backup_manual_file(target_component)
    else:
        from sync_changelog_to_manual import TARGET_LISTS
        for component in TARGET_LISTS:
            backup_manual_file(component)
    
    # Run the sync script
    try:
        print(f"Running sync script: {sync_script}")
        result = subprocess.run([sys.executable, sync_script], check=True)
        print(f"Sync completed with return code: {result.returncode}")
    except subprocess.CalledProcessError as e:
        print(f"Error: Sync failed with return code {e.returncode}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: Failed to run sync script: {e}")
        sys.exit(1)
    
    print("Manual sync completed successfully")

def backup_manual_file(component):
    """Create a backup of the manual file before testing"""
    manual_dir = "build/MinSizeRel/output/bin/manual"
    
    # Handle special case for lensAdjust
    if component == "lensAdjust":
        manual_file = "lensAdjust_manual.md"
    else:
        manual_file = f"{component}_manual.md"
    
    manual_path = os.path.join(manual_dir, component, manual_file)
    backup_path = manual_path + ".bak"
    
    if os.path.exists(manual_path):
        print(f"Backing up {manual_path} to {backup_path}")
        try:
            import shutil
            shutil.copy2(manual_path, backup_path)
            print(f"Backup created: {backup_path}")
        except Exception as e:
            print(f"Warning: Failed to create backup: {e}")

if __name__ == "__main__":
    main() 