#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化系统扩展点注册管理器
用于注册和管理各类扩展点
"""

import os
import importlib
import importlib.util
import inspect
import logging
from typing import Dict, Type, Any, List, Optional


class ExtensionRegistry:
    """可视化扩展点注册管理器"""
    
    def __init__(self):
        """初始化注册管理器"""
        self.data_sources = {}
        self.visualizations = {}
        self.exporters = {}
        self.logger = logging.getLogger("extension_registry")
        
    def register_data_source(self, name: str, data_source_class: Type):
        """注册数据源
        
        Args:
            name: 数据源名称
            data_source_class: 数据源类
        """
        self.logger.info(f"注册数据源: {name}")
        self.data_sources[name] = data_source_class
        
    def register_visualization(self, name: str, visualization_class: Type):
        """注册可视化组件
        
        Args:
            name: 可视化组件名称
            visualization_class: 可视化组件类
        """
        self.logger.info(f"注册可视化组件: {name}")
        self.visualizations[name] = visualization_class
        
    def register_exporter(self, name: str, exporter_class: Type):
        """注册导出器
        
        Args:
            name: 导出器名称
            exporter_class: 导出器类
        """
        self.logger.info(f"注册导出器: {name}")
        self.exporters[name] = exporter_class
        
    def get_data_source(self, name: str, config: Optional[Dict] = None) -> Any:
        """获取数据源实例
        
        Args:
            name: 数据源名称
            config: 配置字典
            
        Returns:
            数据源实例
            
        Raises:
            ValueError: 如果数据源未注册
        """
        if name not in self.data_sources:
            raise ValueError(f"未注册的数据源: {name}")
        return self.data_sources[name](config)
        
    def get_visualization(self, name: str, config: Optional[Dict] = None) -> Any:
        """获取可视化组件实例
        
        Args:
            name: 可视化组件名称
            config: 配置字典
            
        Returns:
            可视化组件实例
            
        Raises:
            ValueError: 如果可视化组件未注册
        """
        if name not in self.visualizations:
            raise ValueError(f"未注册的可视化组件: {name}")
        return self.visualizations[name](config)
        
    def get_exporter(self, name: str, config: Optional[Dict] = None) -> Any:
        """获取导出器实例
        
        Args:
            name: 导出器名称
            config: 配置字典
            
        Returns:
            导出器实例
            
        Raises:
            ValueError: 如果导出器未注册
        """
        if name not in self.exporters:
            raise ValueError(f"未注册的导出器: {name}")
        return self.exporters[name](config)
        
    def get_all_data_sources(self) -> Dict[str, Type]:
        """获取所有已注册的数据源类"""
        return self.data_sources
        
    def get_all_visualizations(self) -> Dict[str, Type]:
        """获取所有已注册的可视化组件类"""
        return self.visualizations
        
    def get_all_exporters(self) -> Dict[str, Type]:
        """获取所有已注册的导出器类"""
        return self.exporters
        
    def auto_discover(self, package_path: str, base_class: Type) -> List[Type]:
        """自动发现并注册符合条件的类
        
        从指定的包路径中发现所有继承自指定基类的类，并返回
        
        Args:
            package_path: 包路径
            base_class: 基类
            
        Returns:
            发现的类列表
        """
        discovered_classes = []
        
        # 获取包中所有模块
        for root, dirs, files in os.walk(package_path):
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    module_path = os.path.join(root, file)
                    module_name = os.path.splitext(os.path.basename(module_path))[0]
                    
                    try:
                        # 动态导入模块
                        spec = importlib.util.spec_from_file_location(module_name, module_path)
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                        
                        # 查找所有继承自基类的类
                        for name, obj in inspect.getmembers(module):
                            if (inspect.isclass(obj) and issubclass(obj, base_class) and 
                                    obj != base_class and not name.startswith('_')):
                                discovered_classes.append(obj)
                                self.logger.info(f"发现类: {name} 在模块 {module_name}")
                    except Exception as e:
                        self.logger.error(f"加载模块 {module_name} 失败: {e}")
                        
        return discovered_classes
        
    def auto_register_all(self, data_sources_path: str, visualizations_path: str, exporters_path: str):
        """自动注册所有扩展点
        
        Args:
            data_sources_path: 数据源路径
            visualizations_path: 可视化组件路径
            exporters_path: 导出器路径
        """
        # 导入基类
        from ..data_sources.base import DataSourceAdapter
        
        # 自动注册数据源
        data_source_classes = self.auto_discover(data_sources_path, DataSourceAdapter)
        for cls in data_source_classes:
            name = cls.__name__.lower().replace('datasource', '')
            self.register_data_source(name, cls)
            
        # 其他类型同理，需要先导入基类
        # ...


# 创建全局注册表实例
registry = ExtensionRegistry()


# 使用示例
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # 自动注册所有扩展点
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_dir = os.path.dirname(script_dir)
    
    registry.auto_register_all(
        os.path.join(base_dir, "data_sources"),
        os.path.join(base_dir, "visualizations"),
        os.path.join(base_dir, "exporters")
    )
    
    # 显示已注册的扩展点
    print("已注册的数据源:")
    for name, cls in registry.get_all_data_sources().items():
        print(f"  - {name}: {cls.__name__}")
        
    print("已注册的可视化组件:")
    for name, cls in registry.get_all_visualizations().items():
        print(f"  - {name}: {cls.__name__}")
        
    print("已注册的导出器:")
    for name, cls in registry.get_all_exporters().items():
        print(f"  - {name}: {cls.__name__}") 