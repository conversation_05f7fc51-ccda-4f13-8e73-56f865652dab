
import json
import os
import argparse

def generate_compatible_canvas(config_path: str, output_path: str):
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)

    canvas = {
        "nodes": [],
        "edges": []
    }

    # Layout parameters
    x_base = 100
    y_base = 100
    x_step = 300
    component_nodes = {}
    node_index = 0

    # Create unique nodes
    for conn in config["workflow"]["connections"]:
        for component in [conn["from"], conn["to"]]:
            if component not in component_nodes:
                node_id = f"{component}"
                node = {
                    "id": node_id,
                    "x": x_base + x_step * node_index,
                    "y": y_base,
                    "width": 250,
                    "height": 100,
                    "type": "text",
                    "text": component,
                    "color": "default"
                }
                component_nodes[component] = node
                canvas["nodes"].append(node)
                node_index += 1

    # Create edges
    for conn in config["workflow"]["connections"]:
        edge = {
            "id": f"{conn['from']}_{conn['to']}",
            "fromNode": conn["from"],
            "toNode": conn["to"],
            "fromSide": "right",
            "toSide": "left"
        }
        canvas["edges"].append(edge)

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(canvas, f, ensure_ascii=False, indent=2)

    print(f"Canvas file generated: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Convert workflow_config.json to Obsidian Canvas')
    parser.add_argument('--config', default='workflow_config.json', help='Path to workflow_config.json')
    parser.add_argument('--output', default='workflow.canvas', help='Output .canvas file path')
    args = parser.parse_args()

    generate_compatible_canvas(args.config, args.output)

if __name__ == '__main__':
    main()
