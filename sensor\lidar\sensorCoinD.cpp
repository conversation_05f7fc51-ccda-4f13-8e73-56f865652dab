#include "sensorCoinD.h"
#include "checkCalculate.h"
#include "coinSensorP.h"

#include "qLog.h"
#include "typeConvert.h"


CSensorCoinD::CSensorCoinD(IComm *port_) : im_port_(port_) {
    m_protocol_ = new CCoinSensorP;

    //* 此处应该可以选择数据接收方式
    im_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

    cmd_init();
}

CSensorCoinD::~CSensorCoinD() {
    if (m_protocol_ != nullptr)
        delete m_protocol_;
}

/**
 * @brief 固定指令直接初始化，有变化指令用成员变量，只初始化固定部分
 *
 */
void CSensorCoinD::cmd_init(void) {
    uint16_t data;
    //*******************固定指令初始化*******************
    //  m_cmd["start"] = m_protocol_->getControlCmd();

    m_cmd["chipId"]    = m_protocol_->getWriteCmd(kMcuId, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(KNone)));
    m_cmd["caliMode"]  = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofCalibrationMode)));  //
    m_cmd["greyMap"]   = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofFaculaMode)));       //
    m_cmd["cloudMode"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofScanMode)));         //

    //  m_cmd["stop"] = m_protocol_->getControlCmd();
    // LOG_INFO(MyLogger::LogType::INIT, QString("start cmd: ").arg(NsTypeConvert::byteArrayToString(m_cmd["start"])));
    // LOG_INFO(MyLogger::LogType::INIT, QString("stop cmd: ").arg(NsTypeConvert::byteArrayToString(m_cmd["stop"])));
    LOG_INFO(MyLogger::LogType::INIT, QString("chipId cmd %1").arg(NsTypeConvert::byteArrayToString(m_cmd["chipId"])));
    LOG_INFO(MyLogger::LogType::INIT, QString("caliMode cmd %1").arg(NsTypeConvert::byteArrayToString(m_cmd["caliMode"])));
    LOG_INFO(MyLogger::LogType::INIT, QString("greyMap cmd %1").arg(NsTypeConvert::byteArrayToString(m_cmd["greyMap"])));
    LOG_INFO(MyLogger::LogType::INIT, QString("cloudMode cmd %1").arg(NsTypeConvert::byteArrayToString(m_cmd["cloudMode"])));
    //******************变化指令初始化********************
}

QByteArray CSensorCoinD::portDataRead() {
    return im_port_->read(5);
}

/**
 * @brief icom_change_interface
 * @param port_
 */
void CSensorCoinD::icom_change_interface(IComm *port_) {
    im_port_ = port_;
}

bool CSensorCoinD::unlockModule() {
    m_strPre.clear();

    return im_port_->write(m_unlock_cmd);  //
}

#if 0
/****************
 * 对于同一类型有多个选项，采用直接写入选项的方式
 * 1. 枚举切换方式：增加了枚举变量，而且添加模式会改动现有函数
 * 2. 每个模式对应一个函数：单一性，但函数会过多
 * *******************************/
/**
 * @brief 切换模式
 * @return
 */
bool CSensorCoinD::modeChange(const EModeType &mode)
{
    bool flag = false;
    switch (mode) {
    case eSINGLE_MODE:  break;
    case eCALI_MODE: flag = im_port_->write(m_cmd["caliMode"]); break;
    case eGREY_MODE: flag = im_port_->write(m_cmd["caliMode"]); break;
    case eCLOUD_MODE: flag = im_port_->write(m_cmd["caliMode"]); break;
    default: flag = false; break;

    }
    return flag;
}
#else
/**
 * @brief 直接写入模式
 * @param mode
 * @return
 */
bool CSensorCoinD::modeChange(const uint16_t &mode) {
    m_strPre.clear();
    QByteArray send_cmd = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(mode));
    LOG_INFO(MyLogger::LogType::COMM, QString("cmd/mode change: %1").arg(NsTypeConvert::byteArrayToString(send_cmd)));

    return im_port_->write(send_cmd);  //
}
#endif
/**
 * @brief 读信息
 * @return
 */
bool CSensorCoinD::readInfo(const uint8_t &id, const uint16_t &data) {
    m_strPre.clear();
    QByteArray cmd = m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data));

    LOG_INFO(MyLogger::LogType::COMM, QString("cmd/read: %1").arg(NsTypeConvert::byteArrayToString(cmd)));

    return im_port_->write(m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data)));  //
}

/**
 * @brief CSensorCoinD::changeRigster
 * @return
 */
bool CSensorCoinD::changeRigster(){

};

#if 1
/**
 * @brief 交互指令解析
 * @param str
 * @param length
 * @return
 */
bool CSensorCoinD::interactionParsing(QByteArray str, int length) {
    Q_UNUSED(length);
    if (str.length() == 0)
        return false;

    QByteArray strSum = m_strPre + str;
    m_strPre.clear();

#if 0  // test buff
    uint8_t buff[112] = {0xa5, 0x22, 0xad, 0x0B, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x10, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa5, 0x16, 0xa1, 0x12, 0x00, 0x00};
    QByteArray strSum((char*)buff, 112);

#endif
    if (strSum.length() < (uint16_t)CCoinSensorP::eHEADER_LEN) {  //固定部分长度6
        m_strPre = strSum;
        return false;
    } else if (strSum.length() > 2000) {  //防止异常解析
        m_strPre.clear();
        return false;
    }

#ifdef COMM_ACK
    qDebug() << "-i sensorBoard/ interaction ack:" << NsTypeConvert::byteArrayToString(strSum);
#endif

    /*1.1 parse*/
    for (;;) {  //留6个数
        if (strSum.length() >= (uint16_t)CCoinSensorP::eHEADER_LEN) {
            if ((uchar)strSum.at(0) == (uint16_t)CCoinSensorP::eHEADER) {

                uint8_t data_num = ((strSum.at((uint16_t)(CCoinSensorP::eNUM_INDEX)) & 0xff) | (strSum.at((uint16_t(CCoinSensorP::eNUM_INDEX + 1))) << 8)) << 1;
                uint8_t frame_num = (data_num + (uint8_t)CCoinSensorP::eHEADER_LEN);

                if (frame_num > 0xff)
                    strSum.remove(0, 1);
                //帧头  && ((uchar)strSum.at(i + 1) == (CCoinSensorP::kD2H | CCoinSensorP::kHRS | CCoinSensorP::kHSS))
                if (strSum.length() >= frame_num) {  //数量符合
                    QByteArray             cache_buff, data_buff;
                    CCoinSensorP::StFrame *st_data_receive_ = nullptr;  // new CCoinSensorP::StFrame;

                    cache_buff.append(strSum, frame_num);
                    //                    memcpy(&cache_buff, strSum.data(), frame_num);
                    st_data_receive_ = (CCoinSensorP::StFrame *)cache_buff.data();
                    //                  CCoinSensorP::StFrame* data_receive_ = new CCoinSensorP::StFrame;
                    //                  data_receive_ = reinterpret_cast<CCoinSensorP::StFrame *>(strSum.data());

                    uint8_t check_xor = CCheckCalculate::calc_checkXor(strSum, frame_num, (uint8_t)CCoinSensorP::eXOR_INDEX);
                    if (check_xor == st_data_receive_->check_xor) {
                        data_buff.append((char *)st_data_receive_->data, data_num);

                        switch ((EProtocolId)st_data_receive_->id) {
                        case kTofMode:
                            emit dataOutput(ECommStep::eMODE_CHANGE, ECommStatus::eCOMM_COMP, data_buff);
                            break;
                        case kMcuId:
                            emit dataOutput(ECommStep::eCHIP_ID, ECommStatus::eCOMM_COMP, data_buff);
                            break;
                        case kVersionBuad:
                            emit dataOutput(ECommStep::eVERSION, ECommStatus::eCOMM_COMP, data_buff);
                            break;
                        default:
                            break;
                        }

                        strSum.remove(0, frame_num);
                    } else
                        strSum.remove(0, 1);
                } else {
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
            } else
                strSum.remove(0, 1);
        } else {
            m_strPre.clear();
            m_strPre.push_back(strSum);  //存储剩余数据
            return false;
        }
    }
}

#else
/**
 * @brief 交互指令解析
 * @param str
 * @param length
 * @return
 */
bool CSensorCoinD::interactionParsing(QByteArray str, int length) {
    Q_UNUSED(length);
    if (str.length() == 0)
        return false;

    QByteArray strSum = m_strPre + str;
    m_strPre.clear();

    if (strSum.length() < CCoinSensorP::eHEADER_LEN)  //固定部分长度6
    {
        m_strPre = strSum;
        return false;
    } else if (strSum.length() > 2000) {  //防止异常解析
        m_strPre.clear();
        return false;
    }
#ifdef COMM_OUTPUT
    qDebug() << "-i sensorBoard/ interaction ack:";
    NsTypeConvert::byteArrayToString(strSum);
#endif

    /*1.1 parse*/
    for (;;) {  //留6个数
        if (strSum.length() >= CCoinSensorP::eHEADER_LEN) {
            if ((uchar)strSum.at(0) ==
                CCoinSensorP::eHEADER) {  //帧头  && ((uchar)strSum.at(i + 1) == (CCoinSensorP::kD2H | CCoinSensorP::kHRS | CCoinSensorP::kHSS))
                //*
                uint8_t                xor_cal       = 0;
                CCoinSensorP::StFrame *data_receive_ = new CCoinSensorP::StFrame;

                //*
                data_receive_ = reinterpret_cast<CCoinSensorP::StFrame *>(strSum.data());
                uint16_t num  = data_receive_->num << 1;  //字节数
                qDebug() << "-i sensorBoard/ interaction data num:" << num;

                if (strSum.length() >= (num + CCoinSensorP::eHEADER_LEN)) {  //数量符合
                    QByteArray array_tmp;
                    memcmp(&array_tmp, &strSum, CCoinSensorP::eHEADER_LEN);

                    //                  data_receive_->data.resize(num);
                    //                  data_receive_ = reinterpret_cast<CCoinSensorP::StFrame*>(array_tmp.data()); //qbytearray 无法 copy

                    QByteArray strTmp;  //指令数据
                    for (uint16_t n = 0; n < num + CCoinSensorP::eHEADER_LEN; ++n) {
                        if (n != (3)) {
                            xor_cal ^= (uchar)strSum.at(n);
                        }
                        if (n >= CCoinSensorP::eHEADER_LEN) {

                            strTmp.append((uchar)strSum.at(n));
                        }
                    }
                    if (xor_cal == data_receive_->check_xor) {
                        switch (data_receive_->id) {
                        case kMcuId:

                            //                          strTmp = data_receive_->data;
                            emit dataOutput(ECommStep::eCHIP_ID, ECommStatus::eCOMM_COMP, strTmp);
                            break;
                        case kTofMode:
                            strTmp.clear();

                            emit dataOutput(ECommStep::eMODE_CHANGE, ECommStatus::eCOMM_COMP, strTmp);
                            break;
                        default:
                            break;
                        }

                        strSum.remove(0, (num + CCoinSensorP::eHEADER_LEN));

                        m_strPre.push_back(strSum);  //存储剩余数据
                    } else {                         //校验不过，寻找下一字节，不输出错误信息，等待超时
                        strSum.remove(0, 1);
                    }
                } else {
                    m_strPre.push_back(strSum);  //存储剩余数据，等待数据补齐
                    return false;
                }
            } else
                strSum.remove(0, 1);
        } else {
            m_strPre.push_back(strSum);  //存储剩余数据
            return false;
        }
    }
}
#endif
/**
 * @brief dtof 灰度图解析 迭代
 */
bool CSensorCoinD::greyParsing(QByteArray str, int length) {
    QByteArray strSum = m_strPre + str;
    if (strSum.length() < 6) {  //固定部分长度6
        m_strPre = strSum;
        return false;
    } else if (strSum.length() > 2000) {  //防止异常解析
        m_strPre.clear();
        return false;
    }

    QByteArray strTmp;  //指令数据
    int        i = 0;
    uint16_t   num;
    uint8_t    xor_cal;
    uint32_t   data_tmp;

    /*1.1 parse*/
    for (i = 0; i < (strSum.length() - 5); ++i) {                                                                       //留6个数
        if (((uchar)strSum.at(i) == 0xA5) && ((uchar)strSum.at(i + 1) == (CCoinSensorP::kD2H | CCoinSensorP::kDFF))) {  //帧头
            switch ((uchar)strSum.at(i + 2)) {                                                                          //
            case 0xAD:                                                                                                  //数据反馈
                num     = (uchar)strSum.at(i + 4) + uint16_t((uchar)strSum.at(i + 5) << 8);
                xor_cal = 0;
                if ((strSum.length() - i) >= (2 * num + 6)) {  //数量符合
                    strTmp.clear();
                    for (uint16_t n = i; n < i + 2 * num + 6; ++n) {
                        if (n != (3 + i)) {
                            xor_cal ^= (uchar)strSum.at(n);
                        }
                        if (n > (i + 5))
                            strTmp.push_back(strSum.at(n));  //
                    }
                    if (xor_cal == (uchar)strSum.at(i + 3)) {
                        //                        for(uint16_t n = 0; n < (num>>1); ++n)
                        //                        {
                        //                            data_tmp = (uchar)strSum.at(i + 6 + (n<<2) + 0) | ((uchar)strSum.at(i + 6 + (n<<2) + 1)<<8) |
                        //                            ((uchar)strSum.at(i + 6 + (n<<2) + 2)<<16) | ((uchar)strSum.at(i + 6 + (n<<2) + 3)<<24);
                        //                            data_tmp_v.push_back(data_tmp); //
                        //                        }
                        //                        data_tmp_v.clear(); //
#if 0  //发送解析后的值
                        for(uint16_t n = 0; n < (num>>1); ++n) //
                        {
                            data_tmp = (uchar)strTmp.at((n<<2) + 0) | ((uchar)strTmp.at((n<<2) + 1)<<8) | ((uchar)strTmp.at((n<<2) + 2)<<16) | ((uchar)strTmp.at((n<<2) + 3)<<24);
                        }
#endif
                        emit dataOutput(ECommStep::eMAP_DATA, ECommStatus::eCOMM_COMP, strTmp);

                        strSum.remove(0, (i + 2 * num + 6));
                        m_strPre.clear();
                        m_strPre.push_back(strSum);  //存储剩余数据
                        return true;
                    } else {
                        i += 2;
                        // qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-clen xor: " << xor_cal;
                        strTmp.clear();
                        emit dataOutput(ECommStep::eMAP_DATA, ECommStatus::eCOMM_ERROR, strTmp);
                    }
                } else {
                    strSum.remove(0, i);  // i-1
                    m_strPre.clear();
                    m_strPre.push_back(strSum);  //存储剩余数据
                    return false;
                }
                break;
            default:  //无效指令
                ++i;  //
                break;
            }  //
        }
    }
    strSum.remove(0, i - 1);
    m_strPre.clear();
    m_strPre.push_back(strSum);  //存储剩余数据
    return false;
}

bool CSensorCoinD::histPrasing(QByteArray str, int length) {
}

/**
 * @brief 点云解析
 * @param str
 * @param length
 * @return
 */
bool CSensorCoinD::cloudPrasing(QByteArray str, int length) {
    Q_UNUSED(length);
    QString ret;
    for (int i = 0; i < str.count(); ++i) {
        ret.append(QString("0x%1,").arg((quint8)str.at(i), 2, 16, QLatin1Char('0')));
    }
    // qDebug() << "-yj-cloud -i: strSum" << ret;
    QByteArray stA = m_strPre + str;

    if (stA.length() < 10) {
        m_strPre = stA;
        if (m_strPre.size() > 102400) {
            m_strPre.clear();
        }
        return false;
    }
    m_strPre.clear();
    uint pointNumber = 0;
    // float spd = 0;
    float startAngle = 0;
    float endAngle   = 0;

    uint8_t checkSumL = 0, checkSumH = 0;

    StPointCloud3D pointCld2d(0, 0, 0, 0, 0);
    uint16_t       sum = 0;
    for (int n = 0; n < stA.length(); n++) {
        if (stA.length() - n >= 85) {
            // 1.0 接收设备数据        先移位再强制转化
            if ((uchar)stA.at(n) == 0xAA && (uchar)stA.at(n + 1) == 0x55 && (((uchar)stA.at(n + 2)) & 0x01) == 0x00) {

                checkSumL = 0;
                checkSumH = 0;
                checkSumL ^= (uint8_t)stA.at(0 + n) ^ (uint8_t)stA.at(2 + n) ^ (uint8_t)stA.at(4 + n) ^ (uint8_t)stA.at(6 + n);
                checkSumH ^= (uint8_t)stA.at(1 + n) ^ (uint8_t)stA.at(3 + n) ^ (uint8_t)stA.at(5 + n) ^ (uint8_t)stA.at(7 + n);
                if ((uchar)stA.at(n + 3) < 0x1A) {
                    for (int i = 0; i < (uchar)stA.at(n + 3); ++i) {
                        checkSumL ^= (uint8_t)stA.at(3 * i + 10 + n) ^ (uint8_t)stA.at(3 * i + 1 + 10 + n);
                        checkSumH ^= (uint8_t)stA.at(3 * i + 2 + 10 + n);
                    }

                    if (checkSumL == (uint8_t)stA.at(8 + n) && checkSumH == (uint8_t)(stA.at(9 + n)))  //校验通过
                    {
                        pointNumber = (uint8_t)stA.at(n + 3);
                        // spd = (((uint8_t)stA.at(n+4) | uint16_t(stA.at(n+5)<<8))>>6)/60.0;

                        startAngle      = (((uint8_t)stA.at(n + 4) | (uint16_t(stA.at(n + 5) << 8))) >> 1) / 64.0;
                        endAngle        = (((uint8_t)stA.at(n + 6) | (uint16_t(stA.at(n + 7) << 8))) >> 1) / 64.0;
                        double incAngle = 0;
                        // 2.0 计算角度
                        if (startAngle > endAngle) {
                            incAngle = (360 - startAngle + endAngle) / (pointNumber - 1);
                        } else {
                            incAngle = (endAngle - startAngle) / (pointNumber - 1);
                        }
                        int m = n + 10;
                        for (uint i = 0; i < pointNumber; i++) {
                            // 2.1 计算距离
                            quint16  down = ((uint8_t)stA.at(m + 3 * i + 1) >> 2) & 0x3f;
                            uint16_t up   = 0x00ff & stA.at(m + 3 * i + 2);  //左移高位会自动补1
                            up            = up << 6;

                            float tmpdepth = up | down;
                            // float tmpdepth =  float(((uint8_t)(stA.at(m+3*i+1)>>2)&0x3f) | uint16_t((stA.at(m+3*i+2))<<6));
                            float tmpAngle = startAngle + i * incAngle;

                            uint tmpIndensity = ((uint8_t)((stA.at(m + 3 * i + 1) & 0x03) << 6) | uint8_t(((stA.at(m + 3 * i)) >> 2) & 0x3f));
                            tmpAngle          = tmpAngle > 360 ? tmpAngle - 360.0 : tmpAngle;
                            // double tmpHeight = 0.0;

                            pointCld2d.m_angle     = tmpAngle;
                            pointCld2d.m_distance  = tmpdepth;
                            pointCld2d.m_intensity = tmpIndensity;
                            m_scanVec.push_back(pointCld2d);

                            if ((uchar)stA.at(n + 2) == 0x28) {
                                m_scanPointCloud.m_hardAndSoftwareVersion = 0x28;
                            }
                        }
                        stA.remove(0, n + pointNumber * 3 + 10);
                        n = -1;
                    }
                } else {
                    n += 3;
                    LOG_ERROR(MyLogger::LogType::COMM, "sensor data error");
                }
            }
            // 1.1 接收设备的 信息
            else if ((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n + 1) == 0x5A && (uchar)stA.at(n + 2) == 0x14 && (uchar)stA.at(n + 3) == 0x00) {
                for (int i = 0; i < 27; i++) {
                    if (i == 4 || i == 5) {
                        continue;
                    }
                    sum += (uint8_t)stA.at(i + n);
                }
                if (sum == ((uint8_t)stA.at(n + 4) | uint16_t(stA.at(n + 5) << 8))) {
                    m_scanPointCloud.m_version = (uint8_t)stA.at(n + 26);
                    m_version                  = (uint8_t)stA.at(n + 26);
                    stA.remove(0, n + 27);
                    n = -1;
                }

            }
            // 1.2 接收 固定的开始发送设备数据的 信息
            else if ((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n + 1) == 0x5A && (uchar)stA.at(n + 6) == 0x81) {

            }
            // 1.4 零度起始包
            else if ((uchar)stA.at(n) == 0xAA && (uchar)stA.at(n + 1) == 0x55 && (((uchar)stA.at(n + 2)) & 0x01) == 0x01) {
                // 4.0 校验 m_scanPointCloud
                checkSumL = 0;
                checkSumH = 0;
                checkSumL ^= (uint8_t)stA.at(0 + n) ^ (uint8_t)stA.at(2 + n) ^ (uint8_t)stA.at(4 + n) ^ (uint8_t)stA.at(6 + n);
                checkSumH ^= (uint8_t)stA.at(1 + n) ^ (uint8_t)stA.at(3 + n) ^ (uint8_t)stA.at(5 + n) ^ (uint8_t)stA.at(7 + n);

                if ((uchar)stA.at(n + 3) == 1) {
                    for (int i = 0; i < (uchar)stA.at(n + 3); i++) {
                        checkSumL ^= (uint8_t)stA.at(3 * i + 10 + n) ^ (uint8_t)stA.at(3 * i + 1 + 10 + n);
                        checkSumH ^= (uint8_t)stA.at(3 * i + 2 + 10 + n);
                    }

                    if (checkSumL == (uint8_t)stA.at(8 + n) && checkSumH == (uint8_t)(stA.at(9 + n)))  //校验通过
                    {
                        m_scanPointCloud.pointCloud2d = m_scanVec;
                        m_scanPointCloud.m_speed      = (((uchar)stA.at(n + 2)) >> 1) / 10.0;
                        m_scanPointCloud.m_healthCode = m_healthCode;
                        m_scanPointCloud.m_mcuVoltage = m_mcuVoltage;
                        QByteArray speed_tmp;
                        speed_tmp.append((uchar)stA.at(n + 2));
                        // emit dataOutput(MOTOR_MONITOR::data_step, MOTOR_MONITOR::data_flow, speed_tmp);
                        speed_tmp.clear();
                        m_scanVec.clear();
                        stA.remove(0, n + 13);
                        m_strPre = stA;
                        // n = -1;
                        return true;
                    } else {
                        LOG_ERROR(MyLogger::LogType::COMM, "checkSum err");
                        return false;
                    }
                } else {
                    n += 3;
                    LOG_ERROR(MyLogger::LogType::COMM, "sensor F data xor e");
                }
            }
        } else {
            if (stA.length() - n >= 11) {
                if ((uchar)stA.at(n) == 0xA5 && (uchar)stA.at(n + 1) == 0x5A && (uchar)stA.at(n + 2) == 0x04) {
                    // 3.0 校验 m_scanPointCloud
                    uint8_t checkXor = 0;
                    for (int i = 0; i < 11; i++) {
                        if (i == 7) {
                            continue;
                        }
                        checkXor ^= (uint8_t)stA.at(n + i);
                    }
                    // 3.1 获取健康信息 并删除该包长度信息
                    if (checkXor == (uchar)stA.at(n + 7)) {
                        m_healthCode     = (uint16_t)(stA.at(n + 8) << 8) | (uchar)stA.at(n + 9);
                        uint16_t voltage = (uint16_t)(stA.at(n + 4) << 8) | (uchar)stA.at(n + 5);
                        if (voltage != 0) {
                            m_mcuVoltage = ~(voltage + 122);
                            // qDebug() << "MCU VOLTAGE: " << m_mcuVoltage;
                        }
                        stA.remove(0, n + 11);
                        m_strPre = stA;
                        return false;
                    }
                }
            } else {
                m_strPre = stA;
                return false;
            }
        }
    }
    return false;
}
