#include "mywidget.h"
#include "assistant.h"
#include "ui_mywidget.h"
#include <QItemSelectionModel>
#include <QSqlRecord>

//#include "tabBarFontDir.h"

#define COMPONENT_NUM 5


myWidget::myWidget(QWidget *parent)
    : QWidget(parent),
      ui(new Ui::myWidget),
      timerId(0),
      timeCnt100ms(0),
      //    , motor_monitor_ptr_(new motorMonitorWidget)


      timeId_ptr_(new unionIDflag),
      m_tableView_database_(new ST_DATABASE) {

    ui->setupUi(this);

    this->setWindowTitle(MAIN_APP_WINDOW_TITLE);
    /* 01. ui init
     * 1.1 tab样式配置
     *      1. icon-每个tab单独配置
     *      2.
     */
    ui->Navigation->setIconSize(QSize(60, 60));
    ui->dateBaseSelect->setCurrentIndex(3);  //调焦
    m_db_select = 3;
    /* 02.工具栏*/

    /* 03. init*/
#if 1
    qDebug() << "database drive list";
    QStringList drivers = QSqlDatabase::drivers();
    foreach (QString driver, drivers)
        qDebug() << driver;
#endif
    //* connect location db
    MyConnSql::createConnection(MyConnSql::m_motor_speed_db.conn, MyConnSql::m_motor_speed_db.table_name);  //数据库初始化
    MyConnSql::createConnection(MyConnSql::m_lidar_speed_db.conn, MyConnSql::m_lidar_speed_db.table_name);
    MyConnSql::createConnection(MyConnSql::m_turn_on_db.conn, MyConnSql::m_turn_on_db.table_name);
    MyConnSql::createConnection(MyConnSql::m_mirror_adjust_db.conn, MyConnSql::m_mirror_adjust_db.table_name);

    /* 04. */
    timerId            = startTimer(5);  // 5ms定时器
    timeId_ptr_->allId = 0;
}

myWidget::~myWidget() {
    delete ui;

    //* components 释放
    if (motor_monitor_ptr_ != nullptr)
        delete motor_monitor_ptr_;
    if (lens_adjust_ptr_ != nullptr)
        delete lens_adjust_ptr_;
    if (mc_com_check_ != nullptr)
        delete mc_com_check_;
    if (mc_nova_calib_ != nullptr)
        delete mc_nova_calib_;

    if (myAssist_ptr_ != nullptr)
        delete myAssist_ptr_;
    if (m_model_ != nullptr)
        delete m_model_;

    delete timeId_ptr_;
    delete m_tableView_database_;
    killTimer(timerId);
}

/*循环任务*/
void myWidget::timerEvent(QTimerEvent *event) {
    if (event->timerId() == timerId) {
        // 1.1 来料电机检测
        if (timeId_ptr_->id1 == true) {
            QObject::disconnect(motor_monitor_ptr_, &motorMonitorWidget::motorMonitorCloseSiganl, this, &myWidget::motorMonitorFB);  //断开连接

            timeId_ptr_->id1 = false;
            delete motor_monitor_ptr_;
            motor_monitor_ptr_ = nullptr;
            this->showNormal();
        }
        // 1.2 调焦
        if (timeId_ptr_->id2 == true) {
            QObject::disconnect(lens_adjust_ptr_, &cLensAdjust::lensAdjustCloseSiganl, this, &myWidget::lensAdjustFB);  //断开连接
            timeId_ptr_->id2 = false;
            delete lens_adjust_ptr_;
            lens_adjust_ptr_ = nullptr;
            this->showNormal();
        }

        // 1.3
        //        if(timeId_ptr_->id3 == true) //电路板检测
        //        {
        //            QObject::disconnect(lens_adjust_ptr_, &cLensAdjust::lensAdjustCloseSiganl, this, &myWidget::lensAdjustFB); //断开连接
        //            timeId_ptr_->id3 = false;
        //            delete lens_adjust_ptr_;
        //            lens_adjust_ptr_ = nullptr;
        //            this->showNormal();
        //        }

        //        if(timeId_ptr_->id4 == true) //启动时间
        //        {
        //            QObject::disconnect(turn_on_time_, &turnOnTime::turnOnTimeCloseSignal, this, &myWidget::turnOnTimeFB); //断开连接
        //            timeId_ptr_->id4 = false;
        //            delete turn_on_time_;
        //            turn_on_time_ = nullptr;
        //            this->showNormal();
        //        }

        if (timeId_ptr_->id5 == true) {  //串口通信

            QObject::disconnect(mc_com_check_, &CComCheck::comCheckCloseSiganl, this, &myWidget::comCheckFB);  //断开连接
            timeId_ptr_->id5 = false;
            delete mc_com_check_;
            mc_com_check_ = nullptr;
            this->showNormal();
        }

        if (timeId_ptr_->id6 == true) {  // nova 校正

            QObject::disconnect(mc_nova_calib_, &CNovaCalibration::windowCloseSiganl, this, &myWidget::novaCalibFB);  //断开连接
            timeId_ptr_->id6 = false;
            delete mc_nova_calib_;
            mc_nova_calib_ = nullptr;
            this->showNormal();
        }


        // 2.0 系统时钟
        timeCnt100ms++;
        if (timeCnt100ms == 20) {
            timeCnt100ms = 0;
            // QDateTime current_time = QDateTime::currentDateTime();
            // QString timestr = current_time.toString( "yyyy-MM-dd hh:mm:ss")+"   | version: "+SOFTWARE_VERSION; //设置显示的格式
            // QString timestr = "version: "+SOFTWARE_VERSION; //设置显示的格式

            //            m_statusBar->showMessage(tr("版本："),1.0);//设置label的文本内容为时间
        }
    }
}

//* 1. tab切换
/**
 * @brief myWidget::on_Navigation_currentChanged
 * @param index
 */
void myWidget::on_Navigation_currentChanged(int index) {
    /*1.1 help doc clear*/
    if (myAssist_ptr_ != nullptr) {
        delete myAssist_ptr_;
        myAssist_ptr_ = nullptr;
    } else if (m_model_ != nullptr) {
        delete m_model_;
        m_model_ = nullptr;
    }
    switch (index + 1) {
    case 1:  //物料检测
             //        ui->metrialInspectT->show();

        break;
    case 2:
        break;
    case 3:

        break;
    case 5:  //
        //设置模型
        m_tableView_database_->table_name = ui->tableName->text();
        m_tableView_database_->conn       = MyConnSql::m_database_v.at(m_db_select).conn;
        m_tableView_database_->db         = QSqlDatabase::database(m_tableView_database_->conn);  //连接数据库

        m_model_ = new QSqlTableModel(this, m_tableView_database_->db);
        //指定使用哪个表
#if 0  //查询模型
        QSqlQueryModel *m_model_ = new QSqlQueryModel;
        m_model_->setQuery("select * from student");
        m_model_->setHeaderData(0, Qt::Horizontal, tr("id"));
        m_model_->setHeaderData(1, Qt::Horizontal, tr("name"));
#else  //表格模型
        m_model_->setTable(m_tableView_database_->table_name);
#endif
        //显示model里的数据
        m_model_->select();
        //设置model的编辑模式，手动提交修改
        m_model_->setEditStrategy(QSqlTableModel::OnManualSubmit);
        //把model放置到view里面
        ui->tableView->setModel(m_model_);
        break;
    case 6:
        break;
    case 7:
        if (myAssist_ptr_ == nullptr)
            myAssist_ptr_ = new Assistant;
        myAssist_ptr_->showDocumentation("index.html");
    default:
        break;
    }
}

//* 2. 功能触发
/**
 * @brief
 */
void myWidget::on_threadMotorMonitor_clicked()  //转速监控
{
    if (motor_monitor_ptr_ == nullptr) {
        motor_monitor_ptr_ = new motorMonitorWidget;
        motor_monitor_ptr_->show();

        this->showMinimized();
        QObject::connect(motor_monitor_ptr_, &motorMonitorWidget::motorMonitorCloseSiganl, this, &myWidget::motorMonitorFB, Qt::AutoConnection);
    }
}

void myWidget::motorMonitorFB(bool closed) {
    if (closed && motor_monitor_ptr_ != nullptr) {
        // timeId_ptr_->id1 = true;
        QObject::disconnect(motor_monitor_ptr_, &motorMonitorWidget::motorMonitorCloseSiganl, this, &myWidget::motorMonitorFB);  //断开连接

        delete motor_monitor_ptr_;
        motor_monitor_ptr_ = nullptr;
        this->showNormal();
    }
}

void myWidget::on_turnOnTimeP_clicked() {
    //    if(turn_on_time_ == nullptr)
    //    {
    //        turn_on_time_ = new turnOnTime;
    //        turn_on_time_->show();
    //        this->showMinimized();
    //        QObject::connect(turn_on_time_, &turnOnTime::turnOnTimeCloseSignal, this, &myWidget::turnOnTimeFB);
    //    }
}

void myWidget::turnOnTimeFB(bool closed) {
    //    if(closed && turn_on_time_ != nullptr)
    //    {
    //        timeId_ptr_->id4 = true;
    //    }
}


void myWidget::on_lightAdjustP_clicked() {
    if (lens_adjust_ptr_ == nullptr) {
        lens_adjust_ptr_ = new cLensAdjust;
        lens_adjust_ptr_->show();
        this->showMinimized();
        QObject::connect(lens_adjust_ptr_, &cLensAdjust::lensAdjustCloseSiganl, this, &myWidget::lensAdjustFB);
    }
}

void myWidget::lensAdjustFB(bool closed) {
    if (closed && lens_adjust_ptr_ != nullptr) {
        timeId_ptr_->id2 = true;
    }
}

void myWidget::on_comCheck_clicked() {
    if (mc_com_check_ == nullptr) {
        mc_com_check_ = new CComCheck;
        mc_com_check_->show();
        this->showMinimized();
        QObject::connect(mc_com_check_, &CComCheck::comCheckCloseSiganl, this, &myWidget::comCheckFB);
    }
}

void myWidget::comCheckFB(bool closed) {
    if (closed && mc_com_check_ != nullptr) {
        timeId_ptr_->id5 = true;
    }
}

void myWidget::on_novaCalibration_clicked() {
    if (mc_nova_calib_ == nullptr) {
        mc_nova_calib_ = new CNovaCalibration;
        mc_nova_calib_->show();
        this->showMinimized();
        QObject::connect(mc_nova_calib_, &CNovaCalibration::windowCloseSiganl, this, &myWidget::novaCalibFB);
    }
}

void myWidget::novaCalibFB(bool closed) {
    if (closed && mc_nova_calib_ != nullptr) {
        timeId_ptr_->id6 = true;
    }
}

//******************************************数据库显示*****************
/**
 * @brief myWidget::on_cellAdd_clicked
 */
void myWidget::on_cellAdd_clicked() {
    //添加空记录
    QSqlRecord record = m_model_->record();  //获取空记录

    //获取行号
    int row = m_model_->rowCount();
    //添加空行
    m_model_->insertRecord(row, record);
}

void myWidget::on_cellDelete_clicked() {
    //获取选中的模型
    QItemSelectionModel *sModel = ui->tableView->selectionModel();
    //取出模型中的索引
    QModelIndexList list = sModel->selectedRows();
    //删除所有选中的行
    for (int i = 0; i < list.size(); i++) {
        m_model_->removeRow(list.at(i).row());
    }
}

void myWidget::on_save_clicked() {
    //提交所有动作
    m_model_->submitAll();
}

void myWidget::on_cancle_clicked() {
    //取下所有动作
    m_model_->revertAll();
    //提交所有动作
    m_model_->submitAll();
}

void myWidget::on_output_clicked()  //导出excel文件
{
}

void myWidget::on_tableName_textChanged(const QString &arg1) {
    m_tableView_database_->table_name = arg1;
    m_model_->setTable(m_tableView_database_->table_name);
    //显示model里的数据
    m_model_->select();
    //设置model的编辑模式，手动提交修改
    m_model_->setEditStrategy(QSqlTableModel::OnManualSubmit);
    //把model放置到view里面
    ui->tableView->setModel(m_model_);
}

void myWidget::on_dateBaseSelect_currentIndexChanged(const QString &arg1) {
}

void myWidget::on_find_clicked() {
    //以name进行查找
    QString key = ui->lineEdit->text();
    QString str = QString("name = '%1'").arg(key);
    //过滤条件
    m_model_->setFilter(str);
    m_model_->select();
}

void myWidget::on_dateBaseSelect_currentIndexChanged(int index) {
    m_db_select                 = index;
    m_tableView_database_->conn = MyConnSql::m_database_v.at(index).conn;
    m_tableView_database_->db   = QSqlDatabase::database(m_tableView_database_->conn);
    if (m_model_ != nullptr) {
        delete m_model_;
        m_model_ = nullptr;
    }
    m_model_ = new QSqlTableModel(this, m_tableView_database_->db);
    m_model_->setTable(m_tableView_database_->table_name);
    //显示model里的数据
    m_model_->select();
    //设置model的编辑模式，手动提交修改
    m_model_->setEditStrategy(QSqlTableModel::OnManualSubmit);
    //把model放置到view里面
    ui->tableView->setModel(m_model_);
}
