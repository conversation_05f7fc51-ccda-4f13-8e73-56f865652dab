/*******************************
 * 底板设备交互
 *******************/

#include "bottomBoardSerial.h"
#include <QThread>

CBottomBoardSerial::CBottomBoardSerial(QObject *parent, IBottom *bottom_board_):
  QObject(parent)
, m_task_id(0)
, mi_bottom_board_(bottom_board_)
{

}

CBottomBoardSerial::~CBottomBoardSerial() {}

void CBottomBoardSerial::loop(int task_id)
{
    Q_UNUSED(task_id);
    QByteArray arr;
    QByteArray device_arr;
    for(;;) {
        if(m_task_id == 1) {
            arr = mi_bottom_board_->portDataRead();
            if(arr.length() > 0) mi_bottom_board_->interactionParsing(arr, 100);
        }
        else if(m_task_id == 4) {
            //QThread::msleep(1);
        }
        else if(m_task_id == 5) {//空
            QThread::msleep(5);
        }
        else if(m_task_id == 0) {//退出线程 //不能用else，时序上存在问题

            break; //return
        }
    }
}

void CBottomBoardSerial::device_change_interface(IBottom* top_board_)
{
  if(top_board_ != nullptr) {
      mi_bottom_board_ = top_board_;
      mi_bottom_board_->m_strPre.clear();
    }
}

void CBottomBoardSerial::task_id_change(const uint8_t &id)
{
    m_task_id = id;

    if(mi_bottom_board_->m_strPre.length() > 0) mi_bottom_board_->m_strPre.clear();

    qDebug() << "-i len thread/ task id:" << m_task_id;
}
