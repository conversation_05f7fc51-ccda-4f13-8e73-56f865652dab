#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一文档扫描器

整合文档级扫描和块级扫描功能，提供统一的扫描接口。
支持两种扫描模式：
1. 文档级扫描：用于Canvas可视化等文档级管理
2. 块级扫描：用于信息追溯等内容级管理
"""

import re
import hashlib
import json
import fnmatch
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from datetime import datetime
import sys

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir.parent
sys.path.insert(0, str(scripts_dir))

from common.config import get_component_dirs, get_all_components, infer_component
from common.gitignore_parser import DocIgnoreParser


class DocumentScanner:
    """文档级扫描器 - 用于Canvas可视化等场景"""
    
    def __init__(self, project_path: Path):
        """
        初始化文档扫描器
        
        Args:
            project_path: 项目根目录路径
        """
        self.project_path = Path(project_path)
        self.component_dirs = get_component_dirs()

        # 初始化git风格的规则解析器（从JSON配置中读取）
        self.gitignore_parser = None

        # 加载配置文件，如果不存在则使用默认值
        self._load_scanning_config()

    def _init_gitignore_parser(self, gitignore_rules):
        """初始化Git风格规则解析器"""
        from common.gitignore_parser import DocIgnoreParser

        # 创建临时解析器实例，传入规则列表而不是文件路径
        self.gitignore_parser = DocIgnoreParser.from_rules(gitignore_rules, self.project_path)
    
    def _load_scanning_config(self):
        """加载文档扫描配置"""
        config_file = self.project_path / 'config' / 'document_links_config.json'
        
        # 默认配置
        default_doc_extensions = {
            # 文本格式
            '.md', '.txt', '.rtf',
            # Microsoft Office格式
            '.doc', '.docx',           # Word文档
            '.xls', '.xlsx', '.xlsm',  # Excel表格
            '.ppt', '.pptx', '.pptm',  # PowerPoint演示文稿
            '.vsd', '.vsdx',           # Visio图表
            # PDF格式
            '.pdf',
            # 其他格式
            '.csv',                    # CSV数据文件
            '.xml', '.json'            # 结构化数据文件
        }
        
        default_exclude_patterns = {
            'INDEX.md', '_INDEX.md', 'README.md', 'GUIDE.md',
            '.git', '.vscode', '__pycache__', 'node_modules',
            'logs', 'temp', 'cache'
        }
        
        try:
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                scan_config = config.get('document_scanning', {})

                # 检查扫描模式
                scan_mode = scan_config.get('mode', 'traditional')

                if scan_mode == 'gitignore_style':
                    # 使用Git风格规则
                    gitignore_rules = scan_config.get('gitignore_rules', [])
                    if gitignore_rules:
                        self._init_gitignore_parser(gitignore_rules)
                        print(f"✓ 启用Git风格扫描规则，共 {len([r for r in gitignore_rules if r and not r.startswith('#')])} 条有效规则")

                        # Git风格模式下从否定规则推断文件类型
                        self.doc_extensions = self._extract_extensions_from_gitignore_rules()
                        if not self.doc_extensions:
                            # 如果没有否定规则，回退到传统配置
                            traditional_config = scan_config.get('traditional_config', {})
                            included_types = traditional_config.get('included_file_types', [])
                            self.doc_extensions = set(
                                ext for ext in included_types
                                if ext.startswith('.') and not ext.startswith('./')
                            )
                        if not self.doc_extensions:
                            self.doc_extensions = default_doc_extensions
                    else:
                        print("⚠ Git风格模式已启用，但未找到有效规则，回退到传统模式")
                        scan_mode = 'traditional'

                if scan_mode == 'traditional':
                    # 使用传统配置
                    traditional_config = scan_config.get('traditional_config', scan_config)

                    # 加载文件类型配置（过滤掉注释）
                    included_types = traditional_config.get('included_file_types', [])
                    self.doc_extensions = set(
                        ext for ext in included_types
                        if ext.startswith('.') and not ext.startswith('./')
                    )
                
                # 如果配置文件中没有有效的文件类型，使用默认值
                if not self.doc_extensions:
                    self.doc_extensions = default_doc_extensions
                
                # 加载排除目录配置（过滤掉注释）
                excluded_dirs = scan_config.get('excluded_directories', [])
                excluded_files = scan_config.get('excluded_files', [])
                
                self.exclude_patterns = set(
                    pattern for pattern in (excluded_dirs + excluded_files)
                    if not pattern.startswith(('系统', '文本', 'Microsoft', 'PDF', 'INDEX', '临时', '说明'))
                )
                
                # 如果配置文件中没有有效的排除模式，使用默认值
                if not self.exclude_patterns:
                    self.exclude_patterns = default_exclude_patterns
                
                # 存储组件特定配置
                self.component_specific = scan_config.get('component_specific', {})

                # 处理scan_only_subdirectories配置
                for comp_name, comp_config in self.component_specific.items():
                    if isinstance(comp_config, dict) and 'scan_only_subdirectories' in comp_config:
                        scan_only_config = comp_config['scan_only_subdirectories']
                        if scan_only_config.get('enabled', False):
                            print(f"组件 {comp_name} 启用了子目录白名单扫描模式: {scan_only_config.get('directories', [])}")
                
                print(f"✓ 已加载文档扫描配置: {config_file}")
                print(f"  - 支持文件类型: {len(self.doc_extensions)} 种")
                print(f"  - 排除模式: {len(self.exclude_patterns)} 个")
                
            else:
                # 使用默认配置
                self.doc_extensions = default_doc_extensions
                self.exclude_patterns = default_exclude_patterns
                self.component_specific = {}
                print(f"[!] 配置文件不存在，使用默认配置: {config_file}")
                
        except Exception as e:
            # 配置加载失败，使用默认值
            self.doc_extensions = default_doc_extensions
            self.exclude_patterns = default_exclude_patterns
            self.component_specific = {}
            print(f"[!] 配置文件加载失败，使用默认配置: {e}")
    
    def get_component_extensions(self, component: str) -> set:
        """获取指定组件的文件扩展名集合"""
        extensions = self.doc_extensions.copy()
        
        # 添加组件特定的文件类型
        comp_config = self.component_specific.get(component, {})
        additional_types = comp_config.get('additional_types', [])
        extensions.update(additional_types)
        
        return extensions
    
    def get_component_exclude_patterns(self, component: str) -> set:
        """获取指定组件的排除模式集合"""
        patterns = self.exclude_patterns.copy()
        
        # 添加组件特定的排除目录
        comp_config = self.component_specific.get(component, {})
        additional_excluded = comp_config.get('additional_excluded_dirs', [])
        patterns.update(additional_excluded)
        
        return patterns
    
    def scan_component_documents(self, component: str) -> List[Dict]:
        """
        扫描指定组件的所有文档

        Args:
            component: 组件名称

        Returns:
            List[Dict]: 文档信息列表
        """
        documents = []

        if component not in self.component_dirs:
            print(f"未知组件: {component}")
            return documents

        # 获取组件目录
        component_dir = self.component_dirs[component]
        component_path = self.project_path / component_dir

        if not component_path.exists():
            print(f"组件目录不存在: {component_path}")
            return documents

        print(f"扫描组件 {component} 目录: {component_path}")
        # 获取组件特定的配置
        component_extensions = self.get_component_extensions(component)
        component_excludes = self.get_component_exclude_patterns(component)

        # 在组件级别加载现有ID，整个扫描过程中维护这个列表
        existing_ids = self._load_existing_ids(component)

        # 检查是否启用了子目录白名单扫描模式
        comp_config = self.component_specific.get(component, {})
        scan_only_config = comp_config.get('scan_only_subdirectories', {})

        if scan_only_config.get('enabled', False):
            # 白名单模式：只扫描指定的子目录
            allowed_dirs = scan_only_config.get('directories', ['docs'])
            apply_depth = scan_only_config.get('apply_to_depth', 2)

            print(f"  启用白名单扫描模式，只扫描子目录: {allowed_dirs}")
            docs = self._scan_with_whitelist(component_path, component, component_extensions,
                                           component_excludes, allowed_dirs, apply_depth, existing_ids)
        else:
            # 常规扫描模式
            docs = self._scan_directory_recursive(component_path, component, component_extensions, component_excludes, existing_ids)

        documents.extend(docs)
        print(f"  找到 {len(docs)} 个文档")

        return documents
    
    def scan_all_components(self) -> Dict[str, List[Dict]]:
        """
        扫描所有组件的文档
        
        Returns:
            Dict[str, List[Dict]]: 按组件分组的文档列表
        """
        all_documents = {}
        
        for component in self.component_dirs.keys():
            print(f"\n=== 扫描组件 {component} ===")
            documents = self.scan_component_documents(component)
            if documents:
                all_documents[component] = documents
                
        return all_documents

    def _scan_with_whitelist(self, directory: Path, component: str,
                           extensions: Set[str], exclude_patterns: Set[str],
                           allowed_dirs: List[str], apply_depth: int, existing_ids: List[str]) -> List[Dict]:
        """
        使用白名单模式扫描目录

        Args:
            directory: 要扫描的根目录
            component: 所属组件名称
            extensions: 要扫描的文件扩展名集合
            exclude_patterns: 要排除的模式集合
            allowed_dirs: 允许扫描的子目录名称列表
            apply_depth: 应用白名单规则的深度级别

        Returns:
            List[Dict]: 文档信息列表
        """
        documents = []

        try:
            # 遍历根目录下的所有项目
            for item in directory.iterdir():
                if not item.is_dir():
                    continue

                # 检查是否是项目目录（如firmware_projects, software_projects等）
                if apply_depth >= 2:
                    # 进入项目类型目录（如firmware_projects）
                    for project_dir in item.iterdir():
                        if not project_dir.is_dir():
                            continue

                        print(f"    检查项目目录: {project_dir.name}")
                        # 在项目目录中只扫描允许的子目录
                        for allowed_dir in allowed_dirs:
                            target_dir = project_dir / allowed_dir
                            if target_dir.exists() and target_dir.is_dir():
                                print(f"      扫描白名单目录: {target_dir.relative_to(directory)}")
                                docs = self._scan_directory_recursive(target_dir, component, extensions, exclude_patterns, existing_ids)
                                documents.extend(docs)
                                # 更新existing_ids以避免后续重复
                                for doc in docs:
                                    if doc['doc_id'] not in existing_ids:
                                        existing_ids.append(doc['doc_id'])
                                print(f"        找到 {len(docs)} 个文档")
                else:
                    # 直接在当前级别应用白名单
                    if item.name in allowed_dirs:
                        print(f"    扫描白名单目录: {item.name}")
                        docs = self._scan_directory_recursive(item, component, extensions, exclude_patterns, existing_ids)
                        documents.extend(docs)
                        # 更新existing_ids以避免后续重复
                        for doc in docs:
                            if doc['doc_id'] not in existing_ids:
                                existing_ids.append(doc['doc_id'])
                        print(f"      找到 {len(docs)} 个文档")

        except Exception as e:
            print(f"白名单扫描失败 {directory}: {e}")

        return documents

    def _scan_directory_recursive(self, directory: Path, component: str,
                                 extensions: Optional[Set[str]] = None, exclude_patterns: Optional[Set[str]] = None,
                                 existing_ids: Optional[List[str]] = None) -> List[Dict]:
        """
        递归扫描目录中的文档
        
        Args:
            directory: 要扫描的目录
            component: 所属组件名称
            extensions: 要扫描的文件扩展名集合
            exclude_patterns: 要排除的模式集合
            
        Returns:
            List[Dict]: 文档信息列表
        """
        # 使用传入的配置或默认配置
        if extensions is None:
            extensions = self.doc_extensions
        if exclude_patterns is None:
            exclude_patterns = self.exclude_patterns
            
        documents = []
        # 使用传入的existing_ids，如果没有则加载
        if existing_ids is None:
            existing_ids = self._load_existing_ids(component)

        try:
            # 使用Git风格规则感知的递归扫描
            documents.extend(self._scan_directory_with_gitignore(
                directory, component, extensions, exclude_patterns, existing_ids
            ))
                    
        except Exception as e:
            print(f"扫描目录失败 {directory}: {e}")
            
        return documents

    def _scan_directory_with_gitignore(self, directory: Path, component: str,
                                     extensions: Set[str], exclude_patterns: Set[str],
                                     existing_ids: List[str]) -> List[Dict]:
        """
        使用Git风格规则感知的目录扫描
        在进入目录前检查Git风格规则，避免扫描被忽略的目录

        Args:
            directory: 要扫描的目录
            component: 所属组件名称
            extensions: 要扫描的文件扩展名集合
            exclude_patterns: 要排除的模式集合
            existing_ids: 已存在的文档ID列表

        Returns:
            List[Dict]: 文档信息列表
        """
        documents = []

        def scan_recursive(current_dir: Path):
            """递归扫描函数，支持Git风格规则"""
            try:
                # 检查当前目录是否应该被忽略（但要考虑否定规则）
                should_skip_directory = False
                if self.gitignore_parser:
                    # 计算相对路径
                    try:
                        rel_path = current_dir.relative_to(self.project_path)

                        # 检查目录本身是否被忽略
                        if self.gitignore_parser.should_ignore(rel_path):
                            should_skip_directory = True

                        # 检查带/的目录路径是否被忽略
                        dir_with_slash = Path(str(rel_path) + '/')
                        if self.gitignore_parser.should_ignore(dir_with_slash):
                            should_skip_directory = True

                    except ValueError:
                        # 如果不是相对路径，继续处理
                        pass

                # 如果目录被忽略，检查是否有否定规则可能包含其中的文件
                if should_skip_directory:
                    # 检查是否有否定规则可能匹配此目录下的文件
                    if not self._has_negation_rules_for_directory(current_dir):
                        return

                # 扫描当前目录中的文件
                for item in current_dir.iterdir():
                    if item.is_file():
                        # 检查文件扩展名
                        if item.suffix.lower() not in extensions:
                            continue

                        # 检查是否应该排除
                        if self._should_exclude(item, exclude_patterns):
                            continue

                        # 获取文档信息
                        doc_info = self._extract_document_info(item, component, existing_ids)
                        if doc_info:
                            documents.append(doc_info)
                            # 将新生成的ID添加到现有ID列表中，避免后续重复
                            existing_ids.append(doc_info['doc_id'])

                    elif item.is_dir():
                        # 递归扫描子目录
                        scan_recursive(item)

            except PermissionError:
                print(f"权限不足，跳过目录: {current_dir}")
            except Exception as e:
                print(f"扫描目录失败 {current_dir}: {e}")

        # 开始递归扫描
        scan_recursive(directory)
        return documents

    def _has_negation_rules_for_directory(self, directory: Path) -> bool:
        """
        检查是否有否定规则可能匹配此目录下的文件

        Args:
            directory: 要检查的目录

        Returns:
            bool: 如果有否定规则可能匹配，返回True
        """
        if not self.gitignore_parser:
            return False

        try:
            rel_path = directory.relative_to(self.project_path)
            dir_path_str = str(rel_path).replace('\\', '/')

            # 检查所有否定规则
            for rule in self.gitignore_parser.rules:
                if rule.get('is_negation', False):
                    pattern = rule.get('pattern', '')

                    # 检查否定规则是否可能匹配此目录下的文件
                    # 例如：!development/**/*.md 应该匹配 development/子目录/文件.md
                    if pattern.startswith(dir_path_str + '/') or pattern.startswith(dir_path_str + '/**'):
                        return True

                    # 检查通配符模式
                    if '**' in pattern and dir_path_str in pattern:
                        return True

            return False

        except ValueError:
            return False

    def _extract_extensions_from_gitignore_rules(self) -> Set[str]:
        """
        从Git风格规则中提取文件扩展名
        只包含被否定规则明确包含的文件类型

        Returns:
            Set[str]: 从否定规则中提取的文件扩展名集合
        """
        extensions = set()

        if not self.gitignore_parser:
            return extensions

        # 遍历所有否定规则
        for rule in self.gitignore_parser.rules:
            if rule.get('is_negation', False):
                pattern = rule.get('original', rule.get('pattern', ''))

                # 提取文件扩展名模式
                # 例如：development/**/*.md -> .md
                if '*.' in pattern:
                    # 找到 *. 后面的扩展名
                    parts = pattern.split('*.')
                    if len(parts) > 1:
                        ext_part = parts[-1]  # 取最后一个部分

                        # 处理可能的通配符或其他字符
                        # 只取字母数字字符作为扩展名
                        ext = ''
                        for char in ext_part:
                            if char.isalnum():
                                ext += char
                            else:
                                break

                        if ext:
                            extensions.add('.' + ext)

        print(f"✓ 从Git风格否定规则中提取的文件类型: {sorted(extensions)}")
        return extensions

    def _load_existing_ids(self, component: str) -> List[str]:
        """从现有INDEX文件中加载已有的文档ID"""
        existing_ids = []

        # 查找对应的INDEX文件
        index_file_name = f"{component}_INDEX.md"

        # 在项目目录下搜索INDEX文件
        for index_file in self.project_path.rglob(index_file_name):
            try:
                content = index_file.read_text(encoding='utf-8')
                # 解析表格中的文档ID
                ids = self._parse_ids_from_index_content(content)
                existing_ids.extend(ids)
            except Exception as e:
                print(f"读取INDEX文件失败 {index_file}: {e}")

        return existing_ids

    def _parse_ids_from_index_content(self, content: str) -> List[str]:
        """从INDEX文件内容中解析文档ID"""
        ids = []
        lines = content.split('\n')
        in_table = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是表格行
            if '|' in line:
                columns = [col.strip() for col in line.split('|')]
                if len(columns) >= 4:  # 至少需要4列
                    # 检查是否是表头
                    if columns[0] == '文档ID' and columns[1] == '文档名称':
                        in_table = True
                        continue

                    # 检查是否是分隔行
                    if all(col.replace('-', '').strip() == '' for col in columns if col):
                        continue

                    # 如果在表格中，提取文档ID
                    if in_table and columns[0] and not columns[0].startswith('文档ID'):
                        doc_id = columns[0].strip()
                        if doc_id and doc_id not in ids:
                            ids.append(doc_id)
            else:
                # 如果不是表格行，重置表格状态
                in_table = False

        return ids

    def _should_exclude(self, file_path: Path, exclude_patterns: Optional[Set[str]] = None) -> bool:
        """检查文件是否应该被排除"""
        # 优先使用Git风格规则（从JSON配置中读取）
        if self.gitignore_parser:
            return self.gitignore_parser.should_ignore(file_path)

        # 回退到传统的配置方式
        if exclude_patterns is None:
            exclude_patterns = self.exclude_patterns

        file_name = file_path.name
        
        # 检查精确匹配和通配符模式
        for pattern in exclude_patterns:
            # 跳过注释行
            if pattern.startswith('#') or pattern.startswith('系统') or pattern.startswith('临时'):
                continue
                
            # 精确匹配文件名
            if file_name == pattern:
                return True
            
            # 通配符模式匹配文件名  
            if '*' in pattern or '?' in pattern:
                if fnmatch.fnmatch(file_name, pattern):
                    return True
        
        # 检查路径中的目录名
        for part in file_path.parts:
            if part in exclude_patterns:
                return True
                
        # 检查是否包含INDEX
        if 'INDEX' in file_path.name.upper():
            return True
        
        # 排除隐藏文件（以.开头但不在配置中明确允许的）
        if file_name.startswith('.') and file_name not in exclude_patterns:
            return True
            
        return False
    
    def _extract_document_info(self, file_path: Path, component: str, existing_ids: Optional[List[str]] = None) -> Optional[Dict]:
        """提取文档信息"""
        try:
            # 计算相对路径
            rel_path = file_path.relative_to(self.project_path)

            # 生成文档ID
            if existing_ids is None:
                existing_ids = []
            doc_id = self._generate_doc_id(component, file_path, existing_ids)

            # 确定文档类型
            doc_type = self._infer_doc_type(file_path)

            # 提取文档名称
            doc_name = self._extract_doc_title(file_path)

            return {
                'doc_id': doc_id,
                'doc_name': doc_name,
                'doc_path': str(rel_path),
                'doc_type': doc_type,
                'file_size': file_path.stat().st_size,
                'modified_time': datetime.fromtimestamp(file_path.stat().st_mtime)
            }

        except Exception as e:
            print(f"提取文档信息失败 {file_path}: {e}")
            return None
    
    def _generate_doc_id(self, component: str, file_path: Path, existing_ids: Optional[List[str]] = None) -> str:
        """生成文档ID"""
        if existing_ids is None:
            existing_ids = []

        # 首先尝试使用序号方式生成ID
        from common.document_id_generator import DocumentIdGenerator
        sequential_id = DocumentIdGenerator.generate_new_id(component, existing_ids)

        # 检查是否与现有ID冲突
        if sequential_id not in existing_ids:
            return sequential_id

        # 如果序号方式有冲突，使用路径哈希方式
        return DocumentIdGenerator.generate_hash_based_id(component, file_path)
    
    def _infer_doc_type(self, file_path: Path) -> str:
        """推断文档类型"""
        name_lower = file_path.name.lower()
        
        # 基于文件名内容推断
        if 'spec' in name_lower or 'specification' in name_lower:
            return '规格说明'
        elif 'requirement' in name_lower or 'req' in name_lower:
            return '需求文档'
        elif 'design' in name_lower or 'des' in name_lower:
            return '设计文档'
        elif 'test' in name_lower or 'qa' in name_lower:
            return '测试文档'
        elif 'manual' in name_lower or 'guide' in name_lower:
            return '操作手册'
        elif 'plan' in name_lower or 'schedule' in name_lower:
            return '计划文档'
        elif 'report' in name_lower:
            return '报告文档'
        elif 'competitor' in name_lower or '竞品' in name_lower:
            return '竞品分析'
        elif 'introduction' in name_lower or 'intro' in name_lower or '介绍' in name_lower:
            return '产品介绍'
        else:
            # 基于文件扩展名
            ext = file_path.suffix.lower()
            type_map = {
                # 文本格式
                '.md': 'Markdown文档',
                '.txt': '文本文档',
                '.rtf': 'RTF文档',
                # Office格式
                '.doc': 'Word文档',
                '.docx': 'Word文档',
                '.xls': 'Excel文档',
                '.xlsx': 'Excel文档',
                '.xlsm': 'Excel宏文档',
                '.ppt': 'PowerPoint文档',
                '.pptx': 'PowerPoint文档',
                '.pptm': 'PowerPoint宏文档',
                '.vsd': 'Visio图表',
                '.vsdx': 'Visio图表',
                # 其他格式
                '.pdf': 'PDF文档',
                '.csv': 'CSV数据文件',
                '.xml': 'XML文档',
                '.json': 'JSON文档'
            }
            return type_map.get(ext, '其他文档')
    
    def _extract_doc_title(self, file_path: Path) -> str:
        """提取文档标题"""
        # 直接返回文件名（不含扩展名），确保与实际文件名一致
        return file_path.stem


class IndexScanner:
    """INDEX文件扫描器 - 解析现有INDEX文件内容"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
        self.component_dirs = get_component_dirs()
    
    def parse_index_file(self, index_path: Path) -> List[Dict]:
        """
        解析INDEX文件，提取文档信息
        
        Args:
            index_path: INDEX文件路径
            
        Returns:
            List[Dict]: 文档信息列表
        """
        documents = []
        
        if not index_path.exists():
            print(f"INDEX文件不存在: {index_path}")
            return documents
            
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 分行处理，更精确地解析表格
            lines = content.split('\n')
            in_table = False
            header_found = False
            
            for line in lines:
                line = line.strip()
                
                # 检查是否是表格行
                if line.startswith('|') and line.endswith('|'):
                    # 分割表格列
                    columns = [col.strip() for col in line.split('|')[1:-1]]
                    
                    if len(columns) >= 4:  # 至少需要4列
                        # 检查是否是表头
                        if columns[0] == '文档ID' and columns[1] == '文档名称':
                            header_found = True
                            in_table = True
                            continue
                        
                        # 检查是否是分隔符行
                        if all(col == '' or col.startswith('-') for col in columns):
                            continue
                        
                        # 检查是否在表格中且有有效的文档ID
                        if in_table and columns[0] and not columns[0].startswith('-'):
                            doc_id = columns[0]
                            doc_name = columns[1] if len(columns) > 1 else ''
                            doc_path = columns[2] if len(columns) > 2 else ''
                            doc_type = columns[3] if len(columns) > 3 else ''
                    
                            # 跳过空行或无效行
                            if doc_id and doc_name and doc_path:
                                documents.append({
                                    'doc_id': doc_id,
                                    'doc_name': doc_name,
                                    'doc_path': doc_path,
                                    'doc_type': doc_type,
                                    'index_file': str(index_path)
                                })
                else:
                    # 非表格行，重置表格状态
                    if in_table:
                        in_table = False
                        
        except Exception as e:
            print(f"解析INDEX文件失败 {index_path}: {e}")
            
        return documents
    
    def find_index_files(self) -> Dict[str, Path]:
        """
        查找项目中的所有INDEX文件
        
        Returns:
            Dict[str, Path]: 组件名到INDEX文件路径的映射
        """
        index_files = {}
        
        for component, dir_name in self.component_dirs.items():
            index_file = self.project_path / dir_name / f"{component}_INDEX.md"
            if index_file.exists():
                index_files[component] = index_file
                
        return index_files 