2025-07-03 09:47:22,976 - __main__ - INFO - PYTHONPATH: Not set
2025-07-03 09:47:22,997 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 09:47:22,998 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 09:47:24,964 - __main__ - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 09:47:24,965 - __main__ - DEBUG - Suppressed output: 
2025-07-03 09:47:27,598 - __main__ - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 09:47:27,599 - __main__ - DEBUG - Suppressed output: 
2025-07-03 09:47:27,603 - __main__ - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 09:47:27,603 - __main__ - DEBUG - Suppressed output: 
2025-07-03 09:47:27,645 - __main__ - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 09:47:27,646 - __main__ - DEBUG - Suppressed output: 
2025-07-03 09:47:27,945 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 09:47:27,955 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 09:47:27,959 - matplotlib - DEBUG - interactive is False
2025-07-03 09:47:27,960 - matplotlib - DEBUG - platform is win32
2025-07-03 09:47:28,123 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 09:47:28,136 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 09:47:28,736 - __main__ - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 09:47:28,737 - __main__ - DEBUG - Suppressed output: 
2025-07-03 11:08:34,297 - mcp_server - INFO - PYTHONPATH: Not set
2025-07-03 11:08:34,298 - mcp_server - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 11:08:34,298 - mcp_server - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 11:08:35,590 - mcp_server - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 11:08:35,590 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:08:37,014 - mcp_server - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 11:08:37,014 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:08:37,017 - mcp_server - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:08:37,017 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:08:37,054 - mcp_server - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 11:08:37,054 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:08:37,254 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 11:08:37,259 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:08:37,263 - matplotlib - DEBUG - interactive is False
2025-07-03 11:08:37,264 - matplotlib - DEBUG - platform is win32
2025-07-03 11:08:37,349 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:08:37,352 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 11:08:37,603 - mcp_server - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 11:08:37,603 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:14:05,182 - mcp_server - INFO - PYTHONPATH: Not set
2025-07-03 11:14:05,183 - mcp_server - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 11:14:05,184 - mcp_server - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 11:14:06,229 - mcp_server - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 11:14:06,234 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:14:07,439 - mcp_server - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 11:14:07,439 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:14:07,441 - mcp_server - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:14:07,441 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:14:07,468 - mcp_server - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 11:14:07,468 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:14:07,663 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 11:14:07,669 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:14:07,670 - matplotlib - DEBUG - interactive is False
2025-07-03 11:14:07,671 - matplotlib - DEBUG - platform is win32
2025-07-03 11:14:07,830 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:14:07,837 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 11:14:08,176 - mcp_server - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 11:14:08,176 - mcp_server - DEBUG - Suppressed output: 
2025-07-03 11:24:43,301 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:24:43,301 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 11:24:43,301 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 11:24:44,209 - server_silent - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 11:24:44,209 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,180 - server_silent - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 11:24:45,180 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,182 - server_silent - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:24:45,182 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,212 - server_silent - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 11:24:45,212 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,402 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 11:24:45,409 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:24:45,412 - matplotlib - DEBUG - interactive is False
2025-07-03 11:24:45,412 - matplotlib - DEBUG - platform is win32
2025-07-03 11:24:45,564 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:24:45,566 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 11:24:45,847 - server_silent - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 11:24:45,847 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,848 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 11:24:45,905 - server_silent - DEBUG - Suppressed output: 🚀 调用 init_project_tool
2025-07-03 11:24:45,906 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,908 - server_silent - DEBUG - Suppressed output: 项目名称: simple_mcp_test_project
2025-07-03 11:24:45,908 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,908 - server_silent - DEBUG - Suppressed output: 项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project
2025-07-03 11:24:45,909 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,909 - server_silent - INFO - 🚀 Starting init_project_tool: simple_mcp_test_project
2025-07-03 11:24:45,909 - server_silent - INFO -    Parameters: path=F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project, type=single_layer
2025-07-03 11:24:45,910 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project
2025-07-03 11:24:45,910 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 11:24:45,910 - server_silent - DEBUG - Suppressed output: 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project
2025-07-03 11:24:45,911 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,916 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\product_info
2025-07-03 11:24:45,916 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,917 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\requirements
2025-07-03 11:24:45,918 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,918 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\design
2025-07-03 11:24:45,919 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,921 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\development
2025-07-03 11:24:45,924 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,926 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\quality
2025-07-03 11:24:45,928 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,930 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\production
2025-07-03 11:24:45,931 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,932 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\deliverables
2025-07-03 11:24:45,933 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,934 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\project_management
2025-07-03 11:24:45,935 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,939 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\scripts
2025-07-03 11:24:45,941 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,943 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\config
2025-07-03 11:24:45,943 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,945 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\reports
2025-07-03 11:24:45,945 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,947 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\product_info\competitor_products/competitor_A
2025-07-03 11:24:45,948 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,950 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\product_info\competitor_products/competitor_B
2025-07-03 11:24:45,951 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,952 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\requirements\main_requirements
2025-07-03 11:24:45,953 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,955 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\requirements\custom_requirements
2025-07-03 11:24:45,956 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,959 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\requirements\market_requirements
2025-07-03 11:24:45,959 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,961 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\design\project_solution
2025-07-03 11:24:45,962 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,963 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\design\specification_document
2025-07-03 11:24:45,963 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,964 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\design\interface_specifications
2025-07-03 11:24:45,965 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,966 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\design\principle_Information
2025-07-03 11:24:45,967 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,968 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\quality\test_plans
2025-07-03 11:24:45,968 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,969 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\quality\test_cases
2025-07-03 11:24:45,971 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,974 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\quality\test_reports/internal_tests
2025-07-03 11:24:45,975 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,976 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\quality\test_reports/competitive_tests
2025-07-03 11:24:45,977 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,978 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\quality\issues
2025-07-03 11:24:45,978 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,979 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\production\bom
2025-07-03 11:24:45,980 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,981 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\production\manufacturing_process
2025-07-03 11:24:45,982 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,983 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\production\quality_control
2025-07-03 11:24:45,983 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,984 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\deliverables\documents
2025-07-03 11:24:45,984 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,986 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\deliverables\firmware
2025-07-03 11:24:45,987 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,991 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\deliverables\hardware
2025-07-03 11:24:45,992 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,993 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\deliverables\software
2025-07-03 11:24:45,994 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,997 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\deliverables\tools
2025-07-03 11:24:45,998 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:45,999 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\project_management\schedules
2025-07-03 11:24:45,999 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,000 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\project_management\resources
2025-07-03 11:24:46,001 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,002 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\project_management\risks
2025-07-03 11:24:46,005 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,007 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\project_management\meeting_notes
2025-07-03 11:24:46,008 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,012 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\README.md
2025-07-03 11:24:46,012 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,014 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project\product.canvas
2025-07-03 11:24:46,015 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,015 - server_silent - DEBUG - Suppressed output: 🔍 开始配置生成过程...
2025-07-03 11:24:46,016 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,017 - server_silent - DEBUG - Suppressed output: 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 11:24:46,018 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:46,018 - server_silent - DEBUG - Suppressed output: 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:24:46,018 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,467 - server_silent - DEBUG - Suppressed output: ✅ 配置文件生成成功
2025-07-03 11:24:49,467 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,467 - server_silent - DEBUG - Suppressed output: 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统
2025-07-03 11:24:49,469 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,470 - server_silent - DEBUG - Suppressed output: ✅ 生成了 10 个配置文件:
2025-07-03 11:24:49,470 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,471 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:24:49,471 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,471 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:24:49,471 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,471 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:24:49,471 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,472 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:24:49,472 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,473 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:24:49,473 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,473 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:24:49,473 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,473 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:24:49,473 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,474 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:24:49,474 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,474 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:24:49,474 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,474 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:24:49,474 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,475 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 11:24:49,475 - server_silent - DEBUG - Suppressed output: 📊 工具调用结果:
2025-07-03 11:24:49,475 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,475 - server_silent - DEBUG - Suppressed output: 成功: True
2025-07-03 11:24:49,476 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,476 - server_silent - DEBUG - Suppressed output: 🔍 验证项目创建结果:
2025-07-03 11:24:49,476 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,477 - server_silent - DEBUG - Suppressed output: ✅ 项目目录存在: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\simple_mcp_test_project
2025-07-03 11:24:49,477 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,478 - server_silent - DEBUG - Suppressed output: ✅ README.md文件存在
2025-07-03 11:24:49,478 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,479 - server_silent - DEBUG - Suppressed output: ✅ config目录存在，包含 10 个配置文件
2025-07-03 11:24:49,479 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,479 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:24:49,480 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,480 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:24:49,480 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,480 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:24:49,480 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,481 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:24:49,481 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,481 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:24:49,482 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,484 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:24:49,484 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,485 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:24:49,485 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,488 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:24:49,492 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,495 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:24:49,496 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,497 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:24:49,498 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,499 - server_silent - DEBUG - Suppressed output: ✅ 关键配置文件存在: document_links_config.json
2025-07-03 11:24:49,500 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,504 - server_silent - DEBUG - Suppressed output: ✅ 配置文件包含document_scanning配置
2025-07-03 11:24:49,512 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:24:49,517 - server_silent - DEBUG - Suppressed output: 🎯 测试结果: ✅ 成功
2025-07-03 11:24:49,527 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:07,238 - server_silent - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:29:07,238 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:29:07,238 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 11:29:07,239 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 11:29:08,120 - server_silent - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 11:29:08,120 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:09,342 - server_silent - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 11:29:09,342 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:09,346 - server_silent - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:29:09,346 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:09,377 - server_silent - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 11:29:09,378 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:09,615 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 11:29:09,621 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:29:09,622 - matplotlib - DEBUG - interactive is False
2025-07-03 11:29:09,623 - matplotlib - DEBUG - platform is win32
2025-07-03 11:29:09,724 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:29:09,730 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 11:29:09,979 - server_silent - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 11:29:09,979 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:09,980 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 11:29:10,039 - server_silent - DEBUG - Suppressed output: 🚀 调用修复后的 init_project_tool
2025-07-03 11:29:10,039 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,039 - server_silent - DEBUG - Suppressed output: 项目名称: fixed_mcp_test_project
2025-07-03 11:29:10,039 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,040 - server_silent - DEBUG - Suppressed output: 项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project
2025-07-03 11:29:10,040 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,040 - server_silent - INFO - 🚀 Starting init_project_tool: fixed_mcp_test_project
2025-07-03 11:29:10,040 - server_silent - INFO -    Parameters: path=F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project, type=single_layer
2025-07-03 11:29:10,041 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project
2025-07-03 11:29:10,041 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 11:29:10,041 - server_silent - DEBUG - Suppressed output: 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project
2025-07-03 11:29:10,042 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,049 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\product_info
2025-07-03 11:29:10,050 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,051 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\requirements
2025-07-03 11:29:10,051 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,052 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\design
2025-07-03 11:29:10,053 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,054 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\development
2025-07-03 11:29:10,054 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,055 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\quality
2025-07-03 11:29:10,055 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,056 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\production
2025-07-03 11:29:10,056 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,057 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\deliverables
2025-07-03 11:29:10,057 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,059 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\project_management
2025-07-03 11:29:10,060 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,062 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\scripts
2025-07-03 11:29:10,064 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,066 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\config
2025-07-03 11:29:10,067 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,068 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\reports
2025-07-03 11:29:10,069 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,071 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\product_info\competitor_products/competitor_A
2025-07-03 11:29:10,072 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,074 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\product_info\competitor_products/competitor_B
2025-07-03 11:29:10,076 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,077 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\requirements\main_requirements
2025-07-03 11:29:10,078 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,080 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\requirements\custom_requirements
2025-07-03 11:29:10,081 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,082 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\requirements\market_requirements
2025-07-03 11:29:10,083 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,084 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\design\project_solution
2025-07-03 11:29:10,084 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,085 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\design\specification_document
2025-07-03 11:29:10,085 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,086 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\design\interface_specifications
2025-07-03 11:29:10,087 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,088 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\design\principle_Information
2025-07-03 11:29:10,089 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,091 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\quality\test_plans
2025-07-03 11:29:10,091 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,092 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\quality\test_cases
2025-07-03 11:29:10,093 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,094 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\quality\test_reports/internal_tests
2025-07-03 11:29:10,096 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,097 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\quality\test_reports/competitive_tests
2025-07-03 11:29:10,098 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,099 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\quality\issues
2025-07-03 11:29:10,099 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,100 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\production\bom
2025-07-03 11:29:10,100 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,101 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\production\manufacturing_process
2025-07-03 11:29:10,102 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,103 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\production\quality_control
2025-07-03 11:29:10,104 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,105 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\deliverables\documents
2025-07-03 11:29:10,105 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,106 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\deliverables\firmware
2025-07-03 11:29:10,107 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,108 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\deliverables\hardware
2025-07-03 11:29:10,108 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,110 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\deliverables\software
2025-07-03 11:29:10,111 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,112 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\deliverables\tools
2025-07-03 11:29:10,113 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,114 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\project_management\schedules
2025-07-03 11:29:10,115 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,116 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\project_management\resources
2025-07-03 11:29:10,116 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,117 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\project_management\risks
2025-07-03 11:29:10,118 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,119 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\project_management\meeting_notes
2025-07-03 11:29:10,119 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,122 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\README.md
2025-07-03 11:29:10,122 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,124 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project\product.canvas
2025-07-03 11:29:10,125 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,126 - server_silent - DEBUG - Suppressed output: 🔍 开始配置生成过程...
2025-07-03 11:29:10,127 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,130 - server_silent - DEBUG - Suppressed output: 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 11:29:10,130 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:10,130 - server_silent - DEBUG - Suppressed output: 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:29:10,131 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,445 - server_silent - DEBUG - Suppressed output: ✅ 配置文件生成成功
2025-07-03 11:29:13,446 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,446 - server_silent - DEBUG - Suppressed output: 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\fixed_mcp_test_project
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统
2025-07-03 11:29:13,449 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,450 - server_silent - DEBUG - Suppressed output: ✅ 生成了 10 个配置文件:
2025-07-03 11:29:13,450 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,450 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:29:13,451 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,451 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:29:13,451 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,451 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:29:13,451 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,451 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:29:13,452 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,452 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:29:13,452 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,452 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:29:13,452 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,453 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:29:13,453 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,453 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:29:13,453 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,453 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:29:13,454 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,454 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:29:13,454 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,454 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 11:29:13,454 - server_silent - DEBUG - Suppressed output: 📊 工具调用结果:
2025-07-03 11:29:13,454 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,454 - server_silent - DEBUG - Suppressed output: 成功: True
2025-07-03 11:29:13,455 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,455 - server_silent - DEBUG - Suppressed output: 🔍 验证项目创建结果:
2025-07-03 11:29:13,455 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,455 - server_silent - DEBUG - Suppressed output: ✅ 项目目录存在
2025-07-03 11:29:13,455 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,456 - server_silent - DEBUG - Suppressed output: ✅ config目录存在，包含 10 个配置文件
2025-07-03 11:29:13,456 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,456 - server_silent - DEBUG - Suppressed output: ✅ 配置文件数量充足 (≥10)
2025-07-03 11:29:13,456 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,457 - server_silent - DEBUG - Suppressed output: ✅ 关键配置文件存在: document_links_config.json
2025-07-03 11:29:13,459 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,463 - server_silent - DEBUG - Suppressed output: ✅ 配置文件包含document_scanning配置
2025-07-03 11:29:13,464 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,471 - server_silent - DEBUG - Suppressed output: 🎯 修复验证结果: ✅ 成功
2025-07-03 11:29:13,474 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,475 - server_silent - DEBUG - Suppressed output: - PYTHONPATH自动设置功能正常
2025-07-03 11:29:13,477 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,479 - server_silent - DEBUG - Suppressed output: - 配置文件生成功能正常
2025-07-03 11:29:13,481 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,482 - server_silent - DEBUG - Suppressed output: - MCP服务器调用功能正常
2025-07-03 11:29:13,484 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,484 - server_silent - DEBUG - Suppressed output: 🎉 修复成功！
2025-07-03 11:29:13,490 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,491 - server_silent - DEBUG - Suppressed output: 现在MCP服务器应该能在AI对话框中正常工作了
2025-07-03 11:29:13,497 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:29:13,500 - server_silent - DEBUG - Suppressed output: 用户可以重新测试'初始化项目'功能
2025-07-03 11:29:13,506 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:37,505 - server_silent - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:47:37,505 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:47:37,505 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 11:47:37,505 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 11:47:38,364 - server_silent - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 11:47:38,364 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,205 - server_silent - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 11:47:39,206 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,209 - server_silent - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:47:39,209 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,238 - server_silent - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 11:47:39,238 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,421 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 11:47:39,426 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:47:39,427 - matplotlib - DEBUG - interactive is False
2025-07-03 11:47:39,428 - matplotlib - DEBUG - platform is win32
2025-07-03 11:47:39,530 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:47:39,536 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 11:47:39,892 - server_silent - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 11:47:39,892 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,892 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 11:47:39,968 - server_silent - DEBUG - Suppressed output: [pythonpath_setting_test] ✅ 成功
2025-07-03 11:47:39,970 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,971 - server_silent - DEBUG - Suppressed output: 📋 开始测试: 路径解析功能
2025-07-03 11:47:39,972 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,972 - server_silent - DEBUG - Suppressed output: 🧪 测试路径解析功能
2025-07-03 11:47:39,972 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,973 - server_silent - DEBUG - Suppressed output: ============================================================
2025-07-03 11:47:39,973 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,973 - server_silent - INFO - 🚀 Starting init_project_tool: path_test_project
2025-07-03 11:47:39,973 - server_silent - INFO -    Parameters: path=example/path_test_project, type=single_layer
2025-07-03 11:47:39,974 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project
2025-07-03 11:47:39,974 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 11:47:39,975 - server_silent - DEBUG - Suppressed output: 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project
2025-07-03 11:47:39,975 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,982 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\product_info
2025-07-03 11:47:39,983 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,985 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\requirements
2025-07-03 11:47:39,987 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,989 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\design
2025-07-03 11:47:39,990 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,991 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\development
2025-07-03 11:47:39,992 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,993 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\quality
2025-07-03 11:47:39,994 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,995 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\production
2025-07-03 11:47:39,995 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,996 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\deliverables
2025-07-03 11:47:39,997 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:39,998 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\project_management
2025-07-03 11:47:40,000 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,003 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\scripts
2025-07-03 11:47:40,005 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,006 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\config
2025-07-03 11:47:40,007 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,008 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\reports
2025-07-03 11:47:40,008 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,009 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\product_info\competitor_products/competitor_A
2025-07-03 11:47:40,010 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,011 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\product_info\competitor_products/competitor_B
2025-07-03 11:47:40,012 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,013 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\requirements\main_requirements
2025-07-03 11:47:40,014 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,016 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\requirements\custom_requirements
2025-07-03 11:47:40,017 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,021 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\requirements\market_requirements
2025-07-03 11:47:40,022 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,023 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\design\project_solution
2025-07-03 11:47:40,025 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,027 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\design\specification_document
2025-07-03 11:47:40,028 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,029 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\design\interface_specifications
2025-07-03 11:47:40,029 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,030 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\design\principle_Information
2025-07-03 11:47:40,031 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,032 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\quality\test_plans
2025-07-03 11:47:40,033 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,036 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\quality\test_cases
2025-07-03 11:47:40,037 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,040 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\quality\test_reports/internal_tests
2025-07-03 11:47:40,043 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,044 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\quality\test_reports/competitive_tests
2025-07-03 11:47:40,045 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,046 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\quality\issues
2025-07-03 11:47:40,047 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,050 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\production\bom
2025-07-03 11:47:40,053 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,056 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\production\manufacturing_process
2025-07-03 11:47:40,057 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,059 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\production\quality_control
2025-07-03 11:47:40,061 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,062 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\deliverables\documents
2025-07-03 11:47:40,063 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,064 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\deliverables\firmware
2025-07-03 11:47:40,066 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,070 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\deliverables\hardware
2025-07-03 11:47:40,071 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,073 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\deliverables\software
2025-07-03 11:47:40,073 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,075 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\deliverables\tools
2025-07-03 11:47:40,075 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,077 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\project_management\schedules
2025-07-03 11:47:40,077 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,079 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\project_management\resources
2025-07-03 11:47:40,080 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,083 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\project_management\risks
2025-07-03 11:47:40,083 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,088 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\project_management\meeting_notes
2025-07-03 11:47:40,089 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,092 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\README.md
2025-07-03 11:47:40,094 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,095 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project\product.canvas
2025-07-03 11:47:40,097 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,100 - server_silent - DEBUG - Suppressed output: 🔍 开始配置生成过程...
2025-07-03 11:47:40,104 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,107 - server_silent - DEBUG - Suppressed output: 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 11:47:40,108 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:40,111 - server_silent - DEBUG - Suppressed output: 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:47:40,111 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,773 - server_silent - DEBUG - Suppressed output: ✅ 配置文件生成成功
2025-07-03 11:47:43,773 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,773 - server_silent - DEBUG - Suppressed output: 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统
2025-07-03 11:47:43,775 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,776 - server_silent - DEBUG - Suppressed output: ✅ 生成了 10 个配置文件:
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:47:43,777 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,778 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:47:43,779 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,779 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:47:43,779 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,779 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:47:43,779 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,779 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:47:43,780 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,780 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 11:47:43,780 - server_silent - DEBUG - Suppressed output: [path_resolution_test] ✅ 成功
2025-07-03 11:47:43,780 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,780 - server_silent - DEBUG - Suppressed output: 📋 开始测试: 项目初始化
2025-07-03 11:47:43,780 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,781 - server_silent - DEBUG - Suppressed output: 🧪 测试 init_project_tool
2025-07-03 11:47:43,781 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,781 - server_silent - DEBUG - Suppressed output: ============================================================
2025-07-03 11:47:43,782 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,782 - server_silent - INFO - 🚀 Starting init_project_tool: unified_test_project
2025-07-03 11:47:43,782 - server_silent - INFO -    Parameters: path=F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project, type=single_layer
2025-07-03 11:47:43,783 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
2025-07-03 11:47:43,784 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 11:47:43,784 - server_silent - DEBUG - Suppressed output: 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
2025-07-03 11:47:43,785 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,788 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product_info
2025-07-03 11:47:43,789 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,790 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements
2025-07-03 11:47:43,790 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,792 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design
2025-07-03 11:47:43,794 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,795 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\development
2025-07-03 11:47:43,795 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,796 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality
2025-07-03 11:47:43,797 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,802 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production
2025-07-03 11:47:43,810 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,811 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables
2025-07-03 11:47:43,818 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,823 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management
2025-07-03 11:47:43,824 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,826 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\scripts
2025-07-03 11:47:43,826 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,827 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\config
2025-07-03 11:47:43,828 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,830 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\reports
2025-07-03 11:47:43,840 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,846 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product_info\competitor_products/competitor_A
2025-07-03 11:47:43,854 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,856 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product_info\competitor_products/competitor_B
2025-07-03 11:47:43,857 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,859 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements\main_requirements
2025-07-03 11:47:43,861 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,863 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements\custom_requirements
2025-07-03 11:47:43,893 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,894 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements\market_requirements
2025-07-03 11:47:43,895 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,895 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\project_solution
2025-07-03 11:47:43,896 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,897 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\specification_document
2025-07-03 11:47:43,899 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,903 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\interface_specifications
2025-07-03 11:47:43,904 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,906 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\principle_Information
2025-07-03 11:47:43,907 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,908 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_plans
2025-07-03 11:47:43,909 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,910 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_cases
2025-07-03 11:47:43,910 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,912 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_reports/internal_tests
2025-07-03 11:47:43,912 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,916 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_reports/competitive_tests
2025-07-03 11:47:43,917 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,922 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\issues
2025-07-03 11:47:43,922 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,923 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production\bom
2025-07-03 11:47:43,924 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,925 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production\manufacturing_process
2025-07-03 11:47:43,926 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,927 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production\quality_control
2025-07-03 11:47:43,928 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,930 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\documents
2025-07-03 11:47:43,930 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,931 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\firmware
2025-07-03 11:47:43,933 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,937 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\hardware
2025-07-03 11:47:43,938 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,939 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\software
2025-07-03 11:47:43,940 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,941 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\tools
2025-07-03 11:47:43,942 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,943 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\schedules
2025-07-03 11:47:43,944 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,945 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\resources
2025-07-03 11:47:43,945 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,946 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\risks
2025-07-03 11:47:43,946 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,949 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\meeting_notes
2025-07-03 11:47:43,950 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,956 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\README.md
2025-07-03 11:47:43,956 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,958 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product.canvas
2025-07-03 11:47:43,959 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,960 - server_silent - DEBUG - Suppressed output: 🔍 开始配置生成过程...
2025-07-03 11:47:43,960 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,961 - server_silent - DEBUG - Suppressed output: 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 11:47:43,962 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:43,962 - server_silent - DEBUG - Suppressed output: 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:47:43,962 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,446 - server_silent - DEBUG - Suppressed output: ✅ 配置文件生成成功
2025-07-03 11:47:47,460 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,461 - server_silent - DEBUG - Suppressed output: 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统
2025-07-03 11:47:47,463 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,466 - server_silent - DEBUG - Suppressed output: ✅ 生成了 10 个配置文件:
2025-07-03 11:47:47,466 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,467 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:47:47,468 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,469 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:47:47,469 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,469 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:47:47,469 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,470 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:47:47,470 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,470 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:47:47,470 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,471 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:47:47,472 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,472 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:47:47,472 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,473 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:47:47,473 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,473 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:47:47,473 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,474 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:47:47,475 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,475 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 11:47:47,478 - server_silent - DEBUG - Suppressed output: [init_project_tool] ✅ 成功
2025-07-03 11:47:47,479 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,479 - server_silent - DEBUG - Suppressed output: 📊 测试总结:
2025-07-03 11:47:47,479 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,480 - server_silent - DEBUG - Suppressed output: 总测试数: 3
2025-07-03 11:47:47,480 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,480 - server_silent - DEBUG - Suppressed output: 通过测试: 3
2025-07-03 11:47:47,480 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,482 - server_silent - DEBUG - Suppressed output: 失败测试: 0
2025-07-03 11:47:47,482 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,482 - server_silent - DEBUG - Suppressed output: 成功率: 100.0%
2025-07-03 11:47:47,482 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:47:47,488 - server_silent - DEBUG - Suppressed output: 📄 测试报告已保存: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\unified_test_report_20250703_114747.json
2025-07-03 11:47:47,492 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:22,702 - server_silent - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:49:22,702 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:49:22,702 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 11:49:22,702 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 11:49:23,591 - server_silent - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 11:49:23,591 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:24,407 - server_silent - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 11:49:24,407 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:24,409 - server_silent - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:49:24,409 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:24,433 - server_silent - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 11:49:24,433 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:24,603 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 11:49:24,609 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:49:24,610 - matplotlib - DEBUG - interactive is False
2025-07-03 11:49:24,612 - matplotlib - DEBUG - platform is win32
2025-07-03 11:49:24,706 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 11:49:24,709 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 11:49:24,967 - server_silent - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 11:49:24,967 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:24,967 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 11:49:25,034 - server_silent - DEBUG - Suppressed output: [pythonpath_setting_test] ✅ 成功
2025-07-03 11:49:25,034 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,035 - server_silent - DEBUG - Suppressed output: 📋 开始测试: 路径解析功能
2025-07-03 11:49:25,035 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,035 - server_silent - DEBUG - Suppressed output: 🧪 测试路径解析功能
2025-07-03 11:49:25,035 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,036 - server_silent - DEBUG - Suppressed output: ============================================================
2025-07-03 11:49:25,036 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,036 - server_silent - INFO - 🚀 Starting init_project_tool: path_test_project
2025-07-03 11:49:25,036 - server_silent - INFO -    Parameters: path=example/path_test_project, type=single_layer
2025-07-03 11:49:25,037 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project
2025-07-03 11:49:25,037 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 11:49:25,038 - server_silent - DEBUG - Suppressed output: 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project
2025-07-03 11:49:25,038 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,046 - server_silent - DEBUG - Suppressed output: 🔍 开始配置生成过程...
2025-07-03 11:49:25,048 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,051 - server_silent - DEBUG - Suppressed output: 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 11:49:25,052 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:25,052 - server_silent - DEBUG - Suppressed output: 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:49:25,052 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,174 - server_silent - DEBUG - Suppressed output: ✅ 配置文件生成成功
2025-07-03 11:49:27,174 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,174 - server_silent - DEBUG - Suppressed output: 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\example\path_test_project
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统
2025-07-03 11:49:27,175 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,176 - server_silent - DEBUG - Suppressed output: ✅ 生成了 10 个配置文件:
2025-07-03 11:49:27,176 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,176 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:49:27,176 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,177 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:49:27,177 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,177 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:49:27,178 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,178 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:49:27,178 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,178 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:49:27,178 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,179 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:49:27,179 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,179 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:49:27,179 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,180 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:49:27,181 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,182 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:49:27,182 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,182 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:49:27,182 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,182 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 11:49:27,183 - server_silent - DEBUG - Suppressed output: [path_resolution_test] ✅ 成功
2025-07-03 11:49:27,183 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,183 - server_silent - DEBUG - Suppressed output: 📋 开始测试: 项目初始化
2025-07-03 11:49:27,183 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,183 - server_silent - DEBUG - Suppressed output: 🧪 测试 init_project_tool
2025-07-03 11:49:27,183 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,184 - server_silent - DEBUG - Suppressed output: ============================================================
2025-07-03 11:49:27,184 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,203 - server_silent - DEBUG - Suppressed output: 🧹 清理之前的测试项目: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
2025-07-03 11:49:27,204 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,205 - server_silent - INFO - 🚀 Starting init_project_tool: unified_test_project
2025-07-03 11:49:27,205 - server_silent - INFO -    Parameters: path=F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project, type=single_layer
2025-07-03 11:49:27,207 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
2025-07-03 11:49:27,207 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 11:49:27,207 - server_silent - DEBUG - Suppressed output: 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
2025-07-03 11:49:27,207 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,209 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product_info
2025-07-03 11:49:27,209 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,218 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements
2025-07-03 11:49:27,219 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,221 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design
2025-07-03 11:49:27,221 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,222 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\development
2025-07-03 11:49:27,223 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,224 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality
2025-07-03 11:49:27,224 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,225 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production
2025-07-03 11:49:27,225 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,228 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables
2025-07-03 11:49:27,230 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,233 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management
2025-07-03 11:49:27,235 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,236 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\scripts
2025-07-03 11:49:27,237 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,238 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\config
2025-07-03 11:49:27,239 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,240 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\reports
2025-07-03 11:49:27,240 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,242 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product_info\competitor_products/competitor_A
2025-07-03 11:49:27,243 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,251 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product_info\competitor_products/competitor_B
2025-07-03 11:49:27,252 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,253 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements\main_requirements
2025-07-03 11:49:27,253 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,254 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements\custom_requirements
2025-07-03 11:49:27,255 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,256 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\requirements\market_requirements
2025-07-03 11:49:27,256 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,257 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\project_solution
2025-07-03 11:49:27,257 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,259 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\specification_document
2025-07-03 11:49:27,259 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,260 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\interface_specifications
2025-07-03 11:49:27,262 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,265 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\design\principle_Information
2025-07-03 11:49:27,266 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,267 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_plans
2025-07-03 11:49:27,268 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,270 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_cases
2025-07-03 11:49:27,270 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,271 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_reports/internal_tests
2025-07-03 11:49:27,272 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,272 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\test_reports/competitive_tests
2025-07-03 11:49:27,273 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,274 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\quality\issues
2025-07-03 11:49:27,274 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,275 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production\bom
2025-07-03 11:49:27,276 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,276 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production\manufacturing_process
2025-07-03 11:49:27,278 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,281 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\production\quality_control
2025-07-03 11:49:27,282 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,284 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\documents
2025-07-03 11:49:27,285 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,286 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\firmware
2025-07-03 11:49:27,286 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,287 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\hardware
2025-07-03 11:49:27,288 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,289 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\software
2025-07-03 11:49:27,289 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,290 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\deliverables\tools
2025-07-03 11:49:27,290 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,291 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\schedules
2025-07-03 11:49:27,292 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,292 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\resources
2025-07-03 11:49:27,292 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,293 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\risks
2025-07-03 11:49:27,295 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,297 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\project_management\meeting_notes
2025-07-03 11:49:27,298 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,301 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\README.md
2025-07-03 11:49:27,302 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,303 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project\product.canvas
2025-07-03 11:49:27,305 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,305 - server_silent - DEBUG - Suppressed output: 🔍 开始配置生成过程...
2025-07-03 11:49:27,305 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,307 - server_silent - DEBUG - Suppressed output: 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 11:49:27,307 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:27,307 - server_silent - DEBUG - Suppressed output: 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 11:49:27,308 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,592 - server_silent - DEBUG - Suppressed output: ✅ 配置文件生成成功
2025-07-03 11:49:30,592 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,592 - server_silent - DEBUG - Suppressed output: 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\test_projects\unified_test_project
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统
2025-07-03 11:49:30,595 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,597 - server_silent - DEBUG - Suppressed output: ✅ 生成了 10 个配置文件:
2025-07-03 11:49:30,597 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,598 - server_silent - DEBUG - Suppressed output: - deliverables_config.json
2025-07-03 11:49:30,598 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,598 - server_silent - DEBUG - Suppressed output: - design_config.json
2025-07-03 11:49:30,598 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,599 - server_silent - DEBUG - Suppressed output: - development_config.json
2025-07-03 11:49:30,599 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,599 - server_silent - DEBUG - Suppressed output: - document_links_config.json
2025-07-03 11:49:30,599 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,599 - server_silent - DEBUG - Suppressed output: - production_config.json
2025-07-03 11:49:30,600 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,600 - server_silent - DEBUG - Suppressed output: - quality_config.json
2025-07-03 11:49:30,600 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,600 - server_silent - DEBUG - Suppressed output: - requirements_analysis_config.json
2025-07-03 11:49:30,600 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,600 - server_silent - DEBUG - Suppressed output: - requirements_import_config.json
2025-07-03 11:49:30,601 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,601 - server_silent - DEBUG - Suppressed output: - traceability_config.json
2025-07-03 11:49:30,601 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,601 - server_silent - DEBUG - Suppressed output: - workflow_config.json
2025-07-03 11:49:30,601 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,602 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 11:49:30,604 - server_silent - DEBUG - Suppressed output: [init_project_tool] ✅ 成功
2025-07-03 11:49:30,604 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,604 - server_silent - DEBUG - Suppressed output: 📊 测试总结:
2025-07-03 11:49:30,605 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,605 - server_silent - DEBUG - Suppressed output: 总测试数: 3
2025-07-03 11:49:30,605 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,605 - server_silent - DEBUG - Suppressed output: 通过测试: 3
2025-07-03 11:49:30,605 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,606 - server_silent - DEBUG - Suppressed output: 失败测试: 0
2025-07-03 11:49:30,606 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,606 - server_silent - DEBUG - Suppressed output: 成功率: 100.0%
2025-07-03 11:49:30,606 - server_silent - DEBUG - Suppressed output: 
2025-07-03 11:49:30,607 - server_silent - DEBUG - Suppressed output: 📄 测试报告已保存: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete\unified_test_report_20250703_114930.json
2025-07-03 11:49:30,608 - server_silent - DEBUG - Suppressed output: 
2025-07-03 12:38:43,644 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 12:38:43,644 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 12:38:43,644 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 12:38:43,645 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 12:38:44,549 - __main__ - DEBUG - Suppressed output: SUCCESS: scripts modules imported
2025-07-03 12:38:44,549 - __main__ - DEBUG - Suppressed output: 
2025-07-03 12:38:45,481 - __main__ - DEBUG - Suppressed output: SUCCESS: requirements modules imported
2025-07-03 12:38:45,482 - __main__ - DEBUG - Suppressed output: 
2025-07-03 12:38:45,484 - __main__ - DEBUG - Suppressed output: SUCCESS: Found scripts directory at F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 12:38:45,485 - __main__ - DEBUG - Suppressed output: 
2025-07-03 12:38:45,508 - __main__ - DEBUG - Suppressed output: SUCCESS: document modules imported
2025-07-03 12:38:45,508 - __main__ - DEBUG - Suppressed output: 
2025-07-03 12:38:45,716 - matplotlib - DEBUG - matplotlib data path: D:\Programs\Python313\Lib\site-packages\matplotlib\mpl-data
2025-07-03 12:38:45,724 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-03 12:38:45,726 - matplotlib - DEBUG - interactive is False
2025-07-03 12:38:45,726 - matplotlib - DEBUG - platform is win32
2025-07-03 12:38:45,816 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-03 12:38:45,820 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-03 12:38:46,112 - __main__ - DEBUG - Suppressed output: WARNING: Cannot import visualization modules: No module named 'networkx'
2025-07-03 12:38:46,112 - __main__ - DEBUG - Suppressed output: 
2025-07-03 12:38:46,112 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 12:38:46,174 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 12:38:46,266 - root - WARNING - Failed to validate request: Received request before initialization was complete
2025-07-03 12:38:46,266 - root - DEBUG - Message that failed validation: method='tools/list' params={} jsonrpc='2.0' id=2
2025-07-03 13:11:43,302 - server_silent - INFO - 📝 PYTHONPATH已设置: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 13:11:43,302 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 13:11:43,302 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 13:11:43,302 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 13:11:45,374 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 13:11:45,416 - server_silent - DEBUG - Suppressed output: ✅ 成功导入server_silent模块
2025-07-03 13:11:45,416 - server_silent - DEBUG - Suppressed output: 🧪 测试不同级别的日志输出:
2025-07-03 13:11:45,416 - server_silent - DEBUG - Suppressed output: ----------------------------------------
2025-07-03 13:11:45,416 - test_logger - DEBUG - 这是DEBUG级别日志 - 应该只在文件中
2025-07-03 13:11:45,416 - test_logger - INFO - 这是INFO级别日志 - 应该只在文件中
2025-07-03 13:11:45,416 - test_logger - WARNING - 这是WARNING级别日志 - 应该只在文件中
2025-07-03 13:11:45,416 - test_logger - ERROR - 这是ERROR级别日志 - 应该在stderr中显示
2025-07-03 13:11:45,416 - test_logger - CRITICAL - 这是CRITICAL级别日志 - 应该在stderr中显示
2025-07-03 13:11:45,416 - server_silent - DEBUG - Suppressed output: 📝 日志测试完成
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: ✅ 如果只看到ERROR和CRITICAL在控制台，说明修复成功
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: ✅ 其他级别的日志应该只在mcp_server_debug.log文件中
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: 🚀 测试init_project_tool（观察日志输出）:
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: ----------------------------------------
2025-07-03 13:11:45,417 - server_silent - INFO - 🚀 Starting init_project_tool: test_logging
2025-07-03 13:11:45,417 - server_silent - INFO -    Parameters: path=example/test_logging, type=single_layer
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: 🔍 智能路径推断开始:
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: - 项目名称: test_logging
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: - 提供的路径: example/test_logging
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: - 当前工作目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 13:11:45,417 - server_silent - DEBUG - Suppressed output: - 基于项目根目录解析相对路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging
2025-07-03 13:11:45,417 - server_silent - INFO - 🔍 智能解析提供的路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging
2025-07-03 13:11:45,417 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging
2025-07-03 13:11:45,417 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 13:11:45,417 - tools.project_tools - INFO - 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging
2025-07-03 13:11:45,422 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\product_info
2025-07-03 13:11:45,422 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\requirements
2025-07-03 13:11:45,423 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\design
2025-07-03 13:11:45,424 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\development
2025-07-03 13:11:45,425 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\quality
2025-07-03 13:11:45,426 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\production
2025-07-03 13:11:45,427 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\deliverables
2025-07-03 13:11:45,428 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\project_management
2025-07-03 13:11:45,428 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\scripts
2025-07-03 13:11:45,428 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\config
2025-07-03 13:11:45,429 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\reports
2025-07-03 13:11:45,430 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\product_info\competitor_products/competitor_A
2025-07-03 13:11:45,430 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\product_info\competitor_products/competitor_B
2025-07-03 13:11:45,431 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\requirements\main_requirements
2025-07-03 13:11:45,432 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\requirements\custom_requirements
2025-07-03 13:11:45,432 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\requirements\market_requirements
2025-07-03 13:11:45,432 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\design\project_solution
2025-07-03 13:11:45,433 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\design\specification_document
2025-07-03 13:11:45,433 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\design\interface_specifications
2025-07-03 13:11:45,434 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\design\principle_Information
2025-07-03 13:11:45,435 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\quality\test_plans
2025-07-03 13:11:45,435 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\quality\test_cases
2025-07-03 13:11:45,436 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\quality\test_reports/internal_tests
2025-07-03 13:11:45,437 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\quality\test_reports/competitive_tests
2025-07-03 13:11:45,438 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\quality\issues
2025-07-03 13:11:45,438 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\production\bom
2025-07-03 13:11:45,439 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\production\manufacturing_process
2025-07-03 13:11:45,440 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\production\quality_control
2025-07-03 13:11:45,441 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\deliverables\documents
2025-07-03 13:11:45,442 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\deliverables\firmware
2025-07-03 13:11:45,444 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\deliverables\hardware
2025-07-03 13:11:45,445 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\deliverables\software
2025-07-03 13:11:45,447 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\deliverables\tools
2025-07-03 13:11:45,447 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\project_management\schedules
2025-07-03 13:11:45,448 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\project_management\resources
2025-07-03 13:11:45,449 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\project_management\risks
2025-07-03 13:11:45,451 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\project_management\meeting_notes
2025-07-03 13:11:45,452 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\README.md
2025-07-03 13:11:45,453 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging\product.canvas
2025-07-03 13:11:45,453 - tools.project_tools - INFO - 🔍 开始配置生成过程...
2025-07-03 13:11:45,454 - tools.project_tools - INFO - 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 13:11:45,454 - tools.project_tools - INFO - 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 13:11:48,710 - tools.project_tools - INFO - ✅ 配置文件生成成功
2025-07-03 13:11:48,710 - tools.project_tools - DEBUG - 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_logging
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统

2025-07-03 13:11:48,711 - tools.project_tools - INFO - ✅ 生成了 10 个配置文件:
2025-07-03 13:11:48,711 - tools.project_tools - INFO -    - deliverables_config.json
2025-07-03 13:11:48,711 - tools.project_tools - INFO -    - design_config.json
2025-07-03 13:11:48,711 - tools.project_tools - INFO -    - development_config.json
2025-07-03 13:11:48,711 - tools.project_tools - INFO -    - document_links_config.json
2025-07-03 13:11:48,711 - tools.project_tools - INFO -    - production_config.json
2025-07-03 13:11:48,712 - tools.project_tools - INFO -    - quality_config.json
2025-07-03 13:11:48,712 - tools.project_tools - INFO -    - requirements_analysis_config.json
2025-07-03 13:11:48,712 - tools.project_tools - INFO -    - requirements_import_config.json
2025-07-03 13:11:48,712 - tools.project_tools - INFO -    - traceability_config.json
2025-07-03 13:11:48,712 - tools.project_tools - INFO -    - workflow_config.json
2025-07-03 13:11:48,712 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 13:11:48,712 - server_silent - DEBUG - Suppressed output: ✅ 函数执行完成，成功状态: True
2025-07-03 13:11:48,712 - server_silent - DEBUG - Suppressed output: 🎉 日志修复测试完成！
2025-07-03 13:11:48,712 - server_silent - DEBUG - Suppressed output: ✅ 现在INFO级别日志不会在Cursor中显示为[error]
2025-07-03 13:11:48,712 - server_silent - DEBUG - Suppressed output: ✅ 只有真正的错误才会显示在stderr中
2025-07-03 13:11:48,712 - server_silent - DEBUG - Suppressed output: ✅ 详细日志信息保存在mcp_server_debug.log文件中
2025-07-03 18:07:05,278 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:07:05,278 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:07:05,278 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:07:05,278 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 18:07:07,988 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 18:07:08,071 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 18:07:27,051 - server_module - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:07:27,051 - server_module - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:07:27,051 - server_module - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:07:27,051 - server_module - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\Scripts\\mcp.exe', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 18:07:29,129 - server_module - INFO - MCP Server logging enabled for debugging
2025-07-03 18:13:32,698 - __main__ - INFO - 📝 PYTHONPATH已设置: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:13:32,698 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:13:32,699 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:13:32,699 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 18:13:34,862 - server_silent - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:13:34,862 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:13:34,863 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:13:34,863 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 18:13:37,834 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 18:13:37,899 - server_silent - DEBUG - Suppressed output: 📞 Calling init_project_tool with:
2025-07-03 18:13:37,899 - server_silent - DEBUG - Suppressed output: project_name: test_mcp_fix
2025-07-03 18:13:37,899 - server_silent - DEBUG - Suppressed output: project_path: example/test_mcp_fix
2025-07-03 18:13:37,899 - server_silent - INFO - 🚀 Starting init_project_tool: test_mcp_fix
2025-07-03 18:13:37,899 - server_silent - INFO -    Parameters: path=example/test_mcp_fix, type=single_layer
2025-07-03 18:13:37,899 - server_silent - DEBUG - Suppressed output: 🔍 智能路径推断开始:
2025-07-03 18:13:37,899 - server_silent - DEBUG - Suppressed output: - 项目名称: test_mcp_fix
2025-07-03 18:13:37,900 - server_silent - DEBUG - Suppressed output: - 提供的路径: example/test_mcp_fix
2025-07-03 18:13:37,900 - server_silent - DEBUG - Suppressed output: - 当前工作目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:13:37,900 - server_silent - DEBUG - Suppressed output: - 基于项目根目录解析相对路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:13:37,900 - server_silent - INFO - 🔍 智能解析提供的路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:13:37,900 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:13:37,900 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 18:13:37,900 - tools.project_tools - INFO - 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:13:37,906 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\product_info
2025-07-03 18:13:37,907 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\requirements
2025-07-03 18:13:37,909 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\design
2025-07-03 18:13:37,911 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\development
2025-07-03 18:13:37,912 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\quality
2025-07-03 18:13:37,913 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\production
2025-07-03 18:13:37,914 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\deliverables
2025-07-03 18:13:37,914 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\project_management
2025-07-03 18:13:37,915 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\scripts
2025-07-03 18:13:37,916 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\config
2025-07-03 18:13:37,916 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\reports
2025-07-03 18:13:37,917 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\product_info\competitor_products/competitor_A
2025-07-03 18:13:37,918 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\product_info\competitor_products/competitor_B
2025-07-03 18:13:37,918 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\requirements\main_requirements
2025-07-03 18:13:37,919 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\requirements\custom_requirements
2025-07-03 18:13:37,919 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\requirements\market_requirements
2025-07-03 18:13:37,920 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\design\project_solution
2025-07-03 18:13:37,921 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\design\specification_document
2025-07-03 18:13:37,922 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\design\interface_specifications
2025-07-03 18:13:37,923 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\design\principle_Information
2025-07-03 18:13:37,925 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\quality\test_plans
2025-07-03 18:13:37,927 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\quality\test_cases
2025-07-03 18:13:37,928 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\quality\test_reports/internal_tests
2025-07-03 18:13:37,929 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\quality\test_reports/competitive_tests
2025-07-03 18:13:37,929 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\quality\issues
2025-07-03 18:13:37,930 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\production\bom
2025-07-03 18:13:37,930 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\production\manufacturing_process
2025-07-03 18:13:37,931 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\production\quality_control
2025-07-03 18:13:37,931 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\deliverables\documents
2025-07-03 18:13:37,932 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\deliverables\firmware
2025-07-03 18:13:37,932 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\deliverables\hardware
2025-07-03 18:13:37,933 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\deliverables\software
2025-07-03 18:13:37,934 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\deliverables\tools
2025-07-03 18:13:37,934 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\project_management\schedules
2025-07-03 18:13:37,935 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\project_management\resources
2025-07-03 18:13:37,935 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\project_management\risks
2025-07-03 18:13:37,935 - server_silent - DEBUG - Suppressed output: 创建目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\project_management\meeting_notes
2025-07-03 18:13:37,936 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\README.md
2025-07-03 18:13:37,937 - server_silent - DEBUG - Suppressed output: 创建文件: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix\product.canvas
2025-07-03 18:13:37,937 - tools.project_tools - INFO - 🔍 开始配置生成过程...
2025-07-03 18:13:37,940 - tools.project_tools - INFO - 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 18:13:37,940 - tools.project_tools - INFO - 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:13:42,402 - tools.project_tools - INFO - ✅ 配置文件生成成功
2025-07-03 18:13:42,402 - tools.project_tools - DEBUG - 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统

2025-07-03 18:13:42,403 - tools.project_tools - INFO - ✅ 生成了 10 个配置文件:
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - deliverables_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - design_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - development_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - document_links_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - production_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - quality_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - requirements_analysis_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - requirements_import_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - traceability_config.json
2025-07-03 18:13:42,403 - tools.project_tools - INFO -    - workflow_config.json
2025-07-03 18:13:42,403 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 18:13:42,403 - server_silent - DEBUG - Suppressed output: ⏱️  Execution time: 4.50 seconds
2025-07-03 18:13:42,403 - server_silent - DEBUG - Suppressed output: 📤 Result type: <class 'str'>
2025-07-03 18:13:42,403 - server_silent - DEBUG - Suppressed output: 📤 Result: ✅ Project 'test_mcp_fix' initialized successfully at: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:13:42,403 - server_silent - DEBUG - Suppressed output: ✅ SUCCESS: Tool returned success message
2025-07-03 18:13:42,404 - server_silent - DEBUG - Suppressed output: 🧪 Testing other fixed tools...
2025-07-03 18:13:42,404 - server_silent - DEBUG - Suppressed output: 📞 Testing link_documents_tool...
2025-07-03 18:13:42,407 - server_silent - DEBUG - Suppressed output: ⏱️  Execution time: 0.00 seconds
2025-07-03 18:13:42,407 - server_silent - DEBUG - Suppressed output: 📤 Result type: <class 'str'>
2025-07-03 18:13:42,409 - server_silent - DEBUG - Suppressed output: 📤 Result: ❌ Document linking failed: 项目路径不存在: example\test
2025-07-03 18:13:42,409 - server_silent - DEBUG - Suppressed output: 📞 Testing get_project_info_tool...
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: ⏱️  Execution time: 0.00 seconds
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: 📤 Result type: <class 'str'>
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: 📤 Result: ❌ Project info retrieval failed: 项目路径不存在: example\test
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: ==================================================
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: 📊 TEST SUMMARY
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: ==================================================
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: init_project_tool: ✅ PASS
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: link_documents_tool: ✅ PASS
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: get_project_info_tool: ✅ PASS
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: Overall: 3/3 tests passed
2025-07-03 18:13:42,410 - server_silent - DEBUG - Suppressed output: 🎉 ALL TESTS PASSED - MCP fix appears successful!
2025-07-03 18:14:18,446 - server_silent - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:14:18,446 - server_silent - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:14:18,446 - server_silent - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:14:18,446 - server_silent - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 18:14:20,808 - server_silent - INFO - MCP Server logging enabled for debugging
2025-07-03 18:14:20,862 - server_silent - DEBUG - Suppressed output: 📞 Calling init_project_tool with:
2025-07-03 18:14:20,862 - server_silent - DEBUG - Suppressed output: project_name: test_mcp_fix
2025-07-03 18:14:20,862 - server_silent - DEBUG - Suppressed output: project_path: example/test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - INFO - 🚀 Starting init_project_tool: test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - INFO -    Parameters: path=example/test_mcp_fix, type=single_layer
2025-07-03 18:14:20,863 - server_silent - DEBUG - Suppressed output: 🔍 智能路径推断开始:
2025-07-03 18:14:20,863 - server_silent - DEBUG - Suppressed output: - 项目名称: test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - DEBUG - Suppressed output: - 提供的路径: example/test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - DEBUG - Suppressed output: - 当前工作目录: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:14:20,863 - server_silent - DEBUG - Suppressed output: - 基于项目根目录解析相对路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - INFO - 🔍 智能解析提供的路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - INFO - 🔧 标准化路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:14:20,863 - server_silent - INFO - 📞 Calling init_project function...
2025-07-03 18:14:20,863 - tools.project_tools - INFO - 📁 使用指定项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:14:20,869 - tools.project_tools - INFO - 🔍 开始配置生成过程...
2025-07-03 18:14:20,871 - tools.project_tools - INFO - 🔍 找到配置生成脚本: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py
2025-07-03 18:14:20,871 - tools.project_tools - INFO - 🔍 执行命令: D:\Programs\Python313\python.exe F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts\config\generate_all_configs.py --project-path F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix --project-type single_layer --scripts-base F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:14:24,919 - tools.project_tools - INFO - ✅ 配置文件生成成功
2025-07-03 18:14:24,919 - tools.project_tools - DEBUG - 输出: [*] 开始生成项目配置...
   项目路径: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
   项目类型: single_layer
   配置类型: document_links, workflow, traceability, deliverables, production, quality, development, design, requirements_analysis, requirements_import

[-] 生成 文档关联系统配置...
[+] 文档关联系统配置 生成完成

[-] 生成 工作流系统配置...
[+] 工作流系统配置 生成完成

[-] 生成 追溯系统配置...
[+] 追溯系统配置 生成完成

[-] 生成 交付物配置...
[+] 交付物配置 生成完成

[-] 生成 生产配置...
[+] 生产配置 生成完成

[-] 生成 质量保证配置...
[+] 质量保证配置 生成完成

[-] 生成 开发配置...
[+] 开发配置 生成完成

[-] 生成 设计配置...
[+] 设计配置 生成完成

[-] 生成 需求分析配置...
[+] 需求分析配置 生成完成

[-] 生成 需求导入配置...
[+] 需求导入配置 生成完成

==================================================
[#] 配置生成总结:
   成功: 10/10
   失败: 0/10

[OK] 所有配置文件生成完成！

[i] 后续步骤:
1. 根据项目需要调整各配置文件
2. 运行文档关联系统进行初始化
3. 启动工作流和追溯系统

2025-07-03 18:14:24,920 - tools.project_tools - INFO - ✅ 生成了 10 个配置文件:
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - deliverables_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - design_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - development_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - document_links_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - production_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - quality_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - requirements_analysis_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - requirements_import_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - traceability_config.json
2025-07-03 18:14:24,920 - tools.project_tools - INFO -    - workflow_config.json
2025-07-03 18:14:24,921 - server_silent - INFO - ✅ init_project completed: True
2025-07-03 18:14:24,921 - server_silent - DEBUG - Suppressed output: ⏱️  Execution time: 4.06 seconds
2025-07-03 18:14:24,921 - server_silent - DEBUG - Suppressed output: 📤 Result type: <class 'str'>
2025-07-03 18:14:24,921 - server_silent - DEBUG - Suppressed output: 📤 Result: ✅ Project 'test_mcp_fix' initialized successfully at: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\example\test_mcp_fix
2025-07-03 18:14:24,922 - server_silent - DEBUG - Suppressed output: ✅ SUCCESS: Tool returned success message
2025-07-03 18:14:24,922 - server_silent - DEBUG - Suppressed output: 🧪 Testing other fixed tools...
2025-07-03 18:14:24,922 - server_silent - DEBUG - Suppressed output: 📞 Testing link_documents_tool...
2025-07-03 18:14:24,924 - server_silent - DEBUG - Suppressed output: ⏱️  Execution time: 0.00 seconds
2025-07-03 18:14:24,924 - server_silent - DEBUG - Suppressed output: 📤 Result type: <class 'str'>
2025-07-03 18:14:24,924 - server_silent - DEBUG - Suppressed output: 📤 Result: ❌ Document linking failed: 项目路径不存在: example\test
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: 📞 Testing get_project_info_tool...
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: ⏱️  Execution time: 0.00 seconds
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: 📤 Result type: <class 'str'>
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: 📤 Result: ❌ Project info retrieval failed: 项目路径不存在: example\test
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: ==================================================
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: 📊 TEST SUMMARY
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: ==================================================
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: init_project_tool: ✅ PASS
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: link_documents_tool: ✅ PASS
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: get_project_info_tool: ✅ PASS
2025-07-03 18:14:24,925 - server_silent - DEBUG - Suppressed output: Overall: 3/3 tests passed
2025-07-03 18:14:24,926 - server_silent - DEBUG - Suppressed output: 🎉 ALL TESTS PASSED - MCP fix appears successful!
2025-07-03 18:19:06,070 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:19:06,070 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 18:19:06,070 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 18:19:06,070 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'D:\\Programs\\Python313\\Lib\\site-packages']
2025-07-03 18:19:08,556 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 18:19:08,624 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 23:04:34,422 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 23:04:34,423 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 23:04:34,423 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 23:04:34,423 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv\\Lib\\site-packages', 'D:\\Pro Packages\\50_mcp_src\\mcp-feedback-enhanced\\src']
2025-07-03 23:04:35,059 - tools.project_tools - INFO - SUCCESS: scripts modules imported and PROJECT_TYPES converted
2025-07-03 23:04:35,059 - tools.project_tools - INFO - 🔍 Final PROJECT_TYPES keys: ['single_layer', 'hardware', 'software', 'firmware', 'hardware+software', 'hardware+firmware', 'firmware+software', 'full']
2025-07-03 23:04:35,059 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[single_layer]['components'] type: <class 'dict'>
2025-07-03 23:04:35,059 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[single_layer]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,059 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware]['components'] type: <class 'dict'>
2025-07-03 23:04:35,060 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,060 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[software]['components'] type: <class 'dict'>
2025-07-03 23:04:35,060 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,060 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware]['components'] type: <class 'dict'>
2025-07-03 23:04:35,061 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,061 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+software]['components'] type: <class 'dict'>
2025-07-03 23:04:35,061 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,062 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+firmware]['components'] type: <class 'dict'>
2025-07-03 23:04:35,062 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+firmware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,062 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware+software]['components'] type: <class 'dict'>
2025-07-03 23:04:35,062 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware+software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,063 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[full]['components'] type: <class 'dict'>
2025-07-03 23:04:35,063 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[full]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:04:35,133 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 23:04:35,174 - mcp.server.fastmcp.tools.tool_manager - WARNING - Tool already exists: test_create_directory
2025-07-03 23:04:35,175 - __main__ - INFO - 🚀 Starting MCP Server...
2025-07-03 23:04:35,179 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 23:07:09,132 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 23:07:09,133 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 23:07:09,133 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 23:07:09,133 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv\\Lib\\site-packages', 'D:\\Pro Packages\\50_mcp_src\\mcp-feedback-enhanced\\src']
2025-07-03 23:07:09,942 - tools.project_tools - INFO - SUCCESS: scripts modules imported and PROJECT_TYPES converted
2025-07-03 23:07:09,942 - tools.project_tools - INFO - 🔍 Final PROJECT_TYPES keys: ['single_layer', 'hardware', 'software', 'firmware', 'hardware+software', 'hardware+firmware', 'firmware+software', 'full']
2025-07-03 23:07:09,942 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[single_layer]['components'] type: <class 'dict'>
2025-07-03 23:07:09,943 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[single_layer]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,943 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware]['components'] type: <class 'dict'>
2025-07-03 23:07:09,943 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,943 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[software]['components'] type: <class 'dict'>
2025-07-03 23:07:09,943 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,944 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware]['components'] type: <class 'dict'>
2025-07-03 23:07:09,944 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,944 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+software]['components'] type: <class 'dict'>
2025-07-03 23:07:09,944 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,945 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+firmware]['components'] type: <class 'dict'>
2025-07-03 23:07:09,945 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+firmware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,945 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware+software]['components'] type: <class 'dict'>
2025-07-03 23:07:09,945 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware+software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:09,946 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[full]['components'] type: <class 'dict'>
2025-07-03 23:07:09,946 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[full]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:07:10,024 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 23:07:10,074 - mcp.server.fastmcp.tools.tool_manager - WARNING - Tool already exists: test_create_directory
2025-07-03 23:07:10,074 - __main__ - INFO - 🚀 Starting MCP Server...
2025-07-03 23:07:10,081 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-03 23:08:26,487 - __main__ - INFO - 🔧 强制设置PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 23:08:26,487 - __main__ - INFO - PYTHONPATH: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\scripts
2025-07-03 23:08:26,487 - __main__ - INFO - Current working directory: F:\101_link-notebook\Obsidian-Vault\KA\Product-development\build-tools\mcp-server_local_integrations\unified\product-development-complete
2025-07-03 23:08:26,488 - __main__ - INFO - Sys path: ['F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\scripts', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\build-tools\\mcp-server_local_integrations\\unified\\product-development-complete', 'D:\\Programs\\Python313\\python313.zip', 'D:\\Programs\\Python313\\DLLs', 'D:\\Programs\\Python313\\Lib', 'D:\\Programs\\Python313', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv', 'F:\\101_link-notebook\\Obsidian-Vault\\KA\\Product-development\\.venv\\Lib\\site-packages', 'D:\\Pro Packages\\50_mcp_src\\mcp-feedback-enhanced\\src']
2025-07-03 23:08:27,157 - tools.project_tools - INFO - SUCCESS: scripts modules imported and PROJECT_TYPES converted
2025-07-03 23:08:27,157 - tools.project_tools - INFO - 🔍 Final PROJECT_TYPES keys: ['single_layer', 'hardware', 'software', 'firmware', 'hardware+software', 'hardware+firmware', 'firmware+software', 'full']
2025-07-03 23:08:27,157 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[single_layer]['components'] type: <class 'dict'>
2025-07-03 23:08:27,157 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[single_layer]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,157 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware]['components'] type: <class 'dict'>
2025-07-03 23:08:27,158 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,158 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[software]['components'] type: <class 'dict'>
2025-07-03 23:08:27,158 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,159 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware]['components'] type: <class 'dict'>
2025-07-03 23:08:27,159 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,159 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+software]['components'] type: <class 'dict'>
2025-07-03 23:08:27,159 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,160 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+firmware]['components'] type: <class 'dict'>
2025-07-03 23:08:27,160 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[hardware+firmware]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,160 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware+software]['components'] type: <class 'dict'>
2025-07-03 23:08:27,160 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[firmware+software]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,161 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[full]['components'] type: <class 'dict'>
2025-07-03 23:08:27,161 - tools.project_tools - INFO - 🔍 PROJECT_TYPES[full]['components']: {'REQ': {'name': 'requirements', 'description': '需求'}, 'DES': {'name': 'design', 'description': '设计'}, 'DEV': {'name': 'development', 'description': '开发'}, 'QA': {'name': 'quality', 'description': '质量'}, 'PROD': {'name': 'production', 'description': '生产'}, 'DEL': {'name': 'deliverables', 'description': '交付物'}, 'PM': {'name': 'project_management', 'description': '项目管理'}}
2025-07-03 23:08:27,234 - __main__ - INFO - MCP Server logging enabled for debugging
2025-07-03 23:08:27,280 - mcp.server.fastmcp.tools.tool_manager - WARNING - Tool already exists: test_create_directory
2025-07-03 23:08:27,280 - __main__ - INFO - 🚀 Starting MCP Server...
2025-07-03 23:08:27,285 - asyncio - DEBUG - Using proactor: IocpProactor
