#ifndef _CLEN_ENTER_DATA_MES_OPT_H_
#define _CLEN_ENTER_DATA_MES_OPT_H_

#include <QObject>
#include <stdint.h>
#include <QVector>
#include <QThread>
#include <QTableWidgetItem>

#include "ILoad.h"
#include "saveExcel.h"
#include "lensReadIni.h"
#include "processListB.h"
#include "IComm.h"
#include "ITopBoard.h"
#include "faculaContext.h"

#include "lensAdjustSerial.h"

#include "interpolation.h"
#include "faculaContext.h"

#include "myConnSql.h"
#include "lenSqlHandle.h"
//***************************************其他配置***************************


namespace NClenEnterDataMes {
typedef struct{
    QString                     cur_port_name; //实时显示端口
    QString                     port_name; //使用端口
//    QString                     work_number;   //工单号
//    bool                        nbr_flag;
    bool                        is_auto_judge_facula;
//    QString                     cur_dev_port_name;
//    QString                     dev_port_name;
} StUiConfig;
}


//*************************************光斑数据***************************
class clenEnterDataMesOpt:public QObject
{
    Q_OBJECT

public:
    clenEnterDataMesOpt(const NClenEnterDataMes::StUiConfig &config);
    ~clenEnterDataMesOpt();

    //*****************************************运行步骤*******************
    enum EProcessStep {
        eOPEN_SERIAL        = 0,
        eSTART                 ,
        eCALIB_MODE            ,
        eCHIP_ID               ,
        eVERSION               ,
        eGREY_MODE             ,
        eFACULA_TEST           ,
        eCOMPLETE              ,
    };
    Q_ENUM(EProcessStep);

private:
    /*2. 通信状态*/
    typedef struct {
        uint                                check_error_cnt;
        uint                                timeout_cnt;
    } ST_COMM_STATUS;

    //*******************************调节***********************
    //* 配置输入
    ClensReadIni::IniConfig mst_iniConfig;
    const NClenEnterDataMes::StUiConfig *mst_config_ = nullptr;

    //* 运行/状态
    int m_timerId;
    uint16_t m_port_update_cnt;

    typedef CProcessListB<clenEnterDataMesOpt, EExecStatus, EProcessStep> TClen_process;
    TClen_process* mc_processList_ = nullptr;
    TClen_process::StStepRecord *mst_task_status_ = nullptr;

public:
    QVector<TClen_process::StTask>  mv_task_list;
    ISensorBoardFactory::StFaculaSensorInfo mst_facula_sensor_info; //光斑、光感应芯片信息

    ITable::StTableInfo getOriginTableInfo();
//    IFaculaAdjust::StMapInfo *mst_map_info_ = nullptr;
//    IFaculaAdjust::StMapData *m_map_data_ = nullptr;

private:
    //* 子线程
    CLensAdjustSerial *m_serial_thread_ = nullptr;
    QThread *m_sub_thread_ = nullptr;

    //* 端口接口
    IComm* mi_icomm_ = nullptr; //主端口
//    IComm* mi_icomm_dev_ = nullptr; //设备端口

    //* devices
    ITopBoard* mi_top_board_ = nullptr; //设备
//    IClensMachine* mi_clen_machine_ = nullptr;

    //* communication status
    StCommunicateStatus* mst_comm_status_ = nullptr;

    //* 数据
    QByteArray m_origin_bytes;

    //* 单步骤光斑结果
    typedef struct {
        QByteArray                           mp_origin_data; //原光斑数据
        QVector<QVector<uint32_t>>           map_matrix; //MP matrix 数据
        uint8_t                              result;
    } StSubResult;

    //* 全过程结果数据
    typedef struct {
        QString                     chip_id;
        QString                     sensor_version;
        QString                     origin_data;
//        QString                     load_data;
        QString                     fatal_reason;

        StSubResult                 finalResult;
        QVector<uint32_t>           target_map;
    } StResult;

    typedef union {
        uint16_t    errors;
        struct {
            uint16_t          serial_open             :1;
            uint16_t          version                 :1;
            uint16_t          chip_id                 :1;
        } all_error;
    } UProcessItemDds;

    //* process items
    QMap<int, QString> mm_process_items_discribe{
        {0x0000, " ok"},
        {0x0001, "串口打开失败 "},
        {0x0002, "版本异常 "},
        {0x0004, "芯片ID异常 "},
        {0x0008, ""},
        {0x0010, ""},
        {0x0020, ""},
        {0x0040, ""},
        {0x0080, ""},
    };

    enum class EError_type {
        ePROCESS_STEP_ERROR         = 0, //流程收发异常
        ePROCESS_ITEM_ERROR         = 1,
        eADJUST_STEP_ERROR          = 2,
        eFACULA_JUDGE_ERRPR         = 3,
        eMES_ERROR                  = 4,
    } ;

    typedef struct {
        UProcessItemDds             process_item_dds; //测试项存在异常
        uint32_t                    process_step_dds; //步骤执行异常，异常显示内容比较固定，不用单独显示
        uint32_t                    mes_dds; //mes写入异常, 窗口显示
    } StDds;

    CFaculaContext *mc_facula_context_ = nullptr;

    //* debug
    QWidget *m_message_box_ = nullptr;

    //* 数据存储
    ISaveFile* mi_save_file_ = nullptr;
    QMap<QString, QVariant> mm_result_data; // = nullptr;

    CLenSqlHandle* mc_sql_handle_ = nullptr;
    CLenSqlHandle::StWorkOrder* mst_work_order_ = nullptr; //工单库
    CLenSqlHandle::StXpiddet* mst_xpid_det_ = nullptr; //
    CLenSqlHandle::StResultToMes* mst_mes_xsub2_data_ = nullptr;

    //* result
    StResult *mst_result_ = nullptr;
    StDds *mst_dds_ = nullptr;

    //********************************
    virtual void timerEvent(QTimerEvent *event) override;

    void varibleInit(void);
    void mesDataShow(const QString &tr);
    void mapDataShow();
    inline void sleepMs(uint16_t msec);

    //*******************************循环任务集***************************
    bool portlistUpdate();
    QString sensorVersionParse(const QByteArray &version);
    bool checkVersion(const QString &version);
    bool originDataHanlde(QByteArray &mp_origin_bytes, IFaculaAdjust::StMapData* map_data_);
    //********************************** MES相关 ************************
    bool mesDataHandle(CLenSqlHandle::StResultToMes *st_mes_data_);

    //************************************ 异常处理 ************************
    QString errorInfoPack(EError_type error_type, const uint32_t &error_code);

    EExecStatus readyStep(void);
    EExecStatus readyAck(void);

    EExecStatus startStep(void); //开始抓取镜片
    EExecStatus startAck(void);

    EExecStatus calibMode(void);
    EExecStatus calibModeAck(void);

    EExecStatus chipIdStep(void);
    EExecStatus chipIdAck(void);

    EExecStatus sensorVersionStep(void);
    EExecStatus sensorVersionAck(void);

    EExecStatus greyMapMode(void);
    EExecStatus greyMapModeAck(void);

    EExecStatus faculaTest(void);
    EExecStatus faculaTestAck(void);

    EExecStatus compStep(void);
    EExecStatus compAck(void);

signals:
    //* status update signal
    void moduleInfoShowSignal(const bool &is_error, const QString &info);

    void subThreadSignal(bool is_exc);

    //* port update signal
    void portUpdateSignal(QStringList *port_list_, bool port_flag);

    //* step ack signal
    void readySignal(bool is_open);
    void chipIdAckSignal(const QString &id);
    void sensorVersionSignal(const QString &version);
    void dataAckSignal(const uint &max, const QVector<QVector<uint32_t>> &matrix);
    void compAckSignal(bool is_comp);

    //* result show signals
    void resultSignal(EResult result, const uint8_t &result_index);

private slots:
    void sensorDataReceive(ITopBoard::ECommStep step, ECommStatus status, QByteArray bytes);
};

#endif
