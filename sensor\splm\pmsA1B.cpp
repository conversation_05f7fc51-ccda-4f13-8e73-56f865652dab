#include "pmsA1B.h"
#include "novaP.h"
#include <QDebug>


#include "typeConvert.h"

#include "loadXml.h"
#include "qLog.h"

CPmsA1B::CPmsA1B(IComm *port_) : mi_port_(port_) {
    mi_load_     = new CLoadXml;
    m_protocol_  = new CNovaP;
    mi_spms_soc_ = new CSpmsVi5300;

    //* 此处应该可以选择数据接收方式
    mi_port_->setBufferSize(2000);  //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

    //********************************** varibles init **********************************
    //* read cmd from loc file
    QString filename = QApplication::applicationDirPath() + "/cmdList/pmsA1.xml";
    mi_load_->readParam(filename, &m_interaction_cmd);

    //* get calib step
    //    QMapIterator<QString, QByteArray> m_iter(mi_spms_soc_->mm_calib_flow);
    QMapIterator<QString, ISpmsSoc::StTaskInfo> m_iter(mi_spms_soc_->mm_calib_flow);
    while (m_iter.hasNext()) {
        m_iter.next();

        if (m_interaction_cmd.contains(m_iter.key())) {
            // m_iter.value().cmd = m_interaction_cmd[""];//(m_interaction_cmd.value(m_iter.key()));

            // m_calib_cmd.insert(m_iter.key(), ); //添加 cmd
        } else {
            qDebug() << "/pmsA1 -e: calib step " << m_iter.key() << " dont have local cmd";
        }
    }

    //* calib config
    QMap<QString, int> xml_param;

    xml_param["xtalk_peak"] = 900;   //
    xml_param["xtalk_tof"]  = 100;   //
    xml_param["ref_offset"] = 1000;  //
    xml_param["ref_tof"]    = 200;   //

    QString configFileName = QApplication::applicationDirPath() + "/config/novaA1_config.xml";
    mi_load_->readParam(configFileName, &xml_param);


    //* 区分不同芯片的校正流程与标准
    QString      pms_soc_name = typeid(*mi_spms_soc_).name();
    StCalibItems calib_items_tmp;
    if (pms_soc_name == "class CSpmsVi5300") {
        calib_items_tmp.calib_item_name = "xtalk_calib";
        calib_items_tmp.peak            = xml_param["xtalk_peak"];
        calib_items_tmp.tof             = xml_param["xtalk_tof"];
        calib_items_tmp.cal             = 0;
        m_calib_items.append(calib_items_tmp);

        calib_items_tmp.calib_item_name = "ref_calib";
        calib_items_tmp.peak            = 0;
        calib_items_tmp.tof             = xml_param["ref_tof"];
        calib_items_tmp.cal             = xml_param["ref_offset"];
        m_calib_items.append(calib_items_tmp);
    } else if (pms_soc_name == "class CSpmsVl53l4cd") {
    }

    //    StTestItem item_tmp;

    //    m_test_items.append(item_tmp);

    //* verify standart values


    //    //* 检测相关
    //    xml_param["accuracy_dist1"] = 50; //mm
    //    xml_param["accuracy_dist2"] = 500; //mm
    //    xml_param["accuracy_dist3"] = 1000; //mm
    //    xml_param["accuracy_dist4"] = 2000; //mm
    //    xml_param["accuracy_dist5"] = 3000; //mm
    //    xml_param["accuracy_dist6"] = 4000; //mm

    //    //*
    //    xml_param["point1_accuracy_range"] = 4; //percent
    //    xml_param["point2_accuracy_range"] = 4;
    //    xml_param["point3_accuracy_range"] = 4;
    //    xml_param["point4_accuracy_range"] = 4;
    //    xml_param["point5_accuracy_range"] = 4;
    //    xml_param["point6_accuracy_range"] = 4;


    //    xml_param["point1_precison_range"] = 10;
    //    xml_param["point2_precison_range"] = 10;
    //    xml_param["point3_precison_range"] = 10;
    //    xml_param["point4_precison_range"] = 10;
    //    xml_param["point5_precison_range"] = 10;
    //    xml_param["point6_precison_range"] = 10;

    //    //*
    //    xml_param["current_max"] = 3;
    //    xml_param["current_min"] = 800;

#ifdef INIT_OUTPUT
    qDebug() << "-i pmsA1/ init: --------------------";
    qDebug() << "-i sensorC/ start cmd: " << m_interaction_cmd["start"];
    qDebug() << "-i sensorC/ stop cmd: " << m_interaction_cmd["stop"];
#endif
}

CPmsA1B::~CPmsA1B() {
    delete mi_load_;

    if (m_protocol_ != nullptr)
        delete m_protocol_;
    if (mi_spms_soc_ != nullptr)
        delete mi_spms_soc_;
}

/**
 * @brief 固定指令直接初始化，有变化指令用成员变量，只初始化固定部分
 *
 */
void CPmsA1B::cmd_init(void) {
    uint16_t data;
    //*******************固定指令初始化*******************
    //  m_interaction_cmd["start"] = m_protocol_->getControlCmd();

    //  m_interaction_cmd["chipId"] = m_protocol_->getWriteCmd(kMcuId, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(KNone)));
    //  m_interaction_cmd["caliMode"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofCalibrationMode))); //
    //  m_interaction_cmd["greyMap"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofFaculaMode))); //
    //  m_interaction_cmd["cloudMode"] = m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(static_cast<uint16_t>(kTofScanMode))); //

    //  m_interaction_cmd["stop"] = m_protocol_->getControlCmd();


#ifdef COMM_OUTPUT
    qDebug() << "-i lensST cmd/ start: " << m_interaction_cmd["start"];
    qDebug() << "-i lensST cmd/ chipId: " << m_interaction_cmd["chipId"];
    qDebug() << "-i lensST cmd/ caliMode: " << m_interaction_cmd["caliMode"];
    qDebug() << "-i lensST cmd/ greyMap: " << m_interaction_cmd["greyMap"];
    qDebug() << "-i lensST cmd/ cloudMode: " << m_interaction_cmd["cloudMode"];
#endif

    //******************变化指令初始化********************
}

QByteArray CPmsA1B::portDataRead() {
    return mi_port_->read(5);
}

/**
 * @brief icom_change_interface
 * @param port_
 */
void CPmsA1B::icom_change_interface(IComm *port_) {
    mi_port_ = port_;
}

#if 0
/****************
 * 对于同一类型有多个选项，采用直接写入选项的方式
 * 1. 枚举切换方式：增加了枚举变量，而且添加模式会改动现有函数
 * 2. 每个模式对应一个函数：单一性，但函数会过多
 * *******************************/
/**
 * @brief 切换模式
 * @return
 */
bool CPmsJamooz::modeChange(const EModeType &mode)
{
    bool flag = false;
    switch (mode) {
    case eSINGLE_MODE:  break;
    case eCALI_MODE: flag = mi_port_->write(m_interaction_cmd["caliMode"]); break;
    case eGREY_MODE: flag = mi_port_->write(m_interaction_cmd["caliMode"]); break;
    case eCLOUD_MODE: flag = mi_port_->write(m_interaction_cmd["caliMode"]); break;
    default: flag = false; break;

    }
    return flag;
}
#else
/**
 * @brief 直接写入模式
 * @param mode
 * @return
 */
bool CPmsA1B::modeChange(const uint16_t &mode) {
    m_strPre.clear();
    //  return mi_port_->write(m_protocol_->getWriteCmd(kTofMode, NsTypeConvert::enumToByteArray(mode))); //
}
#endif
/**
 * @brief 读信息
 * @return
 */
bool CPmsA1B::readInfo(const uint8_t &id, const uint16_t &data) {
    QByteArray cmd = m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data));
    qDebug() << "-i clen/ mcu_id: " << cmd;
    m_strPre.clear();
    return mi_port_->write(m_protocol_->getReadCmd(id, NsTypeConvert::enumToByteArray(data)));  //
}

/**
 * @brief CPmsJamooz::changeRigster
 * @return
 */
bool CPmsA1B::changeRigster(){

};

void CPmsA1B::calibTasksInit() {
    QMapIterator<QString, ISpmsSoc::StTaskInfo> m_iter(mi_spms_soc_->mm_calib_flow);
    //    while (m_iter.hasNext()) {
    //        m_iter.next();

    //        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction(&CSpmsVi5300::calibRotate2, &CSpmsVi5300::calibRotate2Ack, 0, 0));
    //        mv_calib_task_list.append(mc_calib_process_->addCallbackFunction(m_iter.value().ptr_, m_iter.value().ack_ptr_, 0, 0)); //
    //    }
}

void CPmsA1B::calibTasksRun(void) {
    mc_calib_process_->tasksRun(mi_spms_soc_, &mv_calib_task_list, mst_calib_task_status_);
}

void CPmsA1B::verifyTasksRun(void) {
}

void CPmsA1B::verifyTasksInit() {
}

//* accuracyVerify items
EExecStatus CPmsA1B::checkRotate() {
}

EExecStatus CPmsA1B::checkRotateAck() {
}

EExecStatus CPmsA1B::distCheck() {
}

EExecStatus CPmsA1B::distCheckAck() {
}


//* work mode items
EExecStatus CPmsA1B::trigRotate() {
}

EExecStatus CPmsA1B::trigRotateAck() {
}


/**
 * @brief 交互指令解析
 * @param str
 * @param length
 * @return
 */
bool CPmsA1B::interactionParsing(QByteArray str, int length) {
    Q_UNUSED(length);
    //  if(str.length() == 0) return false;

    //  QByteArray strSum = m_strPre + str;
    //  if(strSum.length() < (uint16_t)CNovaP::EReceiveFrame::eHEADER_LEN) //固定部分长度6
    //  {
    //      m_strPre = strSum;
    //      return false;
    //  }

    //  qDebug() << "-i senCoinD/ interaction ack:" << strSum;
    //  int i = 0;
    /*1.1 parse*/
    //  for(;;) //留6个数
    //    {
    //      if(strSum.length() >= (uint16_t)CNovaP::EReceiveFrame::eHEADER_LEN) {
    //          if((uchar)strSum.at(0) == (uint16_t)CNovaP::EReceiveFrame::eHEADER) {//帧头  && ((uchar)strSum.at(i + 1) == (CNovaP::kD2H | CNovaP::kHRS |
    //          CNovaP::kHSS))
    //              //*
    //              uint8_t xor_cal = 0;
    //              CNovaP::StFrame* data_receive_ = new CNovaP::StFrame;

    //              //*
    //              data_receive_ = reinterpret_cast<CNovaP::StFrame *>(strSum.data());
    //              uint16_t num = data_receive_->num << 1; //字节数

    //              if(strSum.length() >= (num + CNovaP::eHEADER_LEN)) {//数量符合
    //                  QByteArray array_tmp;
    //                  memcmp(&array_tmp, &strSum, CNovaP::eHEADER_LEN);

    ////                  data_receive_->data.resize(num);
    ////                  data_receive_ = reinterpret_cast<CNovaP::StFrame*>(array_tmp.data()); //qbytearray 无法 copy

    //                  QByteArray strTmp; //指令数据
    //                  for(uint16_t n = 0; n < num + CNovaP::eHEADER_LEN; ++n) {
    //                      if(n != (3)) {
    //                          xor_cal ^= (uchar)strSum.at(n);
    //                        }
    //                      if(n >= CNovaP::eHEADER_LEN) {

    //                          strTmp.append((uchar)strSum.at(n));
    //                        }
    //                    }
    //                  if(xor_cal == data_receive_->check_xor)
    //                    {
    //                      switch (data_receive_->id) {
    //                        case kMcuId:

    ////                          strTmp = data_receive_->data;
    //                          emit dataOutput(ECommStep::eCHIP_ID, ECommStatus::eCOMM_COMP, strTmp);
    //                          break;
    //                        case kTofMode:
    //                          strTmp.clear();
    //                          emit dataOutput(ECommStep::eMODE_CHANGE, ECommStatus::eCOMM_COMP, strTmp);
    //                          break;
    //                        default:
    //                          break;
    //                        }

    //                      strSum.remove(0, (num + CNovaP::eHEADER_LEN));
    //                      m_strPre.clear();
    //                      m_strPre.push_back(strSum); //存储剩余数据
    //                      //                  return true;
    //                    }
    //                  //              else
    //                  //                {
    //                  //                  strSum.remove(0, 1); //i-1
    //                  //                  i = 0;
    //                  //                }
    //                }
    //              else {
    //                  m_strPre.clear();
    //                  m_strPre.push_back(strSum); //存储剩余数据
    //                  return false;
    //                }
    //            }
    //          strSum.remove(0, 1);
    //        }
    //      else {
    //          m_strPre.clear();
    //          m_strPre.push_back(strSum); //存储剩余数据
    //          return false;
    //        }
    //    }
}

/**
 * @brief dtof 灰度图解析 迭代
 */
bool CPmsA1B::dataParsing(QByteArray str, int length) {
    if (length == 0)
        return false;

    //  QByteArray strSum = m_strPre + str;
    //  if(strSum.length() < 6) //固定部分长度6
    //    {
    //      m_strPre = strSum;
    //      return false;
    //    }

    //  QByteArray strTmp; //指令数据
    //  int i = 0;
    //  uint16_t num;
    //  uint8_t xor_cal;
    //  uint32_t data_tmp;

    //  /*1.1 parse*/
    //  for(i = 0; i < (strSum.length() - 5); ++i) //留6个数
    //    {
    //      if(((uchar)strSum.at(i) == 0xA5) && ((uchar)strSum.at(i + 1) == (CNovaP::kD2H | CNovaP::kDFF))) //帧头
    //        {
    //          switch ((uchar)strSum.at(i + 2)) {    //
    //            case 0xAD: //数据反馈
    //              num = (uchar)strSum.at(i + 4) + uint16_t((uchar)strSum.at(i + 5)<<8);
    //              xor_cal = 0;
    //              if((strSum.length() - i) >= (2*num + 6)) //数量符合
    //                {
    //                  strTmp.clear();
    //                  for(uint16_t n = i; n < i + 2*num + 6; ++n)
    //                    {
    //                      if(n != (3 + i))
    //                        {
    //                          xor_cal ^= (uchar)strSum.at(n);
    //                        }
    //                      if(n > (i + 5))
    //                        strTmp.push_back(strSum.at(n)); //
    //                    }
    //                  if(xor_cal == (uchar)strSum.at(i + 3))
    //                    {
    //                      //                        for(uint16_t n = 0; n < (num>>1); ++n)
    //                      //                        {
    //                      //                            data_tmp = (uchar)strSum.at(i + 6 + (n<<2) + 0) | ((uchar)strSum.at(i + 6 + (n<<2) + 1)<<8) |
    //                      ((uchar)strSum.at(i + 6 + (n<<2) + 2)<<16) | ((uchar)strSum.at(i + 6 + (n<<2) + 3)<<24);
    //                      //                            data_tmp_v.push_back(data_tmp); //
    //                      //                        }
    //                      //                        data_tmp_v.clear(); //
    //#if 0 //发送解析后的值
    //                      for(uint16_t n = 0; n < (num>>1); ++n) //
    //                        {
    //                          data_tmp = (uchar)strTmp.at((n<<2) + 0) | ((uchar)strTmp.at((n<<2) + 1)<<8) | ((uchar)strTmp.at((n<<2) + 2)<<16) |
    //                          ((uchar)strTmp.at((n<<2) + 3)<<24);
    //                        }
    //#endif
    //                      emit dataOutput(EProtocolId::eMAP_DATA, ECommStatus::eCOMM_COMP, strTmp);

    //                      strSum.remove(0, (i + 2*num + 6));
    //                      m_strPre.clear();
    //                      m_strPre.push_back(strSum); //存储剩余数据
    //                      return true;
    //                    }
    //                  else
    //                    {
    //                      i += 2;
    //                      qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-clen xor: " << xor_cal;
    //                      strTmp.clear();
    //                      emit dataOutput(ECommStep::eMAP_DATA, ECommStatus::eCOMM_ERROR, strTmp);
    //                    }
    //                }
    //              else
    //                {
    //                  strSum.remove(0, i); //i-1
    //                  m_strPre.clear();
    //                  m_strPre.push_back(strSum); //存储剩余数据
    //                  return false;
    //                }
    //              break;
    //            default:   //无效指令
    //              ++i; //
    //              break;
    //            } //
    //        }
    //    }
    //  strSum.remove(0, i - 1);
    //  m_strPre.clear();
    //  m_strPre.push_back(strSum); //存储剩余数据
    //  return false;
}

void CPmsA1B::cmdUpdate(const uint8_t &cmd_id, const QString &cmd_name, const QByteArray &cmd) {
}
