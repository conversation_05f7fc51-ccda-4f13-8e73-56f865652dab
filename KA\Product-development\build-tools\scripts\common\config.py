#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一配置管理模块

提供所有脚本使用的统一配置接口，基于shared_config.py
但提供更简洁和友好的API。
"""

import sys
from pathlib import Path

# 添加scripts目录到路径
scripts_dir = Path(__file__).parent.parent
sys.path.insert(0, str(scripts_dir))

from shared_config import (
    ALL_COMPONENTS,
    CORE_COMPONENTS, 
    AUXILIARY_COMPONENTS,
    PROJECT_TYPES,
    RELATION_COLORS,
    get_component_info as _get_component_info,
    get_directory_mapping,
    get_component_color,
    infer_component_from_directory
)

def get_shared_config():
    """获取完整的共享配置"""
    return {
        'components': ALL_COMPONENTS,
        'core_components': CORE_COMPONENTS,
        'auxiliary_components': AUXILIARY_COMPONENTS,
        'project_types': PROJECT_TYPES,
        'relation_colors': RELATION_COLORS
    }

def get_component_info(component_id: str):
    """获取组件信息"""
    return _get_component_info(component_id)

def get_all_components():
    """获取所有组件信息"""
    return ALL_COMPONENTS

def get_component_dirs():
    """获取组件目录映射"""
    return get_directory_mapping()

def get_component_list():
    """获取组件列表（按顺序）"""
    return sorted(ALL_COMPONENTS.keys(), key=lambda x: ALL_COMPONENTS[x].order)

def get_core_components():
    """获取核心组件列表"""
    return list(CORE_COMPONENTS.keys())

def get_auxiliary_components():
    """获取辅助组件列表"""
    return list(AUXILIARY_COMPONENTS.keys())

def infer_component(directory_or_path: str) -> str:
    """从目录名或路径推断组件类型"""
    return infer_component_from_directory(directory_or_path)

def get_canvas_layout_config():
    """获取Canvas布局配置"""
    canvas_config = {}
    x_start = 0
    spacing = 700  # 区域间距
    
    for component_id in get_component_list():
        info = get_component_info(component_id)
        canvas_config[component_id] = {
            "x_start": x_start,
            "x_end": x_start + 400,
            "color": info.color,
            "name": info.name,
            "icon": _get_component_icon(component_id)
        }
        x_start += spacing
    
    return canvas_config

def _get_component_icon(component_id: str) -> str:
    """获取组件图标"""
    icons = {
        "PROD_INFO": "📋",
        "REQ": "📝", 
        "DES": "🎨",
        "DEV": "💻",
        "QA": "🧪",
        "PROD": "🏭",
        "PM": "📊",
        "DEL": "📦",
        "DOC": "📚",
        "PROG": "📈"
    }
    return icons.get(component_id, "❓") 