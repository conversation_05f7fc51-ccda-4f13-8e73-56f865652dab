# 本地MCP服务器调试排查方法

## 1. 日志系统设计

### 1.1 简化日志记录系统
```python
# 简化的日志记录 - 只输出到stderr，不创建本地日志文件
import logging
import sys

# 创建简单的日志记录器
logger = logging.getLogger(__name__)
logger.addHandler(logging.StreamHandler(sys.stderr))
logger.setLevel(logging.ERROR)

# 注意：所有日志将输出到 D:\Programs\Microsoft VS Code\mcp_server.log
# 不再在项目目录下创建logs/目录和日志文件
```

### 1.2 关键位置日志记录

**函数入口**：
```python
debug_logger.info(f"=== {function_name} 开始执行 ===")
debug_logger.info(f"输入参数: {params}")
```

**参数处理**：
```python
debug_logger.info(f"开始解析kwargs参数...")
debug_logger.info(f"kwargs解析成功: {kwargs_dict}")
```

**路径解析**：
```python
debug_logger.info(f"=== 路径解析阶段 ===")
debug_logger.info(f"原始project_path: {project_path_param}")
debug_logger.info(f"最终路径: base_path={base_path}, full_project_path={full_project_path}")
```

**脚本执行**：
```python
debug_logger.info(f"=== 脚本执行阶段 ===")
debug_logger.info(f"脚本路径: {script_path}")
debug_logger.info(f"脚本存在: {script_path.exists()}")
debug_logger.info(f"Python解释器: {python_executable}")
debug_logger.info(f"完整命令: {cmd}")
debug_logger.info(f"执行开始时间: {datetime.now()}")
```

## 2. 调试方法分级

### 2.0 Level 0: 重启server

### 2.1 Level 1: 基础调试

#### 📁 **日志文件位置统一说明**

- **唯一日志位置**: `D:\Programs\Microsoft VS Code\mcp_server.log` (系统级MCP服务器日志)
- **不再使用**: 项目本地logs/目录（已移除所有本地日志文件创建）
- **检查命令**: 直接查看 `D:\Programs\Microsoft VS Code\mcp_server.log` 文件

#### 🔍 **基础调试步骤**
- **日志文件检查**：查看上述日志文件位置
- **参数验证**：确认输入参数格式和内容
- **路径验证**：确认所有路径存在且可访问

### 2.2 Level 2: 进程调试
- **subprocess详细日志**：记录命令、工作目录、环境变量
- **超时监控**：记录执行开始/结束时间
- **返回码分析**：分析subprocess返回码和输出

### 2.3 Level 3: 环境调试
- **Python解释器验证**：确认使用正确的Python版本
- **模块导入测试**：验证所有依赖模块可正常导入
- **权限检查**：确认文件/目录访问权限

### 2.4 Level 4: 系统调试
- **MCP协议调试**：检查JSON-RPC通信
- **内存/CPU监控**：监控资源使用情况
- **网络/IO监控**：检查文件IO操作

## 3. 常见问题排查流程

### 3.1 脚本执行超时
```bash
# 1. 检查日志文件
D:/Programs/Microsoft VS Code/mcp_server.log

# 2. 直接测试脚本
python script.py --args

# 3. 检查Python解释器
which python
python --version

# 4. 检查虚拟环境
ls -la .venv/Scripts/
```

### 3.2 参数传递错误
```python
# 在关键位置添加参数打印
debug_logger.info(f"原始参数: {original_params}")
debug_logger.info(f"处理后参数: {processed_params}")
debug_logger.info(f"脚本参数: {script_args}")
```

### 3.3 路径解析问题
```python
# 详细路径调试
debug_logger.info(f"当前工作目录: {os.getcwd()}")
debug_logger.info(f"脚本基础路径: {SCRIPTS_BASE}")
debug_logger.info(f"项目路径解析: {project_path} -> {resolved_path}")
```

## 4. 高级调试技术

### 4.1 实时监控
```bash
# 监控日志文件 (在项目根目录执行)
tail -f D:/Programs/Microsoft VS Code/mcp_server.log | grep "ERROR\|CRITICAL"

# 监控进程
ps aux | grep python | grep mcp

# 监控文件操作
lsof -p <mcp_process_id>
```

### 4.2 性能分析
```python
import time
import psutil

# 执行时间监控
start_time = time.time()
# ... 执行代码 ...
execution_time = time.time() - start_time
debug_logger.info(f"执行时间: {execution_time:.2f}秒")

# 内存使用监控
process = psutil.Process()
memory_info = process.memory_info()
debug_logger.info(f"内存使用: {memory_info.rss / 1024 / 1024:.2f}MB")
```

### 4.3 环境诊断
```python
import sys
import os
import subprocess

def diagnose_environment():
    debug_logger.info(f"=== 环境诊断 ===")
    debug_logger.info(f"Python版本: {sys.version}")
    debug_logger.info(f"Python路径: {sys.executable}")
    debug_logger.info(f"当前工作目录: {os.getcwd()}")
    debug_logger.info(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
    debug_logger.info(f"PATH: {os.environ.get('PATH', 'Not set')}")

    # 测试subprocess
    try:
        result = subprocess.run([sys.executable, "--version"],
                              capture_output=True, text=True, timeout=5)
        debug_logger.info(f"subprocess测试成功: {result.stdout.strip()}")
    except Exception as e:
        debug_logger.error(f"subprocess测试失败: {e}")
```

## 5. 调试工具推荐

### 5.1 日志分析工具
- **grep/awk**：快速过滤和分析日志
- **jq**：JSON格式日志解析
- **tail -f**：实时监控日志

### 5.2 进程监控工具
- **htop/top**：系统资源监控
- **lsof**：文件描述符监控
- **strace/dtrace**：系统调用跟踪

### 5.3 开发工具
- **pdb**：Python调试器
- **pytest**：单元测试框架
- **coverage**：代码覆盖率分析

## 6. 最佳实践

### 6.1 日志管理
- 使用结构化日志格式
- 设置日志轮转和清理
- 区分不同级别的日志信息
- 避免敏感信息泄露

### 6.2 错误处理
- 捕获并记录所有异常
- 提供有意义的错误消息
- 实现优雅的降级机制
- 设置合理的超时时间

### 6.3 测试策略
- 单元测试覆盖核心功能
- 集成测试验证端到端流程
- 性能测试确保响应时间
- 压力测试验证稳定性

## 7. 调试检查清单

### 7.1 基础检查
- [ ] 日志文件是否正常生成
- [ ] 输入参数格式是否正确
- [ ] 文件路径是否存在
- [ ] Python解释器是否正确

### 7.2 执行检查
- [ ] subprocess命令是否正确
- [ ] 工作目录是否正确
- [ ] 环境变量是否设置
- [ ] 超时时间是否合理

### 7.3 输出检查
- [ ] 返回码是否为0
- [ ] 标准输出是否有内容
- [ ] 标准错误是否有错误信息
- [ ] JSON格式是否正确

### 7.4 环境检查
- [ ] MCP服务器是否正常启动
- [ ] 依赖模块是否正确安装
- [ ] 文件权限是否正确
- [ ] 网络连接是否正常

## 8. 实际调试案例

[[local_mcp-servers_issues]]

## 9. 其他开发者的调试方法

### 9.1 业界标准做法
- **结构化日志**：使用JSON格式记录关键信息
- **分布式追踪**：使用trace ID跟踪请求链路
- **健康检查**：定期检查服务状态
- **监控告警**：设置关键指标阈值

### 9.2 MCP特定调试
- **协议验证**：确保JSON-RPC格式正确
- **工具隔离**：单独测试每个工具函数
- **环境一致性**：确保开发和生产环境一致
- **版本管理**：跟踪MCP协议版本兼容性