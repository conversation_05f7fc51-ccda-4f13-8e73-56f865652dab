#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置文件验证器

专门负责验证配置文件的格式和内容。
遵循单一职责原则，只负责配置文件的验证功能。
"""

import json
import yaml
import argparse
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List


def validate_config(
    config_data: Dict[str, Any],
    schema: Optional[Dict[str, Any]] = None,
    required_fields: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    验证配置文件
    
    Args:
        config_data: 配置数据
        schema: 配置模式（可选）
        required_fields: 必需字段列表（可选）
    
    Returns:
        Dict[str, Any]: 验证结果
    """
    try:
        validation_errors = []
        warnings = []
        
        # 检查必需字段
        if required_fields:
            for field in required_fields:
                if field not in config_data:
                    validation_errors.append(f"缺少必需字段: {field}")
                elif config_data[field] is None:
                    validation_errors.append(f"必需字段不能为空: {field}")
        
        # 检查常见配置问题
        if isinstance(config_data, dict):
            # 检查空值
            empty_fields = [k for k, v in config_data.items() if v == ""]
            if empty_fields:
                warnings.extend([f"字段为空: {field}" for field in empty_fields])
            
            # 检查数据库配置
            if "database" in config_data:
                db_config = config_data["database"]
                if isinstance(db_config, dict):
                    required_db_fields = ["host", "port", "name"]
                    for field in required_db_fields:
                        if field not in db_config:
                            warnings.append(f"数据库配置缺少字段: {field}")
            
            # 检查日志配置
            if "logging" in config_data:
                log_config = config_data["logging"]
                if isinstance(log_config, dict):
                    if "level" not in log_config:
                        warnings.append("日志配置缺少级别设置")
            
            # 检查项目配置
            if "project" in config_data:
                project_config = config_data["project"]
                if isinstance(project_config, dict):
                    recommended_fields = ["name", "version", "description"]
                    for field in recommended_fields:
                        if field not in project_config:
                            warnings.append(f"项目配置建议包含字段: {field}")
        
        is_valid = len(validation_errors) == 0
        
        return {
            "success": True,
            "is_valid": is_valid,
            "errors": validation_errors,
            "warnings": warnings,
            "summary": {
                "total_fields": len(config_data) if isinstance(config_data, dict) else 0,
                "error_count": len(validation_errors),
                "warning_count": len(warnings)
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"配置验证失败: {str(e)}"
        }


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='配置文件验证器')
    parser.add_argument('config_path', help='配置文件路径')
    parser.add_argument('--required-fields', nargs='*', 
                       help='必需字段列表')
    parser.add_argument('--schema-file', help='配置模式文件路径')
    
    args = parser.parse_args()
    
    # 加载配置文件
    try:
        config_path = Path(args.config_path)
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {args.config_path}")
            return 1
        
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                config_data = yaml.safe_load(f) or {}
            else:
                config_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return 1
    
    # 加载模式文件（如果提供）
    schema = None
    if args.schema_file:
        try:
            with open(args.schema_file, 'r', encoding='utf-8') as f:
                schema = json.load(f)
        except Exception as e:
            print(f"⚠️ 加载模式文件失败: {e}")
    
    # 验证配置
    result = validate_config(config_data, schema, args.required_fields)
    
    if result["success"]:
        summary = result["summary"]
        print(f"📊 配置验证结果:")
        print(f"   总字段数: {summary['total_fields']}")
        print(f"   错误数量: {summary['error_count']}")
        print(f"   警告数量: {summary['warning_count']}")
        print(f"   验证状态: {'✅ 通过' if result['is_valid'] else '❌ 失败'}")
        print()
        
        if result["errors"]:
            print("❌ 验证错误:")
            for error in result["errors"]:
                print(f"   - {error}")
            print()
        
        if result["warnings"]:
            print("⚠️ 验证警告:")
            for warning in result["warnings"]:
                print(f"   - {warning}")
            print()
        
        return 0 if result["is_valid"] else 1
    else:
        print(f"❌ 验证失败: {result['error']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
