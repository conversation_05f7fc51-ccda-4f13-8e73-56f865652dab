<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>myWidget</class>
 <widget class="QWidget" name="myWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>761</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="windowIcon">
   <iconset resource="../resource.qrc">
    <normaloff>:/icon/cspc1.jpg</normaloff>:/icon/cspc1.jpg</iconset>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <widget class="QTabWidget" name="Navigation">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>黑体</family>
       <pointsize>10</pointsize>
       <weight>50</weight>
       <italic>false</italic>
       <bold>false</bold>
      </font>
     </property>
     <property name="layoutDirection">
      <enum>Qt::LeftToRight</enum>
     </property>
     <property name="styleSheet">
      <string notr="true">QWidget
{
	/*color: rgba(25, 25, 25);
	font: 10pt &quot;黑体&quot;;*/
}
QTabWidget
{
	font: 10pt &quot;黑体&quot;;
	border: 2px solid gray;
	border-radius: 6px;
}
QTabBar::tab{ /*tab统一设置，图表代码中分别添加*/
	color: #F0F0F0;
	background-color: rgb(249, 249, 249);/*rgb(35, 35, 35)*/
	color: rgb(248, 248, 248);

	border-bottom-left-radius:0px;
    border-bottom-right-radius:0px;

    min-width: 60px;
    min-height: 20px;

    font-size: 10px &quot;黑体&quot;;
    /*border: 2px solid #C4C4C3;*/
    border-bottom-color: #C2C7CB;
    padding: 2px;
    padding: 3px 8px 3px 8px;
}
QTabBar::tab:hover {
	/*background-color: rgb(111, 111, 111);*/
	
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 0, 255), stop:1 rgba(255, 255, 255, 255));
}	

QTabBar::tab:selected {
	border-left: 3px solid black;
	background-color: #cbcbcb;
    border-bottom-color:#0b0b0b;
}

::pane/*pane 统一设置*/
{
	border-top:2px solid #C2C7CB; /**/
    /*position:absolute; 
    top:-10px; /*上移*/
	background-color: rgb(31, 31, 31);
	color: rgb(255,255,255);
	font: 10pt &quot;黑体&quot;;
}
QPushButton{
	min-width: 150px;
	min-height: 80px;
	max-width: 150px;
	max-height: 80px;
	
	background-color: rgb(255, 255, 255);
	
	font: 10pt &quot;黑体&quot;;
	font-weight: bold;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 10px;
}

QPushButton:hover{
	min-width: 150px;
	min-height: 80px;
	max-width: 150px;
	max-height: 80px;
	
	background-color: rgb(25, 25, 25);
	
	font: 10pt &quot;黑体&quot;;
	font-weight: bold;
	color: rgb(255, 255, 255);

	border: 2px solid white;
	border-radius: 10px;
}
</string>
     </property>
     <property name="tabPosition">
      <enum>QTabWidget::West</enum>
     </property>
     <property name="tabShape">
      <enum>QTabWidget::Rounded</enum>
     </property>
     <property name="currentIndex">
      <number>1</number>
     </property>
     <property name="iconSize">
      <size>
       <width>50</width>
       <height>50</height>
      </size>
     </property>
     <property name="elideMode">
      <enum>Qt::ElideMiddle</enum>
     </property>
     <property name="tabsClosable">
      <bool>false</bool>
     </property>
     <property name="movable">
      <bool>true</bool>
     </property>
     <property name="tabBarAutoHide">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="rangeFinderT">
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/lens-1.png</normaloff>:/icon/w_b/lens-1.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
     </widget>
     <widget class="QWidget" name="metrialInspectT">
      <property name="font">
       <font>
        <family>黑体</family>
        <pointsize>10</pointsize>
        <weight>50</weight>
        <italic>false</italic>
        <bold>false</bold>
       </font>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/metrial_in.png</normaloff>:/icon/w_b/metrial_in.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
      <widget class="QPushButton" name="threadMotorMonitor">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>70</y>
         <width>154</width>
         <height>84</height>
        </rect>
       </property>
       <property name="text">
        <string>电机监控</string>
       </property>
      </widget>
      <widget class="QPushButton" name="lightAdjustP">
       <property name="geometry">
        <rect>
         <x>560</x>
         <y>70</y>
         <width>154</width>
         <height>84</height>
        </rect>
       </property>
       <property name="text">
        <string>调焦</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="boardCheck">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>210</y>
         <width>154</width>
         <height>84</height>
        </rect>
       </property>
       <property name="text">
        <string>电路板检测</string>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="functionInspectT">
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/checklist.png</normaloff>:/icon/w_b/checklist.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
      <widget class="QPushButton" name="turnOnTimeP">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>110</y>
         <width>154</width>
         <height>84</height>
        </rect>
       </property>
       <property name="text">
        <string>转速监控</string>
       </property>
      </widget>
      <widget class="QPushButton" name="comCheck">
       <property name="geometry">
        <rect>
         <x>340</x>
         <y>110</y>
         <width>154</width>
         <height>84</height>
        </rect>
       </property>
       <property name="text">
        <string>通信监控</string>
       </property>
      </widget>
      <widget class="QPushButton" name="novaCalibration">
       <property name="geometry">
        <rect>
         <x>610</x>
         <y>110</y>
         <width>154</width>
         <height>84</height>
        </rect>
       </property>
       <property name="text">
        <string>NOVA校正</string>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="dashBoardT">
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/dashboard-1.png</normaloff>:/icon/w_b/dashboard-1.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
     </widget>
     <widget class="QWidget" name="databaseT">
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/database-2.png</normaloff>:/icon/w_b/database-2.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0" colspan="2">
        <widget class="QTableView" name="tableView"/>
       </item>
       <item row="1" column="0" colspan="2">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>库：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="dateBaseSelect">
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <item>
            <property name="text">
             <string>motor speed monitor</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>lidar speed</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>turn on time</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>facula adjust</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_3">
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>table：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="tableName">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 100px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>2022-8-2</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>1160</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="output">
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>导出</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item row="2" column="0">
        <widget class="QLineEdit" name="lineEdit">
         <property name="styleSheet">
          <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 100px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <layout class="QGridLayout" name="gridLayout">
         <item row="1" column="0">
          <widget class="QPushButton" name="execute">
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>执行</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QPushButton" name="save">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>39</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>保存</string>
           </property>
          </widget>
         </item>
         <item row="0" column="2">
          <widget class="QPushButton" name="cellAdd">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>39</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>增加</string>
           </property>
          </widget>
         </item>
         <item row="1" column="2">
          <widget class="QPushButton" name="cellDelete">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>39</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>删除</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QPushButton" name="find">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>39</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>查找</string>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <widget class="QPushButton" name="cancle">
           <property name="minimumSize">
            <size>
             <width>85</width>
             <height>39</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;
	max-width: 81px;
	max-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
#label_12{
	/*font: 8pt  &quot;黑体&quot;;
	font-weight: bold;*/
}</string>
           </property>
           <property name="text">
            <string>取消</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="1">
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="cmdT">
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/command.png</normaloff>:/icon/w_b/command.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
     </widget>
     <widget class="QWidget" name="helpT">
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/help-1.png</normaloff>:/icon/w_b/help-1.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
     </widget>
     <widget class="QWidget" name="setting">
      <attribute name="icon">
       <iconset resource="../resource.qrc">
        <normaloff>:/icon/w_b/setting.png</normaloff>:/icon/w_b/setting.png</iconset>
      </attribute>
      <attribute name="title">
       <string/>
      </attribute>
     </widget>
    </widget>
   </item>
  </layout>
  <action name="actionhelp">
   <property name="icon">
    <iconset resource="../resource.qrc">
     <normaloff>:/icon/w_b/help.png</normaloff>:/icon/w_b/help.png</iconset>
   </property>
   <property name="text">
    <string>help</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="../resource.qrc"/>
 </resources>
 <connections/>
</ui>
