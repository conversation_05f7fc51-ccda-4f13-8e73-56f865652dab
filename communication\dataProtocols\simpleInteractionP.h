#ifndef  _SIMPLE_INTERACTION_P_H_
#define  _SIMPLE_INTERACTION_P_H_

#include <QByteArray>
#include "IProtocol.h"
#include <QVector>


class CSimpleInteraction:public IProtocol
{
public:
    CSimpleInteraction();
    ~CSimpleInteraction();

    //*协议cmd枚举
    enum class EInteractionFrame {
        eHEADER     = 0x3EA5,
        eHEADER_LEN = 0x06,
        eXOR_INDEX  = 0x04,
    };

    /*协议cmd枚举*/
    enum class ECmd{
        eH2D        = 0<<0, //host->device
        eD2H        = 1<<0, //device->host

        eCMD        = 0<<2 | 0<<1, //cmd
        eACK        = 0<<2 | 1<<1, //ack
//        eINFO       = 1<<2 | 0<<1, //info
//        eDATA       = 1<<2 | 1<<1, //data

        eW          = 0<<3, //write
        eR          = 1<<3, //read
    };

    enum class EFunctionCode {
        eSINGLE_ACK         = 0x06,
        eAUTO_ACK           = 0x10,
    };

#define DATA_CACHE          20

//#pragma pack(1)
    typedef struct {
        uint16_t            header;
        uint8_t             cmd;
        uint8_t             id;
        uint8_t             checkXor;
        uint8_t             num;
        uint8_t             data[DATA_CACHE]; //LSB
    } StInteractionFrame;


    QByteArray getControlCmd(const char &id) override;
    QByteArray getWriteCmd(const char &id, const QByteArray &w_data) override;
    QByteArray getReadCmd(const uint8_t &id, const QByteArray &w_data) override;

private:
    StInteractionFrame* mst_interaction_frame_ = nullptr;
};


#endif
