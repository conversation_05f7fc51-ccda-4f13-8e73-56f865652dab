#!/usr/bin/env python
# 脚本路径: scripts/requirements/sync_status.py

import os
import re
import json
import argparse

def update_requirement_status(matrix_path, req_id, new_status):
    """更新单个需求的状态"""
    with open(matrix_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并更新需求状态
    req_pattern = rf'\| {req_id} \|(.*?)\|(.*?)\| (.*?) \|'
    updated_content = re.sub(req_pattern, f'| {req_id} |\\1|\\2| {new_status} |', content)
    
    with open(matrix_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"已更新需求 {req_id} 状态为 {new_status}")
    return True

def find_dependent_requirements(config_path, req_id):
    """查找依赖于指定需求的其他需求"""
    dependents = []
    
    # 解析层级配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 扫描所有矩阵文件
    for level in config['levels']:
        level_id = level['level_id']
        for instance in level['instances']:
            matrix_path = f"level_{level_id}_{instance}/requirements/requirements_matrix.md"
            
            if os.path.exists(matrix_path):
                with open(matrix_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找引用了指定需求的行
                pattern = rf'\|(.*?)\|.*\[{req_id}\].*\|(.*?)\|(.*?)\|'
                matches = re.findall(pattern, content)
                
                for match in matches:
                    dependent_id = match[0].strip()
                    dependents.append((dependent_id, matrix_path))
    
    return dependents

def find_requirement_matrix(config_path, req_id):
    """查找指定需求ID所在的矩阵文件"""
    # 解析层级配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 扫描所有矩阵文件
    for level in config['levels']:
        level_id = level['level_id']
        for instance in level['instances']:
            matrix_path = f"level_{level_id}_{instance}/requirements/requirements_matrix.md"
            
            if os.path.exists(matrix_path):
                with open(matrix_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含指定需求ID
                if re.search(rf'\| {req_id} \|', content):
                    return matrix_path
    
    return None

def sync_status(config_path, req_id, new_status, propagate=False):
    """同步需求状态，可选择传播到依赖需求"""
    # 查找需求所在矩阵
    req_matrix = find_requirement_matrix(config_path, req_id)
    
    if not req_matrix:
        print(f"错误: 未找到需求 {req_id}")
        return False
    
    # 更新需求状态
    update_requirement_status(req_matrix, req_id, new_status)
    
    # 如果需要传播状态变更
    if propagate:
        dependents = find_dependent_requirements(config_path, req_id)
        for dependent_id, matrix_path in dependents:
            print(f"传播状态变更到依赖需求: {dependent_id}")
            update_requirement_status(matrix_path, dependent_id, new_status)
    
    return True

def main():
    parser = argparse.ArgumentParser(description='同步需求状态')
    parser.add_argument('--config', required=True, help='层级配置文件路径')
    parser.add_argument('--id', required=True, help='需求ID')
    parser.add_argument('--status', required=True, help='新状态')
    parser.add_argument('--propagate', action='store_true', help='是否传播到依赖需求')
    args = parser.parse_args()
    
    success = sync_status(args.config, args.id, args.status, args.propagate)
    return 0 if success else 1

if __name__ == "__main__":
    exit(main()) 