#!/usr/bin/env python3
"""
信息追溯管理器
负责管理整个产品开发过程中的信息追溯系统，实现从需求到输出的完整闭环追溯
"""

import os
import json
import re
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

class TraceabilityManager:
    def __init__(self, config_file):
        """初始化追溯管理器"""
        self.config_file = config_file
        self.config = self.load_config()
        self.setup_logging()
        
    def load_config(self):
        """加载追溯配置"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def generate_document_id(self, component: str, doc_type: str, doc_name: str) -> str:
        """生成文档ID"""
        # 获取下一个序号
        next_seq = self.get_next_sequence_number(component, doc_type)
        
        # 格式: [组件代号]_[文档类型]_[序号]_[文档名称]
        doc_id = f"{component}_{doc_type}_{next_seq:03d}_{doc_name}"
        
        self.logger.info(f"生成文档ID: {doc_id}")
        return doc_id
    
    def get_next_sequence_number(self, component: str, doc_type: str) -> int:
        """获取下一个序号"""
        tracking_file = 'reports/id_tracking.json'
        
        if os.path.exists(tracking_file):
            with open(tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)
        else:
            tracking_data = {
                "id_tracking": {
                    "version": "1.0.0",
                    "last_updated": datetime.now().isoformat(),
                    "documents": {},
                    "relationships": []
                }
            }
        
        # 找到最大序号
        max_seq = 0
        documents = tracking_data['id_tracking']['documents']
        
        for doc_id, doc_info in documents.items():
            if doc_id.startswith(f"{component}_{doc_type}_"):
                try:
                    seq_part = doc_id.split('_')[2]
                    seq_num = int(seq_part)
                    max_seq = max(max_seq, seq_num)
                except (IndexError, ValueError):
                    continue
        
        return max_seq + 1
    
    def register_document(self, doc_id: str, file_path: str, doc_type: str, 
                         component: str, metadata: Dict = None) -> bool:
        """注册文档到追溯系统"""
        tracking_file = 'reports/id_tracking.json'
        
        if os.path.exists(tracking_file):
            with open(tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)
        else:
            tracking_data = {
                "id_tracking": {
                    "version": "1.0.0",
                    "last_updated": datetime.now().isoformat(),
                    "documents": {},
                    "relationships": []
                }
            }
        
        # 注册文档
        tracking_data['id_tracking']['documents'][doc_id] = {
            'file_path': file_path,
            'doc_type': doc_type,
            'component': component,
            'created_at': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        # 更新时间戳
        tracking_data['id_tracking']['last_updated'] = datetime.now().isoformat()
        
        # 保存
        with open(tracking_file, 'w', encoding='utf-8') as f:
            json.dump(tracking_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"文档已注册: {doc_id} -> {file_path}")
        return True
    
    def create_relationship(self, source_id: str, target_id: str, 
                          relationship_type: str, metadata: Dict = None) -> bool:
        """创建文档间的关联关系"""
        tracking_file = 'reports/id_tracking.json'
        
        if os.path.exists(tracking_file):
            with open(tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)
        else:
            return False
        
        # 检查文档是否存在
        documents = tracking_data['id_tracking']['documents']
        if source_id not in documents or target_id not in documents:
            self.logger.warning(f"文档不存在: {source_id} 或 {target_id}")
            return False
        
        # 创建关系
        relationship = {
            'source_id': source_id,
            'target_id': target_id,
            'relationship_type': relationship_type,
            'created_at': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
        
        tracking_data['id_tracking']['relationships'].append(relationship)
        tracking_data['id_tracking']['last_updated'] = datetime.now().isoformat()
        
        # 保存
        with open(tracking_file, 'w', encoding='utf-8') as f:
            json.dump(tracking_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"创建关系: {source_id} -> {target_id} ({relationship_type})")
        return True
    
    def scan_project_documents(self) -> List[Dict]:
        """扫描项目中的所有文档并识别ID - 基于INDEX文件"""
        documents = []
        
        # 扫描各个组件目录的INDEX文件
        components = self.config['traceability']['components']
        
        for comp_code, comp_info in components.items():
            directory = comp_info['directory']
            index_file = comp_info.get('index_file', f"{comp_code}_INDEX.md")
            index_path = os.path.join(directory, index_file)
            
            if os.path.exists(index_path):
                index_docs = self.parse_index_file(index_path, comp_code, directory)
                documents.extend(index_docs)
                self.logger.info(f"从 {index_path} 解析到 {len(index_docs)} 个文档")
            else:
                self.logger.warning(f"INDEX文件不存在: {index_path}")
        
        self.logger.info(f"扫描到 {len(documents)} 个文档")
        return documents
    
    def parse_index_file(self, index_path: str, component: str, base_dir: str) -> List[Dict]:
        """解析INDEX文件，提取文档信息"""
        documents = []
        
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找文档列表表格
            lines = content.split('\n')
            in_table = False
            header_found = False
            
            for line in lines:
                line = line.strip()
                
                # 检测表格开始
                if '| 文档ID |' in line and '| 文档名称 |' in line:
                    header_found = True
                    continue
                
                # 跳过表格分隔行
                if header_found and line.startswith('|---'):
                    in_table = True
                    continue
                
                # 解析表格数据行
                if in_table and line.startswith('|') and line.endswith('|'):
                    # 检查是否是空行或示例行
                    if '| | | | | | | | |' in line or '[示例]' in line:
                        continue
                    
                    # 解析表格行
                    parts = [p.strip() for p in line.split('|')[1:-1]]  # 去掉首尾空元素
                    
                    if len(parts) >= 8 and parts[0]:  # 确保有文档ID
                        doc_id = parts[0]
                        doc_name = parts[1]
                        doc_path = parts[2]
                        doc_type = parts[3]
                        related_comp = parts[4]
                        related_doc_id = parts[5]
                        relation_type = parts[6]
                        last_update = parts[7]
                        
                        # 构建完整文件路径
                        if doc_path and not os.path.isabs(doc_path):
                            full_path = os.path.join(base_dir, doc_path)
                        else:
                            full_path = doc_path
                        
                        # 检查文件是否存在
                        if os.path.exists(full_path):
                            documents.append({
                                'id': doc_id,
                                'name': doc_name,
                                'path': full_path,
                                'relative_path': doc_path,
                                'component': component,
                                'doc_type': doc_type,
                                'related_component': related_comp,
                                'related_doc_id': related_doc_id,
                                'relation_type': relation_type,
                                'last_update': last_update
                            })
                        else:
                            self.logger.warning(f"文档文件不存在: {full_path}")
                
                # 检测表格结束
                elif in_table and not line.startswith('|'):
                    break
        
        except Exception as e:
            self.logger.error(f"解析INDEX文件失败 {index_path}: {str(e)}")
        
        return documents
    
    def update_tracking_tables(self):
        """更新追溯表格"""
        self.logger.info("开始更新追溯表格")
        
        # 扫描项目文档
        documents = self.scan_project_documents()
        
        # 加载现有追溯数据
        tracking_file = 'reports/id_tracking.json'
        if os.path.exists(tracking_file):
            with open(tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)
        else:
            tracking_data = {
                "id_tracking": {
                    "version": "1.0.0",
                    "last_updated": datetime.now().isoformat(),
                    "documents": {},
                    "relationships": []
                }
            }
        
        # 更新文档注册
        for doc in documents:
            doc_id = doc['id']
            if doc_id not in tracking_data['id_tracking']['documents']:
                self.register_document(
                    doc_id, 
                    doc['path'], 
                    doc['doc_type'], 
                    doc['component'],
                    {
                        'name': doc['name'],
                        'relative_path': doc['relative_path'],
                        'last_update': doc['last_update']
                    }
                )
        
        # 从INDEX文件中创建关系
        self.create_relationships_from_index(documents)
        
        # 自动创建组件间关系
        self.auto_create_relationships()
        
        self.logger.info("追溯表格更新完成")
    
    def create_relationships_from_index(self, documents: List[Dict]):
        """根据INDEX文件中的关联信息创建关系"""
        tracking_file = 'reports/id_tracking.json'
        with open(tracking_file, 'r', encoding='utf-8') as f:
            tracking_data = json.load(f)
        
        existing_relationships = tracking_data['id_tracking']['relationships']
        existing_pairs = set()
        for rel in existing_relationships:
            existing_pairs.add((rel['source_id'], rel['target_id']))
        
        # 根据INDEX文件中的关联信息创建关系
        for doc in documents:
            source_id = doc['id']
            related_doc_id = doc.get('related_doc_id', '').strip()
            relation_type = doc.get('relation_type', '').strip()
            
            if related_doc_id and relation_type and related_doc_id != '':
                # 支持多个关联文档ID（用逗号分隔）
                related_ids = [rid.strip() for rid in related_doc_id.split(',') if rid.strip()]
                
                for target_id in related_ids:
                    if (source_id, target_id) not in existing_pairs:
                        # 检查目标文档是否存在
                        target_exists = any(d['id'] == target_id for d in documents)
                        if target_exists:
                            self.create_relationship(source_id, target_id, relation_type)
                            existing_pairs.add((source_id, target_id))
                        else:
                            self.logger.warning(f"关联的目标文档不存在: {target_id}")
        
        self.logger.info("INDEX关系创建完成")
    
    def auto_create_relationships(self):
        """根据配置自动创建文档关系"""
        relationships = self.config['traceability']['relationships']
        
        tracking_file = 'reports/id_tracking.json'
        with open(tracking_file, 'r', encoding='utf-8') as f:
            tracking_data = json.load(f)
        
        documents = tracking_data['id_tracking']['documents']
        existing_relationships = tracking_data['id_tracking']['relationships']
        
        # 构建现有关系的查找表
        existing_pairs = set()
        for rel in existing_relationships:
            existing_pairs.add((rel['source_id'], rel['target_id']))
        
        # 根据配置创建关系
        for rel_config in relationships:
            from_comp = rel_config['from']
            to_comp = rel_config['to']
            rel_type = rel_config['type']
            
            # 找到对应组件的文档
            from_docs = [doc_id for doc_id, doc_info in documents.items() 
                        if doc_info['component'] == from_comp]
            to_docs = [doc_id for doc_id, doc_info in documents.items() 
                      if doc_info['component'] == to_comp]
            
            # 创建关系（简化版本，实际可能需要更复杂的匹配逻辑）
            for from_doc in from_docs:
                for to_doc in to_docs:
                    if (from_doc, to_doc) not in existing_pairs:
                        self.create_relationship(from_doc, to_doc, rel_type)
                        existing_pairs.add((from_doc, to_doc))
    
    def generate_traceability_report(self):
        """生成追溯报告"""
        tracking_file = 'reports/id_tracking.json'
        if not os.path.exists(tracking_file):
            self.logger.warning("未找到追溯数据文件")
            return
        
        with open(tracking_file, 'r', encoding='utf-8') as f:
            tracking_data = json.load(f)
        
        documents = tracking_data['id_tracking']['documents']
        relationships = tracking_data['id_tracking']['relationships']
        
        # 生成报告
        report = f"""# 产品追溯报告

## 概览

- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 文档总数: {len(documents)}
- 关系总数: {len(relationships)}

## 文档统计

"""
        
        # 按组件统计文档
        component_stats = {}
        for doc_id, doc_info in documents.items():
            component = doc_info['component']
            if component not in component_stats:
                component_stats[component] = 0
            component_stats[component] += 1
        
        for component, count in component_stats.items():
            comp_name = self.config['traceability']['components'][component]['name']
            report += f"- **{comp_name} ({component})**: {count} 个文档\n"
        
        report += "\n## 文档列表\n\n"
        
        # 按组件列出文档
        for comp_code, comp_info in self.config['traceability']['components'].items():
            comp_docs = [doc_id for doc_id, doc_info in documents.items() 
                        if doc_info['component'] == comp_code]
            
            if comp_docs:
                report += f"### {comp_info['name']} ({comp_code})\n\n"
                for doc_id in sorted(comp_docs):
                    doc_info = documents[doc_id]
                    report += f"- `{doc_id}`: {doc_info['file_path']}\n"
                report += "\n"
        
        report += "\n## 关系网络\n\n"
        
        # 列出关系
        for relationship in relationships:
            source = relationship['source_id']
            target = relationship['target_id']
            rel_type = relationship['relationship_type']
            report += f"- `{source}` → `{target}` ({rel_type})\n"
        
        # 保存报告
        with open('reports/traceability_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info("追溯报告已生成: reports/traceability_report.md")
    
    def generate_reports(self):
        """生成所有报告"""
        self.generate_traceability_report()
        # 可以添加其他报告生成函数

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='信息追溯管理器')
    parser.add_argument('--config', default='config/traceability_config.json', 
                       help='追溯配置文件路径')
    parser.add_argument('--action', choices=['update', 'report', 'scan'], 
                       default='update', help='执行的操作')
    
    args = parser.parse_args()
    
    tm = TraceabilityManager(args.config)
    
    if args.action == 'update':
        tm.update_tracking_tables()
        tm.generate_reports()
    elif args.action == 'report':
        tm.generate_reports()
    elif args.action == 'scan':
        documents = tm.scan_project_documents()
        print(json.dumps(documents, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main() 