# VSCode Tasks 配置指南

本目录包含VSCode任务配置文件和相关工具，用于在VSCode环境中快速执行产品开发相关的脚本和工具。

## 文件说明

- `updated_tasks.json` - VSCode任务配置文件
- `naming_config.json` - 任务命名配置
- `naming_conventions.md` - 任务命名规范说明

## 使用方法

### 1. 配置VSCode任务

1. 将 `updated_tasks.json` 中的内容复制到VSCode的用户任务配置中
2. 或者在项目根目录创建 `.vscode/tasks.json` 文件
3. 配置环境变量指向脚本目录

### 2. 执行任务

1. 在VSCode中按 `Ctrl+Shift+P`
2. 输入 "Tasks: Run Task"
3. 选择要执行的任务

## 任务分类

### 项目初始化任务

**初始化产品项目**
- 任务名称：`初始化产品项目`
- 功能：创建新的产品项目结构
- 参数：项目名称、结构类型
- 脚本：`scripts/init_product_project.py`

**初始化硬件项目**
- 任务名称：`初始化硬件项目`
- 功能：创建硬件开发项目
- 参数：项目路径、硬件类型
- 脚本：`scripts/hardware/init_hw_project.py`

**初始化固件项目**
- 任务名称：`初始化固件项目`
- 功能：创建固件开发项目
- 参数：项目路径、MCU类型
- 脚本：`scripts/firmware/init_fw_project.py`

**初始化软件项目**
- 任务名称：`初始化Qt应用项目`
- 功能：创建Qt应用开发项目
- 参数：项目路径
- 脚本：`scripts/software/init_qt_project.py`

### 需求管理任务

**导入需求文档**
- 任务名称：`导入需求文档`
- 功能：从Excel、Word等格式导入需求
- 参数：文档路径、需求类型
- 脚本：`scripts/requirements/import_requirements.py`

**需求分析**
- 任务名称：`需求分析`
- 功能：分析需求内容和结构
- 参数：需求目录
- 脚本：`scripts/requirements/analyze_requirements.py`

**需求转任务**
- 任务名称：`需求转任务`
- 功能：将需求矩阵转换为任务列表
- 参数：需求矩阵文件、输出路径
- 脚本：`scripts/integration/req_to_tasks.py`

### 项目管理任务

**生成任务计划**
- 任务名称：`生成任务计划`
- 功能：生成任务分解和进度计划
- 参数：需求文件、输出目录
- 脚本：`scripts/project_management/generate_tasks.py`

**同步需求任务状态**
- 任务名称：`同步需求任务状态`
- 功能：同步需求和任务的状态
- 参数：需求目录、任务目录
- 脚本：`scripts/integration/sync_req_tasks.py`

**生成追踪报告**
- 任务名称：`生成追踪报告`
- 功能：生成需求-任务追踪报告
- 参数：输出路径
- 脚本：`scripts/reporting/generate_req_task_report.py`

### 质量管理任务

**硬件设计检查**
- 任务名称：`硬件设计检查`
- 功能：检查硬件设计文件
- 参数：设计文件路径
- 脚本：`scripts/quality/hw_design_check.py`

**代码质量检查**
- 任务名称：`代码质量检查`
- 功能：静态代码分析和质量检查
- 参数：源码目录
- 脚本：`scripts/quality/code_check.py`

**生成测试用例**
- 任务名称：`生成测试用例`
- 功能：从需求生成测试用例
- 参数：需求目录
- 脚本：`scripts/quality/generate_test_cases.py`

**执行自动化测试**
- 任务名称：`执行自动化测试`
- 功能：运行自动化测试套件
- 参数：项目路径
- 脚本：`scripts/quality/run_tests.py`

**UI自动化测试**
- 任务名称：`UI自动化测试`
- 功能：执行UI自动化测试
- 参数：应用程序路径
- 脚本：`scripts/quality/ui_test.py`

### 生产管理任务

**生成BOM清单**
- 任务名称：`生成BOM清单`
- 功能：从硬件项目生成BOM清单
- 参数：硬件项目路径
- 脚本：`scripts/production/generate_bom.py`

**生成生产测试流程**
- 任务名称：`生成生产测试流程`
- 功能：生成生产测试程序
- 参数：产品信息文件
- 脚本：`scripts/production/generate_test_procedure.py`

### 发布管理任务

**构建发布版本**
- 任务名称：`构建发布版本`
- 功能：构建产品发布包
- 参数：版本号、项目路径
- 脚本：`scripts/release/build_release.py`

**生成发布文档**
- 任务名称：`生成发布文档`
- 功能：生成版本发布说明
- 参数：版本号
- 脚本：`scripts/doc_generation/generate_release_notes.py`

### 配置管理任务

**生成所有配置**
- 任务名称：`生成所有配置`
- 功能：生成项目所需的所有配置文件
- 参数：项目路径、项目类型
- 脚本：`scripts/config/generate_all_configs.py`

**生成工作流配置**
- 任务名称：`生成工作流配置`
- 功能：生成工作流配置文件
- 参数：项目路径、项目类型
- 脚本：`scripts/config/generate_workflow_config.py`

### 文档管理任务

**文档关联处理**
- 任务名称：`文档关联处理`
- 功能：自动关联和注册文档
- 参数：无（处理所有文档）
- 脚本：`scripts/links/auto_link_documents.py`

**Canvas同步**
- 任务名称：`Canvas同步`
- 功能：同步INDEX文件到Canvas
- 参数：无
- 脚本：`scripts/canvas/auto_link_documents.py`

**快速可视化**
- 任务名称：`快速可视化`
- 功能：生成项目可视化界面
- 参数：项目路径
- 脚本：`scripts/visualization/quickviz.py`

**信息追溯管理**
- 任务名称：`信息追溯管理`
- 功能：扫描和管理信息追溯
- 参数：无（扫描所有）
- 脚本：`scripts/infoTrace/auto_index_manager.py`

## 配置说明

### 任务配置结构

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "任务名称",
      "type": "shell",
      "command": "python",
      "args": [
        "${workspaceFolder}/build-tools/scripts/脚本路径",
        "--参数名=${input:参数输入ID}"
      ],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    }
  ],
  "inputs": [
    {
      "id": "参数输入ID",
      "description": "参数描述",
      "default": "默认值",
      "type": "promptString"
    }
  ]
}
```

### 环境变量配置

在VSCode设置中配置环境变量：

```json
{
  "terminal.integrated.env.windows": {
    "SCRIPTS_PATH": "${workspaceFolder}/build-tools/scripts",
    "PYTHON_PATH": "python"
  }
}
```

### 参数输入类型

- `promptString` - 字符串输入
- `pickString` - 选择列表
- `promptFile` - 文件选择
- `promptFolder` - 文件夹选择

## 最佳实践

### 任务组织

- 按功能领域分组任务
- 使用清晰的任务名称
- 提供参数输入提示
- 配置合适的输出格式

### 错误处理

- 配置问题匹配器
- 设置合适的工作目录
- 处理脚本执行错误
- 提供用户友好的错误信息

### 性能优化

- 避免重复的环境设置
- 使用共享的输出面板
- 缓存常用的输入值
- 并行执行独立任务

详细配置说明请参考 `naming_conventions.md`。
