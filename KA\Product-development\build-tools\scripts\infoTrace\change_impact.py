#!/usr/bin/env python3
"""
变更影响分析工具 - 分析文档变更的影响范围
用法: python change_impact.py --input="docs/" --change="changed_file.md" --output="impact_report.md"
"""

import os
import sys
import argparse
import json
import re
from pathlib import Path
import networkx as nx
import matplotlib.pyplot as plt
from datetime import datetime

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="变更影响分析工具")
    parser.add_argument('--input', required=True, help="文档目录路径")
    parser.add_argument('--change', required=True, help="变更文件路径")
    parser.add_argument('--output', default="impact_report.md", help="输出报告路径")
    parser.add_argument('--format', default="markdown", choices=["markdown", "html", "graph"], help="输出格式")
    parser.add_argument('--depth', type=int, default=3, help="影响分析深度")
    parser.add_argument('--show-details', action='store_true', help="显示详细信息")
    return parser.parse_args()

def find_documents(input_dir):
    """查找所有文档文件"""
    doc_files = []
    for ext in ['.md', '.txt', '.rst']:
        doc_files.extend(list(Path(input_dir).rglob(f'*{ext}')))
    return doc_files

def extract_links(content):
    """提取文档中的链接"""
    # Markdown链接模式
    md_links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
    # Wiki链接模式
    wiki_links = re.findall(r'\[\[([^\]]+)\]\]', content)
    return md_links, wiki_links

def build_document_graph(doc_files):
    """构建文档关联图"""
    G = nx.DiGraph()
    
    for doc_file in doc_files:
        try:
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加文档节点
            doc_name = doc_file.stem
            G.add_node(doc_name, path=str(doc_file))
            
            # 提取链接
            md_links, wiki_links = extract_links(content)
            
            # 添加Markdown链接
            for link_text, link_url in md_links:
                target = Path(link_url).stem
                if G.has_node(target):
                    G.add_edge(doc_name, target, type='md', text=link_text)
            
            # 添加Wiki链接
            for link in wiki_links:
                target = link.split('|')[0].strip()
                if G.has_node(target):
                    G.add_edge(doc_name, target, type='wiki')
        
        except Exception as e:
            print(f"警告: 处理文件 {doc_file} 时出错: {str(e)}")
    
    return G

def analyze_impact(G, changed_file, depth=3):
    """分析变更影响"""
    changed_name = Path(changed_file).stem
    
    if not G.has_node(changed_name):
        print(f"错误: 变更文件 {changed_file} 不在文档图中")
        sys.exit(1)
    
    # 使用BFS查找所有受影响的节点
    affected_nodes = set()
    queue = [(changed_name, 0)]  # (节点, 深度)
    visited = {changed_name}
    
    while queue:
        node, current_depth = queue.pop(0)
        affected_nodes.add(node)
        
        if current_depth >= depth:
            continue
        
        # 检查所有邻居节点
        for neighbor in G.neighbors(node):
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append((neighbor, current_depth + 1))
    
    # 构建影响分析结果
    impact_analysis = {
        'changed_file': changed_name,
        'affected_files': [],
        'impact_levels': {
            'direct': [],
            'indirect': []
        },
        'statistics': {
            'total_affected': len(affected_nodes),
            'direct_impact': 0,
            'indirect_impact': 0
        }
    }
    
    # 分析每个受影响节点
    for node in affected_nodes:
        if node == changed_name:
            continue
        
        # 计算到变更节点的最短路径
        try:
            path = nx.shortest_path(G, changed_name, node)
            impact_level = 'direct' if len(path) == 2 else 'indirect'
            
            node_data = {
                'name': node,
                'path': G.nodes[node]['path'],
                'impact_level': impact_level,
                'path_length': len(path) - 1,
                'path': path
            }
            
            impact_analysis['affected_files'].append(node_data)
            impact_analysis['impact_levels'][impact_level].append(node)
            
            if impact_level == 'direct':
                impact_analysis['statistics']['direct_impact'] += 1
            else:
                impact_analysis['statistics']['indirect_impact'] += 1
        
        except nx.NetworkXNoPath:
            print(f"警告: 无法找到从 {changed_name} 到 {node} 的路径")
    
    return impact_analysis

def generate_report(impact_analysis, output_path, format_type, show_details=False):
    """生成影响分析报告"""
    if format_type == "markdown":
        generate_markdown_report(impact_analysis, output_path, show_details)
    elif format_type == "html":
        generate_html_report(impact_analysis, output_path, show_details)
    elif format_type == "graph":
        generate_graph_visualization(impact_analysis, output_path, show_details)

def generate_markdown_report(impact_analysis, output_path, show_details):
    """生成Markdown格式报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        # 写入标题
        f.write("# 变更影响分析报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 写入变更文件信息
        f.write(f"## 变更文件\n\n")
        f.write(f"- 文件名: {impact_analysis['changed_file']}\n\n")
        
        # 写入统计信息
        f.write("## 影响统计\n\n")
        stats = impact_analysis['statistics']
        f.write(f"- 总受影响文件数: {stats['total_affected']}\n")
        f.write(f"- 直接影响文件数: {stats['direct_impact']}\n")
        f.write(f"- 间接影响文件数: {stats['indirect_impact']}\n\n")
        
        # 写入直接影响文件
        f.write("## 直接影响文件\n\n")
        for file in impact_analysis['impact_levels']['direct']:
            f.write(f"- {file}\n")
        f.write("\n")
        
        # 写入间接影响文件
        f.write("## 间接影响文件\n\n")
        for file in impact_analysis['impact_levels']['indirect']:
            f.write(f"- {file}\n")
        f.write("\n")
        
        # 如果显示详细信息，写入详细影响路径
        if show_details:
            f.write("## 详细影响路径\n\n")
            for file in impact_analysis['affected_files']:
                f.write(f"### {file['name']}\n\n")
                f.write(f"- 影响级别: {file['impact_level']}\n")
                f.write(f"- 路径长度: {file['path_length']}\n")
                f.write("- 影响路径:\n")
                for node in file['path']:
                    f.write(f"  - {node}\n")
                f.write("\n")

def generate_html_report(impact_analysis, output_path, show_details):
    """生成HTML格式报告"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>变更影响分析报告</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .stats { background-color: #f5f5f5; padding: 20px; border-radius: 5px; }
            .file { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
            .direct { border-left: 4px solid #4CAF50; }
            .indirect { border-left: 4px solid #FFC107; }
        </style>
    </head>
    <body>
        <h1>变更影响分析报告</h1>
        <p>生成时间: {timestamp}</p>
        
        <h2>变更文件</h2>
        <p>{changed_file}</p>
        
        <div class="stats">
            <h2>影响统计</h2>
            <p>总受影响文件数: {total_affected}</p>
            <p>直接影响文件数: {direct_impact}</p>
            <p>间接影响文件数: {indirect_impact}</p>
        </div>
        
        <h2>直接影响文件</h2>
        <ul>
    """.format(
        timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        changed_file=impact_analysis['changed_file'],
        total_affected=impact_analysis['statistics']['total_affected'],
        direct_impact=impact_analysis['statistics']['direct_impact'],
        indirect_impact=impact_analysis['statistics']['indirect_impact']
    )
    
    for file in impact_analysis['impact_levels']['direct']:
        html_content += f"<li>{file}</li>"
    
    html_content += """
        </ul>
        
        <h2>间接影响文件</h2>
        <ul>
    """
    
    for file in impact_analysis['impact_levels']['indirect']:
        html_content += f"<li>{file}</li>"
    
    html_content += """
        </ul>
    """
    
    if show_details:
        html_content += """
        <h2>详细影响路径</h2>
        """
        
        for file in impact_analysis['affected_files']:
            html_content += f"""
            <div class="file {file['impact_level']}">
                <h3>{file['name']}</h3>
                <p>影响级别: {file['impact_level']}</p>
                <p>路径长度: {file['path_length']}</p>
                <p>影响路径:</p>
                <ul>
            """
            
            for node in file['path']:
                html_content += f"<li>{node}</li>"
            
            html_content += """
                </ul>
            </div>
            """
    
    html_content += """
    </body>
    </html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

def generate_graph_visualization(impact_analysis, output_path, show_details):
    """生成图形可视化"""
    G = nx.DiGraph()
    
    # 添加变更节点
    changed_name = impact_analysis['changed_file']
    G.add_node(changed_name, type='changed')
    
    # 添加受影响节点
    for file in impact_analysis['affected_files']:
        G.add_node(file['name'], type=file['impact_level'])
        G.add_edge(changed_name, file['name'])
    
    # 设置图形布局
    pos = nx.spring_layout(G)
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 绘制节点
    node_colors = []
    for node in G.nodes():
        if G.nodes[node]['type'] == 'changed':
            node_colors.append('red')
        elif G.nodes[node]['type'] == 'direct':
            node_colors.append('green')
        else:
            node_colors.append('yellow')
    
    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=500)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, edge_color='gray', arrows=True)
    
    # 绘制标签
    nx.draw_networkx_labels(G, pos)
    
    plt.title("变更影响关系图")
    plt.axis('off')
    
    # 保存图形
    plt.savefig(output_path, format='png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    args = parse_arguments()
    
    # 查找文档文件
    doc_files = find_documents(args.input)
    if not doc_files:
        print(f"错误: 在目录 {args.input} 中未找到文档文件")
        sys.exit(1)
    
    # 构建文档关联图
    G = build_document_graph(doc_files)
    
    # 分析变更影响
    impact_analysis = analyze_impact(G, args.change, args.depth)
    
    # 确保输出目录存在
    output_path = Path(args.output)
    output_dir = output_path.parent
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成报告
    generate_report(impact_analysis, str(output_path), args.format, args.show_details)
    
    print(f"成功: 已生成变更影响分析报告 {args.output}")

if __name__ == "__main__":
    main() 