#ifndef IMAGEPROCESSING_FILTERFACTORY_H
#define IMAGEPROCESSING_FILTERFACTORY_H

#include "../filters/ConvolutionFilter.h"
#include "../filters/KalmanFilter.h"
#include "../filters/WeightedAverageFilter.h"
#include "../interfaces/IImageFilter.h"
#include <QDebug>
#include <QMap>
#include <functional>

namespace ImageProcessing {

/**
 * @brief 滤波器工厂实现
 *
 * 实现滤波器的创建和管理，遵循工厂模式
 */
class FilterFactory : public IFilterFactory {
  public:
    /**
     * @brief 获取工厂单例实例
     * @return 工厂实例引用
     */
    static FilterFactory &getInstance();

    /**
     * @brief 析构函数
     */
    ~FilterFactory() override = default;

    // IFilterFactory接口实现
    std::unique_ptr<IImageFilter> createFilter(FilterType type) override;
    QVector<FilterType>           getSupportedTypes() const override;
    QString                       getTypeDescription(FilterType type) const override;

    /**
     * @brief 注册自定义滤波器
     * @param type 滤波器类型
     * @param creator 创建函数
     */
    void registerFilter(FilterType type, std::function<std::unique_ptr<IImageFilter>()> creator);

    /**
     * @brief 检查是否支持指定类型
     * @param type 滤波器类型
     * @return true if supported, false otherwise
     */
    bool isSupported(FilterType type) const;

    /**
     * @brief 获取工厂版本
     * @return 版本字符串
     */
    QString getVersion() const;

    /**
     * @brief 创建预配置的滤波器
     * @param type 滤波器类型
     * @param preset 预设名称
     * @return 配置好的滤波器实例
     */
    std::unique_ptr<IImageFilter> createPresetFilter(FilterType type, const QString &preset);

    /**
     * @brief 获取支持的预设列表
     * @param type 滤波器类型
     * @return 预设名称列表
     */
    QStringList getSupportedPresets(FilterType type) const;

  private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    FilterFactory();

    /**
     * @brief 初始化默认滤波器
     */
    void initializeDefaultFilters();

    /**
     * @brief 配置预设参数
     * @param filter 滤波器实例
     * @param type 滤波器类型
     * @param preset 预设名称
     */
    void applyPresetConfiguration(IImageFilter *filter, FilterType type, const QString &preset);

    /**
     * @brief 记录调试信息
     * @param message 调试信息
     */
    void logDebug(const QString &message) const;

    // 滤波器创建函数映射
    QMap<FilterType, std::function<std::unique_ptr<IImageFilter>()>> creators_;
};

}  // namespace ImageProcessing

#endif  // IMAGEPROCESSING_FILTERFACTORY_H
