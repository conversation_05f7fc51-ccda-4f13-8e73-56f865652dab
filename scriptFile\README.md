# CHANGELOG to Manual Synchronization

This folder contains scripts to synchronize CHANGELOG.md files to the corresponding manual files in the build/MinSizeRel/output/bin/manual directory.

## Scripts

1. **sync_changelog_to_manual.py**
   - Automatically syncs new CHANGELOG entries to manual files
   - Updates only entries newer than the last sync date
   - Called automatically by the commit process

2. **manual_sync_changelog.py**
   - Run this script manually to force a complete sync of all CHANGELOG entries
   - Useful for initial setup or if you want to rebuild all manual files

## Usage

### Automatic Sync

The sync happens automatically when you make a git commit. The `check_commit_msg.py` script calls `sync_changelog_to_manual.py` after updating the CHANGELOG files.

### Manual Sync

To manually synchronize all CHANGELOG entries to the manual files:

Windows:

```
python scriptFile/manual_sync_changelog.py
```

Linux/macOS:

```
python3 scriptFile/manual_sync_changelog.py
```

## File Structure

Each component has a CHANGELOG.md file in its directory:

- components/lensAdjust/CHANGELOG.md
- components/lensAdjust_MEMD/CHANGELOG.md
- components/lensAdjust_rework/CHANGELOG.md
- etc.

The manual files are created or updated in:

- build/MinSizeRel/output/bin/manual/lensAdjust/lensAdjust_manual.md
- build/MinSizeRel/output/bin/manual/lensAdjust_MEMD/lensAdjust_MEMD_manual.md
- build/MinSizeRel/output/bin/manual/lensAdjust_rework/lensAdjust_rework_manual.md
- etc.

## Configuration

The last sync date is stored in `scriptFile/config.ini` in the [SYNC] section. If you delete this file or set the environment variable FORCE_SYNC=1, all CHANGELOG entries will be synced.
