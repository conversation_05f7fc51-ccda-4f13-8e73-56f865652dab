#!/usr/bin/env python3
"""
产品开发完整功能集成MCP服务器
整合所有核心功能的统一MCP服务器实现
遵循脚本包装模式和MCP协议标准
使用业务流程模块化架构
"""

import sys
import os
import logging
from pathlib import Path

# 设置日志 - 所有日志输出到stderr和文件，让MCP框架管理stdout
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_server.log', encoding='utf-8'),  # 文件日志
        logging.StreamHandler(sys.stderr)  # stderr日志，不干扰MCP的stdout通信
    ]
)
logger = logging.getLogger(__name__)

# 设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    try:
        os.environ['PYTHONIOENCODING'] = 'utf-8'
    except Exception as e:
        logger.error(f"Failed to set PYTHONIOENCODING: {e}")

# 添加tools目录到Python路径
tools_path = Path(__file__).parent.parent.parent / "tools"
sys.path.insert(0, str(tools_path))

# 强制设置PYTHONPATH以确保脚本能正确导入模块
scripts_path = Path(__file__).parent.parent.parent.parent / "scripts"
if scripts_path.exists():
    scripts_path_str = str(scripts_path)
    if 'PYTHONPATH' not in os.environ or not os.environ['PYTHONPATH']:
        os.environ['PYTHONPATH'] = scripts_path_str
        sys.path.insert(0, scripts_path_str)
        logger.info(f"设置PYTHONPATH: {scripts_path_str}")
    else:
        logger.info(f"PYTHONPATH已设置: {os.environ['PYTHONPATH']}")
else:
    logger.warning(f"Scripts路径不存在: {scripts_path}")

# 尝试不同的FastMCP导入方式
try:
    from fastmcp import FastMCP
    logger.info("✅ 使用独立的 fastmcp 包")
except ImportError:
    try:
        from mcp.server.fastmcp import FastMCP
        logger.info("✅ 使用 MCP SDK 内置的 FastMCP")
    except ImportError:
        logger.error("❌ 无法导入 FastMCP，请安装 fastmcp 包: pip install fastmcp")
        raise ImportError("FastMCP 模块不可用")

# 创建MCP服务器实例
mcp = FastMCP("Product Development Complete")

# 导入业务流程模块
try:
    import project_lifecycle
    import requirements_flow
    import development_flow
    import document_flow
    import config_flow

    # 注册项目生命周期工具 (T06, T27)
    mcp.tool()(project_lifecycle.get_project_info)
    mcp.tool()(project_lifecycle.init_project)

    # 注册需求流程工具 (T01, T03, T04, T05, T09)
    mcp.tool()(requirements_flow.import_requirements)
    mcp.tool()(requirements_flow.analyze_requirements)
    mcp.tool()(requirements_flow.create_requirements_matrix)
    mcp.tool()(requirements_flow.req_to_tasks)

    # 注册开发流程工具 (T15, T16, T22)
    mcp.tool()(development_flow.create_project_dashboard)
    mcp.tool()(development_flow.analyze_code_quality)
    mcp.tool()(development_flow.generate_document_report)

    # 注册文档流程工具 (T23, T24, T25, T26)
    mcp.tool()(document_flow.link_documents)
    mcp.tool()(document_flow.sync_canvas)
    mcp.tool()(document_flow.manage_trace)
    mcp.tool()(document_flow.create_flow_chart)

    # 注册配置流程工具 (T28相关6个工具)
    mcp.tool()(config_flow.load_config)
    mcp.tool()(config_flow.save_config)
    mcp.tool()(config_flow.validate_config)
    mcp.tool()(config_flow.merge_configs)
    mcp.tool()(config_flow.create_config_template)
    mcp.tool()(config_flow.backup_config)

except ImportError as e:
    logger.error(f"警告: 无法导入业务流程模块: {e}")
    logger.error("将使用内置工具定义")


if __name__ == "__main__":
    mcp.run(transport="stdio")
