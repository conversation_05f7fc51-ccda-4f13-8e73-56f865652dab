/*******************************
 * sensor、镜片调节设备交互
 *******************/

#include "topBoardSerial.h"
#include <QThread>

CTopBoardSerial::CTopBoardSerial(QObject *parent, ITopBoard *top_board_):
  QObject(parent)
, m_task_id(0)
, mc_top_board_(top_board_)
{

}

CTopBoardSerial::~CTopBoardSerial()
{
  //if(mc_sensor_ != nullptr) delete mc_sensor_;
  //if(mc_machine_ != nullptr) delete mc_machine_;
}

void CTopBoardSerial::loop(int task_id)
{
    Q_UNUSED(task_id);
    QByteArray arr;
    QByteArray device_arr;
    for(;;) {
        if(m_task_id == 1) { //接收sensor数据
            arr = mc_top_board_->portDataRead();
            if(arr.length() > 0) mc_top_board_->greyParsing(arr, arr.length());
        }
        else if(m_task_id == 3) {
            arr = mc_top_board_->portDataRead();
            if(arr.length() > 0) mc_top_board_->interactionParsing(arr, 100);
        }
        else if(m_task_id == 4) {
            //QThread::msleep(1);
        }
        else if(m_task_id == 5) {//空
            QThread::msleep(5);
        }
        else if(m_task_id == 0) {//退出线程 //不能用else，时序上存在问题

            break; //return
        }
    }
}

void CTopBoardSerial::device_change_interface(ITopBoard* top_board_)
{
  if(top_board_ != nullptr) {
      mc_top_board_ = top_board_;
      mc_top_board_->m_strPre.clear();
    }
}

void CTopBoardSerial::task_id_change(const uint8_t &id)
{
    m_task_id = id;
//    if(id == 1);
//        mc_sensor_->m_strPre.clear();
//    else if(id == 2)

    if(mc_top_board_->m_strPre.length() > 0) mc_top_board_->m_strPre.clear();

    qDebug() << "-i len thread/ task id:" << m_task_id;
}
