/** 通讯接口类: 包括串口、T<PERSON>、DUP、IIC、SPI
  * @lj 2022-10-18
  *
  */

#ifndef _ICOMM_H_
#define _ICOMM_H_

#include <QByteArray>
#include <QStringList>
#include <QMetaType>

#if 0
typedef struct{
  struct {
    QString port_name;
    QString last_port_name;
    uint baud;
  } StSerialInfo; //端口信息

  struct {

  } StI2cInfo;

  struct {

  } StNetwork;
} StPortInfo;
#endif

//* 通信状态
enum ECommStatus
{
    eCOMM_NONE = 0, //
    eCOMM_OK,       //
    eCOMM_BUSY,     //
    eCOMM_ERROR,
    eCOMM_TIMEOUT,
    eCOMM_FATAL,
    eCOMM_COMP,
};

typedef struct {
    uint16_t comm_time;
    ECommStatus comm_status;
} StCommunicateStatus;

class IComm{
public:
  IComm() {
      qRegisterMetaType<ECommStatus>("ECommStatus");
  };
  virtual ~IComm(){};


//  StPortInfo st_port_info;

  //* 端口列表扫描
  virtual QStringList detectPort() = 0;
  virtual bool scanPort(QStringList *port_list_, const QString &last_port_name) = 0;
  virtual QString getPortName() = 0;

  //* 端口开关
  virtual bool openPort() = 0;
  virtual void closePort() = 0;
  virtual bool checkPort() = 0;

  //* 输出传输
  virtual QByteArray read(uint16_t wait_ms) = 0;
  virtual void setBufferSize(uint16_t bytes) = 0;
  virtual bool write(QByteArray data) = 0;

protected:
    QString m_port_name;
};


#endif
