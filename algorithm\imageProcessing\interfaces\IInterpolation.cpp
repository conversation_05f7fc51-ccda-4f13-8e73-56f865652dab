#include "IInterpolation.h"
#include "../common/ValidationUtils.h"
#include <QDebug>
#include <QtMath>

namespace ImageProcessing {

// InterpolationParams 实现
void InterpolationParams::validate() const {
    ValidationUtils::validateRange(offset, 0.0f, 1.0f, "offset");
    if (minValue > maxValue) {
        throw InvalidParameterException("minValue", "must be less than or equal to maxValue");
    }
}

void InterpolationParams::reset() {
    offset = 0.5f;
    preserveEdges = true;
    clampValues = true;
    minValue = 0;
    maxValue = UINT32_MAX;
}

bool InterpolationParams::operator==(const InterpolationParams& other) const {
    return qFuzzyCompare(offset, other.offset) &&
           preserveEdges == other.preserveEdges &&
           clampValues == other.clampValues &&
           minValue == other.minValue &&
           maxValue == other.maxValue;
}

bool InterpolationParams::operator!=(const InterpolationParams& other) const {
    return !(*this == other);
}

QString InterpolationParams::toString() const {
    return QString("InterpolationParams{offset=%1, preserveEdges=%2, clampValues=%3, minValue=%4, maxValue=%5}")
           .arg(offset)
           .arg(preserveEdges ? "true" : "false")
           .arg(clampValues ? "true" : "false")
           .arg(minValue)
           .arg(maxValue);
}

// IInterpolation 基类实现
void IInterpolation::validateInputs(const ImageDataU32& src, const ImageDataU32& dst) const {
    ValidationUtils::validateImageData(src, "source image");
    ValidationUtils::validateImageData(dst, "destination image");
}

uint32_t IInterpolation::clampValue(uint32_t value, uint32_t minVal, uint32_t maxVal) const {
    if (value < minVal) return minVal;
    if (value > maxVal) return maxVal;
    return value;
}

uint32_t IInterpolation::safeFloatToUint32(float value) const {
    if (value < 0.0f) return 0;
    if (value > static_cast<float>(UINT32_MAX)) return UINT32_MAX;
    return static_cast<uint32_t>(value + 0.5f); // 四舍五入
}

} // namespace ImageProcessing
