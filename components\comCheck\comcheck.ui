<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class><PERSON>omCheck</class>
 <widget class="QMainWindow" name="CComCheck">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>761</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>comCheck_V1.1</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDockWidget
{
	background-color: rgb(255,255,255);
	
	font: 12pt &quot;黑体&quot;;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 6px;
}

QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}

QPushButton{
	min-width: 150px;
	min-height: 25px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}

QLineEdit
{
	/*min-width: 150px;*/
	min-height: 25px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	min-height: 25px;
    max-height: 25px;

	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
QCustomPlot{
	/*min-width: 1200px;
	min-height: 1000px;*/
}
#Result{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	/*min-width: 230px;
	min-height:150px;*/

	text-align: left top;
    padding-left: 60px;
    padding-top: 2px;
	font: 30pt &quot;黑体&quot;;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_4">
    <item row="0" column="1" colspan="2">
     <widget class="QScrollArea" name="cmdScrollArea">
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <widget class="QWidget" name="scrollAreaWidgetContents">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>275</width>
         <height>382</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="4">
           <widget class="QLabel" name="label_6">
            <property name="text">
             <string>间隔/ms</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>HEX</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>发送</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="label_3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>指令</string>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QLabel" name="label_5">
            <property name="text">
             <string>顺序</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item row="4" column="0" colspan="2">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="device_label">
        <property name="text">
         <string>device</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="device_select"/>
      </item>
      <item>
       <widget class="QLabel" name="port_label">
        <property name="text">
         <string>port</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="port_select"/>
      </item>
      <item>
       <widget class="QLabel" name="baud_label">
        <property name="text">
         <string>baud</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="baud_select">
        <item>
         <property name="text">
          <string>9600</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>19200</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>115200</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>150000</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>230400</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>600000</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </item>
    <item row="0" column="0">
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="1" column="0">
       <widget class="QLabel" name="label_10">
        <property name="text">
         <string>result</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>interaction Log</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QTextEdit" name="result"/>
      </item>
      <item row="0" column="1">
       <widget class="QTextEdit" name="interactionLog"/>
      </item>
     </layout>
    </item>
    <item row="2" column="0" colspan="3">
     <widget class="QTableWidget" name="CProtocolView"/>
    </item>
    <item row="4" column="2">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QCheckBox" name="whileCheckBox">
        <property name="text">
         <string>循环发送</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="start_btn">
        <property name="text">
         <string>start</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="1" column="1">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="3" column="0">
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1138</width>
     <height>25</height>
    </rect>
   </property>
   <widget class="QMenu" name="menucomCheck_V1_1">
    <property name="title">
     <string>file</string>
    </property>
   </widget>
   <addaction name="menucomCheck_V1_1"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="windowTitle">
    <string>toolBar_2</string>
   </property>
   <attribute name="toolBarArea">
    <enum>LeftToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="separator"/>
   <addaction name="ActionRecord"/>
   <addaction name="actionImport"/>
   <addaction name="ActionClean"/>
  </widget>
  <action name="ActionClean">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../../resource.qrc">
     <normaloff>:/icon/w_b/clean.png</normaloff>:/icon/w_b/clean.png</iconset>
   </property>
   <property name="text">
    <string>clean</string>
   </property>
  </action>
  <action name="ActionRecord">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../../resource.qrc">
     <normaloff>:/icon/w_b/record-2.jpg</normaloff>:/icon/w_b/record-2.jpg</iconset>
   </property>
   <property name="text">
    <string>record</string>
   </property>
  </action>
  <action name="actionImport">
   <property name="icon">
    <iconset resource="../../resource.qrc">
     <normaloff>:/icon/w_b/import.png</normaloff>:/icon/w_b/import.png</iconset>
   </property>
   <property name="text">
    <string>import</string>
   </property>
   <property name="toolTip">
    <string>import</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="../../resource.qrc"/>
 </resources>
 <connections/>
</ui>
