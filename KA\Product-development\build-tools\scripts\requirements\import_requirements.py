#!/usr/bin/env python3
"""
需求导入工具 - 从Excel、Word或其他格式导入需求文档
用法: python import_requirements.py --source="需求源文件" --type="需求类型"
"""

import os
import sys
import argparse
import json
import csv
import datetime
import re
import shutil
from pathlib import Path

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("警告: pandas未安装，Excel导入功能受限。建议运行 'pip install pandas openpyxl' 安装依赖")

try:
    import docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("警告: python-docx未安装，Word导入功能受限。建议运行 'pip install python-docx' 安装依赖")

# 需求类型定义
REQUIREMENT_TYPES = {
    "market": "market_requirements",
    "technical": "technical_requirements",
    "functional": "technical_requirements/functional",
    "performance": "technical_requirements/performance",
    "interface": "technical_requirements/interface",
    "usability": "technical_requirements/usability",
    "reliability": "technical_requirements/reliability",
    "security": "technical_requirements/security"
}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="导入需求文档")
    parser.add_argument('--source', required=True, help="需求源文件路径")
    parser.add_argument('--type', required=True, choices=REQUIREMENT_TYPES.keys(), help="需求类型")
    parser.add_argument('--output', default="requirements", help="输出目录")
    parser.add_argument('--format', choices=['markdown', 'json'], default='markdown', help="输出格式")
    parser.add_argument('--project', help="项目名称")
    return parser.parse_args()

def import_from_excel(source_file, req_type):
    """从Excel文件导入需求"""
    if not PANDAS_AVAILABLE:
        print("错误: 导入Excel需要安装pandas和openpyxl")
        return None
    
    try:
        # 读取Excel文件
        df = pd.read_excel(source_file)
        
        # 检查必要列是否存在
        required_cols = ['ID', '需求描述']
        for col in required_cols:
            if col not in df.columns:
                print(f"错误: Excel文件缺少必要列 '{col}'")
                return None
        
        # 转换为需求列表
        requirements = []
        for _, row in df.iterrows():
            req = {
                'id': str(row['ID']),
                'description': row['需求描述'],
                'type': req_type,
                'priority': row.get('优先级', 'Medium'),
                'status': row.get('状态', 'New'),
                'source': os.path.basename(source_file)
            }
            
            # 添加可选字段
            for col in df.columns:
                if col not in ['ID', '需求描述', '优先级', '状态']:
                    req[col.lower().replace(' ', '_')] = str(row[col])
            
            requirements.append(req)
        
        return requirements
    
    except Exception as e:
        print(f"导入Excel文件时出错: {e}")
        return None

def import_from_csv(source_file, req_type):
    """从CSV文件导入需求"""
    try:
        requirements = []
        with open(source_file, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            # 检查必要列是否存在
            header = reader.fieldnames
            if 'ID' not in header or '需求描述' not in header:
                print(f"错误: CSV文件缺少必要列 'ID' 或 '需求描述'")
                return None
            
            for row in reader:
                req = {
                    'id': row['ID'],
                    'description': row['需求描述'],
                    'type': req_type,
                    'priority': row.get('优先级', 'Medium'),
                    'status': row.get('状态', 'New'),
                    'source': os.path.basename(source_file)
                }
                
                # 添加可选字段
                for col in row:
                    if col not in ['ID', '需求描述', '优先级', '状态']:
                        req[col.lower().replace(' ', '_')] = row[col]
                
                requirements.append(req)
        
        return requirements
    
    except Exception as e:
        print(f"导入CSV文件时出错: {e}")
        return None

def import_from_docx(source_file, req_type):
    """从Word文件导入需求"""
    if not DOCX_AVAILABLE:
        print("错误: 导入Word需要安装python-docx")
        return None
    
    try:
        doc = docx.Document(source_file)
        requirements = []
        current_req = None
        
        # 尝试从Word文档中提取需求
        # 假设格式为 "REQ-001: 需求描述"
        req_pattern = re.compile(r'^(REQ-\d+|[A-Z]+-\d+):\s*(.+)$')
        
        for para in doc.paragraphs:
            text = para.text.strip()
            if not text:
                continue
            
            match = req_pattern.match(text)
            if match:
                if current_req:
                    requirements.append(current_req)
                
                req_id, desc = match.groups()
                current_req = {
                    'id': req_id,
                    'description': desc,
                    'type': req_type,
                    'priority': 'Medium',
                    'status': 'New',
                    'source': os.path.basename(source_file),
                    'details': []
                }
            elif current_req:
                current_req['details'].append(text)
        
        # 添加最后一个需求
        if current_req:
            requirements.append(current_req)
        
        # 处理需求详情
        for req in requirements:
            if req['details']:
                req['details'] = '\n'.join(req['details'])
            else:
                req.pop('details', None)
        
        return requirements
    
    except Exception as e:
        print(f"导入Word文件时出错: {e}")
        return None

def import_from_markdown(source_file, req_type):
    """从Markdown文件导入需求"""
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        requirements = []
        lines = content.split('\n')
        
        # 假设格式为 "## REQ-001: 需求描述" 或 "- REQ-001: 需求描述"
        req_pattern = re.compile(r'^(#{1,6}|-)\s*(REQ-\d+|[A-Z]+-\d+):\s*(.+)$')
        
        current_req = None
        current_details = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            match = req_pattern.match(line)
            if match:
                if current_req:
                    if current_details:
                        current_req['details'] = '\n'.join(current_details)
                    requirements.append(current_req)
                    current_details = []
                
                _, req_id, desc = match.groups()
                current_req = {
                    'id': req_id,
                    'description': desc,
                    'type': req_type,
                    'priority': 'Medium',
                    'status': 'New',
                    'source': os.path.basename(source_file)
                }
            elif current_req and not line.startswith('#'):
                current_details.append(line)
        
        # 添加最后一个需求
        if current_req:
            if current_details:
                current_req['details'] = '\n'.join(current_details)
            requirements.append(current_req)
        
        return requirements
    
    except Exception as e:
        print(f"导入Markdown文件时出错: {e}")
        return None

def export_to_markdown(requirements, output_path, req_type, project_name=None):
    """将需求导出为Markdown格式"""
    try:
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # 创建输出目录
        os.makedirs(output_path, exist_ok=True)
        
        # 创建需求文档
        filename = f"{req_type}_requirements.md"
        filepath = os.path.join(output_path, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入标题
            if project_name:
                f.write(f"# {project_name} - {req_type.capitalize()} 需求\n\n")
            else:
                f.write(f"# {req_type.capitalize()} 需求\n\n")
            
            f.write(f"导入日期: {today}\n\n")
            f.write(f"源文件: {requirements[0]['source'] if requirements else 'N/A'}\n\n")
            
            # 写入需求表格
            f.write("## 需求列表\n\n")
            
            f.write("| ID | 描述 | 优先级 | 状态 |\n")
            f.write("|---|---|---|---|\n")
            
            for req in requirements:
                f.write(f"| {req['id']} | {req['description']} | {req['priority']} | {req['status']} |\n")
            
            f.write("\n## 需求详情\n\n")
            
            # 写入每个需求的详细信息
            for req in requirements:
                f.write(f"### {req['id']}: {req['description']}\n\n")
                
                f.write(f"- **优先级**: {req['priority']}\n")
                f.write(f"- **状态**: {req['status']}\n")
                
                # 写入其他属性
                for key, value in req.items():
                    if key not in ['id', 'description', 'type', 'priority', 'status', 'source', 'details']:
                        f.write(f"- **{key.replace('_', ' ').capitalize()}**: {value}\n")
                
                # 写入详细说明
                if 'details' in req and req['details']:
                    f.write("\n**详细说明**:\n\n")
                    f.write(req['details'])
                    f.write("\n\n")
                
                f.write("---\n\n")
        
        print(f"已将需求导出为Markdown格式: {filepath}")
        return filepath
    
    except Exception as e:
        print(f"导出Markdown文件时出错: {e}")
        return None

def export_to_json(requirements, output_path, req_type, project_name=None):
    """将需求导出为JSON格式"""
    try:
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # 创建输出目录
        os.makedirs(output_path, exist_ok=True)
        
        # 创建需求文档
        filename = f"{req_type}_requirements.json"
        filepath = os.path.join(output_path, filename)
        
        # 构建JSON数据
        data = {
            "project": project_name,
            "type": req_type,
            "date": today,
            "source": requirements[0]['source'] if requirements else 'N/A',
            "requirements": requirements
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"已将需求导出为JSON格式: {filepath}")
        return filepath
    
    except Exception as e:
        print(f"导出JSON文件时出错: {e}")
        return None

def create_traceability_matrix(requirements, output_dir, project_name=None):
    """创建需求追溯矩阵"""
    try:
        filepath = os.path.join(output_dir, "requirement_traceability.md")
        
        with open(filepath, 'w', encoding='utf-8') as f:
            # 写入标题
            if project_name:
                f.write(f"# {project_name} - 需求追溯矩阵\n\n")
            else:
                f.write("# 需求追溯矩阵\n\n")
            
            f.write("创建日期: " + datetime.datetime.now().strftime("%Y-%m-%d") + "\n\n")
            
            # 写入需求表格
            f.write("| 需求ID | 描述 | 类型 | 设计文档 | 测试用例 | 实现状态 |\n")
            f.write("|---|---|---|---|---|---|\n")
            
            for req in requirements:
                f.write(f"| {req['id']} | {req['description']} | {req['type']} | | | {req['status']} |\n")
        
        print(f"已创建需求追溯矩阵: {filepath}")
        return filepath
    
    except Exception as e:
        print(f"创建需求追溯矩阵时出错: {e}")
        return None

def main():
    """主函数"""
    args = parse_arguments()
    
    print(f"正在导入需求: {args.source}")
    print(f"需求类型: {args.type}")
    
    # 根据文件扩展名选择导入方法
    source_file = args.source
    file_ext = os.path.splitext(source_file)[1].lower()
    
    if file_ext == '.xlsx' or file_ext == '.xls':
        requirements = import_from_excel(source_file, args.type)
    elif file_ext == '.csv':
        requirements = import_from_csv(source_file, args.type)
    elif file_ext == '.docx' or file_ext == '.doc':
        requirements = import_from_docx(source_file, args.type)
    elif file_ext == '.md' or file_ext == '.markdown':
        requirements = import_from_markdown(source_file, args.type)
    else:
        print(f"不支持的文件格式: {file_ext}")
        return
    
    if not requirements:
        print("导入需求失败")
        return
    
    print(f"成功导入 {len(requirements)} 条需求")
    
    # 确定输出目录
    output_subdir = REQUIREMENT_TYPES[args.type]
    output_path = os.path.join(args.output, output_subdir)
    
    # 导出需求
    if args.format == 'markdown':
        result = export_to_markdown(requirements, output_path, args.type, args.project)
    else:
        result = export_to_json(requirements, output_path, args.type, args.project)
    
    if result:
        # 更新需求追溯矩阵
        create_traceability_matrix(requirements, os.path.join(args.output), args.project)
        
        print("需求导入完成!")
        print("下一步建议使用 'analyze_requirements.py' 对需求进行分析")

if __name__ == "__main__":
    main() 