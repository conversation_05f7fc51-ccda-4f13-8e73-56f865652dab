#include "clensadjust.h"

#include <QApplication>
#include <QStringList>

#include "qLog.h"


int main(int argc, char *argv[]) {
    QApplication a(argc, argv);
    cLensAdjust  w;

    // 初始化日志系统
    MyLogger::QLog::init();
    // MyLogger::QLog::setLogPath("logs/");

    //#ifndef LOG_TO_CONSOLE
    MyLogger::QLog::installMessageHandler();
    //#endif

    // 使用Qt调试函数
    // qDebug() << "LenAdjust Application started";

    // 使用日志宏
    // LOG_INFO(MyLogger::LogType::INIT, "Application initialized");

    // 使用组件日志
    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust started");

    w.show();
    return a.exec();
}
