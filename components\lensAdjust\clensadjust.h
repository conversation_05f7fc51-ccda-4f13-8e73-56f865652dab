#ifndef CLENSADJUST_H
#define CLENSADJUST_H

#include "clenAdjustOperation.h"
#include "lensAdjust.h"
#include "tableViewModule.h"
#include <QDockWidget>
#include <QImage>



namespace Ui {
class cLensAdjust;
}

class cLensAdjust : public QDockWidget {
    Q_OBJECT

  public:
    explicit cLensAdjust(QWidget *parent = nullptr);
    ~cLensAdjust();

  signals:
    void lensAdjustCloseSiganl(bool);
    void windowCloseSiganl(bool);

  private:
    typedef struct {
        enum pressBtnType  //手动模式下按下的是哪个按钮
        {
            None   = 0x0000,
            right  = 0x0001,
            up     = 0x0002,
            left   = 0x0004,
            down   = 0x0008,
            z_up   = 0x0010,
            z_down = 0x0020,

            center = 0x1000,
            inner  = 0x2000,
            middle = 0x4000,
            out    = 0x8000,
        };
    } ST_MASK;  //定义域限制

    ST_MASK::pressBtnType pressedBtn{ST_MASK::None};

    enum FACULA_TYPE {
        Cross,  //十字光斑
        Line,   //一字光斑
    };          //目标光斑模型

    Ui::cLensAdjust *  ui;
    NClen::StUiConfig *mst_config_         = nullptr;
    CTableViewModule * mc_auto_grey_map_   = nullptr;
    CTableViewModule * mc_manual_grey_map_ = nullptr;
    CTableViewModule * mc_expand_grey_map_ = nullptr;

    CLenAdjustOpt *mc_operation_ = nullptr;
    //  clenDataMes *mc_lens_data_mes_ = nullptr;
    bool          m_is_unnormal_mode;
    const QString process_normal_sheetStyle = "QTextEdit{"
                                              "min-width: 300px;"
                                              "min-height: 50px;"
                                              "font: 10pt;"  //'Agency FB'
                                              "border-radius: 6px;"
                                              "border:2px solid black;"
                                              "background: rgb(255,255,255);"
                                              "color: rgb(0, 0, 0);}"; /*浅黑*/

    const QString process_unnormal_sheetStyle = "QTextEdit{"
                                                "min-width: 300px;"
                                                "min-height: 50px;"
                                                "font: 10pt;"  // 'Agency FB'
                                                "border-radius: 6px;"
                                                "border:2px solid black;"
                                                "background: rgb(255,255,255);"
                                                "color: rgb(255, 0, 0);"; /*浅黑*/
    //  UStatusFlag *m_status_flag_;

    uint16_t m_move_multiple;

    QRectF       drawRect;  //整个大圆的范围
    QPainterPath fanShaped[24];
    QRectF       centerCircularRect;  //中心圆按钮的范围

    //* ui config
    void updateConfig(NClen::StUiConfig *config_);
    void tableInit();
    void modeTableChange();  // const IClensMachine::EMode &mode);
    //* 光斑显示
    //  bool cellNumUpdate(uint16_t);
    //  void greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data);

    //* 目标区域凸显
    //  void targetFaculaAreaShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_); //, StMapData *map_data

    //* 手动控制圆盘
    bool         isPointInCir(const QPoint &point, const QRect &rect);
    int          isPointInCirRing(const QPoint &point, const QRect &rect);  //判断点是否在圆环范围内
    QPainterPath gradientArc(double startAngle, double angleLength, double arcHeight);
    void         paintWidget();
    //  void faculaViewShow(const bool &show_view);

    //* 状态栏update
    void resultClean(void);
    void lenMesDataFB(bool closed);

  private slots:
    void portListShow(QStringList *port_list, bool port_flag);
    void devicePortListShow(QStringList *port_list_, bool port_flag);
    void readyShow(bool is_open);
    void startAckShow();
    //    void greyMapShowInit(QTableWidget *table_, const uint8_t &xlen, const uint8_t &ylen);
    void autoUnnormalModeShow(const bool &is_unnormal);
    void dataAckShow(const uint &max, const QVector<QVector<uint32_t>> &matrix, const uint &max2, const QVector<QVector<uint32_t>> &matrix2);

    void originCoordinateShow(const float &loc_x, const float &loc_y, const float &loc_z);
    void adjustedCoordinatShow(const float &loc_x, const float &loc_y, const float &loc_z);
    void movedLocShow(const int16_t &loc_x, const int16_t &loc_y, const int16_t &loc_z);

    void optInitErrorShow_slog(const QString &info);
    void processStatusShow(const int16_t &step, const int16_t &status);
    //  void processDdsShow(const int16_t &step, const QString &error);
    void processTaskInfoShow_slot(const bool &is_error, const QString &info);
    void resultShow(EResult result, const uint8_t &result_index);
    void productTestInfoShow(const bool &is_clean);
    void compAckShow(bool is_comp);
    //  void errorAckShow(const QString &error_tring);

  private slots:
    void on_serialOpen_clicked();

    void on_portBox_currentIndexChanged(int index);

    void on_devicePortBox_currentIndexChanged(int index);

    void on_modeBox_currentIndexChanged(int index);

    void on_pushButton_clicked();

    void on_pushButton_6_clicked();

    void on_faculaType_currentIndexChanged(int index);

    void on_pushButton_2_clicked();

    void on_pushButton_7_clicked();

    void on_mesDataInput_clicked();

    void on_manualAdjust_clicked();

    void on_manualSolid_clicked();

    void on_manualDeflate_clicked();

    void on_manualExit_clicked();

    void on_retest_clicked();

    void on_greyMode_clicked();

    void on_productionInfoClean_clicked();

    void on_coordinate_clicked();

protected:
    //    virtual void paintEvent(QPaintEvent *event)override;
    virtual bool eventFilter(QObject *watched, QEvent *event) override;
    virtual void mousePressEvent(QMouseEvent *event) override;
    virtual void mouseReleaseEvent(QMouseEvent *event) override;
    virtual void closeEvent(QCloseEvent *event) override;
};

#endif  // CLENSADJUST_H
