#ifndef MYWIDGET_H
#define MYWIDGET_H

#include <QWidget>
#include "motormonitorwidget.h"
#include "clensadjust.h"
//#include "turnOnTime.h"
#include "comcheck.h"
#include "novaCalibration.h"

#include <QSqlTableModel>
#include "myConnSql.h"
//#include "component.h"
//#include "myFactory.h"
#include "assistant.h"

QT_BEGIN_NAMESPACE
namespace Ui { class myWidget; }
QT_END_NAMESPACE

typedef struct
{
    bool isMotorMonitorRecFinish;
    bool isLensAdjustRecFinish;
    uint8_t isFirstArr;
}SYSTEM_SERIAL_FLAG;

typedef union{
    uint8_t allId;
    struct{
        bool id1    :1;
        bool id2    :1;
        bool id3    :1;
        bool id4    :1;
        bool id5    :1;
        bool id6    :1;
        bool id7    :1;
        bool id8    :1;
    };
}unionIDflag;

class Assistant;

class myWidget : public QWidget
{
    Q_OBJECT

public:
    myWidget(QWidget *parent = nullptr);
    ~myWidget();

private slots:

    void on_threadMotorMonitor_clicked();

    void on_Navigation_currentChanged(int index);

    void on_lightAdjustP_clicked();

    void motorMonitorFB(bool closed);

    void lensAdjustFB(bool closed);

    void turnOnTimeFB(bool closed);

    void comCheckFB(bool closed);

    void novaCalibFB(bool closed);

    void on_cellAdd_clicked();

    void on_cellDelete_clicked();

    void on_save_clicked();

    void on_cancle_clicked();

    void on_output_clicked();

    void on_tableName_textChanged(const QString &arg1);

    void on_dateBaseSelect_currentIndexChanged(const QString &arg1);

    void on_find_clicked();

    void on_dateBaseSelect_currentIndexChanged(int index);

    void on_turnOnTimeP_clicked();

    void on_comCheck_clicked();

    void on_novaCalibration_clicked();

private:
    typedef struct{
        QString             table_name;
        QSqlDatabase        db;
        QString             conn;
    } ST_DATABASE;

    Ui::myWidget *ui;
    uint8_t m_db_select;
    Assistant* myAssist_ptr_ = nullptr;
//    QToolBar* compsBoxToolBar = nullptr;
    int timerId,timeCnt100ms;

    //* functions
    motorMonitorWidget* motor_monitor_ptr_ = nullptr;
    cLensAdjust* lens_adjust_ptr_ = nullptr;
//    turnOnTime* turn_on_time_ = nullptr;
    CComCheck* mc_com_check_ = nullptr;
    CNovaCalibration* mc_nova_calib_ = nullptr;


    //CMyFactory* my_factory_ = nullptr;

    //CMyFactory::EComponent e_component_create;
    //CMyFactory::EComponent e_component_del;
    unionIDflag* timeId_ptr_ = nullptr;


    QSqlTableModel  *m_model_ = nullptr;
    ST_DATABASE     *m_tableView_database_ = nullptr;

    virtual void timerEvent(QTimerEvent *event) override;
};

#endif // MYWIDGET_H
