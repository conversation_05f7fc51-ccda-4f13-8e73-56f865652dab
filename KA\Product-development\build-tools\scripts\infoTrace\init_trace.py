#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
产品信息追溯系统初始化脚本
专注于块级内容管理、精确追溯链条建立、变更影响分析等追溯系统特有功能
依赖文档关联系统提供的xx_INDEX.md文件基础
"""

import os
import sys
import json
import argparse
import datetime
from pathlib import Path

def create_traceability_config(project_path, structure_type):
    """创建追溯系统配置文件"""
    config_dir = os.path.join(project_path, 'config')
    os.makedirs(config_dir, exist_ok=True)
    
    if structure_type == "single_layer":
        # 单层级追溯配置
        traceability_config = {
            "traceability": {
                "version": "1.0.0",
                "system_type": "block_level_precise_traceability",
                "framework_type": "single_layer",
                "created_date": datetime.datetime.now().isoformat(),
                "description": "完整的块级追溯系统，基于文档关联建立精确追溯",
                "responsibilities": [
                    "块级内容管理within文档",
                    "精确追溯链条从deliverables到原始需求",
                    "变更影响分析基于块级关系",
                    "关系验证in INDEX文件"
                ],
                "block_management": {
                    "auto_identification": True,
                    "supported_types": ["FUNC", "API", "CODE", "ARCH", "CONFIG", "TEST", "DOC"],
                    "id_format": "DOC_ID.BLOCK_TYPE.SEQUENCE",
                    "manual_marking": "<!-- BLOCK_ID: DOC001.XXX.001 -->",
                    "reference_syntax": "[[REF:DOC_ID.BLOCK_ID:relationship]]"
                },
                "content_identification": {
                    "headers": {"enabled": True, "levels": [1, 2, 3, 4, 5, 6]},
                    "lists": {"enabled": True, "types": ["ordered", "unordered"]},
                    "code_blocks": {"enabled": True, "languages": ["python", "javascript", "yaml", "json"]},
                    "tables": {"enabled": True, "min_rows": 2},
                    "quotes": {"enabled": True, "nested": False},
                    "manual_blocks": {"enabled": True, "pattern": "<!-- BLOCK_ID: .*? -->"}
                },
                "traceability_relationships": {
                    "requirement_refinement": "上级需求被下级需求细化",
                    "solution_implementation": "需求通过方案设计实现",
                    "technical_realization": "设计方案通过技术手段实现",
                    "functional_verification": "实现功能通过测试验证",
                    "dependency_relation": "某块依赖其他块的输出",
                    "constraint_relation": "某块受其他块的约束",
                    "extension_relation": "某块是其他块的扩展或补充"
                },
                "validation_rules": {
                    "document_association_constraint": True,
                    "block_dependency_check": True,
                    "circular_reference_detection": True,
                    "orphaned_block_detection": True
                },
                "change_impact": {
                    "enabled": True,
                    "analysis_depth": 3,
                    "impact_types": ["direct", "indirect", "cascade"],
                    "notification_threshold": "medium"
                }
            }
        }
    else:
        # 多层级追溯配置
        traceability_config = {
            "traceability": {
                "version": "1.0.0",
                "system_type": "block_level_precise_traceability",
                "framework_type": "multi_level",
                "created_date": datetime.datetime.now().isoformat(),
                "description": "多层级块级追溯系统，支持跨层级精确追溯",
                "responsibilities": [
                    "块级内容管理within文档",
                    "跨层级精确追溯链条",
                    "层级间变更影响分析",
                    "多层级关系验证"
                ],
                "block_management": {
                    "auto_identification": True,
                    "supported_types": ["FUNC", "API", "CODE", "ARCH", "CONFIG", "TEST", "DOC"],
                    "id_format": "L{level}_{component}_{doc_seq}.{block_type}.{block_seq}",
                    "manual_marking": "<!-- BLOCK_ID: L1_REQ_001.FUNC.001 -->",
                    "reference_syntax": "[[REF:L1_REQ_001.FUNC.001:下级实现]]"
                },
                "level_relationships": {
                    "platform_to_application": "平台层到应用层的实现展开",
                    "cross_level_dependency": "跨层级的依赖关系",
                    "level_constraint": "层级约束关系",
                    "inheritance_relation": "层级继承关系"
                },
                "multi_level_validation": {
                    "cross_level_consistency": True,
                    "level_dependency_check": True,
                    "inheritance_validation": True
                }
            }
        }
    
    config_path = os.path.join(config_dir, 'traceability_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(traceability_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建追溯系统配置文件: {config_path}")
    return config_path

def create_block_analysis_config(project_path):
    """创建块级分析配置"""
    config_dir = os.path.join(project_path, 'config')
    
    block_config = {
        "block_analysis": {
            "version": "1.0.0",
            "content_parsing": {
                "markdown_parser": {
                    "enabled": True,
                    "extensions": ["tables", "fenced_code", "toc"],
                    "custom_patterns": {
                        "manual_blocks": r"<!-- BLOCK_ID: (.*?) -->",
                        "block_references": r"\[\[REF:(.*?):(.*?)\]\]",
                        "function_definitions": r"^(def|function|class)\s+(\w+)",
                        "api_endpoints": r"^(GET|POST|PUT|DELETE)\s+(/.*?)$"
                    }
                },
                "content_extraction": {
                    "headers": {"pattern": r"^(#{1,6})\s+(.+)$", "extract_level": True},
                    "lists": {"pattern": r"^(\s*[-*+]|\s*\d+\.)\s+(.+)$", "extract_indent": True},
                    "code_blocks": {"pattern": r"```(\w+)?\n(.*?)\n```", "extract_language": True},
                    "tables": {"pattern": r"\|.*?\|", "extract_headers": True}
                }
            },
            "relationship_detection": {
                "explicit_references": {
                    "enabled": True,
                    "patterns": ["[[REF:", "参见", "见", "参考", "基于", "实现", "验证"]
                },
                "implicit_relationships": {
                    "enabled": True,
                    "semantic_analysis": False,  # 语义分析由文档关联系统负责
                    "keyword_matching": True,
                    "structure_analysis": True
                },
                "relationship_types": {
                    "refinement": {"keywords": ["细化", "详述", "展开"], "direction": "downward"},
                    "implementation": {"keywords": ["实现", "执行", "开发"], "direction": "forward"},
                    "verification": {"keywords": ["验证", "测试", "检查"], "direction": "backward"},
                    "dependency": {"keywords": ["依赖", "需要", "基于"], "direction": "upward"},
                    "constraint": {"keywords": ["约束", "限制", "要求"], "direction": "constraint"}
                }
            },
            "impact_analysis": {
                "change_propagation": {
                    "enabled": True,
                    "max_depth": 5,
                    "bidirectional": True,
                    "include_suggestions": True
                },
                "risk_assessment": {
                    "enabled": True,
                    "factors": ["relationship_strength", "change_frequency", "block_complexity"],
                    "risk_levels": ["low", "medium", "high", "critical"]
                }
            }
        }
    }
    
    config_path = os.path.join(config_dir, 'block_analysis_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(block_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建块级分析配置: {config_path}")
    return config_path

def create_traceability_tools_config(project_path, scripts_base):
    """创建追溯工具配置"""
    config_dir = os.path.join(project_path, 'config')
    
    tools_config = {
        "traceability_tools": {
            "version": "1.0.0",
            "block_manager": {
                "script": f"{scripts_base}/infoTrace/auto_index_manager.py",
                "operations": ["scan-blocks", "validate", "update"],
                "auto_run": True,
                "schedule": "on_file_change"
            },
            "traceability_manager": {
                "script": f"{scripts_base}/infoTrace/traceability_manager.py",
                "operations": ["build-chains", "validate-relationships", "generate-reports"],
                "auto_run": False,
                "schedule": "daily"
            },
            "change_impact_analyzer": {
                "script": f"{scripts_base}/infoTrace/change_impact.py",
                "operations": ["analyze", "visualize", "report"],
                "auto_run": True,
                "trigger": "on_block_change"
            },
            "relationship_validator": {
                "script": f"{scripts_base}/infoTrace/relationship_validator.py",
                "operations": ["validate", "fix", "report"],
                "auto_run": True,
                "schedule": "weekly"
            }
        }
    }
    
    config_path = os.path.join(config_dir, 'traceability_tools_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(tools_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建追溯工具配置: {config_path}")
    return config_path

def create_initial_tracking_tables(project_path):
    """创建初始追溯表格"""
    reports_dir = os.path.join(project_path, 'reports')
    os.makedirs(reports_dir, exist_ok=True)
    
    # 创建块级追踪表格
    tracking_tables = {
        "block_tracking": {
            "version": "1.0.0",
            "last_updated": datetime.datetime.now().isoformat(),
            "tracking_method": "block_level_precise",
            "constraint": "基于文档关联系统的关联关系",
            "blocks": {},
            "relationships": [],
            "change_history": [],
            "statistics": {
                "total_blocks": 0,
                "total_relationships": 0,
                "components_with_blocks": [],
                "relationship_types": {}
            }
        },
        "traceability_chains": {
            "version": "1.0.0",
            "last_updated": datetime.datetime.now().isoformat(),
            "chains": {},
            "validation_results": {},
            "impact_analysis": {}
        }
    }
    
    tracking_path = os.path.join(reports_dir, 'block_tracking.json')
    with open(tracking_path, 'w', encoding='utf-8') as f:
        json.dump(tracking_tables, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建初始块级追踪表格: {tracking_path}")

def create_usage_guide(project_path, structure_type):
    """创建追溯系统使用指南"""
    guide_content = f"""# 产品信息追溯系统使用指南

## 系统概述

产品信息追溯系统是一个完整的块级追溯系统，专注于：

1. **块级内容管理**: 管理文档内部的内容块和块级追溯关系
2. **精确追溯链条**: 建立从deliverables到原始需求的精确追溯
3. **变更影响分析**: 基于块级关系进行影响分析
4. **关系验证**: 验证INDEX文件中关联关系的有效性

## 系统协作

### 与文档关联系统的协作

**重要约束**: 内容追溯关系建立在文档关联基础上。如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

- **文档关联系统**负责：XX_INDEX.md文件创建、文档注册、语义关联发现
- **追溯系统**负责：块级内容管理、精确追溯、变更影响分析

### INDEX文件协作管理

XX_INDEX.md文件的10个字段中：
- 前4列（文档ID、文档名称、文档路径、文档类型）由文档关联系统管理
- 后6列（块ID、块类型、块标题/描述、追溯来源块ID、关系类型、最后更新时间）由追溯系统管理

## 核心功能

### 1. 块级内容识别

#### 自动识别的内容块
- **标题块**: H1-H6标题自动识别为文档结构块
- **列表块**: 有序和无序列表自动识别
- **代码块**: 各种编程语言的代码块
- **表格块**: 包含数据的表格结构
- **引用块**: Markdown引用格式

#### 手动标记的内容块
```markdown
<!-- BLOCK_ID: {('L1_' if structure_type == 'multi_level' else '')}REQ001.FUNC.001 -->
这里是一个功能需求的详细描述...
<!-- /BLOCK -->
```

### 2. 块级关系管理

#### 关系类型定义
- **需求细化**: 上级需求被下级需求细化
- **方案实现**: 需求通过方案设计实现  
- **技术实现**: 设计方案通过技术手段实现
- **功能验证**: 实现功能通过测试验证
- **依赖关系**: 某块依赖其他块的输出
- **约束关系**: 某块受其他块的约束
- **扩展关系**: 某块是其他块的扩展或补充

#### 块级引用语法
```markdown
[[REF:{('L1_' if structure_type == 'multi_level' else '')}REQ001.FUNC.001:需求]]            # 引用需求块
[[REF:{('L1_' if structure_type == 'multi_level' else '')}DES001.ARCH.001:实现]]           # 实现设计架构
[[REF:{('L1_' if structure_type == 'multi_level' else '')}DEV001.CODE.001:代码实现]]       # 代码实现块
[[REF:{('L1_' if structure_type == 'multi_level' else '')}QA001.TEST.001:验证]]            # 测试验证块
```

### 3. 变更影响分析

#### 影响分析类型
- **直接影响**: 直接引用或被引用的块
- **间接影响**: 通过关系链传播的影响
- **级联影响**: 多层次传播的复合影响

#### 风险评估
- **低风险**: 孤立块的变更，影响范围有限
- **中风险**: 有少量依赖关系的块变更
- **高风险**: 核心块变更，影响多个下游块
- **关键风险**: 基础架构块变更，可能影响整个系统

## 使用流程

### 1. 块级扫描和管理

```bash
# 扫描指定文档的内容块
python scripts/infoTrace/auto_index_manager.py --project-path . --doc-id REQ001 --file-path requirements/user_requirements.md --scan-blocks

# 验证块级关系
python scripts/infoTrace/auto_index_manager.py --project-path . --validate

# 更新所有组件的块级信息
python scripts/infoTrace/auto_index_manager.py --project-path . --update-all
```

### 2. 追溯链条建立

```bash
# 建立追溯链条
python scripts/infoTrace/traceability_manager.py --project-path . --build-chains

# 验证追溯关系
python scripts/infoTrace/traceability_manager.py --project-path . --validate-relationships

# 生成追溯报告
python scripts/infoTrace/traceability_manager.py --project-path . --generate-reports
```

### 3. 变更影响分析

```bash
# 分析特定块的变更影响
python scripts/infoTrace/change_impact.py --project-path . --block-id REQ001.FUNC.001 --analyze

# 可视化影响范围
python scripts/infoTrace/change_impact.py --project-path . --block-id REQ001.FUNC.001 --visualize

# 生成影响分析报告
python scripts/infoTrace/change_impact.py --project-path . --report
```

### 4. 关系验证

```bash
# 验证所有关系的有效性
python scripts/infoTrace/relationship_validator.py --project-path . --validate

# 自动修复无效关系
python scripts/infoTrace/relationship_validator.py --project-path . --fix

# 生成验证报告
python scripts/infoTrace/relationship_validator.py --project-path . --report
```

## 配置管理

### 追溯系统配置
在 `config/traceability_config.json` 中配置：
- 块级管理参数
- 追溯关系类型
- 验证规则
- 变更影响设置

### 块级分析配置
在 `config/block_analysis_config.json` 中配置：
- 内容解析规则
- 关系检测模式
- 影响分析参数

### 工具配置
在 `config/traceability_tools_config.json` 中配置：
- 各个工具的脚本路径
- 自动化运行规则
- 调度策略

## 最佳实践

### 1. 块级标记
- 对重要的功能点、API接口、架构设计等进行手动块级标记
- 使用清晰的块ID命名规范
- 保持块级描述的准确性和完整性

### 2. 关系建立
- 明确标识块级依赖关系
- 使用标准的引用语法
- 及时更新关系链条

### 3. 变更管理
- 变更前进行影响分析
- 记录变更对下游块的影响
- 定期验证关系的有效性

### 4. 系统协作
- 确保文档已在文档关联系统中注册
- 遵循两系统的职责分工
- 定期同步INDEX文件状态

## 故障排除

### 常见问题

1. **块级扫描失败**: 检查文档格式和路径
2. **关系建立失败**: 验证文档关联系统的基础数据
3. **影响分析不准确**: 检查块级关系的完整性
4. **追溯链条断裂**: 验证相关文档的存在性

### 恢复方法

```bash
# 重新初始化追溯系统
python scripts/infoTrace/init_trace.py --project_path . --structure_type {structure_type}

# 重建所有块级关系
python scripts/infoTrace/auto_index_manager.py --project-path . --rebuild

# 重新验证追溯链条
python scripts/infoTrace/traceability_manager.py --project-path . --rebuild-chains
```

---
*此系统专注于块级精确追溯，与文档关联系统协作提供完整的追溯解决方案*
"""
    
    guide_path = os.path.join(project_path, 'TRACEABILITY_GUIDE.md')
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"[+] 创建追溯系统使用指南: {guide_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="产品信息追溯系统初始化工具")
    parser.add_argument("--project_path", required=True, help="项目路径")
    parser.add_argument("--structure_type", choices=["single_layer", "multi_level"], 
                       default="single_layer", help="目录结构类型")
    parser.add_argument("--scripts_base", required=True, help="公共脚本基础路径")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.project_path):
        print(f"错误: 项目路径不存在 {args.project_path}")
        return False
    
    print(f"初始化产品信息追溯系统...")
    print(f"项目路径: {args.project_path}")
    print(f"结构类型: {args.structure_type}")
    print(f"脚本路径: {args.scripts_base}")
    print(f"依赖: 文档关联系统提供的INDEX文件基础")
    
    try:
        # 1. 创建追溯系统配置文件
        create_traceability_config(args.project_path, args.structure_type)
        
        # 2. 创建块级分析配置文件
        create_block_analysis_config(args.project_path)
        
        # 3. 创建追溯工具配置文件
        create_traceability_tools_config(args.project_path, args.scripts_base)
        
        # 4. 创建初始追踪表格
        create_initial_tracking_tables(args.project_path)
        
        # 5. 创建使用指南
        create_usage_guide(args.project_path, args.structure_type)
        
        print(f"✅ 产品信息追溯系统初始化完成!")
        print(f"ℹ 注意: INDEX文件由文档关联系统创建和管理")
        print(f"ℹ 注意: 追溯系统将在INDEX文件基础上添加块级信息")
        return True
        
    except Exception as e:
        print(f"[X] 产品信息追溯系统初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 