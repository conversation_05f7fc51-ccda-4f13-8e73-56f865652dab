# 产品体系一体化流程框架

本目录包含产品体系一体化流程框架的实现脚本，用于管理产品开发过程中各组件之间的连接和自动化处理。

## 目录结构

- `workflow_manager.py` - 工作流管理器核心实现
- `enhanced_workflow_diagram.py` - 流程图生成工具
- `init_workflow_config.py` - 工作流配置初始化工具
- `add_handler.py` - 处理器添加工具
- `simulate_event.py` - 事件模拟工具

## 使用指南

### 初始化工作流

首先需要初始化工作流配置和必要的目录结构：

```bash
python scripts/workflow/init_workflow_config.py
```

这将创建以下内容：

- 基本配置文件 `config/workflow_config.json`
- 必要的目录结构
- 示例MCP服务器脚本
- 工作流程图

### 管理工作流配置

#### 添加组件连接

```bash
python scripts/workflow/add_handler.py add_connection --from "组件A" --to "组件B"
```

#### 添加触发器

```bash
python scripts/workflow/add_handler.py add_trigger --from "组件A" --to "组件B" --name "触发器名称" --type "event"
```

#### 添加处理器

```bash
python scripts/workflow/add_handler.py add_handler --from "组件A" --to "组件B" --trigger "触发器名称" --handler-type "script" --handler-name "处理器名称" --script "scripts/path/to/script.py" --params '{"key": "value"}'
```

### 模拟事件

可以使用事件模拟工具测试工作流：

```bash
python scripts/workflow/simulate_event.py --from "需求分析" --to "方案设计" --trigger "需求矩阵更新" --event-type "document_change" --source-file "requirements/requirements_matrix.md"
```

### 生成流程图

可以使用增强的流程图生成工具可视化工作流配置：

```bash
python scripts/workflow/enhanced_workflow_diagram.py --type all --output "reports/workflow_diagram.png"
```

支持的图表类型：

- `main` - 主要组件连接图
- `detailed` - 每个连接的详细图
- `complete` - 完整流程图（包含所有组件、触发器和处理器）
- `all` - 生成所有类型的图表

## 常见用例

### 需求变更自动更新设计

1. 添加连接和触发器：

```bash
python scripts/workflow/add_handler.py add_connection --from "需求分析" --to "方案设计"
python scripts/workflow/add_handler.py add_trigger --from "需求分析" --to "方案设计" --name "需求变更"
```

2. 添加MCP处理器：

```bash
python scripts/workflow/add_handler.py add_handler --from "需求分析" --to "方案设计" --trigger "需求变更" --handler-type "mcp_server" --handler-name "更新设计方案" --script "scripts/mcp-server_local_integrations/update_design.py" --params '{"output_dir": "design/"}'
```

3. 模拟需求变更事件：

```bash
python scripts/workflow/simulate_event.py --from "需求分析" --to "方案设计" --trigger "需求变更" --event-type "document_change" --source-file "requirements/requirements.md"
```

### 代码提交自动审查和测试

1. 添加连接和触发器：

```bash
python scripts/workflow/add_handler.py add_connection --from "开发" --to "测试"
python scripts/workflow/add_handler.py add_trigger --from "开发" --to "测试" --name "代码提交"
```

2. 添加代码审查处理器：

```bash
python scripts/workflow/add_handler.py add_handler --from "开发" --to "测试" --trigger "代码提交" --handler-type "mcp_server" --handler-name "代码审查" --script "scripts/mcp-server_local_integrations/code_review.py"
```

3. 添加单元测试生成处理器：

```bash
python scripts/workflow/add_handler.py add_handler --from "开发" --to "测试" --trigger "代码提交" --handler-type "mcp_server" --handler-name "生成单元测试" --script "scripts/mcp-server_local_integrations/generate_unit_tests.py"
```

4. 模拟代码提交事件：

```bash
python scripts/workflow/simulate_event.py --from "开发" --to "测试" --trigger "代码提交" --event-type "code_commit" --source-file "src/main.cpp"
```

## 扩展指南

### 创建新的MCP服务器

1. 在 `scripts/mcp-server_local_integrations/` 目录下创建新的Python脚本
2. 确保脚本接受命令行参数并返回结构化输出
3. 将脚本添加到工作流配置中

### 添加新的组件

1. 确定新组件的名称和职责
2. 添加从其他组件到新组件的连接
3. 添加从新组件到其他组件的连接
4. 定义适当的触发器和处理器

## 故障排除

### 日志文件

工作流管理器的日志存储在 `logs/workflow_manager.log` 文件中，包含详细的执行信息和错误消息。

### 常见问题

1. **处理器脚本未找到**：确保脚本路径正确，路径相对于项目根目录。

2. **事件未触发任何处理器**：检查触发器名称和组件名称是否与配置文件匹配。

3. **参数模板变量未替换**：确保事件数据中包含对应的字段。
