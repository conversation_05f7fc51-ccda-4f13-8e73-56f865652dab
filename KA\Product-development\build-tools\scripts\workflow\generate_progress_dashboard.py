#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
进度仪表板生成器 - 用于项目的进度可视化
"""

import os
import re
import json
import yaml
from pathlib import Path
import argparse
import time
from datetime import datetime
import matplotlib.pyplot as plt
import numpy as np
import shutil

def analyze_document_status(workspace_path, config_path=None):
    """分析工作区内各类文档的状态和完成度"""
    # 加载配置
    if config_path and os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        phases = {phase["id"]: {"weight": phase["weight"], "required_docs": phase["required_docs"]} 
                 for phase in config.get("phases", [])}
        project_name = config.get("project_name", "项目")
    else:
        # 默认配置
        phases = {
            "product_info": {"weight": 0.1, "required_docs": ["product_brief.md", "market_analysis.md"]},
            "requirements": {"weight": 0.2, "required_docs": ["market_requirements.md", "technical_requirements.md"]},
            "design": {"weight": 0.3, "required_docs": ["system_architecture.md", "interface_specifications.md"]},
            "development": {"weight": 0.3, "required_docs": ["hardware_design.md", "firmware_development.md"]},
            "quality": {"weight": 0.1, "required_docs": ["test_plans.md", "test_reports.md"]}
        }
        project_name = "16. line lidar"
    
    results = {}
    
    # 从document_analyzer.py导入分析函数
    module_path = os.path.dirname(os.path.abspath(__file__))
    analyzer_path = os.path.join(module_path, "document_analyzer.py")
    
    # 动态导入
    import importlib.util
    spec = importlib.util.spec_from_file_location("document_analyzer", analyzer_path)
    analyzer_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(analyzer_module)
    analyze_single_document = analyzer_module.analyze_single_document
    
    # 遍历每个阶段目录
    for phase, config in phases.items():
        phase_dir = os.path.join(workspace_path, phase)
        if not os.path.exists(phase_dir):
            results[phase] = {
                "exists": False,
                "completion": 0,
                "documents": []
            }
            continue
            
        # 获取目录内所有文档
        docs = list(Path(phase_dir).glob("**/*.md"))
        all_docs = [os.path.basename(str(doc)) for doc in docs]
        
        # 计算必要文档的存在情况
        required_docs = config["required_docs"]
        required_found = [doc for doc in required_docs if doc in all_docs]
        
        # 分析文档质量和完成度
        doc_status = []
        for doc in docs:
            doc_info = analyze_single_document(str(doc))
            doc_status.append(doc_info)
            
        # 计算阶段完成度
        req_completion = len(required_found) / len(required_docs) if required_docs else 1
        doc_quality = sum([doc["completion"] for doc in doc_status]) / len(doc_status) if doc_status else 0
        
        # 综合评分 (文档存在 * 文档质量)
        completion = req_completion * 0.6 + doc_quality * 0.4
            
        results[phase] = {
            "exists": True,
            "required_docs": required_docs,
            "required_found": required_found,
            "required_completion": req_completion,
            "document_quality": doc_quality,
            "completion": completion,
            "documents": doc_status
        }
    
    # 计算整体完成度
    total_weight = sum([config["weight"] for phase, config in phases.items()])
    weighted_completion = sum([results[phase]["completion"] * phases[phase]["weight"] 
                             for phase in phases if phase in results])
    
    overall_completion = weighted_completion / total_weight if total_weight > 0 else 0
    
    return {
        "project_name": project_name,
        "phases": results,
        "overall_completion": overall_completion,
        "timestamp": datetime.now().isoformat()
    }

def generate_progress_dashboard(status_data, output_dir="reports"):
    """生成项目进度仪表板"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取数据
    phases = status_data["phases"]
    phase_names = list(phases.keys())
    completions = [phases[p]["completion"] * 100 for p in phase_names]
    
    # 创建进度条图表
    plt.figure(figsize=(10, 6))
    
    # 水平条形图
    y_pos = np.arange(len(phase_names))
    bars = plt.barh(y_pos, completions, align='center', alpha=0.7)
    plt.yticks(y_pos, [p.capitalize() for p in phase_names])
    plt.xlabel('完成度 (%)')
    plt.title(f'{status_data.get("project_name", "项目")}各阶段完成度')
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        plt.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, 
                f'{completions[i]:.1f}%', va='center')
    
    # 设置刻度和网格
    plt.xlim(0, 105)
    plt.grid(axis='x', linestyle='--', alpha=0.6)
    
    # 保存图表
    chart_path = os.path.join(output_dir, "phase_progress.png")
    plt.tight_layout()
    plt.savefig(chart_path, dpi=300)
    plt.close()
    
    # 生成总体进度饼图
    plt.figure(figsize=(8, 8))
    overall = status_data["overall_completion"] * 100
    remaining = 100 - overall
    
    # 饼图
    colors = ['#4CAF50', '#F5F5F5']
    plt.pie([overall, remaining], labels=['已完成', '未完成'], colors=colors, 
            autopct='%1.1f%%', startangle=90, wedgeprops={"edgecolor":"white", 'linewidth': 1})
    plt.title(f'{status_data.get("project_name", "项目")}总体完成度: {overall:.1f}%')
    plt.axis('equal')  # 确保饼图是圆形的
    
    # 保存饼图
    pie_path = os.path.join(output_dir, "overall_progress.png")
    plt.savefig(pie_path, dpi=300)
    plt.close()
    
    # 生成文档状态表格
    doc_status_table = generate_document_status_table(status_data)
    table_path = os.path.join(output_dir, "document_status.md")
    with open(table_path, 'w', encoding='utf-8') as f:
        f.write(doc_status_table)
    
    # 生成HTML仪表板
    dashboard_html = generate_html_dashboard(status_data, chart_path, pie_path)
    html_path = os.path.join(output_dir, "progress_dashboard.html")
    with open(html_path, 'w', encoding='utf-8') as f:
        f.write(dashboard_html)
    
    return {
        "chart_path": chart_path,
        "pie_path": pie_path,
        "table_path": table_path,
        "html_path": html_path
    }

def generate_document_status_table(status_data):
    """生成文档状态Markdown表格"""
    output = f"# {status_data.get('project_name', '项目')}文档状态\n\n"
    output += f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
    output += f"**项目总体完成度: {status_data['overall_completion']*100:.1f}%**\n\n"
    
    for phase, data in status_data["phases"].items():
        output += f"## {phase.capitalize()}\n\n"
        output += f"阶段完成度: {data['completion']*100:.1f}%\n\n"
        
        if not data.get("exists", False):
            output += "*此阶段目录不存在*\n\n"
            continue
            
        output += "| 文档 | 状态 | 完成度 | 字数 |\n"
        output += "|------|------|--------|------|\n"
        
        for doc in data.get("documents", []):
            status_emoji = "✅" if doc["status"] == "completed" else "🔄" if doc["status"] == "in_progress" else "🔘"
            output += f"| {doc['title']} | {status_emoji} {doc['status'].capitalize()} | {doc['completion']*100:.1f}% | {doc['word_count']} |\n"
        
        output += "\n"
    
    return output

def generate_html_dashboard(status_data, chart_path, pie_path):
    """生成HTML仪表板"""
    # 简化实现，实际项目中可以使用模板引擎
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{project_name}进度仪表板</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
            .dashboard {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ text-align: center; margin-bottom: 30px; }}
            .charts {{ display: flex; flex-wrap: wrap; justify-content: center; gap: 20px; margin-bottom: 30px; }}
            .chart {{ box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 15px; border-radius: 5px; background: white; }}
            .phases {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }}
            .phase {{ border: 1px solid #eee; border-radius: 5px; padding: 15px; }}
            .phase-header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }}
            .progress-bar {{ height: 10px; background: #f0f0f0; border-radius: 5px; overflow: hidden; }}
            .progress-fill {{ height: 100%; background: #4CAF50; }}
            .status-complete {{ color: #4CAF50; }}
            .status-progress {{ color: #2196F3; }}
            .status-initial {{ color: #9E9E9E; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ text-align: left; padding: 8px; border-bottom: 1px solid #ddd; }}
        </style>
    </head>
    <body>
        <div class="dashboard">
            <div class="header">
                <h1>{project_name}进度仪表板</h1>
                <p>生成时间: {timestamp}</p>
                <h2>总体完成度: {overall_completion:.1f}%</h2>
            </div>
            
            <div class="charts">
                <div class="chart">
                    <h3>阶段完成度</h3>
                    <img src="{chart_path}" alt="阶段完成度" style="max-width: 100%;">
                </div>
                <div class="chart">
                    <h3>总体进度</h3>
                    <img src="{pie_path}" alt="总体进度" style="max-width: 100%;">
                </div>
            </div>
            
            <h2>各阶段详情</h2>
            <div class="phases">
    """.format(
        project_name=status_data.get("project_name", "项目"),
        timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        overall_completion=status_data["overall_completion"]*100,
        chart_path=os.path.basename(chart_path),
        pie_path=os.path.basename(pie_path)
    )
    
    # 添加每个阶段的信息
    for phase, data in status_data["phases"].items():
        completion = data["completion"] * 100
        html += f"""
                <div class="phase">
                    <div class="phase-header">
                        <h3>{phase.capitalize()}</h3>
                        <span>{completion:.1f}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {completion}%;"></div>
                    </div>
        """
        
        if not data.get("exists", False):
            html += "<p><em>此阶段目录不存在</em></p>"
        else:
            html += """
                    <table>
                        <tr>
                            <th>文档</th>
                            <th>状态</th>
                            <th>完成度</th>
                        </tr>
            """
            
            for doc in data.get("documents", []):
                status_class = "status-complete" if doc["status"] == "completed" else \
                              "status-progress" if doc["status"] == "in_progress" else "status-initial"
                status_emoji = "✅" if doc["status"] == "completed" else "🔄" if doc["status"] == "in_progress" else "🔘"
                
                html += f"""
                        <tr>
                            <td>{doc['title']}</td>
                            <td class="{status_class}">{status_emoji} {doc['status'].capitalize()}</td>
                            <td>{doc['completion']*100:.1f}%</td>
                        </tr>
                """
            
            html += """
                    </table>
            """
        
        html += """
                </div>
        """
    
    html += """
            </div>
        </div>
    </body>
    </html>
    """
    
    return html

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成项目进度仪表板")
    parser.add_argument("--workspace", required=True, help="工作区路径")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--output", default="reports", help="输出目录")
    args = parser.parse_args()
    
    print(f"分析项目: {args.workspace}")
    start_time = time.time()
    
    # 确保输出目录存在
    output_dir = args.output
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 分析文档状态
    print("分析文档状态...")
    config_path = args.config if args.config else os.path.join("config", "phases.json")
    status_data = analyze_document_status(args.workspace, config_path)
    
    # 保存状态数据为JSON
    status_path = os.path.join(output_dir, "project_status.json")
    with open(status_path, 'w', encoding='utf-8') as f:
        json.dump(status_data, f, ensure_ascii=False, indent=2)
    
    # 2. 生成进度仪表板
    print("生成进度仪表板...")
    dashboard_paths = generate_progress_dashboard(status_data, output_dir)
    
    # 3. 生成项目报告
    print("生成项目报告...")
    report_path = os.path.join(output_dir, "project_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# {status_data.get('project_name', '项目')}进度报告\n\n")
        f.write(f"*生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
        
        # 摘要
        f.write("## 摘要\n\n")
        f.write(f"- **总体完成度**: {status_data['overall_completion']*100:.1f}%\n")
        for phase, data in status_data["phases"].items():
            f.write(f"- **{phase.capitalize()}**: {data['completion']*100:.1f}%\n")
        f.write("\n")
        
        # 各阶段详情
        f.write("\n## 各阶段详情\n\n")
        for phase, data in status_data["phases"].items():
            f.write(f"### {phase.capitalize()}\n\n")
            f.write(f"完成度: {data['completion']*100:.1f}%\n\n")
            
            if not data.get("exists", False):
                f.write("*此阶段目录不存在*\n\n")
                continue
                
            f.write("| 文档 | 状态 | 完成度 | 字数 |\n")
            f.write("|------|------|--------|------|\n")
            
            for doc in data.get("documents", []):
                status_emoji = "✅" if doc["status"] == "completed" else "🔄" if doc["status"] == "in_progress" else "🔘"
                f.write(f"| {doc['title']} | {status_emoji} {doc['status'].capitalize()} | {doc['completion']*100:.1f}% | {doc['word_count']} |\n")
            
            f.write("\n")
        
        # 建议
        f.write("## 改进建议\n\n")
        
        # 查找完成度较低的阶段
        low_phases = [(p, d["completion"]) for p, d in status_data["phases"].items() 
                      if d["completion"] < 0.5 and d.get("exists", False)]
        low_phases.sort(key=lambda x: x[1])
        
        if low_phases:
            f.write("### 需要关注的阶段\n\n")
            for phase, completion in low_phases:
                f.write(f"- **{phase.capitalize()}** - 当前完成度仅为 {completion*100:.1f}%\n")
            f.write("\n")
        
        # 查找缺失的必要文档
        missing_docs = []
        for phase, data in status_data["phases"].items():
            if not data.get("exists", False):
                continue
                
            required = set(data.get("required_docs", []))
            found = set(data.get("required_found", []))
            missing = required - found
            
            for doc in missing:
                missing_docs.append((phase, doc))
        
        if missing_docs:
            f.write("### 缺失的关键文档\n\n")
            for phase, doc in missing_docs:
                f.write(f"- {phase.capitalize()}: **{doc}**\n")
            f.write("\n")
            
    elapsed = time.time() - start_time
    print(f"完成! 处理时间: {elapsed:.2f}秒")
    print(f"项目状态数据: {status_path}")
    print(f"进度仪表板: {dashboard_paths['html_path']}")
    print(f"项目报告: {report_path}")
    
    return 0

if __name__ == "__main__":
    exit(main()) 