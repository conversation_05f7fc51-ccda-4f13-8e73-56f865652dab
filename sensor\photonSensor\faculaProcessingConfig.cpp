#include "faculaProcessingConfig.h"
#include "interfaces/IImageFilter.h"
#include "interfaces/IInterpolation.h"

ImageProcessing::InterpolationType FaculaProcessingConfig::mapInterpolationType(uint8_t configType) {
    switch (configType) {
    case 0:
        return ImageProcessing::InterpolationType::None;
    case 1:
        return ImageProcessing::InterpolationType::Bilinear;
    case 2:
        return ImageProcessing::InterpolationType::Bicubic;
    case 3:
        return ImageProcessing::InterpolationType::Nonlinear;
    case 4:
        return ImageProcessing::InterpolationType::NearestNeighbor;
    default:
        logWarning(QString("Unknown interpolation type: %1, using None as default").arg(configType));
        return ImageProcessing::InterpolationType::None;
    }
}

QVector<ImageProcessing::FilterType> FaculaProcessingConfig::mapFilterTypes(const QString &filterTypesStr) {
    QVector<ImageProcessing::FilterType> filterTypes;

    if (filterTypesStr.isEmpty()) {
        logWarning("Empty filter types string, no filters will be applied");
        return filterTypes;  // 返回空列表，表示无滤波
    }

    QStringList typeStrings = filterTypesStr.split(',', Qt::SkipEmptyParts);
    for (const QString &typeStr : typeStrings) {
        bool    ok;
        uint8_t typeValue = typeStr.trimmed().toUInt(&ok);

        if (!ok) {
            logWarning(QString("Invalid filter type string: %1").arg(typeStr));
            continue;
        }

        switch (typeValue) {
        case 0:
            // 0 = None (无滤波)，跳过不添加任何滤波器
            logDebug("Filter type 0 (None) specified, skipping filter");
            break;
        case 1:
            filterTypes.append(ImageProcessing::FilterType::Kalman);
            break;
        case 2:
            filterTypes.append(ImageProcessing::FilterType::Convolution);
            break;
        case 3:
            filterTypes.append(ImageProcessing::FilterType::Median);
            break;
        case 4:
            filterTypes.append(ImageProcessing::FilterType::Gaussian);
            break;
        case 5:
            filterTypes.append(ImageProcessing::FilterType::Bilateral);
            break;
        case 6:
            filterTypes.append(ImageProcessing::FilterType::WeightedAverage);
            break;
        default:
            logWarning(QString("Unknown filter type: %1").arg(typeValue));
            break;
        }
    }

    // 注意：现在允许返回空的滤波器列表，表示无滤波

    return filterTypes;
}

ImageProcessing::InterpolationParams FaculaProcessingConfig::createInterpolationParams(const ProcessingConfig &config) {
    ImageProcessing::InterpolationParams params;

    params.offset        = config.interpolation_offset;
    params.preserveEdges = true;  // 默认保持边缘
    params.clampValues   = true;  // 默认限制数值范围
    params.minValue      = 0;
    params.maxValue      = UINT32_MAX;

    // 验证参数有效性
    if (params.offset < 0.0f || params.offset > 1.0f) {
        logWarning(QString("Invalid interpolation offset: %1, using default 0.5").arg(params.offset));
        params.offset = 0.5f;
    }

    return params;
}

ImageProcessing::KalmanParams FaculaProcessingConfig::createKalmanParams(const ProcessingConfig &config) {
    ImageProcessing::KalmanParams params;

    params.strength         = config.kalman_strength;
    params.processNoise     = 0.1f;  // 默认过程噪声
    params.measurementNoise = 0.1f;  // 默认测量噪声

    // 验证参数有效性
    if (params.strength <= 0.0f || params.strength > 10.0f) {
        logWarning(QString("Invalid Kalman strength: %1, using default 1.0").arg(params.strength));
        params.strength = 1.0f;
    }

    return params;
}

ImageProcessing::ConvolutionParams FaculaProcessingConfig::createConvolutionParams(const ProcessingConfig &config) {
    ImageProcessing::ConvolutionParams params;

    params.kernelSize = config.convolution_kernel_size;
    params.normalize  = true;  // 默认归一化

    // 验证参数有效性
    if (params.kernelSize < 3 || params.kernelSize > 15 || params.kernelSize % 2 == 0) {
        logWarning(QString("Invalid convolution kernel size: %1, using default 3").arg(params.kernelSize));
        params.kernelSize = 3;
    }

    // 创建默认的锐化核
    params.kernel.resize(params.kernelSize);
    for (auto &row : params.kernel) {
        row.resize(params.kernelSize);
        row.fill(0.0f);
    }

    if (params.kernelSize == 3) {
        // 3x3锐化核
        params.kernel[0] = {0.0f, -1.0f, 0.0f};
        params.kernel[1] = {-1.0f, 5.0f, -1.0f};
        params.kernel[2] = {0.0f, -1.0f, 0.0f};
    } else {
        // 其他尺寸使用单位核
        int center                    = params.kernelSize / 2;
        params.kernel[center][center] = 1.0f;
    }

    logDebug(QString("Created convolution params with %1x%1 kernel").arg(params.kernelSize));
    return params;
}

bool FaculaProcessingConfig::validateConfig(const ProcessingConfig &config) {
    bool isValid = true;

    // 验证插值类型
    if (config.interpolation_type > 3) {
        logWarning(QString("Invalid interpolation type: %1").arg(config.interpolation_type));
        isValid = false;
    }

    // 验证插值偏移量
    if (config.interpolation_offset < 0.0f || config.interpolation_offset > 1.0f) {
        logWarning(QString("Invalid interpolation offset: %1").arg(config.interpolation_offset));
        isValid = false;
    }

    // 验证卡尔曼滤波强度
    if (config.kalman_strength <= 0.0f || config.kalman_strength > 10.0f) {
        logWarning(QString("Invalid Kalman strength: %1").arg(config.kalman_strength));
        isValid = false;
    }

    // 验证卷积核大小
    if (config.convolution_kernel_size < 3 || config.convolution_kernel_size > 15 || config.convolution_kernel_size % 2 == 0) {
        logWarning(QString("Invalid convolution kernel size: %1").arg(config.convolution_kernel_size));
        isValid = false;
    }

    return isValid;
}

QString FaculaProcessingConfig::getInterpolationTypeDescription(ImageProcessing::InterpolationType type) {
    switch (type) {
    case ImageProcessing::InterpolationType::Bilinear:
        return "双线性插值";
    case ImageProcessing::InterpolationType::Bicubic:
        return "双三次插值";
    case ImageProcessing::InterpolationType::Nonlinear:
        return "非线性插值";
    case ImageProcessing::InterpolationType::NearestNeighbor:
        return "最近邻插值";
    default:
        return "未知插值类型";
    }
}

QString FaculaProcessingConfig::getFilterTypeDescription(ImageProcessing::FilterType type) {
    switch (type) {
    case ImageProcessing::FilterType::Kalman:
        return "卡尔曼滤波";
    case ImageProcessing::FilterType::Convolution:
        return "卷积滤波";
    case ImageProcessing::FilterType::Median:
        return "中值滤波";
    case ImageProcessing::FilterType::Gaussian:
        return "高斯滤波";
    case ImageProcessing::FilterType::Bilateral:
        return "双边滤波";
    case ImageProcessing::FilterType::WeightedAverage:
        return "加权均值滤波";
    default:
        return "未知滤波类型";
    }
}

void FaculaProcessingConfig::logDebug(const QString &message) {
    qDebug() << "[FaculaProcessingConfig]" << message;
}

void FaculaProcessingConfig::logWarning(const QString &message) {
    qWarning() << "[FaculaProcessingConfig]" << message;
}
