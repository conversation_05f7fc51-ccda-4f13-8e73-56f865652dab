{"traceability": {"version": "1.0.0", "framework_type": "single_layer", "id_format": "[组件代号]_[文档类型]_[序号]_[文档名称]", "id_register_method": "index_file", "created_date": "2025-07-09T15:06:19.954850", "components": {"REQ": {"name": "需求", "directory": "requirements", "index_file": "REQ_INDEX.md", "types": ["MATRIX", "SPEC", "ANALYSIS", "CHANGE"]}, "DES": {"name": "设计", "directory": "design", "index_file": "DES_INDEX.md", "types": ["ARCH", "DETAIL", "REVIEW", "CHANGE"]}, "DEV": {"name": "开发", "directory": "development", "index_file": "DEV_INDEX.md", "types": ["PLAN", "CODE", "TEST", "REVIEW"]}, "QA": {"name": "质量", "directory": "quality", "index_file": "QA_INDEX.md", "types": ["PLAN", "CASE", "REPORT", "ISSUE"]}, "PROD": {"name": "生产", "directory": "production", "index_file": "PROD_INDEX.md", "types": ["BOM", "PROCESS", "TEST", "REPORT"]}, "DEL": {"name": "交付", "directory": "deliverables", "index_file": "DEL_INDEX.md", "types": ["DOC", "SOFT", "HARD", "PACKAGE"]}}, "relationships": [{"from": "REQ", "to": "DES", "type": "需求追溯"}, {"from": "DES", "to": "DEV", "type": "设计实现"}, {"from": "DEV", "to": "QA", "type": "开发验证"}, {"from": "QA", "to": "PROD", "type": "质量确认"}, {"from": "PROD", "to": "DEL", "type": "生产交付"}], "block_management": {"enabled": true, "id_format": "[组件代号]_[序号]_[块类型]", "traceability_matrix": {"file": "reports/traceability_matrix.md", "auto_update": true}}, "change_management": {"enabled": true, "change_log": "logs/change_log.md", "impact_analysis": true}}}