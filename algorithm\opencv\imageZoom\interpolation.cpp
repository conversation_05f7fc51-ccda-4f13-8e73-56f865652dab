#include "interpolation.h"
#include <QDebug>
#include <QtMath>

my_interPolation::my_interPolation() {
    ;
}

my_interPolation::~my_interPolation() {
    ;
}

/*
 * @BRIEF: 原索引计算
 * @param: 目标图索引， 原图边宽， 目标图边宽， 偏移（缩小加偏移，默认偏移0.5，例如 9*9->3*3，dst（0，2）->src(1, 7)）
 * @result: 原图中的索引
 */
float inline my_interPolation::src_index_cal(const uint8_t &dst_index, const uint8_t &src_board_len, const uint8_t &dst_board_len, const float &offset) {
    return (float)dst_index * src_board_len / dst_board_len + offset * ((float)src_board_len / dst_board_len - 1);
}

/*
 * @BRIEF: 放大原索引计算
 * @param: 目标图索引， 原图边宽， 目标图边宽
 * @result: 原图中的索引
 */
float inline my_interPolation::src_index_cal(const uint8_t &dst_index, const uint8_t &src_board_len, const uint8_t &dst_board_len) {
    return (dst_index + 1) * (src_board_len / dst_board_len) - 1;
}

/*
 * @BRIEF: 线性插值
 * @param: 离左端点距离，左端点数值，右端点数值（默认两点坐标间距为 1）
 * @result: 目标点数值
 */
inline uint32_t my_interPolation::linear_iterpolation(const float &rate, const uint32_t &point_f, const uint32_t &point_b) {
    //    uint32_t int_rate = (rate*100);
    //    uint32_t b = point_b;
    //    uint32_t f = point_f;

    int32_t  p_b = point_b;
    int32_t  f_b = point_f;
    uint32_t tmp = (uint32_t)(point_f + rate * (p_b - f_b) + 0.5f);
    return tmp;  //左边界 四舍五入;
}

/*
 * @BRIEF: 权重插值：
 * @param: 离左端点距离，左端点数值，右端点数值（默认两点坐标间距为 1）
 * @result: 目标点数值
 */
inline uint32_t my_interPolation::weight_iterpolation(const float &rate, const uint32_t &point_f, const uint32_t &point_b) {
    return point_f + pow(rate, 2) * (point_b - point_f) + 0.5f;  //权重-平方（未验证）
}

/*
 * @brief: 双线性插值计算
 * @param:
 */
inline uint32_t my_interPolation::bilinear_interpolation_cal(const float &   rateX,
                                                             const float &   rateY,
                                                             const uint32_t &f1,
                                                             const uint32_t &f2,
                                                             const uint32_t &f3,
                                                             const uint32_t &f4) {
    return (1 - rateY) * (1 - rateX) * f1 + (1 - rateY) * rateX * f4 + rateY * (1 - rateX) * f2 + rateY * rateX * f3;
}
/*
 * @BRIEF: 双线性插值
 * @param: 2-dimension array； src-原图，dst-目标
 * @result: 2-dimension array
 */
void my_interPolation::bilinear_interpolation(const QVector<QVector<uint32_t>> &src_array, QVector<QVector<uint32_t>> &dst_array) {
    uint8_t  src_row_num, src_col_num, dst_row_num, dst_column_num;  //矩阵行列数
    uint32_t p1, p2, p3, p4;                                         //边缘四个元素值
    float    rateX, rateY;                                           //原图坐标 rateX-原图中column坐标

    src_row_num = src_array.length();
    if (src_row_num < 2)  //非二维->退出
    {
        qDebug() << "-interpolation: bilinear row num less";
        return;
    }
    src_col_num    = src_array.begin()->length();
    dst_row_num    = dst_array.length();
    dst_column_num = dst_array.begin()->length();

    /*1. 顶点*/
    dst_array[0][0]                                = src_array[0][0];
    dst_array[0][dst_column_num - 1]               = src_array[0][src_col_num - 1];
    dst_array[dst_row_num - 1][0]                  = src_array[src_row_num - 1][0];
    dst_array[dst_row_num - 1][dst_column_num - 1] = src_array[src_row_num - 1][src_col_num - 1];

    /*2. 边界点*/
    for (uint i = 1; i < dst_row_num - 1; ++i)  //左右边界
    {
        rateY               = src_index_cal(i, src_row_num, dst_row_num, 0.5);
        uint8_t src_f_index = rateY;                                                  //前索引
        uint8_t src_b_index = (rateY + 1) < src_row_num ? (rateY + 1) : src_row_num;  //后索引
        rateY -= floor(rateY);                                                        //取小数部分
        uint32_t si1    = src_array[src_f_index][0];
        uint32_t si2    = src_array[src_b_index][0];
        dst_array[i][0] = linear_iterpolation(rateY, si1, si2);  //左边界 四舍五入
        dst_array[i][dst_column_num - 1] =
            linear_iterpolation(rateY, src_array[src_f_index][src_col_num - 1], src_array[src_b_index][src_col_num - 1]);  //右边界 四舍五入
    }
    for (uint i = 1; i < dst_column_num - 1; ++i)  //上下边界
    {
        rateX               = src_index_cal(i, src_col_num, dst_column_num, 0.5);
        uint8_t src_f_index = rateX;                                                  //左索引
        uint8_t src_b_index = (rateX + 1) < src_col_num ? (rateX + 1) : src_col_num;  //右索引
        rateX -= floor(rateX);
        dst_array[0][i]               = linear_iterpolation(rateX, src_array[0][src_f_index], src_array[0][src_b_index]);
        dst_array[dst_row_num - 1][i] = linear_iterpolation(rateX, src_array[src_row_num - 1][src_f_index], src_array[src_row_num - 1][src_b_index]);
    }

    /*3. 中间点 目标点->原点（带小数索引）->对应点peak值*/
    for (int i = 1; i < dst_row_num - 1; i++)  //
    {
        for (int j = 1; j < dst_column_num - 1; j++) {
            /*1. 原图中索引计算*/
            rateY                 = src_index_cal(i, src_row_num, dst_row_num, 0.5);
            rateX                 = src_index_cal(j, src_col_num, dst_column_num, 0.5);
            uint8_t src_y_f_index = rateY;                                                  //前索引
            uint8_t src_y_b_index = (rateY + 1) < src_row_num ? (rateY + 1) : src_row_num;  //后索引
            uint8_t src_x_f_index = rateX;                                                  //前索引
            uint8_t src_x_b_index = (rateX + 1) < src_col_num ? (rateX + 1) : src_col_num;  //后索引
            rateY -= floor(rateY);                                                          //取小数部分
            rateX -= floor(rateX);

            /*2. 临近4个元素*/
            p1 = src_array[src_y_f_index][src_x_f_index];
            p2 = src_array[src_y_b_index][src_x_f_index];
            p3 = src_array[src_y_b_index][src_x_b_index];
            p4 = src_array[src_y_f_index][src_x_b_index];
            //            p1 = src_array[i][j];
            //            p2 = src_array[i + 1][j];
            //            p3 = src_array[i + 1][j + 1];
            //            p4 = src_array[i][j + 1];

            /*3. dst(目标）数值计算*/
            dst_array[i][j] = bilinear_interpolation_cal(rateX, rateY, p1, p2, p3, p4);
        }
    }
}
/*
 * @BRIEF: 双立方插值
 * @param:
 * @resutl:
 */
void my_interPolation::biCubic_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num) {
    ;
}
/*
 * @brief: 非线性插值-权重插值
 * @param_in；
 * @result:
 */
void my_interPolation::nonlinear_interpolation(QVector<QVector<uint32_t>> *array, uint8_t dst_row_num, uint8_t dst_column_num) {
    ;
}

/*
 * @BRIEF:
 */
void my_interPolation::median_filter() {
    ;
}

/*
 * @brief:
 */
void my_interPolation::gaussian_filter() {
    ;
}

/*
 * @brief:
 */
void my_interPolation::kalman_filter() {
    ;
}

/*
 * @brief:
 */
void my_interPolation::bilateral_filter() {
    ;
}

/*
 * @brief:
 */
void my_interPolation::guide_filter() {
    ;
}
/*
 * @brief:
 */
void my_interPolation::Convolution_filter() {
    ;
}
