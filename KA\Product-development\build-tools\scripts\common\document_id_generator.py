#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一文档ID生成器

提供统一的文档ID生成逻辑，避免各个模块重复实现。
遵循"单一来源"原则，所有文档ID生成都通过此模块。
"""

import re
import hashlib
from typing import List, Dict, Optional
from pathlib import Path


class DocumentIdGenerator:
    """统一的文档ID生成器"""
    
    @staticmethod
    def generate_new_id(component_code: str, existing_ids: List[str]) -> str:
        """
        生成新的文档ID
        
        Args:
            component_code: 组件代码 (如 REQ, DES, DEV 等)
            existing_ids: 现有的ID列表
            
        Returns:
            str: 新的文档ID，格式为 {component_code}{序号:03d}
        """
        # 过滤出当前组件的ID
        component_ids = [id for id in existing_ids if id.startswith(component_code)]
        
        # 如果没有现有ID，从001开始
        if not component_ids:
            return f"{component_code}001"
        
        # 找出最大的数字部分
        max_num = 0
        for id in component_ids:
            # 提取数字部分
            match = re.search(r'(\d+)', id)
            if match:
                num = int(match.group(1))
                max_num = max(max_num, num)
        
        # 生成新ID
        return f"{component_code}{(max_num + 1):03d}"
    
    @staticmethod
    def generate_hash_based_id(component_code: str, file_path: Path) -> str:
        """
        基于文件路径生成文档ID（用于扫描场景）
        
        Args:
            component_code: 组件代码
            file_path: 文件路径
            
        Returns:
            str: 基于路径哈希的文档ID
        """
        # 计算相对路径的哈希值
        relative_path = str(file_path.name)  # 使用文件名
        hash_value = hashlib.md5(relative_path.encode()).hexdigest()[:6]
        return f"{component_code}{hash_value.upper()}"
    
    @staticmethod
    def extract_existing_id_from_content(content: str) -> Optional[str]:
        """
        从文档内容中提取现有的文档ID
        
        Args:
            content: 文档内容
            
        Returns:
            Optional[str]: 找到的文档ID，如果没有则返回None
        """
        # 查找文档ID模式
        id_patterns = [
            r'文档ID[：:]\s*([A-Z]+\d+)',
            r'ID[：:]\s*([A-Z]+\d+)',
            r'-\s*文档ID[：:]\s*([A-Z]+\d+)',
            r'doc_id[：:]?\s*([A-Z]+\d+)'
        ]
        
        for pattern in id_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return None
    
    @staticmethod
    def validate_id_format(doc_id: str) -> bool:
        """
        验证文档ID格式是否正确
        
        Args:
            doc_id: 要验证的文档ID
            
        Returns:
            bool: 格式正确返回True
        """
        # 标准格式：字母+数字，如 REQ001, DES002
        pattern = r'^[A-Z]+\d+$'
        return bool(re.match(pattern, doc_id))
    
    @staticmethod
    def parse_id_components(doc_id: str) -> Dict[str, str]:
        """
        解析文档ID的组成部分
        
        Args:
            doc_id: 文档ID
            
        Returns:
            Dict[str, str]: 包含component和number的字典
        """
        match = re.match(r'^([A-Z]+)(\d+)$', doc_id)
        if match:
            return {
                'component': match.group(1),
                'number': match.group(2)
            }
        else:
            return {'component': '', 'number': ''} 