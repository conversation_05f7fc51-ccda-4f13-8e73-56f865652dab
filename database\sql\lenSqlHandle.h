#ifndef _LEN_SQL_HANDLE_H_
#define _LEN_SQL_HANDLE_H_

#include <QMessageBox>
#include <QDebug>
#include "myConnSql.h"


class CLenSqlHandle{ //:public QObject
//    Q_OBJECT
public:
    explicit CLenSqlHandle();
    ~CLenSqlHandle();

    //* process items
    static QMap<int, QString> mm_mes_error_discribe;

    //* mes ERROR
    typedef union {
        uint16_t errors;
        struct {
           uint16_t          conn_wo_db          :1;
           uint16_t          conn_xsub2_db       :1;
           uint16_t          query_table_err     :1;
           uint16_t          exec_error          :1; //
           uint16_t          wo_miss             :1; //dont have this order
           uint16_t          wo_status_cls       :1;
//           uint16_t          wo_status_unknow    :1; //unnormal wo status
           uint16_t          nbr_tag_miss        :1;
           uint16_t          trnbr_miss          :1; //work index missing
           uint16_t          save_data_false     :1;
        } all_error;
    } UMesErrors;

    //* ALL TABLES NAME
    enum EAllTable{
        e//
    };

    //*
    typedef struct{
        QString             table_name;
//        QSqlDatabase        db;
        QString             conn;
    } ST_DATABASE;

    //* 工单表
    typedef struct {
        QString                     wo_status; //wor order status
        QString                     wo_lot; //work order list
        QString                     domain;
    } StWorkOrder;


    //* mes 标签生成表
    typedef struct {
        QString                     mcuid; //芯片ID
        QString                     work_order; //工单
        QString                     domain;
    } StXpiddet; //标签生成

    typedef struct {
        QString                      domain; //根据标签号设置域名
        uint32_t                     trnbr; //事务号
        QString                      nbr; //产品编号，由det 标签表单生成
        QString                      nbr_pre; //上一个产品编号
        QString                      op; //工位号，产线号的意思
        QString                      userid; //操作员
        QString                      date; //操作时间 year/month/date
        uint32_t                     time; //单位：s（h m s）
        bool                         rslt; //结果 1/0
        bool                         rslt_pre;
        QString                      rsn_code; //
        uint32_t                     param[9];
    } StResultToMes;

    static MyConnSql::StSqlConnInfo mst_lens_mstr_database; //
    static MyConnSql::StSqlConnInfo mst_lens_main_database;
    static const QMap<uint, QString> msm_len_mes_error;

    bool connectTestDB();
    bool connectWorkOrderDB();
    bool connectXSub2DetDB();
    uint16_t checkWorkOrder(StWorkOrder *st_work_order_);
    uint16_t searchTagNumberByMCUID(const StXpiddet &xpid_det, QString &nbr);
    uint16_t searchTagNumber(const StXpiddet &xpid_det, QString &nbr, const bool &rewrite);
    uint16_t printfTagTable(const StXpiddet &xpid_det);
    uint16_t loadMesData(StResultToMes *st_mes_data_);
    uint16_t saveMesData(const StXpiddet &xpid_det, StResultToMes *st_mes_data_);

private:

    QSqlDatabase m_db; //用静态变量，会导致找不到ODBC driver

};

#endif
