# lenAdjust 功能异常日志汇总

| 序号 | 异常问题 | 功能模块 | 日期 | 版本 | 原因 | 修改点 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | 子线程设备切换闪退 | len adjust | 2023-09-12 | 1.0.A | 主线程、子线程共享m_task_id，修改时未互锁 | 见软件日志 | - |
| 2 | 修改后指令无法保存至xml本地文件 | ss_com | 2023-09-11 | 1.6.B | 本地 xx.xml文件属性只读 | - | - |
| 3 | 实际有返回，软件无显示 | ss_com | 2024-08-06 | 1.6.14 | 与编译方式有关 | - | ack 指令不显示，验证debug和minimum release build type→正常 |
| 4 | TOF光路耦合异常 | TOF光路耦合 | 2024.3.1 | TOF光路耦合_V1.2.1_24.1.22 | - | - | 错误提示：MES: 3. 执行失败 / task: eVERSION fatal |
| 5 | 模式切换接收ack指令异常 | len adjust | 2023-09-11 | 1.0.A | ack指令： a5 16 a1 12 00 a5 02 ….，num 字节缺少1byte，与后一帧融合，导致字节数异常，一直缓存数据 | 见软件日志 | - |
| 6 | mes数据上传时间过长 | len adjust | 2023-08-30 | 1.0.9 | 事务号查找未加入domin filter，时间过长 | 见软件日志 | - |
