#ifndef _TABLE_VIEW_MODULE_H
#define _TABLE_VIEW_MODULE_H

#include <QDockWidget>
#include <QImage>
#include <QTableWidget>
#include "ITable.h"

class CTableViewModule: public ITable
{
public:
    explicit CTableViewModule(QTableWidget* table_, const StTableInfo &table_info);
    ~CTableViewModule();


    const QString process_normal_sheetStyle = "QTextEdit{"
                                              "min-width: 300px;"
                                              "min-height: 50px;"
                                              "font: 10pt;" //'Agency FB'
            "border-radius: 6px;"
            "border:2px solid black;"
            "background: rgb(255,255,255);"
            "color: rgb(0, 0, 0);}"; /*浅黑*/

    const QString process_unnormal_sheetStyle = "QTextEdit{"
                                                "min-width: 300px;"
                                                "min-height: 50px;"
                                                "font: 10pt;" // 'Agency FB'
            "border-radius: 6px;"
            "border:2px solid black;"
            "background: rgb(255,255,255);"
            "color: rgb(255, 0, 0);"; /*浅黑*/


    //* 光斑显示
    void tableShow(const uint8_t &tf_x, const uint8_t &tf_y);

    //* 目标区域凸显
    void targetFaculaAreaShow(); //const uint8_t &tf_x, const uint8_t &tf_y, const bool &is_left_2_right);

    //* 灰度图显示
    void greyMapShow(const uint &max, const QVector<QVector<uint32_t>> &matrix);

private:
    QTableWidget* m_table_;

    StTableInfo mst_table_info;
    QVector<QTableWidgetItem*>           mv_table_item;

    void tableInit();
};

#endif // CTableViewModule_H
