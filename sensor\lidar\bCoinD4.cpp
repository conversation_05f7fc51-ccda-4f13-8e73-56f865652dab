#include <QDebug>

#include "bCoinD4.h"
#include "bottomBoardP.h"

CBcoinD4::CBcoinD4(IComm *port_) :
    im_port_(port_)
{
    m_protocol_ = new CBottomBoardP;

    //* 此处应该可以选择数据接收方式
    im_port_->setBufferSize(2000); //与waitForReadyRead() 阻塞方式组合使用 一个一个字节的接收和解析

}

CBcoinD4::~CBcoinD4() {
    if(m_protocol_ != nullptr) delete m_protocol_;
}

void CBcoinD4::cmd_init(void)
{

}

QByteArray CBcoinD4::portDataRead()
{
    return im_port_->read(5);
}

void CBcoinD4::icom_change_interface(IComm *port_) {

}

bool CBcoinD4::readInfo(const uint8_t &id, const uint16_t &data) {

}

bool CBcoinD4::start(const uint8_t &id, const QByteArray &data) {

}

bool CBcoinD4::stop(const QByteArray &data) {

}

bool CBcoinD4::changeSpeed(const QByteArray &data) {

}

bool CBcoinD4::interactionParsing(QByteArray str, const int &length) {

}

