#ifndef _SNESOR_COIND_H_
#define _SENSOR_COIND_H_

#include "ITopBoard.h"
#include "processListA.h"
#include "pointcloud.h"
//#include "ILaser.h"
//#include "IPhotonSensor.h"

class CSensorCoinD:public ITopBoard {
  Q_OBJECT
public:
    CSensorCoinD(IComm *port_);
    ~CSensorCoinD();

    enum EProtocolId{
      kTriMode = 0xA0, //(模式切换) 三⾓法
      kTofMode = 0xA1, //(模式切换) tof
      kSysParam = 0xA2, //系统参数
      kMcuId = 0xA3, //写mcu ID
      kVersionBuad = 0xA4, //version buad
      kMeasureRange = 0xA5, //写测距范围
      kCalibrationParam = 0xA6, //写矫正参数
      kTgParam = 0xA7, //写温度补偿参数
      kMaterialParam = 0xA8, //写材质补偿参数
      kHisAndFacula = 0xA9, //写直⽅图与spad的频率
      kTemperature = 0xAA, //温度
      kRegisterSetting = 0xAB, //写寄存器
      kZeroAngleSetting = 0xAC, //零度⾓设置
      kLaserControl = 0xAD, //激光器控制
      kLedControl = 0xAE, //led控制
      kDebugSetting = 0xBF, //DEBUG1
      kDebugSetting2 = 0xBE, //DEBUG2
      kDebugSetting3 = 0xBD, //DEBUG3 在芯世界⽅案上⽤于显⽰不同的led逻辑
      kDebugSetting4 = 0xBC, //DEBUG4
      kSize,
    };

    /*模式与枚举*/
    enum EMode{
      KNone                                     = 0x0000,
      kTriStopMode                              = 0xA5FF,
      kTriTestMode  				= 0xA5FE,
      kTriFaculaMode                      	= 0xA5FD,
      kTriScanMode  				= 0xA5FC,
      ktRICalibrationMode                       = 0xA5FB,

      kTofStopMode  				= 0xA5FF,
      kTofHistMode  				= 0xA5FE,
      kTofFaculaMode                		= 0xA5FD,
      kTofScanMode  				= 0xA5FC,
      kTofCalibrationMode                       = 0xA5FB,
      kTofSinglePoint				= 0xA5FA,
      kTofSize                                  ,
    };

    QByteArray portDataRead(void) override;
    void icom_change_interface(IComm *port_) override;
    bool unlockModule() override;
    bool modeChange(const uint16_t &mode) override;
    bool changeRigster() override;
    bool readInfo(const uint8_t &id, const uint16_t &data) override;

    bool interactionParsing(QByteArray str, int length) override;
    bool greyParsing(QByteArray str, int length) override;
    bool histPrasing(QByteArray str, int length) override;
    bool cloudPrasing(QByteArray str, int length) override;

signals:
    void dataOutput(ITopBoard::ECommStep step, ECommStatus status, QByteArray bytes);

private:
    IComm *im_port_ = nullptr;
    QByteArray m_str_send; //指令数据
    const QByteArray m_unlock_cmd = QByteArray::fromHex("A505BE1E0000");

    std::vector<StPointCloud3D>     m_scanVec;
    std::vector<StPointCloud3D>     m_scanVecShow;
    SCAN_POINTCLOUD                 m_scanPointCloud;

    uint8_t m_version;
    uint16_t m_healthCode, m_mcuVoltage;

    void cmd_init();
};



#endif
