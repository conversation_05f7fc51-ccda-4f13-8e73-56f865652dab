#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QDialog>
#include "registerdialog.h"

namespace Ui {
class loginDialog;
}

class loginDialog : public QDialog
{
    Q_OBJECT

public:
    explicit loginDialog(QWidget *parent = nullptr);
    ~loginDialog();

private slots:
    void on_pButtonLogin_clicked();

    void on_pButtonRegister_clicked();

private:
    Ui::loginDialog *ui;
    registerDialog *m_regDialog_ptr = nullptr;
};

#endif // LOGINDIALOG_H
