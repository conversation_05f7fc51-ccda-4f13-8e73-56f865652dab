#!/usr/bin/env python3
"""
文档关联追踪工具 - 分析和管理文档之间的关联关系
用法: python doc_trace.py --input="docs/" --output="doc_relations.md"
"""

import os
import sys
import argparse
import json
import re
from pathlib import Path
import networkx as nx
import matplotlib.pyplot as plt

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="文档关联追踪工具")
    parser.add_argument('--input', required=True, help="文档目录路径")
    parser.add_argument('--output', default="doc_relations.md", help="输出报告路径")
    parser.add_argument('--format', default="markdown", choices=["markdown", "html", "graph"], help="输出格式")
    parser.add_argument('--depth', type=int, default=2, help="关联深度")
    parser.add_argument('--show-weights', action='store_true', help="显示关联权重")
    return parser.parse_args()

def find_documents(input_dir):
    """查找所有文档文件"""
    doc_files = []
    for ext in ['.md', '.txt', '.rst']:
        doc_files.extend(list(Path(input_dir).rglob(f'*{ext}')))
    return doc_files

def extract_links(content):
    """提取文档中的链接"""
    # Markdown链接模式
    md_links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
    # Wiki链接模式
    wiki_links = re.findall(r'\[\[([^\]]+)\]\]', content)
    return md_links, wiki_links

def build_document_graph(doc_files):
    """构建文档关联图"""
    G = nx.DiGraph()
    
    for doc_file in doc_files:
        try:
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加文档节点
            doc_name = doc_file.stem
            G.add_node(doc_name, path=str(doc_file))
            
            # 提取链接
            md_links, wiki_links = extract_links(content)
            
            # 添加Markdown链接
            for link_text, link_url in md_links:
                target = Path(link_url).stem
                if G.has_node(target):
                    G.add_edge(doc_name, target, type='md', text=link_text)
            
            # 添加Wiki链接
            for link in wiki_links:
                target = link.split('|')[0].strip()
                if G.has_node(target):
                    G.add_edge(doc_name, target, type='wiki')
        
        except Exception as e:
            print(f"警告: 处理文件 {doc_file} 时出错: {str(e)}")
    
    return G

def analyze_relationships(G, depth=2):
    """分析文档关联关系"""
    relationships = {
        'nodes': [],
        'edges': [],
        'statistics': {
            'total_docs': len(G.nodes()),
            'total_links': len(G.edges()),
            'isolated_docs': 0,
            'most_referenced': [],
            'most_referencing': []
        }
    }
    
    # 分析节点
    for node in G.nodes():
        in_degree = G.in_degree(node)
        out_degree = G.out_degree(node)
        
        node_data = {
            'name': node,
            'path': G.nodes[node]['path'],
            'in_degree': in_degree,
            'out_degree': out_degree,
            'is_isolated': in_degree == 0 and out_degree == 0
        }
        relationships['nodes'].append(node_data)
        
        if node_data['is_isolated']:
            relationships['statistics']['isolated_docs'] += 1
    
    # 分析边
    for source, target, data in G.edges(data=True):
        edge_data = {
            'source': source,
            'target': target,
            'type': data.get('type', 'unknown'),
            'text': data.get('text', '')
        }
        relationships['edges'].append(edge_data)
    
    # 更新统计信息
    in_degrees = [(node, G.in_degree(node)) for node in G.nodes()]
    out_degrees = [(node, G.out_degree(node)) for node in G.nodes()]
    
    relationships['statistics']['most_referenced'] = sorted(in_degrees, key=lambda x: x[1], reverse=True)[:5]
    relationships['statistics']['most_referencing'] = sorted(out_degrees, key=lambda x: x[1], reverse=True)[:5]
    
    return relationships

def generate_report(relationships, output_path, format_type, show_weights=False):
    """生成关联报告"""
    if format_type == "markdown":
        generate_markdown_report(relationships, output_path, show_weights)
    elif format_type == "html":
        generate_html_report(relationships, output_path, show_weights)
    elif format_type == "graph":
        generate_graph_visualization(relationships, output_path, show_weights)

def generate_markdown_report(relationships, output_path, show_weights):
    """生成Markdown格式报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        # 写入标题
        f.write("# 文档关联分析报告\n\n")
        
        # 写入统计信息
        f.write("## 统计信息\n\n")
        stats = relationships['statistics']
        f.write(f"- 总文档数: {stats['total_docs']}\n")
        f.write(f"- 总链接数: {stats['total_links']}\n")
        f.write(f"- 孤立文档数: {stats['isolated_docs']}\n\n")
        
        # 写入最常被引用的文档
        f.write("### 最常被引用的文档\n\n")
        for doc, count in stats['most_referenced']:
            f.write(f"- {doc}: {count} 次引用\n")
        f.write("\n")
        
        # 写入最常引用其他文档的文档
        f.write("### 最常引用其他文档的文档\n\n")
        for doc, count in stats['most_referencing']:
            f.write(f"- {doc}: {count} 次引用\n")
        f.write("\n")
        
        # 写入文档关联关系
        f.write("## 文档关联关系\n\n")
        for node in relationships['nodes']:
            f.write(f"### {node['name']}\n\n")
            f.write(f"- 路径: {node['path']}\n")
            if show_weights:
                f.write(f"- 入度: {node['in_degree']}\n")
                f.write(f"- 出度: {node['out_degree']}\n")
            f.write("\n")

def generate_html_report(relationships, output_path, show_weights):
    """生成HTML格式报告"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>文档关联分析报告</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .stats { background-color: #f5f5f5; padding: 20px; border-radius: 5px; }
            .document { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h1>文档关联分析报告</h1>
        
        <div class="stats">
            <h2>统计信息</h2>
            <p>总文档数: {total_docs}</p>
            <p>总链接数: {total_links}</p>
            <p>孤立文档数: {isolated_docs}</p>
        </div>
        
        <h2>最常被引用的文档</h2>
        <ul>
    """.format(
        total_docs=relationships['statistics']['total_docs'],
        total_links=relationships['statistics']['total_links'],
        isolated_docs=relationships['statistics']['isolated_docs']
    )
    
    for doc, count in relationships['statistics']['most_referenced']:
        html_content += f"<li>{doc}: {count} 次引用</li>"
    
    html_content += """
        </ul>
        
        <h2>最常引用其他文档的文档</h2>
        <ul>
    """
    
    for doc, count in relationships['statistics']['most_referencing']:
        html_content += f"<li>{doc}: {count} 次引用</li>"
    
    html_content += """
        </ul>
        
        <h2>文档关联关系</h2>
    """
    
    for node in relationships['nodes']:
        html_content += f"""
        <div class="document">
            <h3>{node['name']}</h3>
            <p>路径: {node['path']}</p>
        """
        
        if show_weights:
            html_content += f"""
            <p>入度: {node['in_degree']}</p>
            <p>出度: {node['out_degree']}</p>
            """
        
        html_content += "</div>"
    
    html_content += """
    </body>
    </html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

def generate_graph_visualization(relationships, output_path, show_weights):
    """生成图形可视化"""
    G = nx.DiGraph()
    
    # 添加节点和边
    for node in relationships['nodes']:
        G.add_node(node['name'], **node)
    
    for edge in relationships['edges']:
        G.add_edge(edge['source'], edge['target'], **edge)
    
    # 设置图形布局
    pos = nx.spring_layout(G)
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 绘制节点
    nx.draw_networkx_nodes(G, pos, node_color='lightblue', node_size=500)
    
    # 绘制边
    nx.draw_networkx_edges(G, pos, edge_color='gray', arrows=True)
    
    # 绘制标签
    nx.draw_networkx_labels(G, pos)
    
    # 如果显示权重，添加边标签
    if show_weights:
        edge_labels = {(u, v): d.get('text', '') for u, v, d in G.edges(data=True)}
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels)
    
    plt.title("文档关联关系图")
    plt.axis('off')
    
    # 保存图形
    plt.savefig(output_path, format='png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    args = parse_arguments()
    
    # 查找文档文件
    doc_files = find_documents(args.input)
    if not doc_files:
        print(f"错误: 在目录 {args.input} 中未找到文档文件")
        sys.exit(1)
    
    # 构建文档关联图
    G = build_document_graph(doc_files)
    
    # 分析关联关系
    relationships = analyze_relationships(G, args.depth)
    
    # 确保输出目录存在
    output_path = Path(args.output)
    output_dir = output_path.parent
    if not output_dir.exists():
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成报告
    generate_report(relationships, str(output_path), args.format, args.show_weights)
    
    print(f"成功: 已生成文档关联报告 {args.output}")

if __name__ == "__main__":
    main() 