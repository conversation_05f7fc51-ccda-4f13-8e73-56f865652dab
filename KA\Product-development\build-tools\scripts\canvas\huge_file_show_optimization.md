# Canvas大文件显示优化方案

## 问题描述

对于文档数量较多的项目（如13_Yapha-Laser-DTof2dMS项目有139个文档），如果都以卡片形式添加到Canvas中，会导致以下问题：

1. **性能问题**：Canvas渲染大量卡片会导致卡顿
2. **视觉混乱**：过多卡片影响整体布局的可读性
3. **内存占用**：大量DOM元素消耗内存资源

## 优化方案分析

### 方案1：链接方式显示（用户建议）

**实现方式**：
```json
{
  "id":"DEV006558BB8",
  "type":"link",
  "url":"obsidian://open?vault=MyVault&path=test_single_layer/development/firmware_projects/06_Nova-A2_old/yapha_nova_A2/yapha_nova_A2_HC88L051/HEDMIN.pdf",
  "x":3480,"y":1683,
  "width":200,"height":80,
  "color":"#96CEB4"
}
```

**优点**：
- ✅ 减少Canvas节点数量，提升性能
- ✅ 链接节点更小，节省空间
- ✅ 点击可直接打开文档
- ✅ 保持文档的可访问性

**缺点**：
- ❌ 失去文档预览功能
- ❌ 无法显示文档内容摘要
- ❌ 链接节点信息量较少

### 方案2：分层显示策略

**核心思想**：根据文档重要性和访问频率分层显示

**实现策略**：
1. **第一层（卡片显示）**：
   - 重要文档（README、主要设计文档）
   - 最近修改的文档
   - 用户标记的重点文档

2. **第二层（链接显示）**：
   - 辅助文档
   - 历史版本文档
   - 参考资料

3. **第三层（折叠显示）**：
   - 大量同类文档可折叠成一个组
   - 点击展开显示详细列表

### 方案3：智能加载策略

**实现方式**：
1. **视口加载**：只渲染当前视口内的文档卡片
2. **懒加载**：滚动时动态加载新区域的卡片
3. **虚拟化**：使用虚拟滚动技术

### 方案4：混合显示模式

**核心思想**：结合多种显示方式

**实现策略**：
1. **项目组级别**：
   - 小项目组（<10个文档）：全部显示为卡片
   - 中项目组（10-30个文档）：重要文档显示为卡片，其他显示为链接
   - 大项目组（>30个文档）：显示为折叠组 + 代表性文档

2. **文档类型级别**：
   - 设计文档、需求文档：优先显示为卡片
   - 代码文件、日志文件：优先显示为链接
   - 图片、PDF：根据大小决定显示方式

## 推荐方案：智能混合显示

### 实现逻辑

```python
def determine_display_mode(document, project_group_size, document_importance):
    """
    确定文档的显示模式
    
    Args:
        document: 文档信息
        project_group_size: 项目组文档数量
        document_importance: 文档重要性评分
    
    Returns:
        str: 'card', 'link', 'collapsed'
    """
    # 重要文档始终显示为卡片
    if document_importance >= 8:
        return 'card'
    
    # 小项目组全部显示为卡片
    if project_group_size <= 10:
        return 'card'
    
    # 中项目组混合显示
    elif project_group_size <= 30:
        if document_importance >= 5:
            return 'card'
        else:
            return 'link'
    
    # 大项目组主要显示为链接
    else:
        if document_importance >= 7:
            return 'card'
        else:
            return 'link'
```

### 文档重要性评分标准

1. **文件类型权重**：
   - README.md, 设计文档: +3分
   - 需求文档, API文档: +2分
   - 代码文件, 配置文件: +1分
   - 日志文件, 临时文件: +0分

2. **修改时间权重**：
   - 最近7天: +2分
   - 最近30天: +1分
   - 超过30天: +0分

3. **文件大小权重**：
   - 适中大小(1KB-1MB): +1分
   - 过小(<1KB)或过大(>10MB): +0分

4. **路径深度权重**：
   - 根目录或一级目录: +2分
   - 二级目录: +1分
   - 三级及以上: +0分

### 配置参数

```json
{
  "canvas_optimization": {
    "max_cards_per_group": 15,
    "max_total_cards": 50,
    "link_node_size": {
      "width": 200,
      "height": 60
    },
    "card_node_size": {
      "width": 400,
      "height": 280
    },
    "importance_threshold": {
      "always_card": 8,
      "prefer_card": 5,
      "prefer_link": 3
    }
  }
}
```

## 实施建议

### 阶段1：基础优化
1. 实现文档重要性评分算法
2. 添加显示模式配置选项
3. 支持链接节点类型

### 阶段2：智能显示
1. 实现混合显示逻辑
2. 添加用户自定义重要性标记
3. 支持动态切换显示模式

### 阶段3：高级优化
1. 实现视口加载
2. 添加搜索和过滤功能
3. 支持自定义布局策略

## 结论

**推荐采用智能混合显示方案**，因为：

1. **平衡性能与功能**：既保证重要文档的可视性，又控制Canvas复杂度
2. **用户体验友好**：重要文档仍可预览，次要文档快速访问
3. **可配置性强**：用户可根据项目特点调整显示策略
4. **扩展性好**：可根据实际使用情况持续优化

该方案比单纯的链接方式更智能，比全卡片方式更高效，是当前最佳的优化方向。
