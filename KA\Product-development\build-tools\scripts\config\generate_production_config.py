#!/usr/bin/env python3
"""
生产配置生成器
负责生成项目的生产制造相关配置文件
"""

import os
import json
from pathlib import Path
import argparse


def generate_production_config(project_path=".", project_type="single_layer"):
    """生成生产配置文件"""
    
    # 创建配置目录
    config_dir = Path(project_path) / 'config'
    config_dir.mkdir(exist_ok=True)
    
    # 生产配置
    production_config = {
        "production_settings": {
            "bom_validation": True,
            "process_documentation": True,
            "quality_control": True,
            "regulatory_compliance": True
        },
        "outputs": {
            "bom_file": "production/bom.xlsx",
            "process_doc": "production/manufacturing_process.md",
            "quality_plan": "production/quality_control_plan.md"
        }
    }
    
    # 写入配置文件
    config_file = config_dir / 'production_config.json'
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(production_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 生产配置文件已生成: {config_file}")
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成生产配置文件')
    parser.add_argument('--project-path', default='.', help='项目路径')
    parser.add_argument('--project-type', default='single_layer', 
                       choices=['single_layer', 'multi_level'], help='项目类型')
    
    args = parser.parse_args()
    
    success = generate_production_config(args.project_path, args.project_type)
    
    if success:
        print("[+] 生产配置生成完成")
    else:
        print("[X] 生产配置生成失败")
        exit(1)


if __name__ == "__main__":
    main() 