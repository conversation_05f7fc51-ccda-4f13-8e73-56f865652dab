/*******************************
 * 通信指令交互
 *******************/

#include "spmsSerial.h"
#include <QThread>

CSpmsSerial::CSpmsSerial(QObject *parent, IPms *pms_):
  QObject(parent)
, m_task_id(0)
, mi_pms_(pms_)
{

}

CSpmsSerial::~CSpmsSerial() {}

void CSpmsSerial::loop(int task_id)
{
    Q_UNUSED(task_id);
    QByteArray arr;
    QByteArray device_arr;

//    qDebug() << "-i spms serial/";

    for(;;) {
        if(m_task_id == 1) {
            arr = mi_pms_->portDataRead();
            if(arr.length() > 0) {
                mi_pms_->interactionParsing(arr, arr.length());
            }
        }
        else if(m_task_id == 2) {
            arr = mi_pms_->portDataRead();
            if(arr.length() > 0) mi_pms_->dataParsing(arr, 100);
        }
        else if(m_task_id == 3) {
            //QThread::msleep(1);
        }
        else if(m_task_id == 0) {//空
            QThread::msleep(1);
        }
        else if(m_task_id < 0) {//退出线程 //不能用else，时序上存在问题

            break; //return
        }
    }
}

void CSpmsSerial::device_change_interface(IPms* pms_)
{
  if(pms_ != nullptr) {
      mi_pms_ = pms_;
      mi_pms_->m_strPre.clear();
    }
}

void CSpmsSerial::taskIdChange(const uint8_t &id)
{
    m_task_id = id;
//    if(id == 1);
//        mc_sensor_->m_strPre.clear();
//    else if(id == 2)

    if(mi_pms_->m_strPre.length() > 0) mi_pms_->m_strPre.clear();

//    qDebug() << "-i pms serial thread/ task id:" << m_task_id;
}
