#ifndef _IPLOT_H_
#define _IPLOT_H_

#include <QWidget>

class IPlot : public QWidget
{
public:
    IPlot(){};
    virtual ~IPlot(){};

    virtual void createFigure(void) = 0;
protected:
    typedef struct {
        uint16_t    start;
        uint16_t    end;
    } StRange;

    typedef struct {
        StRange     local; //
        QString     title;
        QString     key_label;
        StRange     key_range;
        QString     value_label;
        StRange     value_range;

    } StFigureInfo;

    typedef struct {
        float   index;
        float   value;
    } St2DiPoint; //
};

#endif
