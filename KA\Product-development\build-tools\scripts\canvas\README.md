# Canvas可视化模块

## 概述

Canvas可视化模块提供基于Obsidian Canvas的文档可视化和关联管理功能，支持与INDEX文件的双向同步。

### 核心功能

- **文档可视化**: 将项目文档以Canvas节点形式展示
- **双向同步**: INDEX文件↔Canvas文件实时同步
- **层级布局**: 组件级+项目级两层分组结构
- **动态布局**: 根据文档存在情况自动调整布局
- **增量更新**: 保留手动调整的节点位置和连线

### 设计原则

- **单一职责**: 专注于Canvas可视化功能
- **单一来源**: 布局配置统一管理
- **奥卡姆剃刀**: 采用最简有效的布局策略
- **双链引用**: 支持[[auto_sync_from_canvas_to_INDEX]]等双链语法

## 验证状态

**测试项目**: `example/test_single_layer`

| 功能 | 状态 | 说明 |
|------|------|------|
| **INDEX→Canvas同步** | ✅ 正常 | 自动创建目录组和文档节点 |
| **Canvas→INDEX同步** | ✅ 正常 | 关联关系同步到INDEX文件 |
| **层级分组** | ✅ 正常 | 组件级+项目级两层结构 |
| **动态布局** | ✅ 正常 | 根据文档存在情况调整位置 |

### 自动同步方法

| 方法 | 状态 | 推荐度 | 说明 |
|------|------|--------|------|
| **Watchdog监控** | ✅ 完全可用 | ⭐⭐⭐⭐⭐ | 推荐使用，开箱即用 |
| **Obsidian Plugin** | ⚠️ 部分可用 | ⭐⭐⭐ | 需要安装jq工具 |

## 功能与脚本对应表

| 功能 | 脚本 | 主要参数 | 使用场景 | 效果 |
|------|------|----------|----------|------|
| **INDEX→Canvas同步** | `auto_link_documents.py` | `--sync-to-canvas` | 文档可视化 | 生成Canvas节点和分组 |
| **Canvas→INDEX同步** | `../links/auto_link_documents.py` | `--sync-from-canvas` | 关联关系同步 | 将Canvas连线同步到INDEX |
| **完全重写Canvas** | `auto_link_documents.py` | `--sync-to-canvas --mode full` | 重置布局 | 重新生成所有节点位置 |
| **Canvas文件监控** | `canvas_watcher.py` | 无参数 | 实时同步 | 自动监控Canvas变化 |
| **同步状态验证** | `auto_link_documents.py` | `--validate-sync` | 问题诊断 | 检查同步一致性 |
| **Canvas统计信息** | `auto_link_documents.py` | `--stats` | 状态查看 | 显示节点和边统计 |

## 核心脚本详细说明

### `canvas_manager.py` - Canvas文件管理器

**职责**: Canvas文件的基础操作和数据管理

**核心功能**:
- JSON格式解析和生成
- 增量同步（保留手动调整）
- 格式化JSON输出（每个节点单独一行）
- 自动备份和恢复

**关键特性**:
- 智能合并算法，区分自动生成和手动创建的节点
- 手动调整保护机制
- Canvas到INDEX的关联同步

### `canvas_layout.py` - 布局管理器

**职责**: 节点位置计算和组件区域划分

**核心功能**:
- 层级分组布局（组件级+项目级）
- 动态位置计算（根据文档存在情况）
- 文档高度统一（280px）
- 重叠检测和避免

**布局策略**:

| 组件状态 | 布局策略 | 项目组起始位置 |
|----------|----------|----------------|
| **有组件级文档** | 文档在顶部，项目组在下方 | 文档底部+40px间距 |
| **无组件级文档** | 项目组直接从顶部开始 | 组件组标题下方+60px |

**组件区域配置**:

| 组件 | X坐标范围 | 说明 |
|------|-----------|------|
| **REQ** | 100-1100 | 需求管理 |
| **DES** | 1400-2400 | 设计文档 |
| **DEV** | 2700-3700 | 开发实施 |
| **DEL** | 4000-5000 | 交付物 |
| **PM** | 5300-6300 | 项目管理 |

### `auto_link_documents.py` - 主要集成脚本

**职责**: Canvas与INDEX文件的双向同步

**核心功能**:
- INDEX→Canvas同步（保留手动调整）
- Canvas→INDEX同步（关联关系）
- 同步状态验证和统计
- 新文档添加和管理

**命令行参数表**:

| 参数 | 功能 | 说明 |
|------|------|------|
| `--sync-to-canvas` | INDEX→Canvas同步 | 默认增量模式，保留手动调整 |
| `--sync-to-canvas --mode full` | 完全重写Canvas | 重置所有手动调整 |
| `--validate-sync` | 验证同步状态 | 检查一致性问题 |
| `--stats` | 显示统计信息 | 节点、边、分组统计 |
| `--add-document` | 添加新文档 | 需配合其他参数使用 |

**注意**：Canvas→INDEX同步功能已移至 `../links/auto_link_documents.py --sync-from-canvas`

### `canvas_watcher.py` - 实时监控脚本

**职责**: Canvas文件变化的实时监控和同步

**核心功能**:
- 实时监控Canvas文件变化
- 自动触发Canvas→INDEX同步
- 变化检测和增量处理
- 同步状态跟踪和日志记录

## 使用流程

### 基本工作流程

| 步骤 | 操作 | 命令 | 说明 |
|------|------|------|------|
| **1. 初始同步** | INDEX→Canvas | `--sync-to-canvas` | 生成Canvas文件 |
| **2. 可视化编辑** | Obsidian编辑 | 手动操作 | 调整布局和连线 |
| **3. 反向同步** | Canvas→INDEX | `--sync-from-canvas` | 同步关联关系 |
| **4. 持续维护** | 自动监控 | `canvas_watcher.py` | 保持实时同步 |

### 快速开始

```bash
# 1. 生成Canvas文件
python ../../scripts/canvas/auto_link_documents.py --sync-to-canvas

# 2. 在Obsidian中编辑product.canvas文件

# 3. 同步关联关系
python ../../scripts/canvas/auto_link_documents.py --sync-from-canvas

# 4. 启动自动监控（可选）
python ../../scripts/canvas/canvas_watcher.py
```

## 技术特性

### 增量同步机制

**保留的手动调整**:

- 节点的位置调整（x, y坐标）
- 节点的尺寸调整（width, height）
- 手动创建的连线（edges）
- 手动添加的节点（非文档节点）

**自动更新的内容**:

- 新增的文档节点
- 文档信息的更新（颜色、标题等）
- 新的目录分组
- 删除不存在的文档节点

### 路径格式

Canvas中使用项目相对路径格式：`项目名/相对路径`

## 故障排除

| 问题 | 现象 | 解决方案 |
|------|------|----------|
| **Canvas文件不存在** | 提示文件不存在 | 运行`--sync-to-canvas`生成 |
| **节点位置重置** | 手动调整被重置 | 使用增量模式，避免`--mode full` |
| **关联关系丢失** | 连线未同步到INDEX | 运行`--sync-from-canvas` |
| **路径格式错误** | 显示绝对路径 | 检查项目路径配置 |

## 最佳实践

- **定期备份**: Canvas文件包含手动调整，建议定期备份
- **增量同步**: 优先使用增量模式，保护手动调整
- **分阶段编辑**: 先完成文档结构，再进行Canvas布局调整
- **监控使用**: 频繁编辑的项目建议启用自动监控

## 相关文档

- [[auto_sync_from_canvas_to_INDEX]] - 自动同步配置
- [[CANVAS_AUTO_SYNC_GUIDE]] - 完整使用指南
- [[文档关联系统]] - 文档关联功能
- [[group_create_principle.md]]

**Q: 自动监控可以在后台运行吗？**
A: 可以，在支持后台任务的系统中可以作为后台服务运行，也可以配置为开机自启动。
