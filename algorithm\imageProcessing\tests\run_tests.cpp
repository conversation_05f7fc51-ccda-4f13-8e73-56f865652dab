#include "ImageProcessingTest.h"
#include "../factories/FilterFactory.h"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>

/**
 * @brief 运行所有滤波器测试的简单程序
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "========================================";
    qDebug() << "图像处理库滤波器测试程序";
    qDebug() << "版本: 1.0.0";
    qDebug() << "========================================";
    
    // 创建测试实例
    ImageProcessing::ImageProcessingTest tester;
    
    // 运行所有测试
    bool allTestsPassed = tester.runAllTests();
    
    // 额外测试：验证所有滤波器类型都能创建
    qDebug() << "\n--- 验证滤波器工厂 ---";
    
    auto& factory = ImageProcessing::FilterFactory::getInstance();
    
    // 测试所有滤波器类型
    QVector<ImageProcessing::FilterType> filterTypes = {
        ImageProcessing::FilterType::Kalman,
        ImageProcessing::FilterType::Convolution,
        ImageProcessing::FilterType::Median,
        ImageProcessing::FilterType::Gaussian,
        ImageProcessing::FilterType::Bilateral,
        ImageProcessing::FilterType::WeightedAverage
    };
    
    bool factoryTestPassed = true;
    for (auto filterType : filterTypes) {
        auto filter = factory.createFilter(filterType);
        if (filter) {
            qDebug() << "✓ 成功创建滤波器:" << factory.getTypeDescription(filterType);
        } else {
            qDebug() << "✗ 创建滤波器失败:" << factory.getTypeDescription(filterType);
            factoryTestPassed = false;
        }
    }
    
    // 测试预设配置
    qDebug() << "\n--- 验证预设配置 ---";
    for (auto filterType : filterTypes) {
        auto presets = factory.getSupportedPresets(filterType);
        qDebug() << factory.getTypeDescription(filterType) << "支持的预设:" << presets;
        
        if (!presets.isEmpty()) {
            auto filter = factory.createFilter(filterType);
            if (filter) {
                try {
                    factory.applyPresetConfiguration(filter.get(), filterType, presets.first());
                    qDebug() << "✓ 预设配置应用成功:" << presets.first();
                } catch (const std::exception& e) {
                    qDebug() << "✗ 预设配置应用失败:" << e.what();
                    factoryTestPassed = false;
                }
            }
        }
    }
    
    qDebug() << "========================================";
    if (allTestsPassed && factoryTestPassed) {
        qDebug() << "🎉 所有测试通过！图像处理库功能正常";
        std::cout << "\n✅ All tests passed! ImageProcessing library is working correctly.\n" << std::endl;
        return 0;
    } else {
        qDebug() << "❌ 部分测试失败，请检查实现";
        std::cout << "\n❌ Some tests failed. Please check the implementation.\n" << std::endl;
        return 1;
    }
}

/**
 * 编译说明：
 * 
 * 在build目录中运行：
 * ninja imageProcessing
 * 
 * 然后编译测试程序：
 * g++ -std=c++17 -I../algorithm/imageProcessing -I../algorithm/imageProcessing/tests \
 *     -I/path/to/qt/include -I/path/to/qt/include/QtCore \
 *     algorithm/imageProcessing/tests/run_tests.cpp \
 *     -L./lib -limageProcessing -lQt5Core -o test_filters
 * 
 * 运行测试：
 * ./test_filters
 */
