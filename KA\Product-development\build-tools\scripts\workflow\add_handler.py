#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Workflow Handler Management Utility
Simplifies adding new handlers to the workflow configuration.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Optional

# Import WorkflowManager if it's in the path
try:
    from workflow_manager import WorkflowManager
except ImportError:
    # If not, add the current directory to the path and try again
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.append(current_dir)
    try:
        from workflow_manager import WorkflowManager
    except ImportError:
        print("Error: Could not import WorkflowManager class.")
        sys.exit(1)

def add_handler(
    config_path: str,
    from_component: str,
    to_component: str,
    trigger_name: str,
    handler_type: str,
    handler_name: str,
    script_path: str,
    params_json: Optional[str] = None
) -> bool:
    """Add a new handler to the workflow configuration."""
    
    # Create workflow manager
    manager = WorkflowManager(config_path)
    
    # Parse params if provided
    params = {}
    if params_json:
        try:
            params = json.loads(params_json)
        except json.JSONDecodeError:
            print(f"Error: Invalid JSON in params: {params_json}")
            return False
    
    # Add the handler
    try:
        handler = manager.add_handler(
            from_component=from_component,
            to_component=to_component,
            trigger_name=trigger_name,
            handler_type=handler_type,
            handler_name=handler_name,
            script_path=script_path,
            params=params
        )
        
        print(f"Handler added successfully: {handler_name}")
        print(f"  From: {from_component}")
        print(f"  To: {to_component}")
        print(f"  Trigger: {trigger_name}")
        print(f"  Type: {handler_type}")
        print(f"  Script: {script_path}")
        if params:
            print(f"  Params: {json.dumps(params, indent=2)}")
        
        return True
    
    except Exception as e:
        print(f"Error adding handler: {e}")
        return False

def add_connection(
    config_path: str,
    from_component: str,
    to_component: str,
) -> bool:
    """Add a new connection to the workflow configuration."""
    
    # Create workflow manager
    manager = WorkflowManager(config_path)
    
    # Add the connection
    try:
        connection = manager.add_connection(
            from_component=from_component,
            to_component=to_component
        )
        
        print(f"Connection added successfully:")
        print(f"  From: {from_component}")
        print(f"  To: {to_component}")
        
        return True
    
    except Exception as e:
        print(f"Error adding connection: {e}")
        return False

def add_trigger(
    config_path: str,
    from_component: str,
    to_component: str,
    trigger_name: str,
    trigger_type: str = "event"
) -> bool:
    """Add a new trigger to the workflow configuration."""
    
    # Create workflow manager
    manager = WorkflowManager(config_path)
    
    # Add the trigger
    try:
        trigger = manager.add_trigger(
            from_component=from_component,
            to_component=to_component,
            trigger_name=trigger_name,
            trigger_type=trigger_type
        )
        
        print(f"Trigger added successfully:")
        print(f"  From: {from_component}")
        print(f"  To: {to_component}")
        print(f"  Name: {trigger_name}")
        print(f"  Type: {trigger_type}")
        
        return True
    
    except Exception as e:
        print(f"Error adding trigger: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Workflow Handler Management Utility")
    parser.add_argument("--config", default="config/workflow_config.json", help="Path to workflow configuration file")
    
    subparsers = parser.add_subparsers(dest="action", help="Action to perform")
    
    # Add connection parser
    conn_parser = subparsers.add_parser("add_connection", help="Add a new connection")
    conn_parser.add_argument("--from", dest="from_component", required=True, help="Source component")
    conn_parser.add_argument("--to", dest="to_component", required=True, help="Target component")
    
    # Add trigger parser
    trigger_parser = subparsers.add_parser("add_trigger", help="Add a new trigger")
    trigger_parser.add_argument("--from", dest="from_component", required=True, help="Source component")
    trigger_parser.add_argument("--to", dest="to_component", required=True, help="Target component")
    trigger_parser.add_argument("--name", dest="trigger_name", required=True, help="Trigger name")
    trigger_parser.add_argument("--type", dest="trigger_type", default="event", help="Trigger type")
    
    # Add handler parser
    handler_parser = subparsers.add_parser("add_handler", help="Add a new handler")
    handler_parser.add_argument("--from", dest="from_component", required=True, help="Source component")
    handler_parser.add_argument("--to", dest="to_component", required=True, help="Target component")
    handler_parser.add_argument("--trigger", dest="trigger_name", required=True, help="Trigger name")
    handler_parser.add_argument("--handler-type", choices=["script", "mcp_server"], required=True, help="Handler type")
    handler_parser.add_argument("--handler-name", required=True, help="Handler name")
    handler_parser.add_argument("--script", required=True, help="Script path")
    handler_parser.add_argument("--params", help="JSON string with handler parameters")
    
    args = parser.parse_args()
    
    if args.action == "add_connection":
        add_connection(args.config, args.from_component, args.to_component)
    
    elif args.action == "add_trigger":
        add_trigger(args.config, args.from_component, args.to_component, args.trigger_name, args.trigger_type)
    
    elif args.action == "add_handler":
        add_handler(
            args.config,
            args.from_component,
            args.to_component,
            args.trigger_name,
            args.handler_type,
            args.handler_name,
            args.script,
            args.params
        )
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 