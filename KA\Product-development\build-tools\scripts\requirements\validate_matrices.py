#!/usr/bin/env python
# 脚本路径: scripts/requirements/validate_matrices.py

import os
import re
import json
import argparse
from collections import defaultdict

def validate_matrix(matrix_path, level_id):
    """验证单个需求矩阵的有效性"""
    issues = []
    
    if not os.path.exists(matrix_path):
        return [f"错误: 矩阵文件不存在 - {matrix_path}"]
    
    with open(matrix_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查需求ID格式
    req_pattern = r'\| (L\d+_R\d+) \|'
    req_ids = re.findall(req_pattern, content)
    
    for req_id in req_ids:
        prefix = f"L{level_id}_"
        if not req_id.startswith(prefix):
            issues.append(f"警告: 需求ID前缀不匹配层级 - {req_id} 在 {matrix_path}")
    
    # 检查链接格式
    link_pattern = r'\[([^\]]+)\]\(#\)'
    links = re.findall(link_pattern, content)
    
    for link in links:
        if not re.match(r'L\d+_R\d+', link):
            issues.append(f"警告: 链接格式不正确 - {link} 在 {matrix_path}")
    
    return issues

def build_requirement_map(config_path):
    """构建需求ID到矩阵文件的映射"""
    req_map = {}
    level_instances = {}
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 构建层级实例映射
    for level in config['levels']:
        level_id = level['level_id']
        level_instances[level_id] = []
        
        for instance in level['instances']:
            instance_path = f"level_{level_id}_{instance}"
            matrix_path = f"{instance_path}/requirements/requirements_matrix.md"
            level_instances[level_id].append((instance, matrix_path))
    
    # 从矩阵中提取需求ID
    for level_id, instances in level_instances.items():
        for instance, matrix_path in instances:
            if os.path.exists(matrix_path):
                with open(matrix_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                req_pattern = r'\| (L\d+_R\d+) \|'
                req_ids = re.findall(req_pattern, content)
                
                for req_id in req_ids:
                    req_map[req_id] = matrix_path
    
    return req_map

def validate_links(config_path):
    """验证需求链接的有效性"""
    issues = []
    req_map = build_requirement_map(config_path)
    
    # 检查每个需求的链接
    for req_id, matrix_path in req_map.items():
        with open(matrix_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取该需求行
        req_pattern = rf'\| {req_id} \|(.*?)\|'
        req_match = re.search(req_pattern, content)
        
        if req_match:
            # 提取链接的需求ID
            link_pattern = r'\[([^\]]+)\]\(#\)'
            links = re.findall(link_pattern, req_match.group(0))
            
            for link in links:
                if link not in req_map:
                    issues.append(f"错误: 链接到不存在的需求 - {link} 在 {matrix_path}")
    
    return issues

def main():
    parser = argparse.ArgumentParser(description='验证需求矩阵')
    parser.add_argument('--config', required=True, help='层级配置文件路径')
    args = parser.parse_args()
    
    with open(args.config, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    all_issues = []
    
    # 验证每个层级的矩阵
    for level in config['levels']:
        level_id = level['level_id']
        for instance in level['instances']:
            matrix_path = f"level_{level_id}_{instance}/requirements/requirements_matrix.md"
            issues = validate_matrix(matrix_path, level_id)
            all_issues.extend(issues)
    
    # 验证链接有效性
    link_issues = validate_links(args.config)
    all_issues.extend(link_issues)
    
    # 输出结果
    if all_issues:
        print(f"发现 {len(all_issues)} 个问题:")
        for issue in all_issues:
            print(f"- {issue}")
        return 1
    else:
        print("验证通过，未发现问题")
        return 0

if __name__ == "__main__":
    exit(main()) 