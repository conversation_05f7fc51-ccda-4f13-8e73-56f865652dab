#ifndef _MODBUS_RTU_
#define _MODBUS_RTU_

#include <QByteArray>
#include <QObject>
#include "IDevicePtl.h"

class CMyModBus:public IDevicePtl{
public:
  CMyModBus(const uint8_t &device);
  ~CMyModBus();

  enum class EModbusRtuFrame {
      eHEADER_LEN = 0x06,
  };

  enum EFunctionCode{ //功能码
    eREAD_RW_COIL           = 0x01, //读输出线圈->读写
    eREAD_R_COIL            = 0x02, //读输入线圈
    eREAD_RW_REG            = 0x03, //读保存寄存器
    eREAD_R_REG             = 0x04, //读输入寄存器
    eWRITE_COIL             = 0x05, //写入线圈
    eWRITE_REG              = 0x06, //写入寄存器
    eWRITE_REG_CONT         = 0x10, //连续保存寄存器写入
  };

  //* 写入字
  typedef struct{
    uint8_t                         device_addr;
    uint8_t                         code; //
    uint16_t                        addr; //HSB
    uint16_t                        data;
    uint16_t                        crc16; //
  } StSingleWriteWord; //* 包含该类型协议所有输入数据

  //* 连续写入字
  typedef struct{
    uint8_t                         device_addr;
    uint8_t                         code; //
    uint16_t                        addr; //HSB
    uint16_t                        reg_num; //HSB
    uint8_t                         bytes_num; //字节数还是数据数
    QVector<uint16_t>               data;
    uint16_t                        crc16; //
  } StMultiWriteWord; //* 包含该类型协议所有输入数据
#if 0
  //* 写入双字（一个寄存器（地址）双字）
  typedef struct{
    uint8_t                         device_addr;
    uint8_t                         code; //
    uint16_t                        addr; //HSB
    uint32_t                        data;
    uint16_t                        crc16; //
  } StSingleWriteDWord;

  //* 连续写入（一个寄存器（地址）双字）
  typedef struct{
    uint8_t                         device_addr;
    uint8_t                         code; //
    uint16_t                        addr; //HSB
    uint16_t                        reg_num; //HSB
    uint8_t                         bytes_num; //字节数还是数据数
    QVector<uint32_t>               data;
    uint16_t                        crc16; //
  } StMultiWriteDWord;
#endif
  typedef struct{
    uint8_t                         device_addr;
    uint8_t                         code; //
    uint16_t                        addr; //HSB
    uint16_t                        reg_num; //HSB
    uint16_t                        crc16; //
  } StRead; //* 包含该类型协议所有输入数据

//  typedef struct{
//    uint8_t                         device_addr;
//    EFunctionCode                   code; //
//    uint16_t                        reg_addr; //HSB
//    uint16_t                        reg_num; //HSB
//    uint8_t                         write_bytes_num; //
//    QByteArray                      data;
//    uint16_t                        crc16; //
//  } StModus;

  QByteArray getControlCmd(const StInput &input_data) override;
  QByteArray getWriteWordCmd(const StInput &input_data) override;
//  QByteArray getWriteDWordCmd(const StInput &input_data) override;
  QByteArray getReadCmd(const StInput &input_data) override;

  static uint16_t crc16(QByteArray puchMsg, unsigned int usDataLen)
  {
    unsigned char uchCRCHi = 0xFF ;   //*高CRC字节初始化
    unsigned char uchCRCLo = 0xFF ;   //*低CRC字节初始化
    unsigned long uIndex;            // CRC循环中的索引

    QByteArray::Iterator it = puchMsg.begin();
    while (usDataLen--)               //传输消息缓冲区
      {
        uIndex = uchCRCHi ^ (uchar)*(it++) ; // 计算CRC
        uchCRCHi = uchCRCLo ^ auchCRCHi[uIndex] ;
        uchCRCLo = auchCRCLo[uIndex] ;
      }

    return (uchCRCHi << 8 | uchCRCLo);
  }

private:
  StSingleWriteWord *mst_single_w_word_ = nullptr;
//  StSingleWriteDWord *mst_single_w_dword_ = nullptr;
  StMultiWriteWord *mst_multi_w_word_ = nullptr;
//  StMultiWriteDWord *mst_multi_w_dword_ = nullptr;
  StRead *mst_read_ = nullptr;

  const static unsigned char auchCRCHi[260];
  const static unsigned char auchCRCLo[260];// CRC低位字节值表
};

#endif
