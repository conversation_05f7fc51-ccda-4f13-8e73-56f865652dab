#ifndef _SENSOR_MINI_YJ_H_
#define _SENSOR_MINI_YJ_H_

#include "ITopBoard.h"
#include "IComm.h"
#include "processListA.h"
#include "pointcloud.h"

class CSensorMiniYJ:public ITopBoard{
public:
    CSensorMiniYJ(IComm *port_);
    ~CSensorMiniYJ();

    QByteArray portDataRead() override;
    void icom_change_interface(IComm *port_) override;
    bool unlockModule() override;
    bool modeChange(const uint16_t &mode) override;
    bool changeRigster() override;
    bool readInfo(const uint8_t &id, const uint16_t &data) override;

    bool interactionParsing(QByteArray str, int length) override;
    bool greyParsing(QByteArray str, int length) override;
    bool cloudPrasing(QByteArray str, int length) override;
signals:
    void dataOutput(ECommStep, ECommStatus, QByteArray);

private:
    enum EProtocolId{
      eSTOP               = 0xB1, //
    };

    /*模式与枚举*/
    enum EMode{

    };

    IComm *im_port_ = nullptr;
    QByteArray m_str_send; //指令数据

    std::vector<StPointCloud3D>     m_scanVec;
    std::vector<StPointCloud3D>     m_scanVecShow;
    SCAN_POINTCLOUD                 m_scanPointCloud;

    uint8_t m_version;
    uint16_t m_healthCode, m_mcuVoltage;
};

#endif
