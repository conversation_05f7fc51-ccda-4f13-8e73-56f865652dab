# LA-T5 使用文档

**版本**: LA-T5 v1.4.4
**更新日期**: 2025-01-16

本目录包含LA-T5系统的完整使用文档，涵盖图像滤波器配置、最佳实践和故障排除指南。

## 📚 文档目录

### 核心使用指南
- **[[WeightedAverageFilter使用指南]]** - 加权均值滤波器详细使用说明 ⭐
- **[[图像滤波器配置说明]]** - 所有滤波器的配置参数和最佳实践
- **[[边界处理最佳实践]]** - 边界处理技术详解和选择指南

### 技术文档
- **[[../issues/weighted_average_boundary_fix.md]]** - v1.4.4重要修复报告
- **[[../../algorithm/imageProcessing/filters/weighted_average.ipynb]]** - 技术分析和验证文档
- **[[../../algorithm/imageProcessing/tests/test_report.md]]** - 测试验证报告

## 🔥 v1.4.4重要更新

### 边界处理优化
- 🔧 **WeightedAverageFilter**: 零填充 → 边缘复制
- 📈 **性能提升**: 边缘像素值提升40-90%
- ✅ **物理合理**: 更符合光斑衰减特性

### 配置流程修复
- 🔧 **重复定义消除**: 配置层和滤波器层职责分离
- ✨ **预设全面支持**: 所有权重模式现在可通过配置文件使用
- 📝 **文档完善**: 详细的使用指南和最佳实践

## 🚀 快速开始

### 1. 基本配置
```ini
[WeightedAverage]
enabled=true
kernel_size=3
preset=center_weighted
strength=1.0
```

### 2. 代码使用
```cpp
#include "WeightedAverageFilter.h"

auto filter = std::make_unique<WeightedAverageFilter>();
filter->setPredefinedWeights("center_weighted");
filter->apply(image);
```

### 3. 效果验证
- 边缘像素值显著提升
- 中心点保持稳定
- 更符合光斑物理特性

## 📖 使用建议

### 光斑处理场景 ⭐
**推荐配置**: WeightedAverageFilter + center_weighted
- 适合光斑中心增强
- 边界处理优化
- 物理特性符合

### 噪声抑制场景
**推荐配置**: MedianFilter + GaussianFilter
- 有效去除噪声
- 保持图像质量

### 边缘检测场景
**推荐配置**: WeightedAverageFilter + edge_enhance
- 增强边缘对比度
- 保持边缘清晰

## 🔧 故障排除

### 常见问题
1. **边缘像素偏小** → 升级到v1.4.4
2. **配置预设不生效** → 检查配置文件格式
3. **性能问题** → 调整kernel_size参数

### 调试技巧
- 启用详细日志
- 分步验证配置
- 对比修复前后效果

## 📞 技术支持

- **文档问题**: 查看相关使用指南
- **配置问题**: 参考配置说明文档
- **技术问题**: 联系开发团队

---

**文档维护**: 请保持文档与代码版本同步
**反馈建议**: 欢迎提供使用反馈和改进建议
