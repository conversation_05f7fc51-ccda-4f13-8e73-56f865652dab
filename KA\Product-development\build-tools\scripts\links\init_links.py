#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文档关联系统初始化脚本
负责文档注册、语义关联发现、双链知识管理等文档级别功能
基于 xx_INDEX.md 文件实现文档注册和关联管理
"""

import os
import sys
import json
import argparse
import datetime
from pathlib import Path

# 导入共享配置
sys.path.append(str(Path(__file__).parent.parent))
from shared_config import (
    get_component_codes,
    get_single_layer_mapping,
    get_multi_level_mapping
)

# 获取配置（替代原来的本地定义）
COMPONENT_CODES = get_component_codes()
SINGLE_LAYER_MAPPING = get_single_layer_mapping()
MULTI_LEVEL_MAPPING = get_multi_level_mapping()

def create_document_links_config(project_path, structure_type, scripts_base):
    """创建文档关联系统配置文件"""
    config_dir = os.path.join(project_path, 'config')
    os.makedirs(config_dir, exist_ok=True)
    
    links_config = {
        "document_association": {
            "version": "1.0.0",
            "system_type": "semantic_analysis_double_link",
            "created_date": datetime.datetime.now().isoformat(),
            "description": "AI语义分析和双链知识管理系统",
            "responsibilities": [
                "文档注册管理到XX_INDEX.md文件",
                "语义关联发现通过AI分析",
                "双链网络构建使用[[document_name]]格式",
                "关联关系建议为INDEX表格"
            ],
            "semantic_analysis": {
                "enabled": True,
                "ai_model": "text-embedding",
                "similarity_threshold": 0.7,
                "analysis_fields": ["title", "content", "tags", "summary"]
            },
            "double_link_system": {
                "enabled": True,
                "link_format": "[[document_name]]",
                "auto_suggestion": True,
                "bidirectional_links": True
            },
            "association_types": [
                "语义关联",
                "引用关联", 
                "主题关联",
                "依赖关联",
                "时序关联",
                "层级关联"
            ],
            "auto_registration": {
                "enabled": True,
                "scan_directories": True,
                "update_frequency": "on_change",
                "file_patterns": ["*.md", "*.txt", "*.doc", "*.docx"]
            },
            "index_management": {
                "auto_generate": True,
                "template_version": "2.0.0",
                "fields": [
                    "文档ID", "文档名称", "文档路径", "文档类型",
                    "块ID", "块类型", "块标题/描述", "追溯来源块ID", 
                    "关系类型", "最后更新时间"
                ]
            }
        }
    }
    
    config_path = os.path.join(config_dir, 'document_links_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(links_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建文档关联配置文件: {config_path}")
    return config_path

def create_component_index(component_dir, component_code, structure_type, level_info=None):
    """创建组件INDEX文件（文档关联系统负责）"""
    index_file = os.path.join(component_dir, f"{component_code}_INDEX.md")
    
    if os.path.exists(index_file):
        print(f"ℹ INDEX文件已存在，跳过: {index_file}")
        return
    
    component_name = COMPONENT_CODES.get(component_code, component_code)
    
    # 构建文档内容
    if structure_type == "single_layer":
        title = f"# {component_name}组件文档索引"
        id_example = f"{component_code}001, {component_code}002..."
    else:
        level_desc = f" ({level_info})" if level_info else ""
        title = f"# {component_name}组件文档索引{level_desc}"
        id_example = f"L1_{component_code}001, L2_{component_code}001..."
    
    content = f"""{title}

最后更新时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 文档注册说明

本文件用于注册{component_name}组件的所有文档，实现基于ID的文档管理和语义关联发现。

### 系统协作架构

**文档关联系统（本系统）职责**:
- **文档注册管理**: 自动扫描并注册文档到此INDEX文件
- **语义关联发现**: 通过AI分析发现文档间的潜在关联
- **双链网络构建**: 使用[[document_name]]格式建立双向链接
- **关联关系建议**: 为INDEX表格提供智能关联建议

**追溯系统职责**:
- **块级内容管理**: 管理文档内部的内容块和块级追溯关系
- **精确追溯链条**: 建立从输出到输入的精确追溯
- **变更影响分析**: 基于块级关系进行影响分析
- **关系验证**: 验证INDEX中关联关系的有效性

**重要约束**: 内容追溯关系建立在文档关联基础上。如果两个文档之间不存在关联，则也必然不会存在块级内容的关联。

### 注册原则

1. **文档命名灵活**: 文档文件名可以任意命名，无需遵循严格格式
2. **ID注册唯一**: 每个文档必须在下表中注册唯一的文档ID
3. **语义分析**: 系统自动分析文档内容并发现相关性
4. **关联建议**: AI系统为文档关联提供智能建议
5. **及时更新**: 创建、修改、删除文档时自动更新此索引

## 文档列表

此表格支持两个系统的协作管理：
- 前4列由**文档关联系统**管理（文档级信息）
- 后6列由**追溯系统**管理（块级信息）

| 文档ID | 文档名称 | 文档路径 | 文档类型 | 块ID | 块类型 | 块标题/描述 | 追溯来源块ID | 关系类型 | 最后更新时间 |
|--------|---------|---------|---------|------|--------|------------|-------------|---------|------------|
| | | | | | | | | | |

## 文档关联管理

### 语义关联发现
- **AI分析**: 自动分析文档内容的语义相似性
- **关联评分**: 为文档间关联计算相似度分数
- **关联建议**: 基于语义分析提供关联建议
- **关联验证**: 验证建议关联的准确性

### 双链网络构建
- **双向链接**: 使用[[document_name]]格式建立双向引用
- **链接发现**: 自动发现文档中的链接引用
- **网络可视化**: 生成文档关联网络图
- **链接维护**: 自动维护链接的有效性

### 文档注册流程
1. **自动扫描**: 定期扫描组件目录发现新文档
2. **信息提取**: 提取文档基本信息（标题、类型、路径等）
3. **ID分配**: 为新文档自动分配唯一ID
4. **关联分析**: 分析与现有文档的关联关系
5. **INDEX更新**: 更新本INDEX文件的文档列表

## 关系类型定义

### 文档级关系（本系统管理）
- **语义关联**: 基于AI分析的内容相似性关联
- **引用关联**: 文档间的直接引用关系
- **主题关联**: 相同主题领域的文档关联
- **依赖关联**: 文档间的逻辑依赖关系
- **时序关联**: 基于时间顺序的文档关联
- **层级关联**: 基于层级结构的文档关联

## 使用指南

### 1. 文档注册（自动化）

```bash
# 注册组件文档
python scripts/links/auto_link_documents.py --project-path . --component {component_code} --register

# 发现语义关联
python scripts/links/auto_link_documents.py --project-path . --component {component_code} --discover-associations

# 生成关联报告
python scripts/links/auto_link_documents.py --project-path . --all --report
```

### 2. 手动文档注册

1. 创建文档文件（文件名可任意）
2. 运行文档注册脚本，系统会：
   - 自动分配文档ID
   - 提取文档基本信息
   - 分析语义关联
   - 更新INDEX表格

### 3. 语义关联管理

1. **自动发现**: 系统定期分析所有文档的语义关联
2. **关联建议**: 为相似文档提供关联建议
3. **人工确认**: 用户可以确认或拒绝关联建议
4. **关联维护**: 系统自动维护关联关系的有效性

## 自动化工具

### 文档关联系统命令

```bash
# 注册所有文档
python scripts/links/auto_link_documents.py --project-path . --register

# 发现关联关系
python scripts/links/auto_link_documents.py --project-path . --discover-associations

# 生成关联分析报告
python scripts/links/auto_link_documents.py --project-path . --report

# 验证链接有效性
python scripts/links/auto_link_documents.py --project-path . --validate

# 完整流程（注册+关联+报告）
python scripts/links/auto_link_documents.py --project-path . --all
```

### 语义关联示例
- {component_code}001 与 {component_code}002 具有 0.85 的语义相似度
- 建议关联类型：主题关联（都涉及产品功能）
- 双链引用：在 {component_code}001 中添加 [[功能规格说明]]

---
*此文件由文档关联系统管理，用于文档注册和语义关联发现*
"""
    
    with open(index_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"[+] 创建INDEX文件: {index_file}")

def create_index_files_single_layer(project_path):
    """为单层级结构创建INDEX文件"""
    print("创建单层级结构的INDEX文件...")
    
    for component_code, directory in SINGLE_LAYER_MAPPING.items():
        component_dir = os.path.join(project_path, directory)
        if os.path.exists(component_dir):
            create_component_index(component_dir, component_code, "single_layer")
        else:
            print(f"⚠ 目录不存在，跳过: {component_dir}")

def create_index_files_multi_level(project_path):
    """为多层级结构创建INDEX文件"""
    print("创建多层级结构的INDEX文件...")
    
    # 扫描level_*目录
    level_dirs = []
    for item in os.listdir(project_path):
        if item.startswith("level_") and os.path.isdir(os.path.join(project_path, item)):
            level_dirs.append(item)
    
    if not level_dirs:
        print("⚠ 未找到level_*目录，请先运行目录结构初始化")
        return
    
    for level_dir in sorted(level_dirs):
        level_path = os.path.join(project_path, level_dir)
        print(f"处理层级目录: {level_dir}")
        
        for component_code, directory in MULTI_LEVEL_MAPPING.items():
            component_dir = os.path.join(level_path, directory)
            if os.path.exists(component_dir):
                create_component_index(component_dir, component_code, "multi_level", level_dir)
            else:
                print(f"⚠ 目录不存在，跳过: {component_dir}")

def create_semantic_analysis_config(project_path):
    """创建语义分析配置"""
    config_dir = os.path.join(project_path, 'config')
    
    semantic_config = {
        "semantic_analysis": {
            "version": "1.0.0",
            "ai_models": {
                "embedding_model": "text-embedding-ada-002",
                "similarity_model": "cosine",
                "classification_model": "gpt-3.5-turbo"
            },
            "analysis_parameters": {
                "similarity_threshold": 0.7,
                "max_associations": 10,
                "min_content_length": 100,
                "exclude_patterns": ["TODO", "FIXME", "DEBUG"]
            },
            "document_processing": {
                "extract_metadata": True,
                "clean_content": True,
                "chunk_size": 1000,
                "overlap_size": 200
            },
            "association_rules": [
                {
                    "rule_name": "高相似度自动关联",
                    "similarity_threshold": 0.9,
                    "auto_associate": True,
                    "association_type": "语义关联"
                },
                {
                    "rule_name": "中等相似度建议关联",
                    "similarity_threshold": 0.7,
                    "auto_associate": False,
                    "association_type": "潜在关联"
                }
            ]
        }
    }
    
    config_path = os.path.join(config_dir, 'semantic_analysis_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(semantic_config, f, ensure_ascii=False, indent=2)
    
    print(f"[+] 创建语义分析配置: {config_path}")
    return config_path

def create_usage_guide(project_path, structure_type):
    """创建文档关联系统使用指南"""
    guide_content = f"""# 文档关联系统使用指南

## 系统概述

文档关联系统是一个AI语义分析和双链知识管理系统，负责：

1. **文档注册管理**: 自动扫描并注册文档到 XX_INDEX.md 文件
2. **语义关联发现**: 通过AI分析发现文档间的潜在关联
3. **双链网络构建**: 使用 [[document_name]] 格式建立双向链接
4. **关联关系建议**: 为INDEX表格提供智能关联建议

## 核心特点

### AI语义分析
- 自动分析文档内容的语义相似性
- 计算文档间的关联度分数
- 发现潜在的主题关联和依赖关系
- 提供智能化的关联建议

### 双链知识管理
- 支持 [[文档名称]] 格式的双向链接
- 自动发现和维护文档间的引用关系
- 构建知识网络图谱
- 提供可视化的关联探索

### 智能文档注册
- 自动扫描目录发现新文档
- 智能提取文档元数据
- 自动分配唯一文档ID
- 实时更新INDEX文件

## 使用流程

### 1. 自动文档注册

系统会自动扫描项目目录并注册新文档：

```bash
# 注册指定组件的文档
python scripts/links/auto_link_documents.py --project-path . --component REQ --register

# 注册所有组件的文档
python scripts/links/auto_link_documents.py --project-path . --register
```

### 2. 语义关联发现

分析已注册文档间的语义关联：

```bash
# 发现指定组件的关联
python scripts/links/auto_link_documents.py --project-path . --component REQ --discover-associations

# 发现所有文档的关联
python scripts/links/auto_link_documents.py --project-path . --discover-associations
```

### 3. 生成关联报告

生成详细的关联分析报告：

```bash
# 生成组件关联报告
python scripts/links/auto_link_documents.py --project-path . --component REQ --report

# 生成完整关联报告
python scripts/links/auto_link_documents.py --project-path . --report
```

### 4. 完整处理流程

执行完整的文档关联处理流程：

```bash
# 完整流程：注册 + 关联发现 + 报告生成
python scripts/links/auto_link_documents.py --project-path . --all
```

## 配置管理

### 语义分析配置

在 `config/semantic_analysis_config.json` 中配置：

- **相似度阈值**: 控制关联发现的敏感度
- **AI模型选择**: 选择用于语义分析的AI模型
- **处理参数**: 配置文档处理和分析参数

### 文档关联配置

在 `config/document_links_config.json` 中配置：

- **关联类型**: 定义支持的关联类型
- **自动化规则**: 配置自动关联的规则
- **双链设置**: 配置双链网络的参数

## INDEX文件结构

每个组件的 XX_INDEX.md 文件包含10个字段的表格：

| 字段 | 责任系统 | 描述 |
|------|---------|------|
| 文档ID | 文档关联系统 | 唯一文档标识符 |
| 文档名称 | 文档关联系统 | 文档显示名称 |
| 文档路径 | 文档关联系统 | 文档文件路径 |
| 文档类型 | 文档关联系统 | 文档分类类型 |
| 块ID | 追溯系统 | 文档内容块标识 |
| 块类型 | 追溯系统 | 内容块分类 |
| 块标题/描述 | 追溯系统 | 内容块描述 |
| 追溯来源块ID | 追溯系统 | 块级追溯来源 |
| 关系类型 | 追溯系统 | 块级关系类型 |
| 最后更新时间 | 两系统共享 | 最后修改时间 |

## 最佳实践

### 1. 文档创建
- 创建文档后立即运行注册命令
- 在文档中使用 [[文档名称]] 格式建立链接
- 保持文档标题和内容的清晰性

### 2. 语义优化
- 使用清晰的标题和章节结构
- 添加适当的标签和关键词
- 保持文档内容的完整性和逻辑性

### 3. 关联维护
- 定期运行关联发现命令
- 审查和确认系统建议的关联
- 及时清理无效的关联关系

## 故障排除

### 常见问题

1. **语义分析失败**: 检查AI模型配置和网络连接
2. **关联发现结果不准确**: 调整相似度阈值
3. **文档注册失败**: 检查文件权限和路径
4. **INDEX文件损坏**: 从备份恢复或重新生成

### 恢复方法

```bash
# 重新初始化INDEX文件
python scripts/links/init_links.py --project_path . --structure_type single_layer

# 强制重新注册所有文档
python scripts/links/auto_link_documents.py --project-path . --register --force

# 重新发现所有关联
python scripts/links/auto_link_documents.py --project-path . --discover-associations --force
```

---
*更多信息请参考项目README.md和系统配置文件*
"""
    
    guide_path = os.path.join(project_path, 'DOCUMENT_LINKS_GUIDE.md')
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"[+] 创建文档关联系统使用指南: {guide_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="文档关联系统初始化工具")
    parser.add_argument("--project_path", required=True, help="项目路径")
    parser.add_argument("--structure_type", choices=["single_layer", "multi_level"], 
                       default="single_layer", help="目录结构类型")
    parser.add_argument("--scripts_base", required=True, help="公共脚本基础路径")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.project_path):
        print(f"错误: 项目路径不存在 {args.project_path}")
        return False
    
    print(f"初始化文档关联系统...")
    print(f"项目路径: {args.project_path}")
    print(f"结构类型: {args.structure_type}")
    print(f"脚本路径: {args.scripts_base}")
    
    try:
        # 1. 创建文档关联配置文件
        create_document_links_config(args.project_path, args.structure_type, args.scripts_base)
        
        # 2. 创建语义分析配置文件
        create_semantic_analysis_config(args.project_path)
        
        # 3. 创建INDEX文件（文档关联系统负责）
        if args.structure_type == "single_layer":
            create_index_files_single_layer(args.project_path)
        else:
            create_index_files_multi_level(args.project_path)
        
        # 4. 创建使用指南
        create_usage_guide(args.project_path, args.structure_type)
        
        print(f"✅ 文档关联系统初始化完成!")
        return True
        
    except Exception as e:
        print(f"[X] 文档关联系统初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 