#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
进度管理可视化模块

单一职责：专门处理进度管理相关的数据提取和可视化逻辑
"""

import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import DataAdapter, VisualizationData, VisualizationMode, Node, Edge
from core.component_utils import component_manager

class ProgressDataExtractor(DataAdapter):
    """进度管理数据提取器 - 单一职责：进度管理可视化数据提取"""
    
    def __init__(self, project_path: Path):
        self.project_path = Path(project_path)
    
    def extract_data(self, project_path: Path, mode: VisualizationMode) -> VisualizationData:
        """提取进度管理可视化数据"""
        self.project_path = Path(project_path)
        return self._extract_progress_data()
    
    def _extract_progress_data(self) -> VisualizationData:
        """提取进度数据"""
        nodes = []
        edges = []
        
        # 尝试从配置文件加载进度信息
        progress_config = self._load_progress_config()
        
        if progress_config:
            # 从配置文件生成进度节点
            nodes.extend([Node(**node_data) for node_data in progress_config['nodes']])
            edges.extend([Edge(**edge_data) for edge_data in progress_config['edges']])
        else:
            # 从项目结构推断进度
            inferred = self._infer_progress_from_structure()
            nodes.extend([Node(**node_data) for node_data in inferred['nodes']])
            edges.extend([Edge(**edge_data) for edge_data in inferred['edges']])
        
        return VisualizationData(
            title="开发进度仪表板",
            mode=VisualizationMode.PROGRESS,
            nodes=nodes,
            edges=edges,
            metadata={
                "progress_nodes": len(nodes),
                "dependency_count": len(edges),
                "completion_rate": self._calculate_completion_rate(nodes),
                "critical_path": self._identify_critical_path(nodes, edges),
                "last_updated": datetime.now().isoformat()
            }
        )
    
    def _load_progress_config(self) -> Dict[str, Any]:
        """加载进度配置文件"""
        config_files = [
            self.project_path / "config" / "progress.json",
            self.project_path / "progress.json",
            self.project_path / "config" / "phases.json"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        return self._convert_config_to_graph(config)
                except Exception as e:
                    print(f"加载进度配置失败 {config_file}: {e}")
        
        return None
    
    def _convert_config_to_graph(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """将配置文件转换为图数据"""
        nodes = []
        edges = []
        
        # 处理阶段信息
        phases = config.get("phases", [])
        for i, phase in enumerate(phases):
            node_data = {
                "id": f"PHASE_{i+1:02d}",
                "name": phase.get("name", f"阶段{i+1}"),
                "type": "progress",
                "component": self._map_phase_to_component(phase.get("name", "")),
                "properties": {
                    "start_date": phase.get("start_date"),
                    "end_date": phase.get("end_date"),
                    "status": phase.get("status", "pending"),
                    "completion": phase.get("completion", 0),
                    "dependencies": phase.get("dependencies", []),
                    "deliverables": phase.get("deliverables", [])
                }
            }
            nodes.append(node_data)
            
            # 创建依赖关系
            for dep in phase.get("dependencies", []):
                edge_data = {
                    "source": dep,
                    "target": node_data["id"],
                    "type": "依赖",
                    "properties": {"dependency_type": "phase"}
                }
                edges.append(edge_data)
        
        return {"nodes": nodes, "edges": edges}
    
    def _map_phase_to_component(self, phase_name: str) -> str:
        """将阶段名称映射到组件"""
        # 使用共享配置中的组件信息来建立映射
        from core.component_utils import component_manager
        
        # 直接从共享配置中获取组件名称映射
        for comp_id, comp_info in component_manager.components.items():
            if comp_info.name in phase_name or any(keyword in phase_name for keyword in comp_info.keywords):
                return comp_id
        
        return "PM"  # 默认为项目管理
    
    def _infer_progress_from_structure(self) -> Dict[str, List[Dict[str, Any]]]:
        """从项目结构推断进度"""
        nodes = []
        edges = []
        
        # 基于组件目录结构分析进度
        for component_id, component_info in component_manager.components.items():
            component_path = self.project_path / f"0{component_info.order + 1}_{component_info.name.lower()}"
            
            # 分析目录状态
            status, completion = self._analyze_directory_progress(component_path)
            
            node_data = {
                "id": f"{component_id}_PROGRESS",
                "name": f"{component_info.name}进度",
                "type": "progress",
                "component": component_id,
                "properties": {
                    "path": str(component_path),
                    "status": status,
                    "completion": completion,
                    "file_count": len(list(component_path.glob("**/*"))) if component_path.exists() else 0,
                    "last_activity": self._get_last_activity(component_path)
                }
            }
            nodes.append(node_data)
        
        # 创建顺序依赖关系
        ordered_components = component_manager.get_ordered_components()
        for i in range(len(ordered_components) - 1):
            source_comp = ordered_components[i]
            target_comp = ordered_components[i + 1]
            
            edge_data = {
                "source": f"{source_comp}_PROGRESS",
                "target": f"{target_comp}_PROGRESS",
                "type": "依赖",
                "properties": {"dependency_type": "sequential"}
            }
            edges.append(edge_data)
        
        return {"nodes": nodes, "edges": edges}
    
    def _analyze_directory_progress(self, directory: Path) -> tuple:
        """分析目录进度状态"""
        if not directory.exists():
            return "not_started", 0
        
        # 统计文件数量
        md_files = list(directory.glob("**/*.md"))
        index_files = list(directory.glob("**/*_INDEX.md"))
        
        if not md_files:
            return "not_started", 0
        elif len(md_files) < 3:
            return "in_progress", 25
        elif not index_files:
            return "in_progress", 50
        else:
            # 检查INDEX文件是否有内容
            total_content = 0
            for md_file in md_files:
                try:
                    with open(md_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        total_content += len(content.strip())
                except:
                    pass
            
            if total_content > 10000:  # 充分的内容
                return "completed", 100
            elif total_content > 5000:
                return "in_progress", 75
            else:
                return "in_progress", 50
    
    def _get_last_activity(self, directory: Path) -> str:
        """获取最后活动时间"""
        if not directory.exists():
            return None
        
        latest_time = 0
        for file_path in directory.glob("**/*"):
            if file_path.is_file():
                mtime = file_path.stat().st_mtime
                if mtime > latest_time:
                    latest_time = mtime
        
        if latest_time > 0:
            return datetime.fromtimestamp(latest_time).isoformat()
        return None
    
    def _calculate_completion_rate(self, nodes: List[Node]) -> float:
        """计算整体完成率"""
        if not nodes:
            return 0.0
        
        total_completion = 0
        count = 0
        
        for node in nodes:
            completion = node.properties.get("completion", 0)
            if isinstance(completion, (int, float)):
                total_completion += completion
                count += 1
        
        return total_completion / count if count > 0 else 0.0
    
    def _identify_critical_path(self, nodes: List[Node], edges: List[Edge]) -> List[str]:
        """识别关键路径"""
        # 简化的关键路径算法
        # 找到没有前置依赖的节点作为起点
        node_ids = {node.id for node in nodes}
        has_incoming = {edge.target for edge in edges}
        start_nodes = node_ids - has_incoming
        
        # 从起点开始的最长路径
        longest_path = []
        for start_node in start_nodes:
            path = self._find_longest_path(start_node, edges, [])
            if len(path) > len(longest_path):
                longest_path = path
        
        return longest_path
    
    def _find_longest_path(self, current_node: str, edges: List[Edge], visited: List[str]) -> List[str]:
        """查找最长路径（递归）"""
        if current_node in visited:
            return visited
        
        new_visited = visited + [current_node]
        
        # 找到当前节点的所有后继
        successors = [edge.target for edge in edges if edge.source == current_node]
        
        longest = new_visited
        for successor in successors:
            path = self._find_longest_path(successor, edges, new_visited)
            if len(path) > len(longest):
                longest = path
        
        return longest
    
    def generate_progress_report(self) -> str:
        """生成进度报告"""
        data = self._extract_progress_data()
        
        report = f"""# 项目进度报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 整体进度概览

- **总体完成率**: {data.metadata['completion_rate']:.1f}%
- **进度节点**: {len(data.nodes)}
- **依赖关系**: {len(data.edges)}

## 各组件进度详情

"""
        
        # 按组件分组显示进度
        component_progress = {}
        for node in data.nodes:
            component = node.component or "未知"
            if component not in component_progress:
                component_progress[component] = []
            component_progress[component].append(node)
        
        for component, nodes in component_progress.items():
            component_name = component_manager.get_component_info(component).name if component in component_manager.components else component
            report += f"### {component_name} ({component})\n\n"
            
            for node in nodes:
                status = node.properties.get("status", "未知")
                completion = node.properties.get("completion", 0)
                
                status_icon = {
                    "completed": "✅",
                    "in_progress": "🔄", 
                    "not_started": "⏸️",
                    "blocked": "🚫"
                }.get(status, "❓")
                
                report += f"- {status_icon} **{node.name}**: {completion}% ({status})\n"
            
            report += "\n"
        
        # 关键路径
        critical_path = data.metadata.get('critical_path', [])
        if critical_path:
            report += f"## 关键路径\n\n"
            report += " → ".join(critical_path) + "\n\n"
        
        return report
    
    def analyze_bottlenecks(self) -> Dict[str, Any]:
        """分析瓶颈"""
        data = self._extract_progress_data()
        
        bottlenecks = []
        delayed_tasks = []
        
        for node in data.nodes:
            status = node.properties.get("status", "unknown")
            completion = node.properties.get("completion", 0)
            
            # 识别瓶颈
            if status == "blocked":
                bottlenecks.append({
                    "node": node.id,
                    "name": node.name,
                    "reason": "任务被阻塞"
                })
            elif completion < 50 and status == "in_progress":
                delayed_tasks.append({
                    "node": node.id, 
                    "name": node.name,
                    "completion": completion
                })
        
        return {
            "bottlenecks": bottlenecks,
            "delayed_tasks": delayed_tasks,
            "recommendations": self._generate_recommendations(bottlenecks, delayed_tasks)
        }
    
    def _generate_recommendations(self, bottlenecks: List[Dict], delayed_tasks: List[Dict]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if bottlenecks:
            recommendations.append("🚫 解除阻塞任务，重新分配资源")
        
        if len(delayed_tasks) > 2:
            recommendations.append("⚡ 关注进度缓慢的任务，考虑增加人力支持")
        
        if not bottlenecks and not delayed_tasks:
            recommendations.append("✅ 项目进展顺利，保持当前节奏")
        
        return recommendations 