#ifndef _3D_HAND_MACHINE_H_
#define _3D_HAND_MACHINE_H_

#include <QObject>

namespace N3DHandMachine {
  //* config
  enum class EMode //操作模式
  {
      auto_mode,
      manual_mode,
  };
}


class C3dHandMachine:public QObject{
public:
  C3dHandMachine();
  ~C3dHandMachine();

  //*4. 设备控制
  template<typename T>
  struct St3D{
      T x;
      T y;
      T z;
  }; //模板结构体不能用 typedef,因为内存还无法确定

protected:

};


#endif
