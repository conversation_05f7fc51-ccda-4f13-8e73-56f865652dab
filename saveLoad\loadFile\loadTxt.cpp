#include "loadTxt.h"
#include <QFile>
#include <QMap>
#include <QDebug>
#include <QMessageBox>
#include <QStringList>

CLoadTxt::CLoadTxt(){

}

CLoadTxt::~CLoadTxt(){

}

bool CLoadTxt::loadFile(const QString &filename)
{
    QFile readFile(filename);
//    QString str_all;
    if(!readFile.open(QIODevice::ReadOnly|QIODevice::Text)) {
        QMessageBox::critical(0, QString("Error"),
                              QString("Cannot OpenTxtFile %1").arg(filename));
        return false;
    }
    else {
        QTextStream stream(&readFile);
        stream.setCodec("UTF-8");
//        stream.setCodec("ANSI");

        m_str_all = stream.readAll();
//        m_bytes_all = readFile.readAll(); //读取文本中全部文件
//        QString oneLine;
//        while(!stream.atEnd()) {
//            oneLine = stream.readLine();  //读取一行
//            qDebug() << oneLine;
//        }
    }
    readFile.close();
    return true;
}

/**
 * @brief 读参数。使用静态函数？如何实现多态
 * @param filename
 * @param xml_read
 */
void CLoadTxt::readParam(const QString &filename, QMap<QString, int> *xml_read)
{

}

void CLoadTxt::dataPrasing(QString &str, QMap<QString, QString> *xml_read) {
    QStringList str_list = str.split("[2023");
    if(str_list.count() > 2) {
        if(str_list.at(1).length() > 0) {
            QStringList sub_str_list = str_list.at(1).split("] ");

            xml_read->insert(sub_str_list.at(0), sub_str_list.at(1));
//            int str_len1 = str_list.at(0).length();
//            int str_len2 = str_list.at(1).length();
            str.remove(0, str_list.at(0).length() + str_list.at(1).length() + 1);
        }
        else {
            qDebug() << "-e loadtxt/ prasing error";
        }
    }
//    for(int i = 0; i < str_list.count(); i++) {
//        if(i == str_list.count() - 1) {
//            //最后一行不需要换行
//            //            stream << strList.at(i);
//            qDebug() << str_list.at(i);
//        }
//        else if(str_list.at(i).contains("$PCSI")) {
//            QString s;
//            //            strListforPCSI = strList.at(i).split(",");

//            //            s = QString::number(p);
//            //            QString tempStr = strList.at(i);
//            //            tempStr.replace(45, len, s);
//            //            qDebug() << "int p:=" << p;
//            //            qDebug() << tempStr;
//            //            qDebug() << "int s=" << s;
//            //            stream << tempStr << '\n';
//        }
//        else {
//            //            stream << strList.at(i) << '\n';
//        }
//    }
}

/**
 * @brief load all params from xml file
 * @param filename
 * @param xml_read
 */
void CLoadTxt::readParam(const QString &filename, QMap<QString, QByteArray> *xml_read) {
}


void CLoadTxt::readParam(const QString &filename, QMap<QString, QString> *xml_read) {

    QFile readFile(filename);
//    QString str_all;
    if(!readFile.open(QIODevice::ReadOnly|QIODevice::Text)) {
        QMessageBox::critical(0, QString("Error"),
                              QString("Cannot OpenTxtFile %1").arg(filename));
        return;
    }
    else {
        QTextStream stream(&readFile);
        stream.setCodec("UTF-8");

        QString prasing_buff;
        while(!stream.atEnd()) {
            QString oneLine = stream.readLine();  //读取一行
            prasing_buff.append(oneLine);
            qDebug() << oneLine;

            dataPrasing(prasing_buff, xml_read);
        }
    }
    readFile.close();
}

bool CLoadTxt::readParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_read) {

}

bool CLoadTxt::writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QByteArray> *xml_write) {

}

bool CLoadTxt::writeParam(const QString &filename, const QString &rootname, const QString &nodename, QMap<QString, QString> *xml_write) {

}
