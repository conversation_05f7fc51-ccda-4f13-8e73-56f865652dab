# 产品开发完整功能集成MCP服务器 - 使用指南

## 🎉 最新更新 - 问题已完全解决！

### ✅ 解决的关键问题
1. **Unicode编码错误** - 完全解决，使用静默版本server
2. **MCP JSON通信干扰** - 完全解决，输出重定向处理
3. **工具功能验证** - 25个工具全部正常工作

### 🚀 推荐配置

**使用 `server_silent.py` 静默版本**：

```json
{
  "mcpServers": {
    "prod-dev": {
      "command": "python",
      "args": [
        "G:/10_note-files/Obsidian-Vault/KA/Product-development/build-tools/mcp-server_local_integrations/unified/product-development-complete/server_silent.py"
      ],
      "env": {
        "PYTHONIOENCODING": "utf-8",
        "PYTHONPATH": "G:/10_note-files/Obsidian-Vault/KA/Product-development/build-tools/scripts"
      }
    }
  }
}
```

### 📊 Server版本对比

| 版本 | 工具数量 | 状态 | 说明 |
|------|----------|------|------|
| `server.py` | 25个 | ❌ | 编码错误，无法启动 |
| `server_en.py` | 25个 | ⚠️ | JSON通信问题 |
| `server_silent.py` | 25个 | ✅ | **推荐使用** |

## 概述

产品开发完整功能集成MCP服务器是一个基于Model Context Protocol (MCP) 标准的统一服务器，集成了产品开发生命周期中的所有核心功能。现已完全解决编码和通信问题，包含25个完整功能的工具。

## 功能特性

### 🚀 项目管理
- **项目初始化**：自动创建标准项目结构
- **目录管理**：支持单层级和多层级项目结构
- **项目信息**：获取项目状态和统计信息

### ⚙️ 配置管理
- **配置加载**：支持JSON和YAML格式
- **配置保存**：自动创建目录和格式化
- **配置验证**：检查配置完整性和正确性
- **配置合并**：支持多配置文件合并

### 📋 需求管理
- **需求导入**：支持多种格式的需求文档
- **需求分析**：自动分析需求分布和优先级
- **需求矩阵**：生成可追溯性矩阵
- **任务生成**：从需求自动生成开发任务

### 📄 文档管理
- **文档关联**：自动发现文档间的语义关系
- **Canvas同步**：将文档关系可视化到Obsidian Canvas
- **信息追溯**：管理块级内容追溯
- **文档报告**：生成文档统计和分析报告

### 🔍 代码分析
- **质量分析**：检查代码复杂度和可读性
- **风格检查**：验证编码规范遵循情况
- **安全扫描**：识别潜在安全漏洞
- **度量生成**：计算各种代码度量指标

### 📊 可视化
- **架构图**：生成系统、组件、部署图
- **流程图**：创建工作流和决策流程图
- **UML图**：从源代码生成类图、序列图等
- **仪表板**：创建项目概览和度量仪表板

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置MCP服务器

在Cursor的 `.cursor/mcp.json` 中添加：

```json
{
  "mcpServers": {
    "product-development-complete": {
      "command": "python",
      "args": ["./unified/product-development-complete/server.py"],
      "description": "产品开发完整功能集成MCP服务器"
    }
  }
}
```

### 3. 启动使用

重启Cursor，MCP服务器会自动启动。然后可以通过AI助手自然语言交互使用所有功能。

## 使用示例

### 项目初始化

```
用户：请帮我创建一个名为"智能家居系统"的多层级项目

AI助手：我来帮您创建多层级项目结构...
✅ 项目初始化成功：智能家居系统
已创建标准的多层级目录结构，包含需求、设计、实现、验证、部署等阶段目录。
```

### 配置管理

```
用户：请加载项目配置文件并检查是否有问题

AI助手：我来加载并验证配置文件...
✅ 配置文件加载成功
⚠️ 发现2个警告：缺少数据库配置、日志级别未设置
```

### 需求分析

```
用户：请导入requirements.xlsx文件并分析需求分布

AI助手：我来导入并分析需求文档...
✅ 需求导入成功：共45个需求
📊 分析结果：
- 功能需求：32个 (71%)
- 非功能需求：13个 (29%)
- 高优先级：15个，中优先级：20个，低优先级：10个
```

### 文档关联

```
用户：请扫描项目文档并建立关联关系

AI助手：我来扫描并分析文档关联...
✅ 文档关联处理成功
处理了28个文档，创建了45个关联链接，更新了6个INDEX文件
```

### 代码分析

```
用户：请分析src目录下的Python代码质量

AI助手：我来分析代码质量...
✅ 代码质量分析完成
分析了15个文件，总体质量评分：85分
发现3个高复杂度函数，建议重构
```

### 可视化

```
用户：请生成系统架构图

AI助手：我来生成系统架构图...
✅ 系统架构图生成成功
包含5个组件，8个连接关系，已保存为architecture_system.svg
```

## 高级功能

### 批量操作

```
用户：请对整个项目执行完整的质量检查流程

AI助手：我来执行完整的质量检查...
1. ✅ 代码质量分析完成
2. ✅ 配置文件验证完成
3. ✅ 文档关联检查完成
4. ✅ 需求追溯验证完成
5. ✅ 生成综合质量报告
```

### 自定义工作流

```
用户：请创建一个包含需求分析、设计评审、代码检查的自定义工作流

AI助手：我来创建自定义工作流...
✅ 工作流创建成功
包含3个阶段，5个检查点，已保存配置文件
```

## 配置选项

### 项目配置

```json
{
  "project": {
    "name": "项目名称",
    "type": "single_layer",
    "description": "项目描述"
  },
  "analysis": {
    "code_quality": {
      "enabled": true,
      "threshold": 80
    },
    "security_scan": {
      "enabled": true,
      "severity": "medium"
    }
  },
  "visualization": {
    "default_format": "svg",
    "theme": "modern"
  }
}
```

### 环境变量

```bash
# 设置项目根目录
export PRODUCT_DEV_ROOT="/path/to/project"

# 设置默认输出格式
export DEFAULT_OUTPUT_FORMAT="svg"

# 启用调试模式
export DEBUG_MODE="true"
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查Python路径和权限
   - 验证依赖是否正确安装
   - 查看错误日志

2. **工具无法调用**
   - 确认函数签名和类型注解
   - 检查装饰器是否正确使用
   - 验证参数类型匹配

3. **文件路径问题**
   - 使用绝对路径
   - 检查文件权限
   - 确认文件存在

4. **配置无效**
   - 验证JSON/YAML格式
   - 检查必需字段
   - 确认配置路径

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查MCP连接**
   ```
   用户：请测试MCP服务器连接状态
   ```

3. **验证工具可用性**
   ```
   用户：请列出所有可用的MCP工具
   ```

## 最佳实践

### 项目组织

1. **标准化目录结构**
   - 使用推荐的项目模板
   - 保持一致的命名约定
   - 定期更新文档

2. **配置管理**
   - 使用版本控制管理配置
   - 分离环境特定配置
   - 定期备份重要配置

3. **文档维护**
   - 保持文档同步更新
   - 使用标准化模板
   - 定期检查链接有效性

### 性能优化

1. **批量处理**
   - 合并相似操作
   - 使用异步处理
   - 避免重复扫描

2. **缓存策略**
   - 缓存分析结果
   - 增量更新
   - 定期清理缓存

3. **资源管理**
   - 监控内存使用
   - 限制并发操作
   - 优化文件I/O

## 扩展开发

### 添加新工具

1. 在 `tools/` 目录下创建新模块
2. 实现工具函数并添加类型注解
3. 在 `server.py` 中注册工具
4. 更新文档和测试

### 自定义提示

1. 在 `prompts/` 目录下创建模板
2. 使用 `@mcp.prompt()` 装饰器
3. 提供参数化支持
4. 测试提示效果

### 集成外部工具

1. 评估工具兼容性
2. 创建适配器模块
3. 处理错误和异常
4. 提供配置选项

## 支持和反馈

如果您遇到问题或有改进建议，请：

1. 查看故障排除部分
2. 检查相关文档
3. 提交问题报告
4. 参与社区讨论

## 更新日志

### v2.0.0
- 重构为符合MCP协议规范的架构
- 添加完整的代码分析功能
- 集成可视化工具
- 改进错误处理和用户体验

### v1.0.0
- 初始版本发布
- 基础项目管理功能
- 配置和需求管理
- 文档关联功能
