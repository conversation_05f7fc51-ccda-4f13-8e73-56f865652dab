#ifndef _ITEM_TEXT_MODULE_H
#define _ITEM_TEXT_MODULE_H

#include <QDockWidget>
#include <QImage>
#include <QTableWidget>

class CItemTextModule
{
public:
    explicit CItemTextModule(QTableWidget* table_);
    ~CItemTextModule();

    const QString process_normal_sheetStyle = "QTextEdit{"
                                              "min-width: 300px;"
                                              "min-height: 50px;"
                                              "font: 10pt;" //'Agency FB'
            "border-radius: 6px;"
            "border:2px solid black;"
            "background: rgb(255,255,255);"
            "color: rgb(0, 0, 0);}"; /*浅黑*/

    const QString process_unnormal_sheetStyle = "QTextEdit{"
                                                "min-width: 300px;"
                                                "min-height: 50px;"
                                                "font: 10pt;" // 'Agency FB'
            "border-radius: 6px;"
            "border:2px solid black;"
            "background: rgb(255,255,255);"
            "color: rgb(255, 0, 0);"; /*浅黑*/

//    QTableWidget *m_table_ = nullptr;
    IPhotonSensor::StMapInfo *m_map_info_ = nullptr;

    //* 光斑显示
    void greyMapShow(QTableWidget *map, IPhotonSensor::StMapData *map_data_);

    //* 目标区域凸显

    //* 状态栏update
    void resultClean(void);

private:
    void mp_init(QTableWidget* table_, IPhotonSensor::StMapInfo *map_info_);
};

#endif // CItemTextModule_H
