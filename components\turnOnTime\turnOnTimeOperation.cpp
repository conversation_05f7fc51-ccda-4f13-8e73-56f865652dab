#include "turnOnTimeOperation.h"

CTurnOnTimeOperation::CTurnOnTimeOperation(StConfig *st_config_){


  /*2.1 创建子线程（初始化就创建子线程？）*/
  qDebug() << "-motor main thread id: " << QThread::currentThread();
  speedMonitorSub_ = new QThread;// 创建线程对象
  m_speedMonitorSerial_ = new motorMonitorSerial;
  m_speedMonitorSerial_->m_single_receive = true;
  m_speedMonitorSerial_->moveToThread(speedMonitorSub_); //创建工作的类对象，千万不要指定给创建的对象指定父对象 如果指定了: QObject::moveToThread: Cannot move objects with a parent

//    Q_DECLARE_METATYPE(monitor_process_two_STEP);

  connect(this, &turnOnTime::subSerialLoop, m_speedMonitorSerial_, &motorMonitorSerial::loop); //连接子线程
  connect(speedMonitorSub_, &QThread::finished, m_speedMonitorSerial_, &QObject::deleteLater); //线程析构
  void (dt2Protocol::* ptrDataOutput1)(monitor_process_two::PROCESS_STEP, monitor_process_two::STEP_STATUS, QByteArray) = &dt2Protocol::dataOutput;
  connect(m_speedMonitorSerial_->m_dt2Protocol, ptrDataOutput1, this, &turnOnTime::dataReceive);

  speedMonitorSub_->start(); //

}

CTurnOnTimeOperation::~CTurnOnTimeOperation(){

}

void CTurnOnTimeOperation::varibleInit()
{
    m_bottom_comm_status->check_error_cnt = 0;
    m_bottom_comm_status->timeout_cnt = 0;
    /*1.2 data init*/
    switch(m_function_select) //
    {
    case 1: //motor monitor
        break;
    case 2: //lidar speeed
        break;
    default:
        break;
    }

    mst_result_->distribute.clear();
}


void turnOnTime::dataReceive(monitor_process_two::PROCESS_STEP step, monitor_process_two::STEP_STATUS status, QByteArray data)
{
    uint16_t num = data.length();
    if(m_stStatus_rec_->cur_step == step)
    {
        qDebug() << "-turnT: receive cur step:" << step << "ack time:" << m_stStatus_rec_->step_timer_cnt.elapsed();
//        if(m_stStatus_rec_->cur_status == monitor_process_two::start)
//            m_stStatus_rec_->step_time_cnt = 0;

        m_stStatus_rec_->cur_status = status;
        //processLoop(m_stStatus_rec_->cur_step, m_stStatus_rec_->cur_status);

        if(num != 0)
        {
//            if(m_comm_data.length() == 0)
//            {
//                m_comm_data = data;
//            }
//            else
//            {
//                qDebug() << "-turnT: data receive busy";
//            }
        }
    }
    else
    {
        qDebug() << "-turnT e: cur step:" << m_stStatus_rec_->cur_step << "receive step:" << step;
    }
}
