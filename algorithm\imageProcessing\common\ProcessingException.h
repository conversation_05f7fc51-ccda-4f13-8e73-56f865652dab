#ifndef IMAGEPROCESSING_PROCESSINGEXCEPTION_H
#define IMAGEPROCESSING_PROCESSINGEXCEPTION_H

#include <stdexcept>
#include <QString>
#include <QDebug>

namespace ImageProcessing {

/**
 * @brief 图像处理异常基类
 */
class ProcessingException : public std::runtime_error {
public:
    explicit ProcessingException(const QString& message)
        : std::runtime_error(message.toStdString()), message_(message) {
        qWarning() << "ProcessingException:" << message_;
    }

    explicit ProcessingException(const std::string& message)
        : std::runtime_error(message), message_(QString::fromStdString(message)) {
        qWarning() << "ProcessingException:" << message_;
    }

    const QString& qMessage() const { return message_; }

private:
    QString message_;
};

/**
 * @brief 无效参数异常
 */
class InvalidParameterException : public ProcessingException {
public:
    explicit InvalidParameterException(const QString& paramName, const QString& reason)
        : ProcessingException(QString("Invalid parameter '%1': %2").arg(paramName, reason)),
          paramName_(paramName), reason_(reason) {}

    const QString& parameterName() const { return paramName_; }
    const QString& reason() const { return reason_; }

private:
    QString paramName_;
    QString reason_;
};

/**
 * @brief 无效图像数据异常
 */
class InvalidImageDataException : public ProcessingException {
public:
    explicit InvalidImageDataException(const QString& reason)
        : ProcessingException(QString("Invalid image data: %1").arg(reason)) {}
};

/**
 * @brief 算法执行异常
 */
class AlgorithmException : public ProcessingException {
public:
    explicit AlgorithmException(const QString& algorithmName, const QString& reason)
        : ProcessingException(QString("Algorithm '%1' failed: %2").arg(algorithmName, reason)),
          algorithmName_(algorithmName) {}

    const QString& algorithmName() const { return algorithmName_; }

private:
    QString algorithmName_;
};

/**
 * @brief 内存分配异常
 */
class MemoryException : public ProcessingException {
public:
    explicit MemoryException(const QString& operation)
        : ProcessingException(QString("Memory allocation failed during: %1").arg(operation)) {}
};

/**
 * @brief 不支持的操作异常
 */
class UnsupportedOperationException : public ProcessingException {
public:
    explicit UnsupportedOperationException(const QString& operation)
        : ProcessingException(QString("Unsupported operation: %1").arg(operation)) {}
};

} // namespace ImageProcessing

#endif // IMAGEPROCESSING_PROCESSINGEXCEPTION_H
