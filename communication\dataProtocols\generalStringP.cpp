#include "generalStringP.h"
#include <qdebug.h>
#include "qLog.h"

CGeneralStringP::CGeneralStringP():
    mst_interaction_frame_(new StInteractionFrame)
  , mst_data_info_frame_(new StDataInfoFrame)
{

}

CGeneralStringP::~CGeneralStringP(){
    delete mst_interaction_frame_;
    delete mst_data_info_frame_;
}

void CGeneralStringP::headerWrite(const QString &header) {
    mst_interaction_frame_->header = header;
}

QByteArray CGeneralStringP::getControlCmd(const QString &cmd_id)
{
    QByteArray cmd;
    mst_interaction_frame_->cmd = cmd_id;

    return cmd;
}

/**
 * @brief 获取写指令
 * @return
 */
QByteArray CGeneralStringP::getWriteCmd(const QString &cmd_id, const QString &data)
{
    QByteArray w_cmd;
    mst_interaction_frame_->cmd = cmd_id;
    mst_interaction_frame_->data = data;

    return w_cmd;
}

/**
 * @brief 获取读指令
 * @return
 */
QByteArray CGeneralStringP::getReadCmd(const QString &cmd)
{
    QByteArray r_cmd;
    StInteractionFrame* st_interaction_frame_ = nullptr;

    return r_cmd;
}


